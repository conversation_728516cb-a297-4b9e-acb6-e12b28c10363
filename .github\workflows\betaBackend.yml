name: Beta Backend
on:
  push:
    branches: [beta]
    paths: ["backend/**", ".github/workflows/betaBackend.yml", "!**.md"]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

defaults:
  run:
    shell: bash
    working-directory: backend

jobs:
  tests:
    name: Tests
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v3

      - name: Set Node Version
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - uses: google-github-actions/auth@v1
        with:
          project_id: ${{ secrets.GOOGLE_PROJECT_ID }}
          credentials_json: ${{ secrets.APPLICATION_CREDENTIALS }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker authentication
        run: |
          gcloud auth configure-docker europe-west1-docker.pkg.dev

      - run: ./run test

      - name: Notify Errors
        uses: 8398a7/action-slack@v3
        if: ${{ failure() }}
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          if_mention: failure
          author_name: Backend Beta Tests Failed 😬
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  build:
    name: Build
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v3

      - uses: google-github-actions/auth@v1
        with:
          project_id: ${{ secrets.GOOGLE_PROJECT_ID }}
          credentials_json: ${{ secrets.APPLICATION_CREDENTIALS }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker authentication
        run: |
          gcloud auth configure-docker europe-west1-docker.pkg.dev

      - run: ./run build

      - name: Notify
        uses: 8398a7/action-slack@v3
        if: ${{ failure() }}
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          if_mention: failure
          author_name: Backend Beta Build Failed 😩
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  deploy_beta:
    name: Deploy
    needs: [tests, build]
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v3

      - uses: google-github-actions/auth@v1
        with:
          project_id: ${{ secrets.GOOGLE_PROJECT_ID }}
          credentials_json: ${{ secrets.APPLICATION_CREDENTIALS }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker authentication
        run: |
          gcloud auth configure-docker europe-west1-docker.pkg.dev

      - run: ./run deploy:beta

      - name: Notify
        uses: 8398a7/action-slack@v3
        if: ${{ failure() }}
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          if_mention: failure
          author_name: Backend Beta Deploy Failed 🤨
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Create Sentry release
        uses: getsentry/action-release@v1
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: hablo
          SENTRY_PROJECT: hablo-backend
        with:
          environment: beta

  tests_e2e:
    name: Tests E2E
    needs: [deploy_beta]
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v3

      - uses: google-github-actions/auth@v1
        with:
          project_id: ${{ secrets.GOOGLE_PROJECT_ID }}
          credentials_json: ${{ secrets.APPLICATION_CREDENTIALS }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Set GCP env
        run: echo "GOOGLE_APPLICATION_CREDENTIALS_JSON=$(cat ${{ env.GOOGLE_APPLICATION_CREDENTIALS }})" >> $GITHUB_ENV

      - uses: mattes/gce-cloudsql-proxy-action@v1
        with:
          creds: ${{ env.GOOGLE_APPLICATION_CREDENTIALS_JSON }}
          instance: hablo-279710:europe-west1:hablo
          port: 3333

      - name: Run Tests
        uses: cypress-io/github-action@v6
        with:
          browser: chrome
          working-directory: e2e
          command: yarn test:beta --record=true --config video=true

      - name: Notify Errors
        uses: 8398a7/action-slack@v3
        if: ${{ failure() }}
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          if_mention: failure
          author_name: Backend BETA E2E Tests Failed 🤔
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify Success
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          author_name: Backend BETA Deployed 🥳
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
