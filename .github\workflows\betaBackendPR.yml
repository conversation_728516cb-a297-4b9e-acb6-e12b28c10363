name: Beta Backend PR
on:
  pull_request:
    paths: ["backend/**", ".github/workflows/betaBackendPR.yml", "!**.md"]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

defaults:
  run:
    shell: bash
    working-directory: backend

jobs:
  tests:
    name: Tests
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v3

      - name: Set Node Version
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - uses: google-github-actions/auth@v1
        with:
          project_id: ${{ secrets.GOOGLE_PROJECT_ID }}
          credentials_json: ${{ secrets.APPLICATION_CREDENTIALS }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - run: ./run test

      - name: Notify Errors
        uses: 8398a7/action-slack@v3
        if: ${{ failure() }}
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          if_mention: failure
          author_name: Backend Beta Tests Failed 😬
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
