name: Beta Frontend
on:
  push:
    branches: [beta]
    paths:
      ["frontend/**", "e2e/**", ".github/workflows/betaFrontend.yml", "!**.md"]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

defaults:
  run:
    shell: bash
    working-directory: frontend

jobs:
  tests:
    name: Tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set Node Version
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - name: Run Test
        id: test
        run: ./run test

      - name: Notify Errors
        uses: 8398a7/action-slack@v3
        if: ${{ failure() }}
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          if_mention: failure
          author_name: Frontend BETA Tests Failed 😩
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set Node Version
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - uses: google-github-actions/auth@v1
        with:
          project_id: ${{ secrets.GOOGLE_PROJECT_ID }}
          credentials_json: ${{ secrets.APPLICATION_CREDENTIALS }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Run Build
        id: build
        run: ./run build

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: build
          path: frontend/build/

      - name: Notify Errors
        uses: 8398a7/action-slack@v3
        if: ${{ failure() }}
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          if_mention: failure
          author_name: Frontend BETA Build Failed 😖
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  deploy_beta:
    name: Deploy
    needs: [tests, build]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - uses: google-github-actions/auth@v1
        with:
          project_id: ${{ secrets.GOOGLE_PROJECT_ID }}
          credentials_json: ${{ secrets.APPLICATION_CREDENTIALS }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Download Artifact
        uses: actions/download-artifact@v4
        with:
          name: build
          path: frontend/build/

      - name: Run Deploy
        run: ./run deploy:beta

      - name: Notify
        uses: 8398a7/action-slack@v3
        if: ${{ failure() }}
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          if_mention: failure
          author_name: Frontend BETA Deploy Failed 😕
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  rollbar:
    name: Rollbar
    needs: [tests, build]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Download Artifact
        uses: actions/download-artifact@v4
        with:
          name: build
          path: frontend/build/

      - name: Set source maps
        id: source_maps
        run: |
          echo "DATA_SOURCE_MAPS=$(find ./build/js/*.js.map | tr '\n' ' ')" >> $GITHUB_ENV
          echo "DATA_SOURCE_MAPS_URLS=$(cd ./build && find ./js/*.js | sed -e 's/.\/js/\/\/beta.myhablo.com\/js/' | tr '\n' ' ')" >> $GITHUB_ENV
      - name: Rollbar deploy
        uses: rollbar/github-deploy-action@2.1.2
        with:
          environment: beta
          status: succeeded
          version: ${{ github.sha }}
          source_maps: ${{ steps.source_maps.env.DATA_SOURCE_MAPS }}
          minified_urls: ${{ steps.source_maps.env.DATA_SOURCE_MAPS_URLS }}
        env:
          ROLLBAR_USERNAME: ${{ github.actor }}
          ROLLBAR_ACCESS_TOKEN: ${{ secrets.ROLLBAR_ACCESS_TOKEN }}

  tests_e2e:
    name: Tests E2E
    needs: [deploy_beta]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - uses: google-github-actions/auth@v1
        with:
          project_id: ${{ secrets.GOOGLE_PROJECT_ID }}
          credentials_json: ${{ secrets.APPLICATION_CREDENTIALS }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Set GCP env
        run: echo "GOOGLE_APPLICATION_CREDENTIALS_JSON=$(cat ${{ env.GOOGLE_APPLICATION_CREDENTIALS }})" >> $GITHUB_ENV

      - uses: mattes/gce-cloudsql-proxy-action@v1
        with:
          creds: ${{ env.GOOGLE_APPLICATION_CREDENTIALS_JSON }}
          instance: hablo-279710:europe-west1:hablo
          port: 3333

      - name: Run Tests
        uses: cypress-io/github-action@v5
        with:
          browser: chrome
          working-directory: e2e
          command: yarn test:beta --record=true --config video=true

      - name: Notify Errors
        uses: 8398a7/action-slack@v3
        if: ${{ failure() }}
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          if_mention: failure
          author_name: Frontend BETA E2E Tests Failed 😢
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify Success
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          author_name: Frontend BETA Deployed 🎉
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
