name: Production
on:
  push:
    branches: [production]
    paths:
      [
        "backend/**",
        "frontend/**",
        ".github/workflows/production.yml",
        "!**.md",
      ]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

defaults:
  run:
    shell: bash

jobs:
  tests_backend:
    name: Tests Backend
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set Node Version
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - uses: google-github-actions/auth@v1
        with:
          project_id: ${{ secrets.GOOGLE_PROJECT_ID }}
          credentials_json: ${{ secrets.APPLICATION_CREDENTIALS }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker authentication
        run: |
          gcloud auth configure-docker europe-west1-docker.pkg.dev

      - name: Run Test
        run: ./run test
        working-directory: backend

      - name: Notify Errors
        uses: 8398a7/action-slack@v3
        if: ${{ failure() }}
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          if_mention: failure
          author_name: Backend Tests Failed 😣
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  tests_frontend:
    name: Tests Frontend
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set Node Version
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - uses: google-github-actions/auth@v1
        with:
          project_id: ${{ secrets.GOOGLE_PROJECT_ID }}
          credentials_json: ${{ secrets.APPLICATION_CREDENTIALS }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker authentication
        run: |
          gcloud auth configure-docker europe-west1-docker.pkg.dev

      - name: Run Test
        run: ./run test
        working-directory: frontend

      - name: Notify Errors
        uses: 8398a7/action-slack@v3
        if: ${{ failure() }}
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          if_mention: failure
          author_name: Frontend Tests Failed 😔
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  build_backend:
    name: Build Backend
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set Node Version
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - uses: google-github-actions/auth@v1
        with:
          project_id: ${{ secrets.GOOGLE_PROJECT_ID }}
          credentials_json: ${{ secrets.APPLICATION_CREDENTIALS }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker authentication
        run: |
          gcloud auth configure-docker europe-west1-docker.pkg.dev

      - name: Run Deploy
        run: ./run build
        working-directory: backend

      - name: Notify
        uses: 8398a7/action-slack@v3
        if: ${{ failure() }}
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          if_mention: failure
          author_name: Backend Build Failed ☹️
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  build_frontend:
    name: Build Frontend
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set Node Version
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - uses: google-github-actions/auth@v1
        with:
          project_id: ${{ secrets.GOOGLE_PROJECT_ID }}
          credentials_json: ${{ secrets.APPLICATION_CREDENTIALS }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker authentication
        run: |
          gcloud auth configure-docker europe-west1-docker.pkg.dev

      - name: Run Build
        run: ./run build
        working-directory: frontend

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: build
          path: frontend/build/

      - name: Notify Errors
        uses: 8398a7/action-slack@v3
        if: ${{ failure() }}
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          if_mention: failure
          author_name: Frontend Build Failed 😤
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  deploy_e2e:
    name: Deploy E2E
    needs: [tests_backend, tests_frontend, build_backend, build_frontend]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set Node Version
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - uses: google-github-actions/auth@v1
        with:
          project_id: ${{ secrets.GOOGLE_PROJECT_ID }}
          credentials_json: ${{ secrets.APPLICATION_CREDENTIALS }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker authentication
        run: |
          gcloud auth configure-docker europe-west1-docker.pkg.dev

      - name: Download Frontend Artifact
        uses: actions/download-artifact@v4
        with:
          name: build
          path: frontend/build/

      - name: Deploy Backend
        run: ./run deploy:e2e
        working-directory: backend

      - name: Deploy Frontend
        run: ./run deploy:e2e
        working-directory: frontend

      - name: Notify
        uses: 8398a7/action-slack@v3
        if: ${{ failure() }}
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          if_mention: failure
          author_name: Deploy E2E Failed 😭
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  tests_e2e:
    name: Tests E2E
    needs: [deploy_e2e]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - uses: google-github-actions/auth@v1
        with:
          project_id: ${{ secrets.GOOGLE_PROJECT_ID }}
          credentials_json: ${{ secrets.APPLICATION_CREDENTIALS }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Set GCP env
        run: echo "GOOGLE_APPLICATION_CREDENTIALS_JSON=$(cat ${{ env.GOOGLE_APPLICATION_CREDENTIALS }})" >> $GITHUB_ENV

      - uses: mattes/gce-cloudsql-proxy-action@v1
        with:
          creds: ${{ env.GOOGLE_APPLICATION_CREDENTIALS_JSON }}
          instance: hablo-279710:europe-west1:hablo
          port: 3333

      - name: Run Tests
        uses: cypress-io/github-action@v5
        with:
          browser: chrome
          working-directory: e2e
          command: yarn test:production --record=true --config video=true
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}

      - name: Notify
        uses: 8398a7/action-slack@v3
        if: ${{ failure() }}
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          if_mention: failure
          author_name: Backend E2E Tests Failed 🧐
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  deploy_production:
    name: Deploy Production
    needs: [tests_e2e]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set Node Version
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - uses: google-github-actions/auth@v1
        with:
          project_id: ${{ secrets.GOOGLE_PROJECT_ID }}
          credentials_json: ${{ secrets.APPLICATION_CREDENTIALS }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker authentication
        run: |
          gcloud auth configure-docker europe-west1-docker.pkg.dev

      - name: Download Frontend Artifact
        uses: actions/download-artifact@v4
        with:
          name: build
          path: frontend/build/

      - name: Deploy Backend
        run: ./run deploy:production
        working-directory: backend

      - name: Deploy Frontend
        run: ./run deploy:production
        working-directory: frontend

      - name: Set source maps
        id: source_maps
        working-directory: frontend
        run: |
          echo "DATA_SOURCE_MAPS=$(find ./build/js/*.js.map | tr '\n' ' ')" >> $GITHUB_ENV
          echo "DATA_SOURCE_MAPS_URLS=$(cd ./build && find ./js/*.js | sed -e 's/.\/js/\/\/myhablo.com\/js/' | tr '\n' ' ')" >> $GITHUB_ENV

      - name: Rollbar deploy
        uses: rollbar/github-deploy-action@2.1.2
        with:
          environment: production
          status: succeeded
          version: ${{ github.sha }}
          source_maps: ${{ steps.source_maps.env.DATA_SOURCE_MAPS }}
          minified_urls: ${{ steps.source_maps.env.DATA_SOURCE_MAPS_URLS }}
        env:
          ROLLBAR_USERNAME: ${{ github.actor }}
          ROLLBAR_ACCESS_TOKEN: ${{ secrets.ROLLBAR_ACCESS_TOKEN }}

      - name: Notify Errors
        uses: 8398a7/action-slack@v3
        if: ${{ failure() }}
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          if_mention: failure
          author_name: Production Deploy Failed 😢
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify Success
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          author_name: Production Deployed 🤩🎉😎
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
