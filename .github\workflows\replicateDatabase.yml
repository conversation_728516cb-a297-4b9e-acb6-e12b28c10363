name: Replicate Database
on:
  workflow_dispatch:
  push:
    branches: [beta]
    paths: [".github/workflows/replicateDatabase.yml"]
  schedule:
    - cron: "30 5 * * *"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

defaults:
  run:
    shell: bash
    working-directory: backend

jobs:
  replicate:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v3

      - name: Set Node Version
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - uses: google-github-actions/auth@v1
        with:
          project_id: ${{ secrets.GOOGLE_PROJECT_ID }}
          credentials_json: ${{ secrets.APPLICATION_CREDENTIALS }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Set GCP env
        run: echo "GOOGLE_APPLICATION_CREDENTIALS_JSON=$(cat ${{ env.GOOGLE_APPLICATION_CREDENTIALS }})" >> $GITHUB_ENV

      - uses: mattes/gce-cloudsql-proxy-action@v1
        with:
          creds: ${{ env.GOOGLE_APPLICATION_CREDENTIALS_JSON }}
          instance: hablo-279710:europe-west1:hablo
          port: 5432

      - name: Replicate Database To Beta
        run: ./run replicateDatabaseTo:beta

      - name: Notify Errors
        uses: 8398a7/action-slack@v3
        if: ${{ failure() }}
        with:
          status: ${{ job.status }}
          channel: "#dev-ci"
          if_mention: failure
          author_name: Replicate Database To Beta Failed 🥺
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
