"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AchievementsResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const achievements_service_1 = require("./achievements.service");
const run_achievement_migrations_input_1 = require("./args/run-achievement-migrations-input");
const achievements_model_1 = require("./models/achievements.model");
const organisations_service_1 = require("../organisations/organisations.service");
const achievements_args_1 = require("./args/achievements.args");
let AchievementsResolver = class AchievementsResolver {
    async getAchievements(user) {
        this.logger.verbose('AchievementsResolver.getAchievements (query)', {
            user: user.toLogObject(),
        });
        return await this.achievementsService.getAchievements({
            profileId: user.profileId,
        });
    }
    async getOrgAchievements(user) {
        this.logger.verbose('AchievementsResolver.getOrgAchievements (query)', {
            user: user.toLogObject(),
        });
        return await this.achievementsService.getOrgAchievements({
            profileId: user.profileId,
        });
    }
    async getArchivedAchievements(user, profileId) {
        this.logger.verbose('AchievementsResolver.getArchivedAchievements (query)', {
            user: user.toLogObject(),
        });
        const [achievements, orgAchievements] = await Promise.all([
            this.achievementsService.getAchievements({
                profileId: profileId,
            }),
            this.organisationsService.getClubHabloSubscriptionOrganisations(profileId),
        ]);
        const achievementResults = achievements.map(achievement => ({
            id: achievement.id,
            isAchieved: achievement.isAchieved,
            level: achievement.level,
            steps: achievement.steps,
            stepsComplete: achievement.stepsComplete,
            achievedDate: achievement.achievedDate,
            type: achievement.type,
            number: achievement.number,
            createdAt: achievement.createdAt,
            updatedAt: achievement.updatedAt,
        }));
        const orgResults = orgAchievements.map(org => ({
            id: org.id,
            isAchieved: org.isAchieved === undefined ? false : org.isAchieved,
            level: org.level,
            name: org.name,
            image: org.image,
            type: 'Organisation',
            createdAt: org.createdAt,
            updatedAt: org.updatedAt,
            achievedCounts: org.achievedCounts,
            totalCounts: org.totalCounts,
        }));
        return [...achievementResults, ...orgResults].filter(result => result.isAchieved);
    }
    async runAchievementMigrations(user, data) {
        this.logger.verbose('AchievementsResolver.runAchievementMigrations (mutation)', {
            user: user.toLogObject(),
            data,
        });
        await this.achievementsService.runAchievementMigrations({
            data,
            profileId: user.profileId,
        });
        return true;
    }
    async addHighFiveAchievement(user, profileId, organisationId) {
        this.logger.verbose('AchievementsResolver.addHighFiveAchievement (mutation)', {
            user: user.toLogObject(),
        });
        return await this.achievementsService.addHighFiveAchievement({
            currentUser: user,
            profileId,
            organisationId,
        });
    }
};
exports.AchievementsResolver = AchievementsResolver;
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], AchievementsResolver.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => achievements_service_1.AchievementsService)),
    __metadata("design:type", achievements_service_1.AchievementsService)
], AchievementsResolver.prototype, "achievementsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], AchievementsResolver.prototype, "organisationsService", void 0);
__decorate([
    (0, graphql_1.Query)(() => [achievements_model_1.Achievement]),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AchievementsResolver.prototype, "getAchievements", null);
__decorate([
    (0, graphql_1.Query)(() => [achievements_model_1.Achievement]),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AchievementsResolver.prototype, "getOrgAchievements", null);
__decorate([
    (0, graphql_1.Query)(() => [achievements_args_1.AchievementResult]),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('profileId', { nullable: false })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], AchievementsResolver.prototype, "getArchivedAchievements", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('data')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, run_achievement_migrations_input_1.RunAchievementMigrationsInput]),
    __metadata("design:returntype", Promise)
], AchievementsResolver.prototype, "runAchievementMigrations", null);
__decorate([
    (0, graphql_1.Mutation)(() => achievements_model_1.Achievement),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('profileId')),
    __param(2, (0, graphql_1.Args)('organisationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], AchievementsResolver.prototype, "addHighFiveAchievement", null);
exports.AchievementsResolver = AchievementsResolver = __decorate([
    (0, graphql_1.Resolver)(() => achievements_model_1.Achievement)
], AchievementsResolver);
//# sourceMappingURL=achievements.resolver.js.map