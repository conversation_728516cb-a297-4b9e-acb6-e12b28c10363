"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AchievementsService = void 0;
const common_1 = require("@nestjs/common");
const moment_timezone_1 = __importDefault(require("moment-timezone"));
const nest_winston_1 = require("nest-winston");
const sequelize_typescript_1 = require("sequelize-typescript");
const winston_1 = require("winston");
const base_service_1 = require("../common/base.service");
const error_1 = require("../common/helpers/error");
const notificationLayoutHelper_1 = require("../email/helpers/notificationLayoutHelper");
const notifications_args_1 = require("../notifications/args/notifications.args");
const notifications_service_1 = require("../notifications/notifications.service");
const profiles_service_1 = require("../profiles/profiles.service");
const achievements_args_1 = require("./args/achievements.args");
const achievements_model_1 = require("./models/achievements.model");
const sequelize_1 = require("sequelize");
const webinars_service_1 = require("../webinars/webinars.service");
const posts_service_1 = require("../posts/posts.service");
const organisations_service_1 = require("../organisations/organisations.service");
const followers_service_1 = require("../followers/followers.service");
const activities_service_1 = require("../activities/activities.service");
const activities_args_1 = require("../activities/args/activities.args");
const underscore_1 = require("../common/helpers/underscore");
const create_user_activity_data_dto_1 = require("../activities/dto/create-user-activity-data.dto");
const follower_model_1 = require("../followers/models/follower.model");
const organisation_model_1 = require("../organisations/models/organisation.model");
let AchievementsService = class AchievementsService extends (0, base_service_1.BaseService)(achievements_model_1.Achievement) {
    async getAchievements({ profileId, }) {
        try {
            this.logger.verbose('AchievementsResolver.getAchievements', {
                profileId,
            });
            return await this.findAll({
                profileId,
                organisationId: null,
            }, {
                includeParams: [
                    {
                        model: organisation_model_1.Organisation,
                        as: 'organisation',
                        attributes: ['name', 'id', 'vanityId', 'image'],
                    },
                ],
                order: [['createdAt', 'DESC']],
            });
        }
        catch (error) {
            this.errorHelper.throwHttpException('AchievementsService.getAchievements', error.message);
        }
    }
    async getOrgAchievements({ profileId, }) {
        try {
            this.logger.verbose('AchievementsResolver.getOrgAchievements', {
                profileId,
            });
            const achievements = await this.findAll({
                profileId,
                organisationId: {
                    [sequelize_1.Op.ne]: null,
                },
            }, {
                includeParams: [
                    {
                        model: organisation_model_1.Organisation,
                        as: 'organisation',
                        attributes: ['name', 'id', 'vanityId', 'image'],
                    },
                ],
                order: [['createdAt', 'DESC']],
            });
            const levelMapping = {
                AvidViewer: [
                    { steps: 175, level: 9 },
                    { steps: 150, level: 8 },
                    { steps: 125, level: 7 },
                    { steps: 100, level: 6 },
                    { steps: 75, level: 5 },
                    { steps: 50, level: 4 },
                    { steps: 25, level: 3 },
                    { steps: 10, level: 2 },
                    { steps: 5, level: 1 },
                ],
                HighFive: [
                    { steps: 100, level: 10 },
                    { steps: 50, level: 9 },
                    { steps: 35, level: 8 },
                    { steps: 25, level: 7 },
                    { steps: 20, level: 6 },
                    { steps: 15, level: 5 },
                    { steps: 10, level: 4 },
                    { steps: 5, level: 3 },
                    { steps: 3, level: 2 },
                    { steps: 1, level: 1 },
                ],
            };
            return achievements.map(achievement => {
                var _a;
                let level = 0;
                if (achievement.type === achievements_args_1.AchievementType.AvidViewer ||
                    achievement.type === achievements_args_1.AchievementType.HighFive) {
                    level =
                        ((_a = levelMapping[achievement.type].find(({ steps }) => achievement.stepsComplete >= steps)) === null || _a === void 0 ? void 0 : _a.level) || null;
                }
                else if (achievement.type === achievements_args_1.AchievementType.IncentiveWarrior) {
                    level = achievement.stepsComplete;
                }
                else if (achievement.type === achievements_args_1.AchievementType.Ambassador ||
                    achievement.type === achievements_args_1.AchievementType.PowerScroller) {
                    level = achievement.number;
                }
                else {
                    level = null;
                }
                return Object.assign(Object.assign({}, achievement.toJSON()), { level });
            });
        }
        catch (error) {
            this.errorHelper.throwHttpException('AchievementsService.getOrgAchievements', error.message);
        }
    }
    async runAchievementMigrations({ profileId, data, }) {
        this.logger.verbose('AchievementsResolver.runAchievementMigrations', {
            profileId,
            data,
        });
        try {
            const migrationFlags = {};
            const migrations = data.migrations;
            for (const migration of migrations) {
                this.logger.verbose(`Running migration: ${migration}`);
                switch (migration) {
                    case 'profile-perfect':
                        await this.addProfilePerfectAchievement({
                            profileId,
                        });
                        migrationFlags['profilePerfectMigration'] = true;
                        break;
                    case 'hablo-founder':
                        await this.addHabloFounderAchievement({
                            profileId,
                        });
                        migrationFlags['habloFounderMigration'] = true;
                        break;
                    default:
                        this.logger.warn(`Unknown migration: ${migration}`);
                        break;
                }
            }
            await this.profilesService.updateById(profileId, {
                migrationFlags,
            });
        }
        catch (error) {
            this.errorHelper.throwHttpException('AchievementsService.runAchievementMigrations', error.message);
        }
    }
    async sendExistingAchievementsNotifications({ profileId, }) {
        try {
            const achievementNotifications = await this.notificationsService.count({
                ownerProfileId: profileId,
                type: notifications_args_1.NotificationType.NewAchievement,
            });
            if (achievementNotifications) {
                this.logger.info('AchievementsService.sendExistingAchievementsNotifications', {
                    profileId,
                    message: 'Achievement notifications already sent.',
                });
                return true;
            }
            const achievements = await this.findAll({
                profileId,
                [sequelize_1.Op.or]: [
                    {
                        number: {
                            [sequelize_1.Op.not]: null,
                            [sequelize_1.Op.lte]: sequelize_typescript_1.Sequelize.col('stepsComplete'),
                        },
                    },
                    {
                        number: null,
                        stepsComplete: {
                            [sequelize_1.Op.gte]: sequelize_typescript_1.Sequelize.col('steps'),
                        },
                    },
                ],
            });
            for (const achievement of achievements) {
                await this.sendNotification({
                    achievementType: achievements_args_1.AchievementName[achievement.type],
                    profileId,
                });
            }
            return true;
        }
        catch (error) {
            this.logger.error('AchievementsService.sendExistingAchievementsNotifications', {
                profileId,
                error: error.message,
            });
        }
    }
    async addProfilePerfectAchievement({ profileId, }) {
        this.logger.debug('AchievementsService.addProfilePerfectAchievement', {
            profileId,
        });
        try {
            const profile = await this.profilesService.findById(profileId);
            if (!profile) {
                this.logger.error(`Profile not found for id ${profileId}`);
                throw new Error('Profile not found.');
            }
            let completedSteps = 0;
            if (profile.image && !profile.image.includes('gravatar.com')) {
                completedSteps++;
            }
            if (profile.backgroundImage) {
                completedSteps++;
            }
            if (profile.headline) {
                completedSteps++;
            }
            if (profile.bio && profile.bio.split(' ').length > 30) {
                completedSteps++;
            }
            if (profile.primaryMembershipId) {
                completedSteps++;
            }
            const achievement = await this.findOne({
                type: achievements_args_1.AchievementType.ProfilePerfect,
                profileId,
            }, {
                order: [['createdAt', 'DESC']],
            });
            if (achievement) {
                if (achievement.stepsComplete === completedSteps) {
                    this.logger.info('ProfilePerfect achievement already up to date.');
                    return achievement;
                }
                let updatedAchievement = await this.updateById(achievement.id, {
                    stepsComplete: completedSteps,
                });
                if (completedSteps === 5 && profile.sellHolidays) {
                    updatedAchievement = await this.updateById(achievement.id, {
                        isAchieved: true,
                        achievedDate: new Date(),
                    });
                    await this.activitiesService.createUserActivity({
                        type: activities_args_1.ActivityType.ProfileCompletion,
                        schema: create_user_activity_data_dto_1.emptyActivityDataDto,
                        data: {},
                        profileId,
                        addLoyaltyPoints: true,
                        checkForUniqueEntry: true,
                    });
                    await this.sendNotification({
                        achievementType: achievements_args_1.AchievementName.ProfilePerfect,
                        profileId,
                    });
                }
                return updatedAchievement;
            }
            if (!completedSteps) {
                this.logger.info('ProfilePerfect achievement not completed.');
                return null;
            }
            const newAchievement = await this.create({
                type: achievements_args_1.AchievementType.ProfilePerfect,
                steps: 5,
                stepsComplete: completedSteps,
                profileId,
                achievedDate: completedSteps === 5 ? new Date() : null,
            });
            if (completedSteps === 5 && profile.sellHolidays) {
                await this.activitiesService.createUserActivity({
                    type: activities_args_1.ActivityType.ProfileCompletion,
                    schema: create_user_activity_data_dto_1.emptyActivityDataDto,
                    data: {},
                    profileId,
                    addLoyaltyPoints: true,
                    checkForUniqueEntry: true,
                });
                await this.sendNotification({
                    achievementType: achievements_args_1.AchievementName.ProfilePerfect,
                    profileId,
                });
            }
            return newAchievement;
        }
        catch (error) {
            this.errorHelper.throwHttpException('AchievementsService.addProfilePerfectAchievement', error.message);
        }
    }
    async addHabloFounderAchievement({ profileId, }) {
        this.logger.debug('AchievementsService.addHabloFounderAchievement', {
            profileId,
        });
        try {
            const achievement = await this.findOne({
                type: achievements_args_1.AchievementType.HabloFounder,
                profileId,
            });
            if (achievement) {
                this.logger.info('HabloFounder achievement already exists.');
                return achievement;
            }
            const profile = await this.profilesService.findById(profileId);
            if (!profile) {
                this.logger.error(`Profile not found for id ${profileId}`);
                throw new Error('Profile not found.');
            }
            const timezone = profile.timezone || 'UTC';
            const profileCreatedAt = (0, moment_timezone_1.default)(profile.createdAt).tz(timezone);
            const habloLaunchDate = (0, moment_timezone_1.default)('2023-01-01').tz(timezone);
            if (profileCreatedAt.isAfter(habloLaunchDate)) {
                this.logger.info('Profile created after Hablo launch date.');
                return null;
            }
            const newAchievement = await this.create({
                type: achievements_args_1.AchievementType.HabloFounder,
                steps: 1,
                stepsComplete: 1,
                profileId,
                isAchieved: true,
                achievedDate: new Date(),
            });
            if (profile.sellHolidays) {
                await this.sendNotification({
                    achievementType: achievements_args_1.AchievementName.HabloFounder,
                    profileId,
                });
            }
            return newAchievement;
        }
        catch (error) {
            this.errorHelper.throwHttpException('AchievementsService.addHabloFounderAchievement', error.message);
        }
    }
    async addDailyQuizStreakAchievement({ profileId, }) {
        this.logger.debug('AchievementsService.addDailyQuizStreakAchievement', {
            profileId,
        });
        try {
            const achievement = await this.findOne({
                type: achievements_args_1.AchievementType.DailyQuizStreak,
                profileId,
            });
            if (!achievement) {
                return await this.create({
                    type: achievements_args_1.AchievementType.DailyQuizStreak,
                    steps: 5,
                    stepsComplete: 1,
                    profileId,
                    number: 0,
                    achievedDate: null,
                });
            }
            const profile = await this.profilesService.findById(profileId);
            if (!profile) {
                this.logger.error(`Profile not found for id ${profileId}`);
                throw new Error('Profile not found.');
            }
            const timezone = profile.timezone || 'UTC';
            const now = moment_timezone_1.default.tz(timezone);
            const today = now.startOf('day');
            const lastUpdatedDay = (0, moment_timezone_1.default)(achievement.updatedAt)
                .tz(timezone)
                .startOf('day');
            if (today.isSame(lastUpdatedDay, 'day') &&
                achievement.stepsComplete !== 0) {
                this.logger.info('User has already completed today’s quiz. No update needed.');
                return achievement;
            }
            if (now.day() === 0 || now.day() === 6) {
                this.logger.info('No progress updates on weekends.');
                return achievement;
            }
            if (today.diff(lastUpdatedDay, 'days') > 1) {
                this.logger.info('DailyQuizStreak achievement streak broken.');
                return await this.updateById(achievement.id, {
                    stepsComplete: 1,
                });
            }
            const newStepsComplete = achievement.stepsComplete + 1;
            let updatedAchievement = await this.updateById(achievement.id, {
                stepsComplete: newStepsComplete,
            });
            if (newStepsComplete === 5) {
                updatedAchievement = await this.updateById(achievement.id, {
                    number: achievement.number + 1,
                    achievedDate: new Date(),
                    isAchieved: true,
                });
                if (profile.sellHolidays) {
                    await this.sendNotification({
                        achievementType: achievements_args_1.AchievementName.DailyQuizStreak,
                        profileId,
                    });
                }
            }
            return updatedAchievement;
        }
        catch (error) {
            this.errorHelper.throwHttpException('AchievementsService.addDailyQuizStreakAchievement', error.message);
        }
    }
    async addHotStreakAchievement({ profileId, }) {
        this.logger.debug('AchievementsService.addHotStreakAchievement', {
            profileId,
        });
        try {
            const achievement = await this.findOne({
                type: achievements_args_1.AchievementType.HotStreak,
                profileId,
            });
            if (!achievement) {
                const newAchievement = await this.create({
                    type: achievements_args_1.AchievementType.HotStreak,
                    steps: 1,
                    stepsComplete: 1,
                    profileId,
                    number: 5,
                    achievedDate: null,
                });
                return newAchievement;
            }
            const profile = await this.profilesService.findById(profileId);
            if (!profile) {
                throw new Error('Profile not found.');
            }
            const timezone = profile.timezone || 'UTC';
            const lastEntry = (0, moment_timezone_1.default)(achievement.updatedAt)
                .tz(timezone)
                .startOf('day');
            const today = moment_timezone_1.default.tz(timezone).startOf('day');
            if (today.isSame(lastEntry, 'day')) {
                return achievement;
            }
            if (today.diff(lastEntry, 'days') === 1) {
                const newStepsComplete = achievement.stepsComplete + 1;
                if (newStepsComplete > achievement.number ||
                    (newStepsComplete === 5 && achievement.number === 5)) {
                    await this.updateById(achievement.id, {
                        stepsComplete: newStepsComplete,
                        number: newStepsComplete,
                        isAchieved: true,
                        achievedDate: new Date(),
                    });
                }
                else {
                    await this.updateById(achievement.id, {
                        stepsComplete: newStepsComplete,
                    });
                }
                if (newStepsComplete === achievement.number && profile.sellHolidays) {
                    await this.sendNotification({
                        achievementType: achievements_args_1.AchievementName.HotStreak,
                        profileId,
                    });
                }
                return achievement;
            }
            if (today.diff(lastEntry, 'days') > 1) {
                return await this.updateById(achievement.id, {
                    stepsComplete: 1,
                });
            }
            return achievement;
        }
        catch (error) {
            this.errorHelper.throwHttpException('AchievementsService.addHotStreakAchievement', error.message);
        }
    }
    async addCommunityBeaconAchievement({ profileId, }) {
        this.logger.debug('AchievementsService.addCommunityBeaconAchievement', {
            profileId,
        });
        try {
            const achievement = await this.findOne({
                type: achievements_args_1.AchievementType.CommunityBeacon,
                profileId,
            });
            if (!achievement) {
                const newAchievement = await this.create({
                    type: achievements_args_1.AchievementType.CommunityBeacon,
                    steps: 0,
                    stepsComplete: 0,
                    profileId,
                    number: 5,
                    achievedDate: null,
                });
                return newAchievement;
            }
            const profile = await this.profilesService.findById(profileId);
            if (!profile) {
                throw new Error('Profile not found.');
            }
            const ConnectionSequence = [5, 10, 25, 50, 100, 150, 250, 500, 1000];
            if (profile.connectionsCount) {
                const newStepsComplete = profile.connectionsCount + 1;
                const isAchieved = newStepsComplete >= 5;
                if (ConnectionSequence.includes(newStepsComplete)) {
                    const updatedAchievement = await this.updateById(achievement.id, {
                        stepsComplete: newStepsComplete,
                        number: newStepsComplete,
                        isAchieved,
                        achievedDate: new Date(),
                    });
                    await this.sendNotification({
                        achievementType: achievements_args_1.AchievementName.CommunityBeacon,
                        profileId,
                    });
                    return updatedAchievement;
                }
                const updatedAchievement = await this.updateById(achievement.id, {
                    stepsComplete: newStepsComplete,
                    isAchieved,
                });
                return updatedAchievement;
            }
            return achievement;
        }
        catch (error) {
            this.errorHelper.throwHttpException('AchievementsService.addCommunityBeaconAchievement', error.message);
        }
    }
    async upsertIndustryInfluencerAchievement({ profileId, }) {
        this.logger.debug('AchievementsService.upsertIndustryInfluencerAchievement', {
            profileId,
        });
        try {
            const achievement = await this.findOne({
                type: achievements_args_1.AchievementType.IndustryInfluencer,
                profileId,
            });
            if (!achievement) {
                const newAchievement = await this.create({
                    type: achievements_args_1.AchievementType.IndustryInfluencer,
                    steps: 10,
                    stepsComplete: 1,
                    profileId,
                    achievedDate: null,
                });
                await this.sendNotification({
                    achievementType: achievements_args_1.AchievementName.IndustryInfluencer,
                    profileId,
                });
                return newAchievement;
            }
            else {
                if (achievement.stepsComplete < achievement.steps) {
                    const updatedAchievement = await this.updateById(achievement.id, {
                        stepsComplete: achievement.stepsComplete + 1,
                        isAchieved: achievement.steps === achievement.stepsComplete + 1
                            ? true
                            : false,
                        achievedDate: achievement.steps === achievement.stepsComplete + 1
                            ? new Date()
                            : achievement.achievedDate,
                    });
                    if (updatedAchievement.isAchieved) {
                        await this.sendNotification({
                            achievementType: achievements_args_1.AchievementName.IndustryInfluencer,
                            profileId,
                        });
                    }
                    return updatedAchievement;
                }
            }
            return achievement;
        }
        catch (error) {
            this.errorHelper.throwHttpException('AchievementsService.addIndustryInfluencerAchievement', error.message);
        }
    }
    async addWellbeingBadgeAchievement(weekStartDate) {
        this.logger.debug('AchievementsService.addWellbeingBadgeAchievement');
        console.log('weekStartDate', weekStartDate);
        try {
            const staticOrganisation = await this.organisationsService.findOne({
                name: 'Hablo Wellbeing | 𝐈𝐧𝐬𝐩𝐢𝐫𝐞𝐝 𝐛𝐲 𝐌𝐚𝐮𝐫𝐢𝐭𝐢𝐮𝐬',
            }, {
                attributes: ['id'],
            });
            const followers = await this.followersService.findAll({
                organisationId: staticOrganisation.id,
            }, {
                attributes: ['profileId'],
            });
            const profileIds = underscore_1.Underscore.uniq(underscore_1.Underscore.map(followers, 'profileId'));
            let previousWeekStart;
            let previousWeekEnd;
            if (!weekStartDate) {
                previousWeekStart = (0, moment_timezone_1.default)()
                    .subtract(1, 'week')
                    .startOf('isoWeek')
                    .toDate();
                previousWeekEnd = (0, moment_timezone_1.default)()
                    .subtract(1, 'week')
                    .endOf('isoWeek')
                    .toDate();
            }
            else {
                previousWeekStart = weekStartDate
                    ? (0, moment_timezone_1.default)(weekStartDate).startOf('isoWeek').toDate()
                    : (0, moment_timezone_1.default)().subtract(1, 'week').startOf('isoWeek').toDate();
                previousWeekEnd = weekStartDate
                    ? (0, moment_timezone_1.default)(weekStartDate).endOf('isoWeek').toDate()
                    : (0, moment_timezone_1.default)().subtract(1, 'week').endOf('isoWeek').toDate();
            }
            const webinars = await this.webinarsService.findAll({
                organisationId: staticOrganisation.id,
                createdAt: {
                    [sequelize_1.Op.between]: [previousWeekStart, previousWeekEnd],
                },
            }, {
                attributes: ['id'],
            });
            const webinarIds = underscore_1.Underscore.uniq(underscore_1.Underscore.map(webinars, 'id'));
            const posts = await this.postsService.findAll({
                organisationId: staticOrganisation.id,
                createdAt: {
                    [sequelize_1.Op.between]: [previousWeekStart, previousWeekEnd],
                },
            }, {
                attributes: ['id'],
            });
            const postIds = underscore_1.Underscore.uniq(underscore_1.Underscore.map(posts, 'id'));
            if (webinarIds.length === 0 && postIds.length === 0) {
                return null;
            }
            for (const follower of profileIds) {
                let webinarActivityData;
                if (webinarIds.length) {
                    webinarActivityData = await this.activitiesService.findAll({
                        profileId: follower,
                        type: activities_args_1.ActivityType.WebinarCompleted,
                        webinarId: {
                            [sequelize_1.Op.in]: webinarIds,
                        },
                    }, {
                        attributes: ['webinarId'],
                    });
                    const webinarUniqueIds = underscore_1.Underscore.uniq(underscore_1.Underscore.map(webinarActivityData, 'webinarId'));
                    if ((webinarUniqueIds === null || webinarUniqueIds === void 0 ? void 0 : webinarUniqueIds.length) !== webinarIds.length) {
                        continue;
                    }
                }
                let postActivityData;
                if (postIds.length) {
                    postActivityData = await this.activitiesService.findAll({
                        profileId: follower,
                        type: activities_args_1.ActivityType.PostView,
                        postId: {
                            [sequelize_1.Op.in]: postIds,
                        },
                    }, {
                        attributes: ['postId'],
                    });
                    const postUniqueIds = underscore_1.Underscore.uniq(underscore_1.Underscore.map(postActivityData, 'postId'));
                    if ((postUniqueIds === null || postUniqueIds === void 0 ? void 0 : postUniqueIds.length) !== postIds.length) {
                        continue;
                    }
                }
                const achievement = await this.findOne({
                    type: achievements_args_1.AchievementType.WellbeingBadge,
                    profileId: follower,
                });
                if (!achievement) {
                    await this.create({
                        type: achievements_args_1.AchievementType.WellbeingBadge,
                        steps: 1,
                        stepsComplete: 1,
                        number: 1,
                        profileId: follower,
                        isAchieved: true,
                        achievedDate: new Date(),
                    });
                    await this.sendNotification({
                        achievementType: achievements_args_1.AchievementName.WellbeingBadge,
                        profileId: follower,
                    });
                }
                else {
                    await this.updateById(achievement.id, {
                        stepsComplete: achievement.stepsComplete + 1,
                        number: achievement.stepsComplete + 1,
                        isAchieved: true,
                        achievedDate: new Date(),
                    });
                }
            }
            return null;
        }
        catch (error) {
            console.error('error-----------', error);
            this.errorHelper.throwHttpException('AchievementsService.addWellbeingBadgeAchievement', error.message);
        }
    }
    async addPowerScrollerAchievement() {
        this.logger.debug('AchievementsService.addPowerScrollerAchievement started');
        try {
            const batchSize = 3000;
            let offset = 0;
            let batchNumber = 1;
            let hasMoreRecords = true;
            const totalCount = await this.count({
                type: achievements_args_1.AchievementType.PowerScroller,
            });
            this.logger.info(`Total PowerScroller achievements to process: ${totalCount}`);
            while (hasMoreRecords) {
                this.logger.info(`Processing batch ${batchNumber} (offset: ${offset}, limit: ${batchSize})`);
                const achievements = await achievements_model_1.Achievement.findAll({
                    where: {
                        type: achievements_args_1.AchievementType.PowerScroller,
                    },
                    offset,
                    limit: batchSize,
                    order: [['id', 'ASC']],
                    include: [
                        {
                            model: organisation_model_1.Organisation,
                            as: 'organisation',
                        },
                    ],
                });
                if (achievements.length === 0) {
                    this.logger.info(`No more records found. Stopping migration at batch ${batchNumber}`);
                    hasMoreRecords = false;
                    break;
                }
                for (const achievement of achievements) {
                    try {
                        if (achievement.steps === achievement.stepsComplete &&
                            achievement.steps > 0) {
                            await this.updateById(achievement.id, {
                                number: achievement.number + 1,
                                isAchieved: true,
                                achievedDate: new Date(),
                            });
                            const organisation = achievement.organisation;
                            if (organisation) {
                                await this.sendNotification({
                                    achievementType: achievements_args_1.AchievementName.PowerScroller,
                                    profileId: achievement.profileId,
                                    organisationDetails: {
                                        id: organisation.id,
                                        name: organisation.name,
                                    },
                                });
                            }
                        }
                        else {
                            await this.updateById(achievement.id, {
                                isAchieved: false,
                            });
                        }
                        await this.updateById(achievement.id, {
                            stepsComplete: 0,
                            steps: 0,
                        });
                    }
                    catch (err) {
                        this.logger.error(`Error processing achievement ${achievement.id}:`, err);
                        continue;
                    }
                }
                await new Promise(resolve => setTimeout(resolve, 100));
                offset += batchSize;
                batchNumber++;
            }
            this.logger.info('PowerScroller achievement processing completed successfully');
        }
        catch (error) {
            this.logger.error('Error in PowerScroller achievement processing:', error);
            this.errorHelper.throwHttpException('AchievementsService.addPowerScrollerAchievement', error.message);
        }
    }
    async addAvidViewerAchievement({ profileId, organisationId, }) {
        this.logger.debug('AchievementsService.addAvidViewerAchievement', {
            profileId,
            organisationId,
        });
        try {
            const organisation = await this.organisationsService.findOne({
                id: organisationId,
                hasClubHabloSubscription: true,
            });
            if (!organisation) {
                return;
            }
            const followerDetails = await this.followersService.findOne({
                profileId,
                organisationId,
                status: follower_model_1.FollowerStatus.Active,
            });
            if (!followerDetails) {
                return;
            }
            const achievement = await this.findOne({
                type: achievements_args_1.AchievementType.AvidViewer,
                profileId,
                organisationId,
            });
            if (!achievement) {
                this.logger.info('Avid Viewer achievement not found.');
                const newAchievement = await this.create({
                    type: achievements_args_1.AchievementType.AvidViewer,
                    steps: 5,
                    stepsComplete: 1,
                    profileId,
                    organisationId,
                    number: 5,
                    achievedDate: null,
                });
                return newAchievement;
            }
            const webinarViewSequence = [5, 10, 25, 50, 75, 100, 125, 150, 175];
            this.logger.info('Avid Viewer achievement streak continued.');
            const newStepsComplete = achievement.stepsComplete + 1;
            const index = webinarViewSequence.indexOf(newStepsComplete);
            const nextNumber = index !== -1 && index < webinarViewSequence.length - 1
                ? webinarViewSequence[index + 1]
                : newStepsComplete;
            const isAchieved = newStepsComplete >= 5;
            if (webinarViewSequence.includes(newStepsComplete)) {
                const updatedAchievement = await this.updateById(achievement.id, {
                    steps: nextNumber,
                    stepsComplete: newStepsComplete,
                    number: newStepsComplete,
                    isAchieved,
                    achievedDate: new Date(),
                });
                await this.sendNotification({
                    achievementType: achievements_args_1.AchievementName.AvidViewer,
                    profileId,
                    organisationDetails: {
                        id: organisation.id,
                        name: organisation.name,
                    },
                });
                return updatedAchievement;
            }
            const updatedAchievement = await this.updateById(achievement.id, {
                stepsComplete: newStepsComplete,
                isAchieved,
            });
            return updatedAchievement;
        }
        catch (error) {
            this.errorHelper.throwHttpException('AchievementsService.addAvidViewerAchievement', error.message);
        }
    }
    async incrementStepsComplete({ profileId, organisationId, postId, }) {
        try {
            const organisation = await this.organisationsService.findById(organisationId);
            if (!organisation || !organisation.hasClubHabloSubscription) {
                return;
            }
            const follower = await this.followersService.findOne({
                profileId,
                organisationId,
                status: follower_model_1.FollowerStatus.Active,
            });
            if (!follower) {
                return;
            }
            let achievement = await this.findOne({
                profileId,
                organisationId,
                type: achievements_args_1.AchievementType.PowerScroller,
            });
            const currentMonthPosts = await this.postsService.getCurrentMonthPosts(organisation.id, (0, moment_timezone_1.default)().endOf('month').subtract(3, 'days').toDate());
            if (!achievement) {
                achievement = await this.create({
                    type: achievements_args_1.AchievementType.PowerScroller,
                    profileId,
                    organisationId,
                    steps: currentMonthPosts.length,
                    stepsComplete: 0,
                    isAchieved: false,
                    achievedDate: null,
                    number: 0,
                });
            }
            const post = await this.postsService.findById(postId);
            if (!post) {
                return;
            }
            const postCreatedAt = (0, moment_timezone_1.default)((post === null || post === void 0 ? void 0 : post.scheduledAt) || post.createdAt);
            const isPostCreatedThisMonth = postCreatedAt.isSame((0, moment_timezone_1.default)(), 'month');
            if (isPostCreatedThisMonth) {
                if (achievement.stepsComplete < achievement.steps) {
                    await this.updateById(achievement.id, {
                        steps: currentMonthPosts.length,
                        stepsComplete: achievement.stepsComplete + 1,
                    });
                }
            }
        }
        catch (error) {
            this.logger.error('Error finding achievement', {
                error: error.message,
                profileId,
                organisationId,
            });
        }
    }
    async decrementStepsComplete({ achievement, post, }) {
        try {
            const startOfMonth = (0, moment_timezone_1.default)().startOf('month').toDate();
            const endOfMonth = (0, moment_timezone_1.default)().endOf('month').toDate();
            const monthEndThreeDaysAgo = (0, moment_timezone_1.default)()
                .endOf('month')
                .subtract(3, 'days')
                .toDate();
            const { profileId, organisationId } = achievement;
            const [activities, currentMonthPost] = await Promise.all([
                this.activitiesService.findAll({
                    organisationId,
                    profileId,
                    type: activities_args_1.ActivityType.PostView,
                    createdAt: {
                        [sequelize_1.Op.between]: [startOfMonth, endOfMonth],
                    },
                }),
                this.postsService.getCurrentMonthPosts(organisationId, monthEndThreeDaysAgo),
            ]);
            const uniquePostIds = underscore_1.Underscore.uniq(activities.map(activity => activity.postId));
            const currentMonthPostIds = new Set(currentMonthPost);
            const viewedAndCreatedPostIds = Array.from(uniquePostIds).filter(postId => currentMonthPostIds.has(postId));
            const isCurrentPostViewedAndCreated = viewedAndCreatedPostIds.includes(post.id);
            const isCurrentPostInMonth = currentMonthPostIds.has(post.id);
            const uniquePostViewCount = viewedAndCreatedPostIds.length -
                (isCurrentPostViewedAndCreated ? 1 : 0);
            const steps = currentMonthPost.length - (isCurrentPostInMonth ? 1 : 0);
            await this.updateById(achievement.id, {
                stepsComplete: uniquePostViewCount,
                steps,
            });
        }
        catch (error) {
            this.logger.error('Error decrementing stepsComplete', {
                error: error.message,
                achievement: achievement.id,
            });
        }
    }
    async addIncentiveWarriorAchievement({ profileId, organisationId, }) {
        this.logger.debug('AchievementsService.addIncentiveWarriorAchievement', {
            profileId,
            organisationId,
        });
        try {
            const organisation = await this.organisationsService.findOne({
                id: organisationId,
                hasClubHabloSubscription: true,
            });
            if (!organisation) {
                return;
            }
            const followerDetails = await this.followersService.findOne({
                profileId,
                organisationId,
                status: follower_model_1.FollowerStatus.Active,
            });
            if (!followerDetails) {
                return;
            }
            let achievement = await this.findOne({
                type: achievements_args_1.AchievementType.IncentiveWarrior,
                profileId,
                organisationId,
            });
            if (!achievement) {
                achievement = await this.create({
                    type: achievements_args_1.AchievementType.IncentiveWarrior,
                    steps: 1,
                    stepsComplete: 1,
                    profileId,
                    organisationId,
                    number: 1,
                    isAchieved: true,
                    achievedDate: new Date(),
                });
            }
            else if (achievement) {
                await this.updateById(achievement.id, {
                    stepsComplete: achievement.stepsComplete + 1,
                    number: achievement.stepsComplete + 1,
                    achievedDate: new Date(),
                });
            }
            await this.sendNotification({
                achievementType: achievements_args_1.AchievementName.IncentiveWarrior,
                profileId,
                organisationDetails: {
                    id: organisation.id,
                    name: organisation.name,
                },
            });
            return achievement;
        }
        catch (error) {
            this.errorHelper.throwHttpException('AchievementsService.addIncentiveWarriorAchievement', error.message);
        }
    }
    async addAmbassadorAchievement() {
        try {
            const allAchievements = (await this.findAll()).filter(achievement => achievement.organisationId !== null);
            const groupedAchievements = allAchievements.reduce((acc, achievement) => {
                const key = `${achievement.profileId}-${achievement.organisationId}`;
                if (!acc[key]) {
                    acc[key] = [];
                }
                acc[key].push(achievement);
                return acc;
            }, {});
            const achievementTypes = [
                achievements_args_1.AchievementType.AvidViewer,
                achievements_args_1.AchievementType.PowerScroller,
                achievements_args_1.AchievementType.IncentiveWarrior,
            ];
            await Promise.all(Object.keys(groupedAchievements).map(async (key) => {
                const achievements = groupedAchievements[key];
                const [profileId, organisationId] = key.split('-');
                const groupedTypes = achievements.map(ach => ach.type);
                const missingTypes = achievementTypes.filter(type => !groupedTypes.includes(type));
                if (missingTypes.length === 0) {
                    const allAchieved = achievementTypes.every(type => {
                        const achievement = achievements.find(ach => ach.type === type);
                        return achievement && achievement.isAchieved;
                    });
                    const ambassador = await this.findOne({
                        type: achievements_args_1.AchievementType.Ambassador,
                        profileId,
                        organisationId,
                    });
                    const organisation = await this.organisationsService.findById(organisationId);
                    if (!organisation) {
                        return;
                    }
                    if (!ambassador) {
                        await this.create({
                            type: achievements_args_1.AchievementType.Ambassador,
                            profileId,
                            organisationId,
                            isAchieved: allAchieved,
                            achievedDate: allAchieved ? new Date() : null,
                            steps: 1,
                            stepsComplete: allAchieved ? 1 : 0,
                            number: 1,
                        });
                        if (allAchieved) {
                            await this.sendNotification({
                                achievementType: achievements_args_1.AchievementName.Ambassador,
                                profileId,
                                organisationDetails: {
                                    id: organisation.id,
                                    name: organisation.name,
                                },
                            });
                        }
                    }
                    else {
                        if (allAchieved) {
                            await this.updateById(ambassador.id, {
                                isAchieved: true,
                                achievedDate: new Date(),
                                number: ambassador.number + 1,
                                stepsComplete: ambassador.stepsComplete + 1,
                            });
                            await this.sendNotification({
                                achievementType: achievements_args_1.AchievementName.Ambassador,
                                profileId,
                                organisationDetails: {
                                    id: organisation.id,
                                    name: organisation.name,
                                },
                            });
                        }
                        else if (!allAchieved) {
                            await this.updateById(ambassador.id, {
                                isAchieved: false,
                            });
                        }
                    }
                }
            }));
            return [];
        }
        catch (error) {
            this.logger.error('Error processing achievements for Ambassador', error.message);
        }
    }
    async addHighFiveAchievement({ profileId, organisationId, currentUser, }) {
        this.logger.debug('AchievementsService.addHighFiveAchievement', {
            profileId,
            organisationId,
        });
        try {
            const clubHabloOrganisation = await this.organisationsService.findOne({
                id: organisationId,
                hasClubHabloSubscription: true,
            }, {
                attributes: ['id', 'name', 'image', 'vanityId', 'type'],
            });
            if (!clubHabloOrganisation) {
                return;
            }
            let achievement = await this.findOne({
                type: achievements_args_1.AchievementType.HighFive,
                profileId,
                organisationId,
            });
            const highFiveSequence = [1, 3, 5, 10, 15, 20, 25, 35, 50, 100];
            if (!achievement) {
                achievement = await this.create({
                    type: achievements_args_1.AchievementType.HighFive,
                    steps: 3,
                    stepsComplete: 1,
                    profileId,
                    organisationId,
                    number: 1,
                    isAchieved: true,
                    achievedDate: new Date(),
                });
            }
            else if (new Date(achievement.updatedAt).toDateString() !=
                new Date().toDateString()) {
                const newStepsComplete = achievement.stepsComplete + 1;
                const index = highFiveSequence.indexOf(newStepsComplete);
                const nextNumber = index !== -1 && index < highFiveSequence.length - 1
                    ? highFiveSequence[index + 1]
                    : highFiveSequence.find(step => step > newStepsComplete) ||
                        newStepsComplete;
                achievement = await this.updateById(achievement.id, {
                    steps: nextNumber,
                    stepsComplete: newStepsComplete,
                    number: newStepsComplete,
                    achievedDate: new Date(),
                });
            }
            else {
                this.logger.error(`High Five limit reached for today.`);
                throw new Error(`High Five limit reached for today.`);
            }
            const currentUserDetails = await this.profilesService.findById(currentUser.profileId);
            await this.activitiesService.createUserActivity({
                profileId,
                organisationId,
                type: activities_args_1.ActivityType.HighFive,
                data: {
                    type: activities_args_1.ActivityType.HighFive,
                    organisationId: clubHabloOrganisation.id,
                    organisationName: clubHabloOrganisation.name,
                    userId: currentUser.profileId,
                    userName: currentUserDetails.name,
                },
                addLoyaltyPoints: true,
                schema: create_user_activity_data_dto_1.highFiveActivityDataDto,
                placeholders: {
                    organisationId: clubHabloOrganisation.id,
                    organisationName: clubHabloOrganisation.name,
                    vanityId: clubHabloOrganisation.vanityId,
                    createdById: currentUser.profileId,
                    name: currentUserDetails.name,
                },
            });
            await this.notificationsService.createNotification({
                ownerProfileId: profileId,
                type: notifications_args_1.NotificationType.HighFiveAchievement,
                profileId: currentUserDetails.id,
                organisationId: clubHabloOrganisation.id,
                data: {
                    organisation: clubHabloOrganisation,
                    user: {
                        id: currentUserDetails.id,
                        name: currentUserDetails.name,
                        email: currentUserDetails.email,
                        image: currentUserDetails.image,
                    },
                },
            });
            await this.notificationsService.sendPushNotification({
                profileIds: [profileId],
                replacements: [currentUserDetails.name, clubHabloOrganisation.name],
                messageType: notifications_args_1.NotificationMessage.HighFiveAchievement,
                route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.clubHabloDashboard),
            });
            return achievement;
        }
        catch (error) {
            this.logger.error('Error processing achievements for High Five', error.message);
            this.errorHelper.throwHttpException('AchievementsService.addHighFiveAchievement', error.message);
        }
    }
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    async addOrganisationAchievements() {
        await this.addPowerScrollerAchievement();
        await this.delay(1 * 60 * 1000);
        await this.addAmbassadorAchievement();
        const usersWithNewAchievements = await this.findAll({
            type: {
                [sequelize_1.Op.in]: [achievements_args_1.AchievementType.PowerScroller, achievements_args_1.AchievementType.Ambassador],
            },
            isAchieved: true,
            updatedAt: { [sequelize_1.Op.gte]: new Date().setHours(0, 0, 0, 0) },
        }, { attributes: ['organisationId', 'profileId'] });
        const profileOrgMap = {};
        for (const { profileId, organisationId } of usersWithNewAchievements) {
            if (!profileOrgMap[profileId]) {
                profileOrgMap[profileId] = new Set();
            }
            profileOrgMap[profileId].add(organisationId);
        }
        for (const [profileId, orgSet] of Object.entries(profileOrgMap)) {
            await this.notificationsService.createNotification({
                ownerProfileId: profileId,
                type: notifications_args_1.NotificationType.MonthlyAchievementSummary,
                profileId,
                data: { uniqueOrgCount: orgSet.size },
            });
            await this.notificationsService.sendPushNotification({
                profileIds: [profileId],
                replacements: [`${orgSet.size}`],
                messageType: notifications_args_1.NotificationMessage.MonthlyAchievementSummary,
                route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.clubHabloDashboard),
            });
        }
    }
    async sendNotification({ achievementType, profileId, organisationDetails, }) {
        var _a, _b;
        try {
            const notificationType = organisationDetails
                ? notifications_args_1.NotificationType.NewOrganisationAchievement
                : notifications_args_1.NotificationType.NewAchievement;
            const achievementTypeMap = {
                [achievements_args_1.AchievementName.AvidViewer]: achievements_args_1.AchievementType.AvidViewer,
                [achievements_args_1.AchievementName.HighFive]: achievements_args_1.AchievementType.HighFive,
                [achievements_args_1.AchievementName.IncentiveWarrior]: achievements_args_1.AchievementType.IncentiveWarrior,
                [achievements_args_1.AchievementName.Ambassador]: achievements_args_1.AchievementType.Ambassador,
                [achievements_args_1.AchievementName.PowerScroller]: achievements_args_1.AchievementType.PowerScroller,
                [achievements_args_1.AchievementName.ProfilePerfect]: achievements_args_1.AchievementType.ProfilePerfect,
                [achievements_args_1.AchievementName.DailyQuizStreak]: achievements_args_1.AchievementType.DailyQuizStreak,
                [achievements_args_1.AchievementName.HotStreak]: achievements_args_1.AchievementType.HotStreak,
                [achievements_args_1.AchievementName.HabloFounder]: achievements_args_1.AchievementType.HabloFounder,
                [achievements_args_1.AchievementName.CommunityBeacon]: achievements_args_1.AchievementType.CommunityBeacon,
                [achievements_args_1.AchievementName.IndustryInfluencer]: achievements_args_1.AchievementType.IndustryInfluencer,
                [achievements_args_1.AchievementName.WellbeingBadge]: achievements_args_1.AchievementType.WellbeingBadge,
            };
            let level = null;
            if (organisationDetails === null || organisationDetails === void 0 ? void 0 : organisationDetails.id) {
                const achievement = await this.findOne({
                    type: achievementTypeMap[achievementType],
                    profileId,
                    organisationId: organisationDetails.id,
                });
                if (achievement) {
                    if (achievementType === achievements_args_1.AchievementName.AvidViewer ||
                        achievementType === achievements_args_1.AchievementName.HighFive) {
                        const levelMapping = {
                            [achievements_args_1.AchievementName.AvidViewer]: [
                                { steps: 175, level: 9 },
                                { steps: 150, level: 8 },
                                { steps: 125, level: 7 },
                                { steps: 100, level: 6 },
                                { steps: 75, level: 5 },
                                { steps: 50, level: 4 },
                                { steps: 25, level: 3 },
                                { steps: 10, level: 2 },
                                { steps: 5, level: 1 },
                            ],
                            [achievements_args_1.AchievementName.HighFive]: [
                                { steps: 100, level: 10 },
                                { steps: 50, level: 9 },
                                { steps: 35, level: 8 },
                                { steps: 25, level: 7 },
                                { steps: 20, level: 6 },
                                { steps: 15, level: 5 },
                                { steps: 10, level: 4 },
                                { steps: 5, level: 3 },
                                { steps: 3, level: 2 },
                                { steps: 1, level: 1 },
                            ],
                        };
                        level =
                            ((_b = (_a = levelMapping[achievementType]) === null || _a === void 0 ? void 0 : _a.find(({ steps }) => achievement.stepsComplete >= steps)) === null || _b === void 0 ? void 0 : _b.level) || null;
                    }
                    else if (achievementType === achievements_args_1.AchievementName.IncentiveWarrior) {
                        level = achievement.stepsComplete;
                    }
                    else if (achievementType === achievements_args_1.AchievementName.Ambassador ||
                        achievementType === achievements_args_1.AchievementName.PowerScroller) {
                        level = achievement.number;
                    }
                }
            }
            const data = { achievementType, level };
            await this.notificationsService.createNotification({
                ownerProfileId: profileId,
                type: notificationType,
                profileId,
                organisationId: organisationDetails === null || organisationDetails === void 0 ? void 0 : organisationDetails.id,
                data,
            });
            const replacements = organisationDetails
                ? [organisationDetails.name, achievementType]
                : [achievementType];
            if (!(organisationDetails === null || organisationDetails === void 0 ? void 0 : organisationDetails.id)) {
                await this.notificationsService.sendPushNotification({
                    profileIds: [profileId],
                    replacements,
                    messageType: notifications_args_1.NotificationMessage.NewAchievement,
                    route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.clubHabloDashboard),
                });
            }
        }
        catch (error) {
            this.errorHelper.throwHttpException('AchievementsService.sendNotification', error.message);
        }
    }
    async resetDailyQuizStreakAchievement() {
        this.logger.debug('AchievementsService.resetDailyQuizStreakAchievement');
        try {
            await this.update({
                where: {
                    type: achievements_args_1.AchievementType.DailyQuizStreak,
                },
                update: { stepsComplete: 0 },
            });
            return;
        }
        catch (error) {
            this.errorHelper.throwHttpException('AchievementsService.resetDailyQuizStreakAchievement', error.message);
        }
    }
};
exports.AchievementsService = AchievementsService;
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", sequelize_typescript_1.Sequelize)
], AchievementsService.prototype, "sequelize", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], AchievementsService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], AchievementsService.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], AchievementsService.prototype, "errorHelper", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => notifications_service_1.NotificationsService)),
    __metadata("design:type", notifications_service_1.NotificationsService)
], AchievementsService.prototype, "notificationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinars_service_1.WebinarsService)),
    __metadata("design:type", webinars_service_1.WebinarsService)
], AchievementsService.prototype, "webinarsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], AchievementsService.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => followers_service_1.FollowersService)),
    __metadata("design:type", followers_service_1.FollowersService)
], AchievementsService.prototype, "followersService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => posts_service_1.PostsService)),
    __metadata("design:type", posts_service_1.PostsService)
], AchievementsService.prototype, "postsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => activities_service_1.ActivitiesService)),
    __metadata("design:type", activities_service_1.ActivitiesService)
], AchievementsService.prototype, "activitiesService", void 0);
exports.AchievementsService = AchievementsService = __decorate([
    (0, common_1.Injectable)()
], AchievementsService);
//# sourceMappingURL=achievements.service.js.map