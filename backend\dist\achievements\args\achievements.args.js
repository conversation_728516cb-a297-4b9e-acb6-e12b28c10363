"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AchievementResult = exports.AchievementName = exports.AchievementType = void 0;
const graphql_1 = require("@nestjs/graphql");
var AchievementType;
(function (AchievementType) {
    AchievementType["ProfilePerfect"] = "ProfilePerfect";
    AchievementType["DailyQuizStreak"] = "DailyQuizStreak";
    AchievementType["HotStreak"] = "HotStreak";
    AchievementType["HabloFounder"] = "HabloFounder";
    AchievementType["CommunityBeacon"] = "CommunityBeacon";
    AchievementType["IndustryInfluencer"] = "IndustryInfluencer";
    AchievementType["WellbeingBadge"] = "WellbeingBadge";
    AchievementType["PowerScroller"] = "PowerScroller";
    AchievementType["AvidViewer"] = "AvidViewer";
    AchievementType["IncentiveWarrior"] = "IncentiveWarrior";
    AchievementType["Ambassador"] = "Ambassador";
    AchievementType["HighFive"] = "HighFive";
})(AchievementType || (exports.AchievementType = AchievementType = {}));
var AchievementName;
(function (AchievementName) {
    AchievementName["ProfilePerfect"] = "Profile Perfect";
    AchievementName["DailyQuizStreak"] = "Daily Quiz Streak";
    AchievementName["HotStreak"] = "Hot Streak";
    AchievementName["HabloFounder"] = "Hablo Founder";
    AchievementName["CommunityBeacon"] = "Community Beacon";
    AchievementName["IndustryInfluencer"] = "Industry Influencer";
    AchievementName["WellbeingBadge"] = "Wellbeing Badge";
    AchievementName["PowerScroller"] = "Power Scroller";
    AchievementName["AvidViewer"] = "Avid Viewer";
    AchievementName["IncentiveWarrior"] = "Incentive Warrior";
    AchievementName["Ambassador"] = "Ambassador";
    AchievementName["HighFive"] = "High Five";
})(AchievementName || (exports.AchievementName = AchievementName = {}));
(0, graphql_1.registerEnumType)(AchievementType, {
    name: 'AchievementType',
});
(0, graphql_1.registerEnumType)(AchievementName, {
    name: 'AchievementName',
});
let AchievementResult = class AchievementResult {
};
exports.AchievementResult = AchievementResult;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    __metadata("design:type", String)
], AchievementResult.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: false }),
    __metadata("design:type", Boolean)
], AchievementResult.prototype, "isAchieved", void 0);
__decorate([
    (0, graphql_1.Field)(() => Number, { nullable: true }),
    __metadata("design:type", Number)
], AchievementResult.prototype, "level", void 0);
__decorate([
    (0, graphql_1.Field)(() => Number, { nullable: true }),
    __metadata("design:type", Number)
], AchievementResult.prototype, "steps", void 0);
__decorate([
    (0, graphql_1.Field)(() => Number, { nullable: true }),
    __metadata("design:type", Number)
], AchievementResult.prototype, "stepsComplete", void 0);
__decorate([
    (0, graphql_1.Field)(() => Date, { nullable: true }),
    __metadata("design:type", Date)
], AchievementResult.prototype, "achievedDate", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    __metadata("design:type", String)
], AchievementResult.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    __metadata("design:type", String)
], AchievementResult.prototype, "image", void 0);
__decorate([
    (0, graphql_1.Field)(() => Number, { nullable: true }),
    __metadata("design:type", Number)
], AchievementResult.prototype, "number", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    __metadata("design:type", String)
], AchievementResult.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)(() => Date),
    __metadata("design:type", Date)
], AchievementResult.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(() => Date),
    __metadata("design:type", Date)
], AchievementResult.prototype, "updatedAt", void 0);
__decorate([
    (0, graphql_1.Field)(() => Number, { nullable: true }),
    __metadata("design:type", Number)
], AchievementResult.prototype, "achievedCounts", void 0);
__decorate([
    (0, graphql_1.Field)(() => Number, { nullable: true }),
    __metadata("design:type", Number)
], AchievementResult.prototype, "totalCounts", void 0);
exports.AchievementResult = AchievementResult = __decorate([
    (0, graphql_1.ObjectType)()
], AchievementResult);
//# sourceMappingURL=achievements.args.js.map