"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Achievement = void 0;
const graphql_1 = require("@nestjs/graphql");
const sequelize_1 = require("sequelize");
const sequelize_typescript_1 = require("sequelize-typescript");
const short_uuid_1 = __importDefault(require("short-uuid"));
const profile_model_1 = require("../../profiles/models/profile.model");
const achievements_args_1 = require("../args/achievements.args");
const organisation_model_1 = require("../../organisations/models/organisation.model");
let Achievement = class Achievement extends sequelize_typescript_1.Model {
};
exports.Achievement = Achievement;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], Achievement.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(() => achievements_args_1.AchievementType, {
        nullable: false,
    }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], Achievement.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)(() => Number),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    }),
    __metadata("design:type", Number)
], Achievement.prototype, "steps", void 0);
__decorate([
    (0, graphql_1.Field)(() => Number),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    }),
    __metadata("design:type", Number)
], Achievement.prototype, "stepsComplete", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.BOOLEAN,
        defaultValue: false,
    }),
    __metadata("design:type", Boolean)
], Achievement.prototype, "isAchieved", void 0);
__decorate([
    (0, graphql_1.Field)(() => Number, {
        nullable: true,
    }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
    }),
    __metadata("design:type", Number)
], Achievement.prototype, "number", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], Achievement.prototype, "profileId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, 'profileId'),
    __metadata("design:type", profile_model_1.Profile)
], Achievement.prototype, "profile", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], Achievement.prototype, "organisationId", void 0);
__decorate([
    (0, graphql_1.Field)(() => organisation_model_1.Organisation, { nullable: true }),
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, 'organisationId'),
    __metadata("design:type", organisation_model_1.Organisation)
], Achievement.prototype, "organisation", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.DATE,
    }),
    __metadata("design:type", Date)
], Achievement.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.DATE,
    }),
    __metadata("design:type", Date)
], Achievement.prototype, "updatedAt", void 0);
__decorate([
    (0, graphql_1.Field)(() => Date, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.DATE,
    }),
    __metadata("design:type", Date)
], Achievement.prototype, "achievedDate", void 0);
__decorate([
    (0, graphql_1.Field)(() => Number, { nullable: true }),
    __metadata("design:type", Number)
], Achievement.prototype, "level", void 0);
exports.Achievement = Achievement = __decorate([
    sequelize_typescript_1.Table,
    (0, graphql_1.ObjectType)()
], Achievement);
//# sourceMappingURL=achievements.model.js.map