"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivitiesResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const achievements_service_1 = require("../achievements/achievements.service");
const activities_service_1 = require("./activities.service");
const activities_args_1 = require("./args/activities.args");
const create_activity_input_1 = require("./dto/create-activity.input");
const activity_model_1 = require("./models/activity.model");
let ActivitiesResolver = class ActivitiesResolver {
    async createActivity(user, activityData) {
        this.logger.verbose('ActivitiesResolver.createActivity (mutation)', {
            profileId: user.profileId,
            type: activityData.type,
        });
        return this.activitiesService.createGenericActivity({
            activityData,
            profileId: user.profileId,
        });
    }
    async getHomePageProfileRewardsData(user) {
        this.logger.verbose('ActivitiesResolver.getHomePageProfileRewardsData (query)', {
            profileId: user.profileId,
        });
        return this.activitiesService.getHomePageProfileRewardsData({
            profileId: user.profileId,
        });
    }
    async getOrganisationRewardsData(organisationId) {
        this.logger.verbose('ActivitiesResolver.getOrganisationRewardsData (query)', {
            organisationId,
        });
        return this.activitiesService.getOrganisationRewardsData({
            organisationId,
        });
    }
    async activityLoyaltyPointsMigration(user) {
        this.logger.verbose('GettingStartedStepsResolver.activityLoyaltyPointsMigration (mutation)');
        await this.activitiesService.activityLoyaltyPointsMigration({
            profileId: user.profileId,
        });
        return await this.achievementsService.sendExistingAchievementsNotifications({
            profileId: user.profileId,
        });
    }
    async checkEverydayActivities(user) {
        this.logger.verbose('ActivitiesResolver.checkEverydayActivities (query)', {
            profileId: user.profileId,
        });
        return this.activitiesService.getEverydayActivity({
            profileId: user.profileId,
        });
    }
};
exports.ActivitiesResolver = ActivitiesResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => activities_service_1.ActivitiesService)),
    __metadata("design:type", activities_service_1.ActivitiesService)
], ActivitiesResolver.prototype, "activitiesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => achievements_service_1.AchievementsService)),
    __metadata("design:type", achievements_service_1.AchievementsService)
], ActivitiesResolver.prototype, "achievementsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], ActivitiesResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Mutation)(() => activity_model_1.Activity),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('activityData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_activity_input_1.CreateActivityInput]),
    __metadata("design:returntype", Promise)
], ActivitiesResolver.prototype, "createActivity", null);
__decorate([
    (0, graphql_1.Query)(() => activities_args_1.HomePageProfileRewardsDataResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ActivitiesResolver.prototype, "getHomePageProfileRewardsData", null);
__decorate([
    (0, graphql_1.Query)(() => activities_args_1.OrganisationRewardsDataResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)('organisationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ActivitiesResolver.prototype, "getOrganisationRewardsData", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ActivitiesResolver.prototype, "activityLoyaltyPointsMigration", null);
__decorate([
    (0, graphql_1.Query)(() => activities_args_1.CheckEverydayActivitiesResult),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ActivitiesResolver.prototype, "checkEverydayActivities", null);
exports.ActivitiesResolver = ActivitiesResolver = __decorate([
    (0, graphql_1.Resolver)(() => activity_model_1.Activity)
], ActivitiesResolver);
//# sourceMappingURL=activities.resolver.js.map