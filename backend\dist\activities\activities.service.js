"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivitiesService = void 0;
const common_1 = require("@nestjs/common");
const getstream_1 = require("getstream");
const lodash_1 = require("lodash");
const nest_winston_1 = require("nest-winston");
const sequelize_1 = require("sequelize");
const sequelize_typescript_1 = require("sequelize-typescript");
const winston_1 = require("winston");
const achievements_service_1 = require("../achievements/achievements.service");
const base_service_1 = require("../common/base.service");
const error_1 = require("../common/helpers/error");
const validateJsonSchema_1 = __importDefault(require("../common/helpers/validateJsonSchema"));
const config_1 = __importDefault(require("../config/config"));
const email_invitations_model_1 = require("../email-invitations/models/email-invitations.model");
const followers_service_1 = require("../followers/followers.service");
const follower_model_1 = require("../followers/models/follower.model");
const incentive_participants_args_1 = require("../incentive-participants/args/incentive-participants.args");
const incentive_participants_service_1 = require("../incentive-participants/incentive-participants.service");
const loyalty_points_service_1 = require("../loyalty-points/loyalty-points.service");
const organisations_service_1 = require("../organisations/organisations.service");
const profiles_service_1 = require("../profiles/profiles.service");
const activities_args_1 = require("./args/activities.args");
const activity_model_1 = require("./models/activity.model");
const create_user_activity_data_dto_1 = require("./dto/create-user-activity-data.dto");
const moment_timezone_1 = __importDefault(require("moment-timezone"));
const organisation_activity_model_1 = require("../activities/models/organisation-activity.model");
const organisation_loyalty_points_service_1 = require("../organisation-loyalty-points/organisation-loyalty-points.service");
const achievements_args_1 = require("../achievements/args/achievements.args");
let ActivitiesService = class ActivitiesService extends (0, base_service_1.BaseService)(activity_model_1.Activity) {
    onModuleInit() {
        if (config_1.default.SERVICE === 'api-beta') {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_BETA_KEY, config_1.default.STREAM_BETA_SECRET);
        }
        else {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_KEY, config_1.default.STREAM_SECRET);
        }
    }
    async createGenericActivity({ activityData, profileId, }) {
        this.logger.verbose('ActivitiesResolver.createActivity', {
            profileId,
            type: activityData.type,
        });
        const allowedTypes = [
            activities_args_1.ActivityType.OrganisationPageView,
            activities_args_1.ActivityType.WebinarVideoView,
            activities_args_1.ActivityType.WebinarCompleted,
        ];
        const loyaltyPointsTypes = [activities_args_1.ActivityType.WebinarCompleted];
        const uniqueEntryTypes = [activities_args_1.ActivityType.WebinarCompleted];
        if (!allowedTypes.includes(activityData.type)) {
            this.errorHelper.throwHttpException(`ActivitiesResolver.createActivity`, `${activityData.type} activity is not allowed to be created via mutation`);
        }
        const placeholders = activityData.data;
        if (activityData.type === activities_args_1.ActivityType.WebinarCompleted) {
            const previousActivity = await this.findOne({
                profileId,
                type: activities_args_1.ActivityType.WebinarCompleted,
                webinarId: activityData.webinarId,
            });
            if (previousActivity) {
                const loyaltyPointEntry = await this.loyaltyPointsService.addPoints({
                    type: activities_args_1.ActivityType.WebinarCompleted,
                    profileId,
                    organisationId: activityData.organisationId,
                    placeholders: Object.assign(Object.assign({}, placeholders), { previouslyTracked: true }),
                    variableRewardPoints: 0,
                });
                return await this.create({
                    type: activities_args_1.ActivityType.WebinarCompleted,
                    data: Object.assign(Object.assign({}, placeholders), { previouslyTracked: true }),
                    profileId,
                    webinarId: activityData.webinarId,
                    organisationId: activityData.organisationId,
                    loyaltyPointId: loyaltyPointEntry.id,
                });
            }
            else if (!previousActivity) {
                await this.achievementsService.addAvidViewerAchievement({
                    profileId,
                    organisationId: activityData.organisationId,
                });
            }
        }
        return this.createUserActivity(Object.assign(Object.assign({}, activityData), { organisationId: activityData.organisationId, data: activityData.data, type: activityData.type, profileId, schema: null, addLoyaltyPoints: loyaltyPointsTypes.includes(activityData.type), checkForUniqueEntry: uniqueEntryTypes.includes(activityData.type), placeholders }));
    }
    async createUserActivity(_a) {
        var { profileId, type, data, schema, placeholders, transaction, addLoyaltyPoints, checkForUniqueEntry, variableRewardPoints, organisationId, postId } = _a, rest = __rest(_a, ["profileId", "type", "data", "schema", "placeholders", "transaction", "addLoyaltyPoints", "checkForUniqueEntry", "variableRewardPoints", "organisationId", "postId"]);
        this.logger.verbose('ActivitiesService.createUserActivity', {
            profileId,
            type,
            streakCount: data === null || data === void 0 ? void 0 : data.streakCount,
            postId,
            organisationId,
            addLoyaltyPoints,
            checkForUniqueEntry,
            variableRewardPoints,
        });
        if (checkForUniqueEntry) {
            const webinarId = data['webinarId'];
            const existingActivity = await this.findOne({
                profileId,
                type,
                webinarId: webinarId !== null && webinarId !== void 0 ? webinarId : null,
            });
            if (existingActivity) {
                return existingActivity;
            }
        }
        if (schema) {
            await (0, validateJsonSchema_1.default)(schema, data, this.logger);
        }
        let loyaltyPointId = null;
        if (addLoyaltyPoints) {
            const loyaltyPointEntry = await this.loyaltyPointsService.addPoints({
                type,
                profileId,
                placeholders,
                transaction,
                variableRewardPoints,
                organisationId,
            });
            loyaltyPointId = loyaltyPointEntry.id;
        }
        const newActivity = await this.create(Object.assign({ type,
            data,
            profileId,
            loyaltyPointId,
            organisationId,
            postId }, rest), {
            transaction,
        });
        if (organisationId &&
            (type === activities_args_1.ActivityType.CreatePost ||
                type === activities_args_1.ActivityType.CreateWebinar ||
                type === activities_args_1.ActivityType.CreateIncentive ||
                type === activities_args_1.ActivityType.CreateEvent ||
                type === activities_args_1.ActivityType.OwnerPostComment)) {
            const orgActivityData = {
                createdById: profileId,
                organisationId: organisationId,
                type: type,
                postId: postId,
                data: Object.assign({ postId: postId }, data),
            };
            await this.createOrganisationActivity(orgActivityData, { transaction });
        }
        if (organisationId &&
            (type === activities_args_1.ActivityType.CreatePost || type === activities_args_1.ActivityType.PostView)) {
            await this.achievementsService.incrementStepsComplete({
                profileId,
                organisationId,
                postId,
            });
        }
        return newActivity;
    }
    async getHomePageProfileRewardsData({ profileId, }) {
        this.logger.verbose('ActivitiesService.getHomePageProfileRewardsData', {
            profileId,
        });
        const [{ lifeTimePoints, rollingPoints, streak: goldTierStreak, currentTier, nextTier, previousMonthTier, activeTier, }, loginDayStreak,] = await Promise.all([
            this.loyaltyPointsService.getUserLoyaltyPoints({
                profileId,
            }),
            this.getUserLoginStreak({
                profileId,
            }),
        ]);
        const achievements = await this.achievementsService.count({
            [sequelize_1.Op.or]: [
                {
                    profileId,
                    isAchieved: true,
                    organisationId: null,
                },
                {
                    profileId,
                    isAchieved: true,
                    type: achievements_args_1.AchievementType.Ambassador,
                },
            ],
        });
        return {
            loginDayStreak,
            habloPoints: rollingPoints,
            lifeTimePoints,
            goldTierStreak,
            activeTier: activeTier.key,
            tier: currentTier.key,
            nextTier: nextTier ? nextTier.key : null,
            nextTierPoints: nextTier ? nextTier.minPoints : 0,
            previousMonthTier: previousMonthTier.key,
            achievements,
        };
    }
    async getOrganisationRewardsData({ organisationId, }) {
        this.logger.verbose('ActivitiesService.getOrganisationRewardsData', {
            organisationId,
        });
        const [{ lifeTimePoints, rollingPoints, streak: goldTierStreak, currentTier, nextTier, previousMonthTier, activeTier, },] = await Promise.all([
            this.organisationLoyaltyPointsService.getOrganisationLoyaltyPoints({
                organisationId,
            }),
        ]);
        return {
            habloPoints: rollingPoints,
            lifeTimePoints,
            goldTierStreak,
            tier: currentTier.key,
            nextTier: nextTier ? nextTier.key : null,
            nextTierPoints: nextTier ? nextTier.minPoints : 0,
            activeTier: activeTier.key,
            previousMonthTier: previousMonthTier.key,
        };
    }
    async getUserLoginStreak({ profileId, }) {
        const lastLoginActivity = await this.findOne({
            profileId,
            type: activities_args_1.ActivityType.ActiveSession,
        }, {
            order: [['createdAt', 'DESC']],
        });
        let streakCount = 0;
        if (lastLoginActivity) {
            const lastLoginDate = (0, moment_timezone_1.default)(lastLoginActivity.createdAt).startOf('day');
            const today = (0, moment_timezone_1.default)().startOf('day');
            const diffInDays = today.diff(lastLoginDate, 'days');
            streakCount =
                diffInDays > 1
                    ? 0
                    : (0, lodash_1.get)(lastLoginActivity.getDataValue('data'), 'streakCount', 0);
        }
        return streakCount;
    }
    async getPaginatedActivitiesForOrg(feed, prevActivityId, prevResults) {
        var _a;
        const reqParams = {
            limit: 100,
        };
        if (prevActivityId)
            reqParams.id_lt = prevActivityId;
        const resultsCurrObj = await feed.get(reqParams);
        const resultsCurr = resultsCurrObj.results;
        let idx = 0;
        for (const activity of resultsCurr) {
            const views = await this.getPaginatedReactionsForActivity(activity.id, 'view');
            const comments = await this.getPaginatedReactionsForActivity(activity.id, 'comment');
            const likes = await this.getPaginatedReactionsForActivity(activity.id, 'like');
            resultsCurr[idx].views = views;
            resultsCurr[idx].comments = comments;
            resultsCurr[idx].likes = likes;
            idx++;
        }
        const results = [...(prevResults || []), ...resultsCurr];
        const nextActivityId = (_a = resultsCurr[resultsCurr.results.length - 1]) === null || _a === void 0 ? void 0 : _a.id;
        if (resultsCurr.length === reqParams.limit && nextActivityId) {
            return this.getPaginatedActivitiesForOrg(feed, nextActivityId, results);
        }
        return results;
    }
    async getPaginatedReactionsForActivity(activityId, reactionType, prevReactionId, prevResults) {
        var _a;
        const reqParams = {
            limit: 25,
            kind: reactionType,
            activity_id: activityId,
        };
        if (prevReactionId)
            reqParams.id_lt = prevReactionId;
        const resultsCurrObj = await this.client.reactions.filter(reqParams);
        const resultsCurr = resultsCurrObj.results;
        const nextReactionId = (_a = resultsCurr[resultsCurr.length - 1]) === null || _a === void 0 ? void 0 : _a.id;
        const results = [...(prevResults || []), ...resultsCurr];
        if (resultsCurr.length === reqParams.limit && nextReactionId) {
            return this.getPaginatedReactionsForActivity(activityId, reactionType, nextReactionId, results);
        }
        return results;
    }
    async migrateAnalyticsDataFromStream() {
        const transaction = await this.sequelize.transaction();
        try {
            const allOrgs = await this.organisationsService.findAll();
            for (const org of allOrgs) {
                const feed = await this.client.feed('organisations', org.id);
                const activities = await this.getPaginatedActivitiesForOrg(feed);
                console.log('retrieved activities for org', org.id, activities.length);
                for (const activity of activities) {
                    const organisationId = activity && activity.object.split('SO:organisations:')[1];
                    const postId = activity && activity.post.split('SO:posts:')[1];
                    const profileId = activity && activity.user && activity.user.id;
                    console.log('processing activity', activity.id, organisationId, postId, profileId);
                    if (activity.data.repostData) {
                        console.log('creating repost entry', profileId, organisationId, postId);
                        await this.create({
                            type: activities_args_1.ActivityType.PostInteractions,
                            profileId: profileId,
                            organisationId: organisationId,
                            postId: postId,
                            createdAt: activity.time,
                        }, {
                            transaction,
                        });
                    }
                    for (const {} of activity.views) {
                        console.log('creating views entry', profileId, organisationId, postId);
                        await this.create({
                            type: activities_args_1.ActivityType.PostImpressions,
                            profileId: profileId,
                            organisationId: organisationId,
                            postId: postId,
                        }, {
                            transaction,
                        });
                    }
                    for (const comment of activity.comments) {
                        console.log('creating comments entry', profileId, organisationId, postId);
                        await this.create({
                            type: activities_args_1.ActivityType.PostInteractions,
                            profileId: profileId,
                            organisationId: organisationId,
                            postId: postId,
                            createdAt: comment.created_at,
                        }, {
                            transaction,
                        });
                    }
                    for (const like of activity.likes) {
                        console.log('creating like entry', profileId, organisationId, postId);
                        await this.create({
                            type: activities_args_1.ActivityType.PostInteractions,
                            profileId: profileId,
                            organisationId: organisationId,
                            postId: postId,
                            createdAt: like.created_at,
                        }, {
                            transaction,
                        });
                    }
                }
            }
            await transaction.commit();
            console.log('analytics sync done');
        }
        catch (err) {
            console.log('analytics sync err', err);
            await transaction.rollback();
        }
    }
    async activityLoyaltyPointsMigration({ profileId, }) {
        this.logger.info('ActivitiesService.activityLoyaltyPointsMigration', {
            profileId,
        });
        try {
            const profile = await this.profilesService.updateById(profileId, {
                sellHolidays: true,
            });
            const currentLoyaltyPoints = await this.loyaltyPointsService.getUserLoyaltyPoints({
                profileId,
            });
            if (currentLoyaltyPoints.lifeTimePoints > 0) {
                return true;
            }
            setTimeout(async () => {
                this.logger.info('ActivitiesService.activityLoyaltyPointsMigration background job', {
                    profileId,
                });
                const transaction = await this.sequelize.transaction();
                try {
                    if (profile.isComplete) {
                        await this.createUserActivity({
                            type: activities_args_1.ActivityType.ProfileCompletion,
                            schema: create_user_activity_data_dto_1.emptyActivityDataDto,
                            data: {},
                            profileId: profile.id,
                            addLoyaltyPoints: true,
                            checkForUniqueEntry: true,
                            transaction,
                        });
                    }
                    let totalIncentives = await this.incentiveParticipantsService.count({
                        status: {
                            [sequelize_1.Op.in]: [incentive_participants_args_1.IncentiveParticipantStatus.Registered],
                        },
                        profileId: profile.id,
                    });
                    if (totalIncentives > 0) {
                        if (totalIncentives > 10) {
                            totalIncentives = 10;
                        }
                        await this.createUserActivity({
                            type: activities_args_1.ActivityType.IncentiveRegistrationMigration,
                            schema: create_user_activity_data_dto_1.emptyActivityDataDto,
                            data: {},
                            profileId: profile.id,
                            addLoyaltyPoints: true,
                            placeholders: {
                                id: profileId,
                                count: totalIncentives,
                            },
                            variableRewardPoints: totalIncentives * 50,
                            transaction,
                        });
                    }
                    let totalFollowedOrganisations = await this.followersService.count({
                        profileId: profile.id,
                        status: follower_model_1.FollowerStatus.Active,
                    });
                    if (totalFollowedOrganisations > 0) {
                        if (totalFollowedOrganisations > 30) {
                            totalFollowedOrganisations = 30;
                        }
                        await this.createUserActivity({
                            type: activities_args_1.ActivityType.OrganisationFollowerMigration,
                            schema: create_user_activity_data_dto_1.emptyActivityDataDto,
                            data: {},
                            profileId: profile.id,
                            addLoyaltyPoints: true,
                            placeholders: {
                                id: profileId,
                                count: totalFollowedOrganisations,
                            },
                            variableRewardPoints: totalFollowedOrganisations * 100,
                            transaction,
                        });
                    }
                    const notificationActivity = await this.findOne({
                        profileId,
                        type: activities_args_1.ActivityType.NotificationEnabled,
                    }, {
                        order: [['createdAt', 'DESC']],
                        transaction,
                    });
                    if (notificationActivity) {
                        const loyaltyPointEntry = await this.loyaltyPointsService.addPoints({
                            type: activities_args_1.ActivityType.NotificationEnabled,
                            profileId,
                            transaction,
                        });
                        await this.updateById(notificationActivity.id, {
                            loyaltyPointId: loyaltyPointEntry.id,
                        }, {
                            transaction,
                        });
                    }
                    const query = `
        SELECT DISTINCT ON ("invitedEmail")
              "invitedEmail",
              "sentConnectionRequestId"
          FROM
              "EmailInvitations"
          WHERE
              "senderProfileId" = :profileId
          ORDER BY
              "invitedEmail",
              "createdAt" DESC
          LIMIT 100;
      `;
                    const options = {
                        replacements: {
                            profileId,
                        },
                        type: 'SELECT',
                    };
                    const emailInvitations = (await email_invitations_model_1.EmailInvitations.sequelize.query(query, options));
                    let entriesWithConnection = 0;
                    let pointsFromConnection = 0;
                    let entriesWithoutConnection = 0;
                    let pointsFromNoConnection = 0;
                    for (const invite of emailInvitations) {
                        if (invite.sentConnectionRequestId) {
                            entriesWithConnection += 1;
                            pointsFromConnection += 1000;
                        }
                        else {
                            entriesWithoutConnection += 1;
                            pointsFromNoConnection += 10;
                        }
                        if (pointsFromConnection + pointsFromNoConnection >= 10000) {
                            break;
                        }
                    }
                    if (entriesWithConnection > 0) {
                        await this.createUserActivity({
                            type: activities_args_1.ActivityType.EmailInvitationRegistrationMigration,
                            schema: create_user_activity_data_dto_1.emptyActivityDataDto,
                            data: {},
                            profileId: profile.id,
                            addLoyaltyPoints: true,
                            placeholders: {
                                id: profileId,
                                count: entriesWithConnection,
                            },
                            variableRewardPoints: +pointsFromConnection,
                            transaction,
                        });
                    }
                    if (entriesWithoutConnection > 0) {
                        await this.createUserActivity({
                            type: activities_args_1.ActivityType.InviteEmailContactMigration,
                            schema: create_user_activity_data_dto_1.emptyActivityDataDto,
                            data: {},
                            profileId: profile.id,
                            addLoyaltyPoints: true,
                            placeholders: {
                                id: profileId,
                                count: entriesWithoutConnection,
                            },
                            variableRewardPoints: +pointsFromNoConnection,
                            transaction,
                        });
                    }
                    await transaction.commit();
                }
                catch (error) {
                    await transaction.rollback();
                    this.logger.error(`ActivitiesService.activityLoyaltyPointsMigration background job Error: ${error.message}`, error.message);
                }
            }, 10);
            return true;
        }
        catch (error) {
            this.logger.error(`ActivitiesService.activityLoyaltyPointsMigration Error: ${error.message}`, error.message);
            this.errorHelper.throwHttpException(`ActivitiesService.activityLoyaltyPointsMigration`, error.message);
        }
    }
    async fixActivityDataMigration() {
        try {
            const activitiesToUpdate = await this.findAll({
                [sequelize_1.Op.or]: [
                    {
                        organisationId: null,
                    },
                    {
                        postId: null,
                    },
                    {
                        profileId: null,
                    },
                ],
                data: {
                    [sequelize_1.Op.or]: [
                        {
                            organisationId: {
                                [sequelize_1.Op.not]: null,
                            },
                        },
                        {
                            postId: {
                                [sequelize_1.Op.not]: null,
                            },
                        },
                        {
                            profileId: {
                                [sequelize_1.Op.not]: null,
                            },
                        },
                    ],
                },
            });
            this.logger.info(`Total Activities to update: ${activitiesToUpdate.length}`);
            let activitiesUpdated = 0;
            let activitiesWithError = 0;
            for (const activity of activitiesToUpdate) {
                const data = activity.getDataValue('data');
                let type = activity.getDataValue('type');
                if (type === 'PostLikeShare' && data.type === 'PostLike') {
                    type = activities_args_1.ActivityType.PostInteractions;
                }
                else if (type === 'OrganisationUnFollower') {
                    type = activities_args_1.ActivityType.OrganisationFollower;
                }
                console.log(Object.assign(Object.assign({}, data), { type, id: activity.id }));
                try {
                    await activity.update({
                        type,
                        organisationId: activity.organisationId || (data === null || data === void 0 ? void 0 : data.organisationId) || null,
                        postId: activity.postId || (data === null || data === void 0 ? void 0 : data.postId) || null,
                        profileId: activity.profileId || (data === null || data === void 0 ? void 0 : data.profileId) || null,
                    });
                    activitiesUpdated += 1;
                }
                catch (error) {
                    this.logger.error(`ActivitiesService.fixActivityDataMigration Error`, error.message);
                    activitiesWithError += 1;
                }
            }
            this.logger.info('ActivitiesService.fixActivityDataMigration completed');
            this.logger.info(`Total Activities updated: ${activitiesUpdated}.`);
            this.logger.info(`Total Activities with error: ${activitiesWithError}`);
        }
        catch (error) {
            this.logger.error(`ActivitiesService.fixActivityDataMigration Error`, error.message);
        }
    }
    async createOrganisationActivity(activityData, options) {
        var _a;
        this.logger.verbose('ActivitiesService.createOrganisationActivity', {
            activityData,
        });
        let parentOrganisationId = null;
        if ('parentOrgId' in activityData && activityData.parentOrgId) {
            parentOrganisationId = activityData.parentOrgId;
        }
        else if ('organisationId' in activityData && activityData.organisationId) {
            const org = await this.organisationsService.findById(activityData.organisationId, {
                transaction: options === null || options === void 0 ? void 0 : options.transaction,
            });
            if (((_a = org === null || org === void 0 ? void 0 : org.parentOrganisations) === null || _a === void 0 ? void 0 : _a.length) > 0) {
                parentOrganisationId = org.parentOrganisations[0].id;
            }
        }
        const newActivity = await organisation_activity_model_1.OrganisationActivity.create({
            type: activityData.type,
            data: 'data' in activityData ? activityData.data : {},
            organisationId: 'organisationId' in activityData ? activityData.organisationId : null,
            createdById: 'createdById' in activityData ? activityData.createdById : null,
            postId: 'postId' in activityData ? activityData.postId : null,
            webinarId: 'webinarId' in activityData ? activityData.webinarId : null,
            eventId: 'eventId' in activityData ? activityData.eventId : null,
            parentOrganisationId,
        }, {
            transaction: options === null || options === void 0 ? void 0 : options.transaction,
        });
        return newActivity;
    }
    async getEverydayActivity({ profileId }) {
        this.logger.verbose('ActivityService.getEverydayActivity', {
            profileId,
        });
        const profile = await this.profilesService.findById(profileId);
        if (!profile) {
            this.logger.error(`Profile not found for profileId: ${profileId}`);
            throw new Error(`Profile not found for profileId: ${profileId}`);
        }
        const timezone = (profile === null || profile === void 0 ? void 0 : profile.timezone) || 'UTC';
        const startOfDay = moment_timezone_1.default.tz(timezone).startOf('day').utc().toDate();
        const activeSessionEntry = await this.findOne({
            type: activities_args_1.ActivityType.ActiveSession,
            profileId,
            createdAt: {
                [sequelize_1.Op.gt]: startOfDay,
            },
        }, {
            useCache: false,
        });
        const variableLoginRewardEntry = await this.findOne({
            type: activities_args_1.ActivityType.VariableLoginReward,
            profileId,
            createdAt: {
                [sequelize_1.Op.gt]: startOfDay,
            },
        }, {
            useCache: false,
        });
        const loginDayStreak = !!activeSessionEntry;
        let variableReward = !!variableLoginRewardEntry;
        if (!profile.sellHolidays) {
            variableReward = true;
        }
        return { loginDayStreak, variableReward };
    }
};
exports.ActivitiesService = ActivitiesService;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], ActivitiesService.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => loyalty_points_service_1.LoyaltyPointsService)),
    __metadata("design:type", loyalty_points_service_1.LoyaltyPointsService)
], ActivitiesService.prototype, "loyaltyPointsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], ActivitiesService.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentive_participants_service_1.IncentiveParticipantsService)),
    __metadata("design:type", incentive_participants_service_1.IncentiveParticipantsService)
], ActivitiesService.prototype, "incentiveParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => followers_service_1.FollowersService)),
    __metadata("design:type", followers_service_1.FollowersService)
], ActivitiesService.prototype, "followersService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => achievements_service_1.AchievementsService)),
    __metadata("design:type", achievements_service_1.AchievementsService)
], ActivitiesService.prototype, "achievementsService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", sequelize_typescript_1.Sequelize)
], ActivitiesService.prototype, "sequelize", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], ActivitiesService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], ActivitiesService.prototype, "errorHelper", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisation_loyalty_points_service_1.OrganisationLoyaltyPointsService)),
    __metadata("design:type", organisation_loyalty_points_service_1.OrganisationLoyaltyPointsService)
], ActivitiesService.prototype, "organisationLoyaltyPointsService", void 0);
exports.ActivitiesService = ActivitiesService = __decorate([
    (0, common_1.Injectable)()
], ActivitiesService);
//# sourceMappingURL=activities.service.js.map