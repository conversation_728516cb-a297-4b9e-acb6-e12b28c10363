"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CheckEverydayActivitiesResult = exports.OrganisationRewardsDataResponse = exports.HomePageProfileRewardsDataResponse = exports.ActivitiesArgs = exports.ActivitiesFilter = exports.ActivityType = void 0;
const graphql_1 = require("@nestjs/graphql");
const pagination_args_1 = require("../../common/args/pagination.args");
var ActivityType;
(function (ActivityType) {
    ActivityType["OrganisationPageView"] = "OrganisationPageView";
    ActivityType["OrganisationFollower"] = "OrganisationFollower";
    ActivityType["OrganisationFollowerMigration"] = "OrganisationFollowerMigration";
    ActivityType["PostSeen"] = "PostSeen";
    ActivityType["EventRSVP"] = "EventRSVP";
    ActivityType["ZeroLoyaltyPoints"] = "ZeroLoyaltyPoints";
    ActivityType["WebinarVideoView"] = "WebinarVideoView";
    ActivityType["PostInteractions"] = "PostInteractions";
    ActivityType["PostImpressions"] = "PostImpressions";
    ActivityType["PostView"] = "PostView";
    ActivityType["PostCommentLike"] = "PostCommentLike";
    ActivityType["UserConnection"] = "UserConnection";
    ActivityType["InviteEmailContact"] = "InviteEmailContact";
    ActivityType["InviteEmailContactMigration"] = "InviteEmailContactMigration";
    ActivityType["EmailInvitationRegistration"] = "EmailInvitationRegistration";
    ActivityType["EmailInvitationRegistrationMigration"] = "EmailInvitationRegistrationMigration";
    ActivityType["ActiveSession"] = "ActiveSession";
    ActivityType["VariableLoginReward"] = "VariableLoginReward";
    ActivityType["NotificationEnabled"] = "NotificationEnabled";
    ActivityType["NewIncentive"] = "NewIncentive";
    ActivityType["IncentiveRegistration"] = "IncentiveRegistration";
    ActivityType["IncentiveRegistrationMigration"] = "IncentiveRegistrationMigration";
    ActivityType["IncentiveBooking"] = "IncentiveBooking";
    ActivityType["ProfileCompletion"] = "ProfileCompletion";
    ActivityType["WebinarCompleted"] = "WebinarCompleted";
    ActivityType["CreatePost"] = "CreatePost";
    ActivityType["RemovePost"] = "RemovePost";
    ActivityType["CreateWebinar"] = "CreateWebinar";
    ActivityType["CreateIncentive"] = "CreateIncentive";
    ActivityType["CreateEvent"] = "CreateEvent";
    ActivityType["UserInvitedToWebinar"] = "UserInvitedToWebinar";
    ActivityType["UserInvitedToIncentive"] = "UserInvitedToIncentive";
    ActivityType["UserInvitedToEvent"] = "UserInvitedToEvent";
    ActivityType["OwnerPostComment"] = "OwnerPostComment";
    ActivityType["OwnerPostCommentLike"] = "OwnerPostCommentLike";
    ActivityType["OwnerPostCommentReply"] = "OwnerPostCommentReply";
    ActivityType["CustomKudosAward"] = "CustomKudosAward";
    ActivityType["HighFive"] = "HighFive";
    ActivityType["SavePost"] = "SavePost";
    ActivityType["UnsavePost"] = "UnsavePost";
})(ActivityType || (exports.ActivityType = ActivityType = {}));
(0, graphql_1.registerEnumType)(ActivityType, { name: 'ActivityType' });
let ActivitiesFilter = class ActivitiesFilter {
};
exports.ActivitiesFilter = ActivitiesFilter;
__decorate([
    (0, graphql_1.Field)(() => [ActivityType], { nullable: true, defaultValue: [] }),
    __metadata("design:type", Array)
], ActivitiesFilter.prototype, "type", void 0);
exports.ActivitiesFilter = ActivitiesFilter = __decorate([
    (0, graphql_1.InputType)()
], ActivitiesFilter);
let ActivitiesArgs = class ActivitiesArgs extends pagination_args_1.PaginationArgs {
};
exports.ActivitiesArgs = ActivitiesArgs;
__decorate([
    (0, graphql_1.Field)(() => ActivitiesFilter, { nullable: true }),
    __metadata("design:type", ActivitiesFilter)
], ActivitiesArgs.prototype, "filter", void 0);
exports.ActivitiesArgs = ActivitiesArgs = __decorate([
    (0, graphql_1.ArgsType)()
], ActivitiesArgs);
let HomePageProfileRewardsDataResponse = class HomePageProfileRewardsDataResponse {
};
exports.HomePageProfileRewardsDataResponse = HomePageProfileRewardsDataResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], HomePageProfileRewardsDataResponse.prototype, "loginDayStreak", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], HomePageProfileRewardsDataResponse.prototype, "habloPoints", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], HomePageProfileRewardsDataResponse.prototype, "lifeTimePoints", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], HomePageProfileRewardsDataResponse.prototype, "goldTierStreak", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], HomePageProfileRewardsDataResponse.prototype, "tier", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], HomePageProfileRewardsDataResponse.prototype, "nextTier", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], HomePageProfileRewardsDataResponse.prototype, "nextTierPoints", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], HomePageProfileRewardsDataResponse.prototype, "achievements", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], HomePageProfileRewardsDataResponse.prototype, "previousMonthTier", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], HomePageProfileRewardsDataResponse.prototype, "activeTier", void 0);
exports.HomePageProfileRewardsDataResponse = HomePageProfileRewardsDataResponse = __decorate([
    (0, graphql_1.ObjectType)()
], HomePageProfileRewardsDataResponse);
let OrganisationRewardsDataResponse = class OrganisationRewardsDataResponse {
};
exports.OrganisationRewardsDataResponse = OrganisationRewardsDataResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], OrganisationRewardsDataResponse.prototype, "habloPoints", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], OrganisationRewardsDataResponse.prototype, "lifeTimePoints", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], OrganisationRewardsDataResponse.prototype, "goldTierStreak", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], OrganisationRewardsDataResponse.prototype, "tier", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], OrganisationRewardsDataResponse.prototype, "nextTier", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], OrganisationRewardsDataResponse.prototype, "nextTierPoints", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], OrganisationRewardsDataResponse.prototype, "previousMonthTier", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], OrganisationRewardsDataResponse.prototype, "activeTier", void 0);
exports.OrganisationRewardsDataResponse = OrganisationRewardsDataResponse = __decorate([
    (0, graphql_1.ObjectType)()
], OrganisationRewardsDataResponse);
let CheckEverydayActivitiesResult = class CheckEverydayActivitiesResult {
};
exports.CheckEverydayActivitiesResult = CheckEverydayActivitiesResult;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], CheckEverydayActivitiesResult.prototype, "loginDayStreak", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], CheckEverydayActivitiesResult.prototype, "variableReward", void 0);
exports.CheckEverydayActivitiesResult = CheckEverydayActivitiesResult = __decorate([
    (0, graphql_1.ObjectType)()
], CheckEverydayActivitiesResult);
//# sourceMappingURL=activities.args.js.map