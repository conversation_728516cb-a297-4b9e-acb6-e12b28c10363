"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ownerPostCommentActivityDataDto = exports.eventInviteUserActivityDataDto = exports.createEventActivityDataDto = exports.createIncentiveActivityDataDto = exports.createWebinarActivityDataDto = exports.createOrganisationActivityDataDto = exports.createPostActivityDataDto = void 0;
const class_validator_1 = require("class-validator");
class createPostActivityDataDto {
}
exports.createPostActivityDataDto = createPostActivityDataDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createPostActivityDataDto.prototype, "createdById", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createPostActivityDataDto.prototype, "postId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createPostActivityDataDto.prototype, "postContent", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createPostActivityDataDto.prototype, "organisationId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createPostActivityDataDto.prototype, "organisationName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createPostActivityDataDto.prototype, "parentOrgId", void 0);
__decorate([
    (0, class_validator_1.IsJSON)(),
    __metadata("design:type", Object)
], createPostActivityDataDto.prototype, "data", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], createPostActivityDataDto.prototype, "type", void 0);
class createOrganisationActivityDataDto {
}
exports.createOrganisationActivityDataDto = createOrganisationActivityDataDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], createOrganisationActivityDataDto.prototype, "createdById", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], createOrganisationActivityDataDto.prototype, "organisationId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], createOrganisationActivityDataDto.prototype, "postId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], createOrganisationActivityDataDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsJSON)(),
    __metadata("design:type", Object)
], createOrganisationActivityDataDto.prototype, "data", void 0);
class createWebinarActivityDataDto {
}
exports.createWebinarActivityDataDto = createWebinarActivityDataDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createWebinarActivityDataDto.prototype, "createdById", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createWebinarActivityDataDto.prototype, "webinarId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createWebinarActivityDataDto.prototype, "webinarContent", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createWebinarActivityDataDto.prototype, "organisationId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createWebinarActivityDataDto.prototype, "organisationName", void 0);
__decorate([
    (0, class_validator_1.IsJSON)(),
    __metadata("design:type", Object)
], createWebinarActivityDataDto.prototype, "data", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], createWebinarActivityDataDto.prototype, "type", void 0);
class createIncentiveActivityDataDto {
}
exports.createIncentiveActivityDataDto = createIncentiveActivityDataDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createIncentiveActivityDataDto.prototype, "createdById", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createIncentiveActivityDataDto.prototype, "incentiveId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createIncentiveActivityDataDto.prototype, "organisationId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], createIncentiveActivityDataDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsJSON)(),
    __metadata("design:type", Object)
], createIncentiveActivityDataDto.prototype, "data", void 0);
class createEventActivityDataDto {
}
exports.createEventActivityDataDto = createEventActivityDataDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createEventActivityDataDto.prototype, "createdById", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createEventActivityDataDto.prototype, "eventId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createEventActivityDataDto.prototype, "organisationId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createEventActivityDataDto.prototype, "organisationName", void 0);
__decorate([
    (0, class_validator_1.IsJSON)(),
    __metadata("design:type", Object)
], createEventActivityDataDto.prototype, "data", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], createEventActivityDataDto.prototype, "type", void 0);
class eventInviteUserActivityDataDto {
}
exports.eventInviteUserActivityDataDto = eventInviteUserActivityDataDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], eventInviteUserActivityDataDto.prototype, "invitedById", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], eventInviteUserActivityDataDto.prototype, "invitedProfileId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], eventInviteUserActivityDataDto.prototype, "organisationId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], eventInviteUserActivityDataDto.prototype, "eventId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], eventInviteUserActivityDataDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsJSON)(),
    __metadata("design:type", Object)
], eventInviteUserActivityDataDto.prototype, "data", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], eventInviteUserActivityDataDto.prototype, "createdById", void 0);
class ownerPostCommentActivityDataDto {
}
exports.ownerPostCommentActivityDataDto = ownerPostCommentActivityDataDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ownerPostCommentActivityDataDto.prototype, "commentById", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ownerPostCommentActivityDataDto.prototype, "type", void 0);
//# sourceMappingURL=create-organisation-activity-data.dto.js.map