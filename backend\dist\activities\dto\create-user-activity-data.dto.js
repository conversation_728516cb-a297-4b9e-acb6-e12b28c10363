"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.highFiveActivityDataDto = exports.incentiveBookingActivityDataDto = exports.incentiveRegistrationActivityDataDto = exports.newIncentiveActivityDataDto = exports.variableLoginRewardActivityDataDto = exports.addPostViewToActivityDataDto = exports.organisationUnFollowActivityDataDto = exports.organisationFollowActivityDataDto = exports.newCommentActivityDataDto = exports.commentLikeActivityDataDto = exports.postLikeActivityDataDto = exports.emailInvitationRegistrationActivityDataDto = exports.inviteEmailContactActivityDataDto = exports.removeConnectionActivityDataDto = exports.createConnectionActivityDataDto = exports.emptyActivityDataDto = void 0;
const class_validator_1 = require("class-validator");
class emptyActivityDataDto {
}
exports.emptyActivityDataDto = emptyActivityDataDto;
class createConnectionActivityDataDto {
}
exports.createConnectionActivityDataDto = createConnectionActivityDataDto;
__decorate([
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], createConnectionActivityDataDto.prototype, "follow", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createConnectionActivityDataDto.prototype, "profileId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createConnectionActivityDataDto.prototype, "connectionProfileId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createConnectionActivityDataDto.prototype, "connectionRequestId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], createConnectionActivityDataDto.prototype, "streamChannelId", void 0);
class removeConnectionActivityDataDto {
}
exports.removeConnectionActivityDataDto = removeConnectionActivityDataDto;
__decorate([
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], removeConnectionActivityDataDto.prototype, "follow", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], removeConnectionActivityDataDto.prototype, "profileId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], removeConnectionActivityDataDto.prototype, "connectionProfileId", void 0);
class inviteEmailContactActivityDataDto {
}
exports.inviteEmailContactActivityDataDto = inviteEmailContactActivityDataDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], inviteEmailContactActivityDataDto.prototype, "profileId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], inviteEmailContactActivityDataDto.prototype, "text", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], inviteEmailContactActivityDataDto.prototype, "email", void 0);
class emailInvitationRegistrationActivityDataDto {
}
exports.emailInvitationRegistrationActivityDataDto = emailInvitationRegistrationActivityDataDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], emailInvitationRegistrationActivityDataDto.prototype, "senderProfileId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], emailInvitationRegistrationActivityDataDto.prototype, "receiverProfileId", void 0);
var PostInteractions;
(function (PostInteractions) {
    PostInteractions["PostLike"] = "PostLike";
    PostInteractions["CommentLike"] = "CommentLike";
    PostInteractions["NewComment"] = "NewComment";
})(PostInteractions || (PostInteractions = {}));
class postLikeActivityDataDto {
}
exports.postLikeActivityDataDto = postLikeActivityDataDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], postLikeActivityDataDto.prototype, "likedById", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(PostInteractions),
    __metadata("design:type", String)
], postLikeActivityDataDto.prototype, "type", void 0);
class commentLikeActivityDataDto {
}
exports.commentLikeActivityDataDto = commentLikeActivityDataDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], commentLikeActivityDataDto.prototype, "likedById", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], commentLikeActivityDataDto.prototype, "postAuthorId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], commentLikeActivityDataDto.prototype, "commentId", void 0);
class newCommentActivityDataDto {
}
exports.newCommentActivityDataDto = newCommentActivityDataDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], newCommentActivityDataDto.prototype, "commentById", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(PostInteractions),
    __metadata("design:type", String)
], newCommentActivityDataDto.prototype, "type", void 0);
class organisationFollowActivityDataDto {
}
exports.organisationFollowActivityDataDto = organisationFollowActivityDataDto;
__decorate([
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], organisationFollowActivityDataDto.prototype, "follow", void 0);
class organisationUnFollowActivityDataDto {
}
exports.organisationUnFollowActivityDataDto = organisationUnFollowActivityDataDto;
__decorate([
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], organisationUnFollowActivityDataDto.prototype, "unFollow", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], organisationUnFollowActivityDataDto.prototype, "profileId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], organisationUnFollowActivityDataDto.prototype, "organisationId", void 0);
class addPostViewToActivityDataDto {
}
exports.addPostViewToActivityDataDto = addPostViewToActivityDataDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], addPostViewToActivityDataDto.prototype, "profileId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], addPostViewToActivityDataDto.prototype, "activityId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], addPostViewToActivityDataDto.prototype, "postType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], addPostViewToActivityDataDto.prototype, "repostPostId", void 0);
class variableLoginRewardActivityDataDto {
}
exports.variableLoginRewardActivityDataDto = variableLoginRewardActivityDataDto;
__decorate([
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], variableLoginRewardActivityDataDto.prototype, "minutes", void 0);
class newIncentiveActivityDataDto {
}
exports.newIncentiveActivityDataDto = newIncentiveActivityDataDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], newIncentiveActivityDataDto.prototype, "profileId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], newIncentiveActivityDataDto.prototype, "incentiveId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], newIncentiveActivityDataDto.prototype, "incentiveName", void 0);
__decorate([
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", String)
], newIncentiveActivityDataDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", String)
], newIncentiveActivityDataDto.prototype, "endDate", void 0);
class incentiveRegistrationActivityDataDto {
}
exports.incentiveRegistrationActivityDataDto = incentiveRegistrationActivityDataDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], incentiveRegistrationActivityDataDto.prototype, "profileId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], incentiveRegistrationActivityDataDto.prototype, "incentiveParticipantId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], incentiveRegistrationActivityDataDto.prototype, "incentiveId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], incentiveRegistrationActivityDataDto.prototype, "incentiveName", void 0);
class incentiveBookingActivityDataDto {
}
exports.incentiveBookingActivityDataDto = incentiveBookingActivityDataDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], incentiveBookingActivityDataDto.prototype, "profileId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], incentiveBookingActivityDataDto.prototype, "incentiveId", void 0);
class highFiveActivityDataDto {
}
exports.highFiveActivityDataDto = highFiveActivityDataDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], highFiveActivityDataDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], highFiveActivityDataDto.prototype, "organisationId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], highFiveActivityDataDto.prototype, "organisationName", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], highFiveActivityDataDto.prototype, "userId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], highFiveActivityDataDto.prototype, "userName", void 0);
//# sourceMappingURL=create-user-activity-data.dto.js.map