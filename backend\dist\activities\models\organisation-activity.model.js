"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganisationActivity = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const sequelize_1 = require("sequelize");
const graphql_1 = require("@nestjs/graphql");
const short_uuid_1 = __importDefault(require("short-uuid"));
const organisation_model_1 = require("../../organisations/models/organisation.model");
const profile_model_1 = require("../../profiles/models/profile.model");
const post_model_1 = require("../../posts/models/post.model");
const event_model_1 = require("../../events/models/event.model");
const webinar_model_1 = require("../../webinars/models/webinar.model");
const graphql_type_json_1 = __importDefault(require("graphql-type-json"));
let OrganisationActivity = class OrganisationActivity extends sequelize_typescript_1.Model {
};
exports.OrganisationActivity = OrganisationActivity;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], OrganisationActivity.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, {
        nullable: false,
    }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], OrganisationActivity.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_type_json_1.default, {
        nullable: true,
    }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.JSONB,
    }),
    __metadata("design:type", Object)
], OrganisationActivity.prototype, "data", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], OrganisationActivity.prototype, "organisationId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, {
        foreignKey: 'organisationId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", organisation_model_1.Organisation)
], OrganisationActivity.prototype, "organisation", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    (0, sequelize_typescript_1.Column)({
        allowNull: true,
    }),
    __metadata("design:type", String)
], OrganisationActivity.prototype, "parentOrganisationId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, {
        foreignKey: 'parentOrganisationId',
        onDelete: 'SET NULL',
    }),
    __metadata("design:type", organisation_model_1.Organisation)
], OrganisationActivity.prototype, "parentOrganisation", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], OrganisationActivity.prototype, "createdById", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, {
        foreignKey: 'createdById',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", profile_model_1.Profile)
], OrganisationActivity.prototype, "createdBy", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => post_model_1.Post),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], OrganisationActivity.prototype, "postId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => post_model_1.Post, {
        foreignKey: 'postId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", post_model_1.Post)
], OrganisationActivity.prototype, "post", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => event_model_1.Event),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], OrganisationActivity.prototype, "eventId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => event_model_1.Event, {
        foreignKey: 'eventId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", event_model_1.Event)
], OrganisationActivity.prototype, "event", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => webinar_model_1.Webinar),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], OrganisationActivity.prototype, "webinarId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => webinar_model_1.Webinar, {
        foreignKey: 'webinarId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", webinar_model_1.Webinar)
], OrganisationActivity.prototype, "webinar", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], OrganisationActivity.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], OrganisationActivity.prototype, "updatedAt", void 0);
exports.OrganisationActivity = OrganisationActivity = __decorate([
    sequelize_typescript_1.Table,
    (0, graphql_1.ObjectType)()
], OrganisationActivity);
//# sourceMappingURL=organisation-activity.model.js.map