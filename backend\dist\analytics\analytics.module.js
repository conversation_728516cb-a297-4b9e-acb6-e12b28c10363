"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsModule = void 0;
const common_1 = require("@nestjs/common");
const analytics_resolver_1 = require("./analytics.resolver");
const common_module_1 = require("../common/common.module");
const analytics_service_1 = require("./analytics.service");
const activities_module_1 = require("../activities/activities.module");
const analytics_repository_1 = require("./analytics.repository");
const analytics_service_helper_1 = require("./helpers/analytics.service.helper");
const partner_organisations_module_1 = require("../partner-organisations/partner-organisations.module");
let AnalyticsModule = class AnalyticsModule {
};
exports.AnalyticsModule = AnalyticsModule;
exports.AnalyticsModule = AnalyticsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            (0, common_1.forwardRef)(() => common_module_1.CommonModule),
            (0, common_1.forwardRef)(() => activities_module_1.ActivitiesModule),
            (0, common_1.forwardRef)(() => partner_organisations_module_1.PartnerOrganisationsModule)
        ],
        providers: [
            analytics_service_1.AnalyticsService,
            analytics_resolver_1.AnalyticsResolver,
            analytics_repository_1.AnalyticsRepository,
            analytics_service_helper_1.AnalyticsServiceHelper,
        ],
        exports: [analytics_service_1.AnalyticsService],
    })
], AnalyticsModule);
//# sourceMappingURL=analytics.module.js.map