"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsRepository = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("sequelize");
const activities_service_1 = require("../activities/activities.service");
const sequelize_typescript_1 = require("sequelize-typescript");
const activities_args_1 = require("../activities/args/activities.args");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
let AnalyticsRepository = class AnalyticsRepository {
    async calculateAnalytics(startDate, endDate, organisationId, webinarId, filter) {
        var _a;
        const queryParams = webinarId
            ? {
                organisationId,
                webinarId,
                createdAt: {
                    [sequelize_1.Op.between]: [startDate, endDate],
                },
            }
            : {
                organisationId,
                createdAt: {
                    [sequelize_1.Op.between]: [startDate, endDate],
                },
            };
        if (((_a = filter === null || filter === void 0 ? void 0 : filter.type) === null || _a === void 0 ? void 0 : _a.length) > 0) {
            queryParams['type'] = {
                [sequelize_1.Op.in]: filter.type,
            };
        }
        if (filter === null || filter === void 0 ? void 0 : filter.period) {
            queryParams['period'] = filter.period;
        }
        const results = await this.activitiesService.getModel().findAll({
            where: queryParams,
            attributes: [
                [sequelize_typescript_1.Sequelize.literal(`DATE("createdAt")`), 'date'],
                'organisationId',
                'type',
                [sequelize_typescript_1.Sequelize.fn('count', sequelize_typescript_1.Sequelize.col('id')), 'sum'],
                [sequelize_typescript_1.Sequelize.fn('count', sequelize_typescript_1.Sequelize.json('data.unfollow')), 'unfollowSum'],
                [sequelize_typescript_1.Sequelize.fn('count', sequelize_typescript_1.Sequelize.json('data.follow')), 'followSum'],
            ],
            group: ['organisationId', 'type', 'date'],
            order: [[sequelize_typescript_1.Sequelize.literal('date'), 'ASC']],
            raw: true,
        });
        return results.map(result => ({
            date: new Date(result.date),
            organisationId: result.organisationId,
            type: activities_args_1.ActivityType[result.type],
            sum: parseInt(result.sum),
            followSum: parseInt(result.followSum),
            unfollowSum: parseInt(result.unfollowSum),
            webinarId: result.webinarId,
        }));
    }
    async calculateAggregatedAnalytics(startDate, endDate, parentOrganisationId, childOrganisationIds, filter) {
        var _a, _b, _c;
        const parentActivitiesQuery = {
            organisationId: parentOrganisationId,
            createdAt: {
                [sequelize_1.Op.between]: [startDate, endDate],
            },
        };
        if (((_a = filter === null || filter === void 0 ? void 0 : filter.type) === null || _a === void 0 ? void 0 : _a.length) > 0) {
            parentActivitiesQuery['type'] = {
                [sequelize_1.Op.in]: filter.type,
            };
        }
        if (filter === null || filter === void 0 ? void 0 : filter.period) {
            parentActivitiesQuery['period'] = filter.period;
        }
        const parentResults = await this.activitiesService.getModel().findAll({
            where: parentActivitiesQuery,
            attributes: [
                [sequelize_typescript_1.Sequelize.literal(`DATE("createdAt")`), 'date'],
                'organisationId',
                'type',
                [sequelize_typescript_1.Sequelize.fn('count', sequelize_typescript_1.Sequelize.col('id')), 'sum'],
                [sequelize_typescript_1.Sequelize.fn('count', sequelize_typescript_1.Sequelize.json('data.unfollow')), 'unfollowSum'],
                [sequelize_typescript_1.Sequelize.fn('count', sequelize_typescript_1.Sequelize.json('data.follow')), 'followSum'],
                'webinarId',
            ],
            group: ['organisationId', 'type', 'date', 'webinarId'],
            order: [[sequelize_typescript_1.Sequelize.literal('date'), 'ASC']],
            raw: true,
        });
        const partnerPostActivitiesQuery = `
      SELECT 
        DATE(a."createdAt") as date,
        a."organisationId",
        a.type,
        COUNT(a.id) as sum,
        COUNT(a.data->>'unfollow') as "unfollowSum",
        COUNT(a.data->>'follow') as "followSum",
        a."webinarId"
      FROM "Activities" a
      JOIN "Posts" p ON a."postId" = p.id
      WHERE a."organisationId" IN (:childOrgIds)
      AND p."parentOrgId" = :parentOrgId
      AND a."createdAt" BETWEEN :startDate AND :endDate
      ${((_b = filter === null || filter === void 0 ? void 0 : filter.type) === null || _b === void 0 ? void 0 : _b.length) ? 'AND a.type IN (:types)' : ''}
      ${(filter === null || filter === void 0 ? void 0 : filter.period) ? 'AND a.period = :period' : ''}
      GROUP BY DATE(a."createdAt"), a."organisationId", a.type, a."webinarId"
      ORDER BY DATE(a."createdAt") ASC
    `;
        const replacements = Object.assign(Object.assign({ childOrgIds: childOrganisationIds, parentOrgId: parentOrganisationId, startDate,
            endDate }, (((_c = filter === null || filter === void 0 ? void 0 : filter.type) === null || _c === void 0 ? void 0 : _c.length) ? { types: filter.type } : {})), ((filter === null || filter === void 0 ? void 0 : filter.period) ? { period: filter.period } : {}));
        let partnerPostResults = [];
        try {
            const queryResult = await this.sequelize.query(partnerPostActivitiesQuery, {
                replacements,
                type: 'SELECT',
                raw: true,
            });
            partnerPostResults = Array.isArray(queryResult) ? queryResult : (queryResult ? [queryResult] : []);
        }
        catch (error) {
            this.logger.warn('Failed to fetch partner post activities, continuing with empty results', {
                parentOrganisationId,
                childOrganisationIds,
                error: error.message
            });
        }
        const allResults = [
            ...parentResults.map(result => ({
                date: new Date(result.date),
                organisationId: result.organisationId,
                type: activities_args_1.ActivityType[result.type],
                sum: parseInt(result.sum),
                followSum: parseInt(result.followSum || '0'),
                unfollowSum: parseInt(result.unfollowSum || '0'),
                webinarId: result.webinarId
            })),
            ...partnerPostResults.map((result) => {
                let mappedType = result.type;
                if (result.type === 'PostView') {
                    mappedType = 'PostSeen';
                }
                return {
                    date: new Date(result.date),
                    organisationId: parentOrganisationId,
                    type: activities_args_1.ActivityType[mappedType] || activities_args_1.ActivityType[result.type],
                    sum: parseInt(result.sum),
                    followSum: parseInt(result.followSum || '0'),
                    unfollowSum: parseInt(result.unfollowSum || '0'),
                    webinarId: result.webinarId
                };
            })
        ];
        const aggregatedResults = new Map();
        allResults.forEach(result => {
            const key = `${result.date.toISOString().split('T')[0]}_${result.type}_${result.webinarId || 'null'}`;
            if (!aggregatedResults.has(key)) {
                aggregatedResults.set(key, {
                    date: result.date,
                    organisationId: parentOrganisationId,
                    type: result.type,
                    sum: 0,
                    followSum: 0,
                    unfollowSum: 0,
                    webinarId: result.webinarId
                });
            }
            const existing = aggregatedResults.get(key);
            existing.sum += result.sum;
            existing.followSum += result.followSum;
            existing.unfollowSum += result.unfollowSum;
        });
        return Array.from(aggregatedResults.values());
    }
};
exports.AnalyticsRepository = AnalyticsRepository;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => activities_service_1.ActivitiesService)),
    __metadata("design:type", activities_service_1.ActivitiesService)
], AnalyticsRepository.prototype, "activitiesService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", sequelize_typescript_1.Sequelize)
], AnalyticsRepository.prototype, "sequelize", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], AnalyticsRepository.prototype, "logger", void 0);
exports.AnalyticsRepository = AnalyticsRepository = __decorate([
    (0, common_1.Injectable)()
], AnalyticsRepository);
//# sourceMappingURL=analytics.repository.js.map