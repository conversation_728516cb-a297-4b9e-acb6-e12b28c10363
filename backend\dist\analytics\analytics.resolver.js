"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const analytics_service_1 = require("./analytics.service");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const analytics_args_1 = require("./args/analytics.args");
const error_1 = require("../common/helpers/error");
let AnalyticsResolver = class AnalyticsResolver {
    analytics(user, calculateAnalyticArgs) {
        this.logger.verbose('AnalyticsResolver.analytics (query)', {
            user: user.toLogObject(),
            calculateAnalyticArgs,
        });
        return this.analyticsService.calculateAnalytics(calculateAnalyticArgs.startDate, calculateAnalyticArgs.endDate, calculateAnalyticArgs.organisationId, calculateAnalyticArgs.webinarId);
    }
    aggregatedAnalytics(user, calculateAnalyticArgs) {
        this.logger.verbose('AnalyticsResolver.aggregatedAnalytics (query)', {
            user: user.toLogObject(),
            calculateAnalyticArgs,
        });
        return this.analyticsService.calculateAggregatedAnalytics(calculateAnalyticArgs.startDate, calculateAnalyticArgs.endDate, calculateAnalyticArgs.organisationId);
    }
};
exports.AnalyticsResolver = AnalyticsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => analytics_service_1.AnalyticsService)),
    __metadata("design:type", analytics_service_1.AnalyticsService)
], AnalyticsResolver.prototype, "analyticsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], AnalyticsResolver.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], AnalyticsResolver.prototype, "errorHelper", void 0);
__decorate([
    (0, graphql_1.Query)(() => analytics_args_1.AnalyticsResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, analytics_args_1.CalculateAnalyticsArgs]),
    __metadata("design:returntype", Promise)
], AnalyticsResolver.prototype, "analytics", null);
__decorate([
    (0, graphql_1.Query)(() => analytics_args_1.AnalyticsResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, analytics_args_1.CalculateAnalyticsArgs]),
    __metadata("design:returntype", Promise)
], AnalyticsResolver.prototype, "aggregatedAnalytics", null);
exports.AnalyticsResolver = AnalyticsResolver = __decorate([
    (0, graphql_1.Resolver)()
], AnalyticsResolver);
//# sourceMappingURL=analytics.resolver.js.map