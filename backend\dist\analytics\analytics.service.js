"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const analytics_repository_1 = require("./analytics.repository");
const analytics_service_helper_1 = require("../analytics/helpers/analytics.service.helper");
const partner_organisations_service_1 = require("../partner-organisations/partner-organisations.service");
const partner_organisations_args_1 = require("../partner-organisations/args/partner-organisations.args");
let AnalyticsService = class AnalyticsService {
    async calculateAnalytics(startDate, endDate, organisationId, webinarId) {
        const calculationResults = await this.analyticsRepository.calculateAnalytics(startDate, endDate, organisationId, webinarId, {});
        return this.analyticsServiceHelper.getAnalyticsResponse(startDate, endDate, calculationResults);
    }
    async calculateAggregatedAnalytics(startDate, endDate, parentOrganisationId) {
        const partnerships = await this.partnerOrganisationsService.findByParentOrgId(parentOrganisationId, { status: [partner_organisations_args_1.PartnerOrganisationStatus.Approved] });
        const childOrgIds = partnerships.map(p => p.childOrgId);
        if (childOrgIds.length === 0) {
            this.logger.debug('No approved child organizations found for parent', { parentOrganisationId });
        }
        const calculationResults = await this.analyticsRepository.calculateAggregatedAnalytics(startDate, endDate, parentOrganisationId, childOrgIds);
        return this.analyticsServiceHelper.getAnalyticsResponse(startDate, endDate, calculationResults);
    }
};
exports.AnalyticsService = AnalyticsService;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => analytics_repository_1.AnalyticsRepository)),
    __metadata("design:type", analytics_repository_1.AnalyticsRepository)
], AnalyticsService.prototype, "analyticsRepository", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => analytics_service_helper_1.AnalyticsServiceHelper)),
    __metadata("design:type", analytics_service_helper_1.AnalyticsServiceHelper)
], AnalyticsService.prototype, "analyticsServiceHelper", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => partner_organisations_service_1.PartnerOrganisationsService)),
    __metadata("design:type", partner_organisations_service_1.PartnerOrganisationsService)
], AnalyticsService.prototype, "partnerOrganisationsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], AnalyticsService.prototype, "logger", void 0);
exports.AnalyticsService = AnalyticsService = __decorate([
    (0, common_1.Injectable)()
], AnalyticsService);
//# sourceMappingURL=analytics.service.js.map