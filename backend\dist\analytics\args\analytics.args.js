"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsResponse = exports.AnalyticsResponseValue = exports.AnalyticsCalculationResult = exports.CalculateAnalyticsArgs = exports.AnalyticsArgs = exports.AnalyticsFilter = exports.AnalyticPeriod = void 0;
const graphql_1 = require("@nestjs/graphql");
const pagination_args_1 = require("../../common/args/pagination.args");
const activities_args_1 = require("../../activities/args/activities.args");
var AnalyticPeriod;
(function (AnalyticPeriod) {
    AnalyticPeriod["Day"] = "Day";
})(AnalyticPeriod || (exports.AnalyticPeriod = AnalyticPeriod = {}));
(0, graphql_1.registerEnumType)(AnalyticPeriod, { name: 'AnalyticPeriod' });
let AnalyticsFilter = class AnalyticsFilter {
};
exports.AnalyticsFilter = AnalyticsFilter;
__decorate([
    (0, graphql_1.Field)(() => [activities_args_1.ActivityType], { nullable: true, defaultValue: [] }),
    __metadata("design:type", Array)
], AnalyticsFilter.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)(() => AnalyticPeriod, { nullable: true }),
    __metadata("design:type", String)
], AnalyticsFilter.prototype, "period", void 0);
__decorate([
    (0, graphql_1.Field)(() => Date, { nullable: true }),
    __metadata("design:type", Date)
], AnalyticsFilter.prototype, "fromDate", void 0);
__decorate([
    (0, graphql_1.Field)(() => Date, { nullable: true }),
    __metadata("design:type", Date)
], AnalyticsFilter.prototype, "toDate", void 0);
exports.AnalyticsFilter = AnalyticsFilter = __decorate([
    (0, graphql_1.InputType)()
], AnalyticsFilter);
let AnalyticsArgs = class AnalyticsArgs extends pagination_args_1.PaginationArgs {
};
exports.AnalyticsArgs = AnalyticsArgs;
__decorate([
    (0, graphql_1.Field)(() => AnalyticsFilter, { nullable: true }),
    __metadata("design:type", AnalyticsFilter)
], AnalyticsArgs.prototype, "filter", void 0);
exports.AnalyticsArgs = AnalyticsArgs = __decorate([
    (0, graphql_1.ArgsType)()
], AnalyticsArgs);
let CalculateAnalyticsArgs = class CalculateAnalyticsArgs {
};
exports.CalculateAnalyticsArgs = CalculateAnalyticsArgs;
__decorate([
    (0, graphql_1.Field)(() => Date),
    __metadata("design:type", Date)
], CalculateAnalyticsArgs.prototype, "startDate", void 0);
__decorate([
    (0, graphql_1.Field)(() => Date),
    __metadata("design:type", Date)
], CalculateAnalyticsArgs.prototype, "endDate", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CalculateAnalyticsArgs.prototype, "organisationId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], CalculateAnalyticsArgs.prototype, "webinarId", void 0);
exports.CalculateAnalyticsArgs = CalculateAnalyticsArgs = __decorate([
    (0, graphql_1.ArgsType)()
], CalculateAnalyticsArgs);
let AnalyticsCalculationResult = class AnalyticsCalculationResult {
};
exports.AnalyticsCalculationResult = AnalyticsCalculationResult;
__decorate([
    (0, graphql_1.Field)(() => Date),
    __metadata("design:type", Date)
], AnalyticsCalculationResult.prototype, "date", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], AnalyticsCalculationResult.prototype, "organisationId", void 0);
__decorate([
    (0, graphql_1.Field)(() => activities_args_1.ActivityType),
    __metadata("design:type", String)
], AnalyticsCalculationResult.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], AnalyticsCalculationResult.prototype, "sum", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], AnalyticsCalculationResult.prototype, "unfollowSum", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], AnalyticsCalculationResult.prototype, "followSum", void 0);
exports.AnalyticsCalculationResult = AnalyticsCalculationResult = __decorate([
    (0, graphql_1.ObjectType)()
], AnalyticsCalculationResult);
let AnalyticsResponseValue = class AnalyticsResponseValue {
};
exports.AnalyticsResponseValue = AnalyticsResponseValue;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], AnalyticsResponseValue.prototype, "date", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int),
    __metadata("design:type", Number)
], AnalyticsResponseValue.prototype, "value", void 0);
exports.AnalyticsResponseValue = AnalyticsResponseValue = __decorate([
    (0, graphql_1.ObjectType)()
], AnalyticsResponseValue);
let AnalyticsResponse = class AnalyticsResponse {
};
exports.AnalyticsResponse = AnalyticsResponse;
_a = activities_args_1.ActivityType.OrganisationFollower;
_b = activities_args_1.ActivityType.OrganisationPageView;
_c = activities_args_1.ActivityType.PostSeen;
_d = activities_args_1.ActivityType.EventRSVP;
_e = activities_args_1.ActivityType.WebinarVideoView;
_f = activities_args_1.ActivityType.PostInteractions;
_g = activities_args_1.ActivityType.PostImpressions;
__decorate([
    (0, graphql_1.Field)(() => [AnalyticsResponseValue]),
    __metadata("design:type", Array)
], AnalyticsResponse.prototype, _a, void 0);
__decorate([
    (0, graphql_1.Field)(() => [AnalyticsResponseValue]),
    __metadata("design:type", Array)
], AnalyticsResponse.prototype, _b, void 0);
__decorate([
    (0, graphql_1.Field)(() => [AnalyticsResponseValue]),
    __metadata("design:type", Array)
], AnalyticsResponse.prototype, _c, void 0);
__decorate([
    (0, graphql_1.Field)(() => [AnalyticsResponseValue]),
    __metadata("design:type", Array)
], AnalyticsResponse.prototype, _d, void 0);
__decorate([
    (0, graphql_1.Field)(() => [AnalyticsResponseValue]),
    __metadata("design:type", Array)
], AnalyticsResponse.prototype, _e, void 0);
__decorate([
    (0, graphql_1.Field)(() => [AnalyticsResponseValue]),
    __metadata("design:type", Array)
], AnalyticsResponse.prototype, _f, void 0);
__decorate([
    (0, graphql_1.Field)(() => [AnalyticsResponseValue]),
    __metadata("design:type", Array)
], AnalyticsResponse.prototype, _g, void 0);
exports.AnalyticsResponse = AnalyticsResponse = __decorate([
    (0, graphql_1.ObjectType)()
], AnalyticsResponse);
//# sourceMappingURL=analytics.args.js.map