"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsServiceHelper = void 0;
const moment_1 = __importDefault(require("moment"));
const common_1 = require("@nestjs/common");
const activities_args_1 = require("../../activities/args/activities.args");
let AnalyticsServiceHelper = class AnalyticsServiceHelper {
    getAnalyticsResponse(startDate, endDate, calculationResults) {
        const days = this.getDaysArray(startDate, endDate);
        const analyticsResponse = {
            [activities_args_1.ActivityType.OrganisationFollower]: days.map(day => ({
                date: day,
                value: 0,
            })),
            [activities_args_1.ActivityType.OrganisationPageView]: days.map(day => ({
                date: day,
                value: 0,
            })),
            [activities_args_1.ActivityType.PostSeen]: days.map(day => ({ date: day, value: 0 })),
            [activities_args_1.ActivityType.EventRSVP]: days.map(day => ({ date: day, value: 0 })),
            [activities_args_1.ActivityType.WebinarVideoView]: days.map(day => ({
                date: day,
                value: 0,
            })),
            [activities_args_1.ActivityType.PostInteractions]: days.map(day => ({ date: day, value: 0 })),
            [activities_args_1.ActivityType.PostImpressions]: days.map(day => ({ date: day, value: 0 })),
        };
        for (const calculationResult of calculationResults) {
            const value = calculationResult.type === activities_args_1.ActivityType.OrganisationFollower
                ? calculationResult.followSum - calculationResult.unfollowSum
                : calculationResult.sum;
            const entry = analyticsResponse[calculationResult.type] && analyticsResponse[calculationResult.type].find(entry => entry.date === calculationResult.date.toISOString().substring(0, 10));
            if (entry) {
                entry.value = value;
            }
        }
        return analyticsResponse;
    }
    getDaysArray(startDate, endDate) {
        const addDays = function (date, days) {
            return (0, moment_1.default)(date.valueOf()).add(days, 'days').toDate();
        };
        const dateArray = [];
        let currentDate = startDate;
        while (currentDate <= endDate) {
            dateArray.push(new Date(currentDate).toISOString().substring(0, 10));
            currentDate = addDays(currentDate, 1);
        }
        return dateArray;
    }
};
exports.AnalyticsServiceHelper = AnalyticsServiceHelper;
exports.AnalyticsServiceHelper = AnalyticsServiceHelper = __decorate([
    (0, common_1.Injectable)()
], AnalyticsServiceHelper);
//# sourceMappingURL=analytics.service.helper.js.map