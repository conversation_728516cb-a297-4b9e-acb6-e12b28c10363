"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
describe('AppController', () => {
    let app;
    beforeAll(async () => {
        app = await testing_1.Test.createTestingModule({
            controllers: [app_controller_1.AppController],
            providers: [app_service_1.AppService],
        }).compile();
    });
    describe('ping', () => {
        it('should return Ok when ping for health checks', () => {
            const appController = app.get(app_controller_1.AppController);
            expect(appController.ping()).toBe('Ok');
        });
    });
});
//# sourceMappingURL=app.controller.spec_.js.map