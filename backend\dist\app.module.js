"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const path_1 = require("path");
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const graphql_1 = require("@nestjs/graphql");
const nestjs_redis_1 = require("nestjs-redis");
const nest_winston_1 = require("nest-winston");
const winston_1 = __importDefault(require("winston"));
const logging_winston_1 = require("@google-cloud/logging-winston");
const nestjs_sendgrid_1 = require("@ntegral/nestjs-sendgrid");
const schedule_1 = require("@nestjs/schedule");
const config_1 = __importDefault(require("./config/config"));
const profiles_module_1 = require("./profiles/profiles.module");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const authz_module_1 = require("./authz/authz.module");
const common_module_1 = require("./common/common.module");
const connections_module_1 = require("./connections/connections.module");
const connection_requests_module_1 = require("./connection-requests/connection-requests.module");
const notifications_module_1 = require("./notifications/notifications.module");
const pubsub_module_1 = require("./pubsub/pubsub.module");
const organisations_module_1 = require("./organisations/organisations.module");
const memberships_module_1 = require("./memberships/memberships.module");
const followers_module_1 = require("./followers/followers.module");
const cloudinary_module_1 = require("./cloudinary/cloudinary.module");
const experiences_module_1 = require("./experiences/experiences.module");
const complexity_plugin_1 = require("./common/plugins/complexity.plugin");
const posts_module_1 = require("./posts/posts.module");
const events_module_1 = require("./events/events.module");
const event_invitations_module_1 = require("./event-invitations/event-invitations.module");
const activities_module_1 = require("./activities/activities.module");
const analytics_module_1 = require("./analytics/analytics.module");
const email_module_1 = require("./email/email.module");
const chat_module_1 = require("./chat/chat.module");
const partnership_requests_module_1 = require("./partnership-requests/partnership-requests.module");
const partnerships_module_1 = require("./partnerships/partnerships.module");
const incentives_module_1 = require("./incentives/incentives.module");
const incentive_participants_module_1 = require("./incentive-participants/incentive-participants.module");
const incentive_bookings_module_1 = require("./incentive-bookings/incentive-bookings.module");
const webinar_broadcast_module_1 = require("./webinar-broadcast/webinar-broadcast.module");
const webinar_participants_module_1 = require("./webinar-participants/webinar-participants.module");
const webinars_module_1 = require("./webinars/webinars.module");
const email_invitations_resolver_1 = require("./email-invitations/email-invitations.resolver");
const email_invitations_module_1 = require("./email-invitations/email-invitations.module");
const stream_module_1 = require("./feeds/stream.module");
const stream_followers_module_1 = require("./feeds-followers/stream-followers.module");
const stream_organisations_module_1 = require("./feeds-organisations/stream-organisations.module");
const stream_posts_module_1 = require("./feeds-posts/stream-posts.module");
const stream_webinars_module_1 = require("./feeds-webinars/stream-webinars.module");
const app_cron_1 = require("./app.cron");
const stream_webinar_participants_module_1 = require("./feeds-webinar-participants/stream-webinar-participants.module");
const stream_events_module_1 = require("./feeds-events/stream-events.module");
const stream_events_invitations_module_1 = require("./feeds-event-invitations/stream-events-invitations.module");
const stream_incentives_module_1 = require("./feeds-incentives/stream-incentives.module");
const stream_incentive_participants_module_1 = require("./feeds-incentive-participants/stream-incentive-participants.module");
const stream_updates_module_1 = require("./feeds-updates/stream-updates.module");
const explore_pages_module_1 = require("./explore-pages/explore-pages.module");
const autofollow_module_1 = require("./autofollow/autofollow.module");
const suggest_follow_module_1 = require("./suggest-follow/suggest-follow.module");
const autocomplete_module_1 = require("./autocomplete/autocomplete.module");
const subscriptions_module_1 = require("./subscriptions/subscriptions.module");
const payment_transactions_module_1 = require("./payment-transactions/payment-transactions.module");
const subscription_webhook_module_1 = require("./subscription-webhooks/subscription-webhook.module");
const subscription_webhook_controller_1 = require("./subscription-webhooks/subscription-webhook.controller");
const stripe_1 = require("./subscriptions/helpers/stripe");
const docsbot_ai_module_1 = require("./docsbot-ai/docsbot-ai.module");
const webinar_participants_controller_1 = require("./webinar-participants/webinar-participants.controller");
const incentive_participants_controller_1 = require("./incentive-participants/incentive-participants.controller");
const event_invitations_controller_1 = require("./event-invitations/event-invitations.controller");
const notifications_controller_1 = require("./notifications/notifications.controller");
const posts_controller_1 = require("./posts/posts.controller");
const getting_started_steps_module_1 = require("./getting-started-steps/getting-started-steps.module");
const loyalty_points_module_1 = require("./loyalty-points/loyalty-points.module");
const achievements_module_1 = require("./achievements/achievements.module");
const mux_module_1 = require("./mux/mux.module");
const db_monitoring_service_1 = require("./common/services/db-monitoring.service");
const partner_organisations_module_1 = require("./partner-organisations/partner-organisations.module");
let AppModule = class AppModule {
    constructor(dbMonitoringService) {
        this.dbMonitoringService = dbMonitoringService;
        this.dbMonitoringService.setupMonitoring();
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            schedule_1.ScheduleModule.forRoot(),
            graphql_1.GraphQLModule.forRoot({
                context: ({ req, res, payload, connection }) => ({
                    req,
                    res,
                    payload,
                    connection,
                }),
                subscriptions: {
                    onConnect: (connectionParams, websocket) => {
                        var _a;
                        return ({
                            headers: Object.assign(Object.assign({}, (_a = websocket === null || websocket === void 0 ? void 0 : websocket.upgradeReq) === null || _a === void 0 ? void 0 : _a.headers), { authorization: connectionParams === null || connectionParams === void 0 ? void 0 : connectionParams.Authorization }),
                        });
                    },
                },
                installSubscriptionHandlers: true,
                autoSchemaFile: !!process.env.AUTO_SCHEMA_FILE
                    ? true
                    : (0, path_1.join)(process.cwd(), 'src/schema.gql'),
                fieldResolverEnhancers: ['interceptors'],
                plugins: [new complexity_plugin_1.ApolloComplexityPlugin(150)],
            }),
            sequelize_1.SequelizeModule.forRoot({
                dialect: 'postgres',
                host: config_1.default.DB_HOST,
                port: config_1.default.DB_PORT,
                username: config_1.default.DB_USER,
                password: config_1.default.DB_PASS,
                database: config_1.default.DB_NAME,
                autoLoadModels: true,
                synchronize: false,
                logging: false,
                pool: {
                    max: 75,
                    min: 15,
                    acquire: 60000,
                    idle: 30000,
                    evict: 1000,
                },
            }),
            nest_winston_1.WinstonModule.forRoot({
                level: config_1.default.LOG_LEVEL,
                transports: config_1.default.NODE_ENV === 'production'
                    ? [new winston_1.default.transports.Console(), new logging_winston_1.LoggingWinston()]
                    : [
                        new winston_1.default.transports.Console({
                            format: winston_1.default.format.combine(winston_1.default.format.timestamp(), nest_winston_1.utilities.format.nestLike()),
                        }),
                    ],
            }),
            nestjs_sendgrid_1.SendGridModule.forRoot({
                apiKey: config_1.default.SENDGRID_API_KEY,
            }),
            nestjs_redis_1.RedisModule.register({
                url: `redis://${config_1.default.REDIS_HOST}:${config_1.default.REDIS_PORT}`,
                keyPrefix: config_1.default.DB_NAME,
            }),
            authz_module_1.AuthZeroModule,
            profiles_module_1.ProfilesModule,
            connection_requests_module_1.ConnectionRequestsModule,
            connections_module_1.ConnectionsModule,
            notifications_module_1.NotificationsModule,
            common_module_1.CommonModule,
            pubsub_module_1.PubSubModule,
            organisations_module_1.OrganisationsModule,
            memberships_module_1.MembershipsModule,
            followers_module_1.FollowersModule,
            cloudinary_module_1.CloudinaryModule,
            experiences_module_1.ExperiencesModule,
            posts_module_1.PostsModule,
            events_module_1.EventsModule,
            event_invitations_module_1.EventsInvitationsModule,
            activities_module_1.ActivitiesModule,
            chat_module_1.ChatModule,
            analytics_module_1.AnalyticsModule,
            email_module_1.EmailModule,
            partnership_requests_module_1.PartnershipRequestsModule,
            partnerships_module_1.PartnershipsModule,
            incentives_module_1.IncentivesModule,
            incentive_participants_module_1.IncentiveParticipantsModule,
            incentive_bookings_module_1.IncentiveBookingsModule,
            webinar_broadcast_module_1.WebinarBroadcastModule,
            webinars_module_1.WebinarsModule,
            webinar_participants_module_1.WebinarParticipantsModule,
            email_invitations_module_1.EmailInvitationsModule,
            stream_module_1.StreamModule,
            stream_followers_module_1.StreamFollowersModule,
            stream_organisations_module_1.StreamOrganisationsModule,
            stream_posts_module_1.StreamPostsModule,
            stream_webinars_module_1.StreamWebinarsModule,
            stream_webinar_participants_module_1.StreamWebinarParticipantsModule,
            stream_events_module_1.StreamEventsModule,
            stream_events_invitations_module_1.StreamEventsInvitationsModule,
            stream_incentives_module_1.StreamIncentivesModule,
            stream_incentive_participants_module_1.StreamIncentiveParticipantsModule,
            stream_updates_module_1.StreamUpdatesModule,
            explore_pages_module_1.DestinationPagesModule,
            autofollow_module_1.AutofollowModule,
            suggest_follow_module_1.SuggestFollowModule,
            autocomplete_module_1.AutocompleteModule,
            subscriptions_module_1.SubscriptionsModule,
            payment_transactions_module_1.PaymentTransactionsModule,
            subscription_webhook_module_1.SubscriptionWebhookModule,
            stripe_1.StripeHelper,
            docsbot_ai_module_1.DocsbotAIModule,
            getting_started_steps_module_1.GettingStartedStepsModule,
            loyalty_points_module_1.LoyaltyPointsModule,
            achievements_module_1.AchievementsModule,
            mux_module_1.MuxModule,
            partner_organisations_module_1.PartnerOrganisationsModule,
        ],
        controllers: [
            app_controller_1.AppController,
            subscription_webhook_controller_1.SubscriptionWebhookController,
            webinar_participants_controller_1.WebinarParticipantController,
            incentive_participants_controller_1.IncentiveParticipantsController,
            event_invitations_controller_1.EventInvitationsController,
            notifications_controller_1.NotificationsController,
            posts_controller_1.PostsController,
        ],
        providers: [
            app_service_1.AppService,
            app_cron_1.TasksService,
            email_invitations_resolver_1.EmailInvitationsResolver,
            stripe_1.StripeHelper,
            db_monitoring_service_1.DatabaseMonitoringService,
        ],
    }),
    __metadata("design:paramtypes", [db_monitoring_service_1.DatabaseMonitoringService])
], AppModule);
//# sourceMappingURL=app.module.js.map