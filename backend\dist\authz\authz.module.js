"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthZeroModule = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const jwt_strategy_1 = require("./jwt.strategy");
const authz_service_1 = require("./authz.service");
const graphql_auth_guard_1 = require("./graphql-auth.guard");
let AuthZeroModule = class AuthZeroModule {
};
exports.AuthZeroModule = AuthZeroModule;
exports.AuthZeroModule = AuthZeroModule = __decorate([
    (0, common_1.Module)({
        imports: [passport_1.PassportModule.register({ defaultStrategy: 'jwt' })],
        providers: [jwt_strategy_1.JwtStrategy, authz_service_1.AuthZeroService, graphql_auth_guard_1.GqlAuthGuard],
        exports: [passport_1.PassportModule, authz_service_1.AuthZeroService],
    })
], AuthZeroModule);
//# sourceMappingURL=authz.module.js.map