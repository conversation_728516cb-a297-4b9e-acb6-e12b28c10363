"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthZeroService = void 0;
const common_1 = require("@nestjs/common");
const auth0_1 = require("auth0");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const config_1 = __importDefault(require("../config/config"));
let AuthZeroService = class AuthZeroService {
    onModuleInit() {
        this.managementClient = new auth0_1.ManagementClient({
            domain: config_1.default.AUTH0_DOMAIN,
            clientId: config_1.default.AUTH0_MANAGEMENT_CLIENT_ID,
            clientSecret: config_1.default.AUTH0_MANAGEMENT_CLIENT_SECRET,
        });
    }
    async getAuthZeroUser(authZeroUserId) {
        this.logger.info('AuthZeroService.getAuthZeroUser', {
            authZeroUserId,
        });
        const res = await this.managementClient.users.get({ id: authZeroUserId });
        return res.data;
    }
    async getUsersByEmail(email) {
        this.logger.info('AuthZeroService.getUsersByEmail', {
            email,
        });
        const res = await this.managementClient.usersByEmail.getByEmail({ email });
        return res.data;
    }
    async sendEmailVerification(authZeroUserId) {
        this.logger.info('AuthZeroService.sendEmailVerification', {
            authZeroUserId,
        });
        await this.managementClient.jobs.verifyEmail({
            user_id: authZeroUserId,
        });
        return true;
    }
    async changeEmail(authZeroUserId, newEmail) {
        this.logger.info('AuthZeroService.changeEmail', {
            authZeroUserId,
            newEmail,
        });
        await this.managementClient.users.update({ id: authZeroUserId }, { email: newEmail, email_verified: false });
        await this.sendEmailVerification(authZeroUserId);
        return true;
    }
    async removeUser(authZeroUserId) {
        this.logger.info('AuthZeroService.removeUser', {
            authZeroUserId,
        });
        await this.managementClient.users.delete({ id: authZeroUserId });
        return true;
    }
};
exports.AuthZeroService = AuthZeroService;
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], AuthZeroService.prototype, "logger", void 0);
exports.AuthZeroService = AuthZeroService = __decorate([
    (0, common_1.Injectable)()
], AuthZeroService);
//# sourceMappingURL=authz.service.js.map