{"version": 3, "file": "authz.service.js", "sourceRoot": "", "sources": ["../../src/authz/authz.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAkE;AAClE,iCAAwE;AACxE,+CAAuD;AACvD,qCAAiC;AACjC,8DAAsC;AAG/B,IAAM,eAAe,GAArB,MAAM,eAAe;IAM1B,YAAY;QACV,IAAI,CAAC,gBAAgB,GAAG,IAAI,wBAAgB,CAAC;YAC3C,MAAM,EAAE,gBAAM,CAAC,YAAY;YAC3B,QAAQ,EAAE,gBAAM,CAAC,0BAA0B;YAC3C,YAAY,EAAE,gBAAM,CAAC,8BAA8B;SACpD,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,cAAsB;QAEtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE;YAClD,cAAc;SACf,CAAC,CAAC;QAEH,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,cAAc,EAAE,CAAC,CAAC;QAC1E,OAAO,GAAG,CAAC,IAAI,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,KAAa;QAEb,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE;YAClD,KAAK;SACN,CAAC,CAAC;QAEH,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;QAC3E,OAAO,GAAG,CAAC,IAAI,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,cAAsB;QAChD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE;YACxD,cAAc;SACf,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC;YAC3C,OAAO,EAAE,cAAc;SACxB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,WAAW,CACf,cAAsB,EACtB,QAAgB;QAEhB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;YAC9C,cAAc;YACd,QAAQ;SACT,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAAM,CACtC,EAAE,EAAE,EAAE,cAAc,EAAE,EACtB,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,KAAK,EAAE,CAC3C,CAAC;QAEF,MAAM,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC;QAEjD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,cAAsB;QACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;YAC7C,cAAc;SACf,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,cAAc,EAAE,CAAC,CAAC;QAEjE,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAA;AA5EY,0CAAe;AAET;IADhB,IAAA,eAAM,EAAC,sCAAuB,CAAC;8BACP,gBAAM;+CAAC;0BAFrB,eAAe;IAD3B,IAAA,mBAAU,GAAE;GACA,eAAe,CA4E3B"}