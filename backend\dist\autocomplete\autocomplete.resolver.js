"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutocompleteResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const autocomplete_service_1 = require("./autocomplete.service");
const autocomplete_args_1 = require("./args/autocomplete.args");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
let AutocompleteResolver = class AutocompleteResolver {
    async autocompleteMentions(user, searchText, isFuzzySearch) {
        this.logger.verbose('AutocompleteResolver.autocompleteMentions (query)');
        return this.autocompleteService.getMentions(user.profileId, searchText, isFuzzySearch);
    }
};
exports.AutocompleteResolver = AutocompleteResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => autocomplete_service_1.AutocompleteService)),
    __metadata("design:type", autocomplete_service_1.AutocompleteService)
], AutocompleteResolver.prototype, "autocompleteService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], AutocompleteResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Query)(() => autocomplete_args_1.MentionsItem),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('searchText')),
    __param(2, (0, graphql_1.Args)('isFuzzySearch')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Boolean]),
    __metadata("design:returntype", Promise)
], AutocompleteResolver.prototype, "autocompleteMentions", null);
exports.AutocompleteResolver = AutocompleteResolver = __decorate([
    (0, graphql_1.Resolver)()
], AutocompleteResolver);
//# sourceMappingURL=autocomplete.resolver.js.map