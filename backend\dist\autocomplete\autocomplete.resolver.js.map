{"version": 3, "file": "autocomplete.resolver.js", "sourceRoot": "", "sources": ["../../src/autocomplete/autocomplete.resolver.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,6CAAqF;AACrF,2CAA+D;AAC/D,+CAAuD;AACvD,qCAAiC;AAEjC,oEAA2D;AAC3D,iEAA6D;AAC7D,gEAAsD;AACtD,wFAAsF;AAI/E,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAQvB,AAAN,KAAK,CAAC,oBAAoB,CACP,IAAkB,EACb,UAAkB,EACf,aAAuB;QAE9C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mDAAmD,CAAC,CAAC;QAEzE,OAAO,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;IAC3F,CAAC;CACJ,CAAA;AAjBY,oDAAoB;AAEZ;IADhB,IAAA,eAAM,EAAC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,0CAAmB,CAAC,CAAC;8BACR,0CAAmB;iEAAC;AAEzC;IADhB,IAAA,eAAM,EAAC,sCAAuB,CAAC;8BACP,gBAAM;oDAAC;AAI1B;IAFL,IAAA,eAAK,EAAC,GAAG,EAAE,CAAC,gCAAY,CAAC;IACzB,IAAA,kBAAS,EAAC,iCAAY,CAAC;IAEnB,WAAA,IAAA,oCAAW,GAAE,CAAA;IACb,WAAA,IAAA,cAAI,EAAC,YAAY,CAAC,CAAA;IAClB,WAAA,IAAA,cAAI,EAAC,eAAe,CAAC,CAAA;;;;gEAKzB;+BAhBQ,oBAAoB;IADhC,IAAA,kBAAQ,GAAE;GACE,oBAAoB,CAiBhC"}