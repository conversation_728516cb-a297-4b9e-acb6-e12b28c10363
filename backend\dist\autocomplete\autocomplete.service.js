"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutocompleteService = void 0;
const common_1 = require("@nestjs/common");
const organisations_service_1 = require("../organisations/organisations.service");
const profiles_service_1 = require("../profiles/profiles.service");
const pagination_args_1 = require("../common/args/pagination.args");
let AutocompleteService = class AutocompleteService {
    async getMentions(profileId, searchText, isFuzzySearch) {
        const profiles = await this.profilesService.findProfiles(profileId, {
            searchText,
            isFuzzySearch,
        }, {
            first: 5,
            after: null,
            sortBy: 'name',
            sortOrder: pagination_args_1.PaginationSortOrder.Descending,
        });
        const organisations = await this.organisationsService.findOrganisations(profileId, {
            searchText,
        }, {
            first: 5,
            after: null,
            sortBy: 'name',
            sortOrder: pagination_args_1.PaginationSortOrder.Descending,
        });
        return {
            profiles: profiles.records,
            organisations: organisations.records,
        };
    }
};
exports.AutocompleteService = AutocompleteService;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], AutocompleteService.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], AutocompleteService.prototype, "profilesService", void 0);
exports.AutocompleteService = AutocompleteService = __decorate([
    (0, common_1.Injectable)()
], AutocompleteService);
//# sourceMappingURL=autocomplete.service.js.map