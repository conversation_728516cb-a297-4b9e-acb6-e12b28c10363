{"version": 3, "file": "autocomplete.service.js", "sourceRoot": "", "sources": ["../../src/autocomplete/autocomplete.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA8E;AAE9E,kFAA8E;AAE9E,mEAA+D;AAC/D,oEAAqE;AAG9D,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAM9B,KAAK,CAAC,WAAW,CACf,SAAS,EACT,UAAkB,EAClB,aAAuB;QAEvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CACtD,SAAS,EACT;YACE,UAAU;YACV,aAAa;SACd,EACD;YACE,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,IAAI;YACX,MAAM,EAAE,MAAM;YACd,SAAS,EAAE,qCAAmB,CAAC,UAAU;SAC1C,CACF,CAAC;QAEF,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CACrE,SAAS,EACT;YACE,UAAU;SACX,EACD;YACE,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,IAAI;YACX,MAAM,EAAE,MAAM;YACd,SAAS,EAAE,qCAAmB,CAAC,UAAU;SAC1C,CACF,CAAC;QAEF,OAAO;YACL,QAAQ,EAAE,QAAQ,CAAC,OAAO;YAC1B,aAAa,EAAE,aAAa,CAAC,OAAO;SACrC,CAAC;IACJ,CAAC;CACF,CAAA;AA3CY,kDAAmB;AAEb;IADhB,IAAA,eAAM,EAAC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,4CAAoB,CAAC,CAAC;8BACR,4CAAoB;iEAAC;AAE3C;IADhB,IAAA,eAAM,EAAC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,kCAAe,CAAC,CAAC;8BACR,kCAAe;4DAAC;8BAJvC,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;GACA,mBAAmB,CA2C/B"}