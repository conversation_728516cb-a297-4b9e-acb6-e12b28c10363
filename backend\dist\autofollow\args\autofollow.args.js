"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RegionsList = exports.AutofollowRegionsItem = void 0;
const graphql_1 = require("@nestjs/graphql");
const organisation_model_1 = require("../../organisations/models/organisation.model");
const profiles_args_1 = require("../../profiles/args/profiles.args");
let AutofollowRegionsItem = class AutofollowRegionsItem {
};
exports.AutofollowRegionsItem = AutofollowRegionsItem;
__decorate([
    (0, graphql_1.Field)({ nullable: false }),
    __metadata("design:type", String)
], AutofollowRegionsItem.prototype, "title", void 0);
__decorate([
    (0, graphql_1.Field)(() => [organisation_model_1.Organisation], { nullable: true }),
    __metadata("design:type", Array)
], AutofollowRegionsItem.prototype, "organisations", void 0);
exports.AutofollowRegionsItem = AutofollowRegionsItem = __decorate([
    (0, graphql_1.ObjectType)()
], AutofollowRegionsItem);
exports.RegionsList = [
    profiles_args_1.Region.All,
    profiles_args_1.Region.UnitedStatesOfAmerica,
    profiles_args_1.Region.Africa,
    profiles_args_1.Region.Asia,
    profiles_args_1.Region.Canada,
    profiles_args_1.Region.TheCaribbean,
    profiles_args_1.Region.CentralAmerica,
    profiles_args_1.Region.Europe,
    profiles_args_1.Region.Mexico,
    profiles_args_1.Region.MiddleEast,
    profiles_args_1.Region.Oceania,
    profiles_args_1.Region.SouthAmerica,
];
//# sourceMappingURL=autofollow.args.js.map