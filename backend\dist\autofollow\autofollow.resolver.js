"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutofollowResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const autofollow_service_1 = require("./autofollow.service");
const autofollow_args_1 = require("./args/autofollow.args");
const update_autofollow_org_input_1 = require("./dto/update-autofollow-org.input");
let AutofollowResolver = class AutofollowResolver {
    async autofollowRegionsOrgs() {
        this.logger.verbose('AutofollowResolver.autofollowRegionsOrgs (query)');
        return await this.autofollowService.getOrganisationsForAutofollowRegions();
    }
    async updateAutofollowRegionOrgs(data) {
        const result = await this.autofollowService.updateOrgsForAutofollowRegion(data);
        return result;
    }
};
exports.AutofollowResolver = AutofollowResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => autofollow_service_1.AutofollowService)),
    __metadata("design:type", autofollow_service_1.AutofollowService)
], AutofollowResolver.prototype, "autofollowService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], AutofollowResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Query)(() => [autofollow_args_1.AutofollowRegionsItem]),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AutofollowResolver.prototype, "autofollowRegionsOrgs", null);
__decorate([
    (0, graphql_1.Mutation)(() => autofollow_args_1.AutofollowRegionsItem),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)('data')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [update_autofollow_org_input_1.UpdateAutofollowOrgInput]),
    __metadata("design:returntype", Promise)
], AutofollowResolver.prototype, "updateAutofollowRegionOrgs", null);
exports.AutofollowResolver = AutofollowResolver = __decorate([
    (0, graphql_1.Resolver)()
], AutofollowResolver);
//# sourceMappingURL=autofollow.resolver.js.map