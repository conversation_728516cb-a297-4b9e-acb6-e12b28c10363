"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutofollowService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const sequelize_typescript_1 = require("sequelize-typescript");
const error_1 = require("../common/helpers/error");
const winston_1 = require("winston");
const base_service_1 = require("../common/base.service");
const autofollow_args_1 = require("./args/autofollow.args");
const autofollow_model_1 = require("./models/autofollow.model");
const organisations_service_1 = require("../organisations/organisations.service");
const followers_service_1 = require("../followers/followers.service");
let AutofollowService = class AutofollowService extends (0, base_service_1.BaseService)(autofollow_model_1.AutoFollowRegionsOrganisations) {
    async followRegionOrgs(profileId, regions) {
        const transaction = await this.sequelize.transaction();
        try {
            const user = {
                sub: "",
                profileId: profileId,
                picture: "",
                email: "",
                name: "",
                idToken: "",
                toLogObject: () => false
            };
            for (let region of regions) {
                const allOrgsForRegion = await this.findAll({
                    autoFollowRegion: region
                }, { transaction });
                for (let org of allOrgsForRegion) {
                    await this.followersService.follow(user, org.organisationId);
                }
            }
            await transaction.commit();
            return true;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`AutofollowService.addOrganisationToAutofollowRegion`, e.message);
        }
    }
    async updateOrgsForAutofollowRegion(data) {
        const transaction = await this.sequelize.transaction();
        try {
            const allOrgsForRegion = await this.findAll({
                autoFollowRegion: data.region
            }, { transaction });
            for (let orgId of data.organisationIds) {
                const exists = allOrgsForRegion.find(el => el.organisationId === orgId);
                if (!exists) {
                    await this.create({
                        autoFollowRegion: data.region,
                        organisationId: orgId,
                    }, { transaction });
                }
            }
            const itemsToDelete = allOrgsForRegion.filter(el => !data.organisationIds.find(v => v === el.organisationId));
            for (let item of itemsToDelete) {
                await this.remove({
                    where: {
                        autoFollowRegion: data.region,
                        organisationId: item.organisationId
                    },
                    transaction
                });
            }
            const result = await this.getOrganisationsForAutofollowRegion(data.region, transaction);
            await transaction.commit();
            return result;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`AutofollowService.updateAutofollowRegionOrgs`, e.message);
        }
    }
    async getOrganisationsForAutofollowRegion(region, transaction) {
        const allOrgsForRegion = await this.findAll({
            autoFollowRegion: region
        }, { transaction });
        const orgIds = allOrgsForRegion.map(el => el.organisationId);
        let orgs = [];
        if (orgIds.length) {
            const orgsModels = await this.orgService.findAll({
                id: orgIds
            }, { transaction });
            orgs = orgsModels.map(el => el.get({ plain: true }));
        }
        return ({
            title: region,
            organisations: orgs
        });
    }
    async getOrganisationsForAutofollowRegions(regions) {
        const transaction = await this.sequelize.transaction();
        const regionsList = regions ? regions : autofollow_args_1.RegionsList;
        try {
            const res = [];
            for (let region of regionsList) {
                const allOrgsForRegion = await this.findAll({
                    autoFollowRegion: region
                }, { transaction });
                const orgIds = allOrgsForRegion.map(el => el.organisationId);
                let orgs = [];
                if (orgIds.length) {
                    const orgsModels = await this.orgService.findAll({
                        id: orgIds
                    }, { transaction });
                    orgs = orgsModels.map(el => el.get({ plain: true }));
                }
                res.push({
                    title: region,
                    organisations: orgs
                });
            }
            await transaction.commit();
            return res;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`AutofollowService.getOrganisationsForAutofollowRegions`, e.message);
        }
    }
};
exports.AutofollowService = AutofollowService;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], AutofollowService.prototype, "orgService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => followers_service_1.FollowersService)),
    __metadata("design:type", followers_service_1.FollowersService)
], AutofollowService.prototype, "followersService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], AutofollowService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", sequelize_typescript_1.Sequelize)
], AutofollowService.prototype, "sequelize", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], AutofollowService.prototype, "errorHelper", void 0);
exports.AutofollowService = AutofollowService = __decorate([
    (0, common_1.Injectable)()
], AutofollowService);
//# sourceMappingURL=autofollow.service.js.map