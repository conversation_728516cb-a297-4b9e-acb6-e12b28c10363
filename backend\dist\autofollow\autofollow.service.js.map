{"version": 3, "file": "autofollow.service.js", "sourceRoot": "", "sources": ["../../src/autofollow/autofollow.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAKwB;AAExB,+CAAuD;AACvD,+DAAiD;AACjD,mDAAsD;AACtD,qCAAiC;AACjC,yDAAqD;AACrD,4DAA4E;AAC5E,gEAA2E;AAE3E,kFAA8E;AAC9E,sEAAkE;AAK3D,IAAM,iBAAiB,GAAvB,MAAM,iBAAkB,SAAQ,IAAA,0BAAW,EAAC,iDAA8B,CAAC;IAYhF,KAAK,CAAC,gBAAgB,CAAC,SAAiB,EAAE,OAAiB;QACzD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAA;QACtD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG;gBACX,GAAG,EAAE,EAAE;gBACP,SAAS,EAAE,SAAS;gBACpB,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,EAAE;gBACT,IAAI,EAAE,EAAE;gBACR,OAAO,EAAE,EAAE;gBACX,WAAW,EAAE,GAAG,EAAE,CAAC,KAAK;aACzB,CAAC;YACF,KAAK,IAAI,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC3B,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;oBAC1C,gBAAgB,EAAE,MAAM;iBACzB,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;gBACpB,KAAK,IAAI,GAAG,IAAI,gBAAgB,EAAE,CAAC;oBACjC,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAChC,IAAI,EACJ,GAAG,CAAC,cAAc,CACnB,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,MAAM,WAAW,CAAC,MAAM,EAAE,CAAC;YAE3B,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAM,CAAC,EAAE,CAAC;YACV,MAAM,WAAW,CAAC,QAAQ,EAAE,CAAC;YAC7B,IAAI,CAAC,WAAW,CAAC,kBAAkB,CACjC,qDAAqD,EACrD,CAAC,CAAC,OAAO,CACV,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,6BAA6B,CAAC,IAA4B;QAC9D,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAA;QACtD,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;gBAC1C,gBAAgB,EAAE,IAAI,CAAC,MAAM;aAC9B,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;YACpB,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvC,MAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,KAAK,KAAK,CAAC,CAAC;gBACxE,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,MAAM,IAAI,CAAC,MAAM,CAAC;wBAChB,gBAAgB,EAAE,IAAI,CAAC,MAAM;wBAC7B,cAAc,EAAE,KAAK;qBACtB,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;gBACtB,CAAC;YACH,CAAC;YACD,MAAM,aAAa,GAAG,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC;YAC9G,KAAK,IAAI,IAAI,IAAI,aAAa,EAAE,CAAC;gBAC/B,MAAM,IAAI,CAAC,MAAM,CAAC;oBAChB,KAAK,EAAE;wBACL,gBAAgB,EAAE,IAAI,CAAC,MAAM;wBAC7B,cAAc,EAAE,IAAI,CAAC,cAAc;qBACpC;oBACD,WAAW;iBACZ,CAAC,CAAC;YACL,CAAC;YAGD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YAExF,MAAM,WAAW,CAAC,MAAM,EAAE,CAAC;YAE3B,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAM,CAAC,EAAE,CAAC;YACV,MAAM,WAAW,CAAC,QAAQ,EAAE,CAAC;YAC7B,IAAI,CAAC,WAAW,CAAC,kBAAkB,CACjC,8CAA8C,EAC9C,CAAC,CAAC,OAAO,CACV,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mCAAmC,CAAC,MAAc,EAAE,WAAgB;QACtE,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;YAC1C,gBAAgB,EAAE,MAAM;SACzB,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;QACpB,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC;QAC7D,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;gBAC9C,EAAE,EAAE,MAAM;aACZ,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;YACpB,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAiB,CAAC,CAAC;QACvE,CAAC;QAED,OAAO,CAAC;YACN,KAAK,EAAE,MAAM;YACb,aAAa,EAAE,IAAI;SACpB,CAAC,CAAA;IACN,CAAC;IAED,KAAK,CAAC,oCAAoC,CAAC,OAAkB;QAC3D,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;QACvD,MAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,6BAAW,CAAC;QACpD,IAAI,CAAC;YACH,MAAM,GAAG,GAA4B,EAAE,CAAC;YACxC,KAAK,IAAI,MAAM,IAAI,WAAW,EAAE,CAAC;gBAC/B,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;oBAC1C,gBAAgB,EAAE,MAAM;iBACzB,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;gBACpB,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC;gBAC7D,IAAI,IAAI,GAAG,EAAE,CAAC;gBACd,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;oBAClB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;wBAC9C,EAAE,EAAE,MAAM;qBACZ,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;oBACpB,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAiB,CAAC,CAAC;gBACvE,CAAC;gBAED,GAAG,CAAC,IAAI,CAAC;oBACP,KAAK,EAAE,MAAM;oBACb,aAAa,EAAE,IAAI;iBACpB,CAAC,CAAC;YACL,CAAC;YAED,MAAM,WAAW,CAAC,MAAM,EAAE,CAAC;YAC3B,OAAO,GAAG,CAAC;QACb,CAAC;QAAC,OAAM,CAAC,EAAE,CAAC;YACV,MAAM,WAAW,CAAC,QAAQ,EAAE,CAAC;YAC7B,IAAI,CAAC,WAAW,CAAC,kBAAkB,CACjC,wDAAwD,EACxD,CAAC,CAAC,OAAO,CACV,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AA9IY,8CAAiB;AAEX;IADhB,IAAA,eAAM,EAAC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,4CAAoB,CAAC,CAAC;8BAClB,4CAAoB;qDAAC;AAEjC;IADhB,IAAA,eAAM,EAAC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,oCAAgB,CAAC,CAAC;8BACR,oCAAgB;2DAAC;AAEnC;IADhB,IAAA,eAAM,EAAC,sCAAuB,CAAC;8BACP,gBAAM;iDAAC;AAEf;IADhB,IAAA,eAAM,GAAE;8BACmB,gCAAS;oDAAC;AAErB;IADhB,IAAA,eAAM,GAAE;8BACqB,mBAAW;sDAAC;4BAV/B,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;GACA,iBAAiB,CA8I7B"}