"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutoFollowRegionsOrganisations = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const graphql_1 = require("@nestjs/graphql");
let AutoFollowRegionsOrganisations = class AutoFollowRegionsOrganisations extends sequelize_typescript_1.Model {
};
exports.AutoFollowRegionsOrganisations = AutoFollowRegionsOrganisations;
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        allowNull: false,
    }),
    __metadata("design:type", String)
], AutoFollowRegionsOrganisations.prototype, "autoFollowRegion", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        allowNull: false,
    }),
    __metadata("design:type", String)
], AutoFollowRegionsOrganisations.prototype, "organisationId", void 0);
__decorate([
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], AutoFollowRegionsOrganisations.prototype, "createdAt", void 0);
__decorate([
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], AutoFollowRegionsOrganisations.prototype, "updatedAt", void 0);
exports.AutoFollowRegionsOrganisations = AutoFollowRegionsOrganisations = __decorate([
    (0, graphql_1.ObjectType)(),
    sequelize_typescript_1.Table
], AutoFollowRegionsOrganisations);
//# sourceMappingURL=autofollow.model.js.map