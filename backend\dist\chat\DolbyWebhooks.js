"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DolbyEvents = void 0;
var DolbyEvents;
(function (DolbyEvents) {
    DolbyEvents["ConferenceCreated"] = "Conference.Created";
    DolbyEvents["ConferenceEnded"] = "Conference.Ended";
    DolbyEvents["ParticipantJoined"] = "Participant.Joined";
    DolbyEvents["ParticipantLeft"] = "Participant.Left";
    DolbyEvents["RecordingLiveInProgress"] = "Recording.Live.InProgress";
    DolbyEvents["RecordingAudioAvailable"] = "Recording.Audio.Available";
    DolbyEvents["RecordingMP4Available"] = "Recording.MP4.Available";
    DolbyEvents["StreamHlsInProgress"] = "Stream.Hls.InProgress";
    DolbyEvents["StreamRtmpInProgress"] = "Stream.Rtmp.InProgress";
    DolbyEvents["MixerServiceError"] = "Mixer.Service.Error";
})(DolbyEvents || (exports.DolbyEvents = DolbyEvents = {}));
//# sourceMappingURL=DolbyWebhooks.js.map