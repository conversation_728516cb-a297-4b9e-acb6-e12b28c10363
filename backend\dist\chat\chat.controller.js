"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatController = void 0;
const common_1 = require("@nestjs/common");
const raw_body_1 = __importDefault(require("raw-body"));
const chat_service_1 = require("./chat.service");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const DolbyWebhooks_1 = require("./DolbyWebhooks");
const gcp_auth_guard_1 = require("../common/guards/gcp-auth.guard");
const config_1 = __importDefault(require("../config/config"));
const error_1 = require("../common/helpers/error");
let ChatController = class ChatController {
    async leaveCall(callChannelId, req) {
        this.logger.verbose('ChatController.leaveCall (REST)');
        console.log('chat leave');
        if (req.readable) {
            const raw = await (0, raw_body_1.default)(req);
            const text = raw.toString().trim();
            const leaveCallDto = JSON.parse(text);
            return this.chatService.leaveCall(leaveCallDto.profileId, callChannelId);
        }
        return false;
    }
    async audioCallHook(event) {
        this.logger.info(`ChatController.audioCallHook (${event.eventType})`);
        if (event.eventType === DolbyWebhooks_1.DolbyEvents.ConferenceEnded) {
            await this.chatService.handleConferenceEnded(event);
        }
        return true;
    }
    async handleZoomMeetingEnded(event) {
        const res = await this.chatService.handleZoomMeetingEnded(event.payload);
        return res;
    }
    async sendBatchMessageScript(startAt, finishAt) {
        this.logger.info(`ChatController.sendBatchMessageScript --> startAt: ${startAt}, finishAt: ${finishAt}`);
        return await this.chatService.sendBatchMessageScript(startAt, finishAt);
    }
    async sendBatchMessageScriptBeta(startAt, finishAt) {
        this.logger.info(`ChatController.sendBatchMessageScript --> startAt: ${startAt}, finishAt: ${finishAt}`);
        try {
            if (config_1.default.NODE_ENV === 'development' || config_1.default.SERVICE === 'api-beta') {
                return await this.chatService.sendBatchMessageScript(startAt, finishAt);
            }
            else {
                throw new Error('only beta env allowed');
            }
        }
        catch (err) {
            this.errorHelper.throwHttpException(`ChatController.sendBatchMessageScriptBeta`, err.message);
        }
    }
};
exports.ChatController = ChatController;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => chat_service_1.ChatService)),
    __metadata("design:type", chat_service_1.ChatService)
], ChatController.prototype, "chatService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], ChatController.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], ChatController.prototype, "errorHelper", void 0);
__decorate([
    (0, common_1.Post)('/call/:channelId/leave'),
    __param(0, (0, common_1.Param)('channelId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "leaveCall", null);
__decorate([
    (0, common_1.Post)('/hook'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "audioCallHook", null);
__decorate([
    (0, common_1.Post)('/zoom-meeting-ended'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "handleZoomMeetingEnded", null);
__decorate([
    (0, common_1.UseGuards)(gcp_auth_guard_1.GcpAuthGuard),
    (0, common_1.Get)('/sendBatchMessageScript/:startAt/:finishAt'),
    __param(0, (0, common_1.Param)('startAt')),
    __param(1, (0, common_1.Param)('finishAt')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "sendBatchMessageScript", null);
__decorate([
    (0, common_1.Get)('/sendBatchMessageScript-beta/:startAt/:finishAt'),
    __param(0, (0, common_1.Param)('startAt')),
    __param(1, (0, common_1.Param)('finishAt')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "sendBatchMessageScriptBeta", null);
exports.ChatController = ChatController = __decorate([
    (0, common_1.Controller)('chat')
], ChatController);
//# sourceMappingURL=chat.controller.js.map