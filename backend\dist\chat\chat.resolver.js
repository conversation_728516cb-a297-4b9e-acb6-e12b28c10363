"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatResolver = exports.ZoomMeetingEndedResponse = exports.ZoomMeetingResponse = exports.ZoomMeetingExpireMessageResponse = exports.UpdateAsModeratorsResponse = exports.CreateGroupChannelResponse = exports.StartCallResponse = exports.ChatAuthTokenResponse = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const chat_service_1 = require("./chat.service");
const graphql_subscriptions_1 = require("graphql-subscriptions");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
let ChatAuthTokenResponse = class ChatAuthTokenResponse {
};
exports.ChatAuthTokenResponse = ChatAuthTokenResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], ChatAuthTokenResponse.prototype, "authToken", void 0);
exports.ChatAuthTokenResponse = ChatAuthTokenResponse = __decorate([
    (0, graphql_1.ObjectType)()
], ChatAuthTokenResponse);
let StartCallResponse = class StartCallResponse {
};
exports.StartCallResponse = StartCallResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], StartCallResponse.prototype, "channelId", void 0);
exports.StartCallResponse = StartCallResponse = __decorate([
    (0, graphql_1.ObjectType)()
], StartCallResponse);
let CreateGroupChannelResponse = class CreateGroupChannelResponse {
};
exports.CreateGroupChannelResponse = CreateGroupChannelResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CreateGroupChannelResponse.prototype, "channelId", void 0);
exports.CreateGroupChannelResponse = CreateGroupChannelResponse = __decorate([
    (0, graphql_1.ObjectType)()
], CreateGroupChannelResponse);
let UpdateAsModeratorsResponse = class UpdateAsModeratorsResponse {
};
exports.UpdateAsModeratorsResponse = UpdateAsModeratorsResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], UpdateAsModeratorsResponse.prototype, "channelId", void 0);
exports.UpdateAsModeratorsResponse = UpdateAsModeratorsResponse = __decorate([
    (0, graphql_1.ObjectType)()
], UpdateAsModeratorsResponse);
let ZoomMeetingExpireMessageResponse = class ZoomMeetingExpireMessageResponse {
};
exports.ZoomMeetingExpireMessageResponse = ZoomMeetingExpireMessageResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], ZoomMeetingExpireMessageResponse.prototype, "success", void 0);
exports.ZoomMeetingExpireMessageResponse = ZoomMeetingExpireMessageResponse = __decorate([
    (0, graphql_1.ObjectType)()
], ZoomMeetingExpireMessageResponse);
let ZoomMeetingResponse = class ZoomMeetingResponse {
};
exports.ZoomMeetingResponse = ZoomMeetingResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], ZoomMeetingResponse.prototype, "startUrl", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], ZoomMeetingResponse.prototype, "joinUrl", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], ZoomMeetingResponse.prototype, "meetingId", void 0);
exports.ZoomMeetingResponse = ZoomMeetingResponse = __decorate([
    (0, graphql_1.ObjectType)()
], ZoomMeetingResponse);
let ZoomMeetingEndedResponse = class ZoomMeetingEndedResponse {
};
exports.ZoomMeetingEndedResponse = ZoomMeetingEndedResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], ZoomMeetingEndedResponse.prototype, "zoomMeetingId", void 0);
exports.ZoomMeetingEndedResponse = ZoomMeetingEndedResponse = __decorate([
    (0, graphql_1.ObjectType)()
], ZoomMeetingEndedResponse);
let ChatResolver = class ChatResolver {
    async getChatAuthToken(user) {
        this.logger.verbose('ChatResolver.getChatAuthToken (query)', {
            user: user.toLogObject(),
        });
        const authToken = await this.chatService.getAuthToken(user.profileId);
        return {
            authToken,
        };
    }
    async startCall(user, participants, isVideoOn, chatChannelId) {
        this.logger.verbose('ChatResolver.startCall (nutation)', {
            user: user.toLogObject(),
        });
        const channelId = await this.chatService.startCall(user.profileId, participants, isVideoOn, chatChannelId);
        return {
            channelId,
        };
    }
    async updateAsModerators(user, channelId, userIds) {
        this.logger.verbose('ChatResolver.updateAsModerators (mutation)', {
            user: user.toLogObject(),
            channelId,
            userIds,
        });
        const updatedChannelId = await this.chatService.updateAsModerators(channelId, userIds);
        return {
            channelId: updatedChannelId,
        };
    }
    async createGroupChannel(user, profileId, connectionProfileIds) {
        this.logger.verbose('ChatResolver.createGroupChannel (nutation)', {
            user: user.toLogObject(),
        });
        const channelId = await this.chatService.createGroupChannel(profileId, connectionProfileIds);
        return {
            channelId,
        };
    }
    async zoomMeetingData() {
        const meetingData = await this.chatService.startZoomMeeting();
        return Object.assign({}, meetingData);
    }
    async expireZoomMeetingMessage(messageId, userId, userName, timeout) {
        const success = await this.chatService.setMessageExpireTime({
            messageId,
            userId,
            userName,
            timeout,
        });
        return { success };
    }
    async zoomMeetingEnded(meetingId) {
        return this.pubSub.asyncIterator('zoomMeetingEnded');
    }
    async sendInboxMessageNotification(user, profileIds, text, messageHeader, senderProfilePhoto) {
        return this.chatService.sendInboxMessageNotification(profileIds, text, messageHeader, user.profileId, senderProfilePhoto);
    }
};
exports.ChatResolver = ChatResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => chat_service_1.ChatService)),
    __metadata("design:type", chat_service_1.ChatService)
], ChatResolver.prototype, "chatService", void 0);
__decorate([
    (0, common_1.Inject)('PUB_SUB'),
    __metadata("design:type", graphql_subscriptions_1.PubSubEngine)
], ChatResolver.prototype, "pubSub", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], ChatResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Query)(() => ChatAuthTokenResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ChatResolver.prototype, "getChatAuthToken", null);
__decorate([
    (0, graphql_1.Mutation)(() => StartCallResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)({ name: 'participants', type: () => [String] })),
    __param(2, (0, graphql_1.Args)({ name: 'isVideoOn' })),
    __param(3, (0, graphql_1.Args)({ name: 'chatChannelId', nullable: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Array, Boolean, String]),
    __metadata("design:returntype", Promise)
], ChatResolver.prototype, "startCall", null);
__decorate([
    (0, graphql_1.Mutation)(() => UpdateAsModeratorsResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)({ name: 'channelId', type: () => String })),
    __param(2, (0, graphql_1.Args)({ name: 'userIds', type: () => [String] })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Array]),
    __metadata("design:returntype", Promise)
], ChatResolver.prototype, "updateAsModerators", null);
__decorate([
    (0, graphql_1.Mutation)(() => CreateGroupChannelResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)({ name: 'profileId' })),
    __param(2, (0, graphql_1.Args)({ name: 'connectionProfileIds', type: () => [String] })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Array]),
    __metadata("design:returntype", Promise)
], ChatResolver.prototype, "createGroupChannel", null);
__decorate([
    (0, graphql_1.Query)(() => ZoomMeetingResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ChatResolver.prototype, "zoomMeetingData", null);
__decorate([
    (0, graphql_1.Mutation)(() => ZoomMeetingExpireMessageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)({ name: 'messageId' })),
    __param(1, (0, graphql_1.Args)({ name: 'userId' })),
    __param(2, (0, graphql_1.Args)({ name: 'userName' })),
    __param(3, (0, graphql_1.Args)({ name: 'timeout' })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, Number]),
    __metadata("design:returntype", Promise)
], ChatResolver.prototype, "expireZoomMeetingMessage", null);
__decorate([
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    (0, graphql_1.Subscription)(() => ZoomMeetingEndedResponse, {
        filter: (payload, variables, context) => {
            var _a;
            if (((_a = payload === null || payload === void 0 ? void 0 : payload.zoomMeetingEnded) === null || _a === void 0 ? void 0 : _a.zoomMeetingId) === variables.meetingId)
                return true;
            return false;
        },
    }),
    __param(0, (0, graphql_1.Args)({ name: 'meetingId' })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ChatResolver.prototype, "zoomMeetingEnded", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)({ name: 'profileIds', type: () => [String] })),
    __param(2, (0, graphql_1.Args)('text')),
    __param(3, (0, graphql_1.Args)('messageHeader')),
    __param(4, (0, graphql_1.Args)('senderProfilePhoto')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Array, String, String, String]),
    __metadata("design:returntype", Promise)
], ChatResolver.prototype, "sendInboxMessageNotification", null);
exports.ChatResolver = ChatResolver = __decorate([
    (0, graphql_1.Resolver)()
], ChatResolver);
//# sourceMappingURL=chat.resolver.js.map