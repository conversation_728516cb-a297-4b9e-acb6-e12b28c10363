"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CloudinaryResolver = exports.CloudinarySignatureResponse = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const cloudinary_signature_args_1 = require("./args/cloudinary.signature.args");
const cloudinary_service_1 = require("./cloudinary.service");
let CloudinarySignatureResponse = class CloudinarySignatureResponse {
};
exports.CloudinarySignatureResponse = CloudinarySignatureResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CloudinarySignatureResponse.prototype, "signature", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], CloudinarySignatureResponse.prototype, "timestamp", void 0);
exports.CloudinarySignatureResponse = CloudinarySignatureResponse = __decorate([
    (0, graphql_1.ObjectType)()
], CloudinarySignatureResponse);
let CloudinaryResolver = class CloudinaryResolver {
    async signature(signatureArgs) {
        this.logger.verbose('CloudinaryResolver.signature (query)', {
            signatureArgs,
        });
        return await this.cloudinaryService.signature(signatureArgs);
    }
};
exports.CloudinaryResolver = CloudinaryResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => cloudinary_service_1.CloudinaryService)),
    __metadata("design:type", cloudinary_service_1.CloudinaryService)
], CloudinaryResolver.prototype, "cloudinaryService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], CloudinaryResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Query)(() => CloudinarySignatureResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [cloudinary_signature_args_1.CloudinarySignatureArgs]),
    __metadata("design:returntype", Promise)
], CloudinaryResolver.prototype, "signature", null);
exports.CloudinaryResolver = CloudinaryResolver = __decorate([
    (0, graphql_1.Resolver)()
], CloudinaryResolver);
//# sourceMappingURL=cloudinary.resolver.js.map