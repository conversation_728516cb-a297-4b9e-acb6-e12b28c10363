"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginationArgs = exports.PaginationSortOrder = void 0;
const graphql_1 = require("@nestjs/graphql");
var PaginationSortOrder;
(function (PaginationSortOrder) {
    PaginationSortOrder["Ascending"] = "ASC";
    PaginationSortOrder["Descending"] = "DESC";
})(PaginationSortOrder || (exports.PaginationSortOrder = PaginationSortOrder = {}));
(0, graphql_1.registerEnumType)(PaginationSortOrder, { name: 'PaginationSortOrder' });
let PaginationArgs = class PaginationArgs {
};
exports.PaginationArgs = PaginationArgs;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int, { nullable: true }),
    __metadata("design:type", Number)
], PaginationArgs.prototype, "first", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    __metadata("design:type", String)
], PaginationArgs.prototype, "after", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int, { nullable: true }),
    __metadata("design:type", Number)
], PaginationArgs.prototype, "offset", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    __metadata("design:type", String)
], PaginationArgs.prototype, "sortBy", void 0);
__decorate([
    (0, graphql_1.Field)(() => PaginationSortOrder, { nullable: true }),
    __metadata("design:type", String)
], PaginationArgs.prototype, "sortOrder", void 0);
exports.PaginationArgs = PaginationArgs = __decorate([
    (0, graphql_1.ArgsType)()
], PaginationArgs);
//# sourceMappingURL=pagination.args.js.map