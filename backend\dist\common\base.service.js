"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseService = BaseService;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const sequelize_2 = require("sequelize");
const sequelize_typescript_1 = require("sequelize-typescript");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const nestjs_redis_1 = require("nestjs-redis");
const object_hash_1 = __importDefault(require("object-hash"));
const underscore_1 = require("./helpers/underscore");
const config_1 = __importDefault(require("../config/config"));
function BaseService(entity) {
    let ServiceHost = class ServiceHost {
        constructor() {
            this._cache = {};
            this.useRedisCache = ['production', 'development'].includes(config_1.default.NODE_ENV) &&
                config_1.default.SERVICE !== 'api-beta';
        }
        async onModuleInit() {
            if (this.useRedisCache) {
                this._redisClient = await this.redisService.getClient();
                await this.cleanRedisCache();
            }
        }
        async cleanRedisCache() {
            const keys = (await this._redisClient.keys(`${entity.name}_*`)) || [];
            for (const key of keys) {
                this.logger.debug(`${entity.name}.BaseService (delete key from redis: "${key}")`);
                await this._redisClient.del(key);
            }
        }
        cacheGet(key) {
            const keyHash = `${(0, object_hash_1.default)(key)}-${entity.name}`;
            const cachedPromise = this._cache[keyHash];
            if (cachedPromise) {
                if (cachedPromise.time > Date.now() - 500)
                    return cachedPromise.promise;
                else
                    this.cacheDelete(key);
            }
            return null;
        }
        cachePut(key, promise) {
            const keyHash = `${(0, object_hash_1.default)(key)}-${entity.name}`;
            this._cache[keyHash] = {
                promise,
                time: Date.now(),
            };
        }
        cacheDelete(key) {
            const keyHash = `${(0, object_hash_1.default)(key)}-${entity.name}`;
            delete this._cache[keyHash];
        }
        getModel() {
            return this.sequelize.model(entity.name);
        }
        async findById(id, options) {
            const useRedisCache = underscore_1.Underscore.isEmpty(options === null || options === void 0 ? void 0 : options.includeParams) &&
                !(options === null || options === void 0 ? void 0 : options.transaction) &&
                !(options === null || options === void 0 ? void 0 : options.avoidRedisCache) &&
                this.useRedisCache;
            const usePromiseCache = !underscore_1.Underscore.isEmpty(options === null || options === void 0 ? void 0 : options.includeParams) &&
                (options === null || options === void 0 ? void 0 : options.useCache) &&
                !useRedisCache;
            if (useRedisCache) {
                const redisCachedObject = await this._redisClient.get(`${entity.name}_${id}`);
                if (!underscore_1.Underscore.isEmpty(redisCachedObject)) {
                    this.logger.verbose(`${entity.name}.findById (redis cached)`, {
                        id,
                    });
                    return JSON.parse(redisCachedObject);
                }
                this.logger.verbose(`${entity.name}.findById`, {
                    id,
                    includeParams: options === null || options === void 0 ? void 0 : options.includeParams,
                    useCache: options === null || options === void 0 ? void 0 : options.useCache,
                });
                const record = await this.getModel().findByPk(id, {
                    include: options === null || options === void 0 ? void 0 : options.includeParams,
                    transaction: options === null || options === void 0 ? void 0 : options.transaction,
                });
                if (record && useRedisCache) {
                    await this._redisClient.set(`${entity.name}_${id}`, JSON.stringify(record), 'EX', 60 * 60);
                }
                return record;
            }
            if (!usePromiseCache) {
                const cacheKey = {
                    operation: 'findById',
                    id,
                    includeParams: JSON.stringify(options === null || options === void 0 ? void 0 : options.includeParams),
                };
                const cachedPromise = this.cacheGet(cacheKey);
                if (cachedPromise && (options === null || options === void 0 ? void 0 : options.useCache)) {
                    this.logger.verbose(`${entity.name}.findById (cached)`, {
                        id,
                        options,
                    });
                    return cachedPromise;
                }
                this.logger.verbose(`${entity.name}.findById`, {
                    id,
                    includeParams: options === null || options === void 0 ? void 0 : options.includeParams,
                    useCache: options === null || options === void 0 ? void 0 : options.useCache,
                });
                const recordPromise = this.getModel().findByPk(id, {
                    include: options === null || options === void 0 ? void 0 : options.includeParams,
                    transaction: options === null || options === void 0 ? void 0 : options.transaction,
                });
                this.cachePut(cacheKey, recordPromise);
                return recordPromise;
            }
        }
        async findOne(queryParams, options) {
            const cacheKey = {
                operation: 'findOne',
                queryParams,
                includeParams: JSON.stringify(options === null || options === void 0 ? void 0 : options.includeParams),
                attributes: JSON.stringify(options === null || options === void 0 ? void 0 : options.attributes),
            };
            const cachedPromise = this.cacheGet(cacheKey);
            if (cachedPromise && (options === null || options === void 0 ? void 0 : options.useCache)) {
                this.logger.verbose(`${entity.name}.findOne (cached)`, {
                    queryParams,
                    options,
                });
                return cachedPromise;
            }
            this.logger.verbose(`${entity.name}.findOne`, {
                queryParams,
                order: options === null || options === void 0 ? void 0 : options.order,
                includeParams: options === null || options === void 0 ? void 0 : options.includeParams,
                attributes: options === null || options === void 0 ? void 0 : options.attributes,
                useCache: options === null || options === void 0 ? void 0 : options.useCache,
            });
            const recordPromise = this.getModel().findOne({
                where: queryParams,
                order: options === null || options === void 0 ? void 0 : options.order,
                include: options === null || options === void 0 ? void 0 : options.includeParams,
                attributes: options === null || options === void 0 ? void 0 : options.attributes,
                transaction: options === null || options === void 0 ? void 0 : options.transaction,
            });
            this.cachePut(cacheKey, recordPromise);
            return recordPromise;
        }
        async findAll(queryParams, options) {
            queryParams = queryParams || {};
            const cacheKey = {
                operation: 'findAll',
                queryParams,
                order: JSON.stringify(options === null || options === void 0 ? void 0 : options.order),
                includeParams: JSON.stringify(options === null || options === void 0 ? void 0 : options.includeParams),
                attributes: JSON.stringify(options === null || options === void 0 ? void 0 : options.attributes),
            };
            const cachedPromises = this.cacheGet(cacheKey);
            if (cachedPromises && (options === null || options === void 0 ? void 0 : options.useCache)) {
                this.logger.verbose(`${entity.name}.findAll (cached)`, {
                    queryParams,
                    options,
                });
                return cachedPromises;
            }
            this.logger.verbose(`${entity.name}.findAll`, {
                queryParams,
                includeParams: options === null || options === void 0 ? void 0 : options.includeParams,
                order: options === null || options === void 0 ? void 0 : options.order,
                attributes: options === null || options === void 0 ? void 0 : options.attributes,
                useCache: options === null || options === void 0 ? void 0 : options.useCache,
            });
            const recordsPromise = this.getModel().findAll({
                where: queryParams,
                include: options === null || options === void 0 ? void 0 : options.includeParams,
                order: options === null || options === void 0 ? void 0 : options.order,
                attributes: options === null || options === void 0 ? void 0 : options.attributes,
                transaction: options === null || options === void 0 ? void 0 : options.transaction,
            });
            this.cachePut(cacheKey, recordsPromise);
            return recordsPromise;
        }
        async count(queryParams, options) {
            queryParams = queryParams || {};
            const cacheKey = {
                operation: 'count',
                queryParams,
            };
            const cachedPromises = this.cacheGet(cacheKey);
            if (cachedPromises && (options === null || options === void 0 ? void 0 : options.useCache)) {
                this.logger.verbose(`${entity.name}.count (cached)`, {
                    queryParams,
                    options,
                });
                return cachedPromises;
            }
            this.logger.verbose(`${entity.name}.count`, {
                queryParams,
                useCache: options === null || options === void 0 ? void 0 : options.useCache,
            });
            const recordsPromise = this.getModel().count({
                where: queryParams,
                transaction: options === null || options === void 0 ? void 0 : options.transaction,
            });
            this.cachePut(cacheKey, recordsPromise);
            return recordsPromise;
        }
        async create(dto, options) {
            var _a;
            this.logger.info(`${entity.name}.create`, {
                dto,
            });
            try {
                const recordPromise = this.getModel().create(dto, options);
                return recordPromise;
            }
            catch (e) {
                this.logger.error(`BaseService.create Error: ${e.message}`, (_a = e.response) === null || _a === void 0 ? void 0 : _a.body);
                throw e;
            }
        }
        async update(options) {
            this.logger.info(`${entity.name}.update`, {
                where: options.where,
            });
            const records = await this.getModel().findAll({
                where: options === null || options === void 0 ? void 0 : options.where,
                transaction: options === null || options === void 0 ? void 0 : options.transaction,
            });
            if (this.useRedisCache) {
                for (const record of records) {
                    await this._redisClient.del(`${entity.name}_${record.id}`);
                }
            }
            return this.getModel().update(Object.assign({}, options.update), {
                where: options.where,
                transaction: options === null || options === void 0 ? void 0 : options.transaction,
            });
        }
        async updateById(id, dto, options) {
            var _a, _b;
            this.logger.info(`${entity.name}.updateById`, {
                id,
                dto,
            });
            try {
                await this.getModel().update(Object.assign({}, dto), {
                    where: {
                        id,
                    },
                    transaction: options === null || options === void 0 ? void 0 : options.transaction,
                });
            }
            catch (e) {
                this.logger.error(`BaseService.updateById Error: ${e.message}`, (_a = e.response) === null || _a === void 0 ? void 0 : _a.body);
                throw e;
            }
            if (this.useRedisCache) {
                try {
                    await this._redisClient.del(`${entity.name}_${id}`);
                }
                catch (e) {
                    this.logger.error(`BaseService.updateById Error: ${e.message}`, (_b = e.response) === null || _b === void 0 ? void 0 : _b.body);
                }
            }
            return this.findById(id, {
                avoidRedisCache: true,
                transaction: options === null || options === void 0 ? void 0 : options.transaction,
                includeParams: options === null || options === void 0 ? void 0 : options.includeParams,
            });
        }
        async remove(options) {
            this.logger.info(`${entity.name}.remove`, {
                where: options.where,
            });
            const records = await this.getModel().findAll({
                where: options === null || options === void 0 ? void 0 : options.where,
                transaction: options === null || options === void 0 ? void 0 : options.transaction,
            });
            if (this.useRedisCache && this._redisClient) {
                for (const record of records) {
                    await this._redisClient.del(`${entity.name}_${record.id}`);
                }
            }
            return this.getModel().destroy({
                where: options === null || options === void 0 ? void 0 : options.where,
                transaction: options === null || options === void 0 ? void 0 : options.transaction,
            });
        }
        async removeById(id, options) {
            this.logger.info(`${entity.name}.removeById`, {
                id,
            });
            if (this.useRedisCache) {
                await this._redisClient.del(`${entity.name}_${id}`);
            }
            return this.getModel().destroy({
                where: {
                    id,
                },
                transaction: options === null || options === void 0 ? void 0 : options.transaction,
            });
        }
    };
    __decorate([
        (0, sequelize_1.InjectModel)(entity),
        __metadata("design:type", sequelize_2.Model)
    ], ServiceHost.prototype, "model", void 0);
    __decorate([
        (0, common_1.Inject)(),
        __metadata("design:type", sequelize_typescript_1.Sequelize)
    ], ServiceHost.prototype, "sequelize", void 0);
    __decorate([
        (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
        __metadata("design:type", winston_1.Logger)
    ], ServiceHost.prototype, "logger", void 0);
    __decorate([
        (0, common_1.Inject)(),
        __metadata("design:type", nestjs_redis_1.RedisService)
    ], ServiceHost.prototype, "redisService", void 0);
    ServiceHost = __decorate([
        (0, common_1.Injectable)()
    ], ServiceHost);
    return ServiceHost;
}
//# sourceMappingURL=base.service.js.map