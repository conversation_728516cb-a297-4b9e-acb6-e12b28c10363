"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CurrentUser = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
exports.CurrentUser = (0, common_1.createParamDecorator)((data, context) => {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
    const ctx = graphql_1.GqlExecutionContext.create(context);
    const user = ((_a = ctx.getContext().req) === null || _a === void 0 ? void 0 : _a.user) || ((_c = (_b = ctx.getContext().connection) === null || _b === void 0 ? void 0 : _b.context) === null || _c === void 0 ? void 0 : _c.user);
    const splitSubId = user.sub.split('|');
    const getUserId = splitSubId[0].toLowerCase() === 'apple'
        ? splitSubId[1].split('.')[1]
        : splitSubId[1];
    user.profileId = getUserId;
    user.idToken =
        ((_f = (_e = (_d = ctx.getContext().req) === null || _d === void 0 ? void 0 : _d.headers) === null || _e === void 0 ? void 0 : _e.authorization) === null || _f === void 0 ? void 0 : _f.split(' ')[1]) ||
            ((_k = (_j = (_h = (_g = ctx
                .getContext()
                .connection) === null || _g === void 0 ? void 0 : _g.context) === null || _h === void 0 ? void 0 : _h.headers) === null || _j === void 0 ? void 0 : _j.authorization) === null || _k === void 0 ? void 0 : _k.split(' ')[1]);
    user.toLogObject = () => ({
        profileId: user.profileId,
    });
    return user;
});
//# sourceMappingURL=current-user.decorator.js.map