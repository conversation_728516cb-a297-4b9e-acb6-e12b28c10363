"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GcpAuthGuard = void 0;
const common_1 = require("@nestjs/common");
const winston_1 = require("winston");
const nest_winston_1 = require("nest-winston");
const config_1 = __importDefault(require("../../config/config"));
let GcpAuthGuard = class GcpAuthGuard {
    canActivate(context) {
        const request = context.switchToHttp().getRequest();
        this.logger.info('GcpAuthGured.canActive', request.headers);
        return config_1.default.SERVICE === 'api' || config_1.default.NODE_ENV === 'development'
            ? request.headers['x-cloudscheduler'] === 'true'
            : false;
    }
};
exports.GcpAuthGuard = GcpAuthGuard;
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], GcpAuthGuard.prototype, "logger", void 0);
exports.GcpAuthGuard = GcpAuthGuard = __decorate([
    (0, common_1.Injectable)()
], GcpAuthGuard);
//# sourceMappingURL=gcp-auth.guard.js.map