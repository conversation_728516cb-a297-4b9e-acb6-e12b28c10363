"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseMonitoring = void 0;
class DatabaseMonitoring {
    constructor(sequelize) {
        this.sequelize = sequelize;
    }
    async getDatabaseStats() {
        const stats = await this.sequelize.query(`
      SELECT 
        d.datname as database_name,
        pg_size_pretty(pg_database_size(d.datname)) as database_size,
        numbackends as active_connections,
        xact_commit as transactions_committed,
        xact_rollback as transactions_rollback,
        blks_read as blocks_read,
        blks_hit as blocks_hit,
        tup_returned as rows_returned,
        tup_fetched as rows_fetched,
        tup_inserted as rows_inserted,
        tup_updated as rows_updated,
        tup_deleted as rows_deleted
      FROM pg_database d
      LEFT JOIN pg_stat_database s ON d.oid = s.datid
      WHERE d.datname IN ('hablo', 'hablo-beta', 'hablo-e2e')
      ORDER BY d.datname;
    `, { type: 'SELECT' });
        return stats;
    }
    async getConnectionPoolStats() {
        const stats = await this.sequelize.query(`
      SELECT 
        datname as database_name,
        count(*) as total_connections,
        count(*) FILTER (WHERE state = 'active') as active_connections,
        count(*) FILTER (WHERE state = 'idle') as idle_connections,
        count(*) FILTER (WHERE state = 'idle in transaction') as idle_in_transaction
      FROM pg_stat_activity
      WHERE datname IN ('hablo', 'hablo-beta', 'hablo-e2e')
      GROUP BY datname
      ORDER BY datname;
    `, { type: 'SELECT' });
        return stats;
    }
    async getBufferHitRatio() {
        const stats = await this.sequelize.query(`
      SELECT 
        datname as database_name,
        blks_hit::float / (blks_hit + blks_read) as buffer_hit_ratio
      FROM pg_stat_database
      WHERE datname IN ('hablo', 'hablo-beta', 'hablo-e2e')
      ORDER BY datname;
    `, { type: 'SELECT' });
        return stats;
    }
    async getTableStats() {
        const stats = await this.sequelize.query(`
      SELECT 
        current_database() as database_name,
        schemaname,
        relname as table_name,
        pg_size_pretty(pg_total_relation_size(relid)) as total_size,
        pg_size_pretty(pg_relation_size(relid)) as table_size,
        pg_size_pretty(pg_total_relation_size(relid) - pg_relation_size(relid)) as index_size,
        n_live_tup as row_count,
        n_dead_tup as dead_rows,
        last_vacuum,
        last_autovacuum,
        last_analyze,
        last_autoanalyze
      FROM pg_stat_user_tables
      ORDER BY pg_total_relation_size(relid) DESC
      LIMIT 20;
    `, { type: 'SELECT' });
        return stats;
    }
}
exports.DatabaseMonitoring = DatabaseMonitoring;
//# sourceMappingURL=db-monitoring.js.map