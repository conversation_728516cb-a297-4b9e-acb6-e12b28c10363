"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorHelper = void 0;
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const common_1 = require("@nestjs/common");
let ErrorHelper = class ErrorHelper {
    throwHttpException(source, message, statusCode) {
        const error = {
            status: statusCode || common_1.HttpStatus.BAD_REQUEST,
            error: message,
        };
        this.logger.error(`${source} - ${message} (${error.status})`, error);
        throw new common_1.HttpException(error, common_1.HttpStatus.BAD_REQUEST);
    }
};
exports.ErrorHelper = ErrorHelper;
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], ErrorHelper.prototype, "logger", void 0);
exports.ErrorHelper = ErrorHelper = __decorate([
    (0, common_1.Injectable)()
], ErrorHelper);
//# sourceMappingURL=error.js.map