"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginationHelper = void 0;
const pagination_args_1 = require("../args/pagination.args");
const sequelize_1 = require("sequelize");
class PaginatedResults {
}
class PaginationHelper {
    async getPaginatedResults(options) {
        const { model, pagination, extraQueryParams, includeParams, includeIds, excludeIds, } = options;
        if ((pagination === null || pagination === void 0 ? void 0 : pagination.sortBy) === 'id') {
            throw new Error(`It's not allowed to sort by ID`);
        }
        const orderParams = [];
        let paginationQueryParams = {};
        let totalQueryParams = {};
        if (extraQueryParams) {
            paginationQueryParams = Object.assign(Object.assign({}, paginationQueryParams), extraQueryParams);
            totalQueryParams = Object.assign(Object.assign({}, totalQueryParams), extraQueryParams);
        }
        const limit = !options.searchText
            ? (pagination === null || pagination === void 0 ? void 0 : pagination.first) || undefined
            : undefined;
        const offset = !options.searchText
            ? (pagination === null || pagination === void 0 ? void 0 : pagination.offset) || undefined
            : undefined;
        const sortBy = (pagination === null || pagination === void 0 ? void 0 : pagination.sortBy) || 'createdAt';
        const sortOrder = (pagination === null || pagination === void 0 ? void 0 : pagination.sortOrder) || pagination_args_1.PaginationSortOrder.Ascending;
        orderParams.push([sortBy, sortOrder]);
        orderParams.push(['id', 'ASC']);
        if (pagination === null || pagination === void 0 ? void 0 : pagination.after) {
            const afterProfile = await model.findOne({
                where: {
                    id: pagination.after,
                },
            });
            if (sortOrder === pagination_args_1.PaginationSortOrder.Ascending) {
                paginationQueryParams[sortBy] = {
                    [sequelize_1.Op.gte]: afterProfile[sortBy],
                };
            }
            else if (sortOrder === pagination_args_1.PaginationSortOrder.Descending) {
                paginationQueryParams[sortBy] = {
                    [sequelize_1.Op.lte]: afterProfile[sortBy],
                };
            }
            const recordsToExclude = await model.findAll({
                where: {
                    [sortBy]: afterProfile[sortBy],
                    id: {
                        [sequelize_1.Op.lt]: pagination.after,
                    },
                },
            });
            paginationQueryParams['id'] = {
                [sequelize_1.Op.ne]: pagination.after,
                [sequelize_1.Op.notIn]: [...recordsToExclude.map(r => r['id']), ...excludeIds],
            };
        }
        else {
            paginationQueryParams['id'] = {
                [sequelize_1.Op.notIn]: excludeIds,
            };
        }
        totalQueryParams['id'] = {
            [sequelize_1.Op.notIn]: excludeIds,
        };
        if ((includeIds === null || includeIds === void 0 ? void 0 : includeIds.length) > 0) {
            paginationQueryParams['id'][sequelize_1.Op.in] = includeIds;
            totalQueryParams['id'][sequelize_1.Op.in] = includeIds;
        }
        const records = await model.findAll({
            where: paginationQueryParams,
            include: includeParams,
            order: orderParams,
            offset,
            limit,
        });
        const totalCount = await model.count({
            where: totalQueryParams,
        });
        return {
            records,
            totalCount,
        };
    }
}
exports.PaginationHelper = PaginationHelper;
//# sourceMappingURL=pagination.js.map