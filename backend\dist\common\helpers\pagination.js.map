{"version": 3, "file": "pagination.js", "sourceRoot": "", "sources": ["../../../src/common/helpers/pagination.ts"], "names": [], "mappings": ";;;AAAA,6DAA8E;AAC9E,yCAAoD;AAEpD,MAAM,gBAAgB;CAGrB;AAKD,MAAa,gBAAgB;IAC3B,KAAK,CAAC,mBAAmB,CAAC,OAQzB;QACC,MAAM,EACJ,KAAK,EACL,UAAU,EACV,gBAAgB,EAChB,aAAa,EACb,UAAU,EACV,UAAU,GACX,GAAG,OAAO,CAAC;QAEZ,IAAI,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,MAAM,MAAK,IAAI,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,MAAM,WAAW,GAAG,EAAE,CAAC;QACvB,IAAI,qBAAqB,GAAG,EAAE,CAAC;QAC/B,IAAI,gBAAgB,GAAG,EAAE,CAAC;QAE1B,IAAI,gBAAgB,EAAE,CAAC;YACrB,qBAAqB,mCAAQ,qBAAqB,GAAK,gBAAgB,CAAE,CAAC;YAC1E,gBAAgB,mCAAQ,gBAAgB,GAAK,gBAAgB,CAAE,CAAC;QAClE,CAAC;QAED,MAAM,KAAK,GAAG,CAAC,OAAO,CAAC,UAAU;YAC/B,CAAC,CAAC,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,KAAK,KAAI,SAAS;YAChC,CAAC,CAAC,SAAS,CAAC;QACd,MAAM,MAAM,GAAG,CAAC,OAAO,CAAC,UAAU;YAChC,CAAC,CAAC,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,MAAM,KAAI,SAAS;YACjC,CAAC,CAAC,SAAS,CAAC;QAEd,MAAM,MAAM,GAAG,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,MAAM,KAAI,WAAW,CAAC;QACjD,MAAM,SAAS,GAAG,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,SAAS,KAAI,qCAAmB,CAAC,SAAS,CAAC;QAEzE,WAAW,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;QACtC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QAEhC,IAAI,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,KAAK,EAAE,CAAC;YACtB,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC;gBACvC,KAAK,EAAE;oBACL,EAAE,EAAE,UAAU,CAAC,KAAK;iBACrB;aACF,CAAC,CAAC;YAEH,IAAI,SAAS,KAAK,qCAAmB,CAAC,SAAS,EAAE,CAAC;gBAChD,qBAAqB,CAAC,MAAM,CAAC,GAAG;oBAC9B,CAAC,cAAE,CAAC,GAAG,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC;iBAC/B,CAAC;YACJ,CAAC;iBAAM,IAAI,SAAS,KAAK,qCAAmB,CAAC,UAAU,EAAE,CAAC;gBACxD,qBAAqB,CAAC,MAAM,CAAC,GAAG;oBAC9B,CAAC,cAAE,CAAC,GAAG,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC;iBAC/B,CAAC;YACJ,CAAC;YAED,MAAM,gBAAgB,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC;gBAC3C,KAAK,EAAE;oBACL,CAAC,MAAM,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC;oBAC9B,EAAE,EAAE;wBACF,CAAC,cAAE,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,KAAK;qBAC1B;iBACF;aACF,CAAC,CAAC;YAEH,qBAAqB,CAAC,IAAI,CAAC,GAAG;gBAC5B,CAAC,cAAE,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,KAAK;gBACzB,CAAC,cAAE,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,UAAU,CAAC;aACnE,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,qBAAqB,CAAC,IAAI,CAAC,GAAG;gBAC5B,CAAC,cAAE,CAAC,KAAK,CAAC,EAAE,UAAU;aACvB,CAAC;QACJ,CAAC;QAED,gBAAgB,CAAC,IAAI,CAAC,GAAG;YACvB,CAAC,cAAE,CAAC,KAAK,CAAC,EAAE,UAAU;SACvB,CAAC;QAEF,IAAI,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,MAAM,IAAG,CAAC,EAAE,CAAC;YAC3B,qBAAqB,CAAC,IAAI,CAAC,CAAC,cAAE,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC;YAChD,gBAAgB,CAAC,IAAI,CAAC,CAAC,cAAE,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC;QAC7C,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC;YAClC,KAAK,EAAE,qBAAqB;YAC5B,OAAO,EAAE,aAAa;YACtB,KAAK,EAAE,WAAW;YAClB,MAAM;YACN,KAAK;SACN,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC;YACnC,KAAK,EAAE,gBAAgB;SACxB,CAAC,CAAC;QAEH,OAAO;YACL,OAAO;YACP,UAAU;SACX,CAAC;IACJ,CAAC;CACF;AA3GD,4CA2GC"}