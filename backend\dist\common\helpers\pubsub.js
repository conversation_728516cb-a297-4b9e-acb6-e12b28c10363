"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.withCancel = withCancel;
function withCancel(asyncIterator, onCancel) {
    if (!asyncIterator.return) {
        asyncIterator.return = () => Promise.resolve({ value: undefined, done: true });
    }
    const savedReturn = asyncIterator.return.bind(asyncIterator);
    asyncIterator.return = () => {
        onCancel();
        return savedReturn();
    };
    return asyncIterator;
}
//# sourceMappingURL=pubsub.js.map