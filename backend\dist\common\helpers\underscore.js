"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Underscore = void 0;
class Underscore {
}
exports.Underscore = Underscore;
Underscore.isEmpty = obj => [Object, Array].includes((obj || {}).constructor) &&
    !Object.entries(obj || {}).length;
Underscore.uniq = array => [...new Set(array || [])];
Underscore.map = (array, field) => (array || []).map(value => value[field]);
Underscore.includesAny = (array1, array2) => (array1 || []).some(r => (array2 || []).includes(r));
//# sourceMappingURL=underscore.js.map