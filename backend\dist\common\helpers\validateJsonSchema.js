"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const extractErrors = (errors) => {
    let extractedErrors = [];
    for (const error of errors) {
        if (error.constraints) {
            extractedErrors.push(...Object.values(error.constraints));
        }
        if (error.children && error.children.length) {
            extractedErrors = extractedErrors.concat(extractErrors(error.children));
        }
    }
    return extractedErrors;
};
const validateData = async (type, data, logger) => {
    const instance = (0, class_transformer_1.plainToClass)(type, data);
    try {
        await (0, class_validator_1.validateOrReject)(instance);
    }
    catch (errors) {
        const errorMessages = extractErrors(errors);
        logger.error('ActivitiesService.createUserActivity', errorMessages);
        throw new Error('Something went wrong while processing your request. Please try again.');
    }
};
exports.default = validateData;
//# sourceMappingURL=validateJsonSchema.js.map