"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApolloComplexityPlugin = void 0;
const graphql_1 = require("graphql");
const graphql_query_complexity_1 = require("graphql-query-complexity");
class ApolloComplexityPlugin {
    constructor(maxComplexity) {
        this.maxComplexity = maxComplexity;
    }
    serverWillStart(service) {
        this.schema = service.schema;
    }
    requestDidStart() {
        return {
            didResolveOperation: ({ request, document }) => {
                const complexity = (0, graphql_query_complexity_1.getComplexity)({
                    schema: this.schema,
                    query: request.operationName
                        ? (0, graphql_1.separateOperations)(document)[request.operationName]
                        : document,
                    variables: request.variables,
                    estimators: [
                        (0, graphql_query_complexity_1.fieldExtensionsEstimator)(),
                        (0, graphql_query_complexity_1.simpleEstimator)({ defaultComplexity: 1 }),
                    ],
                });
                if (complexity > this.maxComplexity) {
                    throw new Error(`Sorry, too complicated query (complexity: ${complexity}, max complexity: ${this.maxComplexity})`);
                }
            },
        };
    }
}
exports.ApolloComplexityPlugin = ApolloComplexityPlugin;
//# sourceMappingURL=complexity.plugin.js.map