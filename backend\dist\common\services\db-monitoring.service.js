"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseMonitoringService = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const sequelize_2 = require("sequelize");
const db_monitoring_1 = require("../helpers/db-monitoring");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
let DatabaseMonitoringService = class DatabaseMonitoringService {
    constructor(sequelize, logger) {
        this.sequelize = sequelize;
        this.logger = logger;
        this.monitoring = new db_monitoring_1.DatabaseMonitoring(sequelize);
    }
    async monitorDatabaseHealth() {
        try {
            const dbStats = await this.monitoring.getDatabaseStats();
            this.logger.info('DBMonitoringService.monitorDatabaseHealth - Database Stats', { dbStats });
            const poolStats = await this.monitoring.getConnectionPoolStats();
            this.logger.info('DBMonitoringService.monitorDatabaseHealth - Connection Pool Stats', { poolStats });
            const bufferStats = await this.monitoring.getBufferHitRatio();
            this.logger.info('DBMonitoringService.monitorDatabaseHealth - Buffer Hit Ratio', { bufferStats });
            const tableStats = await this.monitoring.getTableStats();
            this.logger.info('DBMonitoringService.monitorDatabaseHealth - Table Stats', { tableStats });
            this.checkForIssues(dbStats, poolStats, bufferStats);
        }
        catch (error) {
            this.logger.error('DBMonitoringService.monitorDatabaseHealth - Error monitoring database health:', error);
        }
    }
    checkForIssues(dbStats, poolStats, bufferStats) {
        const isProduction = process.env.NODE_ENV === 'production';
        const isBeta = process.env.NODE_ENV === 'beta';
        const isE2E = process.env.NODE_ENV === 'e2e';
        bufferStats.forEach(stat => {
            if (stat.buffer_hit_ratio < 0.99) {
                this.logger.warn(`Low buffer hit ratio for ${stat.database_name}: ${stat.buffer_hit_ratio}`);
            }
        });
        poolStats.forEach(stat => {
            let maxConnections;
            if (stat.database_name === 'hablo' && isProduction) {
                maxConnections = 500;
            }
            else if (stat.database_name === 'hablo-beta' && isBeta) {
                maxConnections = 25;
            }
            else if (stat.database_name === 'hablo-e2e' && isE2E) {
                maxConnections = 25;
            }
            else {
                maxConnections = 25;
            }
            if (stat.total_connections > maxConnections * 0.95) {
                this.logger.error(`Extremely high connection usage for ${stat.database_name}: ${stat.total_connections}/${maxConnections}`);
            }
            else if (stat.total_connections > maxConnections * 0.8) {
                this.logger.warn(`High connection usage for ${stat.database_name}: ${stat.total_connections}/${maxConnections}`);
            }
        });
        dbStats.forEach(stat => {
            if (stat.transactions_rollback > 0) {
                this.logger.warn(`Rollbacks detected for ${stat.database_name}: ${stat.transactions_rollback}`);
            }
        });
    }
    async setupMonitoring() {
        try {
            setInterval(() => this.monitorDatabaseHealth(), 5 * 60 * 1000);
            this.logger.info('Database monitoring started');
        }
        catch (error) {
            this.logger.error('Error setting up monitoring:', error);
        }
    }
};
exports.DatabaseMonitoringService = DatabaseMonitoringService;
exports.DatabaseMonitoringService = DatabaseMonitoringService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, sequelize_1.InjectConnection)()),
    __param(1, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [sequelize_2.Sequelize,
        winston_1.Logger])
], DatabaseMonitoringService);
//# sourceMappingURL=db-monitoring.service.js.map