"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestModules = exports.TestHelper = void 0;
const moment_1 = __importDefault(require("moment"));
const util_1 = __importDefault(require("util"));
const child_process_1 = __importDefault(require("child_process"));
const exec = util_1.default.promisify(child_process_1.default.exec);
const organisations_args_1 = require("../../organisations/args/organisations.args");
const events_args_1 = require("../../events/args/events.args");
const sequelize_1 = require("./sequelize");
const winston_1 = require("./winston");
const sendgrid_1 = require("./sendgrid");
const redis_1 = require("./redis");
const incentives_args_1 = require("../../incentives/args/incentives.args");
const webinars_args_1 = require("../../webinars/args/webinars.args");
const testcontainers_1 = require("testcontainers");
class TestHelper {
    createProfile(profilesService, testName) {
        return profilesService.create({
            id: `test-user-1-${testName}`,
            authZeroUserId: `auth|test-user-1-${testName}`,
            email: `test1-${testName}@localhost.com`,
            name: `Test User 1 (${testName})`,
            sellHolidays: false,
        });
    }
    createOrganisation(organisationsService, testName, type = organisations_args_1.OrganisationType.Destination, status = organisations_args_1.OrganisationStatus.Active) {
        return organisationsService.create({
            id: `test-organisation-1-${testName}`,
            name: `Test Org 1 (${testName})`,
            type,
            status,
        });
    }
    createEvent(eventsService, testName, type = events_args_1.EventType.Event) {
        const startDate = (0, moment_1.default)().add(1, 'days').toDate();
        const endDate = (0, moment_1.default)().add(5, 'days').toDate();
        return eventsService.create({
            id: `test-event-1-${testName}`,
            name: `Test Event 1 (${testName})`,
            description: 'Event Desc',
            type,
            startDate,
            endDate,
            isPublic: false,
        });
    }
    createIncentive(incentivesService, testName, organisationId, type = incentives_args_1.IncentiveType.Prize) {
        const startDate = (0, moment_1.default)().add(1, 'days').toDate();
        const endDate = (0, moment_1.default)().add(5, 'days').toDate();
        return incentivesService.create({
            id: `test-incentive-1-${testName}`,
            organisationId,
            name: `Test Incentive 1 (${testName})`,
            description: 'Incentive Desc',
            type,
            startDate,
            endDate,
            isPublic: false,
        });
    }
    createWebinar(webinarsService, testName, organisationId, type = webinars_args_1.WebinarType.LiveStream) {
        const startDate = (0, moment_1.default)().add(1, 'days').toDate();
        const endDate = (0, moment_1.default)().add(5, 'days').toDate();
        return webinarsService.create({
            id: `test-webinar-1-${testName}`,
            organisationId,
            name: `Test Webinar 1 (${testName})`,
            description: 'Webinar Desc',
            type,
            startDate,
            endDate,
            isPublic: false,
        });
    }
    getCurrentUser(profile) {
        return {
            sub: `auth|${profile.id}`,
            name: profile.name,
            profileId: profile.id,
            email: profile.email,
            picture: profile.image,
            idToken: 'IDTOKEN1',
            toLogObject: () => ({}),
        };
    }
}
exports.TestHelper = TestHelper;
class TestModules {
    static async runMigration(host, port) {
        try {
            const { stdout, stderr } = await exec(`yarn sequelize-cli db:migrate --url 'postgres://postgres:password@${host}:${port}/postgres'`);
            if (stderr) {
                console.error('stderr:', stderr);
            }
        }
        catch (err) {
            console.error(err);
        }
    }
    static async init(options) {
        const { host, port } = options.postgres;
        await this.runMigration(host, port);
        return [
            new sequelize_1.SequelizeTestModule().init(host, port),
            new winston_1.WinstonTestModule().init(options),
            new sendgrid_1.SendGridTestModule().init(),
            new redis_1.RedisTestModule().init(),
        ];
    }
    static async createPostgresContainer() {
        return new testcontainers_1.GenericContainer('postgres')
            .withEnvironment({
            POSTGRES_USER: 'postgres',
            POSTGRES_PASSWORD: 'password',
            POSTGRES_DB: 'postgres',
        })
            .withExposedPorts(5432)
            .start();
    }
}
exports.TestModules = TestModules;
//# sourceMappingURL=helper.js.map