"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MockChatService = void 0;
class MockChatService {
    constructor() {
        this.createChannel = jest.fn().mockReturnValue({
            channel: {
                id: 'test-id',
            },
        });
        this.removeChannel = jest.fn();
        this.freezeChannel = jest.fn();
        this.upsertUser = jest.fn();
    }
}
exports.MockChatService = MockChatService;
//# sourceMappingURL=chat.service.js.map