"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MockWebinarBroadcastService = void 0;
class MockWebinarBroadcastService {
    constructor() {
        this.createBroadcastStream = jest.fn().mockReturnValue({
            liveStreamId: 'livestream id',
            liveStreamPrivateKey: 'livestream secret',
            liveStreamPlaybackId: 'livestream playback id',
        });
        this.createWebinarChannel = jest.fn();
        this.addToWebinarChannel = jest.fn();
    }
}
exports.MockWebinarBroadcastService = MockWebinarBroadcastService;
//# sourceMappingURL=webinar-broadcast.service.js.map