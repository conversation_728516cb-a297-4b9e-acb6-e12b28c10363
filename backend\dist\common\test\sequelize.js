"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SequelizeTestModule = void 0;
const sequelize_1 = require("@nestjs/sequelize");
class SequelizeTestModule {
    init(host, port) {
        return sequelize_1.SequelizeModule.forRoot({
            dialect: 'postgres',
            host,
            port,
            username: 'postgres',
            password: 'password',
            database: 'postgres',
            autoLoadModels: true,
            synchronize: false,
            logging: false,
        });
    }
}
exports.SequelizeTestModule = SequelizeTestModule;
//# sourceMappingURL=sequelize.js.map