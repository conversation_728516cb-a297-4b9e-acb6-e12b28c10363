"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WinstonTestModule = void 0;
const nest_winston_1 = require("nest-winston");
const winston_1 = __importDefault(require("winston"));
const config_1 = __importDefault(require("../../config/config"));
class WinstonTestModule {
    init(options) {
        return nest_winston_1.WinstonModule.forRoot({
            level: (options === null || options === void 0 ? void 0 : options.logLevel) || config_1.default.LOG_LEVEL,
            transports: [
                new winston_1.default.transports.Console({
                    format: winston_1.default.format.combine(winston_1.default.format.timestamp(), nest_winston_1.utilities.format.nestLike()),
                    silent: !(options === null || options === void 0 ? void 0 : options.debug),
                }),
            ],
        });
    }
}
exports.WinstonTestModule = WinstonTestModule;
//# sourceMappingURL=winston.js.map