"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NodeEnv = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = require("path");
var NodeEnv;
(function (NodeEnv) {
    NodeEnv["development"] = "development";
    NodeEnv["production-e2e"] = "production-e2e";
    NodeEnv["production"] = "production";
})(NodeEnv || (exports.NodeEnv = NodeEnv = {}));
dotenv_1.default.config({ path: (0, path_1.join)(__dirname, `../../.env.${process.env.NODE_ENV}`) });
const localMode = process.env.NODE_ENV_LOCAL === 'true';
const { NODE_ENV, PORT, DB_HOST, DB_USER, DB_PASS, DB_NAME = 'hablo', DB_PORT, LOG_LEVEL, AUTH0_DOMAIN, AUTH0_CLIENT_ID, AUTH0_CLIENT_SECRET, AUTH0_MANAGEMENT_CLIENT_ID, AUTH0_MANAGEMENT_CLIENT_SECRET, CLOUDINARY_API_SECRET, CLOUDINARY_CLOUD_NAME, STREAM_KEY, STREAM_SECRET, STREAM_BETA_KEY, STREAM_BETA_SECRET, STREAM_CHAT_KEY, STREAM_CHAT_SECRET, STREAM_BETA_CHAT_KEY, STREAM_BETA_CHAT_SECRET, SENDGRID_API_KEY, SENDGRID_FROM, SENDGRID_REPLY_TO, SENDGRID_NAME, REDIS_HOST, REDIS_PORT, SERVICE, ZOOM_CLIENT_ID, ZOOM_CLIENT_SECRET, ZOOM_REDIRECT_URL, STRIPE_SECRET_KEY, DOCSBOT_TEAM_ID, DOCSBOT_API_KEY, ZOOM_ACCOUNT_ID, ONE_SIGNAL_APP_ID, ONE_SIGNAL_API_KEY, } = process.env;
const frontendUrl = SERVICE === 'api-beta' ? 'https://beta.myhablo.com' : 'https://myhablo.com';
const config = {
    DB_HOST: localMode ? 'localhost' : DB_HOST,
    DB_USER,
    DB_PASS,
    DB_NAME,
    DB_PORT: +DB_PORT,
    LOG_LEVEL,
    NODE_ENV,
    PORT: localMode ? 4000 : +PORT,
    AUTH0_DOMAIN,
    AUTH0_CLIENT_ID,
    AUTH0_CLIENT_SECRET,
    AUTH0_MANAGEMENT_CLIENT_ID,
    AUTH0_MANAGEMENT_CLIENT_SECRET,
    CLOUDINARY_API_SECRET,
    CLOUDINARY_CLOUD_NAME,
    STREAM_KEY,
    STREAM_SECRET,
    STREAM_BETA_KEY,
    STREAM_BETA_SECRET,
    STREAM_CHAT_KEY,
    STREAM_CHAT_SECRET,
    STREAM_BETA_CHAT_KEY,
    STREAM_BETA_CHAT_SECRET,
    SENDGRID_API_KEY,
    SENDGRID_FROM,
    SENDGRID_REPLY_TO,
    SENDGRID_NAME,
    REDIS_HOST: localMode ? 'localhost' : REDIS_HOST,
    REDIS_PORT: +REDIS_PORT,
    SERVICE,
    ZOOM_CLIENT_ID,
    ZOOM_CLIENT_SECRET,
    ZOOM_REDIRECT_URL,
    FRONTEND_URL: localMode || NODE_ENV === 'development'
        ? 'https://localhost.myhablo.com:3000'
        : frontendUrl,
    STRIPE_SECRET_KEY,
    DOCSBOT_TEAM_ID,
    DOCSBOT_API_KEY,
    ZOOM_ACCOUNT_ID,
    ONE_SIGNAL_APP_ID,
    ONE_SIGNAL_API_KEY,
};
exports.default = config;
//# sourceMappingURL=config.js.map