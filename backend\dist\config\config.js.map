{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../src/config/config.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAAyB;AACzB,+BAA4B;AA6C5B,IAAY,OAIX;AAJD,WAAY,OAAO;IACjB,sCAA2B,CAAA;IAC3B,4CAAmC,CAAA;IACnC,oCAAyB,CAAA;AAC3B,CAAC,EAJW,OAAO,uBAAP,OAAO,QAIlB;AAED,gBAAG,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,IAAA,WAAI,EAAC,SAAS,EAAE,cAAc,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AAE5E,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,KAAK,MAAM,CAAC;AAExD,MAAM,EACJ,QAAQ,EACR,IAAI,EACJ,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,GAAG,OAAO,EACjB,OAAO,EACP,SAAS,EACT,YAAY,EACZ,eAAe,EACf,mBAAmB,EACnB,0BAA0B,EAC1B,8BAA8B,EAC9B,qBAAqB,EACrB,qBAAqB,EACrB,UAAU,EACV,aAAa,EACb,eAAe,EACf,kBAAkB,EAClB,eAAe,EACf,kBAAkB,EAClB,oBAAoB,EACpB,uBAAuB,EACvB,gBAAgB,EAChB,aAAa,EACb,iBAAiB,EACjB,aAAa,EACb,UAAU,EACV,UAAU,EACV,OAAO,EACP,cAAc,EACd,kBAAkB,EAClB,iBAAiB,EACjB,iBAAiB,EACjB,eAAe,EACf,eAAe,EACf,eAAe,EACf,iBAAiB,EACjB,kBAAkB,GACnB,GAAG,OAAO,CAAC,GAAG,CAAC;AAEhB,MAAM,WAAW,GACf,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAC,CAAC,qBAAqB,CAAC;AAE9E,MAAM,MAAM,GAAW;IACrB,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO;IAC1C,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO,EAAE,CAAC,OAAO;IACjB,SAAS;IACT,QAAQ;IACR,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI;IAC9B,YAAY;IACZ,eAAe;IACf,mBAAmB;IACnB,0BAA0B;IAC1B,8BAA8B;IAC9B,qBAAqB;IACrB,qBAAqB;IACrB,UAAU;IACV,aAAa;IACb,eAAe;IACf,kBAAkB;IAClB,eAAe;IACf,kBAAkB;IAClB,oBAAoB;IACpB,uBAAuB;IACvB,gBAAgB;IAChB,aAAa;IACb,iBAAiB;IACjB,aAAa;IACb,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU;IAChD,UAAU,EAAE,CAAC,UAAU;IACvB,OAAO;IACP,cAAc;IACd,kBAAkB;IAClB,iBAAiB;IACjB,YAAY,EACV,SAAS,IAAI,QAAQ,KAAK,aAAa;QACrC,CAAC,CAAC,oCAAoC;QACtC,CAAC,CAAC,WAAW;IACjB,iBAAiB;IACjB,eAAe;IACf,eAAe;IACf,eAAe;IACf,iBAAiB;IACjB,kBAAkB;CACnB,CAAC;AAEF,kBAAe,MAAM,CAAC"}