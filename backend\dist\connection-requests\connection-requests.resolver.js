"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectionRequestsResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const connection_request_model_1 = require("./models/connection-request.model");
const connection_request_model_2 = require("./models/connection-request.model");
const connection_requests_service_1 = require("./connection-requests.service");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
let ConnectionRequestsResolver = class ConnectionRequestsResolver {
    async connect(user, profileId) {
        this.logger.verbose('ConnectionRequestsResolver.connect (mutation)', {
            user: user.toLogObject(),
            profileId,
        });
        const oppositeConnectionRequest = await this.connectionRequestsService.findOne({
            receiverProfileId: user.profileId,
            senderProfileId: profileId,
        });
        if (oppositeConnectionRequest &&
            oppositeConnectionRequest.status === connection_request_model_1.ConnectionRequestStatus.Pending) {
            await this.connectionRequestsService.accept(profileId, user.profileId);
            return true;
        }
        await this.connectionRequestsService.connect({
            senderProfileId: user.profileId,
            receiverProfileId: profileId,
            status: connection_request_model_1.ConnectionRequestStatus.Pending,
        });
        return true;
    }
    async acceptInvitation(user, profileId) {
        this.logger.verbose('ConnectionRequestsResolver.acceptInvitation (mutation)', {
            user: user.toLogObject(),
            profileId,
        });
        return await this.connectionRequestsService.accept(profileId, user.profileId);
    }
    async rejectInvitation(user, profileId) {
        this.logger.verbose('ConnectionRequestsResolver.rejectInvitation (mutation)', {
            user: user.toLogObject(),
            profileId,
        });
        return await this.connectionRequestsService.reject(profileId, user.profileId);
    }
};
exports.ConnectionRequestsResolver = ConnectionRequestsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => connection_requests_service_1.ConnectionRequestsService)),
    __metadata("design:type", connection_requests_service_1.ConnectionRequestsService)
], ConnectionRequestsResolver.prototype, "connectionRequestsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], ConnectionRequestsResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('profileId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ConnectionRequestsResolver.prototype, "connect", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('profileId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ConnectionRequestsResolver.prototype, "acceptInvitation", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('profileId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ConnectionRequestsResolver.prototype, "rejectInvitation", null);
exports.ConnectionRequestsResolver = ConnectionRequestsResolver = __decorate([
    (0, graphql_1.Resolver)(() => connection_request_model_2.ConnectionRequest)
], ConnectionRequestsResolver);
//# sourceMappingURL=connection-requests.resolver.js.map