"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectionRequest = exports.ConnectionRequestStatus = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const profile_model_1 = require("../../profiles/models/profile.model");
const notification_model_1 = require("../../notifications/models/notification.model");
const short_uuid_1 = __importDefault(require("short-uuid"));
const graphql_1 = require("@nestjs/graphql");
var ConnectionRequestStatus;
(function (ConnectionRequestStatus) {
    ConnectionRequestStatus["Pending"] = "Pending";
    ConnectionRequestStatus["Accepted"] = "Accepted";
    ConnectionRequestStatus["Rejected"] = "Rejected";
})(ConnectionRequestStatus || (exports.ConnectionRequestStatus = ConnectionRequestStatus = {}));
(0, graphql_1.registerEnumType)(ConnectionRequestStatus, { name: 'ConnectionRequestStatus' });
let ConnectionRequest = class ConnectionRequest extends sequelize_typescript_1.Model {
};
exports.ConnectionRequest = ConnectionRequest;
__decorate([
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], ConnectionRequest.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], ConnectionRequest.prototype, "status", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    (0, sequelize_typescript_1.Column)({ allowNull: false }),
    __metadata("design:type", String)
], ConnectionRequest.prototype, "senderProfileId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, 'senderProfileId'),
    __metadata("design:type", profile_model_1.Profile)
], ConnectionRequest.prototype, "senderProfile", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    (0, sequelize_typescript_1.Column)({ allowNull: false }),
    __metadata("design:type", String)
], ConnectionRequest.prototype, "receiverProfileId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, 'receiverProfileId'),
    __metadata("design:type", profile_model_1.Profile)
], ConnectionRequest.prototype, "receiverProfile", void 0);
__decorate([
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], ConnectionRequest.prototype, "createdAt", void 0);
__decorate([
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], ConnectionRequest.prototype, "updatedAt", void 0);
__decorate([
    (0, graphql_1.Field)(() => profile_model_1.Profile),
    __metadata("design:type", profile_model_1.Profile)
], ConnectionRequest.prototype, "profile", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => notification_model_1.Notification, {
        foreignKey: 'membershipId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", Array)
], ConnectionRequest.prototype, "notifications", void 0);
exports.ConnectionRequest = ConnectionRequest = __decorate([
    (0, graphql_1.ObjectType)(),
    sequelize_typescript_1.Table
], ConnectionRequest);
//# sourceMappingURL=connection-request.model.js.map