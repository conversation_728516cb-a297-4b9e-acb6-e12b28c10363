"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.connectionArgs = exports.ConnectionsFilter = exports.InvitationOptions = void 0;
const pagination_args_1 = require("./../../common/args/pagination.args");
const graphql_1 = require("@nestjs/graphql");
exports.InvitationOptions = {
    1: `I'd like to add you to my global travel network on Hablo`,
    2: `I'd like our organisations to connect on Hablo to help drive sales`,
    3: `I'd like to connect on Hablo to help me drive sales`,
    4: `I'd like to connect on Hablo for my professional development`,
    5: `I'd like to connect on Hablo to build a closer working relationship`,
};
let ConnectionsFilter = class ConnectionsFilter {
};
exports.ConnectionsFilter = ConnectionsFilter;
__decorate([
    (0, graphql_1.Field)({
        nullable: true,
    }),
    __metadata("design:type", String)
], ConnectionsFilter.prototype, "connectionProfileId", void 0);
exports.ConnectionsFilter = ConnectionsFilter = __decorate([
    (0, graphql_1.InputType)()
], ConnectionsFilter);
let connectionArgs = class connectionArgs extends pagination_args_1.PaginationArgs {
};
exports.connectionArgs = connectionArgs;
__decorate([
    (0, graphql_1.Field)(() => ConnectionsFilter, { nullable: true }),
    __metadata("design:type", ConnectionsFilter)
], connectionArgs.prototype, "filter", void 0);
exports.connectionArgs = connectionArgs = __decorate([
    (0, graphql_1.ArgsType)()
], connectionArgs);
//# sourceMappingURL=connection.args.js.map