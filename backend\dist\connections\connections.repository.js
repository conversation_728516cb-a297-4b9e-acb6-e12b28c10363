"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectionsRepository = void 0;
const helper_1 = require("./helpers/helper");
const organisation_model_1 = require("./../organisations/models/organisation.model");
const membership_model_1 = require("./../memberships/models/membership.model");
const connection_model_1 = require("./models/connection.model");
const common_1 = require("@nestjs/common");
const profile_model_1 = require("../profiles/models/profile.model");
let ConnectionsRepository = class ConnectionsRepository {
    async findConnections(profileId, filter, pagination) {
        const extraQueryParams = {};
        if (filter === null || filter === void 0 ? void 0 : filter.profileId) {
            extraQueryParams.profileId = filter.profileId;
        }
        if (filter === null || filter === void 0 ? void 0 : filter.connectionProfileId) {
            extraQueryParams.connectionProfileId = filter.connectionProfileId;
        }
        return await new helper_1.PaginationHelper().getPaginatedResults({
            model: connection_model_1.Connection,
            pagination,
            extraQueryParams,
            excludeIds: ['_deleted-user_', filter === null || filter === void 0 ? void 0 : filter.profileId],
            includeParams: [
                {
                    as: 'profile',
                    model: profile_model_1.Profile,
                    include: [
                        {
                            as: 'memberships',
                            model: membership_model_1.Membership,
                            attributes: [
                                'id',
                                'position',
                                'isPrimary',
                                'organisationName',
                                'organisationId',
                                'status',
                            ],
                            include: [
                                {
                                    as: 'organisation',
                                    model: organisation_model_1.Organisation,
                                    attributes: [
                                        'name',
                                        'id',
                                        'type',
                                        'vanityId',
                                        'image',
                                        'privacy',
                                        'status',
                                        'additionalPrivacy',
                                        'followingPrivacy',
                                        'peoplePrivacy',
                                        'connectedOrganisations',
                                    ],
                                },
                            ],
                        },
                    ],
                },
            ],
        });
    }
};
exports.ConnectionsRepository = ConnectionsRepository;
exports.ConnectionsRepository = ConnectionsRepository = __decorate([
    (0, common_1.Injectable)()
], ConnectionsRepository);
//# sourceMappingURL=connections.repository.js.map