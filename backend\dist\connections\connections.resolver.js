"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectionsResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const error_1 = require("../common/helpers/error");
const email_service_1 = require("../email/email.service");
const profile_model_1 = require("../profiles/models/profile.model");
const profiles_service_1 = require("../profiles/profiles.service");
const connections_service_1 = require("./connections.service");
const email_connection_input_1 = require("./dto/email-connection.input");
const connection_model_1 = require("./models/connection.model");
let ConnectionsResolver = class ConnectionsResolver {
    async inviteByEmail(user, emailConnectionInput) {
        this.logger.verbose('ConnectionsResolver.inviteByEmail (query)', {
            user: user.toLogObject(),
        });
        await this.emailService.sendInvitations(user.profileId, emailConnectionInput);
        return true;
    }
    async removeConnection(user, profileId) {
        this.logger.verbose('ConnectionsResolver.removeConnection (mutation)', {
            user: user.toLogObject(),
            profileId,
        });
        return this.connectionsService.removeConnection(user.profileId, profileId);
    }
    async profile(connection) {
        if (connection.profile)
            return connection.profile;
        this.logger.verbose('ConnectionsResolver.profile (field resolver)', {
            connectionId: connection.id,
        });
        return this.profilesService.findById(connection.profileId);
    }
};
exports.ConnectionsResolver = ConnectionsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => connections_service_1.ConnectionsService)),
    __metadata("design:type", connections_service_1.ConnectionsService)
], ConnectionsResolver.prototype, "connectionsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], ConnectionsResolver.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => email_service_1.EmailService)),
    __metadata("design:type", email_service_1.EmailService)
], ConnectionsResolver.prototype, "emailService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], ConnectionsResolver.prototype, "errorHelper", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], ConnectionsResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('emailConnectionInput')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, email_connection_input_1.EmailConnectionInput]),
    __metadata("design:returntype", Promise)
], ConnectionsResolver.prototype, "inviteByEmail", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('profileId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ConnectionsResolver.prototype, "removeConnection", null);
__decorate([
    (0, graphql_1.ResolveField)('profile', () => profile_model_1.Profile),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [connection_model_1.Connection]),
    __metadata("design:returntype", Promise)
], ConnectionsResolver.prototype, "profile", null);
exports.ConnectionsResolver = ConnectionsResolver = __decorate([
    (0, graphql_1.Resolver)(() => connection_model_1.Connection)
], ConnectionsResolver);
//# sourceMappingURL=connections.resolver.js.map