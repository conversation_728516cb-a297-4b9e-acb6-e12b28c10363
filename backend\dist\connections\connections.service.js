"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectionsService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const sequelize_1 = require("sequelize");
const sequelize_typescript_1 = require("sequelize-typescript");
const winston_1 = require("winston");
const activities_service_1 = require("../activities/activities.service");
const activities_args_1 = require("../activities/args/activities.args");
const create_user_activity_data_dto_1 = require("../activities/dto/create-user-activity-data.dto");
const chat_service_1 = require("../chat/chat.service");
const base_service_1 = require("../common/base.service");
const error_1 = require("../common/helpers/error");
const connection_requests_service_1 = require("../connection-requests/connection-requests.service");
const connection_request_model_1 = require("../connection-requests/models/connection-request.model");
const profiles_service_1 = require("../profiles/profiles.service");
const connections_repository_1 = require("./connections.repository");
const connection_model_1 = require("./models/connection.model");
const getting_started_steps_service_1 = require("../getting-started-steps/getting-started-steps.service");
const getting_started_steps_args_1 = require("../getting-started-steps/args/getting-started-steps.args");
const achievements_service_1 = require("../achievements/achievements.service");
let ConnectionsService = class ConnectionsService extends (0, base_service_1.BaseService)(connection_model_1.Connection) {
    async createConnection(profileIds, connectionRequestId, options) {
        this.logger.info('ConnectionsService.createConnection', {
            profileIds,
            connectionRequestId,
        });
        if (profileIds.length !== 2) {
            this.errorHelper.throwHttpException(`ConnectionsService.createConnection`, 'CreateConnectionDto requires profileIds with 2 element');
        }
        const profileId = profileIds[0];
        const connectionProfileId = profileIds[1];
        const transaction = options === null || options === void 0 ? void 0 : options.transaction;
        const connectionRequest = await this.connectionRequestsService.findById(connectionRequestId, { transaction });
        if (!connectionRequest) {
            throw new Error('Connection request not exists');
        }
        if (connectionRequest.status !== connection_request_model_1.ConnectionRequestStatus.Accepted) {
            throw new Error('Connection request status must be Accepted');
        }
        const profile = await this.profilesService.findById(profileId, {
            transaction,
        });
        if (!(profile === null || profile === void 0 ? void 0 : profile.streamUserId)) {
            await this.chatService.upsertUser(profileId, { transaction });
        }
        const connectionProfile = await this.profilesService.findById(connectionProfileId, {
            transaction,
        });
        if (!(connectionProfile === null || connectionProfile === void 0 ? void 0 : connectionProfile.streamUserId)) {
            await this.chatService.upsertUser(connectionProfileId, { transaction });
        }
        const channelApiResponse = await this.chatService.createChannel(profileId, connectionProfileId);
        const connection = await this.create({
            profileId,
            connectionProfileId,
            connectionRequestId,
            streamChannelId: channelApiResponse.channel.id,
        }, { transaction });
        const previousActivity = await this.activitiesService.findOne({
            type: activities_args_1.ActivityType.UserConnection,
            profileId,
            data: {
                [sequelize_1.Op.contains]: {
                    connectionProfileId,
                    connectionRequestId,
                },
            },
        });
        await this.activitiesService.createUserActivity({
            type: activities_args_1.ActivityType.UserConnection,
            schema: create_user_activity_data_dto_1.createConnectionActivityDataDto,
            data: {
                follow: true,
                profileId,
                connectionProfileId,
                connectionRequestId,
                streamChannelId: channelApiResponse.channel.id,
            },
            profileId,
            transaction,
            addLoyaltyPoints: !previousActivity,
            placeholders: {
                id: connectionProfileId,
                userId: connectionProfileId,
                name: connectionProfile.name,
            },
        });
        await this.create({
            profileId: connectionProfileId,
            connectionProfileId: profileId,
            connectionRequestId,
            streamChannelId: channelApiResponse.channel.id,
        }, { transaction });
        const previousActivityForConnectionProfile = await this.activitiesService.findOne({
            type: activities_args_1.ActivityType.UserConnection,
            profileId: connectionProfileId,
            data: {
                [sequelize_1.Op.contains]: {
                    connectionProfileId: profileId,
                    connectionRequestId,
                },
            },
        });
        await this.activitiesService.createUserActivity({
            type: activities_args_1.ActivityType.UserConnection,
            schema: create_user_activity_data_dto_1.createConnectionActivityDataDto,
            data: {
                follow: true,
                profileId: connectionProfileId,
                connectionProfileId: profileId,
                connectionRequestId,
                streamChannelId: channelApiResponse.channel.id,
            },
            profileId: connectionProfileId,
            transaction,
            addLoyaltyPoints: !previousActivityForConnectionProfile,
            placeholders: {
                id: profileId,
                userId: profileId,
                name: profile.name,
            },
        });
        const currentUserConnections = await this.findAll({
            profileId,
        });
        const connectedUserConnections = await this.findAll({
            profileId: connectionProfileId,
        });
        if (currentUserConnections.length + 1 > 2) {
            await this.gettingStartedStepsService.createGettingStartedStep({
                step: getting_started_steps_args_1.GettingStartedStepEnum.ConnectWithPeople,
                profileId,
                transaction,
            });
        }
        if (connectedUserConnections.length + 1 > 2) {
            await this.gettingStartedStepsService.createGettingStartedStep({
                step: getting_started_steps_args_1.GettingStartedStepEnum.ConnectWithPeople,
                profileId: connectionProfileId,
                transaction,
            });
        }
        await this.profilesService.incrementConnectionsCount([profileId, connectionProfileId], 1, transaction);
        await this.achievementsService.addCommunityBeaconAchievement({
            profileId: connectionProfileId,
        });
        return this.findById(connection.id, { transaction });
    }
    async removeConnection(profileId, connectionProfileId) {
        this.logger.info('ConnectionsService.removeConnection', {
            profileId,
            connectionProfileId,
        });
        const transaction = await this.sequelize.transaction();
        try {
            const connection = await this.findOne({
                profileId,
                connectionProfileId,
            }, { transaction });
            if (connection) {
                await this.removeById(connection.id, { transaction });
                await this.activitiesService.createUserActivity({
                    type: activities_args_1.ActivityType.UserConnection,
                    schema: create_user_activity_data_dto_1.removeConnectionActivityDataDto,
                    data: {
                        follow: false,
                        profileId,
                        connectionProfileId,
                    },
                    profileId,
                    transaction,
                });
            }
            const oppositeConnection = await this.findOne({
                profileId: connectionProfileId,
                connectionProfileId: profileId,
            }, { transaction });
            if (oppositeConnection) {
                await this.removeById(oppositeConnection.id, { transaction });
                await this.activitiesService.createUserActivity({
                    type: activities_args_1.ActivityType.UserConnection,
                    schema: create_user_activity_data_dto_1.removeConnectionActivityDataDto,
                    data: {
                        follow: false,
                        profileId: connectionProfileId,
                        connectionProfileId: profileId,
                    },
                    profileId: connectionProfileId,
                    transaction,
                });
            }
            const connectionRequests = await this.connectionRequestsService.findAll({
                [sequelize_1.Op.or]: [
                    {
                        senderProfileId: profileId,
                        receiverProfileId: connectionProfileId,
                    },
                    {
                        senderProfileId: connectionProfileId,
                        receiverProfileId: profileId,
                    },
                ],
                status: {
                    [sequelize_1.Op.ne]: connection_request_model_1.ConnectionRequestStatus.Rejected,
                },
            }, { transaction });
            const acceptedConnectionRequest = connectionRequests.find(req => req.status === connection_request_model_1.ConnectionRequestStatus.Accepted);
            if (connectionRequests.length > 0) {
                for (const connectionRequest of connectionRequests) {
                    await this.connectionRequestsService.removeConnectionRequest(connectionRequest.id, { transaction });
                }
            }
            await this.profilesService.removeFromProfileIdArray('profileIdsConnections', profileId, {
                profileId: connectionProfileId,
                transaction,
            });
            await this.profilesService.removeFromProfileIdArray('profileIdsConnections', connectionProfileId, {
                profileId: profileId,
                transaction,
            });
            await this.chatService.freezeChannel(profileId, connectionProfileId);
            if (acceptedConnectionRequest) {
                await this.profilesService.incrementConnectionsCount([profileId, connectionProfileId], -1, transaction);
            }
            await transaction.commit();
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`ConnectionsService.removeConnection`, e.message);
            throw e;
        }
        return true;
    }
    async findConnections(profileId, filter, pagination) {
        this.logger.verbose('ConnectionsService.findConnections', {
            profileId,
            pagination,
        });
        const connectionsPaginationResult = await this.connectionsRepository.findConnections(profileId, filter, pagination);
        return {
            records: connectionsPaginationResult.records,
            totalCount: connectionsPaginationResult.totalCount,
        };
    }
};
exports.ConnectionsService = ConnectionsService;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => connection_requests_service_1.ConnectionRequestsService)),
    __metadata("design:type", connection_requests_service_1.ConnectionRequestsService)
], ConnectionsService.prototype, "connectionRequestsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => chat_service_1.ChatService)),
    __metadata("design:type", chat_service_1.ChatService)
], ConnectionsService.prototype, "chatService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], ConnectionsService.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => getting_started_steps_service_1.GettingStartedStepsService)),
    __metadata("design:type", getting_started_steps_service_1.GettingStartedStepsService)
], ConnectionsService.prototype, "gettingStartedStepsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => connections_repository_1.ConnectionsRepository)),
    __metadata("design:type", connections_repository_1.ConnectionsRepository)
], ConnectionsService.prototype, "connectionsRepository", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => activities_service_1.ActivitiesService)),
    __metadata("design:type", activities_service_1.ActivitiesService)
], ConnectionsService.prototype, "activitiesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => achievements_service_1.AchievementsService)),
    __metadata("design:type", achievements_service_1.AchievementsService)
], ConnectionsService.prototype, "achievementsService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", sequelize_typescript_1.Sequelize)
], ConnectionsService.prototype, "sequelize", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], ConnectionsService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], ConnectionsService.prototype, "errorHelper", void 0);
exports.ConnectionsService = ConnectionsService = __decorate([
    (0, common_1.Injectable)()
], ConnectionsService);
//# sourceMappingURL=connections.service.js.map