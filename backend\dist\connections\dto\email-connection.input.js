"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailConnectionInput = void 0;
const graphql_1 = require("@nestjs/graphql");
const class_validator_1 = require("class-validator");
const connection_args_1 = require("../args/connection.args");
let EmailConnectionInput = class EmailConnectionInput {
};
exports.EmailConnectionInput = EmailConnectionInput;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(),
    (0, class_validator_1.Matches)(/^[\W]*([\w+\-.%]+@[\w\-.]+\.[A-Za-z]{2,24}[\W]*,{1}[\W]*)*([\w+\-.%]+@[\w\-.]+\.[A-Za-z]{2,24})[\W]*$/, {
        message: 'The input should contain valid emails, separated by a coma',
    }),
    __metadata("design:type", String)
], EmailConnectionInput.prototype, "emailAddresses", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(type => graphql_1.Int),
    (0, class_validator_1.IsIn)(Object.keys(connection_args_1.InvitationOptions).map(el => Number(el))),
    __metadata("design:type", Number)
], EmailConnectionInput.prototype, "inputTextChoice", void 0);
exports.EmailConnectionInput = EmailConnectionInput = __decorate([
    (0, graphql_1.InputType)()
], EmailConnectionInput);
//# sourceMappingURL=email-connection.input.js.map