"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginationHelper = void 0;
const pagination_args_1 = require("./../../common/args/pagination.args");
const sequelize_1 = require("sequelize");
class PaginatedResults {
}
class PaginationHelper {
    async getPaginatedResults(options) {
        const { model, pagination, extraQueryParams, includeParams } = options;
        if ((pagination === null || pagination === void 0 ? void 0 : pagination.sortBy) === 'id') {
            throw new Error(`It's not allowed to sort by ID`);
        }
        const orderParams = [];
        let paginationQueryParams = {};
        let totalQueryParams = {};
        if (extraQueryParams) {
            paginationQueryParams = Object.assign(Object.assign({}, paginationQueryParams), extraQueryParams);
            totalQueryParams = Object.assign(Object.assign({}, totalQueryParams), extraQueryParams);
        }
        const limit = (pagination === null || pagination === void 0 ? void 0 : pagination.first) || undefined;
        if (!limit) {
            const totalCount = await model.count({
                where: totalQueryParams,
            });
            return {
                records: [],
                totalCount,
            };
        }
        const sortBy = (pagination === null || pagination === void 0 ? void 0 : pagination.sortBy) || 'createdAt';
        const sortOrder = (pagination === null || pagination === void 0 ? void 0 : pagination.sortOrder) || pagination_args_1.PaginationSortOrder.Ascending;
        orderParams.push([sortBy, sortOrder]);
        if (pagination === null || pagination === void 0 ? void 0 : pagination.after) {
            const afterProfile = await model.findOne({
                where: Object.assign({ profileId: pagination.after }, extraQueryParams),
            });
            if (sortOrder === pagination_args_1.PaginationSortOrder.Ascending) {
                paginationQueryParams[sortBy] = {
                    [sequelize_1.Op.gte]: afterProfile[sortBy],
                };
            }
            else if (sortOrder === pagination_args_1.PaginationSortOrder.Descending) {
                paginationQueryParams[sortBy] = {
                    [sequelize_1.Op.lte]: afterProfile[sortBy],
                };
            }
            paginationQueryParams['profileId'] = {
                [sequelize_1.Op.ne]: pagination.after,
            };
        }
        const records = await model.findAll({
            where: paginationQueryParams,
            attributes: ['id', 'createdAt'],
            include: includeParams,
            order: orderParams,
            limit,
        });
        const totalCount = await model.count({
            where: totalQueryParams,
        });
        return {
            records,
            totalCount,
        };
    }
}
exports.PaginationHelper = PaginationHelper;
//# sourceMappingURL=helper.js.map