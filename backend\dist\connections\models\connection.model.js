"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Connection = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const profile_model_1 = require("../../profiles/models/profile.model");
const short_uuid_1 = __importDefault(require("short-uuid"));
const connection_request_model_1 = require("../../connection-requests/models/connection-request.model");
const graphql_1 = require("@nestjs/graphql");
let Connection = class Connection extends sequelize_typescript_1.Model {
};
exports.Connection = Connection;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID, { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], Connection.prototype, "id", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    (0, sequelize_typescript_1.Column)({ allowNull: false }),
    __metadata("design:type", String)
], Connection.prototype, "profileId", void 0);
__decorate([
    (0, graphql_1.Field)(() => profile_model_1.Profile),
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, 'profileId'),
    __metadata("design:type", profile_model_1.Profile)
], Connection.prototype, "profile", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    (0, sequelize_typescript_1.Column)({ allowNull: false }),
    __metadata("design:type", String)
], Connection.prototype, "connectionProfileId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, 'connectionProfileId'),
    __metadata("design:type", profile_model_1.Profile)
], Connection.prototype, "connectionProfile", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => connection_request_model_1.ConnectionRequest),
    (0, sequelize_typescript_1.Column)({ allowNull: false }),
    __metadata("design:type", String)
], Connection.prototype, "connectionRequestId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => connection_request_model_1.ConnectionRequest, 'connectionRequestId'),
    __metadata("design:type", connection_request_model_1.ConnectionRequest)
], Connection.prototype, "connectionRequest", void 0);
__decorate([
    sequelize_typescript_1.Column,
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Connection.prototype, "streamChannelId", void 0);
__decorate([
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Connection.prototype, "createdAt", void 0);
__decorate([
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Connection.prototype, "updatedAt", void 0);
exports.Connection = Connection = __decorate([
    (0, graphql_1.ObjectType)(),
    sequelize_typescript_1.Table
], Connection);
//# sourceMappingURL=connection.model.js.map