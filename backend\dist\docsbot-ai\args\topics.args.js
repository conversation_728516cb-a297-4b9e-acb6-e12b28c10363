"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SuggestedQuestionsResult = exports.ChatResult = exports.TopicsArgs = exports.TopicsFilter = void 0;
const graphql_1 = require("@nestjs/graphql");
const pagination_args_1 = require("../../common/args/pagination.args");
const graphql_type_json_1 = __importDefault(require("graphql-type-json"));
let TopicsFilter = class TopicsFilter {
};
exports.TopicsFilter = TopicsFilter;
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true, defaultValue: null }),
    __metadata("design:type", Boolean)
], TopicsFilter.prototype, "isPublic", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], TopicsFilter.prototype, "searchText", void 0);
exports.TopicsFilter = TopicsFilter = __decorate([
    (0, graphql_1.InputType)()
], TopicsFilter);
let TopicsArgs = class TopicsArgs extends pagination_args_1.PaginationArgs {
};
exports.TopicsArgs = TopicsArgs;
__decorate([
    (0, graphql_1.Field)(() => TopicsFilter, { nullable: true }),
    __metadata("design:type", TopicsFilter)
], TopicsArgs.prototype, "filter", void 0);
exports.TopicsArgs = TopicsArgs = __decorate([
    (0, graphql_1.ArgsType)()
], TopicsArgs);
let ChatResult = class ChatResult {
};
exports.ChatResult = ChatResult;
__decorate([
    (0, graphql_1.Field)(() => graphql_type_json_1.default, { nullable: true }),
    __metadata("design:type", Object)
], ChatResult.prototype, "response", void 0);
exports.ChatResult = ChatResult = __decorate([
    (0, graphql_1.ObjectType)()
], ChatResult);
let SuggestedQuestionsResult = class SuggestedQuestionsResult {
};
exports.SuggestedQuestionsResult = SuggestedQuestionsResult;
__decorate([
    (0, graphql_1.Field)(() => [String], { nullable: true }),
    __metadata("design:type", Array)
], SuggestedQuestionsResult.prototype, "response", void 0);
exports.SuggestedQuestionsResult = SuggestedQuestionsResult = __decorate([
    (0, graphql_1.ObjectType)()
], SuggestedQuestionsResult);
//# sourceMappingURL=topics.args.js.map