"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocsbotAIRepository = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const topics_model_1 = require("./models/topics.model");
const sequelize_2 = require("sequelize");
const pagination_1 = require("../common/helpers/pagination");
const organisations_service_1 = require("../organisations/organisations.service");
const docsbot_ai_service_1 = require("./docsbot-ai.service");
let DocsbotAIRepository = class DocsbotAIRepository {
    async findTopics(profileId, filter, pagination) {
        const profileMembershipOrgs = await this.docsbotAIService.getProfileMembershipOrganisationIds(profileId);
        const extraQueryParams = {
            [sequelize_2.Op.or]: [
                {
                    isPublic: (filter === null || filter === void 0 ? void 0 : filter.isPublic) === true || (filter === null || filter === void 0 ? void 0 : filter.isPublic) === false
                        ? filter.isPublic
                        : [true, false]
                },
                {
                    [sequelize_2.Op.and]: [
                        {
                            isPublic: false,
                        },
                        {
                            linkedOrganisations: {
                                [sequelize_2.Op.overlap]: profileMembershipOrgs,
                            },
                        },
                    ],
                },
            ],
        };
        if (filter === null || filter === void 0 ? void 0 : filter.searchText) {
            extraQueryParams.fullName = {
                [sequelize_2.Op.iLike]: `%${filter.searchText}%`,
            };
        }
        const results = await new pagination_1.PaginationHelper().getPaginatedResults({
            model: topics_model_1.Topics,
            pagination,
            extraQueryParams,
            excludeIds: [],
            searchText: filter === null || filter === void 0 ? void 0 : filter.searchText,
        });
        const finalArr = [];
        await Promise.all(results.records.map(async (rec) => {
            const orgData = [];
            rec.linkedOrganisations.length !== 0 &&
                (await Promise.all(rec.linkedOrganisations.map(async (org) => {
                    const organisation = await this.organisationsService.findById(org);
                    orgData.push({
                        id: organisation.id,
                        name: organisation.name,
                        image: organisation.image,
                        vanityId: organisation.vanityId,
                    });
                })));
            rec.organisationDetails = orgData;
            finalArr.push(rec);
        }));
        const finalRes = {
            records: finalArr,
            totalCount: results.totalCount,
        };
        return finalRes;
    }
};
exports.DocsbotAIRepository = DocsbotAIRepository;
__decorate([
    (0, sequelize_1.InjectModel)(topics_model_1.Topics),
    __metadata("design:type", Object)
], DocsbotAIRepository.prototype, "topicsModel", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], DocsbotAIRepository.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => docsbot_ai_service_1.DocsbotAIService)),
    __metadata("design:type", docsbot_ai_service_1.DocsbotAIService)
], DocsbotAIRepository.prototype, "docsbotAIService", void 0);
exports.DocsbotAIRepository = DocsbotAIRepository = __decorate([
    (0, common_1.Injectable)()
], DocsbotAIRepository);
//# sourceMappingURL=docsbot-ai.repository.js.map