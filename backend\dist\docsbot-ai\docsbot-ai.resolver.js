"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocsbotAIResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const topics_model_1 = require("./models/topics.model");
const common_1 = require("@nestjs/common");
const docsbot_ai_service_1 = require("./docsbot-ai.service");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const create_topic_dto_1 = require("./dto/create-topic.dto");
const update_topic_input_dto_1 = require("./dto/update-topic-input.dto");
const paginated_result_1 = require("../common/args/paginated-result");
const topics_args_1 = require("./args/topics.args");
const chat_dto_1 = require("./dto/chat.dto");
const rate_answer_dto_1 = require("./dto/rate-answer.dto");
let DocsbotAIResolver = class DocsbotAIResolver {
    async createTopic(user, topicData) {
        this.logger.verbose('DocsbotAIResolver.createTopic (mutation)', {
            user: user.toLogObject(),
            topicData,
        });
        return this.docsbotAIService.createTopic(topicData, {
            profileId: user.profileId,
        });
    }
    async updateTopic(user, topicId, topicData) {
        this.logger.verbose('DocsbotAIResolver.updateTopic (mutation)', {
            user: user.toLogObject(),
            topicData,
        });
        const topic = await this.docsbotAIService.findById(topicId);
        if (!topic) {
            throw new Error('Topic not found');
        }
        return this.docsbotAIService.updateTopic(topicId, topicData, {
            profileId: user.profileId,
        });
    }
    async removeTopic(user, topicId) {
        this.logger.verbose('DocsbotAIResolver.removeTopic (mutation)', {
            user: user.toLogObject(),
            topicId,
        });
        const topic = await this.docsbotAIService.findById(topicId);
        if (!topic)
            return true;
        await this.docsbotAIService.removeTopic(topicId, {
            profileId: user.profileId,
        });
        return true;
    }
    async topics(user, topcicArgs) {
        this.logger.verbose('DocsbotAIResolver.topics (query)', {
            user: user.toLogObject(),
            topcicArgs,
        });
        return await this.docsbotAIService.findTopics(user.profileId, topcicArgs.filter, {
            first: topcicArgs === null || topcicArgs === void 0 ? void 0 : topcicArgs.first,
            after: topcicArgs === null || topcicArgs === void 0 ? void 0 : topcicArgs.after,
            sortBy: topcicArgs === null || topcicArgs === void 0 ? void 0 : topcicArgs.sortBy,
            sortOrder: topcicArgs === null || topcicArgs === void 0 ? void 0 : topcicArgs.sortOrder,
        });
    }
    async chatWithAI(user, chatData) {
        this.logger.verbose('DocsbotAIResolver.chatWithAI (mutation)', {
            user: user.toLogObject(),
            chatData,
        });
        return await this.docsbotAIService.chatWithAI(chatData, {
            profileId: user.profileId,
        });
    }
    async rateDocsbotAnswer(user, rateAnswerData) {
        this.logger.verbose('DocsbotAIResolver.rateDocsbotAnswer (mutation)', {
            rateAnswerData,
            user,
        });
        return await this.docsbotAIService.rateDocsbotAnswer(rateAnswerData);
    }
    async getSuggestedQuestions(user, docsbotId) {
        this.logger.verbose('DocsbotAIResolver.getSuggestedQuestions (mutation)', {
            docsbotId,
            user,
        });
        return await this.docsbotAIService.getSuggestedQuestions(docsbotId);
    }
};
exports.DocsbotAIResolver = DocsbotAIResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => docsbot_ai_service_1.DocsbotAIService)),
    __metadata("design:type", docsbot_ai_service_1.DocsbotAIService)
], DocsbotAIResolver.prototype, "docsbotAIService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], DocsbotAIResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Mutation)(() => topics_model_1.Topics),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('topicData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_topic_dto_1.CreateTopicDto]),
    __metadata("design:returntype", Promise)
], DocsbotAIResolver.prototype, "createTopic", null);
__decorate([
    (0, graphql_1.Mutation)(() => topics_model_1.Topics),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('topicId')),
    __param(2, (0, graphql_1.Args)('topicData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, update_topic_input_dto_1.UpdateTopicInput]),
    __metadata("design:returntype", Promise)
], DocsbotAIResolver.prototype, "updateTopic", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('topicId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], DocsbotAIResolver.prototype, "removeTopic", null);
__decorate([
    (0, graphql_1.Query)(() => paginated_result_1.TopicsResult),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, topics_args_1.TopicsArgs]),
    __metadata("design:returntype", Promise)
], DocsbotAIResolver.prototype, "topics", null);
__decorate([
    (0, graphql_1.Mutation)(() => topics_args_1.ChatResult),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('chatData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, chat_dto_1.ChatDto]),
    __metadata("design:returntype", Promise)
], DocsbotAIResolver.prototype, "chatWithAI", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('rateAnswerData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, rate_answer_dto_1.RateAnswerDto]),
    __metadata("design:returntype", Promise)
], DocsbotAIResolver.prototype, "rateDocsbotAnswer", null);
__decorate([
    (0, graphql_1.Query)(() => topics_args_1.SuggestedQuestionsResult),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('docsbotId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], DocsbotAIResolver.prototype, "getSuggestedQuestions", null);
exports.DocsbotAIResolver = DocsbotAIResolver = __decorate([
    (0, graphql_1.Resolver)(() => topics_model_1.Topics)
], DocsbotAIResolver);
//# sourceMappingURL=docsbot-ai.resolver.js.map