"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocsbotAIService = void 0;
const common_1 = require("@nestjs/common");
const base_service_1 = require("../common/base.service");
const topics_model_1 = require("./models/topics.model");
const sequelize_1 = require("sequelize");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const error_1 = require("../common/helpers/error");
const docsbot_ai_repository_1 = require("./docsbot-ai.repository");
const config_1 = __importDefault(require("../config/config"));
const axios_1 = __importDefault(require("axios"));
const memberships_service_1 = require("../memberships/memberships.service");
const membership_model_1 = require("../memberships/models/membership.model");
const underscore_1 = require("../common/helpers/underscore");
let DocsbotAIService = class DocsbotAIService extends (0, base_service_1.BaseService)(topics_model_1.Topics) {
    async createTopic(createTopicDto, options) {
        this.logger.info('DocsbotAIService.createTopic', {
            profileId: options.profileId,
            createTopicDto,
        });
        const transaction = await this.sequelize.transaction();
        try {
            const topic = await this.create(Object.assign({}, createTopicDto), { transaction });
            await transaction.commit();
            return topic;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`DocsbotAIService.createTopic`, e.message);
        }
    }
    async updateTopic(id, updateTopicInput, options) {
        this.logger.info('DocsbotAIService.updateTopic', {
            profileId: options.profileId,
            id,
            updateTopicInput,
        });
        const transaction = await this.sequelize.transaction();
        try {
            const topic = await this.updateById(id, updateTopicInput, {
                transaction,
            });
            await transaction.commit();
            return topic;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`DocsbotAIService.updateTopic`, e.message);
        }
    }
    async removeTopic(id, options) {
        this.logger.info('DocsbotAIService.removeTopic', {
            id,
            profileId: options.profileId,
        });
        const transaction = await this.sequelize.transaction();
        try {
            await this.removeById(id, {
                transaction,
            });
            await transaction.commit();
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`DocsbotAIService.removeTopic`, e.message);
        }
    }
    async findTopics(profileId, filter, pagination) {
        this.logger.verbose('DocsbotAIService.findTopics', {
            profileId,
            filter,
            pagination,
        });
        const paginationPostResult = await this.docsbotAIRepository.findTopics(profileId, filter, pagination);
        return {
            records: paginationPostResult.records,
            totalCount: paginationPostResult.totalCount,
        };
    }
    async chatWithAI(chatData, options) {
        var _a;
        this.logger.verbose('DocsbotAIService.chatWithAI', {
            profileId: options === null || options === void 0 ? void 0 : options.profileId,
            chatData,
        });
        try {
            const topic = await this.findById(chatData.topicId);
            if (!topic) {
                throw new Error('Topic not found');
            }
            const docsbotTeamId = config_1.default.DOCSBOT_TEAM_ID;
            const url = `https://api.docsbot.ai/teams/${docsbotTeamId}/bots/${topic.docsbotId}/chat`;
            const data = JSON.stringify({
                question: chatData.question,
                full_source: false,
                history: ((_a = chatData === null || chatData === void 0 ? void 0 : chatData.history) === null || _a === void 0 ? void 0 : _a.length) !== 0 ? chatData.history : [],
            });
            const headers = {
                'Content-Type': 'application/json',
            };
            const result = await axios_1.default.post(url, data, { headers });
            return { response: result.data };
        }
        catch (e) {
            this.errorHelper.throwHttpException(`DocsbotAIService.chatWithAI`, e.message);
        }
    }
    async rateDocsbotAnswer(rateAnswerData) {
        this.logger.verbose('DocsbotAIService.rateDocsbotAnswer', {
            rateAnswerData,
        });
        try {
            const docsbotTeamId = config_1.default.DOCSBOT_TEAM_ID;
            const url = `https://api.docsbot.ai/teams/${docsbotTeamId}/bots/${rateAnswerData.docsbotId}/rate/${rateAnswerData.answerId}`;
            const data = JSON.stringify({
                rating: rateAnswerData.rating,
            });
            const headers = {
                'Content-Type': 'application/json',
            };
            const result = await axios_1.default.put(url, data, { headers });
            return true;
        }
        catch (e) {
            this.errorHelper.throwHttpException(`DocsbotAIService.rateDocsbotAnswer`, e.message);
        }
    }
    async getSuggestedQuestions(docsbotId) {
        this.logger.verbose('DocsbotAIService.getSuggestedQuestions', {
            docsbotId,
        });
        try {
            const docsbotTeamId = config_1.default.DOCSBOT_TEAM_ID;
            const docsbotAuthToken = config_1.default.DOCSBOT_API_KEY;
            const url = `https://docsbot.ai/api/teams/${docsbotTeamId}/bots/${docsbotId}`;
            const headers = {
                Authorization: `Bearer ${docsbotAuthToken}`,
            };
            const result = await axios_1.default.get(url, { headers });
            return { response: result.data.questions };
        }
        catch (e) {
            this.errorHelper.throwHttpException(`DocsbotAIService.getSuggestedQuestions`, e.message);
        }
    }
    async getProfileMembershipOrganisationIds(profileId) {
        const memberships = await this.membershipsService.findAll({
            profileId,
            status: membership_model_1.MembershipStatus.Active,
        }, {
            attributes: ['organisationId'],
        });
        return underscore_1.Underscore.map(memberships, 'organisationId');
    }
};
exports.DocsbotAIService = DocsbotAIService;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => docsbot_ai_repository_1.DocsbotAIRepository)),
    __metadata("design:type", docsbot_ai_repository_1.DocsbotAIRepository)
], DocsbotAIService.prototype, "docsbotAIRepository", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], DocsbotAIService.prototype, "membershipsService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", sequelize_1.Sequelize)
], DocsbotAIService.prototype, "sequelize", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], DocsbotAIService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], DocsbotAIService.prototype, "errorHelper", void 0);
exports.DocsbotAIService = DocsbotAIService = __decorate([
    (0, common_1.Injectable)()
], DocsbotAIService);
//# sourceMappingURL=docsbot-ai.service.js.map