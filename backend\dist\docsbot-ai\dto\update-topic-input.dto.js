"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateTopicInput = void 0;
const graphql_1 = require("@nestjs/graphql");
const class_validator_1 = require("class-validator");
let UpdateTopicInput = class UpdateTopicInput {
};
exports.UpdateTopicInput = UpdateTopicInput;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.MaxLength)(255),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], UpdateTopicInput.prototype, "fullName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.MaxLength)(255),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], UpdateTopicInput.prototype, "shortName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.MaxLength)(255),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], UpdateTopicInput.prototype, "docsbotId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)(type => [String], { nullable: true }),
    __metadata("design:type", Array)
], UpdateTopicInput.prototype, "linkedOrganisations", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], UpdateTopicInput.prototype, "isPublic", void 0);
exports.UpdateTopicInput = UpdateTopicInput = __decorate([
    (0, graphql_1.InputType)()
], UpdateTopicInput);
//# sourceMappingURL=update-topic-input.dto.js.map