"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Topics = exports.AccessType = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const short_uuid_1 = __importDefault(require("short-uuid"));
const graphql_1 = require("@nestjs/graphql");
const sequelize_1 = require("sequelize");
var AccessType;
(function (AccessType) {
    AccessType["Public"] = "Public";
    AccessType["Private"] = "Private";
})(AccessType || (exports.AccessType = AccessType = {}));
(0, graphql_1.registerEnumType)(AccessType, {
    name: 'AccessType',
});
let OrganisationDetailData = class OrganisationDetailData {
};
__decorate([
    (0, graphql_1.Field)({ nullable: false }),
    __metadata("design:type", String)
], OrganisationDetailData.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: false }),
    __metadata("design:type", String)
], OrganisationDetailData.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], OrganisationDetailData.prototype, "image", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], OrganisationDetailData.prototype, "vanityId", void 0);
OrganisationDetailData = __decorate([
    (0, graphql_1.ObjectType)()
], OrganisationDetailData);
let Topics = class Topics extends sequelize_typescript_1.Model {
};
exports.Topics = Topics;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], Topics.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: false }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Topics.prototype, "fullName", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: false }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Topics.prototype, "shortName", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: false }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Topics.prototype, "docsbotId", void 0);
__decorate([
    (0, graphql_1.Field)(type => [String], { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], Topics.prototype, "linkedOrganisations", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.BOOLEAN,
        defaultValue: true,
    }),
    __metadata("design:type", Boolean)
], Topics.prototype, "isPublic", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Topics.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Topics.prototype, "updatedAt", void 0);
__decorate([
    (0, graphql_1.Field)(type => [OrganisationDetailData], { nullable: true }),
    __metadata("design:type", Array)
], Topics.prototype, "organisationDetails", void 0);
exports.Topics = Topics = __decorate([
    (0, graphql_1.ObjectType)(),
    sequelize_typescript_1.Table
], Topics);
//# sourceMappingURL=topics.model.js.map