"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailInvitationsModule = void 0;
const email_invitations_service_1 = require("./email-invitations.service");
const sequelize_1 = require("@nestjs/sequelize");
const common_1 = require("@nestjs/common");
const email_invitations_resolver_1 = require("./email-invitations.resolver");
const email_invitations_model_1 = require("./models/email-invitations.model");
let EmailInvitationsModule = class EmailInvitationsModule {
};
exports.EmailInvitationsModule = EmailInvitationsModule;
exports.EmailInvitationsModule = EmailInvitationsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            sequelize_1.SequelizeModule.forFeature([email_invitations_model_1.EmailInvitations]),
        ],
        providers: [email_invitations_resolver_1.EmailInvitationsResolver, email_invitations_service_1.EmailInvitationsService],
        exports: [email_invitations_service_1.EmailInvitationsService]
    })
], EmailInvitationsModule);
//# sourceMappingURL=email-invitations.module.js.map