"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvitationDayLimit = exports.EmailStatus = exports.EmailTemplate = exports.EmailProcessType = void 0;
var EmailProcessType;
(function (EmailProcessType) {
    EmailProcessType["UNREAD_NOTIFICATIONS"] = "UNREAD_NOTIFICATIONS";
    EmailProcessType["UNREAD_MESSAGES"] = "UNREAD_MESSAGES";
})(EmailProcessType || (exports.EmailProcessType = EmailProcessType = {}));
var EmailTemplate;
(function (EmailTemplate) {
    EmailTemplate["NewNotifications"] = "d-675be24ee49e4318ba3dc65294ab424a";
    EmailTemplate["NewMessages"] = "d-1dd7f2d8b615494e9f4ed4485a5e0c78";
    EmailTemplate["NewInvitation"] = "d-d45f8a55149e4fffbb86787f19eccc0e";
    EmailTemplate["SuggestFollow"] = "d-c322269e9d6d4e71906e069f742793f6";
})(EmailTemplate || (exports.EmailTemplate = EmailTemplate = {}));
var EmailStatus;
(function (EmailStatus) {
    EmailStatus["Sent"] = "Sent";
    EmailStatus["Hold"] = "Hold";
})(EmailStatus || (exports.EmailStatus = EmailStatus = {}));
exports.InvitationDayLimit = 1000;
//# sourceMappingURL=email.args.js.map