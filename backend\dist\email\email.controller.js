"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailController = void 0;
const common_1 = require("@nestjs/common");
const email_args_1 = require("./args/email.args");
const gcp_auth_guard_1 = require("../common/guards/gcp-auth.guard");
const email_service_1 = require("./email.service");
const error_1 = require("../common/helpers/error");
const config_1 = __importDefault(require("../config/config"));
let EmailController = class EmailController {
    runJob(type) {
        return this.emailService.handleProcessEmails({
            type: email_args_1.EmailProcessType[type],
        });
    }
    runJobBeta(type) {
        try {
            if (config_1.default.NODE_ENV === 'development' || config_1.default.SERVICE === 'api-beta') {
                return this.emailService.handleProcessEmails({
                    type: email_args_1.EmailProcessType[type],
                });
            }
            else {
                throw new Error('only beta env allowed');
            }
        }
        catch (err) {
            this.errorHelper.throwHttpException(`SuggestFollowService.handleProcessSuggestFollows`, err.message);
        }
    }
};
exports.EmailController = EmailController;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => email_service_1.EmailService)),
    __metadata("design:type", email_service_1.EmailService)
], EmailController.prototype, "emailService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], EmailController.prototype, "errorHelper", void 0);
__decorate([
    (0, common_1.UseGuards)(gcp_auth_guard_1.GcpAuthGuard),
    (0, common_1.Get)('run-job/:EmailProcessType'),
    __param(0, (0, common_1.Param)('EmailProcessType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EmailController.prototype, "runJob", null);
__decorate([
    (0, common_1.Get)('run-beta/:EmailProcessType'),
    __param(0, (0, common_1.Param)('EmailProcessType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EmailController.prototype, "runJobBeta", null);
exports.EmailController = EmailController = __decorate([
    (0, common_1.Controller)('email')
], EmailController);
//# sourceMappingURL=email.controller.js.map