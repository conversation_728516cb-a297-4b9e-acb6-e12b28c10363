"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailService = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("sequelize");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const activities_service_1 = require("../activities/activities.service");
const activities_args_1 = require("../activities/args/activities.args");
const create_user_activity_data_dto_1 = require("../activities/dto/create-user-activity-data.dto");
const base_service_1 = require("../common/base.service");
const error_1 = require("../common/helpers/error");
const connection_args_1 = require("../connections/args/connection.args");
const profiles_service_1 = require("../profiles/profiles.service");
const email_args_1 = require("./args/email.args");
const email_service_helper_1 = require("./helpers/email.service.helper");
const email_model_1 = require("./models/email.model");
let EmailService = class EmailService extends (0, base_service_1.BaseService)(email_model_1.Email) {
    async handleProcessEmails(options) {
        switch (options.type) {
            case email_args_1.EmailProcessType.UNREAD_NOTIFICATIONS:
                await this.emailServiceHelper.processNotificationEmails();
                break;
            case email_args_1.EmailProcessType.UNREAD_MESSAGES:
                await this.emailServiceHelper.processUnreadMessagesEmails();
                break;
            default:
                break;
        }
    }
    async exceedDayLimit(profileId) {
        const count = await this.count({
            [sequelize_1.Op.and]: [
                sequelize_1.Sequelize.where(sequelize_1.Sequelize.fn('DATE', sequelize_1.Sequelize.col('createdAt')), sequelize_1.Sequelize.literal('CURRENT_DATE')),
                {
                    profileId,
                    status: email_args_1.EmailStatus.Sent,
                    templateId: email_args_1.EmailTemplate.NewInvitation,
                },
            ],
        });
        return count;
    }
    async sendInvitations(profileId, { inputTextChoice, emailAddresses }) {
        const text = connection_args_1.InvitationOptions[inputTextChoice];
        const emails = emailAddresses
            .split(',')
            .map((email) => email.trim());
        const emailsWithDuplicatesAndEmptyRemoved = Array.from(new Set(emails)).filter(item => item);
        const emailSentTodayCount = await this.exceedDayLimit(profileId);
        if (emailSentTodayCount + emailsWithDuplicatesAndEmptyRemoved.length >
            email_args_1.InvitationDayLimit) {
            this.errorHelper.throwHttpException(`ConnectionsResolver.emailConnection`, 'Exceeding maximum email daily limit');
        }
        const profile = await this.profilesService.findById(profileId);
        await this.emailServiceHelper.sendConnectInvitations({
            name: profile.name,
            profileId: profileId,
        }, text, emailsWithDuplicatesAndEmptyRemoved);
        for (const email of emailsWithDuplicatesAndEmptyRemoved) {
            const previousActivity = await this.activitiesService.findOne({
                type: activities_args_1.ActivityType.InviteEmailContact,
                profileId,
                data: {
                    [sequelize_1.Op.contains]: {
                        email,
                    },
                },
            });
            await this.activitiesService.createUserActivity({
                type: activities_args_1.ActivityType.InviteEmailContact,
                schema: create_user_activity_data_dto_1.inviteEmailContactActivityDataDto,
                data: {
                    email,
                    profileId,
                    text,
                },
                profileId,
                addLoyaltyPoints: !previousActivity,
                placeholders: {
                    id: profileId,
                    userId: profileId,
                    email: email,
                },
            });
        }
        return true;
    }
    async sendSuggestFollowEmail(mailData) {
        if (!mailData.orgs.length)
            return;
        await this.emailServiceHelper.sendEmail({
            profileId: mailData.profileId,
            to: mailData.email,
            templateId: email_args_1.EmailTemplate.SuggestFollow,
            customFromName: 'Hablo Team',
            customFromEmail: '<EMAIL>',
            dynamicTemplateData: {
                orgname1: mailData.orgs[0].name,
                orgstext: `${mailData.orgs[0].name}${mailData.orgs[1] ? ', ' + mailData.orgs[1].name : ''}${mailData.orgs[2] ? ', ' + mailData.orgs[2].name : ''} and others`,
                firstName: mailData.firstName,
                surname: mailData.surname,
                text: mailData.orgs.map(el => el.name).join('<br>'),
            },
        });
        return true;
    }
};
exports.EmailService = EmailService;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => email_service_helper_1.EmailServiceHelper)),
    __metadata("design:type", email_service_helper_1.EmailServiceHelper)
], EmailService.prototype, "emailServiceHelper", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], EmailService.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => activities_service_1.ActivitiesService)),
    __metadata("design:type", activities_service_1.ActivitiesService)
], EmailService.prototype, "activitiesService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], EmailService.prototype, "errorHelper", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], EmailService.prototype, "logger", void 0);
exports.EmailService = EmailService = __decorate([
    (0, common_1.Injectable)()
], EmailService);
//# sourceMappingURL=email.service.js.map