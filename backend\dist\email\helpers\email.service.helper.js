"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailServiceHelper = void 0;
const email_invitations_service_1 = require("./../../email-invitations/email-invitations.service");
const moment_1 = __importDefault(require("moment"));
const sequelize_1 = require("sequelize");
const common_1 = require("@nestjs/common");
const nestjs_sendgrid_1 = require("@ntegral/nestjs-sendgrid");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const profiles_service_1 = require("../../profiles/profiles.service");
const email_args_1 = require("../args/email.args");
const chat_service_1 = require("../../chat/chat.service");
const connections_service_1 = require("../../connections/connections.service");
const profile_model_1 = require("../../profiles/models/profile.model");
const config_1 = __importDefault(require("../../config/config"));
const email_service_1 = require("../email.service");
const notifications_args_1 = require("../../notifications/args/notifications.args");
const webinar_participant_model_1 = require("../../webinar-participants/models/webinar-participant.model");
const webinar_model_1 = require("../../webinars/models/webinar.model");
const organisation_model_1 = require("../../organisations/models/organisation.model");
const partnership_request_model_1 = require("../../partnership-requests/models/partnership-request.model");
const membership_model_1 = require("../../memberships/models/membership.model");
const follower_model_1 = require("../../followers/models/follower.model");
const event_invitation_model_1 = require("../../event-invitations/models/event-invitation.model");
const event_model_1 = require("../../events/models/event.model");
const incentive_participant_model_1 = require("../../incentive-participants/models/incentive-participant.model");
const incentive_model_1 = require("../../incentives/models/incentive.model");
const notificationLayoutHelper_1 = require("./notificationLayoutHelper");
const notifications_service_1 = require("../../notifications/notifications.service");
const organisations_service_1 = require("../../organisations/organisations.service");
let EmailServiceHelper = class EmailServiceHelper {
    getNotificationEmailHTML(notifications) {
        const notificationHTML = notifications
            .sort((a, b) => {
            return ((0, moment_1.default)(b.createdAt).toDate().getTime() -
                (0, moment_1.default)(a.createdAt).toDate().getTime());
        })
            .slice(0, 5)
            .map(el => {
            const notifData = notificationLayoutHelper_1.NotificationHelper.getConfiguration(el);
            const layout = notificationLayoutHelper_1.NotificationHelper.getNotificationLayout(Object.assign(Object.assign({}, notifData), { createdAt: el.createdAt }));
            return layout;
        })
            .join('');
        return `
      <div style="display: flex; width: 100%;">
        <table style="max-width: 400px; margin: auto;">
          <tbody>
            ${notificationHTML}
          </tbody>
        </table>
      </div>
      `;
    }
    getSubject(notifications) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, _16, _17, _18, _19, _20, _21, _22, _23, _24, _25, _26, _27, _28, _29, _30, _31, _32, _33, _34, _35, _36, _37, _38, _39, _40, _41, _42, _43, _44, _45, _46, _47, _48, _49, _50, _51, _52, _53, _54, _55, _56, _57, _58, _59, _60, _61, _62, _63, _64, _65, _66, _67, _68, _69, _70, _71, _72, _73, _74, _75, _76, _77, _78, _79, _80, _81, _82, _83, _84, _85, _86, _87, _88, _89, _90, _91, _92, _93, _94, _95, _96, _97, _98, _99, _100, _101, _102, _103, _104, _105, _106, _107, _108, _109, _110, _111, _112, _113, _114, _115, _116, _117, _118, _119, _120, _121, _122, _123, _124, _125, _126, _127, _128, _129, _130, _131, _132, _133, _134, _135, _136, _137, _138, _139, _140, _141, _142, _143, _144, _145, _146, _147, _148, _149, _150, _151, _152, _153, _154, _155;
        const notification = notifications.sort((a, b) => {
            return ((0, moment_1.default)(b.createdAt).toDate().getTime() -
                (0, moment_1.default)(a.createdAt).toDate().getTime());
        })[0];
        switch (notification.type) {
            case notifications_args_1.NotificationType.InvitationAccepted: {
                return `You are now connected with ${(_a = notification.profile) === null || _a === void 0 ? void 0 : _a.name}`;
            }
            case notifications_args_1.NotificationType.InvitationReceived: {
                return `${(_b = notification.profile) === null || _b === void 0 ? void 0 : _b.name} wants to connect with you`;
            }
            case notifications_args_1.NotificationType.OrganisationOwnershipRequested: {
                return `${(_c = notification.profile) === null || _c === void 0 ? void 0 : _c.name} requested you take over as Owner of ${(_e = (_d = notification.membership) === null || _d === void 0 ? void 0 : _d.organisation) === null || _e === void 0 ? void 0 : _e.name}`;
            }
            case notifications_args_1.NotificationType.OrganisationOwnershipAccepted: {
                return `${(_f = notification.profile) === null || _f === void 0 ? void 0 : _f.name} agreed to become Owner of ${(_h = (_g = notification.membership) === null || _g === void 0 ? void 0 : _g.organisation) === null || _h === void 0 ? void 0 : _h.name}`;
            }
            case notifications_args_1.NotificationType.PartnershipRequestReceived: {
                return `${(_k = (_j = notification.partnershipRequest) === null || _j === void 0 ? void 0 : _j.senderOrganisation) === null || _k === void 0 ? void 0 : _k.name} requested to become a Connected Organisation with ${(_m = (_l = notification.partnershipRequest) === null || _l === void 0 ? void 0 : _l.receiverOrganisation) === null || _m === void 0 ? void 0 : _m.name}`;
            }
            case notifications_args_1.NotificationType.PartnershipRequestApproved: {
                return `${(_p = (_o = notification.partnershipRequest) === null || _o === void 0 ? void 0 : _o.receiverOrganisation) === null || _p === void 0 ? void 0 : _p.name} approved a connection request from ${(_r = (_q = notification.partnershipRequest) === null || _q === void 0 ? void 0 : _q.senderOrganisation) === null || _r === void 0 ? void 0 : _r.name}`;
            }
            case notifications_args_1.NotificationType.PartnershipRequestApprovedByOrganisation: {
                return `${(_t = (_s = notification.partnershipRequest) === null || _s === void 0 ? void 0 : _s.senderOrganisation) === null || _t === void 0 ? void 0 : _t.name} is now a Connected Organisation with ${(_v = (_u = notification.partnershipRequest) === null || _u === void 0 ? void 0 : _u.receiverOrganisation) === null || _v === void 0 ? void 0 : _v.name}`;
            }
            case notifications_args_1.NotificationType.MembershipRequested: {
                return `${(_w = notification.profile) === null || _w === void 0 ? void 0 : _w.name} requested to join ${(_y = (_x = notification.membership) === null || _x === void 0 ? void 0 : _x.organisation) === null || _y === void 0 ? void 0 : _y.name}`;
            }
            case notifications_args_1.NotificationType.MembershipAccepted: {
                return `You've been verified as an employee of ${(_0 = (_z = notification.membership) === null || _z === void 0 ? void 0 : _z.organisation) === null || _0 === void 0 ? void 0 : _0.name}`;
            }
            case notifications_args_1.NotificationType.FollowerAccepted: {
                return `${(_2 = (_1 = notification.follower) === null || _1 === void 0 ? void 0 : _1.organisation) === null || _2 === void 0 ? void 0 : _2.name} accepted your follow request`;
            }
            case notifications_args_1.NotificationType.MembershipPermissionsUpdated: {
                return `You've been assigned as ${(((_3 = notification === null || notification === void 0 ? void 0 : notification.data) === null || _3 === void 0 ? void 0 : _3.permissions) ||
                    ((_4 = notification.membership) === null || _4 === void 0 ? void 0 : _4.permissions) ||
                    []).join(', ')} of ${(_6 = (_5 = notification.membership) === null || _5 === void 0 ? void 0 : _5.organisation) === null || _6 === void 0 ? void 0 : _6.name}`;
            }
            case notifications_args_1.NotificationType.EventInvitationByGuest: {
                return `${(_8 = (_7 = notification.eventInvitation) === null || _7 === void 0 ? void 0 : _7.inviterProfile) === null || _8 === void 0 ? void 0 : _8.name} invited you to an event: ${(_10 = (_9 = notification.eventInvitation) === null || _9 === void 0 ? void 0 : _9.event) === null || _10 === void 0 ? void 0 : _10.name}`;
            }
            case notifications_args_1.NotificationType.EventInvitationByHosts: {
                return `${(_13 = (_12 = (_11 = notification.eventInvitation) === null || _11 === void 0 ? void 0 : _11.event) === null || _12 === void 0 ? void 0 : _12.organisation) === null || _13 === void 0 ? void 0 : _13.name} invited you to an event: ${(_15 = (_14 = notification.eventInvitation) === null || _14 === void 0 ? void 0 : _14.event) === null || _15 === void 0 ? void 0 : _15.name}`;
            }
            case notifications_args_1.NotificationType.EventInvitationApproved: {
                return `${(_18 = (_17 = (_16 = notification.eventInvitation) === null || _16 === void 0 ? void 0 : _16.event) === null || _17 === void 0 ? void 0 : _17.organisation) === null || _18 === void 0 ? void 0 : _18.name} accepted your request to attend ${(_20 = (_19 = notification.eventInvitation) === null || _19 === void 0 ? void 0 : _19.event) === null || _20 === void 0 ? void 0 : _20.name}`;
            }
            case notifications_args_1.NotificationType.NewEventUpdate: {
                return `${(_23 = (_22 = (_21 = notification.eventInvitation) === null || _21 === void 0 ? void 0 : _21.event) === null || _22 === void 0 ? void 0 : _22.organisation) === null || _23 === void 0 ? void 0 : _23.name} posted an update in ${(_25 = (_24 = notification.eventInvitation) === null || _24 === void 0 ? void 0 : _24.event) === null || _25 === void 0 ? void 0 : _25.name}`;
            }
            case notifications_args_1.NotificationType.EventLocationChanged:
                return `${(_28 = (_27 = (_26 = notification.eventInvitation) === null || _26 === void 0 ? void 0 : _26.event) === null || _27 === void 0 ? void 0 : _27.organisation) === null || _28 === void 0 ? void 0 : _28.name} updated the location of ${(_30 = (_29 = notification.eventInvitation) === null || _29 === void 0 ? void 0 : _29.event) === null || _30 === void 0 ? void 0 : _30.name}`;
            case notifications_args_1.NotificationType.EventDateTimeChanged:
                return `${(_33 = (_32 = (_31 = notification.eventInvitation) === null || _31 === void 0 ? void 0 : _31.event) === null || _32 === void 0 ? void 0 : _32.organisation) === null || _33 === void 0 ? void 0 : _33.name} made a change to the date / time of ${(_35 = (_34 = notification.eventInvitation) === null || _34 === void 0 ? void 0 : _34.event) === null || _35 === void 0 ? void 0 : _35.name}`;
            case notifications_args_1.NotificationType.IncentiveDateChanged:
                return `${(_38 = (_37 = (_36 = notification.incentiveParticipant) === null || _36 === void 0 ? void 0 : _36.incentive) === null || _37 === void 0 ? void 0 : _37.organisation) === null || _38 === void 0 ? void 0 : _38.name} made a change to the dates of ${(_40 = (_39 = notification.incentiveParticipant) === null || _39 === void 0 ? void 0 : _39.incentive) === null || _40 === void 0 ? void 0 : _40.name}`;
            case notifications_args_1.NotificationType.IncentiveInvitationByParticipant:
                return `${(_42 = (_41 = notification.incentiveParticipant) === null || _41 === void 0 ? void 0 : _41.inviterProfile) === null || _42 === void 0 ? void 0 : _42.name} invited you to an incentive: ${(_44 = (_43 = notification.incentiveParticipant) === null || _43 === void 0 ? void 0 : _43.incentive) === null || _44 === void 0 ? void 0 : _44.name}`;
            case notifications_args_1.NotificationType.IncentiveInvitationByHosts:
                return `${(_47 = (_46 = (_45 = notification.incentiveParticipant) === null || _45 === void 0 ? void 0 : _45.incentive) === null || _46 === void 0 ? void 0 : _46.organisation) === null || _47 === void 0 ? void 0 : _47.name} invited you to an incentive: ${(_49 = (_48 = notification.incentiveParticipant) === null || _48 === void 0 ? void 0 : _48.incentive) === null || _49 === void 0 ? void 0 : _49.name}`;
            case notifications_args_1.NotificationType.IncentiveRegistrationRequested:
                return `${(_51 = (_50 = notification.incentiveParticipant) === null || _50 === void 0 ? void 0 : _50.profile) === null || _51 === void 0 ? void 0 : _51.name} requested to register for ${(_53 = (_52 = notification.incentiveParticipant) === null || _52 === void 0 ? void 0 : _52.incentive) === null || _53 === void 0 ? void 0 : _53.name}`;
            case notifications_args_1.NotificationType.IncentiveRegistrationApproved:
                return `${(_56 = (_55 = (_54 = notification.incentiveParticipant) === null || _54 === void 0 ? void 0 : _54.incentive) === null || _55 === void 0 ? void 0 : _55.organisation) === null || _56 === void 0 ? void 0 : _56.name} accepted your request to register for ${(_58 = (_57 = notification.incentiveParticipant) === null || _57 === void 0 ? void 0 : _57.incentive) === null || _58 === void 0 ? void 0 : _58.name}`;
            case notifications_args_1.NotificationType.NewIncentiveUpdate: {
                return `${(_61 = (_60 = (_59 = notification.incentiveParticipant) === null || _59 === void 0 ? void 0 : _59.incentive) === null || _60 === void 0 ? void 0 : _60.organisation) === null || _61 === void 0 ? void 0 : _61.name} posted an update in ${(_63 = (_62 = notification.incentiveParticipant) === null || _62 === void 0 ? void 0 : _62.incentive) === null || _63 === void 0 ? void 0 : _63.name}`;
            }
            case notifications_args_1.NotificationType.WebinarDateChanged:
                return `${(_66 = (_65 = (_64 = notification.webinarParticipant) === null || _64 === void 0 ? void 0 : _64.webinar) === null || _65 === void 0 ? void 0 : _65.organisation) === null || _66 === void 0 ? void 0 : _66.name} made a change to the date / time of ${(_68 = (_67 = notification.webinarParticipant) === null || _67 === void 0 ? void 0 : _67.webinar) === null || _68 === void 0 ? void 0 : _68.name}`;
            case notifications_args_1.NotificationType.WebinarInvitationByParticipant:
                return `${(_70 = (_69 = notification.webinarParticipant) === null || _69 === void 0 ? void 0 : _69.inviterProfile) === null || _70 === void 0 ? void 0 : _70.name} invited you to a webinar: ${(_72 = (_71 = notification.webinarParticipant) === null || _71 === void 0 ? void 0 : _71.webinar) === null || _72 === void 0 ? void 0 : _72.name}`;
            case notifications_args_1.NotificationType.WebinarInvitationByHosts:
                return `${(_75 = (_74 = (_73 = notification.webinarParticipant) === null || _73 === void 0 ? void 0 : _73.webinar) === null || _74 === void 0 ? void 0 : _74.organisation) === null || _75 === void 0 ? void 0 : _75.name} invited you to a webinar: ${(_77 = (_76 = notification.webinarParticipant) === null || _76 === void 0 ? void 0 : _76.webinar) === null || _77 === void 0 ? void 0 : _77.name}`;
            case notifications_args_1.NotificationType.WebinarRegistrationRequested:
                return `${(_79 = (_78 = notification.webinarParticipant) === null || _78 === void 0 ? void 0 : _78.profile) === null || _79 === void 0 ? void 0 : _79.name} requested to register for ${(_81 = (_80 = notification.webinarParticipant) === null || _80 === void 0 ? void 0 : _80.webinar) === null || _81 === void 0 ? void 0 : _81.name}`;
            case notifications_args_1.NotificationType.WebinarRegistrationApproved:
                return `${(_84 = (_83 = (_82 = notification.webinarParticipant) === null || _82 === void 0 ? void 0 : _82.webinar) === null || _83 === void 0 ? void 0 : _83.organisation) === null || _84 === void 0 ? void 0 : _84.name} accepted your request to register for ${(_86 = (_85 = notification.webinarParticipant) === null || _85 === void 0 ? void 0 : _85.webinar) === null || _86 === void 0 ? void 0 : _86.name}`;
            case notifications_args_1.NotificationType.NewWebinarUpdate: {
                return `${(_89 = (_88 = (_87 = notification.webinarParticipant) === null || _87 === void 0 ? void 0 : _87.webinar) === null || _88 === void 0 ? void 0 : _88.organisation) === null || _89 === void 0 ? void 0 : _89.name} posted an update in ${(_91 = (_90 = notification.webinarParticipant) === null || _90 === void 0 ? void 0 : _90.webinar) === null || _91 === void 0 ? void 0 : _91.name}`;
            }
            case notifications_args_1.NotificationType.WebinarParticipantAddedAsHostAdmin: {
                return `${(_94 = (_93 = (_92 = notification.webinarParticipant) === null || _92 === void 0 ? void 0 : _92.webinar) === null || _93 === void 0 ? void 0 : _93.organisation) === null || _94 === void 0 ? void 0 : _94.name} assigned you as a Host Admin of ${(_96 = (_95 = notification.webinarParticipant) === null || _95 === void 0 ? void 0 : _95.webinar) === null || _96 === void 0 ? void 0 : _96.name}`;
            }
            case notifications_args_1.NotificationType.WebinarParticipantAddedAsHost: {
                return `${(_99 = (_98 = (_97 = notification.webinarParticipant) === null || _97 === void 0 ? void 0 : _97.webinar) === null || _98 === void 0 ? void 0 : _98.organisation) === null || _99 === void 0 ? void 0 : _99.name} assigned you as a Host of ${(_101 = (_100 = notification.webinarParticipant) === null || _100 === void 0 ? void 0 : _100.webinar) === null || _101 === void 0 ? void 0 : _101.name}`;
            }
            case notifications_args_1.NotificationType.WebinarParticipantAddedAsSpeaker: {
                return `${(_104 = (_103 = (_102 = notification.webinarParticipant) === null || _102 === void 0 ? void 0 : _102.webinar) === null || _103 === void 0 ? void 0 : _103.organisation) === null || _104 === void 0 ? void 0 : _104.name} assigned you as a Speaker at ${(_106 = (_105 = notification.webinarParticipant) === null || _105 === void 0 ? void 0 : _105.webinar) === null || _106 === void 0 ? void 0 : _106.name}`;
            }
            case notifications_args_1.NotificationType.SuggestFollow: {
                return `You've had a suggestion to follow some new organisations that travel industry professionals similar to you also follow`;
            }
            case notifications_args_1.NotificationType.PostComment: {
                const countOfUsers = (_108 = (_107 = notification.data) === null || _107 === void 0 ? void 0 : _107.users) === null || _108 === void 0 ? void 0 : _108.length;
                return `${(_111 = (_110 = (_109 = notification.data) === null || _109 === void 0 ? void 0 : _109.users) === null || _110 === void 0 ? void 0 : _110[0]) === null || _111 === void 0 ? void 0 : _111.name} ${countOfUsers === 1 ? '' : ' & others'} commented on your post`;
            }
            case notifications_args_1.NotificationType.CommentReact: {
                return `${((_113 = (_112 = notification.data) === null || _112 === void 0 ? void 0 : _112.users) === null || _113 === void 0 ? void 0 : _113.length) && notification.data.users[0].name} reacted to your comment on ${(_115 = (_114 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _114 === void 0 ? void 0 : _114.name) !== null && _115 !== void 0 ? _115 : ''}'s post`;
            }
            case notifications_args_1.NotificationType.PostShared: {
                return `${(_116 = notification.organisation) === null || _116 === void 0 ? void 0 : _116.name} reshared your post with their network`;
            }
            case notifications_args_1.NotificationType.CommentMention: {
                return `${(_117 = notification.profile) === null || _117 === void 0 ? void 0 : _117.name} mentioned you in a comment`;
            }
            case notifications_args_1.NotificationType.PostMention: {
                return `${(_118 = notification.organisation) === null || _118 === void 0 ? void 0 : _118.name} mentioned you in a post`;
            }
            case notifications_args_1.NotificationType.OrgPostMention: {
                return `${(_119 = notification.organisation) === null || _119 === void 0 ? void 0 : _119.name} mentioned ${(_121 = (_120 = notification === null || notification === void 0 ? void 0 : notification.data) === null || _120 === void 0 ? void 0 : _120.organisation) === null || _121 === void 0 ? void 0 : _121.name} in a post`;
            }
            case notifications_args_1.NotificationType.OrgCommentMention: {
                return `${(_122 = notification.profile) === null || _122 === void 0 ? void 0 : _122.name} mentioned ${(_124 = (_123 = notification === null || notification === void 0 ? void 0 : notification.data) === null || _123 === void 0 ? void 0 : _123.organisation) === null || _124 === void 0 ? void 0 : _124.name} in a comment`;
            }
            case notifications_args_1.NotificationType.NewFollower: {
                const countOfUsers = (_126 = (_125 = notification.data) === null || _125 === void 0 ? void 0 : _125.users) === null || _126 === void 0 ? void 0 : _126.length;
                return `${(_129 = (_128 = (_127 = notification.data) === null || _127 === void 0 ? void 0 : _127.users) === null || _128 === void 0 ? void 0 : _128[0]) === null || _129 === void 0 ? void 0 : _129.name} ${countOfUsers === 1 ? '' : ' & others'} requested to follow ${(_130 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _130 === void 0 ? void 0 : _130.name}`;
            }
            case notifications_args_1.NotificationType.NewFollower: {
                const usersFullNames = (_132 = (_131 = notification.data) === null || _131 === void 0 ? void 0 : _131.users) === null || _132 === void 0 ? void 0 : _132.map(({ name }) => name);
                const countOfUsers = (_134 = (_133 = notification.data) === null || _133 === void 0 ? void 0 : _133.users) === null || _134 === void 0 ? void 0 : _134.length;
                const listOfNames = usersFullNames[0] + countOfUsers > 1 ? `` : ` & others`;
                return ` ${listOfNames} requested to follow ${(_135 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _135 === void 0 ? void 0 : _135.name}`;
            }
            case notifications_args_1.NotificationType.RecurringPaymentReminder: {
                return `Your paid subscription with ${(_137 = (_136 = notification.partnershipRequest) === null || _136 === void 0 ? void 0 : _136.senderOrganisation) === null || _137 === void 0 ? void 0 : _137.name} will renew soon`;
            }
            case notifications_args_1.NotificationType.MembershipAutoApprove: {
                return `${(_138 = notification.profile) === null || _138 === void 0 ? void 0 : _138.name} joined ${(_140 = (_139 = notification.membership) === null || _139 === void 0 ? void 0 : _139.organisation) === null || _140 === void 0 ? void 0 : _140.name} using a signup invite link`;
            }
            case notifications_args_1.NotificationType.ParentMembershipRequested: {
                return `${(_141 = notification.profile) === null || _141 === void 0 ? void 0 : _141.name} requested to become a member of ${(_143 = (_142 = notification.membership) === null || _142 === void 0 ? void 0 : _142.organisation) === null || _143 === void 0 ? void 0 : _143.name}`;
            }
            case notifications_args_1.NotificationType.ParentMembershipAccepted: {
                return `You have been verified as a member of ${(_145 = (_144 = notification.membership) === null || _144 === void 0 ? void 0 : _144.organisation) === null || _145 === void 0 ? void 0 : _145.name}`;
            }
            case notifications_args_1.NotificationType.ParentMembershipDeclined: {
                return `You have been declined as a member of ${(_146 = notification.organisation) === null || _146 === void 0 ? void 0 : _146.name}`;
            }
            case notifications_args_1.NotificationType.OrgPostReminder7Days: {
                return `It's been a week since ${(_147 = notification.organisation) === null || _147 === void 0 ? void 0 : _147.name}'s last Hablo post. Keep your community engaged`;
            }
            case notifications_args_1.NotificationType.OrgPostReminder14Days: {
                return `${(_148 = notification.organisation) === null || _148 === void 0 ? void 0 : _148.name}'s community on Hablo is waiting to hear from you`;
            }
            case notifications_args_1.NotificationType.InactiveUserReminder: {
                const organisationNames = (_149 = notification.data) === null || _149 === void 0 ? void 0 : _149.map(({ organisationName }) => organisationName);
                const countOfOrganisations = organisationNames.length;
                const listOfNames = countOfOrganisations === 3
                    ? organisationNames[0] +
                        ', ' +
                        organisationNames[1] +
                        ' and ' +
                        organisationNames[2]
                    : countOfOrganisations === 2
                        ? organisationNames[0] + ' and ' + organisationNames[1]
                        : countOfOrganisations === 1
                            ? organisationNames[0]
                            : organisationNames[0];
                return `${listOfNames} shared new posts that you’ve missed. Login to view latest updates in your feed.`;
            }
            case notifications_args_1.NotificationType.InboxMessage: {
                return `You have a new message from ${(_150 = notification.profile) === null || _150 === void 0 ? void 0 : _150.name}`;
            }
            case notifications_args_1.NotificationType.DailyLoginStreak: {
                return `You've hit a new daily streak!`;
            }
            case notifications_args_1.NotificationType.NewAchievement: {
                return `You've unlocked a new Achievement!`;
            }
            case notifications_args_1.NotificationType.NewTier: {
                return `Your Club Hablo tier has changed!`;
            }
            case notifications_args_1.NotificationType.NewOrganisationAchievement: {
                return `You've unlocked a new Organisation Achievement!`;
            }
            case notifications_args_1.NotificationType.HighFiveAchievement: {
                return `You received a High Five!`;
            }
            case notifications_args_1.NotificationType.PostReact: {
                return 'Someone reacted to your Post';
            }
            case notifications_args_1.NotificationType.WeeklySummary: {
                return 'Here`s how many points you earned this week';
            }
            case notifications_args_1.NotificationType.OrgPartnerRequestReceived:
                return `${(_151 = notification.data) === null || _151 === void 0 ? void 0 : _151.senderOrgName} has requested to become a Connected Organisation with ${(_152 = notification.data) === null || _152 === void 0 ? void 0 : _152.receiverOrgName}`;
            case notifications_args_1.NotificationType.OrgPartnerAcceptedSender:
                return `${(_153 = notification.data) === null || _153 === void 0 ? void 0 : _153.receiverOrgName} has approved your request to become a Connected Organisation`;
            case notifications_args_1.NotificationType.OrgPartnerAcceptedReceiver:
                return `${(_154 = notification.data) === null || _154 === void 0 ? void 0 : _154.senderOrgName} is now a Connected Organisation with ${(_155 = notification.data) === null || _155 === void 0 ? void 0 : _155.receiverOrgName}`;
            default:
                return `You have new unread notifications`;
        }
    }
    async processNotificationEmails() {
        const timezoneOffsets = [];
        for (let i = -12; i <= 13; i += 0.5) {
            timezoneOffsets.push(i * 60);
        }
        const utcNow = new Date();
        utcNow.setTime(utcNow.getTime() + utcNow.getTimezoneOffset() * 60000);
        timezoneOffsets.forEach(async (tzOffset) => {
            const tzNow = new Date();
            tzNow.setTime(utcNow.getTime() - tzOffset * 60000);
            const tzHours = tzNow.getHours();
            const tzMinutes = tzNow.getMinutes();
            if (tzHours === 9 && tzMinutes >= 30 && tzMinutes < 59) {
                this.logger.info('EmailService.sendEveryDay0930AMEmails');
                await this.sendEveryDay0930AMEmails(tzOffset);
            }
            if (tzHours === 12 && tzMinutes >= 30 && tzMinutes < 59) {
                this.logger.info('EmailService.sendEveryDay1230PMEmails');
                await this.sendEveryDay1230PMEmails(tzOffset);
            }
            if (tzHours === 15 && tzMinutes >= 30 && tzMinutes < 59) {
                this.logger.info('EmailService.sendEveryDay1530PMEmails');
                await this.sendEveryDay1530PMEmails(tzOffset);
            }
        });
    }
    async processUnreadMessagesEmails() {
        const timezoneOffsets = [];
        for (let i = -12; i <= 13; i += 0.5) {
            timezoneOffsets.push(i * 60);
        }
        const utcNow = new Date();
        utcNow.setTime(utcNow.getTime() + utcNow.getTimezoneOffset() * 60000);
        const tzOffsets = [];
        timezoneOffsets.forEach(async (tzOffset) => {
            const tzNow = new Date();
            tzNow.setTime(utcNow.getTime() - tzOffset * 60000);
            const tzHours = tzNow.getHours();
            const tzMinutes = tzNow.getMinutes();
            if (tzHours >= 8 && tzHours < 20) {
                tzOffsets.push(tzOffset);
            }
        });
        await this.sendUnreadMessageEmails(tzOffsets);
    }
    async sendUnreadMessageEmails(tzOffsets) {
        const profiles = await this.profilesService.findAll({
            timezoneOffset: {
                [sequelize_1.Op.in]: tzOffsets,
            },
            lastActivityAt: {
                [sequelize_1.Op.lte]: (0, moment_1.default)().add(-1, 'hours').toDate(),
            },
            id: {
                [sequelize_1.Op.notLike]: '%deleted%',
            },
        }, {
            attributes: ['id', 'email', 'name', 'lastActivityAt'],
        });
        this.logger.info('EmailService.sendUnreadMessageEmails (Number of Profiles not active in last hour)', {
            numOfProfiles: profiles.length,
        });
        if (profiles.length === 0)
            return;
        const emailsToday = await this.emailService.findAll({
            [sequelize_1.Op.and]: [
                {
                    templateId: email_args_1.EmailTemplate.NewMessages,
                    profileId: {
                        [sequelize_1.Op.in]: profiles.map(p => p.id),
                    },
                    createdAt: {
                        [sequelize_1.Op.gte]: (0, moment_1.default)().subtract(12, 'hours').toDate(),
                    },
                },
            ],
        }, {
            attributes: ['profileId', 'createdAt'],
        });
        const emailFilteredProfiles = [];
        for (const profile of profiles) {
            const alreadySentEmails = emailsToday.filter(email => email.profileId === profile.id);
            if (alreadySentEmails.length < 4) {
                emailFilteredProfiles.push(profile);
            }
        }
        this.logger.info('EmailService.sendUnreadMessageEmails emailFilteredProfiles.length', {
            TotalEmailFilteredProfiles: emailFilteredProfiles.length,
        });
        if (emailFilteredProfiles.length === 0)
            return;
        const userResults = await this.chatService.getUsers(emailFilteredProfiles);
        this.logger.info('EmailService.sendUnreadMessageEmails userResults.length', {
            userResults: userResults.length,
        });
        for (const userResult of userResults) {
            if (userResult.unread_channels.count > 0) {
                const lastEmailToday = emailsToday
                    .filter(email => email.profileId === userResult.profile.id)
                    .pop();
                this.logger.info('EmailService.sendUnreadMessageEmails (consider)', {
                    userId: userResult.profile.id,
                    latestMessage: userResult.latest_message_created_at,
                    lastActivityAt: userResult.profile.lastActivityAt,
                    lastEmailAt: lastEmailToday === null || lastEmailToday === void 0 ? void 0 : lastEmailToday.createdAt,
                });
                if (userResult.latest_message_created_at >
                    userResult.profile.lastActivityAt &&
                    ((lastEmailToday === null || lastEmailToday === void 0 ? void 0 : lastEmailToday.createdAt)
                        ? userResult.latest_message_created_at > (lastEmailToday === null || lastEmailToday === void 0 ? void 0 : lastEmailToday.createdAt)
                        : true)) {
                    const text = await this.getNewMessageText(userResult);
                    if (text.length > 0) {
                        await this.sendEmail({
                            profileId: userResult.profile.id,
                            to: userResult.profile.email,
                            templateId: email_args_1.EmailTemplate.NewMessages,
                            dynamicTemplateData: {
                                name: userResult.profile.name,
                                text,
                            },
                        });
                    }
                }
            }
        }
    }
    getIncludeParams() {
        return [
            {
                model: webinar_participant_model_1.WebinarParticipant,
                as: 'webinarParticipant',
                include: [
                    {
                        model: webinar_model_1.Webinar,
                        as: 'webinar',
                        include: [
                            {
                                model: organisation_model_1.Organisation,
                                as: 'organisation',
                                attributes: ['name', 'id', 'vanityId', 'image'],
                            },
                        ],
                    },
                    {
                        model: profile_model_1.Profile,
                        as: 'profile',
                        attributes: ['id', 'name', 'image', 'lastOnlineAt'],
                    },
                    {
                        model: profile_model_1.Profile,
                        as: 'inviterProfile',
                        attributes: ['id', 'name', 'image', 'lastOnlineAt'],
                    },
                ],
            },
            {
                model: partnership_request_model_1.PartnershipRequest,
                as: 'partnershipRequest',
                include: [
                    {
                        model: organisation_model_1.Organisation,
                        as: 'senderOrganisation',
                        attributes: ['name', 'id', 'vanityId', 'image'],
                    },
                    {
                        model: organisation_model_1.Organisation,
                        as: 'receiverOrganisation',
                        attributes: ['name', 'id', 'vanityId', 'image'],
                    },
                ],
            },
            {
                model: membership_model_1.Membership,
                as: 'membership',
                attributes: [
                    'id',
                    'position',
                    'isPrimary',
                    'organisationName',
                    'organisationId',
                    'status',
                    'profileId',
                ],
                include: [
                    {
                        model: organisation_model_1.Organisation,
                        as: 'organisation',
                        attributes: ['name', 'id', 'vanityId', 'image'],
                    },
                    {
                        model: profile_model_1.Profile,
                        as: 'profile',
                        attributes: ['id', 'name', 'image', 'lastOnlineAt'],
                    },
                ],
            },
            {
                model: event_invitation_model_1.EventInvitation,
                as: 'eventInvitation',
                include: [
                    {
                        model: event_model_1.Event,
                        as: 'event',
                        include: [
                            {
                                model: organisation_model_1.Organisation,
                                as: 'organisation',
                                attributes: ['name', 'id', 'vanityId', 'image'],
                            },
                        ],
                    },
                    {
                        model: profile_model_1.Profile,
                        as: 'profile',
                        attributes: ['id', 'name', 'image', 'lastOnlineAt'],
                    },
                    {
                        model: profile_model_1.Profile,
                        as: 'inviterProfile',
                        attributes: ['id', 'name', 'image', 'lastOnlineAt'],
                    },
                ],
            },
            {
                model: incentive_participant_model_1.IncentiveParticipant,
                as: 'incentiveParticipant',
                include: [
                    {
                        model: incentive_model_1.Incentive,
                        as: 'incentive',
                        include: [
                            {
                                model: organisation_model_1.Organisation,
                                as: 'organisation',
                                attributes: ['name', 'id', 'vanityId', 'image'],
                            },
                        ],
                    },
                    {
                        model: profile_model_1.Profile,
                        as: 'profile',
                        attributes: ['id', 'name', 'image', 'lastOnlineAt'],
                    },
                    {
                        model: profile_model_1.Profile,
                        as: 'inviterProfile',
                        attributes: ['id', 'name', 'image', 'lastOnlineAt'],
                    },
                ],
            },
            {
                model: follower_model_1.Follower,
                as: 'follower',
                include: [
                    {
                        model: organisation_model_1.Organisation,
                        as: 'organisation',
                        attributes: ['name', 'id', 'vanityId', 'image'],
                    },
                    {
                        model: profile_model_1.Profile,
                        as: 'profile',
                        attributes: ['id', 'name', 'image', 'lastOnlineAt'],
                    },
                ],
            },
            {
                model: organisation_model_1.Organisation,
                as: 'organisation',
                attributes: ['name', 'id', 'vanityId', 'image'],
            },
            {
                model: profile_model_1.Profile,
                as: 'profile',
                attributes: ['id', 'name', 'image', 'lastOnlineAt'],
            },
        ];
    }
    async sendEveryDay0930AMEmails(tzOffset) {
        var _a;
        const previousDay0430PM = (0, moment_1.default)()
            .add(-1, 'days')
            .set('hours', 16)
            .set('minutes', 30)
            .set('seconds', 0)
            .toDate();
        previousDay0430PM.setTime(previousDay0430PM.getTime() -
            (tzOffset - previousDay0430PM.getTimezoneOffset()) * 60000);
        const emailsToday = await this.emailService.findAll({
            [sequelize_1.Op.and]: [
                sequelize_1.Sequelize.where(sequelize_1.Sequelize.fn('DATE', sequelize_1.Sequelize.col('createdAt')), sequelize_1.Sequelize.literal('CURRENT_DATE')),
                {
                    templateId: email_args_1.EmailTemplate.NewNotifications,
                },
            ],
        }, {
            attributes: ['profileId', 'createdAt'],
        });
        const past6months = (0, moment_1.default)().add(-180, 'days').toDate();
        const profiles = await this.profilesService.findAll({
            timezoneOffset: tzOffset,
            lastActivityAt: {
                [sequelize_1.Op.lt]: previousDay0430PM,
                [sequelize_1.Op.gt]: past6months,
            },
            id: {
                [sequelize_1.Op.notIn]: emailsToday.map(email => email.profileId),
                [sequelize_1.Op.notLike]: '%deleted%',
            },
        }, {
            attributes: ['id', 'email', 'name'],
        });
        for (const profile of profiles) {
            this.logger.info('EmailService.foundInactiveProfile', {
                profileId: profile.id,
            });
            const notifications = await this.getNotificationsWithOrganisations(profile);
            if (notifications.length) {
                this.logger.info('EmailService.sendNewNotificationsEmail.930 ONE', {
                    profileId: profile.id,
                });
                try {
                    const subject = await this.getSubject(notifications);
                    const html = await this.getNotificationEmailHTML(notifications);
                    await this.sendEmail({
                        profileId: profile.id,
                        subject: subject,
                        to: profile.email,
                        templateId: email_args_1.EmailTemplate.NewNotifications,
                        dynamicTemplateData: {
                            name: profile.name,
                            subject: subject,
                            notificationHTML: html,
                        },
                    });
                }
                catch (e) {
                    this.logger.error(`SendEmailConsumer Error: ${e.message}`, (_a = e.response) === null || _a === void 0 ? void 0 : _a.body);
                }
            }
        }
    }
    async sendEveryDay1230PMEmails(tzOffset) {
        var _a;
        const previousMidnight = new Date();
        previousMidnight.setHours(0);
        previousMidnight.setMinutes(0);
        previousMidnight.setSeconds(0);
        previousMidnight.setTime(previousMidnight.getTime() -
            (tzOffset - previousMidnight.getTimezoneOffset()) * 60000);
        const emailsToday = await this.emailService.findAll({
            [sequelize_1.Op.and]: [
                sequelize_1.Sequelize.where(sequelize_1.Sequelize.fn('DATE', sequelize_1.Sequelize.col('createdAt')), sequelize_1.Sequelize.literal('CURRENT_DATE')),
                {
                    templateId: email_args_1.EmailTemplate.NewNotifications,
                },
            ],
        }, {
            attributes: ['profileId', 'createdAt'],
        });
        const past6months = (0, moment_1.default)().add(-180, 'days').toDate();
        const profiles = await this.profilesService.findAll({
            timezoneOffset: tzOffset,
            lastActivityAt: {
                [sequelize_1.Op.lt]: previousMidnight,
                [sequelize_1.Op.gt]: past6months,
            },
            id: {
                [sequelize_1.Op.notIn]: emailsToday.map(email => email.profileId),
                [sequelize_1.Op.notLike]: '%deleted%',
            },
        }, {
            attributes: ['id', 'email', 'name'],
        });
        for (const profile of profiles) {
            this.logger.info('EmailService.foundInactiveProfile', {
                profileId: profile.id,
            });
            const notifications = await this.getNotificationsWithOrganisations(profile);
            if (notifications.length) {
                this.logger.info('EmailService.sendNewNotificationsEmail.930 ONE', {
                    profileId: profile.id,
                });
                try {
                    const subject = await this.getSubject(notifications);
                    const html = await this.getNotificationEmailHTML(notifications);
                    await this.sendEmail({
                        profileId: profile.id,
                        subject: subject,
                        to: profile.email,
                        templateId: email_args_1.EmailTemplate.NewNotifications,
                        dynamicTemplateData: {
                            name: profile.name,
                            subject: subject,
                            notificationHTML: html,
                        },
                    });
                }
                catch (e) {
                    this.logger.error(`SendEmailConsumer Error: ${e.message}`, (_a = e.response) === null || _a === void 0 ? void 0 : _a.body);
                }
            }
        }
    }
    async sendEveryDay1530PMEmails(tzOffset) {
        var _a;
        const today1130AM = new Date();
        today1130AM.setHours(11);
        today1130AM.setMinutes(30);
        today1130AM.setSeconds(0);
        today1130AM.setTime(today1130AM.getTime() -
            (tzOffset - today1130AM.getTimezoneOffset()) * 60000);
        const emailsToday = await this.emailService.findAll({
            [sequelize_1.Op.and]: [
                sequelize_1.Sequelize.where(sequelize_1.Sequelize.fn('DATE', sequelize_1.Sequelize.col('createdAt')), sequelize_1.Sequelize.literal('CURRENT_DATE')),
                {
                    templateId: email_args_1.EmailTemplate.NewNotifications,
                },
            ],
        }, {
            attributes: ['profileId', 'createdAt'],
        });
        const past6months = (0, moment_1.default)().add(-180, 'days').toDate();
        const profiles = await this.profilesService.findAll({
            timezoneOffset: tzOffset,
            lastActivityAt: {
                [sequelize_1.Op.lt]: today1130AM,
                [sequelize_1.Op.gt]: past6months,
            },
            id: {
                [sequelize_1.Op.notIn]: emailsToday.map(email => email.profileId),
                [sequelize_1.Op.notLike]: '%deleted%',
            },
        }, {
            attributes: ['id', 'email', 'name'],
        });
        for (const profile of profiles) {
            this.logger.info('EmailService.foundInactiveProfile', {
                profileId: profile.id,
            });
            const notifications = await this.getNotificationsWithOrganisations(profile);
            if (notifications.length) {
                this.logger.info('EmailService.sendNewNotificationsEmail.930 ONE', {
                    profileId: profile.id,
                });
                try {
                    const subject = await this.getSubject(notifications);
                    const html = await this.getNotificationEmailHTML(notifications);
                    await this.sendEmail({
                        profileId: profile.id,
                        subject: subject,
                        to: profile.email,
                        templateId: email_args_1.EmailTemplate.NewNotifications,
                        dynamicTemplateData: {
                            name: profile.name,
                            subject: subject,
                            notificationHTML: html,
                        },
                    });
                }
                catch (e) {
                    this.logger.error(`SendEmailConsumer Error: ${e.message}`, (_a = e.response) === null || _a === void 0 ? void 0 : _a.body);
                }
            }
        }
    }
    async getNewMessageText(userResult) {
        const profile = userResult.profile;
        const channelsCount = userResult.unread_channels.channelIds.length;
        const names = [];
        let groupsText = '';
        let peopleText = '';
        let unreadGroupChannels = 0;
        for (let i = 0; i < channelsCount; i++) {
            const connection = await this.connectionsService.findOne({
                connectionProfileId: profile.id,
                streamChannelId: userResult.unread_channels.channelIds[i],
            }, {
                includeParams: [
                    {
                        as: 'profile',
                        model: profile_model_1.Profile,
                        attributes: ['name'],
                    },
                ],
            });
            if (connection) {
                names.push(connection.profile.name);
            }
            else {
                unreadGroupChannels++;
            }
        }
        if (unreadGroupChannels > 0) {
            groupsText = `${unreadGroupChannels} ${unreadGroupChannels > 1 ? 'unread group chats' : 'unread group chat'}`;
        }
        if (names.length > 0) {
            peopleText = `${names.length} ${names.length > 1 ? 'unread chats with' : 'unread chat with'} `;
            peopleText += names.join(', ');
            if (names.length > 1) {
                peopleText =
                    peopleText.substring(0, peopleText.lastIndexOf(', ')) +
                        ' and ' +
                        names[names.length - 1];
            }
        }
        if (peopleText === '' && groupsText === '')
            return '';
        let andText = peopleText !== '' && groupsText !== '' ? ', plus ' : '';
        let result = `You have ${peopleText}${andText}${groupsText}`;
        return result + '.';
    }
    async sendConnectInvitations(user, text, emails) {
        var _a;
        try {
            const profile = await this.profilesService.findById(user.profileId);
            for (const email of emails) {
                if (email !== profile.email) {
                    await this.sendEmail({
                        profileId: user.profileId,
                        to: email,
                        templateId: email_args_1.EmailTemplate.NewInvitation,
                        customFromName: user.name,
                        customFromEmail: '<EMAIL>',
                        dynamicTemplateData: {
                            name: user.name,
                            text,
                        },
                    });
                    await this.emailInvitationsService.saveEmailInvitationInfo(user.profileId, email);
                }
            }
        }
        catch (e) {
            this.logger.error(`SendEmailConsumer Error: ${e.message}`, (_a = e.response) === null || _a === void 0 ? void 0 : _a.body);
        }
        return true;
    }
    async sendEmail(options) {
        var _a, _b;
        const sendGridOptions = {
            to: options.to,
            templateId: options.templateId,
            dynamicTemplateData: options.dynamicTemplateData,
            subject: (_a = options === null || options === void 0 ? void 0 : options.subject) !== null && _a !== void 0 ? _a : '',
            from: {
                email: options.customFromEmail
                    ? options.customFromEmail
                    : config_1.default.SENDGRID_FROM,
                name: options.customFromName
                    ? options.customFromName
                    : config_1.default.SENDGRID_NAME,
            },
            replyTo: config_1.default.SENDGRID_REPLY_TO,
        };
        const whitelist = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ];
        this.logger.info('EmailService.sendEmail', {
            profileId: options.profileId,
            email: options.to,
            templateId: options.templateId,
            text: sendGridOptions.text,
        });
        const isWhitelisted = whitelist.includes(options.to);
        const isNewMessages = email_args_1.EmailTemplate.NewMessages === options.templateId;
        const isNewNotifications = email_args_1.EmailTemplate.NewNotifications === options.templateId;
        const isEmailInvitation = email_args_1.EmailTemplate.NewInvitation === options.templateId;
        const isSuggestFollowEmail = email_args_1.EmailTemplate.SuggestFollow === options.templateId;
        const profile = await this.profilesService.findById(options.profileId);
        if (!profile.receiveNewMessagesEmails &&
            options.templateId === email_args_1.EmailTemplate.NewMessages) {
            return;
        }
        if (!profile.receiveNotificationEmails &&
            options.templateId === email_args_1.EmailTemplate.NewNotifications) {
            return;
        }
        const transaction = await this.sequelize.transaction();
        try {
            const isAllowedToSend = isEmailInvitation ||
                ((isNewMessages || isNewNotifications) &&
                    (config_1.default.SERVICE === 'api' ||
                        (isWhitelisted &&
                            (config_1.default.NODE_ENV === 'development' ||
                                config_1.default.SERVICE === 'api-beta')))) ||
                (isSuggestFollowEmail &&
                    (config_1.default.SERVICE === 'api' || config_1.default.NODE_ENV === 'development'));
            if (isAllowedToSend) {
                await this.sendGridClient.send(sendGridOptions);
            }
            await this.emailService.create({
                profileId: options.profileId,
                templateId: options.templateId,
                status: isAllowedToSend ? email_args_1.EmailStatus.Sent : email_args_1.EmailStatus.Hold,
            }, { transaction });
            await transaction.commit();
        }
        catch (e) {
            await transaction.rollback();
            this.logger.error(`SendEmailConsumer Error: ${e.message}`, (_b = e.response) === null || _b === void 0 ? void 0 : _b.body);
        }
    }
    async getNotificationsWithOrganisations(profile) {
        const notifications = await this.notificationsService.findAll({
            ownerProfileId: profile.id,
            isSeen: false,
            createdAt: {
                [sequelize_1.Op.gt]: (0, moment_1.default)().subtract(7, 'days').toDate(),
            },
        }, {
            includeParams: this.getIncludeParams(),
        });
        return await Promise.all(notifications.map(async (notification) => {
            var _a, _b;
            if ((_a = notification === null || notification === void 0 ? void 0 : notification.data) === null || _a === void 0 ? void 0 : _a.organisationId) {
                const organisationDetail = await this.organisationsService.findOne({ id: (_b = notification === null || notification === void 0 ? void 0 : notification.data) === null || _b === void 0 ? void 0 : _b.organisationId }, { attributes: ['name', 'id', 'vanityId', 'image'] });
                notification.data.organisation = organisationDetail;
            }
            return notification;
        }));
    }
};
exports.EmailServiceHelper = EmailServiceHelper;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => email_service_1.EmailService)),
    __metadata("design:type", email_service_1.EmailService)
], EmailServiceHelper.prototype, "emailService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], EmailServiceHelper.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => connections_service_1.ConnectionsService)),
    __metadata("design:type", connections_service_1.ConnectionsService)
], EmailServiceHelper.prototype, "connectionsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => chat_service_1.ChatService)),
    __metadata("design:type", chat_service_1.ChatService)
], EmailServiceHelper.prototype, "chatService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => email_invitations_service_1.EmailInvitationsService)),
    __metadata("design:type", email_invitations_service_1.EmailInvitationsService)
], EmailServiceHelper.prototype, "emailInvitationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => notifications_service_1.NotificationsService)),
    __metadata("design:type", notifications_service_1.NotificationsService)
], EmailServiceHelper.prototype, "notificationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], EmailServiceHelper.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", sequelize_1.Sequelize)
], EmailServiceHelper.prototype, "sequelize", void 0);
__decorate([
    (0, nestjs_sendgrid_1.InjectSendGrid)(),
    __metadata("design:type", nestjs_sendgrid_1.SendGridService)
], EmailServiceHelper.prototype, "sendGridClient", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], EmailServiceHelper.prototype, "logger", void 0);
exports.EmailServiceHelper = EmailServiceHelper = __decorate([
    (0, common_1.Injectable)()
], EmailServiceHelper);
//# sourceMappingURL=email.service.helper.js.map