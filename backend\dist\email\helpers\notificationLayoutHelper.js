"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationHelper = exports.getOrgNameStringsForNotifications = exports.getStringsForNotifications = exports.Routes = exports.getFullPathFromRoute = exports.encodePathParams = exports.DATE_SHORT = exports.DATE_MONTH = void 0;
exports.FromNow = FromNow;
const notifications_args_1 = require("../../notifications/args/notifications.args");
const config_1 = __importDefault(require("../../config/config"));
const moment_1 = __importDefault(require("moment"));
const lodash_1 = require("lodash");
exports.DATE_MONTH = 'D MMMM';
exports.DATE_SHORT = 'D MMM YYYY';
const CLOUDINARY_URL = `https://res.cloudinary.com/${config_1.default.CLOUDINARY_CLOUD_NAME}/image/upload`;
const Icons = {
    calendar: `${CLOUDINARY_URL}/v1672224432/calendar_ldzj2s.png`,
    comment: `${CLOUDINARY_URL}/v1672224432/comment_olk8df.png`,
    explore: `${CLOUDINARY_URL}/v1672224432/explore_cx9cor.png`,
    incentives: `${CLOUDINARY_URL}/v1672224432/incentives_pozanz.png`,
    like: `${CLOUDINARY_URL}/v1672224432/like_bkoa2y.png`,
    posts: `${CLOUDINARY_URL}/v1672224432/posts_ismiyi.png`,
    security: `${CLOUDINARY_URL}/v1672224432/security_xp6ney.png`,
    time: `${CLOUDINARY_URL}/v1672224432/time_samgyp.png`,
    users: `${CLOUDINARY_URL}/v1672224432/users_tepktq.png`,
    webinar: `${CLOUDINARY_URL}/v1672224432/webinar_mkgkuo.png`,
};
const encodePathParams = (url, data, searchParams) => {
    try {
        const baseUrl = config_1.default.FRONTEND_URL;
        if (!data)
            return baseUrl;
        const basePath = `${baseUrl}${url}`;
        const pathResult = Object.keys(data).reduce((acc, key) => {
            const newPath = acc.replace(`:${key}`, data[key]);
            return searchParams && searchParams.length
                ? newPath + '?' + searchParams
                : newPath;
        }, basePath);
        return pathResult;
    }
    catch (e) {
        return '#';
    }
};
exports.encodePathParams = encodePathParams;
const getFullPathFromRoute = (url) => {
    const baseUrl = config_1.default.FRONTEND_URL;
    return `${baseUrl}${url}`;
};
exports.getFullPathFromRoute = getFullPathFromRoute;
function FromNow(date) {
    if ((0, moment_1.default)(date, 'YYYY-MM-DD HH:mm:ss.SSSSSS +Z').isValid()) {
        date = (0, moment_1.default)(date, 'YYYY-MM-DD HH:mm:ss.SSSSSS +Z');
    }
    if (date.isAfter((0, moment_1.default)()))
        return null;
    const seconds = (0, moment_1.default)().diff(date, 's');
    const minutes = Math.round(seconds / 60);
    const hours = Math.round(minutes / 60);
    const days = Math.round(hours / 24);
    const years = Math.round(days / 365);
    if (seconds < 60) {
        return `<span style="text-decoration:none">${seconds}s</span>`;
    }
    if (minutes < 60) {
        return `<span style="text-decoration:none">${minutes}m</span>`;
    }
    if (hours < 24) {
        return `<span style="text-decoration:none">${hours}h</span>`;
    }
    if (days === 1) {
        return `<span style="text-decoration:none">yesterday</span>`;
    }
    if (days <= 7) {
        return `<span style="text-decoration:none">${days}d</span>`;
    }
    if (years < 1) {
        return `<span style="text-decoration:none">${date.format(exports.DATE_MONTH)}</span>`;
    }
    return `<span style="text-decoration:none">${date.format(exports.DATE_SHORT)}</span>`;
}
var Routes;
(function (Routes) {
    Routes["login"] = "/login";
    Routes["signUp"] = "/signup";
    Routes["authCallback"] = "/auth/callback";
    Routes["networkError"] = "/networkerror";
    Routes["default"] = "/";
    Routes["home"] = "/home";
    Routes["calendar"] = "/calendar";
    Routes["notifications"] = "/notifications";
    Routes["search"] = "/search";
    Routes["inbox"] = "/inbox";
    Routes["inboxChat"] = "/inbox/chat/:chatId";
    Routes["inboxChatNew"] = "/inbox/chat/new";
    Routes["video"] = "/inbox/call/:callId";
    Routes["explore"] = "/explore";
    Routes["organisationChooseType"] = "/organisation/join";
    Routes["organisationCreate"] = "/organisation/join/:type";
    Routes["organisationSettings"] = "/organisation/:organisationId";
    Routes["organisationSettingsInfo"] = "/organisation/:organisationId/info";
    Routes["organisationSettingsEmployees"] = "/organisation/:organisationId/employees";
    Routes["organisationSettingsUserRoles"] = "/organisation/:organisationId/user-roles";
    Routes["organisationSettingsPrivacy"] = "/organisation/:organisationId/privacy";
    Routes["organisationSettingsConnections"] = "/organisation/:organisationId/connections";
    Routes["organisationSettingsInvitations"] = "/organisation/:organisationId/events";
    Routes["connect"] = "/connect";
    Routes["connectInviteContacts"] = "/connect/invite-contacts";
    Routes["connectInviteContactsSent"] = "/connect/invite-contacts/sent";
    Routes["incentives"] = "/incentives";
    Routes["profile"] = "/profile";
    Routes["profileSetup"] = "/profile/setup";
    Routes["profileSettings"] = "/profile/settings";
    Routes["profileSettingsAccount"] = "/profile/settings";
    Routes["profileSettingsAccountDelete"] = "/profile/settings/account/delete";
    Routes["profileSettingsPrivacy"] = "/profile/settings/privacy";
    Routes["profileSettingsNotifications"] = "/profile/settings/notifications";
    Routes["profileById"] = "/profile/:profileId";
    Routes["habloAdmin"] = "/habloadmin";
    Routes["habloAdminManageOrganisation"] = "/habloadmin/manage-organisation";
    Routes["habloAdminManageExplorePages"] = "/habloadmin/manage-explore-pages";
    Routes["habloAdminManageAutoFollows"] = "/habloadmin/manage-auto-follows";
    Routes["organisationSettingsMembers"] = "/organisation/:organisationId/members";
    Routes["organisationProfile"] = "/:vanityId";
    Routes["organisationAbout"] = "/:vanityId/about";
    Routes["organisationWebinars"] = "/:vanityId/webinars";
    Routes["organisationWebinarBroadcast"] = "/:vanityId/webinars/:id/broadcast/";
    Routes["organisationResources"] = "/:vanityId/resources";
    Routes["organisationEvents"] = "/:vanityId/events";
    Routes["organisationEvent"] = "/:vanityId/events/:eventId";
    Routes["organisationIncentive"] = "/:vanityId/incentives/:incentiveId";
    Routes["organisationWebinar"] = "/:vanityId/webinars/:id";
    Routes["organisationPeople"] = "/:vanityId/people";
    Routes["organisationAnalytics"] = "/:vanityId/analytics";
    Routes["organisationPost"] = "/:vanityId/post/:id/userid/:userId";
    Routes["userPost"] = "/:vanityId/post/:id";
    Routes["organisationNewPost"] = "/home?create=post";
    Routes["exploreAllDestinations"] = "/explore/destination";
    Routes["exploreDestination"] = "/explore/destination/:destinationId";
    Routes["explorePrivateSector"] = "/explore/private-sector";
    Routes["exploreTourOperator"] = "/explore/tour-operator";
    Routes["exploreTravelAgent"] = "/explore/travel-agent";
    Routes["exploreAgencies"] = "/explore/agencies";
    Routes["exploreOrganisationsYouFollow"] = "/explore/organisations-you-follow";
    Routes["addConnection"] = "/organisation/:organisationId/connections/add";
    Routes["pendingConnections"] = "/organisation/:organisationId/connections/pending";
    Routes["approvedConnections"] = "/organisation/:organisationId/connections/pending/success";
    Routes["activeConnections"] = "/organisation/:organisationId/connections/active";
    Routes["previousConnections"] = "/organisation/:organisationId/connections/previous";
    Routes["clubHabloDashboard"] = "/club-hablo/dashboard";
})(Routes || (exports.Routes = Routes = {}));
const getStringsForNotifications = (countOfUsers, usersFullNames = []) => {
    switch (countOfUsers) {
        case 1:
            return {
                usersFullNamesString: usersFullNames[0],
                other: '',
                has: 'has ',
            };
        case 2:
            return {
                usersFullNamesString: `${usersFullNames[0]} & ${usersFullNames[1]} `,
                other: '',
                has: 'have ',
            };
        case 3:
            return {
                usersFullNamesString: `${usersFullNames[0]}, ${usersFullNames[1]} & 1 `,
                other: 'other ',
                has: 'have ',
            };
        default:
            return {
                usersFullNamesString: `${usersFullNames[0]}, ${usersFullNames[1]} & ${countOfUsers} `,
                other: 'others ',
                has: 'have ',
            };
    }
};
exports.getStringsForNotifications = getStringsForNotifications;
const getOrgNameStringsForNotifications = (countOfOrgs, organistionNames = []) => {
    switch (countOfOrgs) {
        case 1:
            return {
                organisationNamesString: organistionNames[0],
                has: 'has ',
            };
        case 2:
            return {
                organisationNamesString: `${organistionNames[0]} and ${organistionNames[1]} `,
                has: 'have ',
            };
        case 3:
            return {
                organisationNamesString: `${organistionNames[0]}, ${organistionNames[1]} and ${organistionNames[2]} `,
                has: 'have ',
            };
        default:
            return {
                organisationNamesString: `${organistionNames[0]} `,
                has: 'has ',
            };
    }
};
exports.getOrgNameStringsForNotifications = getOrgNameStringsForNotifications;
class NotificationHelper {
    static getEmptyConfiguration() {
        return {
            image: '',
            route: '',
            text: '<div></div>',
            icon: Icons.time,
            rightImage: '',
        };
    }
    static getConfiguration(notification) {
        return {
            image: this.getImage(notification),
            route: this.getRoute(notification),
            text: this.getText(notification),
            icon: this.getIcon(notification),
            rightImage: this.getRightImage(notification),
        };
    }
    static getImage(notification) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, _16, _17, _18, _19, _20;
        switch (notification.type) {
            case notifications_args_1.NotificationType.InvitationAccepted:
            case notifications_args_1.NotificationType.InvitationReceived:
                return (_a = notification.profile) === null || _a === void 0 ? void 0 : _a.image;
            case notifications_args_1.NotificationType.PartnershipRequestReceived:
                return (_c = (_b = notification.partnershipRequest) === null || _b === void 0 ? void 0 : _b.senderOrganisation) === null || _c === void 0 ? void 0 : _c.image;
            case notifications_args_1.NotificationType.PartnershipRequestApproved:
                return (_e = (_d = notification.partnershipRequest) === null || _d === void 0 ? void 0 : _d.receiverOrganisation) === null || _e === void 0 ? void 0 : _e.image;
            case notifications_args_1.NotificationType.PartnershipRequestApprovedByOrganisation:
                return (_g = (_f = notification.partnershipRequest) === null || _f === void 0 ? void 0 : _f.senderOrganisation) === null || _g === void 0 ? void 0 : _g.image;
            case notifications_args_1.NotificationType.MembershipRequested:
            case notifications_args_1.NotificationType.MembershipAccepted:
            case notifications_args_1.NotificationType.ParentMembershipRequested:
            case notifications_args_1.NotificationType.ParentMembershipAccepted:
            case notifications_args_1.NotificationType.ParentMembershipDeclined:
                return (_j = (_h = notification.membership) === null || _h === void 0 ? void 0 : _h.organisation) === null || _j === void 0 ? void 0 : _j.image;
            case notifications_args_1.NotificationType.FollowerAccepted:
                return (_l = (_k = notification.follower) === null || _k === void 0 ? void 0 : _k.organisation) === null || _l === void 0 ? void 0 : _l.image;
            case notifications_args_1.NotificationType.MembershipPermissionsUpdated:
            case notifications_args_1.NotificationType.OrganisationOwnershipAccepted:
            case notifications_args_1.NotificationType.OrganisationOwnershipRequested:
                return (_o = (_m = notification.membership) === null || _m === void 0 ? void 0 : _m.organisation) === null || _o === void 0 ? void 0 : _o.image;
            case notifications_args_1.NotificationType.EventInvitationByGuest:
                return (_q = (_p = notification.eventInvitation) === null || _p === void 0 ? void 0 : _p.inviterProfile) === null || _q === void 0 ? void 0 : _q.image;
            case notifications_args_1.NotificationType.EventInvitationByHosts:
            case notifications_args_1.NotificationType.EventInvitationApproved:
            case notifications_args_1.NotificationType.NewEventUpdate:
            case notifications_args_1.NotificationType.EventLocationChanged:
            case notifications_args_1.NotificationType.EventDateTimeChanged:
                return (_t = (_s = (_r = notification.eventInvitation) === null || _r === void 0 ? void 0 : _r.event) === null || _s === void 0 ? void 0 : _s.organisation) === null || _t === void 0 ? void 0 : _t.image;
            case notifications_args_1.NotificationType.IncentiveDateChanged:
            case notifications_args_1.NotificationType.IncentiveInvitationByHosts:
            case notifications_args_1.NotificationType.IncentiveRegistrationApproved:
            case notifications_args_1.NotificationType.IncentiveRegistrationRequested:
            case notifications_args_1.NotificationType.NewIncentiveUpdate:
                return (_w = (_v = (_u = notification.incentiveParticipant) === null || _u === void 0 ? void 0 : _u.incentive) === null || _v === void 0 ? void 0 : _v.organisation) === null || _w === void 0 ? void 0 : _w.image;
            case notifications_args_1.NotificationType.IncentiveInvitationByParticipant:
                return (_y = (_x = notification.incentiveParticipant) === null || _x === void 0 ? void 0 : _x.inviterProfile) === null || _y === void 0 ? void 0 : _y.image;
            case notifications_args_1.NotificationType.WebinarDateChanged:
            case notifications_args_1.NotificationType.WebinarInvitationByHosts:
            case notifications_args_1.NotificationType.WebinarRegistrationApproved:
            case notifications_args_1.NotificationType.WebinarRegistrationRequested:
            case notifications_args_1.NotificationType.WebinarParticipantAddedAsHostAdmin:
            case notifications_args_1.NotificationType.WebinarParticipantAddedAsHost:
            case notifications_args_1.NotificationType.WebinarParticipantAddedAsSpeaker:
            case notifications_args_1.NotificationType.NewWebinarUpdate:
                return (_1 = (_0 = (_z = notification.webinarParticipant) === null || _z === void 0 ? void 0 : _z.webinar) === null || _0 === void 0 ? void 0 : _0.organisation) === null || _1 === void 0 ? void 0 : _1.image;
            case notifications_args_1.NotificationType.WebinarInvitationByParticipant:
                return (_3 = (_2 = notification.webinarParticipant) === null || _2 === void 0 ? void 0 : _2.inviterProfile) === null || _3 === void 0 ? void 0 : _3.image;
            case notifications_args_1.NotificationType.SuggestFollow:
                return 'jpwbh9kjy7an2w05jijd';
            case notifications_args_1.NotificationType.PostComment:
                return notification.data.users[0].image;
            case notifications_args_1.NotificationType.CommentReact:
                return notification.data.users[0].image;
            case notifications_args_1.NotificationType.PostReact:
            case notifications_args_1.NotificationType.NewFollower:
                return (_5 = (_4 = notification.data) === null || _4 === void 0 ? void 0 : _4.users) === null || _5 === void 0 ? void 0 : _5[0].image;
            case notifications_args_1.NotificationType.PostShared:
            case notifications_args_1.NotificationType.PostMention:
                return (_6 = notification.organisation) === null || _6 === void 0 ? void 0 : _6.image;
            case notifications_args_1.NotificationType.CommentMention:
                return (_7 = notification.profile) === null || _7 === void 0 ? void 0 : _7.image;
            case notifications_args_1.NotificationType.OrgCommentMention:
                return (_8 = notification.profile) === null || _8 === void 0 ? void 0 : _8.image;
            case notifications_args_1.NotificationType.RecurringPaymentReminder:
                return (_10 = (_9 = notification.partnershipRequest) === null || _9 === void 0 ? void 0 : _9.senderOrganisation) === null || _10 === void 0 ? void 0 : _10.image;
            case notifications_args_1.NotificationType.MembershipAutoApprove:
                return (_12 = (_11 = notification.membership) === null || _11 === void 0 ? void 0 : _11.profile) === null || _12 === void 0 ? void 0 : _12.image;
            case notifications_args_1.NotificationType.OrgPostReminder7Days:
            case notifications_args_1.NotificationType.OrgPostReminder14Days:
            case notifications_args_1.NotificationType.OrgPostMention:
            case notifications_args_1.NotificationType.HighFiveAchievement:
            case notifications_args_1.NotificationType.NewOrganisationAchievement:
                return (_13 = notification.organisation) === null || _13 === void 0 ? void 0 : _13.image;
            case notifications_args_1.NotificationType.InactiveUserReminder:
                return (_15 = (_14 = notification.data) === null || _14 === void 0 ? void 0 : _14[0]) === null || _15 === void 0 ? void 0 : _15.organisationImage;
            case notifications_args_1.NotificationType.InboxMessage:
            case notifications_args_1.NotificationType.NewTier:
            case notifications_args_1.NotificationType.WeeklySummary:
            case notifications_args_1.NotificationType.DailyLoginStreak:
            case notifications_args_1.NotificationType.NewAchievement:
                return (_16 = notification.profile) === null || _16 === void 0 ? void 0 : _16.image;
            case notifications_args_1.NotificationType.OrgPartnerRequestReceived:
                return (_18 = (_17 = notification.data) === null || _17 === void 0 ? void 0 : _17.senderOrg) === null || _18 === void 0 ? void 0 : _18.image;
            case notifications_args_1.NotificationType.OrgPartnerAcceptedSender:
            case notifications_args_1.NotificationType.OrgPartnerAcceptedReceiver:
                return (_20 = (_19 = notification.data) === null || _19 === void 0 ? void 0 : _19.receiverOrg) === null || _20 === void 0 ? void 0 : _20.image;
            default: {
                return null;
            }
        }
    }
    static getRightImage(notification) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6;
        switch (notification.type) {
            case notifications_args_1.NotificationType.EventInvitationByGuest:
            case notifications_args_1.NotificationType.EventInvitationByHosts:
            case notifications_args_1.NotificationType.EventInvitationApproved:
            case notifications_args_1.NotificationType.NewEventUpdate:
            case notifications_args_1.NotificationType.EventLocationChanged:
            case notifications_args_1.NotificationType.EventDateTimeChanged:
                return (_b = (_a = notification.eventInvitation) === null || _a === void 0 ? void 0 : _a.event) === null || _b === void 0 ? void 0 : _b.image;
            case notifications_args_1.NotificationType.MembershipRequested:
            case notifications_args_1.NotificationType.ParentMembershipRequested:
            case notifications_args_1.NotificationType.ParentMembershipAccepted:
            case notifications_args_1.NotificationType.ParentMembershipDeclined:
                return (_c = notification.profile) === null || _c === void 0 ? void 0 : _c.image;
            case notifications_args_1.NotificationType.IncentiveDateChanged:
            case notifications_args_1.NotificationType.IncentiveInvitationByHosts:
            case notifications_args_1.NotificationType.IncentiveInvitationByParticipant:
            case notifications_args_1.NotificationType.IncentiveRegistrationApproved:
            case notifications_args_1.NotificationType.NewIncentiveUpdate:
                return (_e = (_d = notification.incentiveParticipant) === null || _d === void 0 ? void 0 : _d.incentive) === null || _e === void 0 ? void 0 : _e.image;
            case notifications_args_1.NotificationType.IncentiveRegistrationRequested:
                return (_g = (_f = notification.incentiveParticipant) === null || _f === void 0 ? void 0 : _f.profile) === null || _g === void 0 ? void 0 : _g.image;
            case notifications_args_1.NotificationType.WebinarDateChanged:
            case notifications_args_1.NotificationType.WebinarInvitationByHosts:
            case notifications_args_1.NotificationType.WebinarInvitationByParticipant:
            case notifications_args_1.NotificationType.WebinarRegistrationApproved:
            case notifications_args_1.NotificationType.NewWebinarUpdate:
            case notifications_args_1.NotificationType.WebinarParticipantAddedAsHostAdmin:
            case notifications_args_1.NotificationType.WebinarParticipantAddedAsHost:
            case notifications_args_1.NotificationType.WebinarParticipantAddedAsSpeaker:
                return (_j = (_h = notification.webinarParticipant) === null || _h === void 0 ? void 0 : _h.webinar) === null || _j === void 0 ? void 0 : _j.image;
            case notifications_args_1.NotificationType.WebinarRegistrationRequested:
                return (_l = (_k = notification.webinarParticipant) === null || _k === void 0 ? void 0 : _k.profile) === null || _l === void 0 ? void 0 : _l.image;
            case notifications_args_1.NotificationType.PostComment:
                return notification.data.users[0].image;
            case notifications_args_1.NotificationType.CommentReact:
                return notification.data.users[0].image;
            case notifications_args_1.NotificationType.PostReact:
            case notifications_args_1.NotificationType.NewFollower:
                return (_o = (_m = notification.data) === null || _m === void 0 ? void 0 : _m.users) === null || _o === void 0 ? void 0 : _o[0].image;
            case notifications_args_1.NotificationType.PostShared:
            case notifications_args_1.NotificationType.PostMention:
                return (_p = notification.organisation) === null || _p === void 0 ? void 0 : _p.image;
            case notifications_args_1.NotificationType.CommentMention:
                return (_q = notification.profile) === null || _q === void 0 ? void 0 : _q.image;
            case notifications_args_1.NotificationType.OrgPostMention:
            case notifications_args_1.NotificationType.NewOrganisationAchievement:
                return (_r = notification.organisation) === null || _r === void 0 ? void 0 : _r.image;
            case notifications_args_1.NotificationType.OrgCommentMention:
                return (_s = notification.profile) === null || _s === void 0 ? void 0 : _s.image;
            case notifications_args_1.NotificationType.RecurringPaymentReminder:
                return (_u = (_t = notification.partnershipRequest) === null || _t === void 0 ? void 0 : _t.receiverOrganisation) === null || _u === void 0 ? void 0 : _u.image;
            case notifications_args_1.NotificationType.MembershipAutoApprove:
                return (_w = (_v = notification.membership) === null || _v === void 0 ? void 0 : _v.profile) === null || _w === void 0 ? void 0 : _w.image;
            case notifications_args_1.NotificationType.OrgPostReminder7Days:
            case notifications_args_1.NotificationType.OrgPostReminder14Days:
                return (_x = notification.organisation) === null || _x === void 0 ? void 0 : _x.image;
            case notifications_args_1.NotificationType.InactiveUserReminder:
                return (_z = (_y = notification.data) === null || _y === void 0 ? void 0 : _y[0]) === null || _z === void 0 ? void 0 : _z.organisationImage;
            case notifications_args_1.NotificationType.InboxMessage:
                return (_0 = notification.profile) === null || _0 === void 0 ? void 0 : _0.image;
            case notifications_args_1.NotificationType.NewTier:
                return (_1 = notification.profile) === null || _1 === void 0 ? void 0 : _1.image;
            case notifications_args_1.NotificationType.WeeklySummary:
                return (_2 = notification.profile) === null || _2 === void 0 ? void 0 : _2.image;
            case notifications_args_1.NotificationType.OrgPartnerRequestReceived:
                return (_4 = (_3 = notification.data) === null || _3 === void 0 ? void 0 : _3.receiverOrg) === null || _4 === void 0 ? void 0 : _4.image;
            case notifications_args_1.NotificationType.OrgPartnerAcceptedSender:
            case notifications_args_1.NotificationType.OrgPartnerAcceptedReceiver:
                return (_6 = (_5 = notification.data) === null || _5 === void 0 ? void 0 : _5.senderOrg) === null || _6 === void 0 ? void 0 : _6.image;
            default: {
                return null;
            }
        }
    }
    static getRoute(notification) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, _16, _17, _18, _19, _20, _21, _22, _23, _24, _25, _26, _27, _28, _29, _30, _31, _32, _33;
        switch (notification.type) {
            case notifications_args_1.NotificationType.InvitationReceived:
                return (0, exports.getFullPathFromRoute)(Routes.connect);
            case notifications_args_1.NotificationType.InvitationAccepted:
                return (0, exports.encodePathParams)(Routes.profileById, {
                    profileId: (_a = notification.profile) === null || _a === void 0 ? void 0 : _a.id,
                });
            case notifications_args_1.NotificationType.PartnershipRequestReceived:
                return (0, exports.encodePathParams)(Routes.pendingConnections, {
                    organisationId: (_c = (_b = notification.partnershipRequest) === null || _b === void 0 ? void 0 : _b.receiverOrganisation) === null || _c === void 0 ? void 0 : _c.id,
                });
            case notifications_args_1.NotificationType.PartnershipRequestApproved:
                return (0, exports.encodePathParams)(Routes.activeConnections, {
                    organisationId: (_e = (_d = notification.partnershipRequest) === null || _d === void 0 ? void 0 : _d.senderOrganisation) === null || _e === void 0 ? void 0 : _e.id,
                });
            case notifications_args_1.NotificationType.PartnershipRequestApprovedByOrganisation:
                return (0, exports.encodePathParams)(Routes.organisationProfile, {
                    vanityId: (_g = (_f = notification.partnershipRequest) === null || _f === void 0 ? void 0 : _f.senderOrganisation) === null || _g === void 0 ? void 0 : _g.vanityId,
                });
            case notifications_args_1.NotificationType.OrganisationOwnershipAccepted:
            case notifications_args_1.NotificationType.OrganisationOwnershipRequested:
                return (0, exports.encodePathParams)(Routes.organisationSettingsUserRoles, {
                    organisationId: (_j = (_h = notification === null || notification === void 0 ? void 0 : notification.membership) === null || _h === void 0 ? void 0 : _h.organisation) === null || _j === void 0 ? void 0 : _j.id,
                });
            case notifications_args_1.NotificationType.MembershipRequested:
                return (0, exports.encodePathParams)(Routes.organisationSettingsEmployees, {
                    organisationId: (_l = (_k = notification === null || notification === void 0 ? void 0 : notification.membership) === null || _k === void 0 ? void 0 : _k.organisation) === null || _l === void 0 ? void 0 : _l.id,
                });
            case notifications_args_1.NotificationType.MembershipAccepted:
            case notifications_args_1.NotificationType.ParentMembershipAccepted:
            case notifications_args_1.NotificationType.ParentMembershipDeclined:
                return (0, exports.encodePathParams)(Routes.profile);
            case notifications_args_1.NotificationType.MembershipPermissionsUpdated:
                return (0, exports.encodePathParams)(Routes.organisationSettingsUserRoles, {
                    organisationId: (_o = (_m = notification === null || notification === void 0 ? void 0 : notification.membership) === null || _m === void 0 ? void 0 : _m.organisation) === null || _o === void 0 ? void 0 : _o.id,
                });
            case notifications_args_1.NotificationType.FollowerAccepted:
                return (0, exports.encodePathParams)(Routes.organisationProfile, {
                    vanityId: (_q = (_p = notification === null || notification === void 0 ? void 0 : notification.follower) === null || _p === void 0 ? void 0 : _p.organisation) === null || _q === void 0 ? void 0 : _q.vanityId,
                });
            case notifications_args_1.NotificationType.IncentiveDateChanged:
            case notifications_args_1.NotificationType.IncentiveInvitationByHosts:
            case notifications_args_1.NotificationType.IncentiveInvitationByParticipant:
            case notifications_args_1.NotificationType.IncentiveRegistrationApproved:
            case notifications_args_1.NotificationType.NewIncentiveUpdate:
                return (0, exports.encodePathParams)(Routes.organisationIncentive, {
                    vanityId: (_r = notification.incentiveParticipant) === null || _r === void 0 ? void 0 : _r.incentive.organisation.vanityId,
                    incentiveId: (_t = (_s = notification.incentiveParticipant) === null || _s === void 0 ? void 0 : _s.incentive) === null || _t === void 0 ? void 0 : _t.id,
                });
            case notifications_args_1.NotificationType.IncentiveRegistrationRequested:
                return (0, exports.encodePathParams)(Routes.organisationIncentive, {
                    vanityId: (_u = notification.incentiveParticipant) === null || _u === void 0 ? void 0 : _u.incentive.organisation.vanityId,
                    incentiveId: (_w = (_v = notification.incentiveParticipant) === null || _v === void 0 ? void 0 : _v.incentive) === null || _w === void 0 ? void 0 : _w.id,
                });
            case notifications_args_1.NotificationType.WebinarDateChanged:
            case notifications_args_1.NotificationType.WebinarInvitationByHosts:
            case notifications_args_1.NotificationType.WebinarInvitationByParticipant:
            case notifications_args_1.NotificationType.WebinarRegistrationApproved:
            case notifications_args_1.NotificationType.NewWebinarUpdate:
            case notifications_args_1.NotificationType.WebinarRegistrationRequested:
            case notifications_args_1.NotificationType.WebinarParticipantAddedAsHostAdmin:
            case notifications_args_1.NotificationType.WebinarParticipantAddedAsHost:
            case notifications_args_1.NotificationType.WebinarParticipantAddedAsSpeaker:
                return (0, exports.encodePathParams)(Routes.organisationWebinar, {
                    vanityId: (_x = notification.webinarParticipant) === null || _x === void 0 ? void 0 : _x.webinar.organisation.vanityId,
                    id: (_z = (_y = notification.webinarParticipant) === null || _y === void 0 ? void 0 : _y.webinar) === null || _z === void 0 ? void 0 : _z.id,
                });
            case notifications_args_1.NotificationType.SuggestFollow:
                return (0, exports.getFullPathFromRoute)(Routes.exploreOrganisationsYouFollow);
            case notifications_args_1.NotificationType.PostComment:
                return (0, exports.encodePathParams)(Routes.organisationPost, {
                    vanityId: (_0 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _0 === void 0 ? void 0 : _0.id,
                    id: (_1 = notification === null || notification === void 0 ? void 0 : notification.data) === null || _1 === void 0 ? void 0 : _1.activityId,
                    userId: (_2 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _2 === void 0 ? void 0 : _2.id,
                });
            case notifications_args_1.NotificationType.CommentReact:
                return (0, exports.encodePathParams)(Routes.organisationPost, {
                    vanityId: (_3 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _3 === void 0 ? void 0 : _3.id,
                    id: (_4 = notification === null || notification === void 0 ? void 0 : notification.data) === null || _4 === void 0 ? void 0 : _4.activityId,
                    userId: (_5 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _5 === void 0 ? void 0 : _5.id,
                });
            case notifications_args_1.NotificationType.PostReact:
                return (0, exports.encodePathParams)(Routes.organisationPost, {
                    vanityId: (_6 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _6 === void 0 ? void 0 : _6.id,
                    id: (_7 = notification === null || notification === void 0 ? void 0 : notification.data) === null || _7 === void 0 ? void 0 : _7.activityId,
                    userId: (_8 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _8 === void 0 ? void 0 : _8.id,
                });
            case notifications_args_1.NotificationType.PostShared:
                return (0, exports.encodePathParams)(Routes.organisationPost, {
                    vanityId: (_9 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _9 === void 0 ? void 0 : _9.id,
                    id: (_10 = notification === null || notification === void 0 ? void 0 : notification.data) === null || _10 === void 0 ? void 0 : _10.activityId,
                    userId: (_11 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _11 === void 0 ? void 0 : _11.id,
                });
            case notifications_args_1.NotificationType.CommentMention:
                return (0, exports.encodePathParams)(Routes.organisationPost, {
                    vanityId: (_12 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _12 === void 0 ? void 0 : _12.id,
                    id: (_13 = notification === null || notification === void 0 ? void 0 : notification.data) === null || _13 === void 0 ? void 0 : _13.activityId,
                    userId: (_14 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _14 === void 0 ? void 0 : _14.id,
                });
            case notifications_args_1.NotificationType.PostMention:
                return (0, exports.encodePathParams)(Routes.organisationPost, {
                    vanityId: (_15 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _15 === void 0 ? void 0 : _15.id,
                    id: (_16 = notification === null || notification === void 0 ? void 0 : notification.data) === null || _16 === void 0 ? void 0 : _16.activityId,
                    userId: (_17 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _17 === void 0 ? void 0 : _17.id,
                });
            case notifications_args_1.NotificationType.OrgPostMention:
                return (0, exports.encodePathParams)(Routes.organisationPost, {
                    vanityId: (_18 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _18 === void 0 ? void 0 : _18.vanityId,
                    id: (_19 = notification === null || notification === void 0 ? void 0 : notification.data) === null || _19 === void 0 ? void 0 : _19.activityId,
                    userId: (_20 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _20 === void 0 ? void 0 : _20.id,
                });
            case notifications_args_1.NotificationType.OrgCommentMention:
                return (0, exports.encodePathParams)(Routes.organisationPost, {
                    vanityId: (_21 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _21 === void 0 ? void 0 : _21.vanityId,
                    id: (_22 = notification === null || notification === void 0 ? void 0 : notification.data) === null || _22 === void 0 ? void 0 : _22.activityId,
                    userId: (_23 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _23 === void 0 ? void 0 : _23.id,
                });
            case notifications_args_1.NotificationType.NewFollower:
                return (0, exports.encodePathParams)(Routes.organisationProfile, {
                    vanityId: (_24 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _24 === void 0 ? void 0 : _24.vanityId,
                }, 'modal=followers');
            case notifications_args_1.NotificationType.EventInvitationByGuest:
            case notifications_args_1.NotificationType.EventInvitationByHosts:
            case notifications_args_1.NotificationType.EventInvitationApproved:
            case notifications_args_1.NotificationType.NewEventUpdate:
            case notifications_args_1.NotificationType.EventLocationChanged:
            case notifications_args_1.NotificationType.EventDateTimeChanged:
            default: {
                return (0, exports.encodePathParams)(Routes.organisationEvent, {
                    vanityId: (_27 = (_26 = (_25 = notification === null || notification === void 0 ? void 0 : notification.eventInvitation) === null || _25 === void 0 ? void 0 : _25.event) === null || _26 === void 0 ? void 0 : _26.organisation) === null || _27 === void 0 ? void 0 : _27.vanityId,
                    eventId: (_29 = (_28 = notification === null || notification === void 0 ? void 0 : notification.eventInvitation) === null || _28 === void 0 ? void 0 : _28.event) === null || _29 === void 0 ? void 0 : _29.id,
                });
            }
            case notifications_args_1.NotificationType.ParentMembershipRequested:
                return (0, exports.encodePathParams)(Routes.organisationSettingsMembers, {
                    organisationId: (_31 = (_30 = notification === null || notification === void 0 ? void 0 : notification.membership) === null || _30 === void 0 ? void 0 : _30.organisation) === null || _31 === void 0 ? void 0 : _31.id,
                });
            case notifications_args_1.NotificationType.OrgPostReminder7Days:
            case notifications_args_1.NotificationType.OrgPostReminder14Days:
                return (0, exports.getFullPathFromRoute)(Routes.organisationNewPost);
            case notifications_args_1.NotificationType.InactiveUserReminder:
                return (0, exports.getFullPathFromRoute)(Routes.home);
            case notifications_args_1.NotificationType.InboxMessage:
                return (0, exports.encodePathParams)(Routes.inboxChat, {
                    chatId: (_32 = notification === null || notification === void 0 ? void 0 : notification.data) === null || _32 === void 0 ? void 0 : _32.streamChannelId,
                });
            case notifications_args_1.NotificationType.DailyLoginStreak:
                return (0, exports.getFullPathFromRoute)(Routes.home);
            case notifications_args_1.NotificationType.NewAchievement:
            case notifications_args_1.NotificationType.NewOrganisationAchievement:
                return (0, exports.getFullPathFromRoute)(Routes.clubHabloDashboard);
            case notifications_args_1.NotificationType.OrgPartnerRequestReceived:
            case notifications_args_1.NotificationType.OrgPartnerAcceptedSender:
            case notifications_args_1.NotificationType.OrgPartnerAcceptedReceiver:
                return (0, exports.encodePathParams)(Routes.organisationSettingsConnections, {
                    organisationId: (_33 = notification.data) === null || _33 === void 0 ? void 0 : _33.receiverOrgId,
                });
        }
    }
    static getText(notification) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, _16, _17, _18, _19, _20, _21, _22, _23, _24, _25, _26, _27, _28, _29, _30, _31, _32, _33, _34, _35, _36, _37, _38, _39, _40, _41, _42, _43, _44, _45, _46, _47, _48, _49, _50, _51, _52, _53, _54, _55, _56, _57, _58, _59, _60, _61, _62, _63, _64, _65, _66, _67, _68, _69, _70, _71, _72, _73, _74, _75, _76, _77, _78, _79, _80, _81, _82, _83, _84, _85, _86, _87, _88, _89, _90, _91, _92, _93, _94, _95, _96, _97, _98, _99, _100, _101, _102, _103, _104, _105, _106, _107, _108, _109, _110, _111, _112, _113, _114, _115, _116, _117, _118, _119, _120, _121, _122, _123, _124, _125, _126, _127, _128, _129, _130, _131, _132, _133, _134, _135, _136, _137, _138, _139, _140, _141, _142, _143, _144, _145, _146, _147, _148, _149, _150;
        switch (notification.type) {
            case notifications_args_1.NotificationType.InvitationAccepted: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${'You are now connected with'} </span>
            <b style="text-decoration:none">${(_a = notification.profile) === null || _a === void 0 ? void 0 : _a.name}</b>
          </p>`;
            }
            case notifications_args_1.NotificationType.InvitationReceived: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_b = notification.profile) === null || _b === void 0 ? void 0 : _b.name}</b>
            <span style="text-decoration:none">
              ${' '}
              ${'wants to connect with you. View your pending connection invitations.'}
            </span>
          </p>`;
            }
            case notifications_args_1.NotificationType.OrganisationOwnershipRequested: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_c = notification.profile) === null || _c === void 0 ? void 0 : _c.name}</b>
            <span style="text-decoration:none"> ${'has requested you take over as Owner of'} </span>
            <span style="text-decoration:none">${(_e = (_d = notification.membership) === null || _d === void 0 ? void 0 : _d.organisation) === null || _e === void 0 ? void 0 : _e.name}.</span>
            <span style="text-decoration:none"> ${'View this request to accept or reject it.'}</span>
          </p>`;
            }
            case notifications_args_1.NotificationType.OrganisationOwnershipAccepted: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_f = notification.profile) === null || _f === void 0 ? void 0 : _f.name}</b>
            <span style="text-decoration:none"> ${'has agreed to become Owner of'} </span>
            <span style="text-decoration:none">${(_h = (_g = notification.membership) === null || _g === void 0 ? void 0 : _g.organisation) === null || _h === void 0 ? void 0 : _h.name}.</span>
            <span style="text-decoration:none"> ${'You have reverted to an Admin role.'}</span>
          </p>`;
            }
            case notifications_args_1.NotificationType.PartnershipRequestReceived: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_k = (_j = notification.partnershipRequest) === null || _j === void 0 ? void 0 : _j.senderOrganisation) === null || _k === void 0 ? void 0 : _k.name}</b>
            <span style="text-decoration:none"> ${'has requested to become a Connected Organisation with'} </span>
            <b style="text-decoration:none">${(_m = (_l = notification.partnershipRequest) === null || _l === void 0 ? void 0 : _l.receiverOrganisation) === null || _m === void 0 ? void 0 : _m.name}</b>
            <span style="text-decoration:none"> ${'View this request.'}</span>
          </p>`;
            }
            case notifications_args_1.NotificationType.PartnershipRequestApproved: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_p = (_o = notification.partnershipRequest) === null || _o === void 0 ? void 0 : _o.receiverOrganisation) === null || _p === void 0 ? void 0 : _p.name}</b>
            <span style="text-decoration:none"> ${'approved a connection request from'} </span>
            <span style="text-decoration:none">${(_r = (_q = notification.partnershipRequest) === null || _q === void 0 ? void 0 : _q.senderOrganisation) === null || _r === void 0 ? void 0 : _r.name}.</span>
          </p>`;
            }
            case notifications_args_1.NotificationType.PartnershipRequestApprovedByOrganisation: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_t = (_s = notification.partnershipRequest) === null || _s === void 0 ? void 0 : _s.senderOrganisation) === null || _t === void 0 ? void 0 : _t.name}</b>
            <span style="text-decoration:none"> ${'is now a Connected Organisation with'} </span>
            <b style="text-decoration:none">${(_v = (_u = notification.partnershipRequest) === null || _u === void 0 ? void 0 : _u.receiverOrganisation) === null || _v === void 0 ? void 0 : _v.name}</b>
          </p>`;
            }
            case notifications_args_1.NotificationType.MembershipRequested: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_w = notification.profile) === null || _w === void 0 ? void 0 : _w.name}</b>
            <span style="text-decoration:none"> ${'has requested to join'} </span>
            <b style="text-decoration:none">${(_y = (_x = notification.membership) === null || _x === void 0 ? void 0 : _x.organisation) === null || _y === void 0 ? void 0 : _y.name}</b>
            <span style="text-decoration:none"> ${'View this request to confirm their employment'}.</span>
          </p>`;
            }
            case notifications_args_1.NotificationType.MembershipAccepted: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${'You have been verified as an employee of'} </span>
            <b style="text-decoration:none">${(_0 = (_z = notification.membership) === null || _z === void 0 ? void 0 : _z.organisation) === null || _0 === void 0 ? void 0 : _0.name}</b>
          </p>`;
            }
            case notifications_args_1.NotificationType.FollowerAccepted: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_2 = (_1 = notification.follower) === null || _1 === void 0 ? void 0 : _1.organisation) === null || _2 === void 0 ? void 0 : _2.name}</b>
            <span style="text-decoration:none">
              ${' '}
              ${'has accepted your follow request. You can now explore their full page'}.
            </span>
          </p>`;
            }
            case notifications_args_1.NotificationType.MembershipPermissionsUpdated: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              ${'You have been assigned as'}${(((_3 = notification === null || notification === void 0 ? void 0 : notification.data) === null || _3 === void 0 ? void 0 : _3.permissions) ||
                    ((_4 = notification.membership) === null || _4 === void 0 ? void 0 : _4.permissions) ||
                    []).join(', ')}${' of  '}
            </span>
            <b style="text-decoration:none">${(_6 = (_5 = notification.membership) === null || _5 === void 0 ? void 0 : _5.organisation) === null || _6 === void 0 ? void 0 : _6.name}</b>
          </p>`;
            }
            case notifications_args_1.NotificationType.EventInvitationByGuest: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_8 = (_7 = notification.eventInvitation) === null || _7 === void 0 ? void 0 : _7.inviterProfile) === null || _8 === void 0 ? void 0 : _8.name}</b>
            <span style="text-decoration:none"> ${'has invited you to an event:'} </span>
            <b style="text-decoration:none">${(_10 = (_9 = notification.eventInvitation) === null || _9 === void 0 ? void 0 : _9.event) === null || _10 === void 0 ? void 0 : _10.name}</b>
          </p>`;
            }
            case notifications_args_1.NotificationType.EventInvitationByHosts: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_13 = (_12 = (_11 = notification.eventInvitation) === null || _11 === void 0 ? void 0 : _11.event) === null || _12 === void 0 ? void 0 : _12.organisation) === null || _13 === void 0 ? void 0 : _13.name}</b>
            <span style="text-decoration:none"> ${'has invited you to an event:'} </span>
            <b style="text-decoration:none">${(_15 = (_14 = notification.eventInvitation) === null || _14 === void 0 ? void 0 : _14.event) === null || _15 === void 0 ? void 0 : _15.name}</b>
          </p>`;
            }
            case notifications_args_1.NotificationType.EventInvitationApproved: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_18 = (_17 = (_16 = notification.eventInvitation) === null || _16 === void 0 ? void 0 : _16.event) === null || _17 === void 0 ? void 0 : _17.organisation) === null || _18 === void 0 ? void 0 : _18.name}</b>
            <span style="text-decoration:none"> ${'has accepted your request to attend'} </span>
            <b style="text-decoration:none">${(_20 = (_19 = notification.eventInvitation) === null || _19 === void 0 ? void 0 : _19.event) === null || _20 === void 0 ? void 0 : _20.name}</b>
          </p>`;
            }
            case notifications_args_1.NotificationType.NewEventUpdate: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_23 = (_22 = (_21 = notification.eventInvitation) === null || _21 === void 0 ? void 0 : _21.event) === null || _22 === void 0 ? void 0 : _22.organisation) === null || _23 === void 0 ? void 0 : _23.name}</b>
            <span style="text-decoration:none"> ${'has posted an update in'} </span>
            <b style="text-decoration:none">${(_25 = (_24 = notification.eventInvitation) === null || _24 === void 0 ? void 0 : _24.event) === null || _25 === void 0 ? void 0 : _25.name}</b>
          </p>`;
            }
            case notifications_args_1.NotificationType.EventLocationChanged:
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_28 = (_27 = (_26 = notification.eventInvitation) === null || _26 === void 0 ? void 0 : _26.event) === null || _27 === void 0 ? void 0 : _27.organisation) === null || _28 === void 0 ? void 0 : _28.name}</b>
            <span style="text-decoration:none"> ${'has updated the location of'} </span>
            <b style="text-decoration:none">${(_30 = (_29 = notification.eventInvitation) === null || _29 === void 0 ? void 0 : _29.event) === null || _30 === void 0 ? void 0 : _30.name}</b>
          </p>`;
            case notifications_args_1.NotificationType.EventDateTimeChanged:
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_33 = (_32 = (_31 = notification.eventInvitation) === null || _31 === void 0 ? void 0 : _31.event) === null || _32 === void 0 ? void 0 : _32.organisation) === null || _33 === void 0 ? void 0 : _33.name}</b>
            <span style="text-decoration:none"> ${'has made a change to the date / time of'} </span>
            <b style="text-decoration:none">${(_35 = (_34 = notification.eventInvitation) === null || _34 === void 0 ? void 0 : _34.event) === null || _35 === void 0 ? void 0 : _35.name}</b>
          </p>`;
            case notifications_args_1.NotificationType.IncentiveDateChanged:
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_38 = (_37 = (_36 = notification.incentiveParticipant) === null || _36 === void 0 ? void 0 : _36.incentive) === null || _37 === void 0 ? void 0 : _37.organisation) === null || _38 === void 0 ? void 0 : _38.name}</b>
            <span style="text-decoration:none"> ${'has made a change to the dates of'} </span>
            <b style="text-decoration:none">${(_40 = (_39 = notification.incentiveParticipant) === null || _39 === void 0 ? void 0 : _39.incentive) === null || _40 === void 0 ? void 0 : _40.name}</b>
          </p>`;
            case notifications_args_1.NotificationType.IncentiveInvitationByParticipant:
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_42 = (_41 = notification.incentiveParticipant) === null || _41 === void 0 ? void 0 : _41.inviterProfile) === null || _42 === void 0 ? void 0 : _42.name}</b>
            <span style="text-decoration:none"> ${'has invited you to an incentive:'} </span>
            <b style="text-decoration:none">${(_44 = (_43 = notification.incentiveParticipant) === null || _43 === void 0 ? void 0 : _43.incentive) === null || _44 === void 0 ? void 0 : _44.name}</b>
          </p>`;
            case notifications_args_1.NotificationType.IncentiveInvitationByHosts:
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_47 = (_46 = (_45 = notification.incentiveParticipant) === null || _45 === void 0 ? void 0 : _45.incentive) === null || _46 === void 0 ? void 0 : _46.organisation) === null || _47 === void 0 ? void 0 : _47.name}</b>
            <span style="text-decoration:none"> ${'has invited you to an incentive:'} </span>
            <b style="text-decoration:none">${(_49 = (_48 = notification.incentiveParticipant) === null || _48 === void 0 ? void 0 : _48.incentive) === null || _49 === void 0 ? void 0 : _49.name}</b>
          </p>`;
            case notifications_args_1.NotificationType.IncentiveRegistrationRequested:
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_51 = (_50 = notification.incentiveParticipant) === null || _50 === void 0 ? void 0 : _50.profile) === null || _51 === void 0 ? void 0 : _51.name}</b>
            <span style="text-decoration:none"> ${'has requested to register in'} </span>
            <b style="text-decoration:none">${(_53 = (_52 = notification.incentiveParticipant) === null || _52 === void 0 ? void 0 : _52.incentive) === null || _53 === void 0 ? void 0 : _53.name}:</b>
            <span style="text-decoration:none"> ${'View this request to confirm their participation'}.</span>
          </p>`;
            case notifications_args_1.NotificationType.IncentiveRegistrationApproved:
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_56 = (_55 = (_54 = notification.incentiveParticipant) === null || _54 === void 0 ? void 0 : _54.incentive) === null || _55 === void 0 ? void 0 : _55.organisation) === null || _56 === void 0 ? void 0 : _56.name}</b>
            <span style="text-decoration:none"> ${'has accepted your request to register'} </span>
            <b style="text-decoration:none">${(_58 = (_57 = notification.incentiveParticipant) === null || _57 === void 0 ? void 0 : _57.incentive) === null || _58 === void 0 ? void 0 : _58.name}</b>
          </p>`;
            case notifications_args_1.NotificationType.NewIncentiveUpdate: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_61 = (_60 = (_59 = notification.incentiveParticipant) === null || _59 === void 0 ? void 0 : _59.incentive) === null || _60 === void 0 ? void 0 : _60.organisation) === null || _61 === void 0 ? void 0 : _61.name}</b>
            <span style="text-decoration:none"> ${'has posted an update in'} </span>
            <b style="text-decoration:none">${(_63 = (_62 = notification.incentiveParticipant) === null || _62 === void 0 ? void 0 : _62.incentive) === null || _63 === void 0 ? void 0 : _63.name}</b>
          </p>`;
            }
            case notifications_args_1.NotificationType.WebinarDateChanged:
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_66 = (_65 = (_64 = notification.webinarParticipant) === null || _64 === void 0 ? void 0 : _64.webinar) === null || _65 === void 0 ? void 0 : _65.organisation) === null || _66 === void 0 ? void 0 : _66.name}</b>
            <span style="text-decoration:none"> ${'has made a change to the date / time of'} </span>
            <b style="text-decoration:none">${(_68 = (_67 = notification.webinarParticipant) === null || _67 === void 0 ? void 0 : _67.webinar) === null || _68 === void 0 ? void 0 : _68.name}</b>
          </p>`;
            case notifications_args_1.NotificationType.WebinarInvitationByParticipant:
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_70 = (_69 = notification.webinarParticipant) === null || _69 === void 0 ? void 0 : _69.inviterProfile) === null || _70 === void 0 ? void 0 : _70.name}</b>
            <span style="text-decoration:none"> ${'has invited you to a webinar:'} </span>
            <b style="text-decoration:none">${(_72 = (_71 = notification.webinarParticipant) === null || _71 === void 0 ? void 0 : _71.webinar) === null || _72 === void 0 ? void 0 : _72.name}</b>
          </p>`;
            case notifications_args_1.NotificationType.WebinarInvitationByHosts:
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_75 = (_74 = (_73 = notification.webinarParticipant) === null || _73 === void 0 ? void 0 : _73.webinar) === null || _74 === void 0 ? void 0 : _74.organisation) === null || _75 === void 0 ? void 0 : _75.name}</b>
            <span style="text-decoration:none"> ${'has invited you to a webinar:'} </span>
            <b style="text-decoration:none">${(_77 = (_76 = notification.webinarParticipant) === null || _76 === void 0 ? void 0 : _76.webinar) === null || _77 === void 0 ? void 0 : _77.name}</b>
          </p>`;
            case notifications_args_1.NotificationType.WebinarRegistrationRequested:
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_79 = (_78 = notification.webinarParticipant) === null || _78 === void 0 ? void 0 : _78.profile) === null || _79 === void 0 ? void 0 : _79.name}</b>
            <span style="text-decoration:none"> ${'has requested to register in'} </span>
            <b style="text-decoration:none">${(_81 = (_80 = notification.webinarParticipant) === null || _80 === void 0 ? void 0 : _80.webinar) === null || _81 === void 0 ? void 0 : _81.name}</b>
            <span style="text-decoration:none"> ${'View this request to confirm their participation'}.</span>
          </p>`;
            case notifications_args_1.NotificationType.WebinarRegistrationApproved:
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_84 = (_83 = (_82 = notification.webinarParticipant) === null || _82 === void 0 ? void 0 : _82.webinar) === null || _83 === void 0 ? void 0 : _83.organisation) === null || _84 === void 0 ? void 0 : _84.name}</b>
            <span style="text-decoration:none"> ${'has accepted your request to register'} </span>
            <b style="text-decoration:none">${(_86 = (_85 = notification.webinarParticipant) === null || _85 === void 0 ? void 0 : _85.webinar) === null || _86 === void 0 ? void 0 : _86.name}</b>
          </p>`;
            case notifications_args_1.NotificationType.NewWebinarUpdate: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_89 = (_88 = (_87 = notification.webinarParticipant) === null || _87 === void 0 ? void 0 : _87.webinar) === null || _88 === void 0 ? void 0 : _88.organisation) === null || _89 === void 0 ? void 0 : _89.name}</b>
            <span style="text-decoration:none"> ${'has posted an update in'} </span>
            <b style="text-decoration:none">${(_91 = (_90 = notification.webinarParticipant) === null || _90 === void 0 ? void 0 : _90.webinar) === null || _91 === void 0 ? void 0 : _91.name}</b>
          </p>`;
            }
            case notifications_args_1.NotificationType.WebinarParticipantAddedAsHostAdmin: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_94 = (_93 = (_92 = notification.webinarParticipant) === null || _92 === void 0 ? void 0 : _92.webinar) === null || _93 === void 0 ? void 0 : _93.organisation) === null || _94 === void 0 ? void 0 : _94.name}</b>
            <span style="text-decoration:none"> ${'has assigned you as a Host Admin of'} </span>
            <b style="text-decoration:none">${(_96 = (_95 = notification.webinarParticipant) === null || _95 === void 0 ? void 0 : _95.webinar) === null || _96 === void 0 ? void 0 : _96.name}</b>
          </p>`;
            }
            case notifications_args_1.NotificationType.WebinarParticipantAddedAsHost: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_99 = (_98 = (_97 = notification.webinarParticipant) === null || _97 === void 0 ? void 0 : _97.webinar) === null || _98 === void 0 ? void 0 : _98.organisation) === null || _99 === void 0 ? void 0 : _99.name}</b>
            <span style="text-decoration:none"> ${'has assigned you as a Host of'} </span>
            <b style="text-decoration:none">${(_101 = (_100 = notification.webinarParticipant) === null || _100 === void 0 ? void 0 : _100.webinar) === null || _101 === void 0 ? void 0 : _101.name}</b>
          </p>`;
            }
            case notifications_args_1.NotificationType.WebinarParticipantAddedAsSpeaker: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_104 = (_103 = (_102 = notification.webinarParticipant) === null || _102 === void 0 ? void 0 : _102.webinar) === null || _103 === void 0 ? void 0 : _103.organisation) === null || _104 === void 0 ? void 0 : _104.name}</b>
            <span style="text-decoration:none"> ${'has assigned you as a Speaker at'} </span>
            <b style="text-decoration:none">${(_106 = (_105 = notification.webinarParticipant) === null || _105 === void 0 ? void 0 : _105.webinar) === null || _106 === void 0 ? void 0 : _106.name}</b>
          </p>`;
            }
            case notifications_args_1.NotificationType.SuggestFollow: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              ${'You’ve had a suggestion to follow some new organisations that travel industry professionals similar to you also follow. Click here to check them out. You can unfollow at any time.'}${' '}
            </span>
          </p>`;
            }
            case notifications_args_1.NotificationType.PostComment: {
                const usersFullNames = notification.data.users.map(({ name }) => name);
                const countOfUsers = notification.data.users.length;
                const objectOfStrings = (0, exports.getStringsForNotifications)(countOfUsers, usersFullNames);
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              <strong style="text-transform: capitalize;">
                ${objectOfStrings.usersFullNamesString} ${objectOfStrings.other}
              </strong>
              ${objectOfStrings.has}
              ${' ' +
                    'commented on your post. Click here to reply or like their comment.'}
            </span>
          </p>`;
            }
            case notifications_args_1.NotificationType.CommentReact: {
                const usersFullNames = notification.data.users.map(({ name }) => name);
                const countOfUsers = notification.data.users.length;
                const objectOfStrings = (0, exports.getStringsForNotifications)(countOfUsers, usersFullNames);
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              <strong style="text-transform: capitalize;">
                ${objectOfStrings.usersFullNamesString} ${objectOfStrings.other}
              </strong>
              ${objectOfStrings.has}
              ${' ' +
                    'reacted to your comment on ' +
                    ((_108 = (_107 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _107 === void 0 ? void 0 : _107.name) !== null && _108 !== void 0 ? _108 : '') +
                    '’s post.'}
            </span>
          </p>`;
            }
            case notifications_args_1.NotificationType.PostReact: {
                const usersFullNames = notification.data.users.map(({ name }) => name);
                const countOfUsers = notification.data.users.length;
                const objectOfStrings = (0, exports.getStringsForNotifications)(countOfUsers, usersFullNames);
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              <strong style="text-transform: capitalize;">
                ${objectOfStrings.usersFullNamesString} ${objectOfStrings.other}
              </strong>
              ${objectOfStrings.has}
              ${' ' + 'reacted to your post. Click here to view your post.'}
            </span>
          </p>`;
            }
            case notifications_args_1.NotificationType.PostShared: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              <strong style="text-transform: capitalize;">${(_109 = notification.organisation) === null || _109 === void 0 ? void 0 : _109.name}</strong>${' '}
              ${'has reshared your post with their network.'}
            </span>
          </p>`;
            }
            case notifications_args_1.NotificationType.NewFollower: {
                const usersFullNames = (_111 = (_110 = notification.data) === null || _110 === void 0 ? void 0 : _110.users) === null || _111 === void 0 ? void 0 : _111.map(({ name }) => name);
                const countOfUsers = (_113 = (_112 = notification.data) === null || _112 === void 0 ? void 0 : _112.users) === null || _113 === void 0 ? void 0 : _113.length;
                const objectOfStrings = (0, exports.getStringsForNotifications)(countOfUsers, usersFullNames);
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              <strong style="text-transform: capitalize;">
                ${objectOfStrings.usersFullNamesString} ${objectOfStrings.other}
              </strong>
              ${objectOfStrings.has}
              ${' ' + 'requested to follow '}
              <strong>${(_115 = (_114 = notification === null || notification === void 0 ? void 0 : notification.organisation) === null || _114 === void 0 ? void 0 : _114.name) !== null && _115 !== void 0 ? _115 : ''}</strong>.
            </span>
          </p>`;
            }
            case notifications_args_1.NotificationType.CommentMention: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              <strong style="text-transform: capitalize;">${(_116 = notification.profile) === null || _116 === void 0 ? void 0 : _116.name}</strong>${' '}
              ${'mentioned you in a comment. Click here to reply or like the comment.'}
            </span>
          </p>`;
            }
            case notifications_args_1.NotificationType.PostMention: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              <strong style="text-transform: capitalize;">${(_117 = notification.organisation) === null || _117 === void 0 ? void 0 : _117.name}</strong>${' '}
              ${'mentioned you in a post. Click here to view the post.'}
            </span>
          </p>`;
            }
            case notifications_args_1.NotificationType.OrgPostMention: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              <strong style="text-transform: capitalize;">${(_118 = notification.organisation) === null || _118 === void 0 ? void 0 : _118.name}</strong>${' mentioned '}
              <strong style="text-transform: capitalize;">${(_120 = (_119 = notification === null || notification === void 0 ? void 0 : notification.data) === null || _119 === void 0 ? void 0 : _119.organisation) === null || _120 === void 0 ? void 0 : _120.name}</strong>
              ${' in a post. Click here to view the post.'}
            </span>
          </p>`;
            }
            case notifications_args_1.NotificationType.OrgCommentMention: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              <strong style="text-transform: capitalize;">${(_121 = notification.profile) === null || _121 === void 0 ? void 0 : _121.name}</strong>${' mentioned '}
              <strong style="text-transform: capitalize;">${(_123 = (_122 = notification === null || notification === void 0 ? void 0 : notification.data) === null || _122 === void 0 ? void 0 : _122.organisation) === null || _123 === void 0 ? void 0 : _123.name}</strong>
              ${' in a comment. Click here to view the post.'}
            </span>
          </p>`;
            }
            case notifications_args_1.NotificationType.RecurringPaymentReminder: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none"> 
            ${'Your paid subscription with' + ' '} </span>    
            <strong style="text-transform: capitalize;">${(_125 = (_124 = notification.partnershipRequest) === null || _124 === void 0 ? void 0 : _124.senderOrganisation) === null || _125 === void 0 ? void 0 : _125.name}</strong>${' '}
            <span style="text-decoration:none"> ${'will renew on' + ' '} </span>
            <b style="text-decoration:none">${(_128 = (_127 = (_126 = notification.partnershipRequest) === null || _126 === void 0 ? void 0 : _126.subscription) === null || _127 === void 0 ? void 0 : _127.lastTransaction) === null || _128 === void 0 ? void 0 : _128.endDate}</b>${' '}
            <span style="text-decoration:none"> ${'Please ensure that your active payment method is still valid.'} </span>
          </p>`;
            }
            case notifications_args_1.NotificationType.MembershipAutoApprove: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_129 = notification.profile) === null || _129 === void 0 ? void 0 : _129.name}</b>${' '}
            <span style="text-decoration:none"> ${'has joined' + ' '} </span>
            <b style="text-decoration:none">${(_131 = (_130 = notification.membership) === null || _130 === void 0 ? void 0 : _130.organisation) === null || _131 === void 0 ? void 0 : _131.name}</b>${' '}
            <span style="text-decoration:none"> ${'using a signup invite link'}.</span>
          </p>`;
            }
            case notifications_args_1.NotificationType.ParentMembershipRequested: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${(_132 = notification.profile) === null || _132 === void 0 ? void 0 : _132.name}</b>
            <span style="text-decoration:none"> ${'has requested to become a member of'} </span>
            <b style="text-decoration:none">${(_134 = (_133 = notification.membership) === null || _133 === void 0 ? void 0 : _133.organisation) === null || _134 === void 0 ? void 0 : _134.name}</b>
            <span style="text-decoration:none"> ${'View this request to confirm their membership'}.</span>
          </p>`;
            }
            case notifications_args_1.NotificationType.ParentMembershipAccepted: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${'You have been verified as an member of'} </span>
            <b style="text-decoration:none">${(_136 = (_135 = notification.membership) === null || _135 === void 0 ? void 0 : _135.organisation) === null || _136 === void 0 ? void 0 : _136.name}</b>
          </p>`;
            }
            case notifications_args_1.NotificationType.ParentMembershipDeclined: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${'You have been declined as an member of'} </span>
            <b style="text-decoration:none">${(_137 = notification.organisation) === null || _137 === void 0 ? void 0 : _137.name}</b>
          </p>`;
            }
            case notifications_args_1.NotificationType.OrgPostReminder7Days: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${'It’s been a week since '} </span>
            <b style="text-decoration:none">${(_138 = notification.organisation) === null || _138 === void 0 ? void 0 : _138.name}</b>
            <span style="text-decoration:none"> ${'’s last post. Create a post to keep your community engaged'}</span>
          </p>`;
            }
            case notifications_args_1.NotificationType.OrgPostReminder14Days: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${'It’s been two weeks since '} </span>
            <b style="text-decoration:none">${(_139 = notification.organisation) === null || _139 === void 0 ? void 0 : _139.name}</b>
            <span style="text-decoration:none"> ${'’s last post and your community is waiting to hear from you. Create your next post now'}</span>
          </p>`;
            }
            case notifications_args_1.NotificationType.InactiveUserReminder: {
                const organisationNames = (_140 = notification.data) === null || _140 === void 0 ? void 0 : _140.map(({ organisationName }) => organisationName);
                const countOfOrgs = organisationNames.length;
                const objectOfStrings = (0, exports.getOrgNameStringsForNotifications)(countOfOrgs, organisationNames);
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">
              ${objectOfStrings.organisationNamesString} </b>
              ${objectOfStrings.has}
            <span style="text-decoration:none"> ${'shared new posts recently. View the latest updates in your feed'}.</span>
          </p>`;
            }
            case notifications_args_1.NotificationType.InboxMessage: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${(_141 = notification.profile) === null || _141 === void 0 ? void 0 : _141.name} </span>
            <span style="text-decoration:none">${': '} </span>
            <b style="text-decoration:none">${(_142 = notification.data) === null || _142 === void 0 ? void 0 : _142.text}</b>
          </p>`;
            }
            case notifications_args_1.NotificationType.DailyLoginStreak: {
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${"You're on a roll! You hit a daily streak of "} </span>
            <b style="text-decoration:none">${(0, lodash_1.get)(notification, 'data.streakCount', 0)}</b>
            <span style="text-decoration:none"> ${' days.'}</span>
          </p>`;
            }
            case notifications_args_1.NotificationType.NewAchievement: {
                const achievementType = (0, lodash_1.get)(notification, 'data.achievementType', '');
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${"Congratulations! You've unlocked the "} </span>
            <b style="text-decoration:none">${achievementType}</b>
            <span style="text-decoration:none"> ${' achievement.'}</span>
          </p>`;
            }
            case notifications_args_1.NotificationType.NewOrganisationAchievement: {
                const achievementType = (0, lodash_1.get)(notification, 'data.achievementType', '');
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${"Congratulations! You've unlocked the "} </span>
            <b style="text-decoration:none">${notification.organisation.name} ${achievementType}</b>
            <span style="text-decoration:none"> ${' achievement.'}</span>
          </p>`;
            }
            case notifications_args_1.NotificationType.HighFiveAchievement: {
                const achievementType = (0, lodash_1.get)(notification, 'data.achievementType', '');
                return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${'Nice work! You received a '}
            <b style="text-decoration:none">
            ${'High Five'}</b>
            ${' from'} </span>
            <b style="text-decoration:none">${(_143 = notification.organisation) === null || _143 === void 0 ? void 0 : _143.name}</b>.
          </p>`;
            }
            case notifications_args_1.NotificationType.NewTier: {
                const currentTier = (0, lodash_1.get)(notification, 'data.currentTier', '');
                const nextTier = (0, lodash_1.get)(notification, 'data.nextTier', '');
                const isTierRetained = (0, lodash_1.get)(notification, 'data.isTierRetained', '');
                if (currentTier === 'Blue') {
                    return `
            <p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              Welcome to the <b>Club Hablo!</b> Keep earning Kudos to move up to Silver Tier! 🥈
            </span>
          </p>`;
                }
                if (isTierRetained) {
                    return `
            <p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              Congratulations ${(_144 = notification.profile) === null || _144 === void 0 ? void 0 : _144.name} you have retained ${currentTier} tier!${nextTier ? ` 🥳 Next stop ${nextTier}` : ''}
            </span>
          </p>`;
                }
                return `
          <p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              Congratulations ${(_145 = notification.profile) === null || _145 === void 0 ? void 0 : _145.name} you are now a ${currentTier} tier member!${nextTier ? ` 🥳 Next stop ${nextTier}` : ''}
            </span>
          </p>`;
            }
            case notifications_args_1.NotificationType.WeeklySummary: {
                const pointsEarned = (0, lodash_1.get)(notification, 'data.pointsEarned', '');
                const rollingPoints = (0, lodash_1.get)(notification, 'data.rollingPoints', '');
                const nextTier = (0, lodash_1.get)(notification, 'data.nextTier', '');
                const nextTierPoints = (0, lodash_1.get)(notification, 'data.nextTierPoints', '');
                const userName = (0, lodash_1.get)(notification, 'profile.name', '');
                return `
          <p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              Congratulations ${userName} you have earned ${pointsEarned} points last week.${nextTier
                    ? ` You need ${nextTierPoints - rollingPoints} points to reach ${nextTier} tier.`
                    : ''}
            </span>
          </p>`;
            }
            case notifications_args_1.NotificationType.OrgPartnerRequestReceived:
                return `${(_146 = notification.data) === null || _146 === void 0 ? void 0 : _146.senderOrgName} has requested to become a Connected Organisation with ${(_147 = notification.data) === null || _147 === void 0 ? void 0 : _147.receiverOrgName}. View this request.`;
            case notifications_args_1.NotificationType.OrgPartnerAcceptedSender:
                return `${(_148 = notification.data) === null || _148 === void 0 ? void 0 : _148.receiverOrgName} has approved your request to become a Connected Organisation.`;
            case notifications_args_1.NotificationType.OrgPartnerAcceptedReceiver:
                return `${(_149 = notification.data) === null || _149 === void 0 ? void 0 : _149.senderOrgName} is now a Connected Organisation with ${(_150 = notification.data) === null || _150 === void 0 ? void 0 : _150.receiverOrgName}.`;
            default: {
                return `<div></div>`;
            }
        }
    }
    static getIcon(notification) {
        switch (notification.type) {
            case notifications_args_1.NotificationType.InvitationAccepted:
            case notifications_args_1.NotificationType.InvitationReceived:
                return Icons.users;
            case notifications_args_1.NotificationType.PartnershipRequestReceived:
            case notifications_args_1.NotificationType.PartnershipRequestApproved:
            case notifications_args_1.NotificationType.PartnershipRequestApprovedByOrganisation:
            case notifications_args_1.NotificationType.MembershipRequested:
            case notifications_args_1.NotificationType.MembershipAutoApprove:
            case notifications_args_1.NotificationType.RecurringPaymentReminder:
            case notifications_args_1.NotificationType.ParentMembershipRequested:
                return Icons.security;
            case notifications_args_1.NotificationType.MembershipAccepted:
            case notifications_args_1.NotificationType.FollowerAccepted:
            case notifications_args_1.NotificationType.MembershipPermissionsUpdated:
            case notifications_args_1.NotificationType.OrganisationOwnershipAccepted:
            case notifications_args_1.NotificationType.OrganisationOwnershipRequested:
            case notifications_args_1.NotificationType.ParentMembershipAccepted:
                return Icons.explore;
            case notifications_args_1.NotificationType.EventInvitationByGuest:
            case notifications_args_1.NotificationType.EventInvitationByHosts:
            case notifications_args_1.NotificationType.EventInvitationApproved:
            case notifications_args_1.NotificationType.NewEventUpdate:
            case notifications_args_1.NotificationType.EventLocationChanged:
            case notifications_args_1.NotificationType.EventDateTimeChanged:
                return Icons.calendar;
            case notifications_args_1.NotificationType.IncentiveDateChanged:
            case notifications_args_1.NotificationType.IncentiveInvitationByHosts:
            case notifications_args_1.NotificationType.IncentiveInvitationByParticipant:
            case notifications_args_1.NotificationType.IncentiveRegistrationApproved:
            case notifications_args_1.NotificationType.IncentiveRegistrationRequested:
            case notifications_args_1.NotificationType.NewIncentiveUpdate:
                return Icons.incentives;
            case notifications_args_1.NotificationType.WebinarDateChanged:
            case notifications_args_1.NotificationType.WebinarInvitationByHosts:
            case notifications_args_1.NotificationType.WebinarInvitationByParticipant:
            case notifications_args_1.NotificationType.WebinarRegistrationApproved:
            case notifications_args_1.NotificationType.WebinarRegistrationRequested:
            case notifications_args_1.NotificationType.NewWebinarUpdate:
            case notifications_args_1.NotificationType.WebinarParticipantAddedAsHostAdmin:
            case notifications_args_1.NotificationType.WebinarParticipantAddedAsHost:
            case notifications_args_1.NotificationType.WebinarParticipantAddedAsSpeaker:
                return Icons.webinar;
            case notifications_args_1.NotificationType.PostShared:
                return Icons.posts;
            case notifications_args_1.NotificationType.PostComment:
            case notifications_args_1.NotificationType.CommentMention:
                return Icons.comment;
            case notifications_args_1.NotificationType.CommentReact:
                return Icons.like;
            case notifications_args_1.NotificationType.PostReact:
                return Icons.like;
            case notifications_args_1.NotificationType.OrgPartnerRequestReceived:
            case notifications_args_1.NotificationType.OrgPartnerAcceptedSender:
            case notifications_args_1.NotificationType.OrgPartnerAcceptedReceiver:
                return Icons.users;
            default: {
                return Icons.time;
            }
        }
    }
    static getFullImageUrl(imgShortcode) {
        const highImageSize = 'w_92';
        const placeholderImage = `${CLOUDINARY_URL}/${highImageSize},c_fill,q_90,b_rgb:E9EEF1,f_auto/pknnbg0x7jcg6cngpjkc`;
        if (!imgShortcode)
            return placeholderImage;
        if (imgShortcode.includes('http'))
            return imgShortcode;
        return `${CLOUDINARY_URL}/${highImageSize},c_fill,q_90,b_rgb:fff,f_auto/${imgShortcode}`;
    }
    static getNotificationLayout(notification) {
        return `
      <tr>
        <td style="vertical-align: top;">
          <a href="${notification.route}"
            style="text-decoration:none !important; text-decoration:none; display: block;"
          >
            <img
              width="56px"
              height="56px"
              src="${this.getFullImageUrl(notification.image)}"
              style="border-radius:18px;overflow:hidden;z-index:5;border:none;"
             />
          </a>
        </td>
        <td style="vertical-align: top; padding-bottom: 15px;">
          <a href="${notification.route}"
            style="text-decoration:none !important; text-decoration:none;"
          >
            <div style="padding: 0 12px; color: #303030; text-align: left;line-height: 20px;">
              ${notification.text}
              <div style="display: inline; line-height: 16px; vertical-align: bottom;">
                <img src="${notification.icon}" width="16px" height="16px" style="margin-top: 3px; margin-right: 3px; display: inline; vertical-align: bottom;" />
                <div style="color: #0093c7; font-size: 13px; display: inline; vertical-align: bottom;">${FromNow(notification.createdAt)}</div>
              </div>
            </div>
          </a>
        </td>
      </tr>
      `;
    }
}
exports.NotificationHelper = NotificationHelper;
//# sourceMappingURL=notificationLayoutHelper.js.map