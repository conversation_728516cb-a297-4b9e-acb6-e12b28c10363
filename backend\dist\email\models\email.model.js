"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Email = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const sequelize_1 = require("sequelize");
const graphql_1 = require("@nestjs/graphql");
const short_uuid_1 = __importDefault(require("short-uuid"));
const profile_model_1 = require("../../profiles/models/profile.model");
const email_args_1 = require("../args/email.args");
let Email = class Email extends sequelize_typescript_1.Model {
};
exports.Email = Email;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], Email.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(() => email_args_1.EmailTemplate, { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], Email.prototype, "templateId", void 0);
__decorate([
    (0, graphql_1.Field)(() => email_args_1.EmailStatus, { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
        defaultValue: email_args_1.EmailStatus.Sent,
    }),
    __metadata("design:type", String)
], Email.prototype, "status", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Email.prototype, "profileId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, 'profileId'),
    __metadata("design:type", profile_model_1.Profile)
], Email.prototype, "profile", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Email.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Email.prototype, "updatedAt", void 0);
exports.Email = Email = __decorate([
    sequelize_typescript_1.Table,
    (0, graphql_1.ObjectType)()
], Email);
//# sourceMappingURL=email.model.js.map