"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventInvitationsBulkInsertArgs = exports.EventInvitationsArgs = exports.EventInvitationsFilter = exports.EventInvitationSentStatus = exports.EventInvitationType = exports.EventInvitationStatus = void 0;
const graphql_1 = require("@nestjs/graphql");
var EventInvitationStatus;
(function (EventInvitationStatus) {
    EventInvitationStatus["Attending"] = "Attending";
    EventInvitationStatus["Interested"] = "Interested";
    EventInvitationStatus["Declined"] = "Declined";
    EventInvitationStatus["InvitedByGuest"] = "InvitedByGuest";
    EventInvitationStatus["InvitedByHost"] = "InvitedByHost";
    EventInvitationStatus["InvitedAttending"] = "InvitedAttending";
    EventInvitationStatus["InvitedInterested"] = "InvitedInterested";
    EventInvitationStatus["InvitedDeclined"] = "InvitedDeclined";
    EventInvitationStatus["Blocked"] = "Blocked";
})(EventInvitationStatus || (exports.EventInvitationStatus = EventInvitationStatus = {}));
(0, graphql_1.registerEnumType)(EventInvitationStatus, { name: 'EventInvitationStatus' });
var EventInvitationType;
(function (EventInvitationType) {
    EventInvitationType["Hosts"] = "Hosts";
    EventInvitationType["Guest"] = "Guest";
})(EventInvitationType || (exports.EventInvitationType = EventInvitationType = {}));
var EventInvitationSentStatus;
(function (EventInvitationSentStatus) {
    EventInvitationSentStatus["Pending"] = "Pending";
    EventInvitationSentStatus["Sent"] = "Sent";
})(EventInvitationSentStatus || (exports.EventInvitationSentStatus = EventInvitationSentStatus = {}));
(0, graphql_1.registerEnumType)(EventInvitationSentStatus, {
    name: 'EventInvitationSentStatus',
});
let EventInvitationsFilter = class EventInvitationsFilter {
};
exports.EventInvitationsFilter = EventInvitationsFilter;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID, {
        nullable: true,
    }),
    __metadata("design:type", String)
], EventInvitationsFilter.prototype, "profileId", void 0);
__decorate([
    (0, graphql_1.Field)(() => [EventInvitationStatus], {
        nullable: true,
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], EventInvitationsFilter.prototype, "status", void 0);
exports.EventInvitationsFilter = EventInvitationsFilter = __decorate([
    (0, graphql_1.InputType)()
], EventInvitationsFilter);
let EventInvitationsArgs = class EventInvitationsArgs {
};
exports.EventInvitationsArgs = EventInvitationsArgs;
__decorate([
    (0, graphql_1.Field)(() => EventInvitationsFilter, { nullable: true }),
    __metadata("design:type", EventInvitationsFilter)
], EventInvitationsArgs.prototype, "filter", void 0);
exports.EventInvitationsArgs = EventInvitationsArgs = __decorate([
    (0, graphql_1.ArgsType)()
], EventInvitationsArgs);
let EventInvitationsBulkInsertArgs = class EventInvitationsBulkInsertArgs {
};
exports.EventInvitationsBulkInsertArgs = EventInvitationsBulkInsertArgs;
__decorate([
    (0, graphql_1.Field)(() => [Object], { nullable: false }),
    __metadata("design:type", Array)
], EventInvitationsBulkInsertArgs.prototype, "bulkInsertData", void 0);
exports.EventInvitationsBulkInsertArgs = EventInvitationsBulkInsertArgs = __decorate([
    (0, graphql_1.ArgsType)()
], EventInvitationsBulkInsertArgs);
//# sourceMappingURL=event-invitations.args.js.map