"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventInvitationsResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const event_invitation_model_1 = require("./models/event-invitation.model");
const events_service_1 = require("../events/events.service");
const event_model_1 = require("../events/models/event.model");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const event_invitations_service_1 = require("./event-invitations.service");
const memberships_service_rights_1 = require("../memberships/helpers/memberships.service.rights");
const error_1 = require("../common/helpers/error");
const create_event_invitations_input_1 = require("./dto/create-event-invitations.input");
const organisation_model_1 = require("../organisations/models/organisation.model");
const organisations_service_1 = require("../organisations/organisations.service");
const profile_model_1 = require("../profiles/models/profile.model");
const profiles_service_1 = require("../profiles/profiles.service");
const event_invitations_args_1 = require("./args/event-invitations.args");
const sequelize_1 = require("sequelize");
let EventInvitationsResolver = class EventInvitationsResolver {
    async eventInvitation(user, id) {
        this.logger.verbose('EventInvitationsResolver.eventInvitation (query)', {
            user: user.toLogObject(),
            id,
        });
        const eventInvitation = await this.eventInvitationsService.findById(id);
        if (!eventInvitation) {
            throw new Error(`EventInvitation not found`);
        }
        return eventInvitation;
    }
    async createEventInvitations(user, eventInvitationsInput) {
        this.logger.verbose('EventInvitationsResolver.createEventInvitations (mutation)', {
            user: user.toLogObject(),
            eventInvitationsInput,
        });
        const event = await this.eventsService.findById(eventInvitationsInput.eventId);
        if (!event) {
            this.errorHelper.throwHttpException(`EventInvitationsResolver.createEventInvitation`, 'Event not found');
        }
        const eventInvitationType = await this.eventInvitationsService.verifyInvitePermission(user, event);
        if (eventInvitationType === event_invitations_args_1.EventInvitationType.Guest) {
            let countInvites = await this.eventInvitationsService.count({
                eventId: eventInvitationsInput.eventId,
                inviterProfileId: user.profileId,
                createdAt: new Date(),
            });
            if (countInvites >= 50) {
                this.errorHelper.throwHttpException(`EventInvitationsResolver.createEventInvitation`, 'Max participant invite limit reached.');
            }
        }
        return await this.eventInvitationsService.createEventInvitations(user, eventInvitationsInput, eventInvitationType);
    }
    async removeEventInvitation(user, id) {
        this.logger.verbose('EventInvitationsResolver.removeEventInvitation (mutation)', {
            user: user.toLogObject(),
            id,
        });
        const eventInvitation = await this.eventInvitationsService.findById(id, {
            includeParams: [
                {
                    model: event_model_1.Event,
                    as: 'event',
                    attributes: ['organisationId'],
                },
            ],
        });
        if (!eventInvitation) {
            this.errorHelper.throwHttpException(`EventInvitationsResolver.removeEventInvitation`, 'Event Invitation not found');
        }
        await this.membershipsServiceRights.checkRights(eventInvitation.event.organisationId, {
            currentProfileId: user.profileId,
            rights: [memberships_service_rights_1.MembershipRight.RemoveEventInvitation],
        });
        await this.eventInvitationsService.remove({ where: { id } });
        return true;
    }
    updateEventInvitationStatus(user, id, status) {
        this.logger.verbose('EventInvitationsResolver.updateEventInvitationStatus (mutation)', {
            user: user.toLogObject(),
            id,
        });
        return this.eventInvitationsService.updateEventInvitationStatus(user, id, status);
    }
    async event(eventInvitation) {
        if (eventInvitation.event)
            return eventInvitation.event;
        this.logger.verbose('EventInvitationsResolver.event (field resolver)', {
            eventInvitationId: eventInvitation.id,
        });
        return this.eventsService.findById(eventInvitation.eventId, {
            useCache: true,
        });
    }
    async organisation(eventInvitation) {
        if (eventInvitation.organisation)
            return eventInvitation.organisation;
        this.logger.verbose('EventInvitationsResolver.organisation (field resolver)', {
            eventInvitationId: eventInvitation.id,
        });
        return this.organisationsService.findById(eventInvitation.organisationId, {
            useCache: true,
        });
    }
    async profile(eventInvitation) {
        if (eventInvitation.profile)
            return eventInvitation.profile;
        this.logger.verbose('EventInvitationsResolver.profile (field resolver)', {
            eventInvitationId: eventInvitation.id,
        });
        return this.profilesService.findById(eventInvitation.profileId);
    }
    async inviterProfile(eventInvitation) {
        if (eventInvitation.inviterProfile)
            return eventInvitation.inviterProfile;
        if (!eventInvitation.inviterProfileId)
            return null;
        this.logger.verbose('EventInvitationsResolver.inviterProfile (field resolver)', {
            eventInvitationId: eventInvitation.id,
        });
        return this.profilesService.findById(eventInvitation.inviterProfileId);
    }
    async eventShareCount(user, eventId) {
        this.logger.verbose('EventInvitationsResolver.eventShareCount (mutation)', {
            user: user.toLogObject(),
            eventId,
        });
        return await this.eventInvitationsService.count({
            [sequelize_1.Op.and]: [
                sequelize_1.Sequelize.where(sequelize_1.Sequelize.fn('DATE', sequelize_1.Sequelize.col('createdAt')), sequelize_1.Sequelize.literal('CURRENT_DATE')),
                {
                    eventId,
                    inviterProfileId: user.profileId,
                },
            ],
        });
    }
};
exports.EventInvitationsResolver = EventInvitationsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => event_invitations_service_1.EventInvitationsService)),
    __metadata("design:type", event_invitations_service_1.EventInvitationsService)
], EventInvitationsResolver.prototype, "eventInvitationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => events_service_1.EventsService)),
    __metadata("design:type", events_service_1.EventsService)
], EventInvitationsResolver.prototype, "eventsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_rights_1.MembershipsServiceRights)),
    __metadata("design:type", memberships_service_rights_1.MembershipsServiceRights)
], EventInvitationsResolver.prototype, "membershipsServiceRights", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], EventInvitationsResolver.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], EventInvitationsResolver.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], EventInvitationsResolver.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], EventInvitationsResolver.prototype, "errorHelper", void 0);
__decorate([
    (0, graphql_1.Query)(() => event_invitation_model_1.EventInvitation),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id', { nullable: false })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], EventInvitationsResolver.prototype, "eventInvitation", null);
__decorate([
    (0, graphql_1.Mutation)(() => [event_invitation_model_1.EventInvitation]),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('eventInvitationData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_event_invitations_input_1.CreateEventInvitationsInput]),
    __metadata("design:returntype", Promise)
], EventInvitationsResolver.prototype, "createEventInvitations", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], EventInvitationsResolver.prototype, "removeEventInvitation", null);
__decorate([
    (0, graphql_1.Mutation)(() => event_invitation_model_1.EventInvitation),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id', { nullable: false })),
    __param(2, (0, graphql_1.Args)('status', { type: () => event_invitations_args_1.EventInvitationStatus, nullable: false })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], EventInvitationsResolver.prototype, "updateEventInvitationStatus", null);
__decorate([
    (0, graphql_1.ResolveField)('event', () => event_model_1.Event),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [event_invitation_model_1.EventInvitation]),
    __metadata("design:returntype", Promise)
], EventInvitationsResolver.prototype, "event", null);
__decorate([
    (0, graphql_1.ResolveField)('organisation', () => organisation_model_1.Organisation, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [event_invitation_model_1.EventInvitation]),
    __metadata("design:returntype", Promise)
], EventInvitationsResolver.prototype, "organisation", null);
__decorate([
    (0, graphql_1.ResolveField)('profile', () => profile_model_1.Profile),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [event_invitation_model_1.EventInvitation]),
    __metadata("design:returntype", Promise)
], EventInvitationsResolver.prototype, "profile", null);
__decorate([
    (0, graphql_1.ResolveField)('inviterProfile', () => profile_model_1.Profile, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [event_invitation_model_1.EventInvitation]),
    __metadata("design:returntype", Promise)
], EventInvitationsResolver.prototype, "inviterProfile", null);
__decorate([
    (0, graphql_1.Query)(() => Number),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('eventId', { nullable: false })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], EventInvitationsResolver.prototype, "eventShareCount", null);
exports.EventInvitationsResolver = EventInvitationsResolver = __decorate([
    (0, graphql_1.Resolver)(() => event_invitation_model_1.EventInvitation)
], EventInvitationsResolver);
//# sourceMappingURL=event-invitations.resolver.js.map