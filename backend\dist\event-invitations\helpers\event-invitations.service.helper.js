"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventInvitationsServiceHelper = void 0;
const sequelize_1 = require("sequelize");
const common_1 = require("@nestjs/common");
let EventInvitationsServiceHelper = class EventInvitationsServiceHelper {
    static getQueryParams(eventInvitationArgs) {
        var _a, _b, _c, _d;
        const queryParams = {};
        if ((_a = eventInvitationArgs === null || eventInvitationArgs === void 0 ? void 0 : eventInvitationArgs.filter) === null || _a === void 0 ? void 0 : _a.organisationId) {
            queryParams['organisationId'] = eventInvitationArgs.filter.organisationId;
        }
        if ((_b = eventInvitationArgs === null || eventInvitationArgs === void 0 ? void 0 : eventInvitationArgs.filter) === null || _b === void 0 ? void 0 : _b.profileId) {
            queryParams['profileId'] = eventInvitationArgs.filter.profileId;
        }
        if (((_d = (_c = eventInvitationArgs === null || eventInvitationArgs === void 0 ? void 0 : eventInvitationArgs.filter) === null || _c === void 0 ? void 0 : _c.status) === null || _d === void 0 ? void 0 : _d.length) > 0) {
            queryParams['status'] = {
                [sequelize_1.Op.or]: eventInvitationArgs.filter.status,
            };
        }
        return queryParams;
    }
};
exports.EventInvitationsServiceHelper = EventInvitationsServiceHelper;
exports.EventInvitationsServiceHelper = EventInvitationsServiceHelper = __decorate([
    (0, common_1.Injectable)()
], EventInvitationsServiceHelper);
//# sourceMappingURL=event-invitations.service.helper.js.map