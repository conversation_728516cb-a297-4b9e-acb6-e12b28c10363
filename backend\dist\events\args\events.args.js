"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventsArgs = exports.EventsFilter = exports.EventType = void 0;
const graphql_1 = require("@nestjs/graphql");
const pagination_args_1 = require("../../common/args/pagination.args");
const event_invitations_args_1 = require("../../event-invitations/args/event-invitations.args");
var EventType;
(function (EventType) {
    EventType["Event"] = "Event";
    EventType["IncentiveStart"] = "IncentiveStart";
    EventType["IncentiveEnd"] = "IncentiveEnd";
    EventType["Webinar"] = "Webinar";
})(EventType || (exports.EventType = EventType = {}));
(0, graphql_1.registerEnumType)(EventType, { name: 'EventType' });
let EventsFilter = class EventsFilter {
};
exports.EventsFilter = EventsFilter;
__decorate([
    (0, graphql_1.Field)(() => [event_invitations_args_1.EventInvitationStatus], { nullable: true, defaultValue: [] }),
    __metadata("design:type", Array)
], EventsFilter.prototype, "eventInvitationStatus", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], EventsFilter.prototype, "organisationId", void 0);
__decorate([
    (0, graphql_1.Field)(() => [EventType], { nullable: true, defaultValue: [] }),
    __metadata("design:type", Array)
], EventsFilter.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true, defaultValue: null }),
    __metadata("design:type", Boolean)
], EventsFilter.prototype, "isOnline", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true, defaultValue: null }),
    __metadata("design:type", Boolean)
], EventsFilter.prototype, "isPublic", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], EventsFilter.prototype, "searchText", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true, defaultValue: null }),
    __metadata("design:type", Boolean)
], EventsFilter.prototype, "isEnded", void 0);
exports.EventsFilter = EventsFilter = __decorate([
    (0, graphql_1.InputType)()
], EventsFilter);
let EventsArgs = class EventsArgs extends pagination_args_1.PaginationArgs {
};
exports.EventsArgs = EventsArgs;
__decorate([
    (0, graphql_1.Field)(() => EventsFilter, { nullable: true }),
    __metadata("design:type", EventsFilter)
], EventsArgs.prototype, "filter", void 0);
exports.EventsArgs = EventsArgs = __decorate([
    (0, graphql_1.ArgsType)()
], EventsArgs);
//# sourceMappingURL=events.args.js.map