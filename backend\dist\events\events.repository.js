"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventsRepository = void 0;
const common_1 = require("@nestjs/common");
const event_model_1 = require("./models/event.model");
const pagination_1 = require("../common/helpers/pagination");
const sequelize_1 = require("sequelize");
const events_repository_helper_1 = require("./helpers/events.repository.helper");
const fuse_js_1 = __importDefault(require("fuse.js"));
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
let EventsRepository = class EventsRepository {
    async findEvents(profileId, isHeadOrganisationRole, filter, pagination) {
        const eventInvitationStatus = (filter === null || filter === void 0 ? void 0 : filter.eventInvitationStatus) || [];
        const eventInvitationIds = await this.helper.getEventInvitationEventIds(profileId, {
            status: eventInvitationStatus.length > 0 ? eventInvitationStatus : null,
        });
        if ((filter === null || filter === void 0 ? void 0 : filter.isPublic) === false && eventInvitationIds.length === 0) {
            return {
                records: [],
                totalCount: 0,
            };
        }
        const extraQueryParams = {
            [sequelize_1.Op.or]: [
                {
                    id: {
                        [sequelize_1.Op.in]: eventInvitationIds,
                    },
                },
            ],
        };
        if (eventInvitationStatus.length === 0) {
            extraQueryParams[sequelize_1.Op.or].push({ isPublic: true });
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.isPublic) === true || (filter === null || filter === void 0 ? void 0 : filter.isPublic) === false) {
            extraQueryParams.isPublic = filter.isPublic;
        }
        if (isHeadOrganisationRole) {
            extraQueryParams[sequelize_1.Op.or].push({ isPublic: false });
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.type) && filter.type.length > 0) {
            extraQueryParams.type = {
                [sequelize_1.Op.in]: filter.type,
            };
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.isOnline) === true || (filter === null || filter === void 0 ? void 0 : filter.isOnline) === false) {
            extraQueryParams.isOnline = filter === null || filter === void 0 ? void 0 : filter.isOnline;
        }
        if (filter === null || filter === void 0 ? void 0 : filter.organisationId) {
            extraQueryParams.organisationId = filter.organisationId;
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.isEnded) === true) {
            extraQueryParams.endDate = {
                [sequelize_1.Op.lt]: new Date(),
            };
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.isEnded) === false || (filter === null || filter === void 0 ? void 0 : filter.searchText)) {
            extraQueryParams.endDate = {
                [sequelize_1.Op.gte]: new Date(),
            };
        }
        const result = await new pagination_1.PaginationHelper().getPaginatedResults({
            model: event_model_1.Event,
            pagination,
            extraQueryParams,
            excludeIds: [],
            searchText: filter === null || filter === void 0 ? void 0 : filter.searchText,
        });
        if (filter === null || filter === void 0 ? void 0 : filter.searchText) {
            const options = {
                includeScore: true,
                includeMatches: true,
                ignoreLocation: true,
                threshold: 0.4,
                keys: ['name', 'description'],
            };
            const fuse = new fuse_js_1.default(result.records, options);
            const filteringRecords = fuse.search(filter === null || filter === void 0 ? void 0 : filter.searchText);
            const filteredRecords = filteringRecords.filter(item => item.score < options.threshold);
            const splicedRecords = !!(pagination === null || pagination === void 0 ? void 0 : pagination.first)
                ? filteringRecords.splice((pagination === null || pagination === void 0 ? void 0 : pagination.offset) || 0, pagination === null || pagination === void 0 ? void 0 : pagination.first)
                : filteringRecords;
            const finalRecords = splicedRecords.map(result => result.item);
            const finalData = {
                records: finalRecords,
                totalCount: filteredRecords.length,
            };
            return finalData;
        }
        return result;
    }
};
exports.EventsRepository = EventsRepository;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => events_repository_helper_1.EventsRepositoryHelper)),
    __metadata("design:type", events_repository_helper_1.EventsRepositoryHelper)
], EventsRepository.prototype, "helper", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], EventsRepository.prototype, "logger", void 0);
exports.EventsRepository = EventsRepository = __decorate([
    (0, common_1.Injectable)()
], EventsRepository);
//# sourceMappingURL=events.repository.js.map