"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventsResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const event_model_1 = require("./models/event.model");
const events_service_1 = require("./events.service");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const create_event_input_1 = require("./dto/create-event.input");
const paginated_result_1 = require("../common/args/paginated-result");
const events_args_1 = require("./args/events.args");
const update_event_input_1 = require("./dto/update-event.input");
const memberships_service_rights_1 = require("../memberships/helpers/memberships.service.rights");
const organisations_service_1 = require("../organisations/organisations.service");
const organisation_model_1 = require("../organisations/models/organisation.model");
const event_invitation_model_1 = require("../event-invitations/models/event-invitation.model");
const event_invitations_service_1 = require("../event-invitations/event-invitations.service");
const event_invitations_args_1 = require("../event-invitations/args/event-invitations.args");
const event_invitations_service_helper_1 = require("../event-invitations/helpers/event-invitations.service.helper");
const error_1 = require("../common/helpers/error");
const incentive_model_1 = require("../incentives/models/incentive.model");
const incentives_service_1 = require("../incentives/incentives.service");
const webinar_model_1 = require("../webinars/models/webinar.model");
const webinars_service_1 = require("../webinars/webinars.service");
let EventsResolver = class EventsResolver {
    async event(user, id) {
        this.logger.verbose('EventsResolver.event (query)', {
            user: user.toLogObject(),
            id,
        });
        const event = await this.eventsService.findById(id);
        if (!event) {
            this.errorHelper.throwHttpException(`EventsResolver.event`, `Sorry, this event does not exist.`);
        }
        return event;
    }
    calendarEvents(user) {
        this.logger.verbose('EventsResolver.calednarEvents (query)', {
            user: user.toLogObject(),
        });
        return this.eventsService.findCalendarEvents(user.profileId);
    }
    events(user, eventArgs) {
        var _a, _b, _c, _d, _e, _f, _g;
        this.logger.verbose('EventsResolver.events (query)', {
            user: user.toLogObject(),
            eventArgs,
        });
        return this.eventsService.findEvents(user.profileId, {
            eventInvitationStatus: (_a = eventArgs === null || eventArgs === void 0 ? void 0 : eventArgs.filter) === null || _a === void 0 ? void 0 : _a.eventInvitationStatus,
            organisationId: (_b = eventArgs === null || eventArgs === void 0 ? void 0 : eventArgs.filter) === null || _b === void 0 ? void 0 : _b.organisationId,
            type: (_c = eventArgs === null || eventArgs === void 0 ? void 0 : eventArgs.filter) === null || _c === void 0 ? void 0 : _c.type,
            isOnline: (_d = eventArgs === null || eventArgs === void 0 ? void 0 : eventArgs.filter) === null || _d === void 0 ? void 0 : _d.isOnline,
            isPublic: (_e = eventArgs === null || eventArgs === void 0 ? void 0 : eventArgs.filter) === null || _e === void 0 ? void 0 : _e.isPublic,
            searchText: (_f = eventArgs === null || eventArgs === void 0 ? void 0 : eventArgs.filter) === null || _f === void 0 ? void 0 : _f.searchText,
            isEnded: (_g = eventArgs === null || eventArgs === void 0 ? void 0 : eventArgs.filter) === null || _g === void 0 ? void 0 : _g.isEnded,
        }, {
            first: eventArgs === null || eventArgs === void 0 ? void 0 : eventArgs.first,
            after: eventArgs === null || eventArgs === void 0 ? void 0 : eventArgs.after,
            sortBy: eventArgs === null || eventArgs === void 0 ? void 0 : eventArgs.sortBy,
            sortOrder: eventArgs === null || eventArgs === void 0 ? void 0 : eventArgs.sortOrder,
        });
    }
    async createEvent(user, eventData) {
        this.logger.verbose('EventsResolver.createEvent (mutation)', {
            user: user.toLogObject(),
            eventData,
        });
        const { organisationId } = eventData;
        await this.membershipsServiceRights.checkRights(organisationId, {
            currentProfileId: user.profileId,
            rights: [memberships_service_rights_1.MembershipRight.CreateEvent],
        });
        eventData.description &&
            (eventData.description = eventData.description.replace(/\n\s*\n\s*\n/g, '\n\n'));
        return this.eventsService.create(eventData);
    }
    async updateEvent(user, eventId, eventData) {
        this.logger.verbose('EventsResolver.updateEvent (mutation)', {
            user: user.toLogObject(),
            eventData,
        });
        const event = await this.eventsService.findById(eventId);
        await this.membershipsServiceRights.checkRights(event.organisationId, {
            currentProfileId: user.profileId,
            rights: [memberships_service_rights_1.MembershipRight.UpdateEvent],
        });
        return this.eventsService.updateEvent(user, eventId, eventData);
    }
    async removeEvent(user, eventId) {
        this.logger.verbose('EventsResolver.removeEvent (mutation)', {
            user: user.toLogObject(),
            eventId,
        });
        const event = await this.eventsService.findById(eventId);
        if (!event)
            return true;
        await this.membershipsServiceRights.checkRights(event.organisationId, {
            currentProfileId: user.profileId,
            rights: [memberships_service_rights_1.MembershipRight.RemoveEvent],
        });
        await this.eventsService.remove({ where: { id: eventId } });
        return true;
    }
    rsvpEvent(user, eventId, status) {
        this.logger.verbose('EventsResolver.rsvpEvent (mutation)', {
            user: user.toLogObject(),
            eventId,
            status,
        });
        return this.eventsService.rsvpEvent(user, eventId, {
            status,
        });
    }
    async organisation(event) {
        if (event.organisation)
            return event.organisation;
        this.logger.verbose('EventsResolver.organisation (field resolver)', {
            eventId: event.id,
        });
        return this.organisationsService.findById(event.organisationId, {
            useCache: true,
        });
    }
    async invitation(user, event) {
        this.logger.verbose('EventsResolver.invitation (field resolver)', {
            eventId: event.id,
        });
        return this.eventInvitationsService.findOne({
            eventId: event.id,
            profileId: user.profileId,
        }, { useCache: true });
    }
    async invitations(event, eventInvitationsArgs) {
        this.logger.verbose('EventsResolver.invitations (field resolver)', {
            eventId: event.id,
        });
        return this.eventInvitationsService.findAll(Object.assign({ eventId: event.id }, event_invitations_service_helper_1.EventInvitationsServiceHelper.getQueryParams(eventInvitationsArgs)), { useCache: true });
    }
    async incentive(event) {
        if (event.incentive)
            return event.incentive;
        this.logger.verbose('EventsResolver.incentive (field resolver)', {
            eventId: event.id,
        });
        return this.incentivesService.findById(event.incentiveId);
    }
    async webinar(event) {
        if (event.webinar)
            return event.webinar;
        this.logger.verbose('EventsResolver.webinar (field resolver)', {
            eventId: event.id,
        });
        return this.webinarsService.findById(event.webinarId);
    }
};
exports.EventsResolver = EventsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => events_service_1.EventsService)),
    __metadata("design:type", events_service_1.EventsService)
], EventsResolver.prototype, "eventsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => event_invitations_service_1.EventInvitationsService)),
    __metadata("design:type", event_invitations_service_1.EventInvitationsService)
], EventsResolver.prototype, "eventInvitationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_rights_1.MembershipsServiceRights)),
    __metadata("design:type", memberships_service_rights_1.MembershipsServiceRights)
], EventsResolver.prototype, "membershipsServiceRights", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], EventsResolver.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentives_service_1.IncentivesService)),
    __metadata("design:type", incentives_service_1.IncentivesService)
], EventsResolver.prototype, "incentivesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinars_service_1.WebinarsService)),
    __metadata("design:type", webinars_service_1.WebinarsService)
], EventsResolver.prototype, "webinarsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], EventsResolver.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], EventsResolver.prototype, "errorHelper", void 0);
__decorate([
    (0, graphql_1.Query)(() => event_model_1.Event),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id', { nullable: false })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], EventsResolver.prototype, "event", null);
__decorate([
    (0, graphql_1.Query)(() => [event_model_1.Event]),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], EventsResolver.prototype, "calendarEvents", null);
__decorate([
    (0, graphql_1.Query)(() => paginated_result_1.EventsResult),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, events_args_1.EventsArgs]),
    __metadata("design:returntype", Promise)
], EventsResolver.prototype, "events", null);
__decorate([
    (0, graphql_1.Mutation)(() => event_model_1.Event),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('eventData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_event_input_1.CreateEventInput]),
    __metadata("design:returntype", Promise)
], EventsResolver.prototype, "createEvent", null);
__decorate([
    (0, graphql_1.Mutation)(() => event_model_1.Event),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('eventId')),
    __param(2, (0, graphql_1.Args)('eventData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, update_event_input_1.UpdateEventInput]),
    __metadata("design:returntype", Promise)
], EventsResolver.prototype, "updateEvent", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('eventId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], EventsResolver.prototype, "removeEvent", null);
__decorate([
    (0, graphql_1.Mutation)(() => event_invitation_model_1.EventInvitation),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('eventId')),
    __param(2, (0, graphql_1.Args)('status', {
        type: () => event_invitations_args_1.EventInvitationStatus,
        defaultValue: event_invitations_args_1.EventInvitationStatus.Attending,
    })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], EventsResolver.prototype, "rsvpEvent", null);
__decorate([
    (0, graphql_1.ResolveField)('organisation', () => organisation_model_1.Organisation),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [event_model_1.Event]),
    __metadata("design:returntype", Promise)
], EventsResolver.prototype, "organisation", null);
__decorate([
    (0, graphql_1.ResolveField)('invitation', () => event_invitation_model_1.EventInvitation, { nullable: true }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, event_model_1.Event]),
    __metadata("design:returntype", Promise)
], EventsResolver.prototype, "invitation", null);
__decorate([
    (0, graphql_1.ResolveField)('invitations', () => [event_invitation_model_1.EventInvitation]),
    __param(0, (0, graphql_1.Parent)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [event_model_1.Event,
        event_invitations_args_1.EventInvitationsArgs]),
    __metadata("design:returntype", Promise)
], EventsResolver.prototype, "invitations", null);
__decorate([
    (0, graphql_1.ResolveField)('incentive', () => incentive_model_1.Incentive, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [event_model_1.Event]),
    __metadata("design:returntype", Promise)
], EventsResolver.prototype, "incentive", null);
__decorate([
    (0, graphql_1.ResolveField)('webinar', () => webinar_model_1.Webinar, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [event_model_1.Event]),
    __metadata("design:returntype", Promise)
], EventsResolver.prototype, "webinar", null);
exports.EventsResolver = EventsResolver = __decorate([
    (0, graphql_1.Resolver)(() => event_model_1.Event)
], EventsResolver);
//# sourceMappingURL=events.resolver.js.map