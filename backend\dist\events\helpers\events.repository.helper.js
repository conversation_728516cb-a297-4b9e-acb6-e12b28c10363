"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventsRepositoryHelper = void 0;
const common_1 = require("@nestjs/common");
const underscore_1 = require("../../common/helpers/underscore");
const event_invitations_service_1 = require("../../event-invitations/event-invitations.service");
const sequelize_1 = require("sequelize");
let EventsRepositoryHelper = class EventsRepositoryHelper {
    async getEventInvitationEventIds(profileId, filters) {
        const queryParams = { profileId };
        if (filters === null || filters === void 0 ? void 0 : filters.status) {
            queryParams.status = {
                [sequelize_1.Op.in]: filters.status,
            };
        }
        const eventInvitations = await this.eventInvitationsService.findAll(queryParams, {
            attributes: ['eventId'],
        });
        return underscore_1.Underscore.map(eventInvitations, 'eventId');
    }
};
exports.EventsRepositoryHelper = EventsRepositoryHelper;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => event_invitations_service_1.EventInvitationsService)),
    __metadata("design:type", event_invitations_service_1.EventInvitationsService)
], EventsRepositoryHelper.prototype, "eventInvitationsService", void 0);
exports.EventsRepositoryHelper = EventsRepositoryHelper = __decorate([
    (0, common_1.Injectable)()
], EventsRepositoryHelper);
//# sourceMappingURL=events.repository.helper.js.map