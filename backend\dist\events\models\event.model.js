"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Event = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const sequelize_1 = require("sequelize");
const graphql_1 = require("@nestjs/graphql");
const short_uuid_1 = __importDefault(require("short-uuid"));
const organisation_model_1 = require("../../organisations/models/organisation.model");
const events_args_1 = require("../args/events.args");
const graphql_type_json_1 = require("graphql-type-json");
const event_invitation_model_1 = require("../../event-invitations/models/event-invitation.model");
const incentive_model_1 = require("../../incentives/models/incentive.model");
const webinar_model_1 = require("../../webinars/models/webinar.model");
let Event = class Event extends sequelize_typescript_1.Model {
    get displayName() {
        if (this.type === events_args_1.EventType.IncentiveStart) {
            return `Start of ${this.incentiveName} incentive`;
        }
        if (this.type === events_args_1.EventType.IncentiveEnd) {
            return `End of ${this.incentiveName} incentive`;
        }
        if (this.type === events_args_1.EventType.Webinar) {
            return this.webinarName;
        }
        return this.name;
    }
    get isEnded() {
        return this.endDate && this.endDate.getTime() < Date.now();
    }
};
exports.Event = Event;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], Event.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        allowNull: false,
    }),
    __metadata("design:type", String)
], Event.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)(sequelize_1.DataTypes.VIRTUAL),
    __metadata("design:type", String),
    __metadata("design:paramtypes", [])
], Event.prototype, "displayName", null);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({
        allowNull: true,
    }),
    __metadata("design:type", String)
], Event.prototype, "image", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_type_json_1.GraphQLJSON, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.JSON,
    }),
    __metadata("design:type", Object)
], Event.prototype, "location", void 0);
__decorate([
    (0, graphql_1.Field)(() => events_args_1.EventType, { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], Event.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.BOOLEAN,
        defaultValue: false,
    }),
    __metadata("design:type", Boolean)
], Event.prototype, "isOnline", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({
        allowNull: true,
    }),
    __metadata("design:type", String)
], Event.prototype, "url", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        allowNull: false,
    }),
    __metadata("design:type", String)
], Event.prototype, "description", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.BOOLEAN,
        defaultValue: false,
    }),
    __metadata("design:type", Boolean)
], Event.prototype, "isAllDay", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Event.prototype, "startDate", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Event.prototype, "endDate", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Event.prototype, "isPublic", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Event.prototype, "isHostsManagedRSVP", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Event.prototype, "isGuestsCanInvite", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Event.prototype, "isGuestListVisible", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean),
    (0, sequelize_typescript_1.Column)(sequelize_1.DataTypes.VIRTUAL),
    __metadata("design:type", Boolean),
    __metadata("design:paramtypes", [])
], Event.prototype, "isEnded", null);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Event.prototype, "organisationId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, {
        foreignKey: 'organisationId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", organisation_model_1.Organisation)
], Event.prototype, "organisation", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => incentive_model_1.Incentive),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Event.prototype, "incentiveId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => incentive_model_1.Incentive, {
        foreignKey: 'incentiveId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", incentive_model_1.Incentive)
], Event.prototype, "incentive", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        allowNull: true,
    }),
    __metadata("design:type", String)
], Event.prototype, "incentiveName", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => webinar_model_1.Webinar),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Event.prototype, "webinarId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => webinar_model_1.Webinar, {
        foreignKey: 'webinarId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", webinar_model_1.Webinar)
], Event.prototype, "webinar", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        allowNull: true,
    }),
    __metadata("design:type", String)
], Event.prototype, "webinarName", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => event_invitation_model_1.EventInvitation, 'eventId'),
    __metadata("design:type", Array)
], Event.prototype, "eventInvitations", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Event.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Event.prototype, "updatedAt", void 0);
exports.Event = Event = __decorate([
    sequelize_typescript_1.Table,
    (0, graphql_1.ObjectType)()
], Event);
//# sourceMappingURL=event.model.js.map