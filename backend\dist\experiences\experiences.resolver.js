"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExperiencesResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const experience_model_1 = require("./models/experience.model");
const organisation_model_1 = require("../organisations/models/organisation.model");
const organisations_service_1 = require("../organisations/organisations.service");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const experiences_service_1 = require("./experiences.service");
const create_experience_input_1 = require("./dto/create-experience.input");
const update_experience_input_1 = require("./dto/update-experience.input");
const profile_model_1 = require("../profiles/models/profile.model");
const profiles_service_1 = require("../profiles/profiles.service");
let ExperiencesResolver = class ExperiencesResolver {
    createExperience(user, experienceData) {
        this.logger.verbose('ExperiencesResolver.createExperience (mutation)', {
            user: user.toLogObject(),
            experienceData,
        });
        return this.experiencesService.create(Object.assign(Object.assign({}, experienceData), { profileId: user.profileId }));
    }
    updateExperience(user, experienceId, experienceData) {
        this.logger.verbose('ExperiencesResolver.updateExperience (mutation)', {
            user: user.toLogObject(),
            experienceData,
        });
        return this.experiencesService.updateExperience(experienceId, experienceData, {
            profileId: user.profileId,
        });
    }
    async removeExperience(user, experienceId) {
        this.logger.verbose('ExperiencesResolver.removeExperience (mutation)', {
            user: user.toLogObject(),
            experienceId,
        });
        await this.experiencesService.removeExperience(experienceId, {
            profileId: user.profileId,
        });
        return true;
    }
    async organisation(experience) {
        this.logger.verbose('ExperiencesResolver.organisation (field resolver)', {
            experience,
        });
        if (!experience.organisationId)
            return null;
        return this.organisationsService.findById(experience.organisationId, {
            useCache: true,
        });
    }
    async profile(experience) {
        this.logger.verbose('ExperiencesResolver.profile (field resolver)', {
            experienceId: experience.id,
        });
        return this.profilesService.findById(experience.profileId);
    }
};
exports.ExperiencesResolver = ExperiencesResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], ExperiencesResolver.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => experiences_service_1.ExperiencesService)),
    __metadata("design:type", experiences_service_1.ExperiencesService)
], ExperiencesResolver.prototype, "experiencesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], ExperiencesResolver.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], ExperiencesResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Mutation)(() => experience_model_1.Experience),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('experienceData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_experience_input_1.CreateExperienceInput]),
    __metadata("design:returntype", Promise)
], ExperiencesResolver.prototype, "createExperience", null);
__decorate([
    (0, graphql_1.Mutation)(() => experience_model_1.Experience),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('experienceId')),
    __param(2, (0, graphql_1.Args)('experienceData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, update_experience_input_1.UpdateExperienceInput]),
    __metadata("design:returntype", Promise)
], ExperiencesResolver.prototype, "updateExperience", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('experienceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ExperiencesResolver.prototype, "removeExperience", null);
__decorate([
    (0, graphql_1.ResolveField)('organisation', () => organisation_model_1.Organisation, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [experience_model_1.Experience]),
    __metadata("design:returntype", Promise)
], ExperiencesResolver.prototype, "organisation", null);
__decorate([
    (0, graphql_1.ResolveField)('profile', () => profile_model_1.Profile),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [experience_model_1.Experience]),
    __metadata("design:returntype", Promise)
], ExperiencesResolver.prototype, "profile", null);
exports.ExperiencesResolver = ExperiencesResolver = __decorate([
    (0, graphql_1.Resolver)(() => experience_model_1.Experience)
], ExperiencesResolver);
//# sourceMappingURL=experiences.resolver.js.map