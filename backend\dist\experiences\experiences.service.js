"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExperiencesService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const experience_model_1 = require("./models/experience.model");
const base_service_1 = require("../common/base.service");
const error_1 = require("../common/helpers/error");
let ExperiencesService = class ExperiencesService extends (0, base_service_1.BaseService)(experience_model_1.Experience) {
    async updateExperience(id, updateExperienceInput, options) {
        this.logger.info('ExperiencesService.updateExperience', {
            id,
            updateExperienceInput,
            profileId: options.profileId,
        });
        const experience = await this.findOne({
            id,
            profileId: options === null || options === void 0 ? void 0 : options.profileId,
        });
        if (!experience) {
            this.errorHelper.throwHttpException(`ExperiencesService.updateExperience`, `Experience can't be modified by the current user`);
        }
        return super.updateById(experience.id, updateExperienceInput);
    }
    async removeExperience(id, options) {
        this.logger.info('ExperiencesService.removeExperience', {
            id,
            profileId: options.profileId,
        });
        const experience = await this.findOne({
            id,
            profileId: options === null || options === void 0 ? void 0 : options.profileId,
        }, {
            transaction: options === null || options === void 0 ? void 0 : options.transaction,
        });
        if (!experience) {
            this.errorHelper.throwHttpException(`ExperiencesService.updateExperience`, `Experience can't be removed by the current user`);
        }
        await this.removeById(experience.id, {
            transaction: options === null || options === void 0 ? void 0 : options.transaction,
        });
    }
};
exports.ExperiencesService = ExperiencesService;
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], ExperiencesService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], ExperiencesService.prototype, "errorHelper", void 0);
exports.ExperiencesService = ExperiencesService = __decorate([
    (0, common_1.Injectable)()
], ExperiencesService);
//# sourceMappingURL=experiences.service.js.map