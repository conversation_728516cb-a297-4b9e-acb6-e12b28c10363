"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Experience = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const graphql_1 = require("@nestjs/graphql");
const profile_model_1 = require("../../profiles/models/profile.model");
const short_uuid_1 = __importDefault(require("short-uuid"));
const graphql_type_json_1 = __importDefault(require("graphql-type-json"));
const sequelize_1 = require("sequelize");
const organisation_model_1 = require("../../organisations/models/organisation.model");
let Experience = class Experience extends sequelize_typescript_1.Model {
};
exports.Experience = Experience;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], Experience.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: false }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Experience.prototype, "position", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Experience.prototype, "description", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_type_json_1.default, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.JSON,
    }),
    __metadata("design:type", Object)
], Experience.prototype, "location", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.DATEONLY,
    }),
    __metadata("design:type", Date)
], Experience.prototype, "startDate", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.DATEONLY,
    }),
    __metadata("design:type", Date)
], Experience.prototype, "endDate", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Experience.prototype, "organisationId", void 0);
__decorate([
    (0, graphql_1.Field)(() => organisation_model_1.Organisation, { nullable: true }),
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, 'organisationId'),
    __metadata("design:type", organisation_model_1.Organisation)
], Experience.prototype, "organisation", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Experience.prototype, "organisationName", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Experience.prototype, "profileId", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, 'profileId'),
    __metadata("design:type", profile_model_1.Profile)
], Experience.prototype, "profile", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Experience.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Experience.prototype, "updatedAt", void 0);
exports.Experience = Experience = __decorate([
    sequelize_typescript_1.Table,
    (0, graphql_1.ObjectType)()
], Experience);
//# sourceMappingURL=experience.model.js.map