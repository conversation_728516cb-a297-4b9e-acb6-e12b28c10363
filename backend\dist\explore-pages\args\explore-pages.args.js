"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DestinationPagesArgs = exports.DestinationPagesFilter = exports.DeleteDestinationPageResponse = exports.DestinationPageContent = exports.DestinationPageResponse = exports.DestinationPageTree = exports.DestinationPagePathItem = exports.DestinationPageCategoryContent = exports.DestinationPageStatus = void 0;
const organisation_model_1 = require("../../organisations/models/organisation.model");
const graphql_1 = require("@nestjs/graphql");
const pagination_args_1 = require("../../common/args/pagination.args");
var DestinationPageStatus;
(function (DestinationPageStatus) {
    DestinationPageStatus["Public"] = "Public";
    DestinationPageStatus["Unlisted"] = "Unlisted";
})(DestinationPageStatus || (exports.DestinationPageStatus = DestinationPageStatus = {}));
let DestinationPageCategoryContent = class DestinationPageCategoryContent {
};
exports.DestinationPageCategoryContent = DestinationPageCategoryContent;
__decorate([
    (0, graphql_1.Field)({ nullable: false }),
    __metadata("design:type", String)
], DestinationPageCategoryContent.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], DestinationPageCategoryContent.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Number)
], DestinationPageCategoryContent.prototype, "order", void 0);
__decorate([
    (0, graphql_1.Field)(() => [organisation_model_1.Organisation], { nullable: true }),
    __metadata("design:type", Array)
], DestinationPageCategoryContent.prototype, "content", void 0);
exports.DestinationPageCategoryContent = DestinationPageCategoryContent = __decorate([
    (0, graphql_1.ObjectType)()
], DestinationPageCategoryContent);
let DestinationPagePathItem = class DestinationPagePathItem {
};
exports.DestinationPagePathItem = DestinationPagePathItem;
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], DestinationPagePathItem.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], DestinationPagePathItem.prototype, "name", void 0);
exports.DestinationPagePathItem = DestinationPagePathItem = __decorate([
    (0, graphql_1.ObjectType)()
], DestinationPagePathItem);
let DestinationPageTree = class DestinationPageTree {
};
exports.DestinationPageTree = DestinationPageTree;
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], DestinationPageTree.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(type => [String], { nullable: true }),
    __metadata("design:type", Array)
], DestinationPageTree.prototype, "path", void 0);
__decorate([
    (0, graphql_1.Field)(type => [DestinationPagePathItem], { nullable: true }),
    __metadata("design:type", Array)
], DestinationPageTree.prototype, "pathDetail", void 0);
__decorate([
    (0, graphql_1.Field)(type => [String], { nullable: true }),
    __metadata("design:type", Array)
], DestinationPageTree.prototype, "categoryIds", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], DestinationPageTree.prototype, "image", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], DestinationPageTree.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], DestinationPageTree.prototype, "vanityId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], DestinationPageTree.prototype, "status", void 0);
__decorate([
    (0, graphql_1.Field)(type => [DestinationPageTree], { nullable: true }),
    __metadata("design:type", Array)
], DestinationPageTree.prototype, "children", void 0);
__decorate([
    (0, graphql_1.Field)(type => [organisation_model_1.Organisation], { nullable: true }),
    __metadata("design:type", Array)
], DestinationPageTree.prototype, "hostOrganisations", void 0);
exports.DestinationPageTree = DestinationPageTree = __decorate([
    (0, graphql_1.ObjectType)()
], DestinationPageTree);
let DestinationPageResponse = class DestinationPageResponse extends DestinationPageTree {
};
exports.DestinationPageResponse = DestinationPageResponse;
__decorate([
    (0, graphql_1.Field)(type => [organisation_model_1.Organisation], { nullable: true }),
    __metadata("design:type", Array)
], DestinationPageResponse.prototype, "hostOrganisations", void 0);
__decorate([
    (0, graphql_1.Field)(type => [DestinationPageCategoryContent], { nullable: true }),
    __metadata("design:type", Array)
], DestinationPageResponse.prototype, "content", void 0);
exports.DestinationPageResponse = DestinationPageResponse = __decorate([
    (0, graphql_1.ObjectType)()
], DestinationPageResponse);
let DestinationPageContent = class DestinationPageContent {
};
exports.DestinationPageContent = DestinationPageContent;
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], DestinationPageContent.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(type => [String], { nullable: true }),
    __metadata("design:type", Array)
], DestinationPageContent.prototype, "hostOrganisationIds", void 0);
__decorate([
    (0, graphql_1.Field)(type => [String], { nullable: true }),
    __metadata("design:type", Array)
], DestinationPageContent.prototype, "path", void 0);
__decorate([
    (0, graphql_1.Field)(type => [DestinationPageCategoryContent], { nullable: true }),
    __metadata("design:type", Array)
], DestinationPageContent.prototype, "content", void 0);
exports.DestinationPageContent = DestinationPageContent = __decorate([
    (0, graphql_1.ObjectType)()
], DestinationPageContent);
let DeleteDestinationPageResponse = class DeleteDestinationPageResponse {
};
exports.DeleteDestinationPageResponse = DeleteDestinationPageResponse;
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], DeleteDestinationPageResponse.prototype, "success", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], DeleteDestinationPageResponse.prototype, "errorMessage", void 0);
exports.DeleteDestinationPageResponse = DeleteDestinationPageResponse = __decorate([
    (0, graphql_1.ObjectType)()
], DeleteDestinationPageResponse);
let DestinationPagesFilter = class DestinationPagesFilter {
};
exports.DestinationPagesFilter = DestinationPagesFilter;
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], DestinationPagesFilter.prototype, "searchText", void 0);
exports.DestinationPagesFilter = DestinationPagesFilter = __decorate([
    (0, graphql_1.InputType)()
], DestinationPagesFilter);
let DestinationPagesArgs = class DestinationPagesArgs extends pagination_args_1.PaginationArgs {
};
exports.DestinationPagesArgs = DestinationPagesArgs;
__decorate([
    (0, graphql_1.Field)(() => DestinationPagesFilter, { nullable: true }),
    __metadata("design:type", DestinationPagesFilter)
], DestinationPagesArgs.prototype, "filter", void 0);
exports.DestinationPagesArgs = DestinationPagesArgs = __decorate([
    (0, graphql_1.ArgsType)()
], DestinationPagesArgs);
//# sourceMappingURL=explore-pages.args.js.map