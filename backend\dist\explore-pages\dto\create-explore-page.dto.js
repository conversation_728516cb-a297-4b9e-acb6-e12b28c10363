"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateDestinationPageDto = void 0;
const class_validator_1 = require("class-validator");
const explore_pages_args_1 = require("../args/explore-pages.args");
class CreateDestinationPageDto {
}
exports.CreateDestinationPageDto = CreateDestinationPageDto;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateDestinationPageDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateDestinationPageDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", Array)
], CreateDestinationPageDto.prototype, "path", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", Array)
], CreateDestinationPageDto.prototype, "categories", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", Array)
], CreateDestinationPageDto.prototype, "hostOrganisationIds", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateDestinationPageDto.prototype, "image", void 0);
//# sourceMappingURL=create-explore-page.dto.js.map