"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateDestinationPageCategoryOrderInput = exports.DestinationPageCategory = void 0;
const graphql_1 = require("@nestjs/graphql");
const class_validator_1 = require("class-validator");
let DestinationPageCategory = class DestinationPageCategory {
};
exports.DestinationPageCategory = DestinationPageCategory;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], DestinationPageCategory.prototype, "id", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], DestinationPageCategory.prototype, "order", void 0);
exports.DestinationPageCategory = DestinationPageCategory = __decorate([
    (0, graphql_1.InputType)()
], DestinationPageCategory);
let UpdateDestinationPageCategoryOrderInput = class UpdateDestinationPageCategoryOrderInput {
};
exports.UpdateDestinationPageCategoryOrderInput = UpdateDestinationPageCategoryOrderInput;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)((type) => [DestinationPageCategory]),
    __metadata("design:type", Array)
], UpdateDestinationPageCategoryOrderInput.prototype, "items", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], UpdateDestinationPageCategoryOrderInput.prototype, "destinationPageId", void 0);
exports.UpdateDestinationPageCategoryOrderInput = UpdateDestinationPageCategoryOrderInput = __decorate([
    (0, graphql_1.InputType)()
], UpdateDestinationPageCategoryOrderInput);
//# sourceMappingURL=update-explore-page-category-order.input.js.map