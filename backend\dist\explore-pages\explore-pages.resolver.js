"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DestinationPagesResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const explore_pages_service_1 = require("./explore-pages.service");
const paginated_result_1 = require("../common/args/paginated-result");
const explore_pages_args_1 = require("./args/explore-pages.args");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const error_1 = require("../common/helpers/error");
const explore_page_model_1 = require("./models/explore-page.model");
const create_explore_page_input_1 = require("./dto/create-explore-page.input");
const update_explore_page_input_1 = require("./dto/update-explore-page.input");
const create_explore_page_org_input_1 = require("./dto/create-explore-page-org.input");
const create_explore_page_category_input_1 = require("./dto/create-explore-page-category.input");
const update_explore_page_category_order_input_1 = require("./dto/update-explore-page-category-order.input");
const explore_pages_args_2 = require("./args/explore-pages.args");
let DestinationPagesResolver = class DestinationPagesResolver {
    async destinationPages(id) {
        this.logger.verbose('DestinationPagesResolver.destinationPageChildren (query)', {
            id
        });
        const destinationPages = await this.destinationPagesService.getChildDestinationPages(id);
        return destinationPages;
    }
    async searchDestinationPages(searchArgs) {
        this.logger.verbose('DestinationPagesResolver.searchDestinationPageChildren (query)', {
            searchArgs
        });
        const destinationPages = await this.destinationPagesService.findDestinationPageByName(searchArgs);
        return destinationPages;
    }
    async destinationPagesTree() {
        this.logger.verbose('DestinationPagesResolver.destinationPageTree (query)');
        const destinationPages = await this.destinationPagesService.getDestinationPagesTree();
        const formatted = this.destinationPagesService.formatDestinationPagesTree(destinationPages);
        return formatted;
    }
    async destinationPage(id, vanityId) {
        this.logger.verbose('DestinationPagesResolver.destinationPage (query)', {
            id,
            vanityId
        });
        if (!id && !vanityId) {
            throw new Error(`Destination page id or vanityId should be set`);
        }
        const destinationPage = await this.destinationPagesService.getDestinationPage(id, vanityId);
        if (!destinationPage) {
            throw new Error(`Destination page not found`);
        }
        return destinationPage;
    }
    async addOrganisationToDestinationPage(destinationPageOrgData) {
        const result = await this.destinationPagesService.addOrganisationToDestinationPage(destinationPageOrgData);
        return result;
    }
    async addCategoryToDestinationPage(categoryData) {
        const result = await this.destinationPagesService.addCategoryToDestinationPage(categoryData);
        if (!result)
            throw new Error(`Request failed`);
        return result;
    }
    async createDestinationPage(destinationPageData) {
        this.logger.verbose('DestinationPagesResolver.createDesintationPage (mutation)', {
            destinationPageData,
        });
        const result = await this.destinationPagesService.createDestinationPage(destinationPageData);
        return result;
    }
    async updateDestinationPage(destinationPageData) {
        const result = await this.destinationPagesService.updateDestinationPage(destinationPageData);
        if (!result)
            throw new Error(`Failed to update destination page`);
        return result;
    }
    async updateDestinationPageCategory(destinationPageId, id, name) {
        const result = await this.destinationPagesService.updateDestinationPageCategory(destinationPageId, id, name);
        if (!result)
            throw new Error(`Request failed`);
        return result;
    }
    async deleteDestinationPageCategory(destinationPageId, id) {
        const result = await this.destinationPagesService.deleteDestinationPageCategory(destinationPageId, id);
        if (!result)
            throw new Error(`Request failed`);
        return result;
    }
    async deleteDestinationPageOrganisation(destinationPageId, categoryId, id) {
        const result = await this.destinationPagesService.deleteDestinationPageOrganisation(destinationPageId, categoryId, id);
        if (!result)
            throw new Error(`Request failed`);
        return result;
    }
    async updateDestinationPageCategoryOrder(destinationPageCategoryOrderData) {
        const result = await this.destinationPagesService.updateDestinationPageCategoryOrder(destinationPageCategoryOrderData);
        if (!result)
            throw new Error(`Request failed`);
        return result;
    }
    async removeDestinationPage(id) {
        this.logger.verbose('DestinationPagesResolver.removeDestinationPage (mutation)', {
            id,
        });
        const result = await this.destinationPagesService.deleteDestinationPage(id);
        return result;
    }
};
exports.DestinationPagesResolver = DestinationPagesResolver;
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], DestinationPagesResolver.prototype, "errorHelper", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => explore_pages_service_1.DestinationPagesService)),
    __metadata("design:type", explore_pages_service_1.DestinationPagesService)
], DestinationPagesResolver.prototype, "destinationPagesService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], DestinationPagesResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Query)(() => [explore_page_model_1.DestinationPage]),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)('id', { nullable: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DestinationPagesResolver.prototype, "destinationPages", null);
__decorate([
    (0, graphql_1.Query)(() => paginated_result_1.DestinationPagesResult),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [explore_pages_args_1.DestinationPagesArgs]),
    __metadata("design:returntype", Promise)
], DestinationPagesResolver.prototype, "searchDestinationPages", null);
__decorate([
    (0, graphql_1.Query)(() => [explore_pages_args_2.DestinationPageTree]),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DestinationPagesResolver.prototype, "destinationPagesTree", null);
__decorate([
    (0, graphql_1.Query)(() => explore_pages_args_1.DestinationPageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)('id', { nullable: true })),
    __param(1, (0, graphql_1.Args)('vanityId', { nullable: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DestinationPagesResolver.prototype, "destinationPage", null);
__decorate([
    (0, graphql_1.Mutation)(() => explore_pages_args_1.DestinationPageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)('destinationPageOrgData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_explore_page_org_input_1.CreateDestinationPageOrgInput]),
    __metadata("design:returntype", Promise)
], DestinationPagesResolver.prototype, "addOrganisationToDestinationPage", null);
__decorate([
    (0, graphql_1.Mutation)(() => explore_pages_args_1.DestinationPageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)('categoryData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_explore_page_category_input_1.CreateDestinationPageCategoryInput]),
    __metadata("design:returntype", Promise)
], DestinationPagesResolver.prototype, "addCategoryToDestinationPage", null);
__decorate([
    (0, graphql_1.Mutation)(() => explore_pages_args_1.DestinationPageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)('destinationPageData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_explore_page_input_1.CreateDestinationPageInput]),
    __metadata("design:returntype", Promise)
], DestinationPagesResolver.prototype, "createDestinationPage", null);
__decorate([
    (0, graphql_1.Mutation)(() => explore_pages_args_1.DestinationPageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)('destinationPageData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [update_explore_page_input_1.UpdateDestinationPageInput]),
    __metadata("design:returntype", Promise)
], DestinationPagesResolver.prototype, "updateDestinationPage", null);
__decorate([
    (0, graphql_1.Mutation)(() => explore_pages_args_1.DestinationPageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)('destinationPageId')),
    __param(1, (0, graphql_1.Args)('id')),
    __param(2, (0, graphql_1.Args)('name')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], DestinationPagesResolver.prototype, "updateDestinationPageCategory", null);
__decorate([
    (0, graphql_1.Mutation)(() => explore_pages_args_1.DestinationPageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)('destinationPageId')),
    __param(1, (0, graphql_1.Args)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DestinationPagesResolver.prototype, "deleteDestinationPageCategory", null);
__decorate([
    (0, graphql_1.Mutation)(() => explore_pages_args_1.DestinationPageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)('destinationPageId')),
    __param(1, (0, graphql_1.Args)('categoryId')),
    __param(2, (0, graphql_1.Args)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], DestinationPagesResolver.prototype, "deleteDestinationPageOrganisation", null);
__decorate([
    (0, graphql_1.Mutation)(() => explore_pages_args_1.DestinationPageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)('destinationPageCategoryOrderData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [update_explore_page_category_order_input_1.UpdateDestinationPageCategoryOrderInput]),
    __metadata("design:returntype", Promise)
], DestinationPagesResolver.prototype, "updateDestinationPageCategoryOrder", null);
__decorate([
    (0, graphql_1.Mutation)(() => explore_pages_args_2.DeleteDestinationPageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DestinationPagesResolver.prototype, "removeDestinationPage", null);
exports.DestinationPagesResolver = DestinationPagesResolver = __decorate([
    (0, graphql_1.Resolver)(() => explore_page_model_1.DestinationPage)
], DestinationPagesResolver);
//# sourceMappingURL=explore-pages.resolver.js.map