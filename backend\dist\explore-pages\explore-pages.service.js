"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DestinationPagesService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const sequelize_typescript_1 = require("sequelize-typescript");
const sequelize_1 = require("sequelize");
const explore_page_model_1 = require("./models/explore-page.model");
const base_service_1 = require("../common/base.service");
const error_1 = require("../common/helpers/error");
const pagination_1 = require("../common/helpers/pagination");
const organisations_service_1 = require("../organisations/organisations.service");
const lodash_1 = require("lodash");
let DestinationPagesService = class DestinationPagesService extends (0, base_service_1.BaseService)(explore_page_model_1.DestinationPage) {
    constructor() {
        super(...arguments);
        this.createDestinationPage = async (data) => {
            const transaction = await this.sequelize.transaction();
            try {
                const vanityId = data.name.split(' ').join('');
                const categories = await Promise.all(data.categories.map((el, i) => explore_page_model_1.DestinationPagesCategories.create({ name: el, order: i })));
                const format = /[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/;
                if (format.test(data.name)) {
                    throw new Error('Destination page name contains invalid characters');
                }
                const alreadyExists = await this.findOne({ vanityId });
                if (alreadyExists)
                    throw new Error(`Destination page with the name ${data.name} already exists`);
                const categoriesIds = categories.map(el => el.id);
                const destinationPage = await this.create(Object.assign(Object.assign({}, ((0, lodash_1.omit)(data, 'categories'))), { categoryIds: categoriesIds, vanityId }), {
                    transaction,
                });
                await transaction.commit();
                const result = await this.getDestinationPage(destinationPage.id);
                return result;
            }
            catch (e) {
                await transaction.rollback();
                this.errorHelper.throwHttpException(`ExplorePagesService.createExplorePage`, e.message);
                return null;
            }
        };
        this.updateDestinationPage = async (data) => {
            const transaction = await this.sequelize.transaction();
            try {
                const old = await this.findById(data.id);
                let vanityId;
                const format = /[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/;
                if (data.name && format.test(data.name)) {
                    throw new Error('Destination page name contains invalid characters');
                }
                if (data.name && data.name !== old.name)
                    vanityId = data.name.split(' ').join('');
                if (vanityId) {
                    const alreadyExists = await this.findOne({ vanityId });
                    if (alreadyExists)
                        throw new Error(`Destination page with the name ${data.name} already exists`);
                }
                const payload = Object.assign({}, (0, lodash_1.omit)(data, 'id'));
                if (vanityId)
                    payload.vanityId = vanityId;
                const destinationPage = await this.updateById(data.id, payload, { transaction });
                if (data.path && !!data.path.find(id => id === data.id)) {
                    throw new Error(`Can't add child page as a parent page`);
                }
                else if (data.path) {
                    const pathPages = await this.findAll({
                        id: data.path
                    });
                    for (let i = 0; i < pathPages.length; i++) {
                        const v = pathPages[i];
                        if (v.path && !!v.path.find(id => id === data.id)) {
                            throw new Error(`Can't add child page as a parent page`);
                        }
                    }
                }
                if (data.path && (old.path.join('') !== data.path.join(''))) {
                    const childPages = await this.findAll({
                        path: {
                            [sequelize_1.Op.contains]: [data.id]
                        }
                    });
                    const updatedPathPages = childPages.map(el => {
                        const pathString = el.path.join('');
                        const newPath = pathString.replace(old.path.join(''), data.path.join('')).match(/.{1,22}/g);
                        return ({
                            id: el.id,
                            path: newPath
                        });
                    });
                    await Promise.all(updatedPathPages.map(el => this.updateById(el.id, { path: el.path }, { transaction })));
                }
                await transaction.commit();
                const result = await this.getDestinationPage(data.id);
                return result;
            }
            catch (e) {
                await transaction.rollback();
                this.errorHelper.throwHttpException(`ExplorePagesService.updateExplorePage`, e.message);
                return null;
            }
        };
        this.findDestinationPages = (filter, pagination) => {
            const extraQueryParams = {};
            if (filter === null || filter === void 0 ? void 0 : filter.searchText) {
                extraQueryParams.name = {
                    [sequelize_1.Op.iLike]: `%${filter.searchText}%`,
                };
            }
            return new pagination_1.PaginationHelper().getPaginatedResults({
                model: explore_page_model_1.DestinationPage,
                pagination,
                extraQueryParams,
                excludeIds: [],
                includeIds: [],
                includeParams: []
            });
        };
        this.findDestinationPageByName = async (searchArgs) => {
            var _a;
            try {
                const results = await this.findDestinationPages({
                    searchText: (_a = searchArgs === null || searchArgs === void 0 ? void 0 : searchArgs.filter) === null || _a === void 0 ? void 0 : _a.searchText,
                }, {
                    first: searchArgs === null || searchArgs === void 0 ? void 0 : searchArgs.first,
                    after: searchArgs === null || searchArgs === void 0 ? void 0 : searchArgs.after,
                    sortBy: searchArgs === null || searchArgs === void 0 ? void 0 : searchArgs.sortBy,
                    sortOrder: searchArgs === null || searchArgs === void 0 ? void 0 : searchArgs.sortOrder,
                });
                const all = await Promise.all(results.records.map(el => this.getDestinationPage(el.id)));
                return {
                    totalCount: results.totalCount,
                    records: all
                };
            }
            catch (err) {
                return {
                    totalCount: 0,
                    records: []
                };
            }
        };
        this.updateDestinationPageCategory = async (destinationPageId, id, name) => {
            const transaction = await this.sequelize.transaction();
            try {
                await explore_page_model_1.DestinationPagesCategories.update({ name }, {
                    where: {
                        id,
                    },
                    transaction
                });
                await transaction.commit();
                const result = await this.getDestinationPage(destinationPageId);
                return result;
            }
            catch (err) {
                await transaction.rollback();
                this.errorHelper.throwHttpException(`ExplorePagesService.updateExplorePageCategory`, err.message);
                return null;
            }
        };
        this.deleteDestinationPageCategory = async (destinationPageId, id) => {
            const transaction = await this.sequelize.transaction();
            try {
                const currData = await this.findById(destinationPageId);
                currData.categoryIds = currData.categoryIds.filter(el => el !== id);
                await this.updateById(destinationPageId, { categoryIds: currData.categoryIds }, { transaction });
                await explore_page_model_1.DestinationPagesCategories.destroy({
                    where: {
                        id
                    },
                    transaction
                });
                await transaction.commit();
                const result = await this.getDestinationPage(destinationPageId);
                return result;
            }
            catch (err) {
                await transaction.rollback();
                this.errorHelper.throwHttpException(`ExplorePagesService.deleteExplorePageCategory`, err.message);
                return null;
            }
        };
        this.updateDestinationPageCategoryOrder = async (data) => {
            const transaction = await this.sequelize.transaction();
            try {
                const categories = await explore_page_model_1.DestinationPagesCategories.findAll({
                    where: {
                        id: data.items.map(el => el.id)
                    }
                });
                await Promise.all(categories.map((el, i) => {
                    const orderItem = data.items.find(v => v.id === el.id);
                    return explore_page_model_1.DestinationPagesCategories.update({ order: orderItem.order }, {
                        where: {
                            id: el.id,
                        },
                        transaction
                    });
                }));
                await transaction.commit();
                const result = await this.getDestinationPage(data.destinationPageId);
                return result;
            }
            catch (err) {
                await transaction.rollback();
                this.errorHelper.throwHttpException(`ExplorePagesService.updateExplorePageCategoryOrder`, err.message);
                return null;
            }
        };
        this.deleteDestinationPageOrganisation = async (destinationPageId, categoryId, id) => {
            const transaction = await this.sequelize.transaction();
            try {
                await explore_page_model_1.DestinationPageOrgs.destroy({
                    where: {
                        destinationPageId: destinationPageId,
                        categoryId: categoryId,
                        organisationId: id
                    },
                    transaction
                });
                await transaction.commit();
                const result = await this.getDestinationPage(destinationPageId);
                return result;
            }
            catch (err) {
                await transaction.rollback();
                this.errorHelper.throwHttpException(`ExplorePagesService.deleteExplorePageOrganisation`, err.message);
                return null;
            }
        };
        this.addOrganisationToDestinationPage = async (data) => {
            try {
                const res = await explore_page_model_1.DestinationPageOrgs.create(data);
                const result = await this.getDestinationPage(data.destinationPageId);
                return result;
            }
            catch (err) {
                this.errorHelper.throwHttpException(`ExplorePagesService.addOrganisationToDestinationPage`, err.message);
                return null;
            }
        };
        this.addCategoryToDestinationPage = async (data) => {
            const transaction = await this.sequelize.transaction();
            try {
                const currData = await this.findById(data.destinationPageId);
                const currCategories = (0, lodash_1.get)(currData, 'categoryIds');
                const res = await explore_page_model_1.DestinationPagesCategories.create(Object.assign(Object.assign({}, ((0, lodash_1.omit)(data, 'destinationPageId'))), { order: currCategories.length }), { transaction });
                const ctId = res.id;
                currCategories.push(res.id);
                await this.updateById(data.destinationPageId, { categoryIds: currCategories }, { transaction });
                await transaction.commit();
                const result = await this.getDestinationPage(data.destinationPageId);
                return result;
            }
            catch (err) {
                await transaction.rollback();
                return null;
            }
        };
        this.getDestinationPage = async (id, vanityId) => {
            try {
                const destinationPage = await this.findOne(id
                    ? { id }
                    : sequelize_typescript_1.Sequelize.where(sequelize_typescript_1.Sequelize.fn('lower', sequelize_typescript_1.Sequelize.col('vanityId')), sequelize_typescript_1.Sequelize.fn('lower', vanityId)));
                const paths = await this.findAll({ id: destinationPage.path });
                const orderedPath = destinationPage.path.map(key => {
                    const fullItem = paths.find(el => el.id === key);
                    if (!fullItem)
                        return null;
                    return {
                        id: fullItem.id,
                        name: fullItem.name
                    };
                }).filter(el => !!el);
                const hostOrganisations = await this.orgService.findAll({
                    id: destinationPage.hostOrganisationIds
                });
                const categories = await explore_page_model_1.DestinationPagesCategories.findAll({
                    where: {
                        id: (0, lodash_1.get)(destinationPage, 'categoryIds', [])
                    },
                    order: sequelize_typescript_1.Sequelize.fn('max', sequelize_typescript_1.Sequelize.col('order')),
                    group: ['DestinationPagesCategories.id']
                });
                const content = [];
                for (let ct of categories) {
                    const categoryOrgs = await explore_page_model_1.DestinationPageOrgs.findAll({
                        where: {
                            categoryId: ct.id,
                            destinationPageId: destinationPage.id
                        }
                    });
                    const orgs = await this.orgService.findAll({
                        id: categoryOrgs.map(el => el.organisationId)
                    });
                    content.push({
                        name: ct.name,
                        id: ct.id,
                        order: ct.order,
                        content: orgs,
                    });
                }
                const destinationPageObj = destinationPage.get({ plain: true });
                return Object.assign(Object.assign({}, destinationPageObj), { categoryIds: (0, lodash_1.get)(destinationPage, 'categoryIds', []), hostOrganisations: hostOrganisations, content: content, pathDetail: orderedPath });
            }
            catch (err) {
                return null;
            }
        };
        this.formatDestinationPagesTree = (tree) => {
            return (0, lodash_1.sortBy)(Object.keys(tree).reduce((acc, el) => {
                const v = tree[el];
                const keys = Object.keys(v).filter(k => k.length === 22);
                const children = keys.reduce((keyObj, key) => (Object.assign(Object.assign({}, keyObj), { [key]: v[key] })), {});
                return [
                    ...acc,
                    Object.assign(Object.assign({}, ((0, lodash_1.omit)(v, keys))), { children: this.formatDestinationPagesTree(children) })
                ];
            }, []), [(v) => v === null || v === void 0 ? void 0 : v.name.toLowerCase()]);
        };
        this.getDestinationPagesTree = async (id = '', acc = null, indexMap = {}, rootMap = {}, prevItem = {}) => {
            try {
                const results = await this.getChildDestinationPages(id);
                if (!acc)
                    acc = results.reduce((vals, el) => (Object.assign(Object.assign({}, vals), { [el.id]: el })), {});
                else {
                    results.forEach(el => {
                        (0, lodash_1.set)(acc, `${el.path.join('.')}.${el.id}`, el);
                    });
                }
                for (let item of results) {
                    await this.getDestinationPagesTree(item.id, acc, indexMap, rootMap, item);
                }
                return acc;
            }
            catch (err) {
                return [];
            }
        };
        this.deleteDestinationPage = async (id) => {
            const transaction = await this.sequelize.transaction();
            try {
                const curr = await this.findById(id);
                const childPages = await this.findAll({
                    path: {
                        [sequelize_1.Op.contains]: [id]
                    }
                });
                if (childPages.length) {
                    throw new Error(`Can't delete destination page with child destination pages`);
                }
                await Promise.all(curr.categoryIds.map(ctId => explore_page_model_1.DestinationPagesCategories.destroy({ where: { id: ctId }, transaction })));
                await this.removeById(id, { transaction });
                await transaction.commit();
                return {
                    success: true
                };
            }
            catch (err) {
                await transaction.rollback();
                return {
                    success: false,
                    errorMessage: err.message
                };
            }
        };
        this.getChildDestinationPages = async (id) => {
            try {
                if (!id) {
                    const pages = await this.findAll(sequelize_typescript_1.Sequelize.where(sequelize_typescript_1.Sequelize.fn('array_length', sequelize_typescript_1.Sequelize.col('path'), 1), { [sequelize_1.Op.eq]: null }));
                    const result = [];
                    for (let item of pages) {
                        if (item.id !== 'explore-home') {
                            const orgs = await this.orgService.findAll({
                                id: item.hostOrganisationIds
                            });
                            const el = Object.assign(Object.assign({}, item.get({ plain: true })), { hostOrganisations: orgs });
                            result.push(el);
                        }
                    }
                    return result;
                }
                else {
                    const page = await this.findOne({ id });
                    if (!page)
                        return [];
                    const targetPathLength = page.path.length + 1;
                    const pages = await this.findAll({
                        [sequelize_1.Op.and]: [
                            sequelize_typescript_1.Sequelize.where(sequelize_typescript_1.Sequelize.fn('array_length', sequelize_typescript_1.Sequelize.col('path'), 1), { [sequelize_1.Op.eq]: targetPathLength }),
                            sequelize_typescript_1.Sequelize.where(sequelize_typescript_1.Sequelize.fn('array_to_string ', sequelize_typescript_1.Sequelize.col('path'), ''), { [sequelize_1.Op.endsWith]: id })
                        ]
                    });
                    const result = [];
                    for (let item of pages) {
                        const orgs = await this.orgService.findAll({
                            id: item.hostOrganisationIds
                        });
                        const el = Object.assign(Object.assign({}, item.get({ plain: true })), { hostOrganisations: orgs });
                        result.push(el);
                    }
                    return result;
                }
            }
            catch (err) {
                return [];
            }
        };
    }
};
exports.DestinationPagesService = DestinationPagesService;
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], DestinationPagesService.prototype, "orgService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", sequelize_typescript_1.Sequelize)
], DestinationPagesService.prototype, "sequelize", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], DestinationPagesService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], DestinationPagesService.prototype, "errorHelper", void 0);
exports.DestinationPagesService = DestinationPagesService = __decorate([
    (0, common_1.Injectable)()
], DestinationPagesService);
//# sourceMappingURL=explore-pages.service.js.map