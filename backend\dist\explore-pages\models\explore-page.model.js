"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DestinationPagesCategories = exports.DestinationPageOrgs = exports.DestinationPage = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const sequelize_1 = require("sequelize");
const short_uuid_1 = __importDefault(require("short-uuid"));
const graphql_1 = require("@nestjs/graphql");
const organisation_model_1 = require("../../organisations/models/organisation.model");
let DestinationPage = class DestinationPage extends sequelize_typescript_1.Model {
};
exports.DestinationPage = DestinationPage;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID, { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], DestinationPage.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(type => [String], { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], DestinationPage.prototype, "hostOrganisationIds", void 0);
__decorate([
    (0, graphql_1.Field)(type => [String], { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], DestinationPage.prototype, "path", void 0);
__decorate([
    (0, graphql_1.Field)(type => [String], { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], DestinationPage.prototype, "categoryIds", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], DestinationPage.prototype, "image", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({
        unique: true,
    }),
    __metadata("design:type", String)
], DestinationPage.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({
        unique: true,
    }),
    __metadata("design:type", String)
], DestinationPage.prototype, "vanityId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], DestinationPage.prototype, "status", void 0);
__decorate([
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], DestinationPage.prototype, "createdAt", void 0);
__decorate([
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], DestinationPage.prototype, "updatedAt", void 0);
__decorate([
    (0, graphql_1.Field)(() => [organisation_model_1.Organisation], { nullable: true }),
    (0, sequelize_typescript_1.BelongsToMany)(() => organisation_model_1.Organisation, () => DestinationPageOrgs),
    __metadata("design:type", Array)
], DestinationPage.prototype, "organisations", void 0);
exports.DestinationPage = DestinationPage = __decorate([
    (0, graphql_1.ObjectType)(),
    sequelize_typescript_1.Table
], DestinationPage);
let DestinationPageOrgs = class DestinationPageOrgs extends sequelize_typescript_1.Model {
};
exports.DestinationPageOrgs = DestinationPageOrgs;
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => DestinationPage),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], DestinationPageOrgs.prototype, "destinationPageId", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], DestinationPageOrgs.prototype, "organisationId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], DestinationPageOrgs.prototype, "categoryId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Number)
], DestinationPageOrgs.prototype, "order", void 0);
exports.DestinationPageOrgs = DestinationPageOrgs = __decorate([
    (0, graphql_1.ObjectType)(),
    sequelize_typescript_1.Table
], DestinationPageOrgs);
let DestinationPagesCategories = class DestinationPagesCategories extends sequelize_typescript_1.Model {
};
exports.DestinationPagesCategories = DestinationPagesCategories;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID, { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], DestinationPagesCategories.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], DestinationPagesCategories.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Number)
], DestinationPagesCategories.prototype, "order", void 0);
__decorate([
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], DestinationPagesCategories.prototype, "createdAt", void 0);
__decorate([
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], DestinationPagesCategories.prototype, "updatedAt", void 0);
exports.DestinationPagesCategories = DestinationPagesCategories = __decorate([
    (0, graphql_1.ObjectType)(),
    sequelize_typescript_1.Table
], DestinationPagesCategories);
//# sourceMappingURL=explore-page.model.js.map