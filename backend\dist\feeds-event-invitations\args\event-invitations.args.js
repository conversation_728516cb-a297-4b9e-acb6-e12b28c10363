"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamEventInvitationsArgs = exports.StreamEventInvitationsFilter = exports.StreamEventInvitationType = exports.StreamEventInvitationStatus = void 0;
const graphql_1 = require("@nestjs/graphql");
var StreamEventInvitationStatus;
(function (StreamEventInvitationStatus) {
    StreamEventInvitationStatus["Attending"] = "Attending";
    StreamEventInvitationStatus["Interested"] = "Interested";
    StreamEventInvitationStatus["Declined"] = "Declined";
    StreamEventInvitationStatus["InvitedByGuest"] = "InvitedByGuest";
    StreamEventInvitationStatus["InvitedByHost"] = "InvitedByHost";
    StreamEventInvitationStatus["InvitedAttending"] = "InvitedAttending";
    StreamEventInvitationStatus["InvitedInterested"] = "InvitedInterested";
    StreamEventInvitationStatus["InvitedDeclined"] = "InvitedDeclined";
    StreamEventInvitationStatus["Blocked"] = "Blocked";
})(StreamEventInvitationStatus || (exports.StreamEventInvitationStatus = StreamEventInvitationStatus = {}));
(0, graphql_1.registerEnumType)(StreamEventInvitationStatus, { name: 'StreamEventInvitationStatus' });
var StreamEventInvitationType;
(function (StreamEventInvitationType) {
    StreamEventInvitationType["Hosts"] = "Hosts";
    StreamEventInvitationType["Guest"] = "Guest";
})(StreamEventInvitationType || (exports.StreamEventInvitationType = StreamEventInvitationType = {}));
let StreamEventInvitationsFilter = class StreamEventInvitationsFilter {
};
exports.StreamEventInvitationsFilter = StreamEventInvitationsFilter;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID, {
        nullable: true,
    }),
    __metadata("design:type", String)
], StreamEventInvitationsFilter.prototype, "profileId", void 0);
__decorate([
    (0, graphql_1.Field)(() => [StreamEventInvitationStatus], {
        nullable: true,
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], StreamEventInvitationsFilter.prototype, "status", void 0);
exports.StreamEventInvitationsFilter = StreamEventInvitationsFilter = __decorate([
    (0, graphql_1.InputType)()
], StreamEventInvitationsFilter);
let StreamEventInvitationsArgs = class StreamEventInvitationsArgs {
};
exports.StreamEventInvitationsArgs = StreamEventInvitationsArgs;
__decorate([
    (0, graphql_1.Field)(() => StreamEventInvitationsFilter, { nullable: true }),
    __metadata("design:type", StreamEventInvitationsFilter)
], StreamEventInvitationsArgs.prototype, "filter", void 0);
exports.StreamEventInvitationsArgs = StreamEventInvitationsArgs = __decorate([
    (0, graphql_1.ArgsType)()
], StreamEventInvitationsArgs);
//# sourceMappingURL=event-invitations.args.js.map