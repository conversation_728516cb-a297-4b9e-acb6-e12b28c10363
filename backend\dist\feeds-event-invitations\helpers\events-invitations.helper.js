"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventsInvitationsHelper = void 0;
const common_1 = require("@nestjs/common");
const event_invitations_args_1 = require("../../event-invitations/args/event-invitations.args");
const config_1 = __importDefault(require("../../config/config"));
const getstream_1 = require("getstream");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
let EventsInvitationsHelper = class EventsInvitationsHelper {
    constructor() {
        this.eventInvitationStatuses = [
            event_invitations_args_1.EventInvitationStatus.Attending,
            event_invitations_args_1.EventInvitationStatus.Interested,
            event_invitations_args_1.EventInvitationStatus.InvitedByHost
        ];
    }
    onModuleInit() {
        if (config_1.default.SERVICE === 'api-beta') {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_BETA_KEY, config_1.default.STREAM_BETA_SECRET);
        }
        else {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_KEY, config_1.default.STREAM_SECRET);
        }
    }
    async getRecursionActivity(eventId, profileId, offset, limit) {
        let isGuestInvitation = false;
        const feedUser = this.client.feed('user', profileId);
        const getActivities = await feedUser.get({ offset, limit });
        if (getActivities.results.length != 0) {
            for (const active of getActivities.results) {
                if (active.status && active.foreign_id == 'events:' + eventId) {
                    if (this.eventInvitationStatuses.includes(active.status)) {
                        isGuestInvitation = true;
                        break;
                    }
                }
            }
        }
        else {
            return false;
        }
        if (isGuestInvitation) {
            return isGuestInvitation;
        }
        else {
            return this.getRecursionActivity(eventId, profileId, limit, limit + 1000);
        }
    }
};
exports.EventsInvitationsHelper = EventsInvitationsHelper;
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], EventsInvitationsHelper.prototype, "logger", void 0);
exports.EventsInvitationsHelper = EventsInvitationsHelper = __decorate([
    (0, common_1.Injectable)()
], EventsInvitationsHelper);
//# sourceMappingURL=events-invitations.helper.js.map