"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamEventsInvitationsResolver = exports.StreamEventMessageResponse = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const stream_events_invitations_service_1 = require("./stream-events-invitations.service");
const create_event_invitations_input_1 = require("./dto/create-event-invitations.input");
let StreamEventMessageResponse = class StreamEventMessageResponse {
};
exports.StreamEventMessageResponse = StreamEventMessageResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], StreamEventMessageResponse.prototype, "success", void 0);
exports.StreamEventMessageResponse = StreamEventMessageResponse = __decorate([
    (0, graphql_1.ObjectType)()
], StreamEventMessageResponse);
let StreamEventsInvitationsResolver = class StreamEventsInvitationsResolver {
    async streamCreateEventInvitations(user, eventInvitationsInput) {
        this.logger.verbose('StreamEventsInvitationsResolver.streamCreateEventInvitations (mutation)', {
            user: user.toLogObject(),
            eventInvitationsInput,
        });
        const success = await this.streamEventsInvitationsService.createEventInvitations(user, eventInvitationsInput);
        return {
            success,
        };
    }
};
exports.StreamEventsInvitationsResolver = StreamEventsInvitationsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => stream_events_invitations_service_1.StreamEventsInvitationsService)),
    __metadata("design:type", stream_events_invitations_service_1.StreamEventsInvitationsService)
], StreamEventsInvitationsResolver.prototype, "streamEventsInvitationsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], StreamEventsInvitationsResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Mutation)(() => StreamEventMessageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('eventInvitationData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_event_invitations_input_1.StreamCreateEventInvitationsInput]),
    __metadata("design:returntype", Promise)
], StreamEventsInvitationsResolver.prototype, "streamCreateEventInvitations", null);
exports.StreamEventsInvitationsResolver = StreamEventsInvitationsResolver = __decorate([
    (0, graphql_1.Resolver)()
], StreamEventsInvitationsResolver);
//# sourceMappingURL=stream-events-invitations.resolver.js.map