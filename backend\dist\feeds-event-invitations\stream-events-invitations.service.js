"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamEventsInvitationsService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const getstream_1 = require("getstream");
const config_1 = __importDefault(require("../config/config"));
const event_invitations_args_1 = require("../event-invitations/args/event-invitations.args");
const memberships_service_rights_1 = require("../memberships/helpers/memberships.service.rights");
const event_invitations_service_1 = require("../event-invitations/event-invitations.service");
const profiles_service_1 = require("../profiles/profiles.service");
const event_invitations_args_2 = require("./args/event-invitations.args");
const events_invitations_helper_1 = require("./helpers/events-invitations.helper");
const stream_followers_service_1 = require("../feeds-followers/stream-followers.service");
const short_uuid_1 = __importDefault(require("short-uuid"));
let StreamEventsInvitationsService = class StreamEventsInvitationsService {
    onModuleInit() {
        if (config_1.default.SERVICE === 'api-beta') {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_BETA_KEY, config_1.default.STREAM_BETA_SECRET);
        }
        else {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_KEY, config_1.default.STREAM_SECRET);
        }
    }
    async onApplicationBootstrap() {
        console.log('onApplicationBootstrap');
    }
    async createEventInvitations(user, createEventInvitationsInput) {
        try {
            const event = await this.client.collections.get('events', createEventInvitationsInput.eventId);
            const eventInvitationType = await this.verifyInvitePermission(user, event);
            if (createEventInvitationsInput.profileIds.length === 0) {
                throw new Error(`StreamEventsInvitationsService.createEventInvitations - profileIds can't be empty`);
                return false;
            }
            try {
                for (const profileId of createEventInvitationsInput.profileIds) {
                    const feedUser = this.client.feed('user', profileId);
                    const organisationRef = this.client.collections.entry('organisations', event.data.organisationId);
                    const profile = await this.profilesService.findById(user.profileId);
                    await feedUser.addActivity({
                        actor: 'SO:' + profileId,
                        foreign_id: 'events:' + createEventInvitationsInput.eventId,
                        verb: 'organisations',
                        object: organisationRef,
                        event: event,
                        status: eventInvitationType === event_invitations_args_2.StreamEventInvitationType.Hosts
                            ? event_invitations_args_2.StreamEventInvitationStatus.InvitedByHost
                            : event_invitations_args_2.StreamEventInvitationStatus.InvitedByGuest,
                        username: profile.name,
                        senderId: user.profileId,
                        inviterProfileId: user.profileId,
                    });
                }
                return true;
            }
            catch (e) {
                throw new Error(`StreamEventsInvitationsService.createEventInvitations - ` +
                    e.message);
            }
        }
        catch (e) {
            throw new Error(`StreamEventsInvitationsService.createEventInvitations - Event not found`);
        }
        return false;
    }
    async createEventInvitation(profileId, createEventInvitationInput, status, event) {
        try {
            const feedUser = this.client.feed('user', createEventInvitationInput.profileId);
            const organisationRef = this.client.collections.entry('organisations', event.data.organisationId);
            const profile = await this.profilesService.findById(profileId);
            await feedUser.addActivity({
                actor: 'SO:' + createEventInvitationInput.profileId,
                foreign_id: 'events:' + createEventInvitationInput.eventId,
                verb: 'organisations',
                object: organisationRef,
                event: event,
                status: status,
                username: profile.name,
                senderId: profileId,
                inviterProfileId: profileId,
            });
            return true;
        }
        catch (err) {
            throw new Error(`StreamEventsInvitationsService.createEventInvitations - ` +
                err.message);
        }
    }
    async verifyInvitePermission(user, event) {
        let isGuestInvitation = false;
        if (event.data.isGuestsCanInvite) {
            isGuestInvitation =
                await this.eventsInvitationsHelper.getRecursionActivity(event.data.eventId, user.profileId, 0, 1000);
        }
        try {
            await this.membershipsServiceRights.checkRights(event.data.organisationId, {
                currentProfileId: user.profileId,
                rights: [memberships_service_rights_1.MembershipRight.CreateEventInvitation],
            });
        }
        catch (e) {
            if (isGuestInvitation) {
                return event_invitations_args_1.EventInvitationType.Guest;
            }
            throw e;
        }
        return event_invitations_args_1.EventInvitationType.Hosts;
    }
    async createEventInviteFeedandBulkFollow(createEventInvitationsInput, user) {
        try {
            const event = await this.client.collections.get('events', createEventInvitationsInput.eventId);
            const randomId = short_uuid_1.default.generate();
            const invitePost = await this.client.collections.add('invites', randomId, Object.assign({
                profileId: user.profileId,
                users: createEventInvitationsInput.profileIds,
                organisation: event.data.organisationId,
                createdAt: new Date(),
            }, event.data));
            const feedInvite = await this.client.feed('invites', randomId);
            const organisationRef = this.client.collections.entry('organisations', event.data.organisationId);
            const params = {
                actor: 'SO:' + user.profileId,
                verb: 'events-invite',
                object: organisationRef,
                event: invitePost,
                foreign_id: `invite:${randomId}`,
                time: new Date().toISOString(),
            };
            await feedInvite.addActivity(params);
            const followsArray = [];
            for (const profileId of createEventInvitationsInput.profileIds) {
                const inviteFeedFollow = {
                    source: 'user:' + profileId,
                    target: 'invites:' + randomId,
                };
                followsArray.push(inviteFeedFollow);
            }
            await this.streamFollowersService.bulkFollowScript(followsArray);
            return true;
        }
        catch (err) {
            throw new Error(`StreamEventsInvitationsService.createEventInviteFeedandBulkFollow - ` +
                err.message);
        }
    }
};
exports.StreamEventsInvitationsService = StreamEventsInvitationsService;
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], StreamEventsInvitationsService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_rights_1.MembershipsServiceRights)),
    __metadata("design:type", memberships_service_rights_1.MembershipsServiceRights)
], StreamEventsInvitationsService.prototype, "membershipsServiceRights", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => event_invitations_service_1.EventInvitationsService)),
    __metadata("design:type", event_invitations_service_1.EventInvitationsService)
], StreamEventsInvitationsService.prototype, "eventInvitationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], StreamEventsInvitationsService.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => events_invitations_helper_1.EventsInvitationsHelper)),
    __metadata("design:type", events_invitations_helper_1.EventsInvitationsHelper)
], StreamEventsInvitationsService.prototype, "eventsInvitationsHelper", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => stream_followers_service_1.StreamFollowersService)),
    __metadata("design:type", stream_followers_service_1.StreamFollowersService)
], StreamEventsInvitationsService.prototype, "streamFollowersService", void 0);
exports.StreamEventsInvitationsService = StreamEventsInvitationsService = __decorate([
    (0, common_1.Injectable)()
], StreamEventsInvitationsService);
//# sourceMappingURL=stream-events-invitations.service.js.map