"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamEventsArgs = exports.StreamEventsFilter = exports.StreamEventType = void 0;
const graphql_1 = require("@nestjs/graphql");
const pagination_args_1 = require("../../common/args/pagination.args");
const event_invitations_args_1 = require("../../feeds-event-invitations/args/event-invitations.args");
var StreamEventType;
(function (StreamEventType) {
    StreamEventType["Event"] = "Event";
    StreamEventType["IncentiveStart"] = "IncentiveStart";
    StreamEventType["IncentiveEnd"] = "IncentiveEnd";
    StreamEventType["Webinar"] = "Webinar";
})(StreamEventType || (exports.StreamEventType = StreamEventType = {}));
(0, graphql_1.registerEnumType)(StreamEventType, { name: 'StreamEventType' });
let StreamEventsFilter = class StreamEventsFilter {
};
exports.StreamEventsFilter = StreamEventsFilter;
__decorate([
    (0, graphql_1.Field)(() => [event_invitations_args_1.StreamEventInvitationStatus], { nullable: true, defaultValue: [] }),
    __metadata("design:type", Array)
], StreamEventsFilter.prototype, "eventInvitationStatus", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], StreamEventsFilter.prototype, "organisationId", void 0);
__decorate([
    (0, graphql_1.Field)(() => [StreamEventType], { nullable: true, defaultValue: [] }),
    __metadata("design:type", Array)
], StreamEventsFilter.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true, defaultValue: null }),
    __metadata("design:type", Boolean)
], StreamEventsFilter.prototype, "isOnline", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true, defaultValue: null }),
    __metadata("design:type", Boolean)
], StreamEventsFilter.prototype, "isPublic", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], StreamEventsFilter.prototype, "searchText", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true, defaultValue: null }),
    __metadata("design:type", Boolean)
], StreamEventsFilter.prototype, "isEnded", void 0);
exports.StreamEventsFilter = StreamEventsFilter = __decorate([
    (0, graphql_1.InputType)()
], StreamEventsFilter);
let StreamEventsArgs = class StreamEventsArgs extends pagination_args_1.PaginationArgs {
};
exports.StreamEventsArgs = StreamEventsArgs;
__decorate([
    (0, graphql_1.Field)(() => StreamEventsFilter, { nullable: true }),
    __metadata("design:type", StreamEventsFilter)
], StreamEventsArgs.prototype, "filter", void 0);
exports.StreamEventsArgs = StreamEventsArgs = __decorate([
    (0, graphql_1.ArgsType)()
], StreamEventsArgs);
//# sourceMappingURL=events.args.js.map