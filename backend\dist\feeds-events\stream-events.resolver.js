"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamEventsResolver = exports.RsvpEventMessageResponse = exports.EventMessageResponse = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const stream_events_service_1 = require("./stream-events.service");
const create_event_input_1 = require("./dto/create-event.input");
const update_event_input_1 = require("./dto/update-event.input");
const event_invitations_args_1 = require("../feeds-event-invitations/args/event-invitations.args");
let EventMessageResponse = class EventMessageResponse {
};
exports.EventMessageResponse = EventMessageResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], EventMessageResponse.prototype, "success", void 0);
exports.EventMessageResponse = EventMessageResponse = __decorate([
    (0, graphql_1.ObjectType)()
], EventMessageResponse);
let RsvpEventMessageResponse = class RsvpEventMessageResponse {
};
exports.RsvpEventMessageResponse = RsvpEventMessageResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], RsvpEventMessageResponse.prototype, "result", void 0);
exports.RsvpEventMessageResponse = RsvpEventMessageResponse = __decorate([
    (0, graphql_1.ObjectType)()
], RsvpEventMessageResponse);
let StreamEventsResolver = class StreamEventsResolver {
    async streamCreateEvent(user, eventData) {
        this.logger.verbose('StreamEventsResolver.streamCreateEvent (mutation)', {
            user: user.toLogObject(),
            eventData,
        });
        eventData.description &&
            (eventData.description = eventData.description.replace(/\n\s*\n\s*\n/g, '\n\n'));
        const success = this.streamEventsService.createEvent(eventData, user.profileId, {
            currentUser: user,
        });
        return {
            success,
        };
    }
    async streamUpdateEvent(user, eventId, eventData) {
        this.logger.verbose('StreamEventsResolver.streamUpdateEvent (mutation)', {
            user: user.toLogObject(),
            eventData,
        });
        const success = this.streamEventsService.updateEvent(user, eventId, eventData);
        return {
            success,
        };
    }
    async streamRemoveEvent(user, eventId) {
        this.logger.verbose('StreamEventsResolver.streamRemoveEvent (mutation)', {
            user: user.toLogObject(),
            eventId,
        });
        await this.streamEventsService.removeEvent(eventId, user.profileId);
        return true;
    }
    async streamRsvpEvent(user, eventId, status) {
        this.logger.verbose('StreamEventsResolver.streamRsvpEvent (mutation)', {
            user: user.toLogObject(),
            eventId,
            status,
        });
        const result = await this.streamEventsService.streamRsvpEvent(user, eventId, {
            status,
        });
        return {
            result,
        };
    }
};
exports.StreamEventsResolver = StreamEventsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => stream_events_service_1.StreamEventsService)),
    __metadata("design:type", stream_events_service_1.StreamEventsService)
], StreamEventsResolver.prototype, "streamEventsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], StreamEventsResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Mutation)(() => EventMessageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('eventData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_event_input_1.StreamCreateEventInput]),
    __metadata("design:returntype", Promise)
], StreamEventsResolver.prototype, "streamCreateEvent", null);
__decorate([
    (0, graphql_1.Mutation)(() => EventMessageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('eventId')),
    __param(2, (0, graphql_1.Args)('eventData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, update_event_input_1.StreamUpdateEventInput]),
    __metadata("design:returntype", Promise)
], StreamEventsResolver.prototype, "streamUpdateEvent", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('eventId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], StreamEventsResolver.prototype, "streamRemoveEvent", null);
__decorate([
    (0, graphql_1.Mutation)(() => RsvpEventMessageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('eventId')),
    __param(2, (0, graphql_1.Args)('status', {
        type: () => event_invitations_args_1.StreamEventInvitationStatus,
        defaultValue: event_invitations_args_1.StreamEventInvitationStatus.Attending,
    })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], StreamEventsResolver.prototype, "streamRsvpEvent", null);
exports.StreamEventsResolver = StreamEventsResolver = __decorate([
    (0, graphql_1.Resolver)()
], StreamEventsResolver);
//# sourceMappingURL=stream-events.resolver.js.map