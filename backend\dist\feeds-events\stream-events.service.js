"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamEventsService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const getstream_1 = require("getstream");
const config_1 = __importDefault(require("../config/config"));
const memberships_service_1 = require("../memberships/memberships.service");
const event_invitations_service_1 = require("../event-invitations/event-invitations.service");
const event_invitations_args_1 = require("../feeds-event-invitations/args/event-invitations.args");
const profiles_service_1 = require("../profiles/profiles.service");
const activities_service_1 = require("../activities/activities.service");
const organisation_loyalty_points_service_1 = require("../organisation-loyalty-points/organisation-loyalty-points.service");
const activities_args_1 = require("../activities/args/activities.args");
const organisations_service_1 = require("../organisations/organisations.service");
let StreamEventsService = class StreamEventsService {
    onModuleInit() {
        if (config_1.default.SERVICE === 'api-beta') {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_BETA_KEY, config_1.default.STREAM_BETA_SECRET);
        }
        else {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_KEY, config_1.default.STREAM_SECRET);
        }
    }
    async onApplicationBootstrap() {
        console.log('onApplicationBootstrap');
    }
    async createEvent(dto, profileId, options) {
        try {
            const event = await this.client.collections.add('events', dto.eventId, dto);
            const organisationRef = this.client.collections.entry('organisations', event.data.organisationId);
            const feedUser = await this.client.feed('user', profileId);
            const profile = await this.profilesService.findById(profileId);
            await feedUser.addActivity({
                actor: 'SO:' + profileId,
                foreign_id: 'events:' + dto.eventId,
                verb: 'organisations',
                object: organisationRef,
                event: event,
                status: event_invitations_args_1.StreamEventInvitationStatus.InvitedByHost,
                username: profile.name,
                senderId: profileId,
                inviterProfileId: profileId,
            });
            const activityData = {
                eventId: dto.eventId,
                data: { eventId: dto.eventId },
                organisationId: dto.organisationId,
                type: activities_args_1.ActivityType.CreateEvent,
                createdById: profileId,
            };
            await this.activityService.create({
                profileId: profileId,
                data: activityData,
                type: 'CreateEvent',
            });
            const orgActivityData = {
                eventId: dto.eventId,
                data: { eventId: dto.eventId },
                organisationId: dto.organisationId,
                type: activities_args_1.ActivityType.CreateEvent,
                createdById: profileId,
            };
            await this.activityService.createOrganisationActivity(orgActivityData, {});
            return true;
        }
        catch (e) {
            throw new Error(`StreamEventsService.streamCreateEvent - ` + e.message);
        }
        return false;
    }
    async updateEvent(user, id, dto) {
        this.logger.verbose('StreamEventsService.updateEvent', {
            user: user.toLogObject(),
            eventId: id,
            dto,
        });
        try {
            const event = await this.client.collections.get('events', id);
            dto.description &&
                (dto.description = dto.description.replace(/\n\s*\n\s*\n/g, '\n\n'));
            await this.client.collections.update('events', id, Object.assign(event.data, dto));
            return true;
        }
        catch (e) {
            throw new Error(`StreamEventsService.streamUpdateEvent - ` + e.message);
        }
        return false;
    }
    async removeEvent(id, profileId) {
        try {
            const event = await this.client.collections.get('events', id);
            const membership = await this.membershipsService.findMembership(event.data.organisationId, profileId);
            if (membership && membership.permissions) {
                const eventInvitationsDB = await this.eventInvitationsService.findAll({
                    eventId: id,
                });
                const currentFeedUser = await this.client.feed('user', profileId);
                await currentFeedUser.removeActivity({ foreignId: 'events:' + id });
                for (const invitation of eventInvitationsDB) {
                    const feedUser = await this.client.feed('user', invitation.profileId);
                    await feedUser.removeActivity({ foreignId: 'events:' + id });
                }
                await this.client.collections.delete('events', id);
                return true;
            }
        }
        catch (e) {
            throw new Error(`StreamEventsService.streamRemoveEvent - ` + e.message);
        }
        return false;
    }
    async streamRsvpEvent(user, id, options) {
        this.logger.info('StreamEventsService.rsvpEvent', {
            user: user.toLogObject(),
            id,
            status: options.status,
        });
        const event = await this.client.collections.get('events', id);
        if (event.data) {
            const feedUser = this.client.feed('user', user.profileId);
            const getActivities = await feedUser.get({ limit: 100 });
            const status = options.status;
            let eventInvitationStatus = null;
            if ([
                event_invitations_args_1.StreamEventInvitationStatus.Declined,
                event_invitations_args_1.StreamEventInvitationStatus.InvitedDeclined,
            ].includes(status)) {
                await feedUser.removeActivity({ foreignId: 'events-updates:' + id });
                await feedUser.unfollow('events', id);
            }
            if (getActivities.results.length != 0) {
                for (const activity of getActivities.results) {
                    if (activity.foreign_id == 'events:' + id) {
                        eventInvitationStatus = activity.status;
                        await this.client.activityPartialUpdate({
                            id: activity.id,
                            set: {
                                status,
                            },
                        });
                        break;
                    }
                }
                if (eventInvitationStatus) {
                    if ([
                        event_invitations_args_1.StreamEventInvitationStatus.InvitedAttending,
                        event_invitations_args_1.StreamEventInvitationStatus.Attending,
                        event_invitations_args_1.StreamEventInvitationStatus.Interested,
                        event_invitations_args_1.StreamEventInvitationStatus.InvitedInterested,
                    ].includes(status)) {
                        await feedUser.follow('events', id);
                    }
                    return true;
                }
            }
            if (event.isHostsManagedRSVP &&
                event.isPublic &&
                ![
                    event_invitations_args_1.StreamEventInvitationStatus.InvitedByHost,
                    event_invitations_args_1.StreamEventInvitationStatus.Attending,
                    event_invitations_args_1.StreamEventInvitationStatus.Interested,
                    event_invitations_args_1.StreamEventInvitationStatus.Declined,
                ].includes(eventInvitationStatus)) {
                if (![
                    event_invitations_args_1.StreamEventInvitationStatus.InvitedAttending,
                    event_invitations_args_1.StreamEventInvitationStatus.InvitedInterested,
                ].includes(options.status)) {
                    throw new Error(`StreamEventsService.streamRsvpEvent - Only InvitedAttending or InvitedInterested status can be set for public events (managed by hosts)`);
                    return false;
                }
            }
            if (event.isHostsManagedRSVP &&
                event.isPublic &&
                eventInvitationStatus === event_invitations_args_1.StreamEventInvitationStatus.InvitedByHost) {
                if (![
                    event_invitations_args_1.StreamEventInvitationStatus.Attending,
                    event_invitations_args_1.StreamEventInvitationStatus.Interested,
                    event_invitations_args_1.StreamEventInvitationStatus.Declined,
                ].includes(options.status)) {
                    throw new Error(`StreamEventsService.streamRsvpEvent - Only Attending, Interested or Declined status can be set for public events (managed by hosts, invited by host)`);
                    return false;
                }
            }
            if (!event.isPublic &&
                eventInvitationStatus === event_invitations_args_1.StreamEventInvitationStatus.InvitedByGuest) {
                if (![
                    event_invitations_args_1.StreamEventInvitationStatus.InvitedAttending,
                    event_invitations_args_1.StreamEventInvitationStatus.InvitedInterested,
                    event_invitations_args_1.StreamEventInvitationStatus.InvitedDeclined,
                ].includes(options.status)) {
                    throw new Error(`StreamEventsService.streamRsvpEvent - Only InvitedAttending, InvitedInterested or InvitedDeclined status can be set for private events (invited by guest)`);
                    return false;
                }
            }
            if ((event.data.isPublic && !event.data.isHostsManagedRSVP) ||
                (!event.data.isPublic &&
                    [
                        event_invitations_args_1.StreamEventInvitationStatus.Attending,
                        event_invitations_args_1.StreamEventInvitationStatus.Declined,
                        event_invitations_args_1.StreamEventInvitationStatus.Interested,
                    ].includes(eventInvitationStatus))) {
                if (![
                    event_invitations_args_1.StreamEventInvitationStatus.Attending,
                    event_invitations_args_1.StreamEventInvitationStatus.Declined,
                    event_invitations_args_1.StreamEventInvitationStatus.Interested,
                ].includes(options.status)) {
                    throw new Error(`StreamEventsService.streamRsvpEvent - Only Attending, Interested or Declined status can be set`);
                    return false;
                }
            }
            if ([
                event_invitations_args_1.StreamEventInvitationStatus.InvitedAttending,
                event_invitations_args_1.StreamEventInvitationStatus.Attending,
                event_invitations_args_1.StreamEventInvitationStatus.Interested,
                event_invitations_args_1.StreamEventInvitationStatus.InvitedInterested,
            ].includes(status)) {
                await feedUser.follow('events', id);
            }
            return true;
        }
        else {
            throw new Error(`StreamEventsService.streamRsvpEvent - Event does not exist.`);
        }
        return false;
    }
};
exports.StreamEventsService = StreamEventsService;
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], StreamEventsService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], StreamEventsService.prototype, "membershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => event_invitations_service_1.EventInvitationsService)),
    __metadata("design:type", event_invitations_service_1.EventInvitationsService)
], StreamEventsService.prototype, "eventInvitationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], StreamEventsService.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => activities_service_1.ActivitiesService)),
    __metadata("design:type", activities_service_1.ActivitiesService)
], StreamEventsService.prototype, "activityService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisation_loyalty_points_service_1.OrganisationLoyaltyPointsService)),
    __metadata("design:type", organisation_loyalty_points_service_1.OrganisationLoyaltyPointsService)
], StreamEventsService.prototype, "organisationLoyaltyPointsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], StreamEventsService.prototype, "organisationsService", void 0);
exports.StreamEventsService = StreamEventsService = __decorate([
    (0, common_1.Injectable)()
], StreamEventsService);
//# sourceMappingURL=stream-events.service.js.map