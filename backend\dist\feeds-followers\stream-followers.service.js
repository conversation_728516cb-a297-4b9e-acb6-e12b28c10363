"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamFollowersService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const getstream_1 = require("getstream");
const config_1 = __importDefault(require("../config/config"));
const membership_model_1 = require("../memberships/models/membership.model");
const underscore_1 = require("../common/helpers/underscore");
const memberships_service_1 = require("../memberships/memberships.service");
let StreamFollowersService = class StreamFollowersService {
    onModuleInit() {
        if (config_1.default.SERVICE === 'api-beta') {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_BETA_KEY, config_1.default.STREAM_BETA_SECRET);
        }
        else {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_KEY, config_1.default.STREAM_SECRET);
        }
    }
    async onApplicationBootstrap() {
        console.log('onApplicationBootstrap');
    }
    async follow(user, organisationId) {
        var _a;
        this.logger.verbose('StreamFollowersService.follow', {
            user: user.profileId,
            organisationId,
        });
        const userFeed = this.client.feed('user', user.profileId);
        let response = await userFeed.following({
            offset: 0,
            limit: 1,
            filter: ['organisations:' + organisationId],
        });
        if (response.results.length != 0)
            return true;
        const organisation = await this.client.feed('organisations', organisationId);
        if (!organisation) {
            throw new Error(`Organisation not found`);
        }
        const existingMembership = await this.membershipsService.findOne({
            organisationId,
            profileId: user.profileId,
        });
        const isAutoApprove = existingMembership &&
            existingMembership.status === membership_model_1.MembershipStatus.Active &&
            underscore_1.Underscore.includesAny(existingMembership.permissions, [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
            ]);
        try {
            await userFeed.follow('organisations', organisationId);
            return true;
        }
        catch (e) {
            this.logger.error(`StreamFollowersService Error: ${e.message}`, (_a = e.response) === null || _a === void 0 ? void 0 : _a.body);
        }
    }
    async unfollow(profileId, organisationId) {
        this.logger.verbose('StreamFollowersService.unfollow', {
            profileId,
            organisationId,
        });
        if (this.client) {
            const organisation = await this.client.feed('organisations', organisationId);
            if (!organisation) {
                throw new Error(`Organisation not found`);
            }
            const userFeed = this.client.feed('user', profileId);
            await userFeed.unfollow('organisations', organisationId);
        }
        return true;
    }
    async updatePrimaryOrganisation(profileId, oldOrganisationId, newOrganisationId) {
        var _a;
        this.logger.verbose('StreamFollowersService.updatePrimaryOrganisation', {
            profileId,
            oldOrganisationId,
            newOrganisationId,
        });
        const userFeed = await this.client.feed('user', profileId);
        try {
            if (oldOrganisationId) {
                await userFeed.unfollow('org_feed_targeted', oldOrganisationId);
                await userFeed.unfollow('org_feed_members', oldOrganisationId);
            }
            if (newOrganisationId) {
                await userFeed.follow('org_feed_targeted', newOrganisationId);
                await userFeed.follow('org_feed_members', newOrganisationId);
                await userFeed.follow('organisations', newOrganisationId);
            }
        }
        catch (err) {
            this.logger.error(`Stream.Service Error - updatePrimaryOrganisation: ${err.message}`, (_a = err.response) === null || _a === void 0 ? void 0 : _a.body);
        }
        return true;
    }
    async bulkFollowScript(followsArray) {
        var _a;
        let follows = followsArray;
        try {
            if (follows.length > 100) {
                const chunkSize = 100;
                const followsChunk = [];
                for (let i = 0; i < follows.length; i = i + chunkSize) {
                    const groupOfFollows = follows.slice(i, i + chunkSize);
                    followsChunk.push(groupOfFollows);
                }
                for (const chunk of followsChunk) {
                    await this.client.followMany(chunk);
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }
            else {
                await this.client.followMany(follows);
            }
        }
        catch (err) {
            this.logger.error(`Stream.Service Error - bulkFollowScript: ${err.message}`, (_a = err.response) === null || _a === void 0 ? void 0 : _a.body);
        }
    }
    async bulkUnfollowScript(unfollowsArray) {
        var _a;
        let unfollows = unfollowsArray;
        try {
            let i = 0;
            for (const unfollow of unfollows) {
                const userFeed = await this.client.feed('user', unfollow.source);
                await userFeed.unfollow('org_feed_targeted', unfollow.target);
                await userFeed.unfollow('org_feed_members', unfollow.target);
                await userFeed.unfollow('organisations', unfollow.target);
                if (i % 100 === 0) {
                    await new Promise(resolve => setTimeout(resolve, 3000));
                }
                i++;
            }
        }
        catch (err) {
            this.logger.error(`Stream.Service Error - bulkUnfollowScript: ${err.message}`, (_a = err.response) === null || _a === void 0 ? void 0 : _a.body);
        }
    }
};
exports.StreamFollowersService = StreamFollowersService;
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], StreamFollowersService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], StreamFollowersService.prototype, "membershipsService", void 0);
exports.StreamFollowersService = StreamFollowersService = __decorate([
    (0, common_1.Injectable)()
], StreamFollowersService);
//# sourceMappingURL=stream-followers.service.js.map