"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamIncentiveParticipantsArgs = exports.StreamIncentiveParticipantsFilter = exports.IncentiveParticipantStatus = void 0;
const graphql_1 = require("@nestjs/graphql");
var IncentiveParticipantStatus;
(function (IncentiveParticipantStatus) {
    IncentiveParticipantStatus["Registered"] = "Registered";
    IncentiveParticipantStatus["InvitedByHost"] = "InvitedByHost";
    IncentiveParticipantStatus["InvitedByParticipant"] = "InvitedByParticipant";
    IncentiveParticipantStatus["InvitedRegistered"] = "InvitedRegistered";
    IncentiveParticipantStatus["Blocked"] = "Blocked";
})(IncentiveParticipantStatus || (exports.IncentiveParticipantStatus = IncentiveParticipantStatus = {}));
(0, graphql_1.registerEnumType)(IncentiveParticipantStatus, {
    name: 'IncentiveParticipantStatus',
});
let StreamIncentiveParticipantsFilter = class StreamIncentiveParticipantsFilter {
};
exports.StreamIncentiveParticipantsFilter = StreamIncentiveParticipantsFilter;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID, {
        nullable: true,
    }),
    __metadata("design:type", String)
], StreamIncentiveParticipantsFilter.prototype, "profileId", void 0);
__decorate([
    (0, graphql_1.Field)(() => [IncentiveParticipantStatus], {
        nullable: true,
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], StreamIncentiveParticipantsFilter.prototype, "status", void 0);
exports.StreamIncentiveParticipantsFilter = StreamIncentiveParticipantsFilter = __decorate([
    (0, graphql_1.InputType)()
], StreamIncentiveParticipantsFilter);
let StreamIncentiveParticipantsArgs = class StreamIncentiveParticipantsArgs {
};
exports.StreamIncentiveParticipantsArgs = StreamIncentiveParticipantsArgs;
__decorate([
    (0, graphql_1.Field)(() => StreamIncentiveParticipantsFilter, { nullable: true }),
    __metadata("design:type", StreamIncentiveParticipantsFilter)
], StreamIncentiveParticipantsArgs.prototype, "filter", void 0);
exports.StreamIncentiveParticipantsArgs = StreamIncentiveParticipantsArgs = __decorate([
    (0, graphql_1.ArgsType)()
], StreamIncentiveParticipantsArgs);
//# sourceMappingURL=incentive-participants.args.js.map