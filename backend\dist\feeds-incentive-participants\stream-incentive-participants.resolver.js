"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamIncentiveParticipantsResolver = exports.StreamIncentiveMessageResponse = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const stream_incentive_participants_service_1 = require("./stream-incentive-participants.service");
let StreamIncentiveMessageResponse = class StreamIncentiveMessageResponse {
};
exports.StreamIncentiveMessageResponse = StreamIncentiveMessageResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], StreamIncentiveMessageResponse.prototype, "success", void 0);
exports.StreamIncentiveMessageResponse = StreamIncentiveMessageResponse = __decorate([
    (0, graphql_1.ObjectType)()
], StreamIncentiveMessageResponse);
let StreamIncentiveParticipantsResolver = class StreamIncentiveParticipantsResolver {
};
exports.StreamIncentiveParticipantsResolver = StreamIncentiveParticipantsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => stream_incentive_participants_service_1.StreamIncentiveParticipantsService)),
    __metadata("design:type", stream_incentive_participants_service_1.StreamIncentiveParticipantsService)
], StreamIncentiveParticipantsResolver.prototype, "streamIncentiveParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], StreamIncentiveParticipantsResolver.prototype, "logger", void 0);
exports.StreamIncentiveParticipantsResolver = StreamIncentiveParticipantsResolver = __decorate([
    (0, graphql_1.Resolver)()
], StreamIncentiveParticipantsResolver);
//# sourceMappingURL=stream-incentive-participants.resolver.js.map