"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamCreateIncentiveDto = void 0;
const class_validator_1 = require("class-validator");
const incentives_args_1 = require("../args/incentives.args");
const update_incentive_dto_1 = require("./update-incentive.dto");
class StreamCreateIncentiveDto extends update_incentive_dto_1.StreamUpdateIncentiveDto {
}
exports.StreamCreateIncentiveDto = StreamCreateIncentiveDto;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], StreamCreateIncentiveDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], StreamCreateIncentiveDto.prototype, "incentiveId", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], StreamCreateIncentiveDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], StreamCreateIncentiveDto.prototype, "isAllDay", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", Date)
], StreamCreateIncentiveDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", Date)
], StreamCreateIncentiveDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", Array)
], StreamCreateIncentiveDto.prototype, "regions", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", Array)
], StreamCreateIncentiveDto.prototype, "bookingTypes", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", Array)
], StreamCreateIncentiveDto.prototype, "bookingFields", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], StreamCreateIncentiveDto.prototype, "isPublic", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], StreamCreateIncentiveDto.prototype, "isParticipantListVisible", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", String)
], StreamCreateIncentiveDto.prototype, "organisationId", void 0);
//# sourceMappingURL=create-incentive.dto.js.map