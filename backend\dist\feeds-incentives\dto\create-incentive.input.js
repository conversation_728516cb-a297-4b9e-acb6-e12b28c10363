"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamCreateIncentiveInput = exports.StreamIncentiveBookingFieldInput = void 0;
const graphql_1 = require("@nestjs/graphql");
const update_incentive_input_1 = require("./update-incentive.input");
const class_validator_1 = require("class-validator");
const incentives_args_1 = require("../args/incentives.args");
const profiles_args_1 = require("../../profiles/args/profiles.args");
let StreamIncentiveBookingFieldInput = class StreamIncentiveBookingFieldInput {
};
exports.StreamIncentiveBookingFieldInput = StreamIncentiveBookingFieldInput;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], StreamIncentiveBookingFieldInput.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)(() => incentives_args_1.StreamIncentiveBookingFieldType, {
        defaultValue: incentives_args_1.StreamIncentiveBookingFieldType.String,
    }),
    __metadata("design:type", String)
], StreamIncentiveBookingFieldInput.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { defaultValue: false }),
    __metadata("design:type", Boolean)
], StreamIncentiveBookingFieldInput.prototype, "isOptional", void 0);
exports.StreamIncentiveBookingFieldInput = StreamIncentiveBookingFieldInput = __decorate([
    (0, graphql_1.InputType)()
], StreamIncentiveBookingFieldInput);
let StreamCreateIncentiveInput = class StreamCreateIncentiveInput extends update_incentive_input_1.StreamUpdateIncentiveInput {
};
exports.StreamCreateIncentiveInput = StreamCreateIncentiveInput;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], StreamCreateIncentiveInput.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], StreamCreateIncentiveInput.prototype, "incentiveId", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    (0, graphql_1.Field)(() => incentives_args_1.StreamIncentiveType),
    __metadata("design:type", String)
], StreamCreateIncentiveInput.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], StreamCreateIncentiveInput.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], StreamCreateIncentiveInput.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], StreamCreateIncentiveInput.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(() => [profiles_args_1.Region]),
    __metadata("design:type", Array)
], StreamCreateIncentiveInput.prototype, "regions", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(() => [incentives_args_1.StreamIncentiveBookingType]),
    __metadata("design:type", Array)
], StreamCreateIncentiveInput.prototype, "bookingTypes", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(() => [StreamIncentiveBookingFieldInput]),
    __metadata("design:type", Array)
], StreamCreateIncentiveInput.prototype, "bookingFields", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], StreamCreateIncentiveInput.prototype, "isPublic", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], StreamCreateIncentiveInput.prototype, "isParticipantListVisible", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(() => graphql_1.ID, { nullable: false }),
    __metadata("design:type", String)
], StreamCreateIncentiveInput.prototype, "organisationId", void 0);
exports.StreamCreateIncentiveInput = StreamCreateIncentiveInput = __decorate([
    (0, graphql_1.InputType)()
], StreamCreateIncentiveInput);
//# sourceMappingURL=create-incentive.input.js.map