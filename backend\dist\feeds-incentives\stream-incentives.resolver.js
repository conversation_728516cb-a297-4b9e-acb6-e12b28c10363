"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamIncentivesResolver = exports.IncentiveMessageResponse = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const stream_incentives_service_1 = require("./stream-incentives.service");
const create_incentive_input_1 = require("./dto/create-incentive.input");
const organisations_service_1 = require("../organisations/organisations.service");
const incentive_model_1 = require("../incentives/models/incentive.model");
const update_incentive_input_1 = require("./dto/update-incentive.input");
let IncentiveMessageResponse = class IncentiveMessageResponse {
};
exports.IncentiveMessageResponse = IncentiveMessageResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], IncentiveMessageResponse.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], IncentiveMessageResponse.prototype, "organisationId", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], IncentiveMessageResponse.prototype, "vanityId", void 0);
exports.IncentiveMessageResponse = IncentiveMessageResponse = __decorate([
    (0, graphql_1.ObjectType)()
], IncentiveMessageResponse);
let StreamIncentivesResolver = class StreamIncentivesResolver {
    async streamCreateIncentive(user, incentiveData) {
        this.logger.verbose('StreamIncentivesResolver.streamCreateIncentive (mutation)', {
            user: user.toLogObject(),
            incentiveData
        });
        const incentive = await this.streamIncentivesService.createIncentive(incentiveData);
        if (incentive) {
            const organisation = await this.organisationsService.findById(incentive.data.organisationId, {
                useCache: true
            });
            if (!organisation) {
                throw new Error(`Organisation not found`);
            }
            return {
                id: incentiveData.incentiveId,
                organisationId: incentiveData.organisationId,
                vanityId: organisation.vanityId
            };
        }
        return {
            id: 'false',
            organisationId: 'false',
            vanityId: 'false'
        };
    }
    async streamRemoveIncentive(user, id) {
        this.logger.verbose('StreamIncentivesResolver.streamRemoveIncentive (mutation)', {
            user: user.toLogObject(),
            id,
        });
        await this.streamIncentivesService.removeIncentive(id, user.profileId);
        return true;
    }
    async streamUpdateIncentive(user, incentiveId, incentiveData) {
        this.logger.verbose('StreamIncentivesResolver.streamUpdateIncentive (mutation)', {
            user: user.toLogObject(),
            incentiveData,
        });
        const incentive = await this.streamIncentivesService.updateIncentive(user, incentiveId, incentiveData);
        if (incentive) {
            const organisation = await this.organisationsService.findById(incentive.organisationId, {
                useCache: true
            });
            if (!organisation) {
                throw new Error(`Organisation not found`);
            }
            return {
                id: incentiveId,
                organisationId: incentive.organisationId,
                vanityId: organisation.vanityId
            };
        }
        return {
            id: 'false',
            organisationId: 'false',
            vanityId: 'false'
        };
    }
};
exports.StreamIncentivesResolver = StreamIncentivesResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => stream_incentives_service_1.StreamIncentivesService)),
    __metadata("design:type", stream_incentives_service_1.StreamIncentivesService)
], StreamIncentivesResolver.prototype, "streamIncentivesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], StreamIncentivesResolver.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], StreamIncentivesResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Mutation)(() => IncentiveMessageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('incentiveData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_incentive_input_1.StreamCreateIncentiveInput]),
    __metadata("design:returntype", Promise)
], StreamIncentivesResolver.prototype, "streamCreateIncentive", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], StreamIncentivesResolver.prototype, "streamRemoveIncentive", null);
__decorate([
    (0, graphql_1.Mutation)(() => IncentiveMessageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('incentiveId')),
    __param(2, (0, graphql_1.Args)('incentiveData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, update_incentive_input_1.StreamUpdateIncentiveInput]),
    __metadata("design:returntype", Promise)
], StreamIncentivesResolver.prototype, "streamUpdateIncentive", null);
exports.StreamIncentivesResolver = StreamIncentivesResolver = __decorate([
    (0, graphql_1.Resolver)(() => incentive_model_1.Incentive)
], StreamIncentivesResolver);
//# sourceMappingURL=stream-incentives.resolver.js.map