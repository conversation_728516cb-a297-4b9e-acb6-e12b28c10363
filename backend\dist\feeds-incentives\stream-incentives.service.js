"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamIncentivesService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const getstream_1 = require("getstream");
const config_1 = __importDefault(require("../config/config"));
const incentives_service_1 = require("../incentives/incentives.service");
const memberships_service_1 = require("../memberships/memberships.service");
const incentive_participants_service_1 = require("../incentive-participants/incentive-participants.service");
const profiles_service_1 = require("../profiles/profiles.service");
let StreamIncentivesService = class StreamIncentivesService {
    onModuleInit() {
        if (config_1.default.SERVICE === 'api-beta') {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_BETA_KEY, config_1.default.STREAM_BETA_SECRET);
        }
        else {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_KEY, config_1.default.STREAM_SECRET);
        }
    }
    async onApplicationBootstrap() {
        console.log('onApplicationBootstrap');
    }
    async createIncentive(dto) {
        dto.description &&
            (dto.description = dto.description.replace(/\n\s*\n\s*\n/g, '\n\n'));
        dto.terms && (dto.terms = dto.terms.replace(/\n\s*\n\s*\n/g, '\n\n'));
        try {
            return await this.client.collections.add('incentives', dto.incentiveId, dto);
        }
        catch (e) {
            throw new Error(`StreamIncentivesService.createIncentive - ` + e.message);
        }
        return false;
    }
    async removeIncentive(id, profileId) {
        try {
            const incentiveDb = await this.incentivesService.findById(id);
            try {
                const incentive = await this.client.collections.get('incentives', id);
                await this.client.collections.delete('incentives', id);
            }
            catch (err) { }
            const membership = await this.membershipsService.findMembership(incentiveDb.organisationId, profileId);
            if (membership && membership.permissions) {
                const incentiveParticipantDB = await this.incentiveParticipantsService.findAll({
                    incentiveId: id,
                });
                for (const participant of incentiveParticipantDB) {
                    const feedUser = this.client.feed('user', participant.profileId);
                    await feedUser.removeActivity({ foreignId: 'incentives:' + id });
                }
            }
            return true;
        }
        catch (e) {
            throw new Error(`StreamIncentivesService.removeIncentive - ` + e.message);
        }
    }
    async updateIncentive(user, id, dto) {
        this.logger.verbose('StreamIncentivesService.updateIncentive', {
            user: user.toLogObject(),
            incentiveId: id,
            dto,
        });
        try {
            let incentive = await this.client.collections.get('incentives', id);
            dto.description &&
                (dto.description = dto.description.replace(/\n\s*\n\s*\n/g, '\n\n'));
            incentive = await this.client.collections.update('incentives', id, Object.assign(incentive.data, dto));
            return incentive.data;
        }
        catch (e) {
            throw new Error(`StreamIncentivesService.updateIncentive - ` + e.message);
        }
        return false;
    }
    async register(user, id, status) {
        this.logger.verbose('StreamIncentivesService.register', {
            user: user.toLogObject(),
            incentiveId: id,
        });
        try {
            const incentive = await this.client.collections.get('incentives', id);
            const feedUser = this.client.feed('user', user.profileId);
            await feedUser.follow('incentives', id);
            const getActivities = await feedUser.get({ limit: 100 });
            for (const activity of getActivities.results) {
                if (activity.foreign_id == 'incentives:' + id) {
                    await this.client.activityPartialUpdate({
                        id: activity.id,
                        set: {
                            status,
                        },
                    });
                    break;
                }
            }
            return {
                id,
                status,
            };
        }
        catch (e) {
            throw new Error(`StreamIncentivesService.register - ` + e.message);
        }
    }
};
exports.StreamIncentivesService = StreamIncentivesService;
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], StreamIncentivesService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentives_service_1.IncentivesService)),
    __metadata("design:type", incentives_service_1.IncentivesService)
], StreamIncentivesService.prototype, "incentivesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], StreamIncentivesService.prototype, "membershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentive_participants_service_1.IncentiveParticipantsService)),
    __metadata("design:type", incentive_participants_service_1.IncentiveParticipantsService)
], StreamIncentivesService.prototype, "incentiveParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], StreamIncentivesService.prototype, "profilesService", void 0);
exports.StreamIncentivesService = StreamIncentivesService = __decorate([
    (0, common_1.Injectable)()
], StreamIncentivesService);
//# sourceMappingURL=stream-incentives.service.js.map