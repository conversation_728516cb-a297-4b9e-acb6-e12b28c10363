"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamOrganisationPrivacySettings = exports.StreamOrganisationPrivacyAccessSettings = exports.StreamOrganisationSize = exports.StreamOrganisationStatus = exports.StreamOrganisationType = void 0;
const graphql_1 = require("@nestjs/graphql");
var StreamOrganisationType;
(function (StreamOrganisationType) {
    StreamOrganisationType["Destination"] = "Destination";
    StreamOrganisationType["PrivateSector"] = "PrivateSector";
    StreamOrganisationType["RepresentationAgency"] = "RepresentationAgency";
    StreamOrganisationType["TourOperator"] = "TourOperator";
    StreamOrganisationType["TravelAgency"] = "TravelAgency";
    StreamOrganisationType["Community"] = "Community";
})(StreamOrganisationType || (exports.StreamOrganisationType = StreamOrganisationType = {}));
(0, graphql_1.registerEnumType)(StreamOrganisationType, { name: 'StreamOrganisationType' });
var StreamOrganisationStatus;
(function (StreamOrganisationStatus) {
    StreamOrganisationStatus["Active"] = "Active";
    StreamOrganisationStatus["Pending"] = "Pending";
    StreamOrganisationStatus["Removed"] = "Removed";
    StreamOrganisationStatus["Suspended"] = "Suspended";
})(StreamOrganisationStatus || (exports.StreamOrganisationStatus = StreamOrganisationStatus = {}));
(0, graphql_1.registerEnumType)(StreamOrganisationStatus, { name: 'StreamOrganisationStatus' });
var StreamOrganisationSize;
(function (StreamOrganisationSize) {
    StreamOrganisationSize["OrgSize1"] = "1";
    StreamOrganisationSize["OrgSize2_10"] = "2-10";
    StreamOrganisationSize["OrgSize11_50"] = "11-50";
    StreamOrganisationSize["OrgSize51_200"] = "51-200";
    StreamOrganisationSize["OrgSize201_500"] = "201-500";
    StreamOrganisationSize["OrgSize501_1000"] = "501-1000";
    StreamOrganisationSize["OrgSize1001_5000"] = "1001-5000";
    StreamOrganisationSize["OrgSize5001"] = "5001+";
})(StreamOrganisationSize || (exports.StreamOrganisationSize = StreamOrganisationSize = {}));
(0, graphql_1.registerEnumType)(StreamOrganisationSize, { name: 'StreamOrganisationSize' });
let StreamOrganisationPrivacyAccessSettings = class StreamOrganisationPrivacyAccessSettings {
};
exports.StreamOrganisationPrivacyAccessSettings = StreamOrganisationPrivacyAccessSettings;
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], StreamOrganisationPrivacyAccessSettings.prototype, "public", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], StreamOrganisationPrivacyAccessSettings.prototype, "approvedFollowers", void 0);
exports.StreamOrganisationPrivacyAccessSettings = StreamOrganisationPrivacyAccessSettings = __decorate([
    (0, graphql_1.InputType)()
], StreamOrganisationPrivacyAccessSettings);
let StreamOrganisationPrivacySettings = class StreamOrganisationPrivacySettings {
};
exports.StreamOrganisationPrivacySettings = StreamOrganisationPrivacySettings;
__decorate([
    (0, graphql_1.Field)(() => StreamOrganisationPrivacyAccessSettings, { nullable: true }),
    __metadata("design:type", StreamOrganisationPrivacyAccessSettings)
], StreamOrganisationPrivacySettings.prototype, "postsAccess", void 0);
__decorate([
    (0, graphql_1.Field)(() => StreamOrganisationPrivacyAccessSettings, { nullable: true }),
    __metadata("design:type", StreamOrganisationPrivacyAccessSettings)
], StreamOrganisationPrivacySettings.prototype, "eventsAccess", void 0);
__decorate([
    (0, graphql_1.Field)(() => StreamOrganisationPrivacyAccessSettings, { nullable: true }),
    __metadata("design:type", StreamOrganisationPrivacyAccessSettings)
], StreamOrganisationPrivacySettings.prototype, "webinarsAccess", void 0);
__decorate([
    (0, graphql_1.Field)(() => StreamOrganisationPrivacyAccessSettings, { nullable: true }),
    __metadata("design:type", StreamOrganisationPrivacyAccessSettings)
], StreamOrganisationPrivacySettings.prototype, "incentivesAccess", void 0);
__decorate([
    (0, graphql_1.Field)(() => StreamOrganisationPrivacyAccessSettings, { nullable: true }),
    __metadata("design:type", StreamOrganisationPrivacyAccessSettings)
], StreamOrganisationPrivacySettings.prototype, "trainingsAccess", void 0);
exports.StreamOrganisationPrivacySettings = StreamOrganisationPrivacySettings = __decorate([
    (0, graphql_1.InputType)()
], StreamOrganisationPrivacySettings);
//# sourceMappingURL=organisations.args.js.map