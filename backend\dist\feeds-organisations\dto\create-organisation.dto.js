"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateStreamOrganisationDto = void 0;
const class_validator_1 = require("class-validator");
const organisations_args_1 = require("../args/organisations.args");
class CreateStreamOrganisationDto {
}
exports.CreateStreamOrganisationDto = CreateStreamOrganisationDto;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateStreamOrganisationDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateStreamOrganisationDto.prototype, "organisationId", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateStreamOrganisationDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateStreamOrganisationDto.prototype, "image", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateStreamOrganisationDto.prototype, "backgroundImage", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], CreateStreamOrganisationDto.prototype, "location", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateStreamOrganisationDto.prototype, "privacy", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateStreamOrganisationDto.prototype, "peoplePrivacy", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateStreamOrganisationDto.prototype, "website", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateStreamOrganisationDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateStreamOrganisationDto.prototype, "size", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.Length)(3, 50),
    __metadata("design:type", String)
], CreateStreamOrganisationDto.prototype, "vanityId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateStreamOrganisationDto.prototype, "isPublic", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateStreamOrganisationDto.prototype, "status", void 0);
//# sourceMappingURL=create-organisation.dto.js.map