"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateStreamOrganisationInput = void 0;
const graphql_1 = require("@nestjs/graphql");
const update_organisation_input_1 = require("./update-organisation.input");
const class_validator_1 = require("class-validator");
const organisations_args_1 = require("../args/organisations.args");
let CreateStreamOrganisationInput = class CreateStreamOrganisationInput extends update_organisation_input_1.UpdateStreamOrganisationInput {
};
exports.CreateStreamOrganisationInput = CreateStreamOrganisationInput;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CreateStreamOrganisationInput.prototype, "organisationId", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CreateStreamOrganisationInput.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    (0, graphql_1.Field)(() => organisations_args_1.StreamOrganisationType),
    __metadata("design:type", String)
], CreateStreamOrganisationInput.prototype, "type", void 0);
exports.CreateStreamOrganisationInput = CreateStreamOrganisationInput = __decorate([
    (0, graphql_1.InputType)()
], CreateStreamOrganisationInput);
//# sourceMappingURL=create-organisation.input.js.map