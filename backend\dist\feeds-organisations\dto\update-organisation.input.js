"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateStreamOrganisationInput = void 0;
const class_validator_1 = require("class-validator");
const graphql_1 = require("@nestjs/graphql");
const graphql_type_json_1 = require("graphql-type-json");
const organisations_args_1 = require("../args/organisations.args");
let UpdateStreamOrganisationInput = class UpdateStreamOrganisationInput {
};
exports.UpdateStreamOrganisationInput = UpdateStreamOrganisationInput;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.MaxLength)(255),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], UpdateStreamOrganisationInput.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.MaxLength)(255),
    (0, graphql_1.Field)(() => organisations_args_1.StreamOrganisationType, { nullable: true }),
    __metadata("design:type", String)
], UpdateStreamOrganisationInput.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.MaxLength)(255),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], UpdateStreamOrganisationInput.prototype, "image", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.MaxLength)(255),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], UpdateStreamOrganisationInput.prototype, "backgroundImage", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)(() => graphql_type_json_1.GraphQLJSON, { nullable: true }),
    __metadata("design:type", Object)
], UpdateStreamOrganisationInput.prototype, "location", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], UpdateStreamOrganisationInput.prototype, "privacy", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], UpdateStreamOrganisationInput.prototype, "peoplePrivacy", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], UpdateStreamOrganisationInput.prototype, "website", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], UpdateStreamOrganisationInput.prototype, "resourceUrl", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], UpdateStreamOrganisationInput.prototype, "resources", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], UpdateStreamOrganisationInput.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)(() => organisations_args_1.StreamOrganisationSize, { nullable: true }),
    __metadata("design:type", String)
], UpdateStreamOrganisationInput.prototype, "size", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.Length)(3, 50),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], UpdateStreamOrganisationInput.prototype, "vanityId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], UpdateStreamOrganisationInput.prototype, "isPublic", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)(() => organisations_args_1.StreamOrganisationPrivacySettings, { nullable: true }),
    __metadata("design:type", organisations_args_1.StreamOrganisationPrivacySettings)
], UpdateStreamOrganisationInput.prototype, "privacySettings", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)(() => organisations_args_1.StreamOrganisationStatus, { nullable: true }),
    __metadata("design:type", String)
], UpdateStreamOrganisationInput.prototype, "status", void 0);
exports.UpdateStreamOrganisationInput = UpdateStreamOrganisationInput = __decorate([
    (0, graphql_1.InputType)()
], UpdateStreamOrganisationInput);
//# sourceMappingURL=update-organisation.input.js.map