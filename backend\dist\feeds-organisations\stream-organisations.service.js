"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamOrganisationsService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const getstream_1 = require("getstream");
const config_1 = __importDefault(require("../config/config"));
const organisations_args_1 = require("./args/organisations.args");
let StreamOrganisationsService = class StreamOrganisationsService {
    onModuleInit() {
        if (config_1.default.SERVICE === 'api-beta') {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_BETA_KEY, config_1.default.STREAM_BETA_SECRET);
        }
        else {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_KEY, config_1.default.STREAM_SECRET);
        }
    }
    async onApplicationBootstrap() {
        console.log('onApplicationBootstrap');
    }
    async createOrganisation(profileId, createOrganisationDto) {
        this.logger.info('StreamOrganisationsService.createOrganisation', {
            profileId,
            createOrganisationDto,
        });
        if (!createOrganisationDto.status) {
            createOrganisationDto.status = organisations_args_1.StreamOrganisationStatus.Active;
        }
        try {
            await this.client.collections.add('organisations', createOrganisationDto.organisationId, createOrganisationDto);
            const user = await this.client.feed('user', profileId);
            await user.follow('organisations', createOrganisationDto.organisationId);
            await this.client
                .feed('org_feed_members', createOrganisationDto.organisationId)
                .follow('organisations', createOrganisationDto.organisationId);
            return true;
        }
        catch (e) {
            throw new Error(`StreamOrganisationsService.createOrganisation - ` + e.message);
        }
        return false;
    }
    async updateOrganisation(id, updateOrganisationInput, options) {
        if (this.client) {
            const org = await this.client.collections.get('organisations', id);
            if (org) {
                await this.client.collections.update('organisations', id, Object.assign(Object.assign({}, org.data), updateOrganisationInput));
            }
        }
        return true;
    }
};
exports.StreamOrganisationsService = StreamOrganisationsService;
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], StreamOrganisationsService.prototype, "logger", void 0);
exports.StreamOrganisationsService = StreamOrganisationsService = __decorate([
    (0, common_1.Injectable)()
], StreamOrganisationsService);
//# sourceMappingURL=stream-organisations.service.js.map