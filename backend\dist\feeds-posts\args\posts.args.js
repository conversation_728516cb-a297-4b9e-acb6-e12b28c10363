"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImageCategory = exports.PostStreamType = void 0;
const graphql_1 = require("@nestjs/graphql");
var PostStreamType;
(function (PostStreamType) {
    PostStreamType["Normal"] = "Normal";
    PostStreamType["Image"] = "Image";
    PostStreamType["Webinar"] = "Webinar";
    PostStreamType["Event"] = "Event";
    PostStreamType["Incentive"] = "Incentive";
    PostStreamType["Video"] = "Video";
    PostStreamType["Mixed"] = "Mixed";
    PostStreamType["Document"] = "Document";
})(PostStreamType || (exports.PostStreamType = PostStreamType = {}));
var ImageCategory;
(function (ImageCategory) {
    ImageCategory["Single"] = "Single";
    ImageCategory["Multiple"] = "Multiple";
})(ImageCategory || (exports.ImageCategory = ImageCategory = {}));
(0, graphql_1.registerEnumType)(PostStreamType, { name: 'PostStreamType' });
(0, graphql_1.registerEnumType)(ImageCategory, { name: 'ImageCategory' });
//# sourceMappingURL=posts.args.js.map