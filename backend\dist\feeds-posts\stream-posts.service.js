"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamPostsService = void 0;
const common_1 = require("@nestjs/common");
const getstream_1 = require("getstream");
const moment_1 = __importDefault(require("moment"));
const nest_winston_1 = require("nest-winston");
const sequelize_1 = require("sequelize");
const winston_1 = require("winston");
const activities_service_1 = require("../activities/activities.service");
const activities_args_1 = require("../activities/args/activities.args");
const create_user_activity_data_dto_1 = require("../activities/dto/create-user-activity-data.dto");
const config_1 = __importDefault(require("../config/config"));
const notificationLayoutHelper_1 = require("../email/helpers/notificationLayoutHelper");
const event_invitations_args_1 = require("../event-invitations/args/event-invitations.args");
const event_invitations_service_1 = require("../event-invitations/event-invitations.service");
const events_service_1 = require("../events/events.service");
const followers_service_1 = require("../followers/followers.service");
const follower_model_1 = require("../followers/models/follower.model");
const incentive_participants_args_1 = require("../incentive-participants/args/incentive-participants.args");
const incentive_participants_service_1 = require("../incentive-participants/incentive-participants.service");
const incentives_service_1 = require("../incentives/incentives.service");
const memberships_service_1 = require("../memberships/memberships.service");
const membership_model_1 = require("../memberships/models/membership.model");
const notifications_args_1 = require("../notifications/args/notifications.args");
const notifications_service_1 = require("../notifications/notifications.service");
const organisations_args_1 = require("../organisations/args/organisations.args");
const organisations_service_1 = require("../organisations/organisations.service");
const organisation_model_1 = require("../organisations/models/organisation.model");
const partnership_request_model_1 = require("../partnership-requests/models/partnership-request.model");
const partnerships_service_1 = require("../partnerships/partnerships.service");
const posts_args_1 = require("../posts/args/posts.args");
const posts_service_1 = require("../posts/posts.service");
const profile_model_1 = require("../profiles/models/profile.model");
const profiles_service_1 = require("../profiles/profiles.service");
const webinar_participants_args_1 = require("../webinar-participants/args/webinar-participants.args");
const webinar_participants_service_1 = require("../webinar-participants/webinar-participants.service");
const webinars_service_1 = require("../webinars/webinars.service");
const getting_started_steps_service_1 = require("../getting-started-steps/getting-started-steps.service");
const getting_started_steps_args_1 = require("../getting-started-steps/args/getting-started-steps.args");
const lodash_1 = require("lodash");
const organisation_loyalty_points_service_1 = require("../organisation-loyalty-points/organisation-loyalty-points.service");
let StreamPostsService = class StreamPostsService {
    onModuleInit() {
        if (config_1.default.SERVICE === 'api-beta') {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_BETA_KEY, config_1.default.STREAM_BETA_SECRET);
        }
        else {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_KEY, config_1.default.STREAM_SECRET);
        }
    }
    async onApplicationBootstrap() { }
    async followNewTargetedMembersFeedsMigration() {
        var _a;
        try {
            const organisations = await this.organisationsService.findAll({
                status: organisations_args_1.OrganisationStatus.Active,
            });
            let orgIndex = 0;
            for (const org of organisations) {
                setTimeout(async function () {
                    var _a;
                    try {
                        await this.client
                            .feed('org_feed_members', org.id)
                            .follow('organisations', org.id);
                        this.logger.info(`Stream.Service Info - followNewTargetedMembersFeedsMigration: orgFeeds`, {
                            organisationId: org.id,
                        });
                    }
                    catch (err) {
                        this.logger.error(`Stream.Service Error - followNewTargetedMembersFeedsMigration: ${err.message}`, (_a = err.response) === null || _a === void 0 ? void 0 : _a.body);
                    }
                }.bind(this), orgIndex * 1500);
                orgIndex++;
            }
            const memberships = await this.membershipsService.findAll({
                status: membership_model_1.MembershipStatus.Active,
                isPrimary: true,
                organisationId: {
                    [sequelize_1.Op.ne]: null,
                },
            });
            let index = 0;
            for (const membership of memberships) {
                setTimeout(async function () {
                    var _a;
                    try {
                        const follows = [
                            {
                                source: `user:${membership.profileId}`,
                                target: `org_feed_members:${membership.organisationId}`,
                            },
                            {
                                source: `user:${membership.profileId}`,
                                target: `org_feed_targeted:${membership.organisationId}`,
                            },
                        ];
                        await this.client.followMany(follows);
                        this.logger.info(`Stream.Service Info - followNewTargetedMembersFeedsMigration: userFeeds`, {
                            profileId: membership.profileId,
                            organisationId: membership.organisationId,
                        });
                    }
                    catch (err) {
                        this.logger.error(`Stream.Service Error - followNewTargetedMembersFeedsMigration: ${err.message}`, (_a = err.response) === null || _a === void 0 ? void 0 : _a.body);
                    }
                }.bind(this), index * 1500);
                index++;
            }
        }
        catch (e) {
            this.logger.error(`Stream.Service Error - followNewTargetedMembersFeedsMigration: ${e.message}`, (_a = e.response) === null || _a === void 0 ? void 0 : _a.body);
        }
    }
    async followNewIncentiveFeeds() {
        var _a, _b;
        try {
            const incentives = await this.incentivesService.findAll({
                endDate: { [sequelize_1.Op.gte]: Date.now() },
            });
            for (const incentive of incentives) {
                const registeredParticipants = await this.incentiveParticipantsService.findAll({
                    incentiveId: incentive.id,
                    status: incentive_participants_args_1.IncentiveParticipantStatus.Registered,
                });
                const incentiveParticipantFollows = [];
                for (const participant of registeredParticipants) {
                    incentiveParticipantFollows.push({
                        source: 'user:' + participant.profileId,
                        target: 'incentives:' + incentive.id,
                    });
                }
                try {
                    this.logger.info(`Stream.Service Info - followNewIncentiveFeeds: `, {
                        incentive: incentive.id,
                        registeredParticipants: registeredParticipants,
                        incentiveParticipantFollows: incentiveParticipantFollows,
                    });
                    incentiveParticipantFollows.length &&
                        (await this.client.followMany(incentiveParticipantFollows));
                }
                catch (err) {
                    this.logger.error(`Stream.Service Error - followNewIncentiveFeeds: ${err.message}`, (_a = err.response) === null || _a === void 0 ? void 0 : _a.body);
                }
            }
        }
        catch (err) {
            this.logger.error(`Stream.Service Error - followNewIncentiveFeeds: ${err.message}`, (_b = err.response) === null || _b === void 0 ? void 0 : _b.body);
        }
    }
    async followNewWebinarFeeds() {
        var _a, _b;
        try {
            const webinars = await this.webinarsService.findAll({
                startDate: { [sequelize_1.Op.gte]: Date.now() },
            });
            for (const webinar of webinars) {
                const activeParticipants = await this.webinarParticipantsService.findAll({
                    webinarId: webinar.id,
                    status: {
                        [sequelize_1.Op.in]: [
                            webinar_participants_args_1.WebinarParticipantStatus.Registered,
                            webinar_participants_args_1.WebinarParticipantStatus.InvitedRegistered,
                            webinar_participants_args_1.WebinarParticipantStatus.Host,
                            webinar_participants_args_1.WebinarParticipantStatus.Speaker,
                            webinar_participants_args_1.WebinarParticipantStatus.HiddenHost,
                            webinar_participants_args_1.WebinarParticipantStatus.HostAdmin,
                        ],
                    },
                });
                const webinarParticipantFollows = [];
                for (const participant of activeParticipants) {
                    webinarParticipantFollows.push({
                        source: 'user:' + participant.profileId,
                        target: 'webinars:' + webinar.id,
                    });
                }
                try {
                    this.logger.info(`Stream.Service Info - followNewWebinarFeeds: `, {
                        webinarId: webinar.id,
                        activeParticipants: activeParticipants,
                        webinarParticipantFollows: webinarParticipantFollows,
                    });
                    webinarParticipantFollows.length &&
                        (await this.client.followMany(webinarParticipantFollows));
                }
                catch (err) {
                    this.logger.error(`Stream.Service Error - followNewWebinarFeeds: ${err.message}`, (_a = err.response) === null || _a === void 0 ? void 0 : _a.body);
                }
            }
        }
        catch (err) {
            this.logger.error(`Stream.Service Error - followNewWebinarFeeds: ${err.message}`, (_b = err.response) === null || _b === void 0 ? void 0 : _b.body);
        }
    }
    async followNewEventFeeds() {
        var _a, _b;
        try {
            const events = await this.eventsService.findAll({
                endDate: { [sequelize_1.Op.gte]: Date.now() },
            });
            for (const event of events) {
                const attendees = await this.eventInvitationsService.findAll({
                    eventId: event.id,
                    status: {
                        [sequelize_1.Op.in]: [
                            event_invitations_args_1.EventInvitationStatus.Attending,
                            event_invitations_args_1.EventInvitationStatus.InvitedAttending,
                            event_invitations_args_1.EventInvitationStatus.InvitedInterested,
                            event_invitations_args_1.EventInvitationStatus.Interested,
                        ],
                    },
                });
                const eventAttendeeFollows = [];
                for (const attendee of attendees) {
                    eventAttendeeFollows.push({
                        source: 'user:' + attendee.profileId,
                        target: 'events:' + event.id,
                    });
                }
                try {
                    this.logger.info(`Stream.Service Info - followNewEventFeeds: `, {
                        eventId: event.id,
                        attendees: attendees,
                        eventAttendeeFollows: eventAttendeeFollows,
                    });
                    eventAttendeeFollows.length &&
                        (await this.client.followMany(eventAttendeeFollows));
                }
                catch (err) {
                    this.logger.error(`Stream.Service Error - followNewEventFeeds: ${err.message}`, (_a = err.response) === null || _a === void 0 ? void 0 : _a.body);
                }
            }
        }
        catch (err) {
            this.logger.error(`Stream.Service Error - followNewEventFeeds: ${err.message}`, (_b = err.response) === null || _b === void 0 ? void 0 : _b.body);
        }
    }
    async followPrimaryMembershipMissedMigration() {
        var _a, _b;
        try {
            const memberships = await this.membershipsService.findAll({
                status: membership_model_1.MembershipStatus.Active,
                isPrimary: true,
                organisationId: {
                    [sequelize_1.Op.ne]: null,
                },
            });
            let follows = [];
            for (const membership of memberships) {
                try {
                    this.logger.info(`Stream.Service Info - followPrimaryMembershipMissedMigration: found primary membership`, {
                        profileId: membership.profileId,
                        organisationId: membership.organisationId,
                    });
                    const followsOrg = await this.followersService.findOne({
                        profileId: membership.profileId,
                        organisationId: membership.organisationId,
                    });
                    this.logger.info(`Stream.Service Info - followPrimaryMembershipMissedMigration: checking follow`);
                    if (followsOrg) {
                        follows.push({
                            source: 'user:' + membership.profileId,
                            target: 'organisations:' + membership.organisationId,
                        });
                    }
                    follows.push({
                        source: 'user:' + membership.profileId,
                        target: 'org_feed_members:' + membership.organisationId,
                    });
                    follows.push({
                        source: 'user:' + membership.profileId,
                        target: 'org_feed_targeted:' + membership.organisationId,
                    });
                    if (follows.length > 100) {
                        const done = await this.client.followMany(follows);
                        if (!!done)
                            follows = [];
                    }
                }
                catch (err) {
                    this.logger.error(`Stream.Service Error - followPrimaryMembershipMissedMigration: ${err.message}`, (_a = err.response) === null || _a === void 0 ? void 0 : _a.body);
                }
            }
            if (follows.length)
                await this.client.followMany(follows);
        }
        catch (e) {
            this.logger.error(`Stream.Service Error - followNewTargetedMembersFeedsMigration: ${e.message}`, (_b = e.response) === null || _b === void 0 ? void 0 : _b.body);
        }
    }
    async followNonPrimaryMembershipMissedMigration() {
        var _a, _b;
        try {
            const memberships = await this.membershipsService.findAll({
                status: membership_model_1.MembershipStatus.Active,
                isPrimary: false,
                organisationId: {
                    [sequelize_1.Op.ne]: null,
                },
            });
            let follows = [];
            for (const membership of memberships) {
                try {
                    this.logger.info(`Stream.Service Info - followNonPrimaryMembershipMissedMigration: found a membership`, {
                        profileId: membership.profileId,
                        organisationId: membership.organisationId,
                    });
                    const followsOrg = await this.followersService.findOne({
                        profileId: membership.profileId,
                        organisationId: membership.organisationId,
                        status: follower_model_1.FollowerStatus.Active,
                    });
                    if (followsOrg)
                        this.logger.info(`Stream.Service Info - followNonPrimaryMembershipMissedMigration: found Follow`, {
                            profileId: membership.profileId,
                            organisationId: membership.organisationId,
                        });
                    else {
                        this.logger.info(`Stream.Service Info - followNonPrimaryMembershipMissedMigration: follow NOT found`, {
                            profileId: membership.profileId,
                            organisationId: membership.organisationId,
                        });
                    }
                    await new Promise(resolve => setTimeout(resolve, 120));
                    const userFeed = this.client.feed('user', membership.profileId);
                    let streamResponse = await userFeed.following({
                        offset: 0,
                        limit: 1,
                        filter: ['organisations:' + membership.organisationId],
                    });
                    if (followsOrg && !streamResponse.results.length) {
                        this.logger.warn(`Stream.Service warning - followNonPrimaryMembershipMissedMigration: MISSING FOLLOW on Stream, being added`, {
                            profileId: membership.profileId,
                            organisationId: membership.organisationId,
                        });
                        follows.push({
                            source: 'user:' + membership.profileId,
                            target: 'organisations:' + membership.organisationId,
                        });
                    }
                    if (follows.length > 100) {
                        this.logger.info('Stream.Service followNonPrimaryMembershipMissedMigration - Stream.followMany (100)');
                        const done = await this.client.followMany(follows);
                        if (!!done)
                            follows = [];
                    }
                }
                catch (err) {
                    this.logger.error(`Stream.Service Error - followNonPrimaryMembershipMissedMigration: ${err.message}`, (_a = err.response) === null || _a === void 0 ? void 0 : _a.body);
                }
            }
            if (follows.length) {
                this.logger.info('Stream.Service followNonPrimaryMembershipMissedMigration - Stream.followMany (final batch)');
                await this.client.followMany(follows);
                this.logger.info('Stream.Service followNonPrimaryMembershipMissedMigration - Stream.followMany done');
            }
        }
        catch (e) {
            this.logger.error(`Stream.Service Error - followNonPrimaryMembershipMissedMigration: ${e.message}`, (_b = e.response) === null || _b === void 0 ? void 0 : _b.body);
        }
    }
    async followConnectedOrgsMissedMigration() {
        var _a, _b;
        try {
            const partnerships = await this.partnershipsService.findAll({}, {
                includeParams: [
                    {
                        model: partnership_request_model_1.PartnershipRequest,
                        as: 'partnershipRequest',
                        where: {
                            status: partnership_request_model_1.PartnershipRequestStatus.Approved,
                        },
                    },
                ],
            });
            let follows = [];
            for (const partnership of partnerships) {
                try {
                    this.logger.info(`Stream.Service Info - followConnectedOrgsMissedMigration: found partnership`, {
                        partnershipId: partnership.id,
                        status: partnership.partnershipRequest.status,
                    });
                    const organisationMemberships = await this.membershipsService.findAll({
                        organisationId: partnership.organisationId,
                        status: membership_model_1.MembershipStatus.Active,
                    });
                    for (const organisationMembership of organisationMemberships) {
                        const existingFollower = await this.followersService.findOne({
                            profileId: organisationMembership.profileId,
                            organisationId: partnership.partnershipOrganisationId,
                            status: follower_model_1.FollowerStatus.Active,
                        });
                        if (existingFollower) {
                            this.logger.info(`Stream.Service Info - followConnectedOrgsMissedMigration: adding follow`, {
                                profileId: organisationMembership.profileId,
                                organisationId: partnership.partnershipOrganisationId,
                            });
                            const orgObj = {
                                source: 'user:' + organisationMembership.profileId,
                                target: 'organisations:' + partnership.partnershipOrganisationId,
                            };
                            const feedMemberObj = {
                                source: 'user:' + organisationMembership.profileId,
                                target: 'org_feed_members:' + partnership.partnershipOrganisationId,
                            };
                            const feedTargetedObj = {
                                source: 'user:' + organisationMembership.profileId,
                                target: 'org_feed_targeted:' + partnership.partnershipOrganisationId,
                            };
                            follows.push(orgObj, feedMemberObj, feedTargetedObj);
                        }
                    }
                    const partnerOrganisationMemberships = await this.membershipsService.findAll({
                        organisationId: partnership.partnershipOrganisationId,
                        status: membership_model_1.MembershipStatus.Active,
                    });
                    for (const partnerOrganisationMembership of partnerOrganisationMemberships) {
                        const existingFollower = await this.followersService.findOne({
                            profileId: partnerOrganisationMembership.profileId,
                            organisationId: partnership.organisationId,
                            status: follower_model_1.FollowerStatus.Active,
                        });
                        if (existingFollower) {
                            this.logger.info(`Stream.Service Info - followConnectedOrgsMissedMigration: adding follow`, {
                                profileId: partnerOrganisationMembership.profileId,
                                organisationId: partnership.organisationId,
                            });
                            const orgObj = {
                                source: 'user:' + partnerOrganisationMembership.profileId,
                                target: 'organisations:' + partnership.organisationId,
                            };
                            const feedMemberObj = {
                                source: 'user:' + partnerOrganisationMembership.profileId,
                                target: 'org_feed_members:' + partnership.organisationId,
                            };
                            const feedTargetedObj = {
                                source: 'user:' + partnerOrganisationMembership.profileId,
                                target: 'org_feed_targeted:' + partnership.organisationId,
                            };
                            follows.push(orgObj, feedMemberObj, feedTargetedObj);
                        }
                    }
                    if (follows.length > 100) {
                        const done = await this.client.followMany(follows);
                        if (!!done)
                            follows = [];
                    }
                }
                catch (err) {
                    this.logger.error(`Stream.Service Error - followConnectedOrgsMissedMigration: ${err.message}`, (_a = err.response) === null || _a === void 0 ? void 0 : _a.body);
                }
            }
            if (follows.length)
                await this.client.followMany(follows);
        }
        catch (e) {
            this.logger.error(`Stream.Service Error - followConnectedOrgsMissedMigration: ${e.message}`, (_b = e.response) === null || _b === void 0 ? void 0 : _b.body);
        }
    }
    async followDailyQuizFeedMigration() {
        var _a, _b;
        try {
            const incentiveParticipants = await this.incentiveParticipantsService.findAll({
                incentiveId: 'pbmfgzp6r7i1WzNMSjqoLm',
                status: incentive_participants_args_1.IncentiveParticipantStatus.Registered,
            });
            let follows = [];
            for (const participant of incentiveParticipants) {
                try {
                    this.logger.info(`Stream.Service Info - followDailyQuizFeedMigration:`, {
                        profileId: participant.profileId,
                    });
                    this.logger.info(`Stream.Service Info - followDailyQuizFeedMigration: adding follow`);
                    follows.push({
                        source: 'user:' + participant.profileId,
                        target: 'incentives:' + participant.incentiveId,
                    });
                    if (follows.length > 100) {
                        const done = await this.client.followMany(follows);
                        if (!!done)
                            follows = [];
                    }
                }
                catch (err) {
                    this.logger.error(`Stream.Service Error - followDailyQuizFeedMigration: ${err.message}`, (_a = err.response) === null || _a === void 0 ? void 0 : _a.body);
                }
            }
            if (follows.length)
                await this.client.followMany(follows);
        }
        catch (e) {
            this.logger.error(`Stream.Service Error - followDailyQuizFeedMigration: ${e.message}`, (_b = e.response) === null || _b === void 0 ? void 0 : _b.body);
        }
    }
    async createPost(profileId, event) {
        this.logger.info('StreamPostsService.addPost', {
            event,
            profileId: profileId,
        });
        const organisationId = event['organisationId'];
        const postId = event['postId'];
        const users = event['users'];
        const organisations = event['organisations'];
        if (!postId)
            return {
                success: false,
                activityId: '',
            };
        const membership = await this.membershipsService.findMembership(organisationId, profileId);
        const post = await this.client.collections.add('posts', postId, Object.assign({
            profileId: profileId,
            permission: membership ? membership.permissions : [],
            users: users,
            organisations: organisations,
            createdAt: new Date(),
            scheduledAt: event['scheduledAt'],
            id: postId,
        }, event));
        const postInDb = await this.postsService.findById(postId);
        let activityId = '';
        if (postInDb.status === posts_args_1.PostStatus.Live) {
            const publishPostDetails = await this.publishPost(profileId, post, membership);
            activityId = publishPostDetails.activityId;
            await this.organisationLoyaltyPointsService.update({
                where: {
                    type: activities_args_1.ActivityType.CreatePost,
                    organisationId: organisationId,
                    [sequelize_1.Op.and]: [
                        sequelize_1.Sequelize.literal(`placeholders::text ILIKE '%"postId": "${postId}"%'`),
                    ],
                },
                update: {
                    placeholders: sequelize_1.Sequelize.literal(`jsonb_set(placeholders, '{streamActivityId}', '"${activityId}"', true)`),
                },
            });
        }
        return {
            success: true,
            activityId,
        };
    }
    async publishPost(profileId, post, membership) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
        const organisationId = post.data.organisationId;
        const parentOrgId = post.data.parentOrgId;
        const postId = post.data.postId;
        const users = post.data.users;
        const organisations = post.data.organisations;
        this.logger.info('StreamPostsService.publishPost', {
            organisationId,
            parentOrgId,
            postId,
            profileId,
        });
        const profile = await this.profilesService.findById(profileId);
        const organisationRef = this.client.collections.entry('organisations', organisationId);
        let activity;
        let isPartnerPost = false;
        if (parentOrgId) {
            isPartnerPost = true;
            const currentMonth = (0, moment_1.default)().startOf('month').toDate();
            const nextMonth = (0, moment_1.default)().endOf('month').toDate();
            const partnerOrgResults = await this.sequelize.query(`SELECT * FROM "PartnerOrganisations" WHERE "childOrgId" = ? AND "parentOrgId" = ? AND "status" = 'approved'`, {
                replacements: [organisationId, parentOrgId],
                type: 'SELECT',
                raw: true,
            });
            const partnerOrgArray = partnerOrgResults;
            if (!partnerOrgArray || partnerOrgArray.length === 0) {
                this.logger.error(`Stream.Service Error - publishPost: No valid partnership found between parent ${parentOrgId} and child ${organisationId}`);
                return { success: false, activityId: '' };
            }
            const partnerOrgData = partnerOrgArray[0];
            const postsLimit = partnerOrgData.postsLimit || 2;
            const countResults = await this.sequelize.query(`SELECT COUNT(*) as count FROM "Posts" WHERE "organisationId" = ? AND "parentOrgId" = ? AND "createdAt" BETWEEN ? AND ?`, {
                replacements: [organisationId, parentOrgId, currentMonth, nextMonth],
                type: 'SELECT',
                raw: true,
            });
            const countData = countResults[0];
            const count = parseInt(countData.count);
            if (count >= postsLimit) {
                this.logger.error(`Stream.Service Error - publishPost: Monthly limit of ${postsLimit} partner posts reached for ${organisationId} to ${parentOrgId}`);
                return { success: false, activityId: '' };
            }
            const parentOrgRef = this.client.collections.entry('organisations', parentOrgId);
            const parentOrganisation = await this.organisationsService.findById(parentOrgId);
            const feedParentOrg = this.client.feed('organisations', organisationId);
            const params = {
                actor: 'SO:' + profileId,
                verb: 'organisations',
                object: parentOrgRef,
                post: post,
                foreign_id: `posts:${postId}`,
                time: new Date().toISOString(),
                user: {
                    name: profile.name,
                    image: profile.image,
                    id: profile.id,
                },
                permission: membership ? membership.permissions : [],
                isPartnerPost: true,
                childOrganisation: organisationRef,
                origin: `organisations:${organisationId}`,
            };
            activity = await feedParentOrg.addActivity(params);
            this.logger.info('StreamPostsService.publishPartnerPost', {
                childOrgId: organisationId,
                parentOrgId: parentOrgId,
                postId,
                activityId: activity.id,
            });
        }
        else if (post.data.postAudience.isMyOrganisation) {
            const orgMembersFeed = this.client.feed('org_feed_members', organisationId);
            const params = {
                actor: 'SO:' + profileId,
                verb: 'org_feed_members',
                object: organisationRef,
                post: post,
                foreign_id: `posts:${postId}`,
                time: new Date().toISOString(),
                user: {
                    name: profile.name,
                    image: profile.image,
                    id: profile.id,
                },
                permission: membership ? membership.permissions : [],
            };
            activity = await orgMembersFeed.addActivity(params);
        }
        else if ((_a = post.data.postAudience.connectedOrganisations) === null || _a === void 0 ? void 0 : _a.length) {
            const listOfConnectedOrgs = post.data.postAudience.connectedOrganisations.map(orgId => `org_feed_targeted:${orgId}`);
            const userFeed = this.client.feed('user', profileId);
            const params = {
                actor: 'SO:' + profileId,
                verb: 'org_feed_targeted',
                object: organisationRef,
                post: post,
                to: listOfConnectedOrgs,
                foreign_id: `posts:${postId}`,
                time: new Date().toISOString(),
                user: {
                    name: profile.name,
                    image: profile.image,
                    id: profile.id,
                },
                permission: membership ? membership.permissions : [],
            };
            try {
                activity = await userFeed.addActivity(params);
            }
            catch (e) {
                this.logger.error(`Stream.Service Error - addPost: ${e.message}`, (_b = e.response) === null || _b === void 0 ? void 0 : _b.body);
            }
        }
        else {
            const feedOrganisation = this.client.feed('organisations', organisationId);
            const params = {
                actor: 'SO:' + profileId,
                verb: 'organisations',
                object: organisationRef,
                post: post,
                foreign_id: `posts:${postId}`,
                time: new Date().toISOString(),
                user: {
                    name: profile.name,
                    image: profile.image,
                    id: profile.id,
                },
                permission: membership ? membership.permissions : [],
            };
            activity = await feedOrganisation.addActivity(params);
        }
        if (users.length > 0) {
            for (const mentionUser of users) {
                if (!((_d = (_c = post.data) === null || _c === void 0 ? void 0 : _c.postAudience) === null || _d === void 0 ? void 0 : _d.isMyOrganisation) &&
                    !((_g = (_f = (_e = post.data) === null || _e === void 0 ? void 0 : _e.postAudience) === null || _f === void 0 ? void 0 : _f.connectedOrganisations) === null || _g === void 0 ? void 0 : _g.length) &&
                    profile.id !== mentionUser.id) {
                    await this.notificationsService.createNotification({
                        ownerProfileId: mentionUser.id,
                        profileId: profile.id,
                        organisationId: organisationId,
                        type: notifications_args_1.NotificationType.PostMention,
                        data: {
                            postId,
                            activityId: activity.id,
                        },
                    });
                }
            }
            const profileIds = users.map(user => user.id);
            const replacementOrg = await this.organisationsService.findById(organisationId);
            const replacements = [replacementOrg.name];
            await this.notificationsService.sendPushNotification({
                profileIds,
                replacements,
                messageType: notifications_args_1.NotificationMessage.PostMention,
                route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.organisationPost, {
                    vanityId: organisationId,
                    id: activity.id,
                    userId: organisationId,
                }),
            });
        }
        if (organisations && organisations.length > 0) {
            for (const mentionOrg of organisations) {
                const orgMemberships = await this.membershipsService.findAll({
                    organisationId: mentionOrg.id,
                    permissions: {
                        [sequelize_1.Op.overlap]: [
                            membership_model_1.MembershipPermission.Owner,
                            membership_model_1.MembershipPermission.Admin,
                            membership_model_1.MembershipPermission.HiddenAdmin,
                            membership_model_1.MembershipPermission.Manager,
                            membership_model_1.MembershipPermission.Editor,
                        ],
                    },
                    status: membership_model_1.MembershipStatus.Active,
                }, {
                    includeParams: [
                        {
                            model: profile_model_1.Profile,
                            as: 'profile',
                            attributes: ['id', 'name', 'image', 'lastOnlineAt'],
                        },
                    ],
                });
                for (const orgMembership of orgMemberships) {
                    const mentionOrgUser = orgMembership.profile;
                    !((_j = (_h = post.data) === null || _h === void 0 ? void 0 : _h.postAudience) === null || _j === void 0 ? void 0 : _j.isMyOrganisation) &&
                        !((_m = (_l = (_k = post.data) === null || _k === void 0 ? void 0 : _k.postAudience) === null || _l === void 0 ? void 0 : _l.connectedOrganisations) === null || _m === void 0 ? void 0 : _m.length) &&
                        (await this.notificationsService.createNotification({
                            ownerProfileId: mentionOrgUser.id,
                            profileId: profile.id,
                            organisationId: organisationId,
                            type: notifications_args_1.NotificationType.OrgPostMention,
                            data: {
                                postId,
                                organisationId: mentionOrg.id,
                                activityId: activity.id,
                            },
                        }));
                }
                const profileIds = orgMemberships.map(user => user.profile.id);
                const replacementOrg = await this.organisationsService.findById(organisationId);
                const mentionedOrg = await this.organisationsService.findById(mentionOrg.id);
                const replacements = [replacementOrg.name, mentionedOrg.name];
                await this.notificationsService.sendPushNotification({
                    profileIds,
                    replacements,
                    messageType: notifications_args_1.NotificationMessage.OrgPostMention,
                    route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.organisationPost, {
                        vanityId: replacementOrg === null || replacementOrg === void 0 ? void 0 : replacementOrg.vanityId,
                        id: activity.id,
                        userId: organisationId,
                    }),
                });
            }
        }
        return {
            success: true,
            activityId: activity.id,
        };
    }
    async addPost(profileId, event) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
        this.logger.info('StreamPostsService.addPost', {
            event,
            profileId: profileId,
        });
        const organisationId = event['organisationId'];
        const postId = event['postId'];
        const users = event['users'];
        const mentionedOrganisations = event['organisations'];
        if (!postId)
            return {
                success: false,
                activityId: '',
            };
        const membership = await this.membershipsService.findMembership(organisationId, profileId);
        const profile = await this.profilesService.findById(profileId);
        const post = await this.client.collections.add('posts', postId, Object.assign({
            profileId: profileId,
            permission: membership ? membership.permissions : [],
            users: users,
            organisations: mentionedOrganisations,
            createdAt: new Date(),
            id: postId,
        }, event));
        const organisationRef = this.client.collections.entry('organisations', organisationId);
        let activity;
        if (post.data.postAudience.isMyOrganisation) {
            const orgMembersFeed = this.client.feed('org_feed_members', organisationId);
            const params = {
                actor: 'SO:' + profileId,
                verb: 'org_feed_members',
                object: organisationRef,
                post: post,
                foreign_id: `posts:${postId}`,
                time: new Date().toISOString(),
                user: {
                    name: profile.name,
                    image: profile.image,
                    id: profile.id,
                },
                permission: membership ? membership.permissions : [],
            };
            activity = await orgMembersFeed.addActivity(params);
        }
        else if ((_a = post.data.postAudience.connectedOrganisations) === null || _a === void 0 ? void 0 : _a.length) {
            const listOfConnectedOrgs = post.data.postAudience.connectedOrganisations.map(orgId => `org_feed_targeted:${orgId}`);
            const userFeed = this.client.feed('user', profileId);
            const params = {
                actor: 'SO:' + profileId,
                verb: 'org_feed_targeted',
                object: organisationRef,
                post: post,
                to: listOfConnectedOrgs,
                foreign_id: `posts:${postId}`,
                time: new Date().toISOString(),
                user: {
                    name: profile.name,
                    image: profile.image,
                    id: profile.id,
                },
                permission: membership ? membership.permissions : [],
            };
            try {
                activity = await userFeed.addActivity(params);
            }
            catch (e) {
                this.logger.error(`Stream.Service Error - addPost: ${e.message}`, (_b = e.response) === null || _b === void 0 ? void 0 : _b.body);
            }
        }
        else {
            const feedOrganisation = this.client.feed('organisations', organisationId);
            const params = {
                actor: 'SO:' + profileId,
                verb: 'organisations',
                object: organisationRef,
                post: post,
                foreign_id: `posts:${postId}`,
                time: new Date().toISOString(),
                user: {
                    name: profile.name,
                    image: profile.image,
                    id: profile.id,
                },
                permission: membership ? membership.permissions : [],
            };
            activity = await feedOrganisation.addActivity(params);
        }
        if (users.length > 0) {
            for (const mentionUser of users) {
                if (!((_d = (_c = post.data) === null || _c === void 0 ? void 0 : _c.postAudience) === null || _d === void 0 ? void 0 : _d.isMyOrganisation) &&
                    !((_g = (_f = (_e = post.data) === null || _e === void 0 ? void 0 : _e.postAudience) === null || _f === void 0 ? void 0 : _f.connectedOrganisations) === null || _g === void 0 ? void 0 : _g.length) &&
                    profile.id !== mentionUser.id) {
                    await this.notificationsService.createNotification({
                        ownerProfileId: mentionUser.id,
                        profileId: profile.id,
                        organisationId: organisationId,
                        type: notifications_args_1.NotificationType.PostMention,
                        data: {
                            postId,
                            activityId: activity.id,
                        },
                    });
                }
            }
            const profileIds = users.map(user => user.id);
            const replacementOrg = await this.organisationsService.findById(organisationId);
            const replacements = [replacementOrg.name];
            await this.notificationsService.sendPushNotification({
                profileIds,
                replacements,
                messageType: notifications_args_1.NotificationMessage.PostMention,
                route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.organisationPost, {
                    vanityId: organisationId,
                    id: activity.id,
                    userId: organisationId,
                }),
            });
        }
        if (mentionedOrganisations && mentionedOrganisations.length > 0) {
            for (const mentionOrg of mentionedOrganisations) {
                const orgMemberships = await this.membershipsService.findAll({
                    organisationId: mentionOrg.id,
                    permissions: {
                        [sequelize_1.Op.overlap]: [
                            membership_model_1.MembershipPermission.Owner,
                            membership_model_1.MembershipPermission.Admin,
                            membership_model_1.MembershipPermission.HiddenAdmin,
                            membership_model_1.MembershipPermission.Manager,
                            membership_model_1.MembershipPermission.Editor,
                        ],
                    },
                    status: membership_model_1.MembershipStatus.Active,
                }, {
                    includeParams: [
                        {
                            model: profile_model_1.Profile,
                            as: 'profile',
                            attributes: ['id', 'name', 'image', 'lastOnlineAt'],
                        },
                    ],
                });
                for (let orgMembership of orgMemberships) {
                    const mentionOrgUser = orgMembership.profile;
                    !((_j = (_h = post.data) === null || _h === void 0 ? void 0 : _h.postAudience) === null || _j === void 0 ? void 0 : _j.isMyOrganisation) &&
                        !((_m = (_l = (_k = post.data) === null || _k === void 0 ? void 0 : _k.postAudience) === null || _l === void 0 ? void 0 : _l.connectedOrganisations) === null || _m === void 0 ? void 0 : _m.length) &&
                        (await this.notificationsService.createNotification({
                            ownerProfileId: mentionOrgUser.id,
                            profileId: profile.id,
                            organisationId: organisationId,
                            type: notifications_args_1.NotificationType.OrgPostMention,
                            data: {
                                postId,
                                organisationId: mentionOrg.id,
                                activityId: activity.id,
                            },
                        }));
                }
                const profileIds = orgMemberships.map(user => user.profile.id);
                const postingOrg = await this.organisationsService.findById(organisationId);
                const replacements = [postingOrg.name, mentionOrg.name];
                await this.notificationsService.sendPushNotification({
                    profileIds,
                    replacements,
                    messageType: notifications_args_1.NotificationMessage.OrgPostMention,
                    route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.organisationPost, {
                        vanityId: postingOrg === null || postingOrg === void 0 ? void 0 : postingOrg.vanityId,
                        id: activity.id,
                        userId: organisationId,
                    }),
                });
            }
        }
        return {
            success: true,
            activityId: activity.id,
        };
    }
    async removePost(postId, organisationId, profileId, isRepost, activityId) {
        var _a, _b, _c, _d, _e;
        this.logger.info('StreamPostsService.removePost', {
            postId,
            activityId,
            organisationId,
            profileId,
        });
        const membership = await this.membershipsService.findMembership(organisationId, profileId);
        let currPost;
        if (membership && membership.permissions) {
            try {
                currPost = await this.client.collections.get('posts', postId);
                await this.client.collections.delete('posts', postId);
            }
            catch (e) {
                this.logger.error(`Stream.Service Error - removePost: ${e.message}`, (_a = e.response) === null || _a === void 0 ? void 0 : _a.body);
            }
            let feed;
            if (currPost && currPost.data && currPost.data.postAudience && currPost.data.postAudience.isMyOrganisation === true) {
                feed = this.client.feed('org_feed_members', organisationId);
            }
            else if (currPost && currPost.data && currPost.data.postAudience &&
                ((_b = currPost.data.postAudience.connectedOrganisations) === null || _b === void 0 ? void 0 : _b.length)) {
                const authorUserId = (_c = currPost.data) === null || _c === void 0 ? void 0 : _c.profileId;
                feed = this.client.feed('user', authorUserId);
            }
            else {
                feed = this.client.feed('organisations', organisationId);
            }
            if (activityId) {
                try {
                    await feed.removeActivity(activityId);
                    this.logger.info('StreamPostsService.removePost success', {
                        postId,
                        activityId,
                        organisationId,
                        profileId,
                    });
                    await this.postsService.removePost(postId, {
                        profileId,
                    });
                    return true;
                }
                catch (e) {
                    this.logger.error(`Stream.Service Error - removePost: ${e.message}`, (_d = e.response) === null || _d === void 0 ? void 0 : _d.body);
                }
            }
            try {
                await feed.removeActivity({
                    foreignId: 'posts:' + postId,
                });
                this.logger.info('StreamPostsService.removePost success', {
                    postId,
                    activityId,
                    organisationId,
                    profileId,
                });
                await this.postsService.removePost(postId, {
                    profileId,
                });
                return true;
            }
            catch (e) {
                this.logger.error(`Stream.Service Error - removePost: ${e.message}`, (_e = e.response) === null || _e === void 0 ? void 0 : _e.body);
                return false;
            }
        }
        else {
            this.logger.error('StreamPostsService.removePost user unauthorised', {
                postId,
                activityId,
                organisationId,
                profileId,
            });
            return false;
        }
    }
    async updatePost(postId, postData, profileId) {
        this.logger.info('StreamPostsService.updatePost', {
            postId,
            postData,
            profileId: profileId,
        });
        try {
            const currPost = await this.client.collections.get('posts', postId);
            const currPostData = currPost.data || {};
            const updPostData = {};
            const newPostData = Object.assign(Object.assign({}, currPostData), updPostData);
            postData.image
                ? (newPostData.image = postData.image)
                : postData.image === ''
                    ? newPostData === null || newPostData === void 0 ? true : delete newPostData.image
                    : '';
            postData.imageHeight
                ? (newPostData.imageHeight = postData.imageHeight)
                : postData.imageHeight === 0
                    ? newPostData === null || newPostData === void 0 ? true : delete newPostData.imageHeight
                    : '';
            postData.imageWidth
                ? (newPostData.imageWidth = postData.imageWidth)
                : postData.imageWidth === 0
                    ? newPostData === null || newPostData === void 0 ? true : delete newPostData.imageWidth
                    : '';
            postData.imageFormat
                ? (newPostData.imageFormat = postData.imageFormat)
                : postData.imageFormat === ''
                    ? newPostData === null || newPostData === void 0 ? true : delete newPostData.imageFormat
                    : '';
            postData.multipleImages
                ? (newPostData.multipleImages =
                    postData.multipleImages)
                : postData.multipleImages === '' ||
                    !postData.hasOwnProperty('multipleImages')
                    ? newPostData === null || newPostData === void 0 ? true : delete newPostData.multipleImages
                    : '';
            postData.imageCategory
                ? (newPostData.imageCategory =
                    postData.imageCategory)
                : postData.imageCategory === ''
                    ? newPostData === null || newPostData === void 0 ? true : delete newPostData.imageCategory
                    : '';
            postData.video
                ? (newPostData.video = postData.video)
                : postData.video === ''
                    ? delete newPostData.video
                    : '';
            postData.videoHeight
                ? (newPostData.videoHeight = postData.videoHeight)
                : postData.videoHeight === 0
                    ? newPostData === null || newPostData === void 0 ? true : delete newPostData.videoHeight
                    : '';
            postData.videoWidth
                ? (newPostData.videoWidth = postData.videoWidth)
                : postData.videoWidth === 0
                    ? newPostData === null || newPostData === void 0 ? true : delete newPostData.videoWidth
                    : '';
            postData.videoFormat
                ? (newPostData.videoFormat = postData.videoFormat)
                : postData.videoFormat === ''
                    ? newPostData === null || newPostData === void 0 ? true : delete newPostData.videoFormat
                    : '';
            postData.document
                ? (newPostData.document = postData.document)
                : postData.document === ''
                    ? delete newPostData.document
                    : '';
            postData.documentUrl
                ? (newPostData.documentUrl = postData.documentUrl)
                : postData.documentUrl === ''
                    ? delete newPostData.documentUrl
                    : '';
            postData.documentSize
                ? (newPostData.documentSize = postData.documentSize)
                : postData.documentSize === ''
                    ? delete newPostData.documentSize
                    : '';
            postData.documentFormat
                ? (newPostData.documentFormat = postData.documentFormat)
                : postData.documentFormat === ''
                    ? delete newPostData.documentFormat
                    : '';
            postData.documentName
                ? (newPostData.documentName = postData.documentName)
                : postData.documentName === ''
                    ? delete newPostData.documentName
                    : '';
            newPostData.text = postData.text;
            newPostData.type = postData.type;
            const streamPostData = Object.assign({}, newPostData);
            if (postData.users) {
                streamPostData.users = postData.users;
            }
            if (postData.organisations) {
                streamPostData.organisations = postData.organisations;
            }
            await this.client.collections.update('posts', postId, streamPostData);
            const postDataDb = Object.assign({}, newPostData);
            if (newPostData.image) {
                postDataDb['mediaHeight'] = newPostData.imageHeight;
                postDataDb['mediaWidth'] = newPostData.imageWidth;
                postDataDb['mediaFormat'] = newPostData.imageFormat;
                delete postDataDb.imageHeight;
                delete postDataDb.imageWidth;
                delete postDataDb.imageFormat;
            }
            if (newPostData.video) {
                postDataDb['mediaHeight'] = newPostData.videoHeight;
                postDataDb['mediaWidth'] = newPostData.videoWidth;
                postDataDb['mediaFormat'] = newPostData.videoFormat;
                delete postDataDb.videoHeight;
                delete postDataDb.videoWidth;
                delete postDataDb.videoFormat;
            }
            const res = await this.postsService.updatePost(postId, postDataDb, {
                profileId,
            });
            return true;
        }
        catch (err) {
            return false;
        }
    }
    async updateStreamPostScheduleTime(postId, profileId, scheduledAt) {
        this.logger.info('StreamPostsService.updatePostScheduleTime', {
            postId,
            profileId: profileId,
            scheduledAt,
        });
        try {
            const currPost = await this.client.collections.get('posts', postId);
            const currPostData = currPost.data || {};
            const updPostData = {
                scheduledAt: scheduledAt,
            };
            const newPostData = Object.assign(Object.assign({}, currPostData), updPostData);
            await this.client.collections.update('posts', postId, newPostData);
            return true;
        }
        catch (err) {
            return false;
        }
    }
    async addPostLike(profileId, activityId, feedId, postId) {
        var _a, _b, _c, _d, _e, _f, _g;
        this.logger.info('StreamPostsService.addPostLike', {
            profileId,
            activityId,
            feedId,
            postId,
        });
        try {
            const currPost = postId && (await this.client.collections.get('posts', postId));
            const feed = ((_b = (_a = currPost === null || currPost === void 0 ? void 0 : currPost.data) === null || _a === void 0 ? void 0 : _a.postAudience) === null || _b === void 0 ? void 0 : _b.isMyOrganisation)
                ? this.client.feed('org_feed_members', feedId)
                : this.client.feed('organisations', feedId);
            const activityData = await feed.get({ limit: 1, id_gte: activityId });
            const activity = activityData.results[0];
            this.client.activityPartialUpdate({
                id: activityId,
                set: {
                    likes: Object.assign(Object.assign({}, (activity.likes || {})), { [profileId]: true }),
                    likesCount: (activity.likesCount || 0) + 1,
                },
            });
            const organisationId = activity && activity.object.split('SO:organisations:')[1];
            postId =
                !postId &&
                    activity != null &&
                    activity.post.split('SO:posts:').length > 0
                    ? activity.post.split('SO:posts:')[1]
                    : postId;
            const repeatActivity = await this.activitiesService.findOne({
                type: activities_args_1.ActivityType.PostInteractions,
                profileId,
                postId,
                data: {
                    [sequelize_1.Op.contains]: {
                        type: 'PostLike',
                    },
                },
            });
            const organisation = await this.organisationsService.findById(organisationId);
            await this.activitiesService.createUserActivity({
                type: activities_args_1.ActivityType.PostInteractions,
                schema: create_user_activity_data_dto_1.postLikeActivityDataDto,
                data: {
                    likedById: profileId,
                    type: 'PostLike',
                },
                profileId,
                addLoyaltyPoints: !repeatActivity,
                postId,
                organisationId,
                placeholders: {
                    postActionType: 'PostLike',
                    userId: profileId,
                    vanityId: organisation.vanityId,
                    streamActivityId: activityId,
                    organisationId,
                    organisationName: organisation.name,
                },
            });
            if (currPost.data.profileId !== profileId) {
                const profile = await this.profilesService.findById(profileId);
                const profileObj = profile.get({ plain: true });
                const usersArr = [
                    {
                        profileId: profileObj.id,
                        id: profileObj.id,
                        name: profileObj.name,
                        image: profileObj.image,
                    },
                ];
                if (config_1.default.NODE_ENV === 'development' ||
                    config_1.default.SERVICE === 'api-beta') {
                    !((_d = (_c = currPost.data) === null || _c === void 0 ? void 0 : _c.postAudience) === null || _d === void 0 ? void 0 : _d.isMyOrganisation) &&
                        !((_g = (_f = (_e = currPost.data) === null || _e === void 0 ? void 0 : _e.postAudience) === null || _f === void 0 ? void 0 : _f.connectedOrganisations) === null || _g === void 0 ? void 0 : _g.length) &&
                        (await this.notificationsService.createNotificationWithGrouping({
                            ownerProfileId: currPost.data.profileId,
                            profileId: currPost.data.profileId,
                            type: notifications_args_1.NotificationType.PostReact,
                            organisationId: organisationId,
                            data: {
                                users: usersArr,
                                postId,
                                activityId: activityId,
                            },
                        }));
                    const usersFullNames = usersArr.map(({ name }) => name);
                    const countOfUsers = usersArr.length;
                    const objectOfStrings = (0, notificationLayoutHelper_1.getStringsForNotifications)(countOfUsers, usersFullNames);
                    const profileIds = [currPost.data.profileId];
                    const replacements = [
                        objectOfStrings.usersFullNamesString,
                        objectOfStrings.other,
                        objectOfStrings.has,
                    ];
                    await this.notificationsService.sendPushNotification({
                        profileIds,
                        replacements,
                        messageType: notifications_args_1.NotificationMessage.PostReact,
                        route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.organisationPost, {
                            vanityId: organisationId,
                            id: activityId,
                            userId: organisationId,
                        }),
                    });
                }
            }
            return true;
        }
        catch (e) {
            return false;
        }
    }
    async removePostLikeLegacy(reactionId) {
        try {
            await this.client.reactions.delete(reactionId);
            return true;
        }
        catch (e) {
            return false;
        }
    }
    async checkNewPost(profileId, activityId) {
        try {
            const feedUser = this.client.feed('user', profileId);
            const getLastActivity = await feedUser.get({ limit: 1 });
            if (!getLastActivity)
                return false;
            const activity = getLastActivity.results[0];
            if (activity.id !== activityId) {
                return true;
            }
            return false;
        }
        catch (e) {
            return false;
        }
    }
    async removePostLike(profileId, activityId, feedId, reactionId, postId) {
        var _a, _b;
        try {
            const currPost = postId && (await this.client.collections.get('posts', postId));
            const feed = ((_b = (_a = currPost === null || currPost === void 0 ? void 0 : currPost.data) === null || _a === void 0 ? void 0 : _a.postAudience) === null || _b === void 0 ? void 0 : _b.isMyOrganisation)
                ? this.client.feed('org_feed_members', feedId)
                : this.client.feed('organisations', feedId);
            const activityData = await feed.get({ limit: 1, id_gte: activityId });
            const activity = activityData.results[0];
            if (reactionId) {
                await this.removePostLikeLegacy(reactionId);
            }
            const newLikes = Object.assign({}, (activity.likes || {}));
            if (newLikes[profileId])
                delete newLikes[profileId];
            const newLikesCount = (activity.likesCount || 0) - 1 >= 0
                ? (activity.likesCount || 0) - 1
                : 0;
            this.client.activityPartialUpdate({
                id: activityId,
                set: {
                    likes: newLikes,
                    likesCount: newLikesCount,
                },
            });
            return true;
        }
        catch (e) {
            return false;
        }
    }
    async addCommentLike(profileId, activityId, commentId, postAuthorId, organisationId, postId) {
        var _a, _b, _c, _d, _e;
        try {
            const comment = await this.client.reactions.get(commentId);
            if (!comment)
                return false;
            const updData = Object.assign({}, comment.data);
            if (!updData.likes) {
                updData.likes = {};
            }
            updData.likes[profileId] = true;
            updData.likesCount = (updData.likesCount || 0) + 1;
            await this.client.reactions.update(commentId, updData);
            const [profile, post] = await Promise.all([
                this.profilesService.findById(profileId, {
                    includeParams: [
                        {
                            model: membership_model_1.Membership,
                            as: 'memberships',
                            where: {
                                isPrimary: true,
                            },
                            include: [
                                {
                                    model: organisation_model_1.Organisation,
                                    as: 'organisation',
                                    attributes: ['id', 'name'],
                                },
                            ],
                        },
                    ],
                }),
                this.client.collections.get('posts', postId),
            ]);
            const profileObj = profile.get({ plain: true });
            const usersArr = [
                {
                    id: profileObj.id,
                    name: profileObj.name,
                    image: profileObj.image,
                },
            ];
            if (!((_b = (_a = post.data) === null || _a === void 0 ? void 0 : _a.postAudience) === null || _b === void 0 ? void 0 : _b.isMyOrganisation) &&
                !((_e = (_d = (_c = post.data) === null || _c === void 0 ? void 0 : _c.postAudience) === null || _d === void 0 ? void 0 : _d.connectedOrganisations) === null || _e === void 0 ? void 0 : _e.length) &&
                comment.user_id !== profileId) {
                await this.notificationsService.createNotificationWithGrouping({
                    ownerProfileId: comment.user_id,
                    profileId: profileId,
                    type: notifications_args_1.NotificationType.CommentReact,
                    organisationId: organisationId,
                    data: {
                        users: usersArr,
                        postId,
                        activityId: activityId,
                    },
                });
                const usersFullNames = usersArr.map(({ name }) => name);
                const countOfUsers = usersArr.length;
                const objectOfStrings = (0, notificationLayoutHelper_1.getStringsForNotifications)(countOfUsers, usersFullNames);
                const profileIds = [comment.user_id];
                const replacementOrg = await this.organisationsService.findById(organisationId);
                const replacements = [
                    objectOfStrings.usersFullNamesString,
                    objectOfStrings.other,
                    objectOfStrings.has,
                    replacementOrg.name,
                ];
                await this.notificationsService.sendPushNotification({
                    profileIds,
                    replacements,
                    messageType: notifications_args_1.NotificationMessage.CommentReact,
                    route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.organisationPost, {
                        vanityId: organisationId,
                        id: activityId,
                        userId: organisationId,
                    }),
                });
            }
            const [repeatActivity, organisationRole, organisation] = await Promise.all([
                this.activitiesService.findOne({
                    type: activities_args_1.ActivityType.PostCommentLike,
                    postId,
                    data: {
                        [sequelize_1.Op.contains]: {
                            likedById: profileId,
                            commentId: comment.id,
                        },
                    },
                }),
                this.membershipsService.findOne({
                    profileId: profile.id,
                    organisationId: organisationId,
                    status: membership_model_1.MembershipStatus.Active,
                    permissions: {
                        [sequelize_1.Op.overlap]: [
                            membership_model_1.MembershipPermission.Owner,
                            membership_model_1.MembershipPermission.Admin,
                            membership_model_1.MembershipPermission.HiddenAdmin,
                            membership_model_1.MembershipPermission.Manager,
                            membership_model_1.MembershipPermission.Editor,
                        ],
                    },
                }),
                this.organisationsService.findById(organisationId),
            ]);
            await this.activitiesService.createUserActivity({
                type: activities_args_1.ActivityType.PostCommentLike,
                schema: create_user_activity_data_dto_1.commentLikeActivityDataDto,
                data: {
                    likedById: profileId,
                    postAuthorId,
                    commentId: comment.id,
                },
                profileId: comment.user_id,
                postId,
                organisationId: organisation === null || organisation === void 0 ? void 0 : organisation.id,
                addLoyaltyPoints: !repeatActivity,
                placeholders: {
                    id: profileId,
                    createdById: profileId,
                    name: profile.name,
                    vanityId: organisation.vanityId,
                    organisationId: organisation === null || organisation === void 0 ? void 0 : organisation.id,
                    streamActivityId: activityId,
                },
            });
            if (!!organisationRole) {
                try {
                    await this.activitiesService.createOrganisationActivity({
                        type: activities_args_1.ActivityType.OwnerPostCommentLike,
                        data: {
                            likedById: profileId,
                            postAuthorId,
                            commentId: comment.id,
                        },
                        postId,
                        organisationId: organisation.id,
                        createdById: postAuthorId,
                    });
                    if (profileId !== comment.user_id) {
                        await this.organisationLoyaltyPointsService.addPoints({
                            type: activities_args_1.ActivityType.OwnerPostCommentLike,
                            organisationId: organisation.id,
                            placeholders: {
                                createdById: profileId,
                                name: profile.name,
                                vanityId: organisation.vanityId,
                                organisationId: organisation.id,
                                streamActivityId: activityId,
                                activityType: activities_args_1.ActivityType.OwnerPostCommentLike,
                            },
                        });
                    }
                }
                catch (error) {
                    this.logger.error('Error creating OwnerPostCommentLike activity:', error);
                }
            }
            return true;
        }
        catch (e) {
            return false;
        }
    }
    async removeCommentLike(profileId, activityId, commentId, reactionId) {
        try {
            const comment = await this.client.reactions.get(commentId);
            if (!comment)
                return false;
            if (reactionId) {
                await this.removePostLikeLegacy(reactionId);
            }
            const updData = Object.assign({}, comment.data);
            if (!updData.likes) {
                updData.likes = {};
            }
            if (updData.likes[profileId]) {
                delete updData.likes[profileId];
            }
            updData.likesCount =
                (updData.likesCount || 0) - 1 >= 0 ? (updData.likesCount || 0) - 1 : 0;
            await this.client.reactions.update(commentId, updData);
            return true;
        }
        catch (e) {
            return false;
        }
    }
    async createComment(activityId, commentData, user, postAuthorId, organisationId, postId) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q;
        const transaction = await this.sequelize.transaction();
        try {
            const userId = user.profileId;
            const [profile, post, organisationRole] = await Promise.all([
                this.profilesService.findById(userId, {
                    includeParams: [
                        {
                            model: membership_model_1.Membership,
                            as: 'memberships',
                            where: {
                                isPrimary: true,
                            },
                            include: [
                                {
                                    model: organisation_model_1.Organisation,
                                    as: 'organisation',
                                    attributes: ['id', 'name'],
                                },
                            ],
                        },
                    ],
                    transaction,
                }),
                this.client.collections.get('posts', postId),
                this.membershipsService.findOne({
                    profileId: userId,
                    organisationId: organisationId,
                    status: membership_model_1.MembershipStatus.Active,
                    permissions: {
                        [sequelize_1.Op.overlap]: [
                            membership_model_1.MembershipPermission.Owner,
                            membership_model_1.MembershipPermission.Admin,
                            membership_model_1.MembershipPermission.HiddenAdmin,
                            membership_model_1.MembershipPermission.Manager,
                            membership_model_1.MembershipPermission.Editor,
                        ],
                    },
                }, { transaction }),
            ]);
            const membership = profile.memberships[0];
            const userOrganisation = membership.organisation;
            const profilePosition = (membership === null || membership === void 0 ? void 0 : membership.status) === membership_model_1.MembershipStatus.Active
                ? membership === null || membership === void 0 ? void 0 : membership.position
                : null;
            const profileOrganisationName = (membership === null || membership === void 0 ? void 0 : membership.status) === membership_model_1.MembershipStatus.Active
                ? userOrganisation
                    ? userOrganisation === null || userOrganisation === void 0 ? void 0 : userOrganisation.name
                    : membership === null || membership === void 0 ? void 0 : membership.organisationName
                : null;
            let parentComment;
            if (commentData.parentId) {
                parentComment = await this.client.reactions.get(commentData.parentId);
                if (!parentComment)
                    return false;
                await this.client.reactions.addChild('comment', parentComment, Object.assign({
                    user: profile,
                    position: profilePosition,
                    organisationName: profileOrganisationName,
                }, commentData), { userId });
            }
            else {
                await this.client.reactions.add('comment', activityId, Object.assign({
                    user: profile,
                    position: profilePosition,
                    organisationName: profileOrganisationName,
                }, commentData), { userId });
            }
            if (!!organisationRole) {
                try {
                    const isCommentReply = !!commentData.parentId;
                    const activityType = isCommentReply
                        ? activities_args_1.ActivityType.OwnerPostCommentReply
                        : activities_args_1.ActivityType.OwnerPostComment;
                    await this.activitiesService.createOrganisationActivity({
                        type: activityType,
                        createdById: userId,
                        data: {
                            commentById: userId,
                            type: activityType,
                        },
                        organisationId: organisationId,
                        postId,
                    });
                    const userReplyingToSelf = parentComment && userId === parentComment.user_id;
                    if (isCommentReply && !userReplyingToSelf) {
                        await this.organisationLoyaltyPointsService.addPoints({
                            type: activities_args_1.ActivityType.OwnerPostCommentReply,
                            organisationId: organisationId,
                            placeholders: {
                                createdById: userId,
                                name: profile.name,
                                vanityId: userOrganisation.vanityId,
                                organisationId: organisationId,
                                streamActivityId: activityId,
                                postId,
                                activityType: activities_args_1.ActivityType.OwnerPostCommentReply,
                            },
                        });
                    }
                }
                catch (error) {
                    this.logger.error('Error creating OwnerPostComment activity:', error);
                }
            }
            await this.activitiesService.createUserActivity({
                type: activities_args_1.ActivityType.PostInteractions,
                schema: create_user_activity_data_dto_1.newCommentActivityDataDto,
                data: {
                    commentById: userId,
                    type: 'NewComment',
                },
                organisationId: userOrganisation === null || userOrganisation === void 0 ? void 0 : userOrganisation.id,
                postId,
                profileId: userId,
            });
            await this.gettingStartedStepsService.createGettingStartedStep({
                profileId: userId,
                step: getting_started_steps_args_1.GettingStartedStepEnum.PostComment,
                transaction,
            });
            if (postAuthorId !== userId) {
                const limitedProfileData = [
                    {
                        id: profile.id,
                        name: profile.name,
                        image: profile.image,
                    },
                ];
                !((_b = (_a = post.data) === null || _a === void 0 ? void 0 : _a.postAudience) === null || _b === void 0 ? void 0 : _b.isMyOrganisation) &&
                    !((_e = (_d = (_c = post.data) === null || _c === void 0 ? void 0 : _c.postAudience) === null || _d === void 0 ? void 0 : _d.connectedOrganisations) === null || _e === void 0 ? void 0 : _e.length) &&
                    (await this.notificationsService.createNotificationWithGrouping({
                        ownerProfileId: postAuthorId,
                        profileId: userId,
                        organisationId: organisationId,
                        type: notifications_args_1.NotificationType.PostComment,
                        data: {
                            users: limitedProfileData,
                            postId: postId,
                            activityId: activityId,
                        },
                    }, {
                        transaction,
                    }));
                const usersFullNames = limitedProfileData.map(({ name }) => name);
                const countOfUsers = limitedProfileData.length;
                const objectOfStrings = (0, notificationLayoutHelper_1.getStringsForNotifications)(countOfUsers, usersFullNames);
                const profileIds = [postAuthorId];
                const replacements = [
                    objectOfStrings.usersFullNamesString,
                    objectOfStrings.other,
                    objectOfStrings.has,
                ];
                await this.notificationsService.sendPushNotification({
                    profileIds,
                    replacements,
                    messageType: notifications_args_1.NotificationMessage.PostComment,
                    route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.organisationPost, {
                        vanityId: organisationId,
                        id: activityId,
                        userId: organisationId,
                    }),
                });
            }
            if (commentData.users.length > 0) {
                for (const mentionUser of commentData.users) {
                    if (!((_g = (_f = post.data) === null || _f === void 0 ? void 0 : _f.postAudience) === null || _g === void 0 ? void 0 : _g.isMyOrganisation) &&
                        !((_k = (_j = (_h = post.data) === null || _h === void 0 ? void 0 : _h.postAudience) === null || _j === void 0 ? void 0 : _j.connectedOrganisations) === null || _k === void 0 ? void 0 : _k.length) &&
                        mentionUser.id !== userId) {
                        await this.notificationsService.createNotification({
                            ownerProfileId: mentionUser.id,
                            profileId: userId,
                            organisationId: organisationId,
                            type: notifications_args_1.NotificationType.CommentMention,
                            data: {
                                postId,
                                activityId: activityId,
                            },
                        }, {
                            transaction,
                        });
                    }
                }
                const profileIds = commentData.users.map(user => user.id);
                const replacements = [profile.name];
                await this.notificationsService.sendPushNotification({
                    profileIds,
                    replacements,
                    messageType: notifications_args_1.NotificationMessage.CommentMention,
                    route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.organisationPost, {
                        vanityId: organisationId,
                        id: activityId,
                        userId: organisationId,
                    }),
                });
            }
            if (commentData.organisations && commentData.organisations.length > 0) {
                for (const mentionOrg of commentData.organisations) {
                    const orgMemberships = await this.membershipsService.findAll({
                        organisationId: mentionOrg.id,
                        permissions: {
                            [sequelize_1.Op.overlap]: [
                                membership_model_1.MembershipPermission.Owner,
                                membership_model_1.MembershipPermission.Admin,
                                membership_model_1.MembershipPermission.HiddenAdmin,
                                membership_model_1.MembershipPermission.Manager,
                                membership_model_1.MembershipPermission.Editor,
                            ],
                        },
                        status: membership_model_1.MembershipStatus.Active,
                    }, {
                        includeParams: [
                            {
                                model: profile_model_1.Profile,
                                as: 'profile',
                                attributes: ['id', 'name', 'image', 'lastOnlineAt'],
                            },
                        ],
                    });
                    for (const orgMembership of orgMemberships) {
                        const mentionOrgUser = orgMembership.profile;
                        !((_m = (_l = post.data) === null || _l === void 0 ? void 0 : _l.postAudience) === null || _m === void 0 ? void 0 : _m.isMyOrganisation) &&
                            !((_q = (_p = (_o = post.data) === null || _o === void 0 ? void 0 : _o.postAudience) === null || _p === void 0 ? void 0 : _p.connectedOrganisations) === null || _q === void 0 ? void 0 : _q.length) &&
                            (await this.notificationsService.createNotification({
                                ownerProfileId: mentionOrgUser.id,
                                profileId: userId,
                                organisationId: organisationId,
                                type: notifications_args_1.NotificationType.OrgCommentMention,
                                data: {
                                    postId,
                                    organisationId: mentionOrg.id,
                                    activityId: activityId,
                                },
                            }));
                    }
                    const profileIds = orgMemberships.map(user => user.profile.id);
                    const orgFromPost = await this.organisationsService.findById(organisationId);
                    const replacements = [profile.name, mentionOrg.name];
                    await this.notificationsService.sendPushNotification({
                        profileIds,
                        replacements,
                        messageType: notifications_args_1.NotificationMessage.OrgCommentMention,
                        route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.organisationPost, {
                            vanityId: orgFromPost === null || orgFromPost === void 0 ? void 0 : orgFromPost.vanityId,
                            id: activityId,
                            userId: organisationId,
                        }),
                    });
                }
            }
            await transaction.commit();
            return 'true';
        }
        catch (e) {
            await transaction.rollback();
            return e;
        }
    }
    async removeComment(commentId) {
        var _a;
        try {
            await this.client.reactions.delete(commentId);
            return true;
        }
        catch (e) {
            this.logger.error(`StreamPostsService Error: ${e.message}`, (_a = e.response) === null || _a === void 0 ? void 0 : _a.body);
            return false;
        }
        return false;
    }
    async updateComment(commentId, activityId, commentData) {
        var _a;
        try {
            const commentObj = await this.client.reactions.get(commentId);
            if (!commentObj)
                throw new Error();
            const newCommentObj = Object.assign(Object.assign({}, commentObj.data), { text: commentData.text });
            await this.client.reactions.update(commentId, newCommentObj);
            return true;
        }
        catch (e) {
            this.logger.error(`StreamPostsService Error: ${e.message}`, (_a = e.response) === null || _a === void 0 ? void 0 : _a.body);
            return false;
        }
        return false;
    }
    async repostPost(profileId, activityId, organisationId, postData, user, postAuthorId, origOrganisationId, postId) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        this.logger.info('StreamPostsService.repostPost', {
            postData,
            activityId,
            postAuthorId,
            organisationId,
            origOrganisationId,
            postId,
            profileId,
        });
        try {
            const profile = await this.profilesService.findById(profileId);
            const users = postData['users'];
            const organisations = postData['organisations'];
            const parentPost = await this.postsService.createPost({
                text: postData['text'],
                type: postData['type'],
                organisationId: postData['organisationId'],
                profileId,
                status: posts_args_1.PostStatus.Repost,
            }, {
                currentUser: user,
            });
            const parentPostStream = await this.client.collections.add('posts', parentPost.id, Object.assign({
                profileId: profileId,
                users: users,
                organisations: organisations,
                createdAt: new Date(),
                id: parentPost.id,
                postId: parentPost.id,
            }, postData));
            const origOrgFeed = this.client.feed('organisations', origOrganisationId);
            const activities = await origOrgFeed.get({
                limit: 1,
                id_gte: activityId,
            });
            const activity = activities.results[0];
            if (!activity)
                throw new Error();
            let activityNew;
            const organisation = await this.client.collections.get('organisations', organisationId);
            let params = {
                actor: 'SO:' + profileId,
                verb: 'repost',
                repostData: activity,
                post: parentPostStream,
                time: new Date().toISOString(),
                object: organisation,
                foreign_id: `repost:${postId}`,
                user: {
                    name: profile.name,
                    image: profile.image,
                    id: profile.id,
                },
                to: null,
            };
            if (parentPostStream.data.postAudience.isMyOrganisation) {
                const orgMembersFeed = this.client.feed('org_feed_members', organisationId);
                activityNew = await orgMembersFeed.addActivity(params);
            }
            else if ((_a = parentPostStream.data.postAudience.connectedOrganisations) === null || _a === void 0 ? void 0 : _a.length) {
                const listOfConnectedOrgs = parentPostStream.data.postAudience.connectedOrganisations.map(orgId => `org_feed_targeted:${orgId}`);
                params.to = listOfConnectedOrgs;
                const userFeed = this.client.feed('user', profileId);
                try {
                    activityNew = await userFeed.addActivity(params);
                }
                catch (e) {
                    this.logger.error(`Stream.Service Error - addPost: ${e.message}`, (_b = e.response) === null || _b === void 0 ? void 0 : _b.body);
                }
            }
            else {
                const feedOrganisation = this.client.feed('organisations', organisationId);
                activityNew = await feedOrganisation.addActivity(params);
            }
            const route = (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.organisationPost, {
                vanityId: organisationId,
                id: activityNew.id,
                userId: organisationId,
            });
            if (((_c = parentPostStream.data.postAudience) === null || _c === void 0 ? void 0 : _c.isFollowers) &&
                postAuthorId !== profileId) {
                await this.notificationsService.createNotification({
                    ownerProfileId: activity.user.id,
                    profileId: activity.user.id,
                    organisationId: organisationId,
                    type: notifications_args_1.NotificationType.PostShared,
                    data: {
                        postId: postId,
                        activityId: activityNew.id,
                    },
                });
                const profileIds = [activity.user.id];
                const replacementOrg = await this.organisationsService.findById(organisationId);
                const replacements = [replacementOrg.name];
                await this.notificationsService.sendPushNotification({
                    profileIds,
                    replacements,
                    messageType: notifications_args_1.NotificationMessage.PostShared,
                    route: route,
                });
            }
            if (users.length > 0) {
                for (const user of users) {
                    if (!((_e = (_d = parentPostStream.data) === null || _d === void 0 ? void 0 : _d.postAudience) === null || _e === void 0 ? void 0 : _e.isMyOrganisation) &&
                        !((_h = (_g = (_f = parentPostStream.data) === null || _f === void 0 ? void 0 : _f.postAudience) === null || _g === void 0 ? void 0 : _g.connectedOrganisations) === null || _h === void 0 ? void 0 : _h.length) &&
                        user.id !== profileId) {
                        await this.notificationsService.createNotification({
                            ownerProfileId: user.id,
                            profileId: profileId,
                            organisationId: organisationId,
                            type: notifications_args_1.NotificationType.CommentMention,
                            data: {
                                postId: postId,
                                activityId: activityNew.id,
                            },
                        });
                    }
                }
                const profileIds = users.map(user => user.id);
                const replacements = [profile.name];
                await this.notificationsService.sendPushNotification({
                    profileIds,
                    replacements,
                    messageType: notifications_args_1.NotificationMessage.CommentMention,
                    route: route,
                });
            }
            return true;
        }
        catch (e) {
            return false;
        }
    }
    async getLast3PostsOrg(profileId, lastActive) {
        const user = this.client.feed('user', profileId);
        const activites = await user.get({ limit: 30 });
        await new Promise(resolve => setTimeout(resolve, 100));
        const profileLastOnline = (0, moment_1.default)(lastActive).format();
        const organisationIds = activites.results
            .filter(obj => obj.post)
            .reduce((acc, obj) => {
            const postDate = (0, moment_1.default)(obj.time).format();
            if (profileLastOnline < postDate) {
                const orgId = obj.object.split('SO:organisations:')[1];
                acc.length < 3 && !acc.includes(orgId) ? acc.push(orgId) : '';
            }
            return acc;
        }, []);
        return organisationIds;
    }
    async getStreamPostById(postId) {
        return await this.client.collections.get('posts', postId);
    }
    async migratePostCommentGettingStartedRecord({ profileId, transaction, }) {
        var _a;
        try {
            const myComments = await this.client.reactions.filter({
                user_id: profileId,
                kind: 'comment',
                limit: 1,
            });
            const commentsCount = (0, lodash_1.get)(myComments, 'results', [])
                .length;
            if (commentsCount > 0) {
                await this.gettingStartedStepsService.createGettingStartedStep({
                    step: getting_started_steps_args_1.GettingStartedStepEnum.PostComment,
                    profileId,
                    transaction,
                });
            }
        }
        catch (e) {
            this.logger.error(`ChatService.createChatGettingStartedStep Error: ${e.message}`, (_a = e.response) === null || _a === void 0 ? void 0 : _a.body);
        }
    }
    async savePost(profileId, activityId, feedId, feedName) {
        var _a;
        this.logger.info('Starting savePost operation', {
            profileId,
            activityId,
            feedId,
            feedName,
        });
        try {
            if (!profileId || !activityId || !feedId || !feedName) {
                this.logger.warn('savePost called with missing parameters', {
                    profileId,
                    activityId,
                    feedId,
                    feedName,
                });
                return false;
            }
            const feed = this.client.feed(feedName, feedId);
            const activity = await this.getActivityById(feed, activityId);
            if (!activity) {
                this.logger.warn('Original activity not found', {
                    activityId,
                    feedId,
                    feedName,
                });
                return false;
            }
            const targetFeed = `user_saved:${profileId}`;
            const existingTargets = activity.to || [];
            if (existingTargets.includes(targetFeed)) {
                this.logger.info('Post already saved', { profileId, activityId });
                return true;
            }
            const updateResponse = await feed.updateActivityToTargets(activity.foreign_id, activity.time, null, [targetFeed], null);
            this.logger.debug('Activity targeting updated', {
                activityId,
                updateResponse,
                targetFeed,
            });
            const activityLog = {
                type: activities_args_1.ActivityType.SavePost,
                profileId: profileId,
                data: {
                    activityId,
                    feedId,
                    feedName,
                },
            };
            this.logger.debug('Attempting to track activity', activityLog);
            await this.activitiesService.create(activityLog);
            this.logger.info('Post saved successfully via targeting', {
                activityId,
                profileId,
            });
            return true;
        }
        catch (error) {
            this.logger.error('Error saving post:', {
                error: {
                    message: error.message,
                    stack: error.stack,
                    response: (_a = error.response) === null || _a === void 0 ? void 0 : _a.body,
                },
                profileId,
                activityId,
                feedId,
                feedName,
            });
            return false;
        }
    }
    async unsavePost(profileId, activityId, feedId, feedName) {
        var _a;
        this.logger.info('Starting unsavePost operation', {
            profileId,
            activityId,
            feedId,
            feedName,
        });
        try {
            const feed = this.client.feed(feedName, feedId);
            const activity = await this.getActivityById(feed, activityId);
            if (!activity) {
                this.logger.warn('Original activity not found', {
                    activityId,
                    feedId,
                    feedName,
                });
                return false;
            }
            const targetToRemove = `user_saved:${profileId}`;
            const existingTargets = activity.to || [];
            if (existingTargets.includes(targetToRemove)) {
                const updateResponse = await feed.updateActivityToTargets(activity.foreign_id, activity.time, null, null, [targetToRemove]);
                await this.activitiesService.create({
                    type: activities_args_1.ActivityType.UnsavePost,
                    profileId: profileId,
                    data: {
                        activityId,
                        feedId,
                        feedName,
                    },
                });
                this.logger.info('Post unsaved successfully', {
                    updateResponse,
                });
                return true;
            }
            else {
                this.logger.info('Post is not saved for user', {
                    profileId,
                    activityId,
                });
                return false;
            }
        }
        catch (error) {
            this.logger.error('Error unsaving post:', {
                error: {
                    message: error.message,
                    stack: error.stack,
                    response: (_a = error.response) === null || _a === void 0 ? void 0 : _a.body,
                },
                profileId,
                activityId,
            });
            return false;
        }
    }
    async getSavedPostIds(profileId) {
        var _a;
        try {
            const userSavedFeed = this.client.feed('user_saved', profileId);
            const userSavedPosts = await this.fetchAllFeedPosts(userSavedFeed);
            return userSavedPosts.map(activity => activity.id);
        }
        catch (error) {
            this.logger.error('Error getting saved post IDs:', {
                error: {
                    message: error.message,
                    stack: error.stack,
                    response: (_a = error.response) === null || _a === void 0 ? void 0 : _a.body,
                },
                profileId,
            });
            return [];
        }
    }
    async getActivityById(feed, activityId) {
        var _a;
        try {
            const activities = await this.fetchAllFeedPosts(feed);
            return activities.find(activity => activity.id === activityId);
        }
        catch (error) {
            this.logger.error('Error getting activity by ID:', {
                error: {
                    message: error.message,
                    stack: error.stack,
                    response: (_a = error.response) === null || _a === void 0 ? void 0 : _a.body,
                },
                activityId,
            });
            return null;
        }
    }
    async fetchAllFeedPosts(feed, limit = 100, offset = 0, allPosts = []) {
        try {
            const response = await feed.get({
                limit: limit,
                offset: offset,
            });
            const posts = response.results;
            allPosts = allPosts.concat(posts);
            if (posts.length === limit) {
                return await this.fetchAllFeedPosts(feed, limit, offset + limit, allPosts);
            }
            else {
                return allPosts;
            }
        }
        catch (error) {
            console.error('Error fetching posts from GetStream:', error);
            throw error;
        }
    }
};
exports.StreamPostsService = StreamPostsService;
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], StreamPostsService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => posts_service_1.PostsService)),
    __metadata("design:type", posts_service_1.PostsService)
], StreamPostsService.prototype, "postsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], StreamPostsService.prototype, "membershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], StreamPostsService.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => notifications_service_1.NotificationsService)),
    __metadata("design:type", notifications_service_1.NotificationsService)
], StreamPostsService.prototype, "notificationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], StreamPostsService.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => activities_service_1.ActivitiesService)),
    __metadata("design:type", activities_service_1.ActivitiesService)
], StreamPostsService.prototype, "activitiesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentives_service_1.IncentivesService)),
    __metadata("design:type", incentives_service_1.IncentivesService)
], StreamPostsService.prototype, "incentivesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentive_participants_service_1.IncentiveParticipantsService)),
    __metadata("design:type", incentive_participants_service_1.IncentiveParticipantsService)
], StreamPostsService.prototype, "incentiveParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinars_service_1.WebinarsService)),
    __metadata("design:type", webinars_service_1.WebinarsService)
], StreamPostsService.prototype, "webinarsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinar_participants_service_1.WebinarParticipantsService)),
    __metadata("design:type", webinar_participants_service_1.WebinarParticipantsService)
], StreamPostsService.prototype, "webinarParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => events_service_1.EventsService)),
    __metadata("design:type", events_service_1.EventsService)
], StreamPostsService.prototype, "eventsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => followers_service_1.FollowersService)),
    __metadata("design:type", followers_service_1.FollowersService)
], StreamPostsService.prototype, "followersService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => partnerships_service_1.PartnershipsService)),
    __metadata("design:type", partnerships_service_1.PartnershipsService)
], StreamPostsService.prototype, "partnershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => event_invitations_service_1.EventInvitationsService)),
    __metadata("design:type", event_invitations_service_1.EventInvitationsService)
], StreamPostsService.prototype, "eventInvitationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => getting_started_steps_service_1.GettingStartedStepsService)),
    __metadata("design:type", getting_started_steps_service_1.GettingStartedStepsService)
], StreamPostsService.prototype, "gettingStartedStepsService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", sequelize_1.Sequelize)
], StreamPostsService.prototype, "sequelize", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisation_loyalty_points_service_1.OrganisationLoyaltyPointsService)),
    __metadata("design:type", organisation_loyalty_points_service_1.OrganisationLoyaltyPointsService)
], StreamPostsService.prototype, "organisationLoyaltyPointsService", void 0);
exports.StreamPostsService = StreamPostsService = __decorate([
    (0, common_1.Injectable)()
], StreamPostsService);
//# sourceMappingURL=stream-posts.service.js.map