"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamUpdatesResolver = exports.UpdateMessageResponse = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const stream_updates_service_1 = require("./stream-updates.service");
const create_event_input_1 = require("./dto/create-event.input");
let UpdateMessageResponse = class UpdateMessageResponse {
};
exports.UpdateMessageResponse = UpdateMessageResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], UpdateMessageResponse.prototype, "success", void 0);
exports.UpdateMessageResponse = UpdateMessageResponse = __decorate([
    (0, graphql_1.ObjectType)()
], UpdateMessageResponse);
let StreamUpdatesResolver = class StreamUpdatesResolver {
    async createStreamUpdate(user, updateData) {
        this.logger.verbose('StreamUpdatesResolver.createStreamUpdate (mutation)', {
            user: user.toLogObject(),
            updateData,
        });
        const success = await this.streamUpdatesService.addUpdate(user.profileId, updateData);
        return {
            success
        };
    }
    async removeStreamUpdate(user, updateId, organisationId, type, eventId) {
        this.logger.verbose('StreamUpdatesResolver.removeStreamUpdate (mutation)', {
            user: user.toLogObject(),
            updateId,
            organisationId,
            type
        });
        await this.streamUpdatesService.removeUpdate(updateId, organisationId, type, user.profileId, eventId);
        return true;
    }
};
exports.StreamUpdatesResolver = StreamUpdatesResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => stream_updates_service_1.StreamUpdatesService)),
    __metadata("design:type", stream_updates_service_1.StreamUpdatesService)
], StreamUpdatesResolver.prototype, "streamUpdatesService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], StreamUpdatesResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Mutation)(() => UpdateMessageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('postData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_event_input_1.CreateStreamUpdateInput]),
    __metadata("design:returntype", Promise)
], StreamUpdatesResolver.prototype, "createStreamUpdate", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('updateId')),
    __param(2, (0, graphql_1.Args)('organisationId')),
    __param(3, (0, graphql_1.Args)('type')),
    __param(4, (0, graphql_1.Args)('eventId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String, String]),
    __metadata("design:returntype", Promise)
], StreamUpdatesResolver.prototype, "removeStreamUpdate", null);
exports.StreamUpdatesResolver = StreamUpdatesResolver = __decorate([
    (0, graphql_1.Resolver)()
], StreamUpdatesResolver);
//# sourceMappingURL=stream-updates.resolver.js.map