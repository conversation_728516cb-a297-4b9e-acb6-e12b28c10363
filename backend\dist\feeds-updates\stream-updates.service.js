"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamUpdatesService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const getstream_1 = require("getstream");
const config_1 = __importDefault(require("../config/config"));
const memberships_service_1 = require("../memberships/memberships.service");
const profiles_service_1 = require("../profiles/profiles.service");
const event_invitations_service_1 = require("../event-invitations/event-invitations.service");
const posts_args_1 = require("../feeds-posts/args/posts.args");
const update_args_1 = require("./args/update.args");
const webinar_participants_service_1 = require("../webinar-participants/webinar-participants.service");
const incentive_participants_service_1 = require("../incentive-participants/incentive-participants.service");
let StreamUpdatesService = class StreamUpdatesService {
    onModuleInit() {
        if (config_1.default.SERVICE === 'api-beta') {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_BETA_KEY, config_1.default.STREAM_BETA_SECRET);
        }
        else {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_KEY, config_1.default.STREAM_SECRET);
        }
    }
    async onApplicationBootstrap() {
        console.log('onApplicationBootstrap');
    }
    async addUpdate(profileId, updateData) {
        const organisationId = updateData['organisationId'];
        const postId = updateData['postId'];
        if (!postId)
            return false;
        let type = update_args_1.StreamUpdateType.Post;
        if (updateData['eventId']) {
            type = update_args_1.StreamUpdateType.Event;
        }
        else if (updateData['webinarId']) {
            type = update_args_1.StreamUpdateType.Webinar;
        }
        else if (updateData['incentiveId']) {
            type = update_args_1.StreamUpdateType.Incentive;
        }
        const membership = await this.membershipsService.findMembership(organisationId, profileId);
        const organisation = await this.client.collections.get('organisations', organisationId);
        const update = await this.client.collections.add('updates', postId, Object.assign({
            profileId: profileId,
            permission: membership ? membership.permissions : [],
            createdAt: new Date(),
            id: postId,
            typeUpdates: type,
            type: posts_args_1.PostStreamType.Normal,
        }, updateData));
        if (updateData['eventId']) {
            await this.addEventUpdate(updateData['eventId'], organisation, update, membership, postId);
        }
        else if (updateData['webinarId']) {
            await this.addWebinarUpdate(updateData['webinarId'], organisation, update, membership, postId);
        }
        else if (updateData['incentiveId']) {
            await this.addIncentiveUpdate(updateData['incentiveId'], organisation, update, membership, postId);
        }
        return true;
    }
    async addEventUpdate(eventId, organisation, update, membership, postId) {
        var _a;
        const eventRef = this.client.collections.entry('events', eventId);
        const authorProfileId = (_a = update.data) === null || _a === void 0 ? void 0 : _a.profileId;
        const eventFeed = await this.client.feed('events', eventId);
        const updateId = update.data.id;
        await eventFeed.addActivity({
            actor: 'SO:' + authorProfileId,
            foreign_id: 'events-updates:' + updateId,
            verb: 'events',
            object: eventRef,
            update: update,
            organisation: organisation,
            permission: membership ? membership.permissions : [],
            to: [`user:${authorProfileId}`],
        });
    }
    async addWebinarUpdate(webinarId, organisation, update, membership, postId) {
        var _a;
        const webinarRef = this.client.collections.entry('webinars', webinarId);
        const authorProfileId = (_a = update.data) === null || _a === void 0 ? void 0 : _a.profileId;
        const webinarFeed = await this.client.feed('webinars', webinarId);
        const updateId = update.data.id;
        await webinarFeed.addActivity({
            actor: 'SO:' + authorProfileId,
            foreign_id: 'webinars-updates:' + updateId,
            verb: 'webinars',
            object: webinarRef,
            update: update,
            organisation: organisation,
            permission: membership ? membership.permissions : [],
            to: [`user:${authorProfileId}`],
        });
    }
    async addIncentiveUpdate(incentiveId, organisation, update, membership, postId) {
        var _a;
        this.logger.info('StreamPostsService.addIncentiveUpdate', {
            incentiveId,
            update,
            postId,
        });
        const incentiveRef = this.client.collections.entry('incentives', incentiveId);
        const authorProfileId = (_a = update.data) === null || _a === void 0 ? void 0 : _a.profileId;
        const incentiveFeed = await this.client.feed('incentives', incentiveId);
        const updateId = update.data.id;
        await incentiveFeed.addActivity({
            actor: 'SO:' + authorProfileId,
            foreign_id: 'incentives-updates:' + updateId,
            verb: 'incentives',
            object: incentiveRef,
            update: update,
            organisation: organisation,
            permission: membership ? membership.permissions : [],
            to: [`user:${authorProfileId}`],
        });
    }
    async removeUpdate(updateId, organisationId, type, profileId, eventId) {
        var _a, _b, _c, _d;
        const membership = await this.membershipsService.findMembership(organisationId, profileId);
        if (membership && membership.permissions) {
            const currUpdate = await this.client.collections.get('updates', updateId);
            let entityId;
            if (type === 'events') {
                entityId = (_a = currUpdate === null || currUpdate === void 0 ? void 0 : currUpdate.data) === null || _a === void 0 ? void 0 : _a.eventId;
            }
            else if (type === 'incentives') {
                entityId = (_b = currUpdate === null || currUpdate === void 0 ? void 0 : currUpdate.data) === null || _b === void 0 ? void 0 : _b.incentiveId;
            }
            else if (type === 'webinars') {
                entityId = (_c = currUpdate === null || currUpdate === void 0 ? void 0 : currUpdate.data) === null || _c === void 0 ? void 0 : _c.webinarId;
            }
            const entityFeed = await this.client.feed(type, entityId);
            try {
                await entityFeed.removeActivity({
                    foreignId: type + '-updates:' + updateId,
                });
                await this.client.collections.delete('updates', updateId);
            }
            catch (e) {
                this.logger.error(`Stream.Service Error - removeUpdate: ${e.message}`, (_d = e.response) === null || _d === void 0 ? void 0 : _d.body);
            }
            return true;
        }
        return false;
    }
};
exports.StreamUpdatesService = StreamUpdatesService;
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], StreamUpdatesService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], StreamUpdatesService.prototype, "membershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], StreamUpdatesService.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => event_invitations_service_1.EventInvitationsService)),
    __metadata("design:type", event_invitations_service_1.EventInvitationsService)
], StreamUpdatesService.prototype, "eventInvitationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinar_participants_service_1.WebinarParticipantsService)),
    __metadata("design:type", webinar_participants_service_1.WebinarParticipantsService)
], StreamUpdatesService.prototype, "webinarParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentive_participants_service_1.IncentiveParticipantsService)),
    __metadata("design:type", incentive_participants_service_1.IncentiveParticipantsService)
], StreamUpdatesService.prototype, "incentiveParticipantsService", void 0);
exports.StreamUpdatesService = StreamUpdatesService = __decorate([
    (0, common_1.Injectable)()
], StreamUpdatesService);
//# sourceMappingURL=stream-updates.service.js.map