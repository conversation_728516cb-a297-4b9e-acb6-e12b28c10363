"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamWebinarParticipantsArgs = exports.StreamWebinarParticipantsFilter = exports.WebinarParticipantStatus = void 0;
const graphql_1 = require("@nestjs/graphql");
var WebinarParticipantStatus;
(function (WebinarParticipantStatus) {
    WebinarParticipantStatus["Host"] = "Host";
    WebinarParticipantStatus["HostAdmin"] = "HostAdmin";
    WebinarParticipantStatus["Speaker"] = "Speaker";
    WebinarParticipantStatus["HiddenHost"] = "HiddenHost";
    WebinarParticipantStatus["Registered"] = "Registered";
    WebinarParticipantStatus["InvitedByHost"] = "InvitedByHost";
    WebinarParticipantStatus["InvitedByParticipant"] = "InvitedByParticipant";
    WebinarParticipantStatus["InvitedRegistered"] = "InvitedRegistered";
    WebinarParticipantStatus["Blocked"] = "Blocked";
})(WebinarParticipantStatus || (exports.WebinarParticipantStatus = WebinarParticipantStatus = {}));
(0, graphql_1.registerEnumType)(WebinarParticipantStatus, {
    name: 'WebinarParticipantStatus',
});
let StreamWebinarParticipantsFilter = class StreamWebinarParticipantsFilter {
};
exports.StreamWebinarParticipantsFilter = StreamWebinarParticipantsFilter;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID, {
        nullable: true,
    }),
    __metadata("design:type", String)
], StreamWebinarParticipantsFilter.prototype, "profileId", void 0);
__decorate([
    (0, graphql_1.Field)(() => [WebinarParticipantStatus], {
        nullable: true,
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], StreamWebinarParticipantsFilter.prototype, "status", void 0);
exports.StreamWebinarParticipantsFilter = StreamWebinarParticipantsFilter = __decorate([
    (0, graphql_1.InputType)()
], StreamWebinarParticipantsFilter);
let StreamWebinarParticipantsArgs = class StreamWebinarParticipantsArgs {
};
exports.StreamWebinarParticipantsArgs = StreamWebinarParticipantsArgs;
__decorate([
    (0, graphql_1.Field)(() => StreamWebinarParticipantsFilter, { nullable: true }),
    __metadata("design:type", StreamWebinarParticipantsFilter)
], StreamWebinarParticipantsArgs.prototype, "filter", void 0);
exports.StreamWebinarParticipantsArgs = StreamWebinarParticipantsArgs = __decorate([
    (0, graphql_1.ArgsType)()
], StreamWebinarParticipantsArgs);
//# sourceMappingURL=webinar-participants.args.js.map