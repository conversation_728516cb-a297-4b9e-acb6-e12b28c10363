"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamWebinarParticipantsResolver = exports.webinarParticipantsMessageResponse = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const stream_webinar_participants_service_1 = require("./stream-webinar-participants.service");
const create_webinar_participant_input_1 = require("./dto/create-webinar-participant.input");
const create_webinar_participants_input_1 = require("../webinar-participants/dto/create-webinar-participants.input");
let webinarParticipantsMessageResponse = class webinarParticipantsMessageResponse {
};
exports.webinarParticipantsMessageResponse = webinarParticipantsMessageResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], webinarParticipantsMessageResponse.prototype, "webinarParticipants", void 0);
exports.webinarParticipantsMessageResponse = webinarParticipantsMessageResponse = __decorate([
    (0, graphql_1.ObjectType)()
], webinarParticipantsMessageResponse);
let StreamWebinarParticipantsResolver = class StreamWebinarParticipantsResolver {
    async streamInviteToWebinar(user, webinarParticipantsData) {
        this.logger.verbose('StreamWebinarParticipantsResolver.streamInviteToWebinar (mutation)', {
            user: user.toLogObject(),
            webinarId: webinarParticipantsData.webinarId,
            profileIds: webinarParticipantsData.profileIds,
        });
        const webinarParticipants = await this.streamWebinarParticipantsService.inviteToWebinar(webinarParticipantsData, user);
        return {
            webinarParticipants
        };
    }
    async streamCreateWebinarParticipant(user, webinarParticipantData) {
        const success = await this
            .streamWebinarParticipantsService
            .createWebinarParticipants({
            profileIds: [webinarParticipantData.profileId],
            inviterProfileId: user.profileId,
            webinarId: webinarParticipantData.webinarId,
            organisationId: webinarParticipantData.organisationId,
            status: webinarParticipantData.role,
        }, user.profileId);
        return success;
    }
    async streamRemoveWebinarParticipant(user, id) {
        this.logger.verbose('StreamWebinarParticipantsResolver.streamRemoveWebinarParticipant (mutation)', {
            user: user.toLogObject(),
            id
        });
        return await this.streamWebinarParticipantsService.removeWebinarParticipant(id);
    }
};
exports.StreamWebinarParticipantsResolver = StreamWebinarParticipantsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => stream_webinar_participants_service_1.StreamWebinarParticipantsService)),
    __metadata("design:type", stream_webinar_participants_service_1.StreamWebinarParticipantsService)
], StreamWebinarParticipantsResolver.prototype, "streamWebinarParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], StreamWebinarParticipantsResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Mutation)(() => webinarParticipantsMessageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('webinarParticipantsData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_webinar_participants_input_1.CreateWebinarParticipantsInput]),
    __metadata("design:returntype", Promise)
], StreamWebinarParticipantsResolver.prototype, "streamInviteToWebinar", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('webinarParticipantData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_webinar_participant_input_1.StreamCreateWebinarParticipantInput]),
    __metadata("design:returntype", Promise)
], StreamWebinarParticipantsResolver.prototype, "streamCreateWebinarParticipant", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], StreamWebinarParticipantsResolver.prototype, "streamRemoveWebinarParticipant", null);
exports.StreamWebinarParticipantsResolver = StreamWebinarParticipantsResolver = __decorate([
    (0, graphql_1.Resolver)()
], StreamWebinarParticipantsResolver);
//# sourceMappingURL=stream-webinar-participants.resolver.js.map