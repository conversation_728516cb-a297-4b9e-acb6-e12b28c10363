"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamWebinarParticipantsService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const getstream_1 = require("getstream");
const config_1 = __importDefault(require("../config/config"));
const memberships_service_1 = require("../memberships/memberships.service");
const webinars_service_1 = require("../webinars/webinars.service");
const webinar_participants_args_1 = require("./args/webinar-participants.args");
const memberships_service_rights_1 = require("../memberships/helpers/memberships.service.rights");
const webinar_participants_service_1 = require("../webinar-participants/webinar-participants.service");
const profiles_service_1 = require("../profiles/profiles.service");
const stream_followers_service_1 = require("../feeds-followers/stream-followers.service");
const short_uuid_1 = __importDefault(require("short-uuid"));
let StreamWebinarParticipantsService = class StreamWebinarParticipantsService {
    onModuleInit() {
        if (config_1.default.SERVICE === 'api-beta') {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_BETA_KEY, config_1.default.STREAM_BETA_SECRET);
        }
        else {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_KEY, config_1.default.STREAM_SECRET);
        }
    }
    async onApplicationBootstrap() {
        console.log('onApplicationBootstrap');
    }
    async inviteToWebinar(data, user) {
        try {
            const webinar = await this.client.collections.get('webinars', data.webinarId);
            const webinarParticipantStatus = await this.verifyInvitePermission(user, webinar);
            return this.createWebinarParticipants({
                profileIds: data.profileIds,
                inviterProfileId: user.profileId,
                webinarId: data.webinarId,
                organisationId: webinar.data.organisationId,
                status: webinarParticipantStatus,
            }, user.profileId);
        }
        catch (e) {
            throw new Error(`StreamWebinarParticipantsService.createWebinarParticipant - Webinar does not exist.`);
        }
        return false;
    }
    async verifyInvitePermission(user, webinar) {
        let isParticipantInvitation = false;
        if (webinar.data.isPublic) {
            isParticipantInvitation = true;
        }
        else if (webinar.data.isParticipantsCanInvite) {
            const webinarParticipant = await this.client.collections.get('webinar-participants', webinar.data.id);
            const webinarParticipantStatus = [
                webinar_participants_args_1.WebinarParticipantStatus.Registered,
                webinar_participants_args_1.WebinarParticipantStatus.Speaker,
                webinar_participants_args_1.WebinarParticipantStatus.Host,
                webinar_participants_args_1.WebinarParticipantStatus.HostAdmin,
                webinar_participants_args_1.WebinarParticipantStatus.InvitedByHost,
                webinar_participants_args_1.WebinarParticipantStatus.HiddenHost,
            ];
            if (!webinarParticipantStatus.includes(webinarParticipant.data.status)) {
                isParticipantInvitation = true;
            }
        }
        try {
            await this.membershipsServiceRights.checkRights(webinar.data.organisationId, {
                currentProfileId: user.profileId,
                rights: [memberships_service_rights_1.MembershipRight.InviteToWebinar],
            });
        }
        catch (e) {
            if (isParticipantInvitation) {
                return webinar_participants_args_1.WebinarParticipantStatus.InvitedByParticipant;
            }
            throw e;
        }
        return webinar_participants_args_1.WebinarParticipantStatus.InvitedByHost;
    }
    async createWebinarParticipants(dto, currentUser) {
        try {
            for (const profileId of dto.profileIds) {
                let webinarParticipant = await this.webinarParticipantsService.findOne({
                    profileId: profileId,
                    webinarId: dto.webinarId,
                });
                if (!webinarParticipant) {
                    const profile = await this.profilesService.findById(currentUser);
                    const organisationRef = this.client.collections.entry('organisations', dto.organisationId);
                    const feedUser = this.client.feed('user', profileId);
                    const webinarRef = this.client.collections.entry('webinars', dto.webinarId);
                    await feedUser.addActivity({
                        actor: 'SO:' + profileId,
                        foreign_id: 'webinars:' + dto.webinarId,
                        verb: 'organisations',
                        object: organisationRef,
                        webinar: webinarRef,
                        status: dto.status,
                        username: profile.name,
                        senderId: currentUser,
                        type: 'invitations',
                    });
                }
            }
            return true;
        }
        catch (e) {
            throw new Error(`StreamWebinarParticipantsService.createWebinarParticipants - ` +
                e.message);
        }
        return false;
    }
    async removeWebinarParticipant(webinarId) {
        try {
            await this.client.collections.delete('webinar-participants', webinarId);
            return true;
        }
        catch (e) {
            throw new Error(`StreamWebinarParticipantsService.removeWebinarParticipant - ` +
                e.message);
        }
        return false;
    }
    async createWebinarParticipant(dto) {
        const profile = await this.profilesService.findById(dto.inviterProfileId);
        const organisationRef = this.client.collections.entry('organisations', dto.organisationId);
        const feedUser = this.client.feed('user', dto.profileId);
        const webinarRef = this.client.collections.entry('webinars', dto.webinarId);
        await feedUser.addActivity({
            actor: 'SO:' + dto.profileId,
            foreign_id: 'webinars:' + dto.webinarId,
            verb: 'organisations',
            object: organisationRef,
            webinar: webinarRef,
            status: dto.status,
            username: profile.name,
            senderId: dto.inviterProfileId,
            type: 'invitations',
        });
        return true;
    }
    async createWebinarInviteFeedandBulkFollow(dto, user) {
        try {
            const webinar = await this.client.collections.get('webinars', dto.webinarId);
            const randomId = short_uuid_1.default.generate();
            const invitePost = await this.client.collections.add('invites', randomId, Object.assign({
                profileId: user.profileId,
                users: dto.profileIds,
                organisation: dto.organisationId,
                createdAt: new Date(),
            }, webinar.data));
            const feedInvite = await this.client.feed('invites', randomId);
            const organisationRef = this.client.collections.entry('organisations', dto.organisationId);
            const params = {
                actor: 'SO:' + user.profileId,
                verb: 'webinars-invite',
                object: organisationRef,
                webinar: invitePost,
                foreign_id: `invite:${randomId}`,
                time: new Date().toISOString(),
            };
            await feedInvite.addActivity(params);
            const followsArray = [];
            for (const profileId of dto.profileIds) {
                const inviteFeedFollow = {
                    source: 'user:' + profileId,
                    target: 'invites:' + randomId,
                };
                followsArray.push(inviteFeedFollow);
            }
            await this.streamFollowersService.bulkFollowScript(followsArray);
            return true;
        }
        catch (err) {
            throw new Error(`StreamWebinarParticipantsService.createWebinarInviteFeedandBulkFollow - ` +
                err.message);
        }
    }
};
exports.StreamWebinarParticipantsService = StreamWebinarParticipantsService;
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], StreamWebinarParticipantsService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], StreamWebinarParticipantsService.prototype, "membershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinars_service_1.WebinarsService)),
    __metadata("design:type", webinars_service_1.WebinarsService)
], StreamWebinarParticipantsService.prototype, "webinarsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinar_participants_service_1.WebinarParticipantsService)),
    __metadata("design:type", webinar_participants_service_1.WebinarParticipantsService)
], StreamWebinarParticipantsService.prototype, "webinarParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_rights_1.MembershipsServiceRights)),
    __metadata("design:type", memberships_service_rights_1.MembershipsServiceRights)
], StreamWebinarParticipantsService.prototype, "membershipsServiceRights", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], StreamWebinarParticipantsService.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => stream_followers_service_1.StreamFollowersService)),
    __metadata("design:type", stream_followers_service_1.StreamFollowersService)
], StreamWebinarParticipantsService.prototype, "streamFollowersService", void 0);
exports.StreamWebinarParticipantsService = StreamWebinarParticipantsService = __decorate([
    (0, common_1.Injectable)()
], StreamWebinarParticipantsService);
//# sourceMappingURL=stream-webinar-participants.service.js.map