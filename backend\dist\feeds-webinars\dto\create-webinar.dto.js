"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamCreateWebinarWithPreRecordedDto = exports.StreamCreateWebinarDto = void 0;
const class_validator_1 = require("class-validator");
const webinars_args_1 = require("../args/webinars.args");
const update_webinar_dto_1 = require("./update-webinar.dto");
class StreamCreateWebinarDto extends update_webinar_dto_1.StreamUpdateWebinarDto {
}
exports.StreamCreateWebinarDto = StreamCreateWebinarDto;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], StreamCreateWebinarDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], StreamCreateWebinarDto.prototype, "webinarId", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], StreamCreateWebinarDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], StreamCreateWebinarDto.prototype, "isAllDay", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Date)
], StreamCreateWebinarDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Date)
], StreamCreateWebinarDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], StreamCreateWebinarDto.prototype, "isPublic", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], StreamCreateWebinarDto.prototype, "isParticipantListVisible", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], StreamCreateWebinarDto.prototype, "isInternal", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", String)
], StreamCreateWebinarDto.prototype, "organisationId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], StreamCreateWebinarDto.prototype, "assetId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], StreamCreateWebinarDto.prototype, "liveStreamRecordingId", void 0);
class StreamCreateWebinarWithPreRecordedDto extends update_webinar_dto_1.StreamUpdateWebinarDto {
}
exports.StreamCreateWebinarWithPreRecordedDto = StreamCreateWebinarWithPreRecordedDto;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], StreamCreateWebinarWithPreRecordedDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], StreamCreateWebinarWithPreRecordedDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], StreamCreateWebinarWithPreRecordedDto.prototype, "isAllDay", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", String)
], StreamCreateWebinarWithPreRecordedDto.prototype, "liveStreamRecordingId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], StreamCreateWebinarWithPreRecordedDto.prototype, "isPublic", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], StreamCreateWebinarWithPreRecordedDto.prototype, "isParticipantListVisible", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", String)
], StreamCreateWebinarWithPreRecordedDto.prototype, "organisationId", void 0);
//# sourceMappingURL=create-webinar.dto.js.map