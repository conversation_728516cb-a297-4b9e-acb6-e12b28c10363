"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamCreateWebinarInput = void 0;
const graphql_1 = require("@nestjs/graphql");
const update_webinar_input_1 = require("./update-webinar.input");
const class_validator_1 = require("class-validator");
const webinars_args_1 = require("../args/webinars.args");
let StreamCreateWebinarInput = class StreamCreateWebinarInput extends update_webinar_input_1.StreamUpdateWebinarInput {
};
exports.StreamCreateWebinarInput = StreamCreateWebinarInput;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], StreamCreateWebinarInput.prototype, "webinarId", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], StreamCreateWebinarInput.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    (0, graphql_1.Field)(() => webinars_args_1.StreamWebinarType),
    __metadata("design:type", String)
], StreamCreateWebinarInput.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], StreamCreateWebinarInput.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Date)
], StreamCreateWebinarInput.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Date)
], StreamCreateWebinarInput.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], StreamCreateWebinarInput.prototype, "isPublic", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], StreamCreateWebinarInput.prototype, "isParticipantsCanInvite", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], StreamCreateWebinarInput.prototype, "isInternal", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(() => graphql_1.ID, { nullable: false }),
    __metadata("design:type", String)
], StreamCreateWebinarInput.prototype, "organisationId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], StreamCreateWebinarInput.prototype, "liveStreamRecordingId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Number)
], StreamCreateWebinarInput.prototype, "duration", void 0);
exports.StreamCreateWebinarInput = StreamCreateWebinarInput = __decorate([
    (0, graphql_1.InputType)()
], StreamCreateWebinarInput);
//# sourceMappingURL=create-webinar.input.js.map