"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamWebinarsResolver = exports.WebinarMessageResponse = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const stream_webinars_service_1 = require("./stream-webinars.service");
const webinar_model_1 = require("../webinars/models/webinar.model");
const create_webinar_input_1 = require("./dto/create-webinar.input");
const update_webinar_input_1 = require("./dto/update-webinar.input");
const webinar_participant_model_1 = require("../webinar-participants/models/webinar-participant.model");
const organisations_service_1 = require("../organisations/organisations.service");
let WebinarMessageResponse = class WebinarMessageResponse {
};
exports.WebinarMessageResponse = WebinarMessageResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], WebinarMessageResponse.prototype, "organisationId", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], WebinarMessageResponse.prototype, "vanityId", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], WebinarMessageResponse.prototype, "id", void 0);
exports.WebinarMessageResponse = WebinarMessageResponse = __decorate([
    (0, graphql_1.ObjectType)()
], WebinarMessageResponse);
let StreamWebinarsResolver = class StreamWebinarsResolver {
    async streamCreateWebinar(user, webinarData) {
        this.logger.verbose('StreamWebinarsResolver.streamCreateWebinar (mutation)', {
            user: user.toLogObject(),
            webinarData,
        });
        let webinar = await this.streamWebinarsService.createWebinar(webinarData, user.profileId);
        if (webinarData.liveStreamRecordingId) {
            const muxData = webinarData.liveStreamRecordingId.split('|');
            webinarData.liveStreamRecordingId = muxData[0];
            webinar = await this.streamWebinarsService.createWebinar(Object.assign({ assetId: muxData[1] }, webinarData), user.profileId);
        }
        if (webinar) {
            const organisation = await this.organisationsService.findById(webinar.data.organisationId, {
                useCache: true
            });
            if (!organisation) {
                throw new Error(`Organisation not found`);
            }
            return {
                id: webinarData.webinarId,
                organisationId: webinarData.organisationId,
                vanityId: organisation.vanityId
            };
        }
        return {
            id: 'false',
            organisationId: 'false',
            vanityId: 'false'
        };
    }
    async streamRemoveWebinar(user, id) {
        this.logger.verbose('StreamWebinarsResolver.streamRemoveWebinar (mutation)', {
            user: user.toLogObject(),
            id
        });
        return await this.streamWebinarsService.removeWebinar(id, user.profileId);
    }
    async streamUpdateWebinar(user, webinarId, webinarData) {
        this.logger.verbose('StreamWebinarsResolver.streamUpdateWebinar (mutation)', {
            user: user.toLogObject(),
            webinarData,
        });
        const success = this.streamWebinarsService.updateWebinar(user, webinarId, webinarData);
        return success;
    }
    async streamRegisterToWebinar(user, webinarId) {
        this.logger.verbose('StreamWebinarsResolver.streamRegisterToWebinar (mutation)', {
            user: user.toLogObject(),
            webinarId,
        });
        return this.streamWebinarsService.register(user, webinarId);
    }
    async streamUnregisterToWebinar(user, webinarId, webinarParticipantId) {
        this.logger.verbose('StreamWebinarsResolver.streamUnregisterToWebinar (mutation)', {
            user: user.toLogObject(),
            webinarId,
        });
        return this.streamWebinarsService.unregister(user, webinarId, webinarParticipantId);
    }
};
exports.StreamWebinarsResolver = StreamWebinarsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => stream_webinars_service_1.StreamWebinarsService)),
    __metadata("design:type", stream_webinars_service_1.StreamWebinarsService)
], StreamWebinarsResolver.prototype, "streamWebinarsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], StreamWebinarsResolver.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], StreamWebinarsResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Mutation)(() => WebinarMessageResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('webinarData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_webinar_input_1.StreamCreateWebinarInput]),
    __metadata("design:returntype", Promise)
], StreamWebinarsResolver.prototype, "streamCreateWebinar", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], StreamWebinarsResolver.prototype, "streamRemoveWebinar", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('webinarId')),
    __param(2, (0, graphql_1.Args)('webinarData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, update_webinar_input_1.StreamUpdateWebinarInput]),
    __metadata("design:returntype", Promise)
], StreamWebinarsResolver.prototype, "streamUpdateWebinar", null);
__decorate([
    (0, graphql_1.Mutation)(() => webinar_participant_model_1.WebinarParticipant),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('webinarId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], StreamWebinarsResolver.prototype, "streamRegisterToWebinar", null);
__decorate([
    (0, graphql_1.Mutation)(() => webinar_participant_model_1.WebinarParticipant),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('webinarId')),
    __param(2, (0, graphql_1.Args)('webinarParticipantId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], StreamWebinarsResolver.prototype, "streamUnregisterToWebinar", null);
exports.StreamWebinarsResolver = StreamWebinarsResolver = __decorate([
    (0, graphql_1.Resolver)(() => webinar_model_1.Webinar)
], StreamWebinarsResolver);
//# sourceMappingURL=stream-webinars.resolver.js.map