"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamWebinarsService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const getstream_1 = require("getstream");
const config_1 = __importDefault(require("../config/config"));
const memberships_service_1 = require("../memberships/memberships.service");
const webinars_service_1 = require("../webinars/webinars.service");
const events_args_1 = require("../events/args/events.args");
const webinar_participants_args_1 = require("../webinar-participants/args/webinar-participants.args");
const webinar_participants_service_1 = require("../webinar-participants/webinar-participants.service");
const profiles_service_1 = require("../profiles/profiles.service");
let StreamWebinarsService = class StreamWebinarsService {
    onModuleInit() {
        if (config_1.default.SERVICE === 'api-beta') {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_BETA_KEY, config_1.default.STREAM_BETA_SECRET);
        }
        else {
            this.client = (0, getstream_1.connect)(config_1.default.STREAM_KEY, config_1.default.STREAM_SECRET);
        }
    }
    async onApplicationBootstrap() {
        console.log('onApplicationBootstrap');
    }
    async createWebinar(dto, profileId) {
        dto.description &&
            (dto.description = dto.description.replace(/\n\s*\n\s*\n/g, '\n\n'));
        let alreadyCreated = false;
        try {
            const { liveStreamId, liveStreamPrivateKey, liveStreamPlaybackId, chatChannelId, eventId, organisationId, } = await this.webinarsService.findById(dto.webinarId);
            const membership = await this.membershipsService.findMembership(organisationId, profileId);
            let webinar;
            try {
                const existingWebinar = await this.client.collections.get('webinars', dto.webinarId);
                if (existingWebinar) {
                    alreadyCreated = true;
                    webinar = await this.client.collections.update('webinars', dto.webinarId, Object.assign({ profileId: profileId, id: dto.webinarId, permission: membership ? membership.permissions : [], liveStreamId,
                        eventId,
                        liveStreamPrivateKey,
                        liveStreamPlaybackId,
                        chatChannelId }, dto));
                }
            }
            catch (err) {
                webinar = await this.client.collections.add('webinars', dto.webinarId, Object.assign({ profileId: profileId, id: dto.webinarId, permission: membership ? membership.permissions : [], liveStreamId,
                    eventId,
                    liveStreamPrivateKey,
                    liveStreamPlaybackId,
                    chatChannelId }, dto));
            }
            let event;
            try {
                const existingEvent = await this.client.collections.get('events', eventId);
                if (existingEvent) {
                    event = await this.client.collections.update('events', eventId, {
                        name: '',
                        description: '',
                        type: events_args_1.EventType.Webinar,
                        isAllDay: false,
                        startDate: dto.startDate,
                        endDate: dto.endDate,
                        organisationId: webinar.organisationId,
                        webinarId: dto.webinarId,
                        webinarName: webinar.name,
                    });
                }
            }
            catch (err) {
                event = await this.client.collections.add('events', eventId, {
                    name: '',
                    description: '',
                    type: events_args_1.EventType.Webinar,
                    isAllDay: false,
                    startDate: dto.startDate,
                    endDate: dto.endDate,
                    organisationId: webinar.organisationId,
                    webinarId: dto.webinarId,
                    webinarName: webinar.name,
                });
            }
            return webinar;
        }
        catch (e) {
            throw new Error(`StreamWebinarsService.streamCreateWebinar - ` + e.message);
        }
        return 'false';
    }
    async removeWebinar(id, profileId) {
        try {
            const webinar = await this.client.collections.get('webinars', id);
            const membership = await this.membershipsService.findMembership(webinar.data.organisationId, profileId);
            if (membership && membership.permissions) {
                const webinarParticipantDB = await this.webinarParticipantsService.findAll({
                    webinarId: id,
                });
                const currentFeedUser = await this.client.feed('user', profileId);
                await currentFeedUser.removeActivity({ foreignId: 'webinars:' + id });
                for (const participant of webinarParticipantDB) {
                    const feedUser = await this.client.feed('user', participant.profileId);
                    await feedUser.removeActivity({ foreignId: 'webinars:' + id });
                }
                await this.client.collections.delete('events', webinar.data.eventId);
                await this.client.collections.delete('webinars', id);
                return true;
            }
        }
        catch (e) {
            if (e && e.response && e.response.status == 404) {
                return false;
            }
            else {
                throw new Error(`StreamWebinarsService.streamRemoveWebinar - ` + e.message);
            }
        }
        return false;
    }
    async updateWebinar(user, id, dto) {
        this.logger.verbose('StreamWebinarsService.updateWebinar', {
            user: user.toLogObject(),
            webinarId: id,
            dto,
        });
        try {
            const webinar = await this.client.collections.get('webinars', id);
            const events = await this.client.collections.get('events', webinar.data.eventId);
            dto.description &&
                (dto.description = dto.description.replace(/\n\s*\n\s*\n/g, '\n\n'));
            await this.client.collections.update('webinars', id, Object.assign(webinar.data, dto));
            await this.client.collections.update('events', webinar.data.eventId, Object.assign(events.data, {
                startDate: dto.startDate || webinar.startDate,
                endDate: dto.endDate || webinar.endDate,
                webinarName: dto.name || webinar.name,
            }));
            return true;
        }
        catch (e) {
            throw new Error(`StreamWebinarsService.streamUpdateWebinar - ` + e.message);
        }
        return false;
    }
    async register(user, id) {
        this.logger.verbose('StreamWebinarsService.register', {
            user: user.toLogObject(),
            webinarId: id,
        });
        const webinar = await this.client.collections.get('webinars', id);
        try {
            const webinarParticipantDB = await this.webinarParticipantsService.findOne({
                profileId: user.profileId,
                webinarId: id,
            });
            let webinarParticipant = await this.client.collections.get('webinar_participants', webinarParticipantDB.id);
            let status = webinarParticipant.data.status;
            if ((webinarParticipant === null || webinarParticipant === void 0 ? void 0 : webinarParticipant.data.status) ===
                webinar_participants_args_1.WebinarParticipantStatus.InvitedByHost ||
                webinar.data.isPublic) {
                status = webinar_participants_args_1.WebinarParticipantStatus.Registered;
                webinarParticipant = await this.client.collections.update('webinar_participants', webinarParticipantDB.id, Object.assign(webinarParticipant.data, {
                    status,
                }));
            }
            if ((webinarParticipant === null || webinarParticipant === void 0 ? void 0 : webinarParticipant.data.status) ===
                webinar_participants_args_1.WebinarParticipantStatus.InvitedByParticipant) {
                status = webinar_participants_args_1.WebinarParticipantStatus.InvitedRegistered;
                webinarParticipant = await this.client.collections.update('webinar_participants', webinarParticipantDB.id, Object.assign(webinarParticipant.data, {
                    status,
                }));
            }
            const feedUser = this.client.feed('user', user.profileId);
            await feedUser.follow('webinars', id);
            const getActivities = await feedUser.get({ limit: 100 });
            for (const activity of getActivities.results) {
                if (activity.foreign_id == 'webinars:' + id) {
                    await this.client.activityPartialUpdate({
                        id: activity.id,
                        set: {
                            status,
                        },
                    });
                    break;
                }
            }
            return {
                id: webinarParticipantDB.id,
                status,
            };
        }
        catch (e) {
            const webinarParticipantDB = await this.webinarParticipantsService.findOne({
                profileId: user.profileId,
                webinarId: webinar.id,
            });
            const newStatus = webinar_participants_args_1.WebinarParticipantStatus.Registered;
            await this.client.collections.add('webinar_participants', webinarParticipantDB.id, {
                webinarId: webinar.id,
                profileId: user.profileId,
                organisationId: webinar.data.organisationId,
                status: newStatus,
            });
            const feedUser = this.client.feed('user', user.profileId);
            await feedUser.follow('webinars', id);
            const getActivities = await feedUser.get({ limit: 100 });
            for (const active of getActivities.results) {
                if (active.foreign_id == 'webinars:' + id) {
                    await this.client.activityPartialUpdate({
                        id: active.id,
                        set: {
                            status: newStatus,
                        },
                    });
                    break;
                }
            }
            return {
                id: webinarParticipantDB.id,
                status: newStatus,
            };
        }
    }
    async unregister(user, id, webinarParticipantId) {
        var _a;
        try {
            this.logger.verbose('StreamWebinarsService.unregister', {
                user: user.toLogObject(),
                webinarId: id,
            });
            let webinar;
            try {
                webinar = await this.client.collections.get('webinars', id);
            }
            catch (err) {
                throw new Error(`StreamWebinarsService.streamUnregisterToWebinar - Webinar does not exist.`);
            }
            let webinarParticipant;
            try {
                webinarParticipant = await this.client.collections.get('webinar_participants', webinarParticipantId);
            }
            catch (err) {
                throw new Error(`StreamWebinarsService.streamUnregisterToWebinar - webinarParticipant not exists`);
            }
            const feedUser = this.client.feed('user', user.profileId);
            await feedUser.unfollow('webinars', id);
            const getActivities = await feedUser.get({ limit: 100 });
            for (const activity of getActivities.results) {
                if (activity.foreign_id == 'webinars:' + id) {
                    await this.client.activityPartialUpdate({
                        id: activity.id,
                        set: {
                            status: null,
                        },
                    });
                    break;
                }
            }
            let status = webinarParticipant.data.status;
            if (!webinar.data.isPublic) {
                if (webinarParticipant.data.status === webinar_participants_args_1.WebinarParticipantStatus.Registered) {
                    status = webinar_participants_args_1.WebinarParticipantStatus.InvitedByHost;
                    return await this.client.collections.update('webinar_participants', webinarParticipantId, Object.assign(webinarParticipant.data, {
                        status,
                    }));
                }
                if (webinarParticipant.data.status ===
                    webinar_participants_args_1.WebinarParticipantStatus.InvitedRegistered) {
                    status = webinar_participants_args_1.WebinarParticipantStatus.InvitedByParticipant;
                    return await this.client.collections.update('webinar_participants', webinarParticipantId, Object.assign(webinarParticipant.data, {
                        status,
                    }));
                }
            }
            return {
                id: webinarParticipantId,
                status: null,
            };
        }
        catch (e) {
            this.logger.error(`WebinarsService.unregister Error: ${e.message}`, (_a = e.response) === null || _a === void 0 ? void 0 : _a.body);
        }
    }
};
exports.StreamWebinarsService = StreamWebinarsService;
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], StreamWebinarsService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], StreamWebinarsService.prototype, "membershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinars_service_1.WebinarsService)),
    __metadata("design:type", webinars_service_1.WebinarsService)
], StreamWebinarsService.prototype, "webinarsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinar_participants_service_1.WebinarParticipantsService)),
    __metadata("design:type", webinar_participants_service_1.WebinarParticipantsService)
], StreamWebinarsService.prototype, "webinarParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], StreamWebinarsService.prototype, "profilesService", void 0);
exports.StreamWebinarsService = StreamWebinarsService = __decorate([
    (0, common_1.Injectable)()
], StreamWebinarsService);
//# sourceMappingURL=stream-webinars.service.js.map