"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FollowersArgs = exports.FollowersFilter = void 0;
const graphql_1 = require("@nestjs/graphql");
const pagination_args_1 = require("../../common/args/pagination.args");
const follower_model_1 = require("../models/follower.model");
let FollowersFilter = class FollowersFilter {
};
exports.FollowersFilter = FollowersFilter;
__decorate([
    (0, graphql_1.Field)({
        nullable: true,
    }),
    __metadata("design:type", String)
], FollowersFilter.prototype, "profileId", void 0);
__decorate([
    (0, graphql_1.Field)({
        nullable: true,
    }),
    __metadata("design:type", String)
], FollowersFilter.prototype, "organisationId", void 0);
__decorate([
    (0, graphql_1.Field)(() => [follower_model_1.FollowerStatus], {
        nullable: true,
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], FollowersFilter.prototype, "status", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, {
        nullable: true,
        defaultValue: null,
    }),
    __metadata("design:type", Boolean)
], FollowersFilter.prototype, "includeActiveProfile", void 0);
exports.FollowersFilter = FollowersFilter = __decorate([
    (0, graphql_1.InputType)()
], FollowersFilter);
let FollowersArgs = class FollowersArgs extends pagination_args_1.PaginationArgs {
};
exports.FollowersArgs = FollowersArgs;
__decorate([
    (0, graphql_1.Field)(() => FollowersFilter, { nullable: true }),
    __metadata("design:type", FollowersFilter)
], FollowersArgs.prototype, "filter", void 0);
exports.FollowersArgs = FollowersArgs = __decorate([
    (0, graphql_1.ArgsType)()
], FollowersArgs);
//# sourceMappingURL=followers.args.js.map