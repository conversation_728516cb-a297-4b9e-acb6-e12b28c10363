"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FollowersRepository = void 0;
const common_1 = require("@nestjs/common");
const follower_model_1 = require("./models/follower.model");
const pagination_1 = require("../common/helpers/pagination");
const sequelize_1 = require("sequelize");
const profile_model_1 = require("../profiles/models/profile.model");
const membership_model_1 = require("../memberships/models/membership.model");
const organisation_model_1 = require("../organisations/models/organisation.model");
const followers_service_helper_1 = require("../followers/helpers/followers.service.helper");
let FollowersRepository = class FollowersRepository {
    async findFollowers(profileId, filter, pagination) {
        const inactiveOrganisationIds = await this.helper.getInactiveOrganisations();
        const extraQueryParams = {
            profileId: {
                [sequelize_1.Op.ne]: profileId,
            },
            organisationId: {
                [sequelize_1.Op.notIn]: inactiveOrganisationIds,
            },
        };
        if (filter === null || filter === void 0 ? void 0 : filter.includeActiveProfile) {
            delete extraQueryParams.profileId;
        }
        if (filter === null || filter === void 0 ? void 0 : filter.profileId) {
            extraQueryParams.profileId = filter.profileId;
        }
        if (filter === null || filter === void 0 ? void 0 : filter.organisationId) {
            extraQueryParams.organisationId = filter.organisationId;
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.status) && filter.status.length > 0) {
            extraQueryParams.status = {
                [sequelize_1.Op.in]: filter.status,
            };
        }
        return await new pagination_1.PaginationHelper().getPaginatedResults({
            model: follower_model_1.Follower,
            pagination,
            extraQueryParams,
            excludeIds: ['_deleted-user_'],
            includeParams: [
                {
                    as: 'profile',
                    model: profile_model_1.Profile,
                    attributes: [
                        'id',
                        'name',
                        'image',
                        'primaryMembershipId',
                        'lastOnlineAt',
                        'profileIdsConnections',
                        'profileIdsInvitationSent',
                        'profileIdsInvitationSentRejected',
                        'profileIdsInvitationReceived',
                        'profileIdsInvitationReceivedRejected',
                    ],
                    include: [
                        {
                            as: 'memberships',
                            model: membership_model_1.Membership,
                            attributes: [
                                'id',
                                'position',
                                'isPrimary',
                                'organisationName',
                                'organisationId',
                                'status',
                            ],
                            include: [
                                {
                                    as: 'organisation',
                                    model: organisation_model_1.Organisation,
                                    attributes: [
                                        'name',
                                        'id',
                                        'vanityId',
                                        'privacy',
                                        'additionalPrivacy',
                                        'followingPrivacy',
                                        'peoplePrivacy',
                                        'image',
                                    ],
                                },
                            ],
                        },
                    ],
                },
            ],
        });
    }
};
exports.FollowersRepository = FollowersRepository;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => followers_service_helper_1.FollowersServiceHelper)),
    __metadata("design:type", followers_service_helper_1.FollowersServiceHelper)
], FollowersRepository.prototype, "helper", void 0);
exports.FollowersRepository = FollowersRepository = __decorate([
    (0, common_1.Injectable)()
], FollowersRepository);
//# sourceMappingURL=followers.repository.js.map