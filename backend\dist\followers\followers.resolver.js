"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FollowersResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const follower_model_1 = require("./models/follower.model");
const profile_model_1 = require("../profiles/models/profile.model");
const profiles_service_1 = require("../profiles/profiles.service");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const followers_service_1 = require("./followers.service");
const memberships_service_rights_1 = require("../memberships/helpers/memberships.service.rights");
const organisation_model_1 = require("../organisations/models/organisation.model");
const organisations_service_1 = require("../organisations/organisations.service");
const memberships_service_1 = require("../memberships/memberships.service");
let FollowersResolver = class FollowersResolver {
    async follow(user, organisationId) {
        this.logger.verbose('FollowersResolver.follow (mutation)', {
            user: user.toLogObject(),
            organisationId,
        });
        return await this.followersService.follow(user, organisationId);
    }
    async unfollow(user, organisationId) {
        this.logger.verbose('FollowersResolver.unfollow (mutation)', {
            user: user.toLogObject(),
            organisationId,
        });
        return await this.followersService.unfollow(user, organisationId);
    }
    async updateFollowerStatus(user, organisationId, profileId, actionType) {
        this.logger.verbose('FollowersResolver.updateFollowerStatus (mutation)', {
            user: user.toLogObject(),
            organisationId,
            profileId,
            actionType,
        });
        await this.membershipsServiceRights.checkRights(organisationId, {
            currentProfileId: user.profileId,
            rights: [memberships_service_rights_1.MembershipRight.UpdateFollowerStatus],
        });
        return this.followersService.updateFollowerStatus(organisationId, profileId, actionType, {
            currentProfileId: user.profileId,
        });
    }
    async profile(follower) {
        if (follower.profile)
            return follower.profile;
        this.logger.verbose('FollowersResolver.profile (field resolver)', {
            followerId: follower.id,
        });
        return this.profilesService.findById(follower.profileId);
    }
    async organisation(follower) {
        if (!follower.organisationId)
            return null;
        if (follower.organisation)
            return follower.organisation;
        this.logger.verbose('MembershipsResolver.organisation (field resolver)', {
            followerId: follower.id,
        });
        return this.organisationsService.findById(follower.organisationId);
    }
};
exports.FollowersResolver = FollowersResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], FollowersResolver.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => followers_service_1.FollowersService)),
    __metadata("design:type", followers_service_1.FollowersService)
], FollowersResolver.prototype, "followersService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], FollowersResolver.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_rights_1.MembershipsServiceRights)),
    __metadata("design:type", memberships_service_rights_1.MembershipsServiceRights)
], FollowersResolver.prototype, "membershipsServiceRights", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], FollowersResolver.prototype, "membershipsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], FollowersResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], FollowersResolver.prototype, "follow", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], FollowersResolver.prototype, "unfollow", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId')),
    __param(2, (0, graphql_1.Args)('profileId')),
    __param(3, (0, graphql_1.Args)('actionType', { type: () => follower_model_1.FollowerActionType })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String]),
    __metadata("design:returntype", Promise)
], FollowersResolver.prototype, "updateFollowerStatus", null);
__decorate([
    (0, graphql_1.ResolveField)('profile', () => profile_model_1.Profile),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [follower_model_1.Follower]),
    __metadata("design:returntype", Promise)
], FollowersResolver.prototype, "profile", null);
__decorate([
    (0, graphql_1.ResolveField)('organisation', () => organisation_model_1.Organisation, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [follower_model_1.Follower]),
    __metadata("design:returntype", Promise)
], FollowersResolver.prototype, "organisation", null);
exports.FollowersResolver = FollowersResolver = __decorate([
    (0, graphql_1.Resolver)(() => follower_model_1.Follower)
], FollowersResolver);
//# sourceMappingURL=followers.resolver.js.map