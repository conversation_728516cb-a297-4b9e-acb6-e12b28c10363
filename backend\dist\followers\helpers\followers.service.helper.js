"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FollowersServiceHelper = void 0;
const sequelize_1 = require("sequelize");
const common_1 = require("@nestjs/common");
const organisations_service_1 = require("../../organisations/organisations.service");
const profiles_service_1 = require("../../profiles/profiles.service");
const underscore_1 = require("../../common/helpers/underscore");
const organisations_args_1 = require("../../organisations/args/organisations.args");
let FollowersServiceHelper = class FollowersServiceHelper {
    static getQueryParams(followerArgs) {
        var _a, _b, _c, _d;
        const queryParams = {};
        if ((_a = followerArgs === null || followerArgs === void 0 ? void 0 : followerArgs.filter) === null || _a === void 0 ? void 0 : _a.organisationId) {
            queryParams['organisationId'] = followerArgs.filter.organisationId;
        }
        if ((_b = followerArgs === null || followerArgs === void 0 ? void 0 : followerArgs.filter) === null || _b === void 0 ? void 0 : _b.profileId) {
            queryParams['profileId'] = followerArgs.filter.profileId;
        }
        if (((_d = (_c = followerArgs === null || followerArgs === void 0 ? void 0 : followerArgs.filter) === null || _c === void 0 ? void 0 : _c.status) === null || _d === void 0 ? void 0 : _d.length) > 0) {
            queryParams['status'] = {
                [sequelize_1.Op.or]: followerArgs.filter.status,
            };
        }
        return queryParams;
    }
    async getInactiveOrganisations() {
        const organisations = await this.organisationsService.findAll({
            status: {
                [sequelize_1.Op.notIn]: [organisations_args_1.OrganisationStatus.Active],
            },
        }, {
            attributes: ['id'],
        });
        return underscore_1.Underscore.map(organisations, 'id');
    }
};
exports.FollowersServiceHelper = FollowersServiceHelper;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], FollowersServiceHelper.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], FollowersServiceHelper.prototype, "profilesService", void 0);
exports.FollowersServiceHelper = FollowersServiceHelper = __decorate([
    (0, common_1.Injectable)()
], FollowersServiceHelper);
//# sourceMappingURL=followers.service.helper.js.map