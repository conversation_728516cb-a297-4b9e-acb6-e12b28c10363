"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Follower = exports.FollowerAction = exports.FollowerActionType = exports.FollowerStatus = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const graphql_1 = require("@nestjs/graphql");
const sequelize_1 = require("sequelize");
const short_uuid_1 = __importDefault(require("short-uuid"));
const profile_model_1 = require("../../profiles/models/profile.model");
const organisation_model_1 = require("../../organisations/models/organisation.model");
var FollowerStatus;
(function (FollowerStatus) {
    FollowerStatus["Active"] = "Active";
    FollowerStatus["Pending"] = "Pending";
    FollowerStatus["Rejected"] = "Rejected";
    FollowerStatus["Inactive"] = "Inactive";
})(FollowerStatus || (exports.FollowerStatus = FollowerStatus = {}));
(0, graphql_1.registerEnumType)(FollowerStatus, { name: 'FollowerStatus' });
var FollowerActionType;
(function (FollowerActionType) {
    FollowerActionType["Accept"] = "Accept";
    FollowerActionType["Reject"] = "Reject";
    FollowerActionType["Inactivate"] = "Inactivate";
})(FollowerActionType || (exports.FollowerActionType = FollowerActionType = {}));
(0, graphql_1.registerEnumType)(FollowerActionType, { name: 'FollowerActionType' });
let FollowerAction = class FollowerAction {
};
exports.FollowerAction = FollowerAction;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], FollowerAction.prototype, "profileId", void 0);
__decorate([
    (0, graphql_1.Field)(() => FollowerActionType),
    __metadata("design:type", String)
], FollowerAction.prototype, "actionType", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], FollowerAction.prototype, "actionDate", void 0);
exports.FollowerAction = FollowerAction = __decorate([
    (0, graphql_1.ObjectType)()
], FollowerAction);
let Follower = class Follower extends sequelize_typescript_1.Model {
};
exports.Follower = Follower;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], Follower.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(() => FollowerStatus, { nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Follower.prototype, "status", void 0);
__decorate([
    (0, graphql_1.Field)(() => [FollowerAction], { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.JSON),
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], Follower.prototype, "actions", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Follower.prototype, "profileId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, 'profileId'),
    __metadata("design:type", profile_model_1.Profile)
], Follower.prototype, "profile", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Follower.prototype, "organisationId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, 'organisationId'),
    __metadata("design:type", organisation_model_1.Organisation)
], Follower.prototype, "organisation", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Follower.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Follower.prototype, "updatedAt", void 0);
exports.Follower = Follower = __decorate([
    sequelize_typescript_1.Table,
    (0, graphql_1.ObjectType)()
], Follower);
//# sourceMappingURL=follower.model.js.map