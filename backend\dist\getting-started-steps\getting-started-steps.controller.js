"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamPostsController = void 0;
const common_1 = require("@nestjs/common");
const error_1 = require("../common/helpers/error");
const gcp_auth_guard_1 = require("../common/guards/gcp-auth.guard");
const config_1 = __importDefault(require("../config/config"));
const getting_started_steps_service_1 = require("./getting-started-steps.service");
let StreamPostsController = class StreamPostsController {
    runActivityDataMigration() {
        void this.gettingStartedStepsService.addMissingCommentStep();
        return;
    }
    runActivityDataMigrationBeta() {
        try {
            if (config_1.default.NODE_ENV === 'development' || config_1.default.SERVICE === 'api-beta') {
                void this.gettingStartedStepsService.addMissingCommentStep();
                return;
            }
            else {
                throw new Error('only beta env allowed');
            }
        }
        catch (err) {
            this.errorHelper.throwHttpException(`ActivitiesService.runJobBeta`, err.message);
        }
    }
};
exports.StreamPostsController = StreamPostsController;
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], StreamPostsController.prototype, "errorHelper", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => getting_started_steps_service_1.GettingStartedStepsService)),
    __metadata("design:type", getting_started_steps_service_1.GettingStartedStepsService)
], StreamPostsController.prototype, "gettingStartedStepsService", void 0);
__decorate([
    (0, common_1.UseGuards)(gcp_auth_guard_1.GcpAuthGuard),
    (0, common_1.Get)('run-addMissingCommentStep'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], StreamPostsController.prototype, "runActivityDataMigration", null);
__decorate([
    (0, common_1.Get)('run-beta-addMissingCommentStep'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], StreamPostsController.prototype, "runActivityDataMigrationBeta", null);
exports.StreamPostsController = StreamPostsController = __decorate([
    (0, common_1.Controller)('getting-started')
], StreamPostsController);
//# sourceMappingURL=getting-started-steps.controller.js.map