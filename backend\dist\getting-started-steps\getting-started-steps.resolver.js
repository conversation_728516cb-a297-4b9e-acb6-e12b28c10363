"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GettingStartedStepsResolver = void 0;
const getting_started_steps_args_1 = require("./args/getting-started-steps.args");
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const getting_started_steps_service_1 = require("./getting-started-steps.service");
const getting_started_step_model_1 = require("./models/getting-started-step.model");
let GettingStartedStepsResolver = class GettingStartedStepsResolver {
    async getGettingStartedSteps(user) {
        this.logger.verbose('GettingStartedStepsResolver.getGettingStartedSteps (query)');
        return await this.gettingStartedStepsService.getGettingStartedSteps({
            profileId: user.profileId,
        });
    }
    async gettingStartedStepsMigration(user) {
        this.logger.verbose('GettingStartedStepsResolver.gettingStartedStepsMigration (mutation)');
        return await this.gettingStartedStepsService.gettingStartedStepsMigration({
            profileId: user.profileId,
        });
    }
};
exports.GettingStartedStepsResolver = GettingStartedStepsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => getting_started_steps_service_1.GettingStartedStepsService)),
    __metadata("design:type", getting_started_steps_service_1.GettingStartedStepsService)
], GettingStartedStepsResolver.prototype, "gettingStartedStepsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], GettingStartedStepsResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Query)(() => [getting_started_steps_args_1.GettingStartedStepResponse]),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], GettingStartedStepsResolver.prototype, "getGettingStartedSteps", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], GettingStartedStepsResolver.prototype, "gettingStartedStepsMigration", null);
exports.GettingStartedStepsResolver = GettingStartedStepsResolver = __decorate([
    (0, graphql_1.Resolver)(() => getting_started_step_model_1.GettingStartedStep)
], GettingStartedStepsResolver);
//# sourceMappingURL=getting-started-steps.resolver.js.map