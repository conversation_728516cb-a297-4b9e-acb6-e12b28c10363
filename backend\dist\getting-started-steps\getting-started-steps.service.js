"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GettingStartedStepsService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const sequelize_1 = require("sequelize");
const sequelize_typescript_1 = require("sequelize-typescript");
const winston_1 = require("winston");
const chat_service_1 = require("../chat/chat.service");
const base_service_1 = require("../common/base.service");
const error_1 = require("../common/helpers/error");
const connections_service_1 = require("../connections/connections.service");
const stream_posts_service_1 = require("../feeds-posts/stream-posts.service");
const followers_service_1 = require("../followers/followers.service");
const profiles_service_1 = require("../profiles/profiles.service");
const getting_started_steps_args_1 = require("./args/getting-started-steps.args");
const getting_started_step_model_1 = require("./models/getting-started-step.model");
let GettingStartedStepsService = class GettingStartedStepsService extends (0, base_service_1.BaseService)(getting_started_step_model_1.GettingStartedStep) {
    async getGettingStartedSteps({ profileId, }) {
        this.logger.info('GettingStartedStepsService.getGettingStartedSteps', {
            profileId,
        });
        const gettingStartedSteps = [
            {
                title: 'sendChatMessage',
                status: 'wait',
            },
            {
                title: 'fillBasicDetails',
                status: 'wait',
            },
            {
                title: 'addWork',
                status: 'wait',
            },
            {
                title: 'postComment',
                status: 'wait',
            },
            {
                title: 'addProfilePic',
                status: 'wait',
            },
            {
                title: 'writeHeadlineBio',
                status: 'wait',
            },
            {
                title: 'connectWithPeople',
                status: 'wait',
            },
            {
                title: 'followOrganisations',
                status: 'wait',
            },
        ];
        const steps = await this.findAll({
            profileId,
        });
        const finishedSteps = [];
        const waitingSteps = [];
        for (const step of gettingStartedSteps) {
            const index = steps.findIndex(item => item.stepKey === step.title);
            if (index !== -1) {
                finishedSteps.push({
                    title: step.title,
                    status: 'finish',
                });
            }
            else {
                waitingSteps.push({
                    title: step.title,
                    status: 'wait',
                });
            }
        }
        return [...finishedSteps, ...waitingSteps];
    }
    async createGettingStartedStep({ step, profileId, transaction, }) {
        try {
            this.logger.info('GettingStartedStepsService.createGettingStartedStep', {
                step,
                profileId,
            });
            const existingStepEntry = await this.findOne({
                stepKey: step,
                profileId,
            }, {
                transaction,
            });
            if (!existingStepEntry) {
                await this.create({
                    stepKey: step,
                    profileId,
                }, {
                    transaction,
                });
            }
            return true;
        }
        catch (error) {
            this.errorHelper.throwHttpException(`GettingStartedStepsService.createGettingStartedStep`, error.message);
        }
    }
    async gettingStartedStepsMigration({ profileId, }) {
        this.logger.info('GettingStartedStepsService.gettingStartedStepsMigration', {
            profileId,
        });
        const completedSteps = await this.findOne({
            profileId,
        });
        if (completedSteps) {
            return true;
        }
        const transaction = await this.sequelize.transaction();
        try {
            const profile = await this.profilesService.findById(profileId, {
                transaction,
            });
            if (!profile) {
                this.logger.error('GettingStartedStepsService.gettingStartedMigration Profile not found', {
                    profileId,
                });
                this.errorHelper.throwHttpException('GettingStartedStepsService.gettingStartedMigration', 'Profile not found');
            }
            if (profile.isComplete) {
                await this.createGettingStartedStep({
                    step: getting_started_steps_args_1.GettingStartedStepEnum.FillBasicDetails,
                    profileId: profile.id,
                    transaction,
                });
            }
            if (profile.primaryMembershipId) {
                await this.createGettingStartedStep({
                    step: getting_started_steps_args_1.GettingStartedStepEnum.AddWork,
                    profileId: profile.id,
                    transaction,
                });
            }
            const totalConnections = await this.connectionsService.count({
                profileId: profile.id,
            }, {
                transaction,
            });
            if (totalConnections > 2) {
                await this.createGettingStartedStep({
                    step: getting_started_steps_args_1.GettingStartedStepEnum.ConnectWithPeople,
                    profileId: profile.id,
                });
            }
            await this.followersService.createOrganisationFollowGettingStartedRecord({
                profileId: profile.id,
                transaction,
            });
            if (profile.image && !profile.image.includes('gravatar.com')) {
                await this.createGettingStartedStep({
                    step: getting_started_steps_args_1.GettingStartedStepEnum.AddProfilePic,
                    profileId: profile.id,
                    transaction,
                });
            }
            if (profile.headline) {
                await this.createGettingStartedStep({
                    step: getting_started_steps_args_1.GettingStartedStepEnum.WriteHeadlineBio,
                    profileId: profile.id,
                    transaction,
                });
            }
            await this.chatService.migrateChatGettingStartedRecord({
                profileId: profile.id,
                transaction,
            });
            await this.streamPostsService.migratePostCommentGettingStartedRecord({
                profileId: profile.id,
                transaction,
            });
            await transaction.commit();
            return true;
        }
        catch (error) {
            await transaction.rollback();
            this.logger.error(`GettingStartedStepsService.gettingStartedMigration Error: ${error.message}`, error.message);
            this.errorHelper.throwHttpException(`GettingStartedStepsService.gettingStartedMigration`, error.message);
        }
    }
    async addMissingCommentStep() {
        this.logger.info('GettingStartedStepsService.addMissingCommentStep');
        const chunkSize = 100;
        const timeout = 10000;
        try {
            const profiles = await this.profilesService.findAll({
                id: {
                    [sequelize_1.Op.notIn]: sequelize_typescript_1.Sequelize.literal(`(SELECT "profileId" FROM "public"."GettingStartedSteps" WHERE "stepKey" = 'postComment')`),
                },
            }, {
                attributes: ['id'],
            });
            this.logger.info('GettingStartedStepsService.addMissingCommentStep found profiles', profiles.length);
            for (let i = 0; i < profiles.length; i += chunkSize) {
                const chunk = profiles.slice(i, i + chunkSize);
                const transaction = await this.sequelize.transaction();
                this.logger.info('GettingStartedStepsService.addMissingCommentStep processing chunk', i);
                try {
                    await Promise.all(chunk.map(async (profile) => {
                        await this.streamPostsService.migratePostCommentGettingStartedRecord({
                            profileId: profile.id,
                            transaction,
                        });
                    }));
                    await transaction.commit();
                }
                catch (error) {
                    await transaction.rollback();
                    this.logger.error(`Error processing chunk: ${error.message}`);
                }
                await new Promise(resolve => setTimeout(resolve, timeout));
            }
            return true;
        }
        catch (error) {
            this.logger.error(`GettingStartedStepsService.addMissingCommentStep Error: ${error.message}`);
            this.errorHelper.throwHttpException(`GettingStartedStepsService.addMissingCommentStep`, error.message);
            return false;
        }
    }
};
exports.GettingStartedStepsService = GettingStartedStepsService;
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", sequelize_typescript_1.Sequelize)
], GettingStartedStepsService.prototype, "sequelize", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], GettingStartedStepsService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], GettingStartedStepsService.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => connections_service_1.ConnectionsService)),
    __metadata("design:type", connections_service_1.ConnectionsService)
], GettingStartedStepsService.prototype, "connectionsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => followers_service_1.FollowersService)),
    __metadata("design:type", followers_service_1.FollowersService)
], GettingStartedStepsService.prototype, "followersService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => chat_service_1.ChatService)),
    __metadata("design:type", chat_service_1.ChatService)
], GettingStartedStepsService.prototype, "chatService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => stream_posts_service_1.StreamPostsService)),
    __metadata("design:type", stream_posts_service_1.StreamPostsService)
], GettingStartedStepsService.prototype, "streamPostsService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], GettingStartedStepsService.prototype, "errorHelper", void 0);
exports.GettingStartedStepsService = GettingStartedStepsService = __decorate([
    (0, common_1.Injectable)()
], GettingStartedStepsService);
//# sourceMappingURL=getting-started-steps.service.js.map