"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GettingStartedStep = void 0;
const graphql_1 = require("@nestjs/graphql");
const sequelize_typescript_1 = require("sequelize-typescript");
const short_uuid_1 = __importDefault(require("short-uuid"));
const profile_model_1 = require("../../profiles/models/profile.model");
let GettingStartedStep = class GettingStartedStep extends sequelize_typescript_1.Model {
};
exports.GettingStartedStep = GettingStartedStep;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], GettingStartedStep.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, {
        nullable: false,
    }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], GettingStartedStep.prototype, "stepKey", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean),
    (0, sequelize_typescript_1.Column)({
        defaultValue: true,
    }),
    __metadata("design:type", Boolean)
], GettingStartedStep.prototype, "value", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], GettingStartedStep.prototype, "profileId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, 'profileId'),
    __metadata("design:type", profile_model_1.Profile)
], GettingStartedStep.prototype, "profile", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], GettingStartedStep.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], GettingStartedStep.prototype, "updatedAt", void 0);
exports.GettingStartedStep = GettingStartedStep = __decorate([
    sequelize_typescript_1.Table,
    (0, graphql_1.ObjectType)()
], GettingStartedStep);
//# sourceMappingURL=getting-started-step.model.js.map