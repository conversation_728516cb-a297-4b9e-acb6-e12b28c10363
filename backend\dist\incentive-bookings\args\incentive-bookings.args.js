"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncentiveBookingsArgs = exports.IncentiveBookingsFilter = void 0;
const graphql_1 = require("@nestjs/graphql");
const pagination_args_1 = require("../../common/args/pagination.args");
let IncentiveBookingsFilter = class IncentiveBookingsFilter {
};
exports.IncentiveBookingsFilter = IncentiveBookingsFilter;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID, {
        nullable: true,
    }),
    __metadata("design:type", String)
], IncentiveBookingsFilter.prototype, "incentiveId", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID, {
        nullable: true,
    }),
    __metadata("design:type", String)
], IncentiveBookingsFilter.prototype, "profileId", void 0);
exports.IncentiveBookingsFilter = IncentiveBookingsFilter = __decorate([
    (0, graphql_1.InputType)()
], IncentiveBookingsFilter);
let IncentiveBookingsArgs = class IncentiveBookingsArgs extends pagination_args_1.PaginationArgs {
};
exports.IncentiveBookingsArgs = IncentiveBookingsArgs;
__decorate([
    (0, graphql_1.Field)(() => IncentiveBookingsFilter, { nullable: true }),
    __metadata("design:type", IncentiveBookingsFilter)
], IncentiveBookingsArgs.prototype, "filter", void 0);
exports.IncentiveBookingsArgs = IncentiveBookingsArgs = __decorate([
    (0, graphql_1.ArgsType)()
], IncentiveBookingsArgs);
//# sourceMappingURL=incentive-bookings.args.js.map