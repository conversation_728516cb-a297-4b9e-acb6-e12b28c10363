"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateIncentiveBookingInput = void 0;
const graphql_1 = require("@nestjs/graphql");
const update_incentive_booking_input_1 = require("./update-incentive-booking.input");
const class_validator_1 = require("class-validator");
const incentive_booking_model_1 = require("../models/incentive-booking.model");
let CreateIncentiveBookingInput = class CreateIncentiveBookingInput extends update_incentive_booking_input_1.UpdateIncentiveBookingInput {
};
exports.CreateIncentiveBookingInput = CreateIncentiveBookingInput;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(() => [incentive_booking_model_1.IncentiveBookingDataItemInput], { nullable: false }),
    __metadata("design:type", Array)
], CreateIncentiveBookingInput.prototype, "dataArray", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(() => graphql_1.ID, { nullable: false }),
    __metadata("design:type", String)
], CreateIncentiveBookingInput.prototype, "incentiveId", void 0);
exports.CreateIncentiveBookingInput = CreateIncentiveBookingInput = __decorate([
    (0, graphql_1.InputType)()
], CreateIncentiveBookingInput);
//# sourceMappingURL=create-incentive-booking.input.js.map