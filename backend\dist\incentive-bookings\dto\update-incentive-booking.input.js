"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateIncentiveBookingInput = void 0;
const class_validator_1 = require("class-validator");
const graphql_1 = require("@nestjs/graphql");
const incentive_booking_model_1 = require("../models/incentive-booking.model");
let UpdateIncentiveBookingInput = class UpdateIncentiveBookingInput {
};
exports.UpdateIncentiveBookingInput = UpdateIncentiveBookingInput;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)(() => [incentive_booking_model_1.IncentiveBookingDataItemInput], { nullable: true }),
    __metadata("design:type", Array)
], UpdateIncentiveBookingInput.prototype, "dataArray", void 0);
exports.UpdateIncentiveBookingInput = UpdateIncentiveBookingInput = __decorate([
    (0, graphql_1.InputType)()
], UpdateIncentiveBookingInput);
//# sourceMappingURL=update-incentive-booking.input.js.map