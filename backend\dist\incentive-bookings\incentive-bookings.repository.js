"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncentiveBookingsRepository = void 0;
const common_1 = require("@nestjs/common");
const pagination_1 = require("../common/helpers/pagination");
const incentive_booking_model_1 = require("./models/incentive-booking.model");
let IncentiveBookingsRepository = class IncentiveBookingsRepository {
    async findIncentiveBookings(profileId, filter, pagination) {
        const extraQueryParams = {};
        if (filter === null || filter === void 0 ? void 0 : filter.incentiveId) {
            extraQueryParams.incentiveId = filter.incentiveId;
        }
        if (filter === null || filter === void 0 ? void 0 : filter.profileId) {
            extraQueryParams.profileId = filter.profileId;
        }
        return await new pagination_1.PaginationHelper().getPaginatedResults({
            model: incentive_booking_model_1.IncentiveBooking,
            pagination,
            extraQueryParams,
            excludeIds: [],
        });
    }
};
exports.IncentiveBookingsRepository = IncentiveBookingsRepository;
exports.IncentiveBookingsRepository = IncentiveBookingsRepository = __decorate([
    (0, common_1.Injectable)()
], IncentiveBookingsRepository);
//# sourceMappingURL=incentive-bookings.repository.js.map