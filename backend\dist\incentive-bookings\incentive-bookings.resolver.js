"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncentiveBookingsResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const incentives_service_1 = require("../incentives/incentives.service");
const incentive_model_1 = require("../incentives/models/incentive.model");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const profile_model_1 = require("../profiles/models/profile.model");
const profiles_service_1 = require("../profiles/profiles.service");
const paginated_result_1 = require("../common/args/paginated-result");
const incentive_bookings_args_1 = require("./args/incentive-bookings.args");
const incentive_bookings_service_1 = require("./incentive-bookings.service");
const incentive_booking_model_1 = require("./models/incentive-booking.model");
const create_incentive_booking_input_1 = require("./dto/create-incentive-booking.input");
const update_incentive_booking_input_1 = require("./dto/update-incentive-booking.input");
const error_1 = require("../common/helpers/error");
let IncentiveBookingsResolver = class IncentiveBookingsResolver {
    createIncentiveBooking(user, incentiveBookingData) {
        this.logger.verbose('IncentiveBookingsResolver.createIncentiveBooking (mutation)', {
            user: user.toLogObject(),
            incentiveBookingData,
        });
        return this.incentiveBookingsService.createIncentiveBooking({
            incentiveBookingData,
            profileId: user.profileId,
        });
    }
    async updateIncentiveBooking(user, incentiveBookingId, incentiveBookingData) {
        this.logger.verbose('IncentiveBookingsResolver.updateIncentiveBooking (mutation)', {
            user: user.toLogObject(),
            incentiveBookingData,
        });
        const incentiveBooking = await this.incentiveBookingsService.findById(incentiveBookingId);
        if (incentiveBooking.profileId !== user.profileId) {
            this.errorHelper.throwHttpException(`IncentiveBookingsResolver.updateIncentiveBooking`, `Only owned incentive booking can be modified`);
        }
        return this.incentiveBookingsService.updateById(incentiveBookingId, incentiveBookingData);
    }
    async removeIncentiveBooking(user, incentiveBookingId) {
        this.logger.verbose('IncentiveBookingsResolver.removeIncentiveBooking (mutation)', {
            user: user.toLogObject(),
            incentiveBookingId,
        });
        const incentiveBooking = await this.incentiveBookingsService.findById(incentiveBookingId);
        if (incentiveBooking.profileId !== user.profileId) {
            this.errorHelper.throwHttpException(`IncentiveBookingsResolver.updateIncentiveBooking`, `Only owned incentive booking can be modified`);
        }
        await this.incentiveBookingsService.removeById(incentiveBookingId);
        return true;
    }
    async incentiveBooking(user, id) {
        this.logger.verbose('IncentiveBookingsResolver.incentiveBooking (query)', {
            user: user.toLogObject(),
            id,
        });
        const incentiveBooking = await this.incentiveBookingsService.findById(id);
        if (!incentiveBooking) {
            throw new Error(`IncentiveBooking not found`);
        }
        return incentiveBooking;
    }
    incentiveBookings(user, incentiveBookingArgs) {
        var _a, _b;
        this.logger.verbose('IncentiveBookingsResolver.incentives (query)', {
            user: user.toLogObject(),
            incentiveBookingArgs,
        });
        return this.incentiveBookingsService.findIncentiveBookings(user.profileId, {
            profileId: (_a = incentiveBookingArgs === null || incentiveBookingArgs === void 0 ? void 0 : incentiveBookingArgs.filter) === null || _a === void 0 ? void 0 : _a.profileId,
            incentiveId: (_b = incentiveBookingArgs === null || incentiveBookingArgs === void 0 ? void 0 : incentiveBookingArgs.filter) === null || _b === void 0 ? void 0 : _b.incentiveId,
        }, {
            first: incentiveBookingArgs === null || incentiveBookingArgs === void 0 ? void 0 : incentiveBookingArgs.first,
            after: incentiveBookingArgs === null || incentiveBookingArgs === void 0 ? void 0 : incentiveBookingArgs.after,
            sortBy: incentiveBookingArgs === null || incentiveBookingArgs === void 0 ? void 0 : incentiveBookingArgs.sortBy,
            sortOrder: incentiveBookingArgs === null || incentiveBookingArgs === void 0 ? void 0 : incentiveBookingArgs.sortOrder,
        });
    }
    incentive(incentiveBooking) {
        this.logger.verbose('IncentiveBookingsResolver.incentive (field resolver)', {
            incentiveBookingId: incentiveBooking.id,
        });
        return this.incentivesService.findById(incentiveBooking.incentiveId, {
            useCache: true,
        });
    }
    profile(incentiveBooking) {
        this.logger.verbose('IncentiveBookingsResolver.profile (field resolver)', {
            incentiveBookingId: incentiveBooking.id,
        });
        return this.profilesService.findById(incentiveBooking.profileId);
    }
};
exports.IncentiveBookingsResolver = IncentiveBookingsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentive_bookings_service_1.IncentiveBookingsService)),
    __metadata("design:type", incentive_bookings_service_1.IncentiveBookingsService)
], IncentiveBookingsResolver.prototype, "incentiveBookingsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentives_service_1.IncentivesService)),
    __metadata("design:type", incentives_service_1.IncentivesService)
], IncentiveBookingsResolver.prototype, "incentivesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], IncentiveBookingsResolver.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], IncentiveBookingsResolver.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], IncentiveBookingsResolver.prototype, "errorHelper", void 0);
__decorate([
    (0, graphql_1.Mutation)(() => incentive_booking_model_1.IncentiveBooking),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('incentiveBookingData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_incentive_booking_input_1.CreateIncentiveBookingInput]),
    __metadata("design:returntype", Promise)
], IncentiveBookingsResolver.prototype, "createIncentiveBooking", null);
__decorate([
    (0, graphql_1.Mutation)(() => incentive_booking_model_1.IncentiveBooking),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('incentiveBookingId')),
    __param(2, (0, graphql_1.Args)('incentiveBookingData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, update_incentive_booking_input_1.UpdateIncentiveBookingInput]),
    __metadata("design:returntype", Promise)
], IncentiveBookingsResolver.prototype, "updateIncentiveBooking", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('incentiveBookingId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], IncentiveBookingsResolver.prototype, "removeIncentiveBooking", null);
__decorate([
    (0, graphql_1.Query)(() => incentive_booking_model_1.IncentiveBooking),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id', { nullable: false })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], IncentiveBookingsResolver.prototype, "incentiveBooking", null);
__decorate([
    (0, graphql_1.Query)(() => paginated_result_1.IncentiveBookingsResult),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, incentive_bookings_args_1.IncentiveBookingsArgs]),
    __metadata("design:returntype", Promise)
], IncentiveBookingsResolver.prototype, "incentiveBookings", null);
__decorate([
    (0, graphql_1.ResolveField)('incentive', () => incentive_model_1.Incentive),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [incentive_booking_model_1.IncentiveBooking]),
    __metadata("design:returntype", Promise)
], IncentiveBookingsResolver.prototype, "incentive", null);
__decorate([
    (0, graphql_1.ResolveField)('profile', () => profile_model_1.Profile),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [incentive_booking_model_1.IncentiveBooking]),
    __metadata("design:returntype", Promise)
], IncentiveBookingsResolver.prototype, "profile", null);
exports.IncentiveBookingsResolver = IncentiveBookingsResolver = __decorate([
    (0, graphql_1.Resolver)(() => incentive_booking_model_1.IncentiveBooking)
], IncentiveBookingsResolver);
//# sourceMappingURL=incentive-bookings.resolver.js.map