"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncentiveBookingsService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const activities_service_1 = require("../activities/activities.service");
const activities_args_1 = require("../activities/args/activities.args");
const create_user_activity_data_dto_1 = require("../activities/dto/create-user-activity-data.dto");
const base_service_1 = require("../common/base.service");
const incentive_bookings_repository_1 = require("./incentive-bookings.repository");
const incentive_booking_model_1 = require("./models/incentive-booking.model");
const incentives_service_1 = require("../incentives/incentives.service");
const achievements_service_1 = require("../achievements/achievements.service");
const organisations_service_1 = require("../organisations/organisations.service");
let IncentiveBookingsService = class IncentiveBookingsService extends (0, base_service_1.BaseService)(incentive_booking_model_1.IncentiveBooking) {
    async createIncentiveBooking({ incentiveBookingData, profileId, }) {
        this.logger.verbose('IncentiveBookingsResolver.createIncentiveBooking', {
            incentiveBookingData,
            profileId,
        });
        const newIncentiveBooking = await this.create(Object.assign(Object.assign({}, incentiveBookingData), { profileId }));
        const incentive = await this.incentivesService.findById(newIncentiveBooking.incentiveId, {
            includeParams: ['organisation'],
        });
        if (!incentive) {
            throw new Error('Incentive not found');
        }
        if (incentive.name.includes('Hablo Daily Quiz') &&
            incentive.organisation.vanityId.toLowerCase() === 'hablo') {
            await this.achievementsService.addDailyQuizStreakAchievement({
                profileId,
            });
        }
        const organisation = await this.organisationsService.findById(incentive.organisationId);
        const activities = await this.activitiesService.findOne({
            type: activities_args_1.ActivityType.IncentiveBooking,
            profileId,
            organisationId: incentive.organisationId,
            'data.incentiveId': incentive.id,
        });
        if (!activities) {
            await this.achievementsService.addIncentiveWarriorAchievement({
                profileId,
                organisationId: incentive.organisationId,
            });
        }
        await this.activitiesService.createUserActivity({
            profileId,
            type: activities_args_1.ActivityType.IncentiveBooking,
            schema: create_user_activity_data_dto_1.incentiveBookingActivityDataDto,
            data: {
                profileId,
                incentiveId: incentive.id,
            },
            addLoyaltyPoints: true,
            organisationId: incentive.organisationId,
            placeholders: {
                id: incentive.id,
                userId: profileId,
                name: incentive.name,
                vanityId: organisation.vanityId,
            },
        });
        return newIncentiveBooking;
    }
    async findIncentiveBookings(profileId, filter, pagination) {
        this.logger.verbose('IncentiveBookingsService.findIncentiveBookings', {
            profileId,
            filter,
            pagination,
        });
        const paginationIncentiveBookingResult = await this.incentiveBookingsRepository.findIncentiveBookings(profileId, filter, pagination);
        return {
            records: paginationIncentiveBookingResult.records,
            totalCount: paginationIncentiveBookingResult.totalCount,
        };
    }
};
exports.IncentiveBookingsService = IncentiveBookingsService;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentive_bookings_repository_1.IncentiveBookingsRepository)),
    __metadata("design:type", incentive_bookings_repository_1.IncentiveBookingsRepository)
], IncentiveBookingsService.prototype, "incentiveBookingsRepository", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => activities_service_1.ActivitiesService)),
    __metadata("design:type", activities_service_1.ActivitiesService)
], IncentiveBookingsService.prototype, "activitiesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentives_service_1.IncentivesService)),
    __metadata("design:type", incentives_service_1.IncentivesService)
], IncentiveBookingsService.prototype, "incentivesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => achievements_service_1.AchievementsService)),
    __metadata("design:type", achievements_service_1.AchievementsService)
], IncentiveBookingsService.prototype, "achievementsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], IncentiveBookingsService.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], IncentiveBookingsService.prototype, "logger", void 0);
exports.IncentiveBookingsService = IncentiveBookingsService = __decorate([
    (0, common_1.Injectable)()
], IncentiveBookingsService);
//# sourceMappingURL=incentive-bookings.service.js.map