"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncentiveBooking = exports.IncentiveBookingDataItemInput = exports.IncentiveBookingDataItem = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const graphql_1 = require("@nestjs/graphql");
const sequelize_1 = require("sequelize");
const short_uuid_1 = __importDefault(require("short-uuid"));
const incentive_model_1 = require("../../incentives/models/incentive.model");
const profile_model_1 = require("../../profiles/models/profile.model");
const incentives_args_1 = require("../../incentives/args/incentives.args");
let IncentiveBookingDataItem = class IncentiveBookingDataItem {
};
exports.IncentiveBookingDataItem = IncentiveBookingDataItem;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], IncentiveBookingDataItem.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], IncentiveBookingDataItem.prototype, "value", void 0);
__decorate([
    (0, graphql_1.Field)(() => incentives_args_1.IncentiveBookingFieldType),
    __metadata("design:type", String)
], IncentiveBookingDataItem.prototype, "type", void 0);
exports.IncentiveBookingDataItem = IncentiveBookingDataItem = __decorate([
    (0, graphql_1.ObjectType)()
], IncentiveBookingDataItem);
let IncentiveBookingDataItemInput = class IncentiveBookingDataItemInput {
};
exports.IncentiveBookingDataItemInput = IncentiveBookingDataItemInput;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], IncentiveBookingDataItemInput.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], IncentiveBookingDataItemInput.prototype, "value", void 0);
__decorate([
    (0, graphql_1.Field)(() => incentives_args_1.IncentiveBookingFieldType),
    __metadata("design:type", String)
], IncentiveBookingDataItemInput.prototype, "type", void 0);
exports.IncentiveBookingDataItemInput = IncentiveBookingDataItemInput = __decorate([
    (0, graphql_1.InputType)()
], IncentiveBookingDataItemInput);
let IncentiveBooking = class IncentiveBooking extends sequelize_typescript_1.Model {
};
exports.IncentiveBooking = IncentiveBooking;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], IncentiveBooking.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(() => [IncentiveBookingDataItem], { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.JSON),
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], IncentiveBooking.prototype, "dataArray", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => incentive_model_1.Incentive),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], IncentiveBooking.prototype, "incentiveId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => incentive_model_1.Incentive, {
        foreignKey: 'incentiveId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", incentive_model_1.Incentive)
], IncentiveBooking.prototype, "incentive", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], IncentiveBooking.prototype, "profileId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, {
        foreignKey: 'profileId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", profile_model_1.Profile)
], IncentiveBooking.prototype, "profile", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], IncentiveBooking.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], IncentiveBooking.prototype, "updatedAt", void 0);
exports.IncentiveBooking = IncentiveBooking = __decorate([
    sequelize_typescript_1.Table,
    (0, graphql_1.ObjectType)()
], IncentiveBooking);
//# sourceMappingURL=incentive-booking.model.js.map