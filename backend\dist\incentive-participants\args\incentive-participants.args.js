"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncentiveParticipantsArgs = exports.IncentiveParticipantsFilter = exports.IncentiveInvitationStatus = exports.IncentiveParticipantStatus = void 0;
const graphql_1 = require("@nestjs/graphql");
var IncentiveParticipantStatus;
(function (IncentiveParticipantStatus) {
    IncentiveParticipantStatus["Registered"] = "Registered";
    IncentiveParticipantStatus["InvitedByHost"] = "InvitedByHost";
    IncentiveParticipantStatus["InvitedByParticipant"] = "InvitedByParticipant";
    IncentiveParticipantStatus["InvitedRegistered"] = "InvitedRegistered";
    IncentiveParticipantStatus["Blocked"] = "Blocked";
})(IncentiveParticipantStatus || (exports.IncentiveParticipantStatus = IncentiveParticipantStatus = {}));
(0, graphql_1.registerEnumType)(IncentiveParticipantStatus, {
    name: 'IncentiveParticipantStatus',
});
var IncentiveInvitationStatus;
(function (IncentiveInvitationStatus) {
    IncentiveInvitationStatus["Pending"] = "Pending";
    IncentiveInvitationStatus["Sent"] = "Sent";
})(IncentiveInvitationStatus || (exports.IncentiveInvitationStatus = IncentiveInvitationStatus = {}));
(0, graphql_1.registerEnumType)(IncentiveInvitationStatus, {
    name: 'IncentiveInvitationStatus',
});
let IncentiveParticipantsFilter = class IncentiveParticipantsFilter {
};
exports.IncentiveParticipantsFilter = IncentiveParticipantsFilter;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID, {
        nullable: true,
    }),
    __metadata("design:type", String)
], IncentiveParticipantsFilter.prototype, "profileId", void 0);
__decorate([
    (0, graphql_1.Field)(() => [IncentiveParticipantStatus], {
        nullable: true,
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], IncentiveParticipantsFilter.prototype, "status", void 0);
exports.IncentiveParticipantsFilter = IncentiveParticipantsFilter = __decorate([
    (0, graphql_1.InputType)()
], IncentiveParticipantsFilter);
let IncentiveParticipantsArgs = class IncentiveParticipantsArgs {
};
exports.IncentiveParticipantsArgs = IncentiveParticipantsArgs;
__decorate([
    (0, graphql_1.Field)(() => IncentiveParticipantsFilter, { nullable: true }),
    __metadata("design:type", IncentiveParticipantsFilter)
], IncentiveParticipantsArgs.prototype, "filter", void 0);
exports.IncentiveParticipantsArgs = IncentiveParticipantsArgs = __decorate([
    (0, graphql_1.ArgsType)()
], IncentiveParticipantsArgs);
//# sourceMappingURL=incentive-participants.args.js.map