"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncentiveParticipantsServiceHelper = void 0;
const sequelize_1 = require("sequelize");
const common_1 = require("@nestjs/common");
let IncentiveParticipantsServiceHelper = class IncentiveParticipantsServiceHelper {
    static getQueryParams(incentiveParticipantArgs) {
        var _a, _b, _c, _d;
        const queryParams = {};
        if ((_a = incentiveParticipantArgs === null || incentiveParticipantArgs === void 0 ? void 0 : incentiveParticipantArgs.filter) === null || _a === void 0 ? void 0 : _a.organisationId) {
            queryParams['organisationId'] =
                incentiveParticipantArgs.filter.organisationId;
        }
        if ((_b = incentiveParticipantArgs === null || incentiveParticipantArgs === void 0 ? void 0 : incentiveParticipantArgs.filter) === null || _b === void 0 ? void 0 : _b.profileId) {
            queryParams['profileId'] = incentiveParticipantArgs.filter.profileId;
        }
        if (((_d = (_c = incentiveParticipantArgs === null || incentiveParticipantArgs === void 0 ? void 0 : incentiveParticipantArgs.filter) === null || _c === void 0 ? void 0 : _c.status) === null || _d === void 0 ? void 0 : _d.length) > 0) {
            queryParams['status'] = {
                [sequelize_1.Op.or]: incentiveParticipantArgs.filter.status,
            };
        }
        return queryParams;
    }
};
exports.IncentiveParticipantsServiceHelper = IncentiveParticipantsServiceHelper;
exports.IncentiveParticipantsServiceHelper = IncentiveParticipantsServiceHelper = __decorate([
    (0, common_1.Injectable)()
], IncentiveParticipantsServiceHelper);
//# sourceMappingURL=incentive-participants.service.helper.js.map