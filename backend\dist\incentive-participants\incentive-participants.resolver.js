"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncentiveParticipantsResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const incentive_participant_model_1 = require("./models/incentive-participant.model");
const incentives_service_1 = require("../incentives/incentives.service");
const incentive_model_1 = require("../incentives/models/incentive.model");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const incentive_participants_service_1 = require("./incentive-participants.service");
const memberships_service_rights_1 = require("../memberships/helpers/memberships.service.rights");
const error_1 = require("../common/helpers/error");
const organisation_model_1 = require("../organisations/models/organisation.model");
const organisations_service_1 = require("../organisations/organisations.service");
const profile_model_1 = require("../profiles/models/profile.model");
const profiles_service_1 = require("../profiles/profiles.service");
const incentive_participants_args_1 = require("./args/incentive-participants.args");
const create_incentive_participants_input_1 = require("./dto/create-incentive-participants.input");
const sequelize_1 = require("sequelize");
const activities_service_1 = require("../activities/activities.service");
const activities_args_1 = require("../activities/args/activities.args");
const organisation_loyalty_points_service_1 = require("../organisation-loyalty-points/organisation-loyalty-points.service");
const memberships_service_1 = require("../memberships/memberships.service");
const membership_model_1 = require("../memberships/models/membership.model");
let IncentiveParticipantsResolver = class IncentiveParticipantsResolver {
    async incentiveParticipant(user, id) {
        this.logger.verbose('IncentiveParticipantsResolver.incentiveParticipant (query)', {
            user: user.toLogObject(),
            id,
        });
        const incentiveParticipant = await this.incentiveParticipantsService.findById(id);
        if (!incentiveParticipant) {
            throw new Error(`IncentiveParticipant not found`);
        }
        return incentiveParticipant;
    }
    async inviteToIncentive(user, incentiveParticipantsData) {
        this.logger.verbose('IncentiveParticipantsResolver.inviteToIncentive (mutation)', {
            user: user.toLogObject(),
            incentiveId: incentiveParticipantsData.incentiveId,
            profileIds: incentiveParticipantsData.profileIds,
        });
        const incentive = await this.incentivesService.findById(incentiveParticipantsData.incentiveId);
        if (!incentive) {
            this.errorHelper.throwHttpException(`IncentiveParticipantsResolver.inviteToIncentive`, 'Incentive not found');
        }
        const incentiveParticipantStatus = await this.incentiveParticipantsService.verifyInvitePermission(user, incentive);
        if (incentiveParticipantStatus ===
            incentive_participants_args_1.IncentiveParticipantStatus.InvitedByParticipant) {
            let countInvites = await this.incentiveParticipantsService.count({
                incentiveId: incentiveParticipantsData.incentiveId,
                inviterProfileId: user.profileId,
                createdAt: new Date(),
            });
            if (countInvites >= 50) {
                this.errorHelper.throwHttpException(`IncentiveParticipantsResolver.inviteToIncentive`, 'Max participant invite limit reached.');
            }
        }
        const participants = await this.incentiveParticipantsService.createIncentiveParticipants({
            profileIds: incentiveParticipantsData.profileIds,
            inviterProfileId: user.profileId,
            incentiveId: incentive.id,
            organisationId: incentive.organisationId,
            status: incentiveParticipantStatus,
        }, user, {});
        const invitedByMembership = await this.membershipsService.findOne({
            profileId: user.profileId,
            organisationId: incentive.organisationId,
        });
        if (invitedByMembership === null || invitedByMembership === void 0 ? void 0 : invitedByMembership.permissions.some(permission => [
            membership_model_1.MembershipPermission.Owner,
            membership_model_1.MembershipPermission.Admin,
            membership_model_1.MembershipPermission.HiddenAdmin,
            membership_model_1.MembershipPermission.Manager,
            membership_model_1.MembershipPermission.Editor,
        ].includes(permission))) {
            const inviterProfile = await this.profilesService.findById(user.profileId);
            await this.organisationLoyaltyPointsService.addPoints({
                type: activities_args_1.ActivityType.UserInvitedToIncentive,
                organisationId: incentive.organisationId,
                placeholders: {
                    invitedProfileId: incentiveParticipantsData.profileIds[0],
                    createdById: inviterProfile.id,
                    createdByName: inviterProfile.name,
                    incentiveId: incentive.id,
                    incentiveName: incentive.name,
                },
            });
        }
        const orgActivityData = {
            invitedById: user.profileId,
            invitedProfileId: incentiveParticipantsData.profileIds[0],
            organisationId: incentive.organisationId,
            incentiveId: incentive.id,
            type: 'UserInvitedToIncentive',
            data: {
                incentiveId: incentive.id,
                invitedProfileId: incentiveParticipantsData.profileIds[0],
            },
            createdById: user.profileId,
        };
        await this.activitiesService.createOrganisationActivity(orgActivityData);
        return participants;
    }
    async removeIncentiveParticipant(user, id) {
        this.logger.verbose('IncentiveParticipantsResolver.removeIncentiveParticipant (mutation)', {
            user: user.toLogObject(),
            id,
        });
        const incentiveParticipant = await this.incentiveParticipantsService.findById(id, {
            includeParams: [
                {
                    model: incentive_model_1.Incentive,
                    as: 'incentive',
                    attributes: ['organisationId'],
                },
            ],
        });
        if (!incentiveParticipant) {
            this.errorHelper.throwHttpException(`IncentiveParticipantsResolver.removeIncentiveParticipant`, 'Incentive Participant not found');
        }
        await this.membershipsServiceRights.checkRights(incentiveParticipant.incentive.organisationId, {
            currentProfileId: user.profileId,
            rights: [memberships_service_rights_1.MembershipRight.RemoveIncentiveParticipant],
        });
        await this.incentiveParticipantsService.remove({ where: { id } });
        return true;
    }
    updateIncentiveParticipantStatus(user, id, status) {
        this.logger.verbose('IncentiveParticipantsResolver.updateIncentiveParticipantStatus (mutation)', {
            user: user.toLogObject(),
            id,
        });
        return this.incentiveParticipantsService.updateIncentiveParticipantStatus(user, id, status);
    }
    incentive(incentiveParticipant) {
        this.logger.verbose('IncentiveParticipantsResolver.incentive (field resolver)', {
            incentiveParticipantId: incentiveParticipant.id,
        });
        return this.incentivesService.findById(incentiveParticipant.incentiveId, {
            useCache: true,
        });
    }
    async organisation(incentiveParticipant) {
        if (incentiveParticipant.organisation)
            return incentiveParticipant.organisation;
        this.logger.verbose('IncentiveParticipantsResolver.organisation (field resolver)', {
            incentiveParticipantId: incentiveParticipant.id,
        });
        return this.organisationsService.findById(incentiveParticipant.organisationId, {
            useCache: true,
        });
    }
    async profile(incentiveParticipant) {
        if (incentiveParticipant.profile)
            return incentiveParticipant.profile;
        this.logger.verbose('IncentiveParticipantsResolver.profile (field resolver)', {
            incentiveParticipantId: incentiveParticipant.id,
        });
        return this.profilesService.findById(incentiveParticipant.profileId);
    }
    async inviterProfile(incentiveParticipant) {
        if (incentiveParticipant.inviterProfile)
            return incentiveParticipant.inviterProfile;
        this.logger.verbose('IncentiveParticipantsResolver.inviterProfile (field resolver)', {
            incentiveParticipantId: incentiveParticipant.id,
        });
        return this.profilesService.findById(incentiveParticipant.inviterProfileId);
    }
    async incentiveShareCount(user, incentiveId) {
        this.logger.verbose('IncentiveParticipantsResolver.incentiveShareCount (mutation)', {
            user: user.toLogObject(),
            incentiveId,
        });
        return await this.incentiveParticipantsService.count({
            [sequelize_1.Op.and]: [
                sequelize_1.Sequelize.where(sequelize_1.Sequelize.fn('DATE', sequelize_1.Sequelize.col('createdAt')), sequelize_1.Sequelize.literal('CURRENT_DATE')),
                {
                    incentiveId,
                    inviterProfileId: user.profileId,
                },
            ],
        });
    }
};
exports.IncentiveParticipantsResolver = IncentiveParticipantsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentive_participants_service_1.IncentiveParticipantsService)),
    __metadata("design:type", incentive_participants_service_1.IncentiveParticipantsService)
], IncentiveParticipantsResolver.prototype, "incentiveParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentives_service_1.IncentivesService)),
    __metadata("design:type", incentives_service_1.IncentivesService)
], IncentiveParticipantsResolver.prototype, "incentivesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_rights_1.MembershipsServiceRights)),
    __metadata("design:type", memberships_service_rights_1.MembershipsServiceRights)
], IncentiveParticipantsResolver.prototype, "membershipsServiceRights", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], IncentiveParticipantsResolver.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], IncentiveParticipantsResolver.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], IncentiveParticipantsResolver.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], IncentiveParticipantsResolver.prototype, "errorHelper", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => activities_service_1.ActivitiesService)),
    __metadata("design:type", activities_service_1.ActivitiesService)
], IncentiveParticipantsResolver.prototype, "activitiesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisation_loyalty_points_service_1.OrganisationLoyaltyPointsService)),
    __metadata("design:type", organisation_loyalty_points_service_1.OrganisationLoyaltyPointsService)
], IncentiveParticipantsResolver.prototype, "organisationLoyaltyPointsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], IncentiveParticipantsResolver.prototype, "membershipsService", void 0);
__decorate([
    (0, graphql_1.Query)(() => incentive_participant_model_1.IncentiveParticipant),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id', { nullable: false })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], IncentiveParticipantsResolver.prototype, "incentiveParticipant", null);
__decorate([
    (0, graphql_1.Mutation)(() => [incentive_participant_model_1.IncentiveParticipant]),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('incentiveParticipantsData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_incentive_participants_input_1.CreateIncentiveParticipantsInput]),
    __metadata("design:returntype", Promise)
], IncentiveParticipantsResolver.prototype, "inviteToIncentive", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], IncentiveParticipantsResolver.prototype, "removeIncentiveParticipant", null);
__decorate([
    (0, graphql_1.Mutation)(() => incentive_participant_model_1.IncentiveParticipant),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id', { nullable: false })),
    __param(2, (0, graphql_1.Args)('status', { type: () => incentive_participants_args_1.IncentiveParticipantStatus, nullable: false })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], IncentiveParticipantsResolver.prototype, "updateIncentiveParticipantStatus", null);
__decorate([
    (0, graphql_1.ResolveField)('incentive', () => incentive_model_1.Incentive),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [incentive_participant_model_1.IncentiveParticipant]),
    __metadata("design:returntype", Promise)
], IncentiveParticipantsResolver.prototype, "incentive", null);
__decorate([
    (0, graphql_1.ResolveField)('organisation', () => organisation_model_1.Organisation, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [incentive_participant_model_1.IncentiveParticipant]),
    __metadata("design:returntype", Promise)
], IncentiveParticipantsResolver.prototype, "organisation", null);
__decorate([
    (0, graphql_1.ResolveField)('profile', () => profile_model_1.Profile),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [incentive_participant_model_1.IncentiveParticipant]),
    __metadata("design:returntype", Promise)
], IncentiveParticipantsResolver.prototype, "profile", null);
__decorate([
    (0, graphql_1.ResolveField)('inviterProfile', () => profile_model_1.Profile, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [incentive_participant_model_1.IncentiveParticipant]),
    __metadata("design:returntype", Promise)
], IncentiveParticipantsResolver.prototype, "inviterProfile", null);
__decorate([
    (0, graphql_1.Query)(() => Number),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('incentiveId', { nullable: false })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], IncentiveParticipantsResolver.prototype, "incentiveShareCount", null);
exports.IncentiveParticipantsResolver = IncentiveParticipantsResolver = __decorate([
    (0, graphql_1.Resolver)(() => incentive_participant_model_1.IncentiveParticipant)
], IncentiveParticipantsResolver);
//# sourceMappingURL=incentive-participants.resolver.js.map