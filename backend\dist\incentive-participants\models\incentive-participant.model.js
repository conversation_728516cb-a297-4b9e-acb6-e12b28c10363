"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncentiveParticipant = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const graphql_1 = require("@nestjs/graphql");
const sequelize_1 = require("sequelize");
const short_uuid_1 = __importDefault(require("short-uuid"));
const incentive_model_1 = require("../../incentives/models/incentive.model");
const profile_model_1 = require("../../profiles/models/profile.model");
const organisation_model_1 = require("../../organisations/models/organisation.model");
const incentive_participants_args_1 = require("../args/incentive-participants.args");
let IncentiveParticipant = class IncentiveParticipant extends sequelize_typescript_1.Model {
};
exports.IncentiveParticipant = IncentiveParticipant;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], IncentiveParticipant.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(() => incentive_participants_args_1.IncentiveParticipantStatus, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], IncentiveParticipant.prototype, "status", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => incentive_model_1.Incentive),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], IncentiveParticipant.prototype, "incentiveId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => incentive_model_1.Incentive, {
        foreignKey: 'incentiveId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", incentive_model_1.Incentive)
], IncentiveParticipant.prototype, "incentive", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], IncentiveParticipant.prototype, "profileId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, {
        foreignKey: 'profileId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", profile_model_1.Profile)
], IncentiveParticipant.prototype, "profile", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], IncentiveParticipant.prototype, "inviterProfileId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, 'inviterProfileId'),
    __metadata("design:type", profile_model_1.Profile)
], IncentiveParticipant.prototype, "inviterProfile", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], IncentiveParticipant.prototype, "organisationId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, {
        foreignKey: 'organisationId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", organisation_model_1.Organisation)
], IncentiveParticipant.prototype, "organisation", void 0);
__decorate([
    (0, graphql_1.Field)(() => incentive_participants_args_1.IncentiveInvitationStatus, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], IncentiveParticipant.prototype, "invitationStatus", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], IncentiveParticipant.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], IncentiveParticipant.prototype, "updatedAt", void 0);
exports.IncentiveParticipant = IncentiveParticipant = __decorate([
    sequelize_typescript_1.Table,
    (0, graphql_1.ObjectType)()
], IncentiveParticipant);
//# sourceMappingURL=incentive-participant.model.js.map