"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncentivesArgs = exports.IncentivesFilter = exports.IncentiveBookingFieldType = exports.IncentiveBookingType = exports.IncentiveType = void 0;
const graphql_1 = require("@nestjs/graphql");
const pagination_args_1 = require("../../common/args/pagination.args");
const incentive_participants_args_1 = require("../../incentive-participants/args/incentive-participants.args");
var IncentiveType;
(function (IncentiveType) {
    IncentiveType["CashbackOrVoucher"] = "CashbackOrVoucher";
    IncentiveType["Prize"] = "Prize";
    IncentiveType["FAMTrip"] = "FAMTrip";
})(IncentiveType || (exports.IncentiveType = IncentiveType = {}));
(0, graphql_1.registerEnumType)(IncentiveType, { name: 'IncentiveType' });
var IncentiveBookingType;
(function (IncentiveBookingType) {
    IncentiveBookingType["Flight"] = "Flight";
    IncentiveBookingType["Hotel"] = "Hotel";
    IncentiveBookingType["Cruise"] = "Cruise";
    IncentiveBookingType["Other"] = "Other";
})(IncentiveBookingType || (exports.IncentiveBookingType = IncentiveBookingType = {}));
(0, graphql_1.registerEnumType)(IncentiveBookingType, { name: 'IncentiveBookingType' });
var IncentiveBookingFieldType;
(function (IncentiveBookingFieldType) {
    IncentiveBookingFieldType["String"] = "String";
})(IncentiveBookingFieldType || (exports.IncentiveBookingFieldType = IncentiveBookingFieldType = {}));
(0, graphql_1.registerEnumType)(IncentiveBookingFieldType, {
    name: 'IncentiveBookingFieldType',
});
let IncentivesFilter = class IncentivesFilter {
};
exports.IncentivesFilter = IncentivesFilter;
__decorate([
    (0, graphql_1.Field)(() => [incentive_participants_args_1.IncentiveParticipantStatus], {
        nullable: true,
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], IncentivesFilter.prototype, "incentiveParticipantStatus", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], IncentivesFilter.prototype, "organisationId", void 0);
__decorate([
    (0, graphql_1.Field)(() => [IncentiveType], { nullable: true, defaultValue: [] }),
    __metadata("design:type", Array)
], IncentivesFilter.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)(() => [IncentiveBookingType], { nullable: true, defaultValue: [] }),
    __metadata("design:type", Array)
], IncentivesFilter.prototype, "bookingType", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true, defaultValue: null }),
    __metadata("design:type", Boolean)
], IncentivesFilter.prototype, "isPublic", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], IncentivesFilter.prototype, "searchText", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true, defaultValue: null }),
    __metadata("design:type", Boolean)
], IncentivesFilter.prototype, "isEnded", void 0);
exports.IncentivesFilter = IncentivesFilter = __decorate([
    (0, graphql_1.InputType)()
], IncentivesFilter);
let IncentivesArgs = class IncentivesArgs extends pagination_args_1.PaginationArgs {
};
exports.IncentivesArgs = IncentivesArgs;
__decorate([
    (0, graphql_1.Field)(() => IncentivesFilter, { nullable: true }),
    __metadata("design:type", IncentivesFilter)
], IncentivesArgs.prototype, "filter", void 0);
exports.IncentivesArgs = IncentivesArgs = __decorate([
    (0, graphql_1.ArgsType)()
], IncentivesArgs);
//# sourceMappingURL=incentives.args.js.map