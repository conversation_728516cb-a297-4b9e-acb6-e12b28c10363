"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateIncentiveDto = void 0;
const class_validator_1 = require("class-validator");
const incentives_args_1 = require("../args/incentives.args");
const update_incentive_dto_1 = require("./update-incentive.dto");
class CreateIncentiveDto extends update_incentive_dto_1.UpdateIncentiveDto {
}
exports.CreateIncentiveDto = CreateIncentiveDto;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateIncentiveDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateIncentiveDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateIncentiveDto.prototype, "isAllDay", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", Date)
], CreateIncentiveDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", Date)
], CreateIncentiveDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", Array)
], CreateIncentiveDto.prototype, "regions", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", Array)
], CreateIncentiveDto.prototype, "bookingTypes", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", Array)
], CreateIncentiveDto.prototype, "bookingFields", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateIncentiveDto.prototype, "isPublic", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateIncentiveDto.prototype, "isParticipantListVisible", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", String)
], CreateIncentiveDto.prototype, "organisationId", void 0);
//# sourceMappingURL=create-incentive.dto.js.map