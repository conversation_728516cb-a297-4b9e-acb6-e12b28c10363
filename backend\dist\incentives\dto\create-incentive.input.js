"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateIncentiveInput = exports.IncentiveBookingFieldInput = void 0;
const graphql_1 = require("@nestjs/graphql");
const update_incentive_input_1 = require("./update-incentive.input");
const class_validator_1 = require("class-validator");
const incentives_args_1 = require("../args/incentives.args");
const profiles_args_1 = require("../../profiles/args/profiles.args");
let IncentiveBookingFieldInput = class IncentiveBookingFieldInput {
};
exports.IncentiveBookingFieldInput = IncentiveBookingFieldInput;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], IncentiveBookingFieldInput.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)(() => incentives_args_1.IncentiveBookingFieldType, {
        defaultValue: incentives_args_1.IncentiveBookingFieldType.String,
    }),
    __metadata("design:type", String)
], IncentiveBookingFieldInput.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { defaultValue: false }),
    __metadata("design:type", Boolean)
], IncentiveBookingFieldInput.prototype, "isOptional", void 0);
exports.IncentiveBookingFieldInput = IncentiveBookingFieldInput = __decorate([
    (0, graphql_1.InputType)()
], IncentiveBookingFieldInput);
let CreateIncentiveInput = class CreateIncentiveInput extends update_incentive_input_1.UpdateIncentiveInput {
};
exports.CreateIncentiveInput = CreateIncentiveInput;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CreateIncentiveInput.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.MaxLength)(255),
    (0, graphql_1.Field)(() => incentives_args_1.IncentiveType),
    __metadata("design:type", String)
], CreateIncentiveInput.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CreateIncentiveInput.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], CreateIncentiveInput.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], CreateIncentiveInput.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(() => [profiles_args_1.Region]),
    __metadata("design:type", Array)
], CreateIncentiveInput.prototype, "regions", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(() => [incentives_args_1.IncentiveBookingType]),
    __metadata("design:type", Array)
], CreateIncentiveInput.prototype, "bookingTypes", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(() => [IncentiveBookingFieldInput]),
    __metadata("design:type", Array)
], CreateIncentiveInput.prototype, "bookingFields", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], CreateIncentiveInput.prototype, "isPublic", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], CreateIncentiveInput.prototype, "isParticipantListVisible", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, graphql_1.Field)(() => graphql_1.ID, { nullable: false }),
    __metadata("design:type", String)
], CreateIncentiveInput.prototype, "organisationId", void 0);
exports.CreateIncentiveInput = CreateIncentiveInput = __decorate([
    (0, graphql_1.InputType)()
], CreateIncentiveInput);
//# sourceMappingURL=create-incentive.input.js.map