"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncentivesRepositoryHelper = void 0;
const sequelize_1 = require("sequelize");
const common_1 = require("@nestjs/common");
const underscore_1 = require("../../common/helpers/underscore");
const incentive_participants_service_1 = require("../../incentive-participants/incentive-participants.service");
const membership_model_1 = require("../../memberships/models/membership.model");
const memberships_service_1 = require("../../memberships/memberships.service");
let IncentivesRepositoryHelper = class IncentivesRepositoryHelper {
    async getIncentiveParticipantIncentiveIds(profileId, filters) {
        const queryParams = { profileId };
        if (filters === null || filters === void 0 ? void 0 : filters.status) {
            queryParams.status = {
                [sequelize_1.Op.in]: filters.status,
            };
        }
        const incentiveParticipants = await this.incentiveParticipantsService.findAll(queryParams, {
            attributes: ['incentiveId'],
        });
        return underscore_1.Underscore.map(incentiveParticipants, 'incentiveId');
    }
    async getHostOrganisationIds(profileId) {
        const memberships = await this.membershipsService.findAll({
            profileId,
            status: membership_model_1.MembershipStatus.Active,
            permissions: {
                [sequelize_1.Op.overlap]: [
                    membership_model_1.MembershipPermission.Owner,
                    membership_model_1.MembershipPermission.Admin,
                    membership_model_1.MembershipPermission.HiddenAdmin,
                    membership_model_1.MembershipPermission.Manager,
                ],
            },
        }, {
            attributes: ['organisationId'],
            useCache: true,
        });
        return underscore_1.Underscore.map(memberships, 'organisationId');
    }
};
exports.IncentivesRepositoryHelper = IncentivesRepositoryHelper;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentive_participants_service_1.IncentiveParticipantsService)),
    __metadata("design:type", incentive_participants_service_1.IncentiveParticipantsService)
], IncentivesRepositoryHelper.prototype, "incentiveParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], IncentivesRepositoryHelper.prototype, "membershipsService", void 0);
exports.IncentivesRepositoryHelper = IncentivesRepositoryHelper = __decorate([
    (0, common_1.Injectable)()
], IncentivesRepositoryHelper);
//# sourceMappingURL=incentives.repository.helper.js.map