"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncentivesRepository = void 0;
const common_1 = require("@nestjs/common");
const incentive_model_1 = require("./models/incentive.model");
const pagination_1 = require("../common/helpers/pagination");
const sequelize_1 = require("sequelize");
const incentives_repository_helper_1 = require("./helpers/incentives.repository.helper");
const incentive_participants_args_1 = require("../incentive-participants/args/incentive-participants.args");
let IncentivesRepository = class IncentivesRepository {
    async findIncentives(profileId, filter, pagination) {
        const incentiveParticipantStatus = (filter === null || filter === void 0 ? void 0 : filter.incentiveParticipantStatus) || [];
        const incentiveParticipantIncentiveIds = await this.helper.getIncentiveParticipantIncentiveIds(profileId, {
            status: incentiveParticipantStatus.length > 0
                ? incentiveParticipantStatus
                : [
                    incentive_participants_args_1.IncentiveParticipantStatus.Registered,
                    incentive_participants_args_1.IncentiveParticipantStatus.InvitedByHost,
                    incentive_participants_args_1.IncentiveParticipantStatus.InvitedByParticipant,
                    incentive_participants_args_1.IncentiveParticipantStatus.InvitedRegistered,
                ],
        });
        const hostOrganisationIds = await this.helper.getHostOrganisationIds(profileId);
        if ((filter === null || filter === void 0 ? void 0 : filter.isPublic) === false &&
            incentiveParticipantIncentiveIds.length === 0) {
            return {
                records: [],
                totalCount: 0,
            };
        }
        const extraQueryParams = {
            [sequelize_1.Op.or]: [
                {
                    organisationId: {
                        [sequelize_1.Op.in]: hostOrganisationIds,
                    },
                },
                {
                    organisationId: {
                        [sequelize_1.Op.notIn]: hostOrganisationIds,
                    },
                    isPublic: true,
                },
                {
                    id: {
                        [sequelize_1.Op.in]: incentiveParticipantIncentiveIds,
                    },
                },
            ],
        };
        let includeIds = [];
        if (incentiveParticipantStatus.length > 0) {
            includeIds =
                incentiveParticipantIncentiveIds.length > 0
                    ? incentiveParticipantIncentiveIds
                    : ['non-existing-id'];
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.isPublic) === true || (filter === null || filter === void 0 ? void 0 : filter.isPublic) === false) {
            extraQueryParams.isPublic = filter.isPublic;
        }
        if (filter === null || filter === void 0 ? void 0 : filter.organisationId) {
            extraQueryParams.organisationId = filter.organisationId;
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.type) && filter.type.length > 0) {
            extraQueryParams.type = {
                [sequelize_1.Op.in]: filter.type,
            };
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.bookingType) && filter.bookingType.length > 0) {
            extraQueryParams.bookingTypes = {
                [sequelize_1.Op.overlap]: filter.bookingType,
            };
        }
        if (filter === null || filter === void 0 ? void 0 : filter.searchText) {
            extraQueryParams.name = {
                [sequelize_1.Op.iLike]: `%${filter.searchText}%`,
            };
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.isEnded) === true) {
            extraQueryParams.endDate = {
                [sequelize_1.Op.lt]: new Date(),
            };
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.isEnded) === false) {
            extraQueryParams.endDate = {
                [sequelize_1.Op.gte]: new Date(),
            };
        }
        return new pagination_1.PaginationHelper().getPaginatedResults({
            model: incentive_model_1.Incentive,
            pagination,
            extraQueryParams,
            includeIds,
            excludeIds: [],
        });
    }
};
exports.IncentivesRepository = IncentivesRepository;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentives_repository_helper_1.IncentivesRepositoryHelper)),
    __metadata("design:type", incentives_repository_helper_1.IncentivesRepositoryHelper)
], IncentivesRepository.prototype, "helper", void 0);
exports.IncentivesRepository = IncentivesRepository = __decorate([
    (0, common_1.Injectable)()
], IncentivesRepository);
//# sourceMappingURL=incentives.repository.js.map