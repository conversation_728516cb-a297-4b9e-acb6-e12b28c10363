"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncentivesResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const incentive_model_1 = require("./models/incentive.model");
const incentives_service_1 = require("./incentives.service");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const create_incentive_input_1 = require("./dto/create-incentive.input");
const paginated_result_1 = require("../common/args/paginated-result");
const incentives_args_1 = require("./args/incentives.args");
const update_incentive_input_1 = require("./dto/update-incentive.input");
const memberships_service_rights_1 = require("../memberships/helpers/memberships.service.rights");
const organisations_service_1 = require("../organisations/organisations.service");
const organisation_model_1 = require("../organisations/models/organisation.model");
const incentive_participant_model_1 = require("../incentive-participants/models/incentive-participant.model");
const incentive_participants_service_1 = require("../incentive-participants/incentive-participants.service");
const incentive_participants_args_1 = require("../incentive-participants/args/incentive-participants.args");
const incentive_participants_service_helper_1 = require("../incentive-participants/helpers/incentive-participants.service.helper");
const error_1 = require("../common/helpers/error");
const incentive_bookings_service_1 = require("../incentive-bookings/incentive-bookings.service");
let IncentivesResolver = class IncentivesResolver {
    async incentive(user, id) {
        this.logger.verbose('IncentivesResolver.incentive (query)', {
            user: user.toLogObject(),
            id,
        });
        const incentive = await this.incentivesService.findById(id);
        if (!incentive) {
            this.errorHelper.throwHttpException(`IncentivesResolver.incentive`, `Sorry, this incentive does not exist.`);
        }
        return incentive;
    }
    incentives(user, incentiveArgs) {
        var _a, _b, _c, _d, _e, _f, _g;
        this.logger.verbose('IncentivesResolver.incentives (query)', {
            user: user.toLogObject(),
            incentiveArgs,
        });
        return this.incentivesService.findIncentives(user.profileId, {
            incentiveParticipantStatus: (_a = incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.filter) === null || _a === void 0 ? void 0 : _a.incentiveParticipantStatus,
            organisationId: (_b = incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.filter) === null || _b === void 0 ? void 0 : _b.organisationId,
            type: (_c = incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.filter) === null || _c === void 0 ? void 0 : _c.type,
            bookingType: (_d = incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.filter) === null || _d === void 0 ? void 0 : _d.bookingType,
            isPublic: (_e = incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.filter) === null || _e === void 0 ? void 0 : _e.isPublic,
            searchText: (_f = incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.filter) === null || _f === void 0 ? void 0 : _f.searchText,
            isEnded: (_g = incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.filter) === null || _g === void 0 ? void 0 : _g.isEnded,
        }, {
            first: incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.first,
            after: incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.after,
            sortBy: incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.sortBy,
            sortOrder: incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.sortOrder,
        });
    }
    async createIncentive(user, incentiveData) {
        this.logger.verbose('IncentivesResolver.createIncentive (mutation)', {
            user: user.toLogObject(),
            incentiveData,
        });
        const { organisationId } = incentiveData;
        await this.membershipsServiceRights.checkRights(organisationId, {
            currentProfileId: user.profileId,
            rights: [memberships_service_rights_1.MembershipRight.CreateIncentive],
        });
        return this.incentivesService.createIncentive(incentiveData, user.profileId, { currentUser: user });
    }
    async updateIncentive(user, incentiveId, incentiveData) {
        this.logger.verbose('IncentivesResolver.updateIncentive (mutation)', {
            user: user.toLogObject(),
            incentiveData,
        });
        const incentive = await this.incentivesService.findById(incentiveId);
        await this.membershipsServiceRights.checkRights(incentive.organisationId, {
            currentProfileId: user.profileId,
            rights: [memberships_service_rights_1.MembershipRight.UpdateIncentive],
        });
        return this.incentivesService.updateIncentive(user, incentiveId, incentiveData);
    }
    async removeIncentive(user, id) {
        this.logger.verbose('IncentivesResolver.removeIncentive (mutation)', {
            user: user.toLogObject(),
            id,
        });
        const incentive = await this.incentivesService.findById(id);
        if (!incentive)
            return true;
        await this.membershipsServiceRights.checkRights(incentive.organisationId, {
            currentProfileId: user.profileId,
            rights: [memberships_service_rights_1.MembershipRight.RemoveIncentive],
        });
        await this.incentivesService.removeIncentive(id);
        return true;
    }
    async registerToIncentive(user, incentiveId) {
        this.logger.verbose('IncentivesResolver.registerToIncentive (mutation)', {
            user: user.toLogObject(),
            incentiveId,
        });
        return this.incentivesService.register(user, incentiveId);
    }
    async organisation(incentive) {
        if (incentive.organisation)
            return incentive.organisation;
        this.logger.verbose('IncentivesResolver.organisation (field resolver)', {
            incentiveId: incentive.id,
        });
        return this.organisationsService.findById(incentive.organisationId, {
            useCache: true,
        });
    }
    async participant(user, incentive) {
        this.logger.verbose('IncentivesResolver.participants (field resolver)', {
            incentiveId: incentive.id,
        });
        return this.incentiveParticipantsService.findOne({
            incentiveId: incentive.id,
            profileId: user.profileId,
        }, { useCache: true });
    }
    async participants(incentive, incentiveParticipantsArgs) {
        this.logger.verbose('IncentivesResolver.participants (field resolver)', {
            incentiveId: incentive.id,
        });
        return this.incentiveParticipantsService.findAll(Object.assign({ incentiveId: incentive.id }, incentive_participants_service_helper_1.IncentiveParticipantsServiceHelper.getQueryParams(incentiveParticipantsArgs)), { useCache: true });
    }
    async incentiveBookingsCount(incentive) {
        this.logger.verbose('IncentivesResolver.incentiveBookingsCount (field resolver)', {
            incentiveId: incentive.id,
        });
        return this.incentiveBookingsService.getModel().count({
            where: {
                incentiveId: incentive.id,
            },
        });
    }
};
exports.IncentivesResolver = IncentivesResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentives_service_1.IncentivesService)),
    __metadata("design:type", incentives_service_1.IncentivesService)
], IncentivesResolver.prototype, "incentivesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentive_participants_service_1.IncentiveParticipantsService)),
    __metadata("design:type", incentive_participants_service_1.IncentiveParticipantsService)
], IncentivesResolver.prototype, "incentiveParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentive_bookings_service_1.IncentiveBookingsService)),
    __metadata("design:type", incentive_bookings_service_1.IncentiveBookingsService)
], IncentivesResolver.prototype, "incentiveBookingsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_rights_1.MembershipsServiceRights)),
    __metadata("design:type", memberships_service_rights_1.MembershipsServiceRights)
], IncentivesResolver.prototype, "membershipsServiceRights", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], IncentivesResolver.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], IncentivesResolver.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], IncentivesResolver.prototype, "errorHelper", void 0);
__decorate([
    (0, graphql_1.Query)(() => incentive_model_1.Incentive),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id', { nullable: false })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], IncentivesResolver.prototype, "incentive", null);
__decorate([
    (0, graphql_1.Query)(() => paginated_result_1.IncentivesResult),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, incentives_args_1.IncentivesArgs]),
    __metadata("design:returntype", Promise)
], IncentivesResolver.prototype, "incentives", null);
__decorate([
    (0, graphql_1.Mutation)(() => incentive_model_1.Incentive),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('incentiveData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_incentive_input_1.CreateIncentiveInput]),
    __metadata("design:returntype", Promise)
], IncentivesResolver.prototype, "createIncentive", null);
__decorate([
    (0, graphql_1.Mutation)(() => incentive_model_1.Incentive),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('incentiveId')),
    __param(2, (0, graphql_1.Args)('incentiveData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, update_incentive_input_1.UpdateIncentiveInput]),
    __metadata("design:returntype", Promise)
], IncentivesResolver.prototype, "updateIncentive", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], IncentivesResolver.prototype, "removeIncentive", null);
__decorate([
    (0, graphql_1.Mutation)(() => incentive_participant_model_1.IncentiveParticipant),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('incentiveId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], IncentivesResolver.prototype, "registerToIncentive", null);
__decorate([
    (0, graphql_1.ResolveField)('organisation', () => organisation_model_1.Organisation),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [incentive_model_1.Incentive]),
    __metadata("design:returntype", Promise)
], IncentivesResolver.prototype, "organisation", null);
__decorate([
    (0, graphql_1.ResolveField)('participant', () => incentive_participant_model_1.IncentiveParticipant, { nullable: true }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, incentive_model_1.Incentive]),
    __metadata("design:returntype", Promise)
], IncentivesResolver.prototype, "participant", null);
__decorate([
    (0, graphql_1.ResolveField)('participants', () => [incentive_participant_model_1.IncentiveParticipant]),
    __param(0, (0, graphql_1.Parent)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [incentive_model_1.Incentive,
        incentive_participants_args_1.IncentiveParticipantsArgs]),
    __metadata("design:returntype", Promise)
], IncentivesResolver.prototype, "participants", null);
__decorate([
    (0, graphql_1.ResolveField)('incentiveBookingsCount', () => graphql_1.Int),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [incentive_model_1.Incentive]),
    __metadata("design:returntype", Promise)
], IncentivesResolver.prototype, "incentiveBookingsCount", null);
exports.IncentivesResolver = IncentivesResolver = __decorate([
    (0, graphql_1.Resolver)(() => incentive_model_1.Incentive)
], IncentivesResolver);
//# sourceMappingURL=incentives.resolver.js.map