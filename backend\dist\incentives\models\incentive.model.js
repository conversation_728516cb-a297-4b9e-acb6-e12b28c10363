"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Incentive = exports.IncentiveBookingField = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const sequelize_1 = require("sequelize");
const graphql_1 = require("@nestjs/graphql");
const short_uuid_1 = __importDefault(require("short-uuid"));
const organisation_model_1 = require("../../organisations/models/organisation.model");
const incentives_args_1 = require("../args/incentives.args");
const profiles_args_1 = require("../../profiles/args/profiles.args");
const incentive_participant_model_1 = require("../../incentive-participants/models/incentive-participant.model");
const event_model_1 = require("../../events/models/event.model");
let IncentiveBookingField = class IncentiveBookingField {
};
exports.IncentiveBookingField = IncentiveBookingField;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], IncentiveBookingField.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)(() => incentives_args_1.IncentiveBookingFieldType, {
        defaultValue: incentives_args_1.IncentiveBookingFieldType.String,
    }),
    __metadata("design:type", String)
], IncentiveBookingField.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { defaultValue: false }),
    __metadata("design:type", Boolean)
], IncentiveBookingField.prototype, "isOptional", void 0);
exports.IncentiveBookingField = IncentiveBookingField = __decorate([
    (0, graphql_1.ObjectType)()
], IncentiveBookingField);
let Incentive = class Incentive extends sequelize_typescript_1.Model {
    get isEnded() {
        return this.endDate && this.endDate.getTime() < Date.now();
    }
};
exports.Incentive = Incentive;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], Incentive.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        allowNull: false,
    }),
    __metadata("design:type", String)
], Incentive.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)(() => incentives_args_1.IncentiveType, { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], Incentive.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({
        allowNull: true,
    }),
    __metadata("design:type", String)
], Incentive.prototype, "image", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        allowNull: false,
    }),
    __metadata("design:type", String)
], Incentive.prototype, "description", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Incentive.prototype, "startDate", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Incentive.prototype, "endDate", void 0);
__decorate([
    (0, graphql_1.Field)(() => [profiles_args_1.Region]),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
    }),
    __metadata("design:type", Array)
], Incentive.prototype, "regions", void 0);
__decorate([
    (0, graphql_1.Field)(() => [incentives_args_1.IncentiveBookingType], { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], Incentive.prototype, "bookingTypes", void 0);
__decorate([
    (0, graphql_1.Field)(() => [IncentiveBookingField], { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.JSON),
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], Incentive.prototype, "bookingFields", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({
        allowNull: true,
    }),
    __metadata("design:type", String)
], Incentive.prototype, "terms", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { defaultValue: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Incentive.prototype, "isPublic", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { defaultValue: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Incentive.prototype, "isParticipantListVisible", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean),
    (0, sequelize_typescript_1.Column)(sequelize_1.DataTypes.VIRTUAL),
    __metadata("design:type", Boolean),
    __metadata("design:paramtypes", [])
], Incentive.prototype, "isEnded", null);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Incentive.prototype, "organisationId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, {
        foreignKey: 'organisationId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", organisation_model_1.Organisation)
], Incentive.prototype, "organisation", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => event_model_1.Event),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Incentive.prototype, "startEventId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => event_model_1.Event, {
        foreignKey: 'startEventId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", event_model_1.Event)
], Incentive.prototype, "startEvent", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => event_model_1.Event),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Incentive.prototype, "endEventId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => event_model_1.Event, {
        foreignKey: 'endEventId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", event_model_1.Event)
], Incentive.prototype, "endEvent", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => incentive_participant_model_1.IncentiveParticipant, 'incentiveId'),
    __metadata("design:type", Array)
], Incentive.prototype, "incentiveParticipants", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Incentive.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Incentive.prototype, "updatedAt", void 0);
exports.Incentive = Incentive = __decorate([
    sequelize_typescript_1.Table,
    (0, graphql_1.ObjectType)()
], Incentive);
//# sourceMappingURL=incentive.model.js.map