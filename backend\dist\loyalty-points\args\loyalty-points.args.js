"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TopAgentLeaderboardArgs = exports.TopAgentLeaderboardResult = exports.LeaderboardResult = exports.LeaderboardEntry = exports.VariableRewardPointsResult = exports.AgentLeaderboardArgs = exports.LoyaltyPointsArgs = void 0;
const graphql_1 = require("@nestjs/graphql");
const pagination_args_1 = require("../../common/args/pagination.args");
const profile_model_1 = require("../../profiles/models/profile.model");
let LoyaltyPointsArgs = class LoyaltyPointsArgs extends pagination_args_1.PaginationArgs {
};
exports.LoyaltyPointsArgs = LoyaltyPointsArgs;
__decorate([
    (0, graphql_1.Field)(() => Date),
    __metadata("design:type", Date)
], LoyaltyPointsArgs.prototype, "startDate", void 0);
__decorate([
    (0, graphql_1.Field)(() => Date),
    __metadata("design:type", Date)
], LoyaltyPointsArgs.prototype, "endDate", void 0);
exports.LoyaltyPointsArgs = LoyaltyPointsArgs = __decorate([
    (0, graphql_1.ArgsType)()
], LoyaltyPointsArgs);
let AgentLeaderboardArgs = class AgentLeaderboardArgs extends pagination_args_1.PaginationArgs {
};
exports.AgentLeaderboardArgs = AgentLeaderboardArgs;
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], AgentLeaderboardArgs.prototype, "organisationId", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int, { nullable: true }),
    __metadata("design:type", Number)
], AgentLeaderboardArgs.prototype, "offset", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int, { nullable: true }),
    __metadata("design:type", Number)
], AgentLeaderboardArgs.prototype, "first", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    __metadata("design:type", String)
], AgentLeaderboardArgs.prototype, "sortBy", void 0);
__decorate([
    (0, graphql_1.Field)(() => pagination_args_1.PaginationSortOrder, { nullable: true }),
    __metadata("design:type", String)
], AgentLeaderboardArgs.prototype, "sortOrder", void 0);
exports.AgentLeaderboardArgs = AgentLeaderboardArgs = __decorate([
    (0, graphql_1.ArgsType)()
], AgentLeaderboardArgs);
let VariableRewardPointsResult = class VariableRewardPointsResult {
};
exports.VariableRewardPointsResult = VariableRewardPointsResult;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int),
    __metadata("design:type", Number)
], VariableRewardPointsResult.prototype, "points", void 0);
exports.VariableRewardPointsResult = VariableRewardPointsResult = __decorate([
    (0, graphql_1.ObjectType)()
], VariableRewardPointsResult);
let LeaderboardEntry = class LeaderboardEntry {
};
exports.LeaderboardEntry = LeaderboardEntry;
__decorate([
    (0, graphql_1.Field)(() => profile_model_1.Profile),
    __metadata("design:type", profile_model_1.Profile)
], LeaderboardEntry.prototype, "profile", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], LeaderboardEntry.prototype, "activeTier", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int),
    __metadata("design:type", Number)
], LeaderboardEntry.prototype, "rollingPoints", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int),
    __metadata("design:type", Number)
], LeaderboardEntry.prototype, "lifetimePoints", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int),
    __metadata("design:type", Number)
], LeaderboardEntry.prototype, "level", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int, { nullable: true }),
    __metadata("design:type", Number)
], LeaderboardEntry.prototype, "rank", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int, { nullable: true }),
    __metadata("design:type", Number)
], LeaderboardEntry.prototype, "lifetimeRank", void 0);
exports.LeaderboardEntry = LeaderboardEntry = __decorate([
    (0, graphql_1.ObjectType)()
], LeaderboardEntry);
let LeaderboardResult = class LeaderboardResult {
};
exports.LeaderboardResult = LeaderboardResult;
__decorate([
    (0, graphql_1.Field)(() => [LeaderboardEntry]),
    __metadata("design:type", Array)
], LeaderboardResult.prototype, "records", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int),
    __metadata("design:type", Number)
], LeaderboardResult.prototype, "totalCount", void 0);
exports.LeaderboardResult = LeaderboardResult = __decorate([
    (0, graphql_1.ObjectType)()
], LeaderboardResult);
let TopAgentLeaderboardResult = class TopAgentLeaderboardResult {
};
exports.TopAgentLeaderboardResult = TopAgentLeaderboardResult;
__decorate([
    (0, graphql_1.Field)(() => [LeaderboardEntry]),
    __metadata("design:type", Array)
], TopAgentLeaderboardResult.prototype, "records", void 0);
exports.TopAgentLeaderboardResult = TopAgentLeaderboardResult = __decorate([
    (0, graphql_1.ObjectType)()
], TopAgentLeaderboardResult);
let TopAgentLeaderboardArgs = class TopAgentLeaderboardArgs extends pagination_args_1.PaginationArgs {
};
exports.TopAgentLeaderboardArgs = TopAgentLeaderboardArgs;
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], TopAgentLeaderboardArgs.prototype, "vanityId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], TopAgentLeaderboardArgs.prototype, "organisationId", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    __metadata("design:type", String)
], TopAgentLeaderboardArgs.prototype, "sortBy", void 0);
__decorate([
    (0, graphql_1.Field)(() => pagination_args_1.PaginationSortOrder, { nullable: true }),
    __metadata("design:type", String)
], TopAgentLeaderboardArgs.prototype, "sortOrder", void 0);
exports.TopAgentLeaderboardArgs = TopAgentLeaderboardArgs = __decorate([
    (0, graphql_1.ArgsType)()
], TopAgentLeaderboardArgs);
//# sourceMappingURL=loyalty-points.args.js.map