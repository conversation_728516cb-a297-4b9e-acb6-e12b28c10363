"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoyaltyPointsController = void 0;
const common_1 = require("@nestjs/common");
const gcp_auth_guard_1 = require("../common/guards/gcp-auth.guard");
const error_1 = require("../common/helpers/error");
const config_1 = __importDefault(require("../config/config"));
const loyalty_points_service_1 = require("./loyalty-points.service");
let LoyaltyPointsController = class LoyaltyPointsController {
    runJob() {
        return this.loyaltyPointsService.handleWeeklyNotification();
    }
    runJobBeta() {
        try {
            if (config_1.default.NODE_ENV === 'development' || config_1.default.SERVICE === 'api-beta') {
                return this.loyaltyPointsService.handleWeeklyNotification();
            }
            else {
                throw new Error('only beta env allowed');
            }
        }
        catch (err) {
            this.errorHelper.throwHttpException(`SuggestFollowService.handleProcessSuggestFollows`, err.message);
        }
    }
    runActivityDataMigration() {
        void this.loyaltyPointsService.addZeroLoyaltyPoints();
        return;
    }
    runActivityDataMigrationBeta() {
        try {
            if (config_1.default.NODE_ENV === 'development' || config_1.default.SERVICE === 'api-beta') {
                void this.loyaltyPointsService.addZeroLoyaltyPoints();
                return;
            }
            else {
                throw new Error('only beta env allowed');
            }
        }
        catch (err) {
            this.errorHelper.throwHttpException(`ActivitiesService.runJobBeta`, err.message);
        }
    }
    agentLeaderboardRefresh() {
        void this.loyaltyPointsService.agentLeaderboardRefresh();
        return;
    }
    agentLeaderboardRefreshBeta() {
        try {
            if (config_1.default.NODE_ENV === 'development' || config_1.default.SERVICE === 'api-beta') {
                void this.loyaltyPointsService.agentLeaderboardRefresh();
                return;
            }
            else {
                throw new Error('only beta env allowed');
            }
        }
        catch (err) {
            this.errorHelper.throwHttpException(`ActivitiesService.runJobBeta`, err.message);
        }
    }
};
exports.LoyaltyPointsController = LoyaltyPointsController;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => loyalty_points_service_1.LoyaltyPointsService)),
    __metadata("design:type", loyalty_points_service_1.LoyaltyPointsService)
], LoyaltyPointsController.prototype, "loyaltyPointsService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], LoyaltyPointsController.prototype, "errorHelper", void 0);
__decorate([
    (0, common_1.UseGuards)(gcp_auth_guard_1.GcpAuthGuard),
    (0, common_1.Get)('run-job'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], LoyaltyPointsController.prototype, "runJob", null);
__decorate([
    (0, common_1.Get)('run-beta'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], LoyaltyPointsController.prototype, "runJobBeta", null);
__decorate([
    (0, common_1.UseGuards)(gcp_auth_guard_1.GcpAuthGuard),
    (0, common_1.Get)('run-addZeroLoyaltyPoints'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], LoyaltyPointsController.prototype, "runActivityDataMigration", null);
__decorate([
    (0, common_1.Get)('run-beta-addZeroLoyaltyPoints'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], LoyaltyPointsController.prototype, "runActivityDataMigrationBeta", null);
__decorate([
    (0, common_1.UseGuards)(gcp_auth_guard_1.GcpAuthGuard),
    (0, common_1.Get)('run-agentLeaderboardRefresh'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], LoyaltyPointsController.prototype, "agentLeaderboardRefresh", null);
__decorate([
    (0, common_1.Get)('run-beta-agentLeaderboardRefresh'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], LoyaltyPointsController.prototype, "agentLeaderboardRefreshBeta", null);
exports.LoyaltyPointsController = LoyaltyPointsController = __decorate([
    (0, common_1.Controller)('loyalty-points')
], LoyaltyPointsController);
//# sourceMappingURL=loyalty-points.controller.js.map