"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoyaltyPointsRepository = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const nest_winston_1 = require("nest-winston");
const sequelize_2 = require("sequelize");
const winston_1 = require("winston");
const pagination_args_1 = require("../common/args/pagination.args");
const pagination_1 = require("../common/helpers/pagination");
const loyalty_points_model_1 = require("./models/loyalty-points.model");
const activities_args_1 = require("../activities/args/activities.args");
let LoyaltyPointsRepository = class LoyaltyPointsRepository {
    async findLoyaltyPoints({ profileId, loyaltyPointsArgs, }) {
        const extraQueryParams = {
            profileId,
            type: { [sequelize_2.Op.ne]: activities_args_1.ActivityType.ZeroLoyaltyPoints },
        };
        const { endDate, startDate } = loyaltyPointsArgs, pagination = __rest(loyaltyPointsArgs, ["endDate", "startDate"]);
        pagination.sortOrder = pagination_args_1.PaginationSortOrder.Descending;
        if (startDate) {
            extraQueryParams.createdAt = {
                [sequelize_2.Op.gte]: startDate,
            };
        }
        if (endDate) {
            extraQueryParams.createdAt = Object.assign(Object.assign({}, extraQueryParams.createdAt), { [sequelize_2.Op.lte]: endDate });
        }
        return await new pagination_1.PaginationHelper().getPaginatedResults({
            model: loyalty_points_model_1.LoyaltyPoint,
            pagination,
            extraQueryParams,
            excludeIds: [],
        });
    }
    async sumPoints({ profileId, startOfWeek, endOfWeek, }) {
        this.logger.verbose('LoyalPointsRepository.sumPoints', {
            profileId,
            startOfWeek,
            endOfWeek,
        });
        const result = await this.loyaltyPointModel.sum('points', {
            where: {
                profileId,
                createdAt: {
                    [sequelize_2.Op.gte]: startOfWeek,
                    [sequelize_2.Op.lte]: endOfWeek,
                },
            },
        });
        return result || 0;
    }
};
exports.LoyaltyPointsRepository = LoyaltyPointsRepository;
__decorate([
    (0, sequelize_1.InjectModel)(loyalty_points_model_1.LoyaltyPoint),
    __metadata("design:type", Object)
], LoyaltyPointsRepository.prototype, "loyaltyPointModel", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], LoyaltyPointsRepository.prototype, "logger", void 0);
exports.LoyaltyPointsRepository = LoyaltyPointsRepository = __decorate([
    (0, common_1.Injectable)()
], LoyaltyPointsRepository);
//# sourceMappingURL=loyalty-points.repository.js.map