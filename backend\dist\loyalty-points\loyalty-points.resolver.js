"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoyaltyPointsResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const paginated_result_1 = require("../common/args/paginated-result");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const loyalty_points_args_1 = require("./args/loyalty-points.args");
const loyalty_points_service_1 = require("./loyalty-points.service");
const loyalty_points_model_1 = require("./models/loyalty-points.model");
const profiles_service_1 = require("../profiles/profiles.service");
const memberships_service_1 = require("../memberships/memberships.service");
const organisations_service_1 = require("../organisations/organisations.service");
const membership_model_1 = require("../memberships/models/membership.model");
let LoyaltyPointsResolver = class LoyaltyPointsResolver {
    async getLoyaltyPointsList(user, loyaltyPointsArgs) {
        this.logger.verbose('LoyaltyPointsResolver.getLoyaltyPointsList (query)', {
            user: user.toLogObject(),
            loyaltyPointsArgs,
        });
        return this.loyaltyPointsService.getLoyaltyPointsList({
            profileId: user.profileId,
            loyaltyPointsArgs,
        });
    }
    async getVariableRewardPoints(user) {
        this.logger.verbose('LoyaltyPointsResolver.getVariableRewardPoints (query)', {
            user: user.toLogObject(),
        });
        return {
            points: await this.loyaltyPointsService.getVariableRewardPoints({
                profileId: user.profileId,
            }),
        };
    }
    async getAgentLeaderboardList(currentUser, agentLeaderboardArgs) {
        this.logger.verbose('LoyaltyPointsResolver.getAgentLeaderboardList (query)', {
            user: currentUser.toLogObject(),
            agentLeaderboardArgs,
        });
        return this.loyaltyPointsService.getAgentLeaderboardList(agentLeaderboardArgs.organisationId, {
            currentUser,
            agentLeaderboardArgs,
        });
    }
    async getTopAgentsWithCurrentUser(currentUser, topAgentLeaderboardArgs) {
        this.logger.verbose('LoyaltyPointsResolver.getTopAgentsWithCurrentUser (query)', {
            user: currentUser.toLogObject(),
            topAgentLeaderboardArgs,
        });
        topAgentLeaderboardArgs.first = 10;
        topAgentLeaderboardArgs.offset = 0;
        const organisationId = await this.organisationsService.getOrganisationIdbyVanityId(topAgentLeaderboardArgs.vanityId);
        topAgentLeaderboardArgs.organisationId = organisationId;
        const topAgents = await this.loyaltyPointsService.getAgentLeaderboardList(topAgentLeaderboardArgs.organisationId, {
            currentUser,
            agentLeaderboardArgs: topAgentLeaderboardArgs,
        });
        const currentUserAgent = topAgents.records.filter((agent) => agent.profileId === currentUser.profileId);
        if (currentUserAgent.length === 0) {
            const currentUserLeaderboard = await this.loyaltyPointsService.getAgentLeaderboardBasedOnUser(topAgentLeaderboardArgs.organisationId, {
                currentUser,
                agentLeaderboardArgs: topAgentLeaderboardArgs,
            });
            if (currentUserLeaderboard && currentUserLeaderboard.profile) {
                topAgents.records.push({
                    profileId: currentUser.profileId,
                    profile: currentUserLeaderboard.profile,
                    rollingPoints: currentUserLeaderboard.rollingPoints,
                    lifetimePoints: currentUserLeaderboard.lifetimePoints,
                    rank: currentUserLeaderboard.rank,
                    lifetimeRank: currentUserLeaderboard.lifetimeRank,
                });
            }
        }
        return {
            records: topAgents.records,
        };
    }
    async addCustomKudos(user, profileId, kudosAmount, customText) {
        this.logger.verbose('LoyaltyPointsResolver.addKudos', {
            user: user.toLogObject(),
            profileId,
            kudosAmount,
            customText,
        });
        const primaryMembership = await this.membershipsService.findOne({
            profileId: user.profileId,
            isPrimary: true,
            status: membership_model_1.MembershipStatus.Active,
        });
        if (!primaryMembership) {
            throw new Error('User does not belong to any organisation.');
        }
        const organisation = await this.organisationsService.findOne({
            id: primaryMembership.organisationId,
        });
        const vanityId = organisation.vanityId;
        const validOrganisations = ['Hablo', 'TheTravelCorpCo'];
        const isValidMembership = validOrganisations.includes(vanityId);
        if (!isValidMembership) {
            throw new Error('User must be a member of an organisation with vanityId "hablo" or "TheTravelCorpCo".');
        }
        return this.loyaltyPointsService.addCustomKudos({
            profileId,
            kudosAmount,
            customText,
        });
    }
};
exports.LoyaltyPointsResolver = LoyaltyPointsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => loyalty_points_service_1.LoyaltyPointsService)),
    __metadata("design:type", loyalty_points_service_1.LoyaltyPointsService)
], LoyaltyPointsResolver.prototype, "loyaltyPointsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], LoyaltyPointsResolver.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], LoyaltyPointsResolver.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], LoyaltyPointsResolver.prototype, "membershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], LoyaltyPointsResolver.prototype, "organisationsService", void 0);
__decorate([
    (0, graphql_1.Query)(() => paginated_result_1.LoyaltyPointsResult),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, loyalty_points_args_1.LoyaltyPointsArgs]),
    __metadata("design:returntype", Promise)
], LoyaltyPointsResolver.prototype, "getLoyaltyPointsList", null);
__decorate([
    (0, graphql_1.Query)(() => loyalty_points_args_1.VariableRewardPointsResult),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], LoyaltyPointsResolver.prototype, "getVariableRewardPoints", null);
__decorate([
    (0, graphql_1.Query)(() => loyalty_points_args_1.LeaderboardResult),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, loyalty_points_args_1.AgentLeaderboardArgs]),
    __metadata("design:returntype", Promise)
], LoyaltyPointsResolver.prototype, "getAgentLeaderboardList", null);
__decorate([
    (0, graphql_1.Query)(() => loyalty_points_args_1.TopAgentLeaderboardResult),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, loyalty_points_args_1.TopAgentLeaderboardArgs]),
    __metadata("design:returntype", Promise)
], LoyaltyPointsResolver.prototype, "getTopAgentsWithCurrentUser", null);
__decorate([
    (0, graphql_1.Mutation)(() => loyalty_points_model_1.LoyaltyPoint),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('profileId')),
    __param(2, (0, graphql_1.Args)('kudosAmount')),
    __param(3, (0, graphql_1.Args)('customText')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Number, String]),
    __metadata("design:returntype", Promise)
], LoyaltyPointsResolver.prototype, "addCustomKudos", null);
exports.LoyaltyPointsResolver = LoyaltyPointsResolver = __decorate([
    (0, graphql_1.Resolver)(() => loyalty_points_model_1.LoyaltyPoint)
], LoyaltyPointsResolver);
//# sourceMappingURL=loyalty-points.resolver.js.map