"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoyaltyPoint = void 0;
const graphql_1 = require("@nestjs/graphql");
const sequelize_1 = require("sequelize");
const sequelize_typescript_1 = require("sequelize-typescript");
const short_uuid_1 = __importDefault(require("short-uuid"));
const activities_args_1 = require("../../activities/args/activities.args");
const activity_model_1 = require("../../activities/models/activity.model");
const profile_model_1 = require("../../profiles/models/profile.model");
const graphql_type_json_1 = __importDefault(require("graphql-type-json"));
const organisation_model_1 = require("../../organisations/models/organisation.model");
let LoyaltyPoint = class LoyaltyPoint extends sequelize_typescript_1.Model {
};
exports.LoyaltyPoint = LoyaltyPoint;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], LoyaltyPoint.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(() => activities_args_1.ActivityType, {
        nullable: false,
    }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], LoyaltyPoint.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)(() => Number),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    }),
    __metadata("design:type", Number)
], LoyaltyPoint.prototype, "points", void 0);
__decorate([
    (0, graphql_1.Field)(() => Number),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 1,
    }),
    __metadata("design:type", Number)
], LoyaltyPoint.prototype, "count", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    }),
    __metadata("design:type", Number)
], LoyaltyPoint.prototype, "rollingPoints", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    }),
    __metadata("design:type", Number)
], LoyaltyPoint.prototype, "lifeTimePoints", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
    }),
    __metadata("design:type", Number)
], LoyaltyPoint.prototype, "previousMonthTierIndex", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
    }),
    __metadata("design:type", Number)
], LoyaltyPoint.prototype, "currentTierIndex", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 1,
    }),
    __metadata("design:type", Number)
], LoyaltyPoint.prototype, "nextTierIndex", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
    }),
    __metadata("design:type", Number)
], LoyaltyPoint.prototype, "streak", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
    }),
    __metadata("design:type", Date)
], LoyaltyPoint.prototype, "streakUpdatedAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
    }),
    __metadata("design:type", String)
], LoyaltyPoint.prototype, "tier", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_type_json_1.default, {
        nullable: true,
    }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.JSONB,
    }),
    __metadata("design:type", Object)
], LoyaltyPoint.prototype, "placeholders", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => activity_model_1.Activity, 'loyaltyPointId'),
    __metadata("design:type", Array)
], LoyaltyPoint.prototype, "activities", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], LoyaltyPoint.prototype, "profileId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, 'profileId'),
    __metadata("design:type", profile_model_1.Profile)
], LoyaltyPoint.prototype, "profile", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], LoyaltyPoint.prototype, "organisationId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, 'organisationId'),
    __metadata("design:type", organisation_model_1.Organisation)
], LoyaltyPoint.prototype, "organisation", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.DATE,
    }),
    __metadata("design:type", Date)
], LoyaltyPoint.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.DATE,
    }),
    __metadata("design:type", Date)
], LoyaltyPoint.prototype, "updatedAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
    }),
    __metadata("design:type", Number)
], LoyaltyPoint.prototype, "activeTier", void 0);
exports.LoyaltyPoint = LoyaltyPoint = __decorate([
    sequelize_typescript_1.Table,
    (0, graphql_1.ObjectType)()
], LoyaltyPoint);
//# sourceMappingURL=loyalty-points.model.js.map