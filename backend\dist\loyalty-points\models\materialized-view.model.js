"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentLeaderboardMaterializedViewTable = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
let AgentLeaderboardMaterializedViewTable = class AgentLeaderboardMaterializedViewTable extends sequelize_typescript_1.Model {
};
exports.AgentLeaderboardMaterializedViewTable = AgentLeaderboardMaterializedViewTable;
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.STRING,
        allowNull: false,
        field: 'organisationId',
    }),
    __metadata("design:type", String)
], AgentLeaderboardMaterializedViewTable.prototype, "organisationId", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.INTEGER,
        allowNull: false,
        field: 'rolling_points',
    }),
    __metadata("design:type", Number)
], AgentLeaderboardMaterializedViewTable.prototype, "rollingPoints", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.STRING,
        field: 'profileId',
    }),
    __metadata("design:type", String)
], AgentLeaderboardMaterializedViewTable.prototype, "profileId", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.INTEGER,
        field: 'current_tier',
    }),
    __metadata("design:type", Number)
], AgentLeaderboardMaterializedViewTable.prototype, "currentTier", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.INTEGER,
        field: 'previous_month_tier',
    }),
    __metadata("design:type", Number)
], AgentLeaderboardMaterializedViewTable.prototype, "previousMonthTier", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.INTEGER,
        field: 'tier',
    }),
    __metadata("design:type", Number)
], AgentLeaderboardMaterializedViewTable.prototype, "streak", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_typescript_1.DataType.INTEGER,
        field: 'tier',
    }),
    __metadata("design:type", Number)
], AgentLeaderboardMaterializedViewTable.prototype, "rank", void 0);
exports.AgentLeaderboardMaterializedViewTable = AgentLeaderboardMaterializedViewTable = __decorate([
    (0, sequelize_typescript_1.Table)({
        tableName: 'agent_leaderboard',
        timestamps: false,
    })
], AgentLeaderboardMaterializedViewTable);
//# sourceMappingURL=materialized-view.model.js.map