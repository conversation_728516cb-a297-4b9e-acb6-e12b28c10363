"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MembershipsArgs = exports.MembershipsFilter = void 0;
const graphql_1 = require("@nestjs/graphql");
const pagination_args_1 = require("../../common/args/pagination.args");
const membership_model_1 = require("../models/membership.model");
let MembershipsFilter = class MembershipsFilter {
};
exports.MembershipsFilter = MembershipsFilter;
__decorate([
    (0, graphql_1.Field)({
        nullable: true,
    }),
    __metadata("design:type", String)
], MembershipsFilter.prototype, "organisationId", void 0);
__decorate([
    (0, graphql_1.Field)(() => [membership_model_1.MembershipStatus], {
        nullable: true,
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], MembershipsFilter.prototype, "status", void 0);
__decorate([
    (0, graphql_1.Field)(() => [membership_model_1.MembershipPermission], {
        nullable: true,
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], MembershipsFilter.prototype, "permission", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, {
        nullable: true,
        defaultValue: null,
    }),
    __metadata("design:type", Boolean)
], MembershipsFilter.prototype, "isPrimary", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, {
        nullable: true,
        defaultValue: null,
    }),
    __metadata("design:type", Boolean)
], MembershipsFilter.prototype, "includeActiveProfile", void 0);
exports.MembershipsFilter = MembershipsFilter = __decorate([
    (0, graphql_1.InputType)()
], MembershipsFilter);
let MembershipsArgs = class MembershipsArgs extends pagination_args_1.PaginationArgs {
};
exports.MembershipsArgs = MembershipsArgs;
__decorate([
    (0, graphql_1.Field)(() => MembershipsFilter, { nullable: true }),
    __metadata("design:type", MembershipsFilter)
], MembershipsArgs.prototype, "filter", void 0);
exports.MembershipsArgs = MembershipsArgs = __decorate([
    (0, graphql_1.ArgsType)()
], MembershipsArgs);
//# sourceMappingURL=memberships.args.js.map