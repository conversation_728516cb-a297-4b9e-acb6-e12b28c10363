"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MembershipsResolverHelper = void 0;
const membership_model_1 = require("../models/membership.model");
const memberships_service_rights_1 = require("./memberships.service.rights");
const common_1 = require("@nestjs/common");
let MembershipsResolverHelper = class MembershipsResolverHelper {
    getRequiredPermissionUpdateRights(permissions) {
        const requiredRights = [memberships_service_rights_1.MembershipRight.UpdatePermissions];
        if (permissions.includes(membership_model_1.MembershipPermission.Admin)) {
            requiredRights.push(memberships_service_rights_1.MembershipRight.AddAdminPermission);
        }
        if (permissions.includes(membership_model_1.MembershipPermission.HiddenAdmin)) {
            requiredRights.push(memberships_service_rights_1.MembershipRight.AddAdminPermission);
        }
        if (permissions.includes(membership_model_1.MembershipPermission.Manager)) {
            requiredRights.push(memberships_service_rights_1.MembershipRight.AddManagerPermission);
        }
        if (permissions.includes(membership_model_1.MembershipPermission.Editor)) {
            requiredRights.push(memberships_service_rights_1.MembershipRight.AddEditorPermission);
        }
        return requiredRights;
    }
    getRequiredPermissionRemoveRights(permissions) {
        const requiredRights = [memberships_service_rights_1.MembershipRight.RemoveMembership];
        if (permissions.includes(membership_model_1.MembershipPermission.Admin)) {
            requiredRights.push(memberships_service_rights_1.MembershipRight.RemoveAdminMembership);
        }
        if (permissions.includes(membership_model_1.MembershipPermission.HiddenAdmin)) {
            requiredRights.push(memberships_service_rights_1.MembershipRight.RemoveAdminMembership);
        }
        if (permissions.includes(membership_model_1.MembershipPermission.Manager)) {
            requiredRights.push(memberships_service_rights_1.MembershipRight.RemoveManagerMembership);
        }
        if (permissions.includes(membership_model_1.MembershipPermission.Editor)) {
            requiredRights.push(memberships_service_rights_1.MembershipRight.RemoveEditorMembership);
        }
        return requiredRights;
    }
};
exports.MembershipsResolverHelper = MembershipsResolverHelper;
exports.MembershipsResolverHelper = MembershipsResolverHelper = __decorate([
    (0, common_1.Injectable)()
], MembershipsResolverHelper);
//# sourceMappingURL=memberships.resolver.helper.js.map