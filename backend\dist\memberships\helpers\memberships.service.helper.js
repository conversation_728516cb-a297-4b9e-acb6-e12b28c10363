"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MembershipsServiceHelper = void 0;
const sequelize_1 = require("sequelize");
const common_1 = require("@nestjs/common");
const organisations_service_1 = require("../../organisations/organisations.service");
const profiles_service_1 = require("../../profiles/profiles.service");
const underscore_1 = require("../../common/helpers/underscore");
let MembershipsServiceHelper = class MembershipsServiceHelper {
    static getQueryParams(membershipArgs) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        const queryParams = {};
        if ((_a = membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.filter) === null || _a === void 0 ? void 0 : _a.organisationId) {
            queryParams['organisationId'] = membershipArgs.filter.organisationId;
        }
        if ((_b = membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.filter) === null || _b === void 0 ? void 0 : _b.profileId) {
            queryParams['profileId'] = membershipArgs.filter.profileId;
        }
        if (((_d = (_c = membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.filter) === null || _c === void 0 ? void 0 : _c.status) === null || _d === void 0 ? void 0 : _d.length) > 0) {
            queryParams['status'] = {
                [sequelize_1.Op.or]: membershipArgs.filter.status,
            };
        }
        if (((_f = (_e = membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.filter) === null || _e === void 0 ? void 0 : _e.permission) === null || _f === void 0 ? void 0 : _f.length) > 0) {
            queryParams['permissions'] = {
                [sequelize_1.Op.overlap]: membershipArgs.filter.permission,
            };
        }
        if (((_g = membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.filter) === null || _g === void 0 ? void 0 : _g.isPrimary) !== undefined &&
            ((_h = membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.filter) === null || _h === void 0 ? void 0 : _h.isPrimary) !== null) {
            queryParams['isPrimary'] = (_j = membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.filter) === null || _j === void 0 ? void 0 : _j.isPrimary;
        }
        return queryParams;
    }
    static filter(memberships, membershipArgs) {
        if (!memberships || memberships.length === 0)
            return [];
        let result = memberships;
        if ((membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.filter.status.length) > 0) {
            result = result.filter(membership => membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.filter.status.includes(membership.status));
        }
        if ((membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.filter.permission.length) > 0) {
            result = result.filter(membership => underscore_1.Underscore.includesAny(membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.filter.permission, membership.permissions));
        }
        if (membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.filter.organisationId) {
            result = result.filter(membership => membership.organisationId === (membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.filter.organisationId));
        }
        return result;
    }
};
exports.MembershipsServiceHelper = MembershipsServiceHelper;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], MembershipsServiceHelper.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], MembershipsServiceHelper.prototype, "profilesService", void 0);
exports.MembershipsServiceHelper = MembershipsServiceHelper = __decorate([
    (0, common_1.Injectable)()
], MembershipsServiceHelper);
//# sourceMappingURL=memberships.service.helper.js.map