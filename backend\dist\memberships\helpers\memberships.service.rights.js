"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MembershipsServiceRights = exports.MembershipRight = void 0;
const common_1 = require("@nestjs/common");
const organisations_service_1 = require("../../organisations/organisations.service");
const membership_model_1 = require("../models/membership.model");
const underscore_1 = require("../../common/helpers/underscore");
const error_1 = require("../../common/helpers/error");
var MembershipRight;
(function (MembershipRight) {
    MembershipRight["UpdateStatus"] = "UpdateStatus";
    MembershipRight["UpdateFollowerStatus"] = "UpdateFollowerStatus";
    MembershipRight["RemoveMembership"] = "RemoveMembership";
    MembershipRight["RemoveAdminMembership"] = "RemoveAdminMembership";
    MembershipRight["RemoveManagerMembership"] = "RemoveManagerMembership";
    MembershipRight["RemoveEditorMembership"] = "RemoveEditorMembership";
    MembershipRight["UpdatePermissions"] = "UpdatePermissions";
    MembershipRight["AddAdminPermission"] = "AddAdminPermission";
    MembershipRight["AddManagerPermission"] = "AddManagerPermission";
    MembershipRight["AddEditorPermission"] = "AddEditorPermission";
    MembershipRight["CreatePost"] = "CreatePost";
    MembershipRight["UpdatePost"] = "UpdatePost";
    MembershipRight["RemovePost"] = "RemovePost";
    MembershipRight["CreateEvent"] = "CreateEvent";
    MembershipRight["UpdateEvent"] = "UpdateEvent";
    MembershipRight["RemoveEvent"] = "RemoveEvent";
    MembershipRight["CreateIncentive"] = "CreateIncentive";
    MembershipRight["UpdateIncentive"] = "UpdateIncentive";
    MembershipRight["RemoveIncentive"] = "RemoveIncentive";
    MembershipRight["RegisterToIncentive"] = "RegisterToIncentive";
    MembershipRight["InviteToIncentive"] = "InviteToIncentive";
    MembershipRight["ApproveBlockIncentiveParticipant"] = "ApproveBlockIncentiveParticipant";
    MembershipRight["RemoveIncentiveParticipant"] = "RemoveIncentiveParticipant";
    MembershipRight["CreateWebinar"] = "CreateWebinar";
    MembershipRight["UpdateWebinar"] = "UpdateWebinar";
    MembershipRight["RemoveWebinar"] = "RemoveWebinar";
    MembershipRight["RegisterToWebinar"] = "RegisterToWebinar";
    MembershipRight["InviteToWebinar"] = "InviteToWebinar";
    MembershipRight["UpdateWebinarParticipantStatus"] = "UpdateWebinarParticipantStatus";
    MembershipRight["RemoveWebinarParticipant"] = "RemoveWebinarParticipant";
    MembershipRight["CreateEventInvitation"] = "CreateEventInvitation";
    MembershipRight["RemoveEventInvitation"] = "RemoveEventInvitation";
    MembershipRight["AcceptRejectEventInvitation"] = "AcceptRejectEventInvitation";
    MembershipRight["UpdateOrganisation"] = "UpdateOrganisation";
    MembershipRight["UpdateOrganisationPage"] = "UpdateOrganisationPage";
    MembershipRight["RemoveOrganisation"] = "RemoveOrganisation";
    MembershipRight["CreatePartnershipRequest"] = "CreatePartnershipRequest";
    MembershipRight["ApprovePartnershipRequest"] = "ApprovePartnershipRequest";
    MembershipRight["DeclinePartnershipRequest"] = "DeclinePartnershipRequest";
    MembershipRight["RemovePartnershipRequest"] = "RemovePartnershipRequest";
    MembershipRight["ChangeOwner"] = "ChangeOwner";
})(MembershipRight || (exports.MembershipRight = MembershipRight = {}));
let MembershipsServiceRights = class MembershipsServiceRights {
    constructor() {
        this.permissions = {
            [MembershipRight.CreateEventInvitation]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.RemoveEventInvitation]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.AcceptRejectEventInvitation]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.CreatePost]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.UpdatePost]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.RemovePost]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.CreateEvent]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.UpdateEvent]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.RemoveEvent]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
            ],
            [MembershipRight.CreateIncentive]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.UpdateIncentive]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.RemoveIncentive]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
            ],
            [MembershipRight.RegisterToIncentive]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
                membership_model_1.MembershipPermission.Staff,
            ],
            [MembershipRight.InviteToIncentive]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.ApproveBlockIncentiveParticipant]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.RemoveIncentiveParticipant]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.CreateWebinar]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.UpdateWebinar]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.RemoveWebinar]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
            ],
            [MembershipRight.RegisterToWebinar]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
                membership_model_1.MembershipPermission.Staff,
            ],
            [MembershipRight.InviteToWebinar]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.UpdateWebinarParticipantStatus]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.RemoveWebinarParticipant]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.UpdateStatus]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
            ],
            [MembershipRight.UpdateFollowerStatus]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.RemoveMembership]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
            ],
            [MembershipRight.RemoveAdminMembership]: [membership_model_1.MembershipPermission.Owner],
            [MembershipRight.RemoveManagerMembership]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
            ],
            [MembershipRight.RemoveEditorMembership]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
            ],
            [MembershipRight.UpdatePermissions]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
            ],
            [MembershipRight.AddAdminPermission]: [membership_model_1.MembershipPermission.Owner],
            [MembershipRight.AddManagerPermission]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
            ],
            [MembershipRight.AddEditorPermission]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
            ],
            [MembershipRight.UpdateOrganisation]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.UpdateOrganisationPage]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
                membership_model_1.MembershipPermission.Manager,
                membership_model_1.MembershipPermission.Editor,
            ],
            [MembershipRight.RemoveOrganisation]: [membership_model_1.MembershipPermission.Owner],
            [MembershipRight.CreatePartnershipRequest]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
            ],
            [MembershipRight.ApprovePartnershipRequest]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
            ],
            [MembershipRight.DeclinePartnershipRequest]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
            ],
            [MembershipRight.RemovePartnershipRequest]: [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
            ],
            [MembershipRight.ChangeOwner]: [membership_model_1.MembershipPermission.Owner],
        };
    }
    hasAccess(right, permissions) {
        return underscore_1.Underscore.includesAny(this.permissions[right], permissions);
    }
    async checkRights(organisationId, options) {
        const organisation = await this.organisationsService.findById(organisationId, {
            includeParams: [
                {
                    model: membership_model_1.Membership,
                    as: 'memberships',
                    where: {
                        profileId: options.currentProfileId,
                        status: membership_model_1.MembershipStatus.Active,
                    },
                },
            ],
            transaction: options.transaction,
        });
        if (!organisation) {
            this.errorHelper.throwHttpException(`MembershipsServiceRights.checkRights`, `You don't have the needed right to do this operation`);
        }
        const membership = organisation.memberships.find(membership => membership.profileId === options.currentProfileId);
        if (!membership) {
            this.errorHelper.throwHttpException(`MembershipsServiceRights.checkRights`, 'Membership not exists or current user not have the needed right');
        }
        for (const right of options.rights) {
            if (!underscore_1.Underscore.includesAny(this.permissions[right], membership.permissions)) {
                const permissionsText = membership.permissions.join(', ');
                this.errorHelper.throwHttpException(`MembershipsServiceRights.checkRights`, `Error: ${right} action is not allowed with current permission(s) (${permissionsText})`);
            }
        }
        return true;
    }
};
exports.MembershipsServiceRights = MembershipsServiceRights;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], MembershipsServiceRights.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], MembershipsServiceRights.prototype, "errorHelper", void 0);
exports.MembershipsServiceRights = MembershipsServiceRights = __decorate([
    (0, common_1.Injectable)()
], MembershipsServiceRights);
//# sourceMappingURL=memberships.service.rights.js.map