"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MembershipsModule = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const membership_model_1 = require("./models/membership.model");
const memberships_service_1 = require("./memberships.service");
const profiles_module_1 = require("../profiles/profiles.module");
const memberships_resolver_1 = require("./memberships.resolver");
const organisations_module_1 = require("../organisations/organisations.module");
const memberships_service_helper_1 = require("./helpers/memberships.service.helper");
const notifications_module_1 = require("../notifications/notifications.module");
const memberships_service_rights_1 = require("./helpers/memberships.service.rights");
const common_module_1 = require("../common/common.module");
const memberships_resolver_helper_1 = require("./helpers/memberships.resolver.helper");
const followers_module_1 = require("../followers/followers.module");
const partnerships_module_1 = require("../partnerships/partnerships.module");
const memberships_repository_1 = require("./memberships.repository");
const stream_followers_module_1 = require("../feeds-followers/stream-followers.module");
const partnership_requests_module_1 = require("../partnership-requests/partnership-requests.module");
const partner_organisations_module_1 = require("../partner-organisations/partner-organisations.module");
let MembershipsModule = class MembershipsModule {
};
exports.MembershipsModule = MembershipsModule;
exports.MembershipsModule = MembershipsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            sequelize_1.SequelizeModule.forFeature([membership_model_1.Membership]),
            (0, common_1.forwardRef)(() => common_module_1.CommonModule),
            (0, common_1.forwardRef)(() => profiles_module_1.ProfilesModule),
            (0, common_1.forwardRef)(() => organisations_module_1.OrganisationsModule),
            (0, common_1.forwardRef)(() => notifications_module_1.NotificationsModule),
            (0, common_1.forwardRef)(() => partnerships_module_1.PartnershipsModule),
            (0, common_1.forwardRef)(() => followers_module_1.FollowersModule),
            (0, common_1.forwardRef)(() => stream_followers_module_1.StreamFollowersModule),
            (0, common_1.forwardRef)(() => partnership_requests_module_1.PartnershipRequestsModule),
            (0, common_1.forwardRef)(() => partner_organisations_module_1.PartnerOrganisationsModule),
        ],
        providers: [
            memberships_service_1.MembershipsService,
            memberships_repository_1.MembershipsRepository,
            memberships_resolver_helper_1.MembershipsResolverHelper,
            memberships_service_helper_1.MembershipsServiceHelper,
            memberships_service_rights_1.MembershipsServiceRights,
            memberships_resolver_1.MembershipsResolver,
        ],
        exports: [
            memberships_service_1.MembershipsService,
            memberships_service_helper_1.MembershipsServiceHelper,
            memberships_service_rights_1.MembershipsServiceRights,
        ],
    })
], MembershipsModule);
//# sourceMappingURL=memberships.module.js.map