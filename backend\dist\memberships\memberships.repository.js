"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MembershipsRepository = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const membership_model_1 = require("./models/membership.model");
const pagination_1 = require("../common/helpers/pagination");
const sequelize_2 = require("sequelize");
let MembershipsRepository = class MembershipsRepository {
    async findMemberships(profileId, filter, pagination) {
        const extraQueryParams = {
            profileId: {
                [sequelize_2.Op.ne]: profileId,
            },
        };
        if (filter === null || filter === void 0 ? void 0 : filter.includeActiveProfile) {
            delete extraQueryParams.profileId;
        }
        if (filter === null || filter === void 0 ? void 0 : filter.profileId) {
            extraQueryParams.profileId = filter.profileId;
        }
        if (filter === null || filter === void 0 ? void 0 : filter.organisationId) {
            extraQueryParams.organisationId = filter.organisationId;
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.status) && filter.status.length > 0) {
            extraQueryParams.status = {
                [sequelize_2.Op.in]: filter.status,
            };
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.permission) && filter.permission.length > 0) {
            extraQueryParams.permissions = {
                [sequelize_2.Op.overlap]: filter.permission,
            };
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.isPrimary) !== null && (filter === null || filter === void 0 ? void 0 : filter.isPrimary) !== undefined) {
            extraQueryParams.isPrimary = filter.isPrimary;
        }
        return await new pagination_1.PaginationHelper().getPaginatedResults({
            model: membership_model_1.Membership,
            pagination,
            extraQueryParams,
            excludeIds: [],
        });
    }
};
exports.MembershipsRepository = MembershipsRepository;
__decorate([
    (0, sequelize_1.InjectModel)(membership_model_1.Membership),
    __metadata("design:type", Object)
], MembershipsRepository.prototype, "membershipModel", void 0);
exports.MembershipsRepository = MembershipsRepository = __decorate([
    (0, common_1.Injectable)()
], MembershipsRepository);
//# sourceMappingURL=memberships.repository.js.map