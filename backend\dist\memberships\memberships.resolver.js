"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MembershipsResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const membership_model_1 = require("./models/membership.model");
const profile_model_1 = require("../profiles/models/profile.model");
const profiles_service_1 = require("../profiles/profiles.service");
const organisation_model_1 = require("../organisations/models/organisation.model");
const organisations_service_1 = require("../organisations/organisations.service");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const memberships_service_1 = require("./memberships.service");
const memberships_service_rights_1 = require("./helpers/memberships.service.rights");
const memberships_resolver_helper_1 = require("./helpers/memberships.resolver.helper");
const error_1 = require("../common/helpers/error");
const partner_organisations_service_1 = require("../partner-organisations/partner-organisations.service");
const partner_organisation_model_1 = require("../partner-organisations/models/partner-organisation.model");
const partner_organisations_args_1 = require("../partner-organisations/args/partner-organisations.args");
let MembershipsResolver = class MembershipsResolver {
    async updateMembershipStatus(user, organisationId, profileId, actionType) {
        this.logger.verbose('MembershipsResolver.updateMembershipStatus (mutation)', {
            user: user.toLogObject(),
            organisationId,
            profileId,
            actionType,
        });
        await this.membershipsServiceRights.checkRights(organisationId, {
            currentProfileId: user.profileId,
            rights: [memberships_service_rights_1.MembershipRight.UpdateStatus],
        });
        return this.membershipsService.updateMembershipStatus(organisationId, profileId, actionType, {
            currentProfileId: user.profileId,
        });
    }
    async removeMembership(user, organisationId, profileId) {
        this.logger.verbose('MembershipsResolver.removeMembership (mutation)', {
            user: user.toLogObject(),
            organisationId,
            profileId,
        });
        const membership = await this.membershipsService.findOne({
            profileId,
            organisationId,
        });
        if (!membership) {
            this.errorHelper.throwHttpException(`OrganisationsService.removeMembership`, `Membership not found`, common_1.HttpStatus.NOT_FOUND);
        }
        await this.membershipsServiceRights.checkRights(organisationId, {
            currentProfileId: user.profileId,
            rights: this.membershipsResolverHelper.getRequiredPermissionRemoveRights(membership.permissions),
        });
        return this.membershipsService.removeMembership(membership);
    }
    async addMembershipPermissions(user, organisationId, profileId, status, permissions) {
        this.logger.verbose('MembershipsResolver.addMembershipPermissions (mutation)', {
            user: user.toLogObject(),
            organisationId,
            profileId,
            permissions,
        });
        await this.membershipsServiceRights.checkRights(organisationId, {
            currentProfileId: user.profileId,
            rights: this.membershipsResolverHelper.getRequiredPermissionUpdateRights(permissions),
        });
        return this.membershipsService.addMembershipPermissions(organisationId, profileId, permissions, status, {
            currentProfileId: user.profileId,
        });
    }
    async updateMembershipPermissions(user, organisationId, profileId, status, permissions) {
        this.logger.verbose('MembershipsResolver.updateMembershipPermissions (mutation)', {
            user: user.toLogObject(),
            organisationId,
            profileId,
            permissions,
        });
        await this.membershipsServiceRights.checkRights(organisationId, {
            currentProfileId: user.profileId,
            rights: this.membershipsResolverHelper.getRequiredPermissionUpdateRights(permissions),
        });
        return this.membershipsService.updateMembershipPermissions(organisationId, profileId, permissions, status, {
            currentProfileId: user.profileId,
        });
    }
    async profile(membership) {
        if (!membership.profileId)
            return null;
        if (membership.profile)
            return membership.profile;
        this.logger.verbose('MembershipsResolver.profile (field resolver)', {
            membershipId: membership.id,
        });
        return this.profilesService.findById(membership.profileId);
    }
    async organisation(membership) {
        if (!membership.organisationId)
            return null;
        if (membership.organisation)
            return membership.organisation;
        this.logger.verbose('MembershipsResolver.organisation (field resolver)', {
            membershipId: membership.id,
        });
        return this.organisationsService.findById(membership.organisationId, {
            useCache: true,
        });
    }
    async partnerOrganisation(membership) {
        if (!membership.organisationId)
            return null;
        this.logger.verbose('MembershipsResolver.partnerOrganisation (field resolver)', {
            membershipId: membership.id,
        });
        const partnerOrg = await this.partnerOrganisationsService.findByChildOrgId(membership.organisationId);
        if (partnerOrg &&
            partnerOrg.status === partner_organisations_args_1.PartnerOrganisationStatus.Approved) {
            return partnerOrg;
        }
        return null;
    }
};
exports.MembershipsResolver = MembershipsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], MembershipsResolver.prototype, "membershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], MembershipsResolver.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], MembershipsResolver.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_rights_1.MembershipsServiceRights)),
    __metadata("design:type", memberships_service_rights_1.MembershipsServiceRights)
], MembershipsResolver.prototype, "membershipsServiceRights", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_resolver_helper_1.MembershipsResolverHelper)),
    __metadata("design:type", memberships_resolver_helper_1.MembershipsResolverHelper)
], MembershipsResolver.prototype, "membershipsResolverHelper", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => partner_organisations_service_1.PartnerOrganisationsService)),
    __metadata("design:type", partner_organisations_service_1.PartnerOrganisationsService)
], MembershipsResolver.prototype, "partnerOrganisationsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], MembershipsResolver.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], MembershipsResolver.prototype, "errorHelper", void 0);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId')),
    __param(2, (0, graphql_1.Args)('profileId')),
    __param(3, (0, graphql_1.Args)('actionType', { type: () => membership_model_1.MembershipActionType })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String]),
    __metadata("design:returntype", Promise)
], MembershipsResolver.prototype, "updateMembershipStatus", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId')),
    __param(2, (0, graphql_1.Args)('profileId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], MembershipsResolver.prototype, "removeMembership", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId')),
    __param(2, (0, graphql_1.Args)('profileId')),
    __param(3, (0, graphql_1.Args)('status', {
        type: () => membership_model_1.MembershipStatus,
        nullable: true,
        defaultValue: membership_model_1.MembershipStatus.Active,
    })),
    __param(4, (0, graphql_1.Args)('permissions', { type: () => [membership_model_1.MembershipPermission] })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String, Array]),
    __metadata("design:returntype", Promise)
], MembershipsResolver.prototype, "addMembershipPermissions", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId')),
    __param(2, (0, graphql_1.Args)('profileId')),
    __param(3, (0, graphql_1.Args)('status', {
        type: () => membership_model_1.MembershipStatus,
        nullable: true,
        defaultValue: membership_model_1.MembershipStatus.Active,
    })),
    __param(4, (0, graphql_1.Args)('permissions', { type: () => [membership_model_1.MembershipPermission] })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String, Array]),
    __metadata("design:returntype", Promise)
], MembershipsResolver.prototype, "updateMembershipPermissions", null);
__decorate([
    (0, graphql_1.ResolveField)('profile', () => profile_model_1.Profile),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [membership_model_1.Membership]),
    __metadata("design:returntype", Promise)
], MembershipsResolver.prototype, "profile", null);
__decorate([
    (0, graphql_1.ResolveField)('organisation', () => organisation_model_1.Organisation, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [membership_model_1.Membership]),
    __metadata("design:returntype", Promise)
], MembershipsResolver.prototype, "organisation", null);
__decorate([
    (0, graphql_1.ResolveField)('partnerOrganisation', () => partner_organisation_model_1.PartnerOrganisation, {
        nullable: true,
    }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [membership_model_1.Membership]),
    __metadata("design:returntype", Promise)
], MembershipsResolver.prototype, "partnerOrganisation", null);
exports.MembershipsResolver = MembershipsResolver = __decorate([
    (0, graphql_1.Resolver)(() => membership_model_1.Membership)
], MembershipsResolver);
//# sourceMappingURL=memberships.resolver.js.map