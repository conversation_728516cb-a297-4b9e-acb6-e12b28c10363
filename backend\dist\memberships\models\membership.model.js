"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Membership = exports.MembershipAction = exports.MembershipActionType = exports.MembershipStatus = exports.MembershipPermission = void 0;
exports.highestPermission = highestPermission;
const sequelize_typescript_1 = require("sequelize-typescript");
const graphql_1 = require("@nestjs/graphql");
const profile_model_1 = require("../../profiles/models/profile.model");
const short_uuid_1 = __importDefault(require("short-uuid"));
const sequelize_1 = require("sequelize");
const organisation_model_1 = require("../../organisations/models/organisation.model");
const notification_model_1 = require("../../notifications/models/notification.model");
var MembershipPermission;
(function (MembershipPermission) {
    MembershipPermission["Owner"] = "Owner";
    MembershipPermission["OwnerPending"] = "OwnerPending";
    MembershipPermission["Admin"] = "Admin";
    MembershipPermission["HiddenAdmin"] = "HiddenAdmin";
    MembershipPermission["Manager"] = "Manager";
    MembershipPermission["Editor"] = "Editor";
    MembershipPermission["Staff"] = "Staff";
    MembershipPermission["Linked"] = "Linked";
    MembershipPermission["InviteMembersToEvents"] = "InviteMembersToEvents";
    MembershipPermission["Member"] = "Member";
})(MembershipPermission || (exports.MembershipPermission = MembershipPermission = {}));
(0, graphql_1.registerEnumType)(MembershipPermission, { name: 'MembershipPermission' });
function highestPermission(permissions) {
    if ((permissions || []).includes(MembershipPermission.Owner))
        return MembershipPermission.Owner;
    if ((permissions || []).includes(MembershipPermission.Admin))
        return MembershipPermission.Admin;
    if ((permissions || []).includes(MembershipPermission.HiddenAdmin))
        return MembershipPermission.HiddenAdmin;
    if ((permissions || []).includes(MembershipPermission.Manager))
        return MembershipPermission.Manager;
    if ((permissions || []).includes(MembershipPermission.Editor))
        return MembershipPermission.Editor;
    if ((permissions || []).includes(MembershipPermission.Staff))
        return MembershipPermission.Staff;
    return MembershipPermission.Linked;
}
var MembershipStatus;
(function (MembershipStatus) {
    MembershipStatus["Active"] = "Active";
    MembershipStatus["Pending"] = "Pending";
    MembershipStatus["Rejected"] = "Rejected";
    MembershipStatus["Inactive"] = "Inactive";
})(MembershipStatus || (exports.MembershipStatus = MembershipStatus = {}));
(0, graphql_1.registerEnumType)(MembershipStatus, { name: 'MembershipStatus' });
var MembershipActionType;
(function (MembershipActionType) {
    MembershipActionType["Accept"] = "Accept";
    MembershipActionType["Reject"] = "Reject";
})(MembershipActionType || (exports.MembershipActionType = MembershipActionType = {}));
(0, graphql_1.registerEnumType)(MembershipActionType, { name: 'MembershipActionType' });
let MembershipAction = class MembershipAction {
};
exports.MembershipAction = MembershipAction;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], MembershipAction.prototype, "profileId", void 0);
__decorate([
    (0, graphql_1.Field)(() => MembershipActionType),
    __metadata("design:type", String)
], MembershipAction.prototype, "actionType", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], MembershipAction.prototype, "actionDate", void 0);
exports.MembershipAction = MembershipAction = __decorate([
    (0, graphql_1.ObjectType)()
], MembershipAction);
let Membership = class Membership extends sequelize_typescript_1.Model {
};
exports.Membership = Membership;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID, { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], Membership.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Membership.prototype, "organisationName", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Membership.prototype, "isPrimary", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({
        defaultValue: '',
    }),
    __metadata("design:type", String)
], Membership.prototype, "position", void 0);
__decorate([
    (0, graphql_1.Field)(() => [MembershipPermission], { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], Membership.prototype, "permissions", void 0);
__decorate([
    (0, graphql_1.Field)(() => MembershipStatus, { nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Membership.prototype, "status", void 0);
__decorate([
    (0, graphql_1.Field)(() => [MembershipAction], { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.JSON),
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], Membership.prototype, "actions", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Membership.prototype, "profileId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, 'profileId'),
    __metadata("design:type", profile_model_1.Profile)
], Membership.prototype, "profile", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Membership.prototype, "organisationId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, 'organisationId'),
    __metadata("design:type", organisation_model_1.Organisation)
], Membership.prototype, "organisation", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true, defaultValue: false }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Membership.prototype, "isAutoApprove", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => notification_model_1.Notification, {
        foreignKey: 'membershipId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", Array)
], Membership.prototype, "notifications", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Membership.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Membership.prototype, "updatedAt", void 0);
exports.Membership = Membership = __decorate([
    sequelize_typescript_1.Table,
    (0, graphql_1.ObjectType)()
], Membership);
//# sourceMappingURL=membership.model.js.map