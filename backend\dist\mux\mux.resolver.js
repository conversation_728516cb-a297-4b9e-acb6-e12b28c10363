"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MuxResolver = exports.MuxDownloadUrlResponse = void 0;
const graphql_1 = require("@nestjs/graphql");
const mux_service_1 = require("./mux.service");
const common_1 = require("@nestjs/common");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const profile_model_1 = require("../profiles/models/profile.model");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
let MuxDownloadUrlResponse = class MuxDownloadUrlResponse {
};
exports.MuxDownloadUrlResponse = MuxDownloadUrlResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], MuxDownloadUrlResponse.prototype, "url", void 0);
exports.MuxDownloadUrlResponse = MuxDownloadUrlResponse = __decorate([
    (0, graphql_1.ObjectType)()
], MuxDownloadUrlResponse);
let MuxResolver = class MuxResolver {
    async getMuxDownloadUrl(user, assetId) {
        this.logger.verbose('MuxResolver.getMuxDownloadUrl (query)', {
            user: user.id,
            assetId,
        });
        const url = await this.muxService.getMuxDownloadUrl(user, assetId);
        return { url };
    }
};
exports.MuxResolver = MuxResolver;
__decorate([
    (0, common_1.Inject)(mux_service_1.MuxService),
    __metadata("design:type", mux_service_1.MuxService)
], MuxResolver.prototype, "muxService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], MuxResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Query)(() => MuxDownloadUrlResponse, { name: 'getMuxDownloadUrl', description: 'Generates a temporary MP4 download URL for a Mux video asset.' }),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('assetId', { type: () => String })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [profile_model_1.Profile, String]),
    __metadata("design:returntype", Promise)
], MuxResolver.prototype, "getMuxDownloadUrl", null);
exports.MuxResolver = MuxResolver = __decorate([
    (0, graphql_1.Resolver)()
], MuxResolver);
//# sourceMappingURL=mux.resolver.js.map