"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var MuxService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MuxService = void 0;
const common_1 = require("@nestjs/common");
const mux_node_1 = __importDefault(require("@mux/mux-node"));
let MuxService = MuxService_1 = class MuxService {
    constructor() {
        this.logger = new common_1.Logger(MuxService_1.name);
    }
    async getMuxDownloadUrl(currentUser, assetId) {
        this.logger.log(`User ${currentUser.id} requesting download URL for asset ${assetId}`);
        const { Video } = new mux_node_1.default(process.env.MUX_API_KEY, process.env.MUX_SECRET);
        const MAX_ATTEMPTS = 40;
        try {
            const existingAsset = await Video.Assets.get(assetId);
            if (existingAsset.master && existingAsset.master.url && existingAsset.master.status === 'ready') {
                return existingAsset.master.url;
            }
            const asset = await Video.Assets.updateMasterAccess(assetId, {
                master_access: 'temporary'
            });
            if (!asset.master || !asset.master.url || asset.master.status !== 'ready') {
                return await this.getMasterUrl(assetId, Video, MAX_ATTEMPTS);
            }
            return asset.master.url;
        }
        catch (error) {
            if (error.messages && error.messages[0] === 'Download already exists') {
                return await this.getMasterUrl(assetId, Video, MAX_ATTEMPTS);
            }
            else {
                this.logger.error(`Error fetching Mux asset or generating download URL for asset ${assetId}: ${error.message}`, error.stack);
                throw new common_1.InternalServerErrorException('Failed to generate video download URL.');
            }
        }
    }
    async getMasterUrl(assetId, Video, attemptsLeft = 40) {
        if (attemptsLeft <= 0) {
            this.logger.error(`Exceeded maximum retries while fetching master URL for asset ${assetId}`);
            throw new common_1.InternalServerErrorException('Failed to generate video download URL: max retries exceeded.');
        }
        const asset = await Video.Assets.get(assetId);
        if (!asset.master || !asset.master.url || asset.master.status !== 'ready') {
            await new Promise(resolve => setTimeout(resolve, 4000));
            return await this.getMasterUrl(assetId, Video, attemptsLeft - 1);
        }
        return asset.master.url;
    }
};
exports.MuxService = MuxService;
exports.MuxService = MuxService = MuxService_1 = __decorate([
    (0, common_1.Injectable)()
], MuxService);
//# sourceMappingURL=mux.service.js.map