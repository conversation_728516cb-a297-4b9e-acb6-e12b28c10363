"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationMessage = exports.PushMessagePreferenceType = exports.PushMessageTypeData = exports.NotificationData = exports.NotificationsArgs = exports.NotificationsFilter = exports.NotificationDeviceType = exports.NotificationType = void 0;
const graphql_1 = require("@nestjs/graphql");
const pagination_args_1 = require("../../common/args/pagination.args");
var NotificationType;
(function (NotificationType) {
    NotificationType["InvitationReceived"] = "InvitationReceived";
    NotificationType["InvitationAccepted"] = "InvitationAccepted";
    NotificationType["OrgPartnerRequestReceived"] = "OrgPartnerRequestReceived";
    NotificationType["OrgPartnerAcceptedSender"] = "OrgPartnerAcceptedSender";
    NotificationType["OrgPartnerAcceptedReceiver"] = "OrgPartnerAcceptedReceiver";
    NotificationType["OrgPartnerRequestRejected"] = "OrgPartnerRequestRejected";
    NotificationType["PartnershipRequestReceived"] = "PartnershipRequestReceived";
    NotificationType["PartnershipRequestApproved"] = "PartnershipRequestApproved";
    NotificationType["PartnershipRequestApprovedByOrganisation"] = "PartnershipRequestApprovedByOrganisation";
    NotificationType["MembershipAccepted"] = "MembershipAccepted";
    NotificationType["OrganisationOwnershipRequested"] = "OrganisationOwnershipRequested";
    NotificationType["OrganisationOwnershipAccepted"] = "OrganisationOwnershipAccepted";
    NotificationType["MembershipRequested"] = "MembershipRequested";
    NotificationType["FollowerAccepted"] = "FollowerAccepted";
    NotificationType["MembershipPermissionsUpdated"] = "MembershipPermissionsUpdated";
    NotificationType["EventInvitationByGuest"] = "EventInvitationByGuest";
    NotificationType["EventInvitationByHosts"] = "EventInvitationByHosts";
    NotificationType["EventInvitationApproved"] = "EventInvitationApproved";
    NotificationType["NewEventUpdate"] = "NewEventUpdate";
    NotificationType["EventLocationChanged"] = "EventLocationChanged";
    NotificationType["EventDateTimeChanged"] = "EventDateTimeChanged";
    NotificationType["IncentiveDateChanged"] = "IncentiveDateChanged";
    NotificationType["IncentiveInvitationByParticipant"] = "IncentiveInvitationByParticipant";
    NotificationType["IncentiveInvitationByHosts"] = "IncentiveInvitationByHosts";
    NotificationType["IncentiveRegistrationRequested"] = "IncentiveRegistrationRequested";
    NotificationType["IncentiveRegistrationApproved"] = "IncentiveRegistrationApproved";
    NotificationType["NewIncentiveUpdate"] = "NewIncentiveUpdate";
    NotificationType["WebinarDateChanged"] = "WebinarDateChanged";
    NotificationType["WebinarInvitationByParticipant"] = "WebinarInvitationByParticipant";
    NotificationType["WebinarInvitationByHosts"] = "WebinarInvitationByHosts";
    NotificationType["WebinarParticipantAddedAsHostAdmin"] = "WebinarParticipantAddedAsHostAdmin";
    NotificationType["WebinarParticipantAddedAsHost"] = "WebinarParticipantAddedAsHost";
    NotificationType["WebinarParticipantAddedAsSpeaker"] = "WebinarParticipantAddedAsSpeaker";
    NotificationType["WebinarRegistrationRequested"] = "WebinarRegistrationRequested";
    NotificationType["WebinarRegistrationApproved"] = "WebinarRegistrationApproved";
    NotificationType["NewWebinarUpdate"] = "NewWebinarUpdate";
    NotificationType["NewFollower"] = "NewFollower";
    NotificationType["SuggestFollow"] = "SuggestFollow";
    NotificationType["PostComment"] = "PostComment";
    NotificationType["CommentMention"] = "CommentMention";
    NotificationType["PostMention"] = "PostMention";
    NotificationType["CommentReact"] = "CommentReact";
    NotificationType["PostReact"] = "PostReact";
    NotificationType["PostShared"] = "PostShared";
    NotificationType["OrgPostMention"] = "OrgPostMention";
    NotificationType["OrgCommentMention"] = "OrgCommentMention";
    NotificationType["RecurringPaymentReminder"] = "RecurringPaymentReminder";
    NotificationType["MembershipAutoApprove"] = "MembershipAutoApprove";
    NotificationType["ParentMembershipRequested"] = "ParentMembershipRequested";
    NotificationType["ParentMembershipAccepted"] = "ParentMembershipAccepted";
    NotificationType["ParentMembershipDeclined"] = "ParentMembershipDeclined";
    NotificationType["OrgPostReminder7Days"] = "OrgPostReminder7Days";
    NotificationType["OrgPostReminder14Days"] = "OrgPostReminder14Days";
    NotificationType["InactiveUserReminder"] = "InactiveUserReminder";
    NotificationType["InboxMessage"] = "InboxMessage";
    NotificationType["DailyLoginStreak"] = "DailyLoginStreak";
    NotificationType["NewTier"] = "NewTier";
    NotificationType["WeeklySummary"] = "WeeklySummary";
    NotificationType["NewAchievement"] = "NewAchievement";
    NotificationType["HighFiveAchievement"] = "HighFiveAchievement";
    NotificationType["NewOrganisationAchievement"] = "NewOrganisationAchievement";
    NotificationType["MonthlyAchievementSummary"] = "MonthlyAchievementSummary";
})(NotificationType || (exports.NotificationType = NotificationType = {}));
(0, graphql_1.registerEnumType)(NotificationType, { name: 'NotificationType' });
var NotificationDeviceType;
(function (NotificationDeviceType) {
    NotificationDeviceType["Web"] = "Web";
    NotificationDeviceType["Mobile"] = "Mobile";
})(NotificationDeviceType || (exports.NotificationDeviceType = NotificationDeviceType = {}));
(0, graphql_1.registerEnumType)(NotificationDeviceType, { name: 'NotificationDeviceType' });
let NotificationsFilter = class NotificationsFilter {
};
exports.NotificationsFilter = NotificationsFilter;
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], NotificationsFilter.prototype, "isRead", void 0);
exports.NotificationsFilter = NotificationsFilter = __decorate([
    (0, graphql_1.InputType)()
], NotificationsFilter);
let NotificationsArgs = class NotificationsArgs extends pagination_args_1.PaginationArgs {
};
exports.NotificationsArgs = NotificationsArgs;
__decorate([
    (0, graphql_1.Field)(() => NotificationsFilter, { nullable: true }),
    __metadata("design:type", NotificationsFilter)
], NotificationsArgs.prototype, "filter", void 0);
exports.NotificationsArgs = NotificationsArgs = __decorate([
    (0, graphql_1.ArgsType)()
], NotificationsArgs);
let NotificationData = class NotificationData {
};
exports.NotificationData = NotificationData;
exports.NotificationData = NotificationData = __decorate([
    (0, graphql_1.ObjectType)()
], NotificationData);
let PushMessageTypeData = class PushMessageTypeData {
};
exports.PushMessageTypeData = PushMessageTypeData;
exports.PushMessageTypeData = PushMessageTypeData = __decorate([
    (0, graphql_1.ObjectType)()
], PushMessageTypeData);
var PushMessagePreferenceType;
(function (PushMessagePreferenceType) {
    PushMessagePreferenceType["connections"] = "connections";
    PushMessagePreferenceType["invitations"] = "invitations";
    PushMessagePreferenceType["events"] = "events";
    PushMessagePreferenceType["incentives"] = "incentives";
    PushMessagePreferenceType["webinars"] = "webinars";
    PushMessagePreferenceType["mentionsInPosts"] = "mentionsInPosts";
    PushMessagePreferenceType["interaction"] = "interaction";
    PushMessagePreferenceType["organisationsYouManage"] = "organisationsYouManage";
    PushMessagePreferenceType["followSuggestions"] = "followSuggestions";
    PushMessagePreferenceType["posts"] = "posts";
    PushMessagePreferenceType["messages"] = "messages";
})(PushMessagePreferenceType || (exports.PushMessagePreferenceType = PushMessagePreferenceType = {}));
(0, graphql_1.registerEnumType)(PushMessagePreferenceType, {
    name: 'PushMessagePreferenceType',
});
exports.NotificationMessage = {
    InvitationReceived: {
        message: '$1 wants to connect with you. View your pending connection invitations.',
        type: PushMessagePreferenceType.connections,
    },
    InvitationAccepted: {
        message: 'You are now connected with $1',
        type: PushMessagePreferenceType.connections,
    },
    OrgPartnerRequestReceived: {
        message: '$1 has requested to become a Connected Organisation with $2. View this request.',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    OrgPartnerAcceptedSender: {
        message: '$1 has approved your request to become a Connected Organisation.',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    OrgPartnerAcceptedReceiver: {
        message: '$1 is now a Connected Organisation with $2.',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    OrgPartnerRequestRejected: {
        message: 'Your request to connect with $1 has been rejected.',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    PartnershipRequestReceived: {
        message: '$1 has requested to become a Connected Organisation with $2. View this request.',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    PartnershipRequestApproved: {
        message: '$1 approved a connection request from $2',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    PartnershipRequestApprovedByOrganisation: {
        message: '$1 is now a Connected Organisation with $2',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    MembershipAccepted: {
        message: 'You have been verified as an employee of $1',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    OrganisationOwnershipRequested: {
        message: '$1 has requested you take over as Owner of $2.',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    OrganisationOwnershipAccepted: {
        message: '$1 has agreed to become Owner of $2. You have reverted to an Admin role.',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    MembershipRequested: {
        message: 'MembershipRequested',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    FollowerAccepted: {
        message: '$1 has accepted your follow request. You can now explore their full page.',
        type: PushMessagePreferenceType.connections,
    },
    MembershipPermissionsUpdated: {
        message: 'You have been assigned as $1 of $2',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    EventInvitationByGuest: {
        message: '$1 has invited you to an event: $2',
        type: PushMessagePreferenceType.invitations,
    },
    EventInvitationByHosts: {
        message: '$1 has invited you to an event: $2',
        type: PushMessagePreferenceType.invitations,
    },
    EventInvitationApproved: {
        message: '$1 has accepted your request to attend $2',
        type: PushMessagePreferenceType.invitations,
    },
    NewEventUpdate: {
        message: '$1 has posted an update in $2',
        type: PushMessagePreferenceType.events,
    },
    EventLocationChanged: {
        message: '$1 has updated the location of $2',
        type: PushMessagePreferenceType.events,
    },
    EventDateTimeChanged: {
        message: '$1 has made a change to the date / time of $2',
        type: PushMessagePreferenceType.events,
    },
    IncentiveDateChanged: {
        message: '$1 has made a change to the dates of $2',
        type: PushMessagePreferenceType.incentives,
    },
    IncentiveInvitationByParticipant: {
        message: '$1 has invited you to an incentive: $2',
        type: PushMessagePreferenceType.invitations,
    },
    IncentiveInvitationByHosts: {
        message: '$1 has invited you to an incentive: $2',
        type: PushMessagePreferenceType.invitations,
    },
    IncentiveRegistrationRequested: {
        message: '$1 has requested to register in $2. View this request to confirm their participation',
        type: PushMessagePreferenceType.incentives,
    },
    IncentiveRegistrationApproved: {
        message: '$1 has accepted your request to register $2',
        type: PushMessagePreferenceType.incentives,
    },
    NewIncentiveUpdate: {
        message: '$1 has posted an update in $2',
        type: PushMessagePreferenceType.incentives,
    },
    WebinarDateChanged: {
        message: '$1 has made a change to the date / time of $2',
        type: PushMessagePreferenceType.webinars,
    },
    WebinarInvitationByParticipant: {
        message: '$1 has invited you to a webinar: $2',
        type: PushMessagePreferenceType.invitations,
    },
    WebinarInvitationByHosts: {
        message: '$1 has invited you to a webinar: $2',
        type: PushMessagePreferenceType.invitations,
    },
    WebinarParticipantAddedAsHostAdmin: {
        message: '$1 has assigned you as a Host Admin of $2',
        type: PushMessagePreferenceType.invitations,
    },
    WebinarParticipantAddedAsHost: {
        message: '$1 has assigned you as a Host of $2',
        type: PushMessagePreferenceType.invitations,
    },
    WebinarParticipantAddedAsSpeaker: {
        message: '$1 has assigned you as a Speaker at $2',
        type: PushMessagePreferenceType.invitations,
    },
    WebinarRegistrationRequested: {
        message: '$1 has requested to register in $2. View this request to confirm their participation',
        type: PushMessagePreferenceType.webinars,
    },
    WebinarRegistrationApproved: {
        message: '$1 has accepted your request to register $2',
        type: PushMessagePreferenceType.webinars,
    },
    NewWebinarUpdate: {
        message: '$1 has posted an update in $2',
        type: PushMessagePreferenceType.webinars,
    },
    NewFollower: {
        message: '$1$2 $3 requested to follow $4',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    SuggestFollow: {
        message: 'You’ve had a suggestion to follow some new organisations that travel industry professionals similar to you also follow.',
        type: PushMessagePreferenceType.followSuggestions,
    },
    PostComment: {
        message: '$1$2 $3commented on your post. Like or reply to their comment.',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    CommentMention: {
        message: '$1 mentioned you in a comment. Like or reply to their comment.',
        type: PushMessagePreferenceType.interaction,
    },
    PostMention: {
        message: '$1 mentioned you in a post. Click here to view the post.',
        type: PushMessagePreferenceType.mentionsInPosts,
    },
    CommentReact: {
        message: '$1$2 $3reacted to your comment on $4’s post.',
        type: PushMessagePreferenceType.interaction,
    },
    PostReact: {
        message: '$1$2 $3reacted to your post.',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    PostShared: {
        message: '$1 has reshared your post with their network.',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    OrgPostMention: {
        message: '$1 mentioned $2 in a post.',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    OrgCommentMention: {
        message: '$1 mentioned $2 in a post.',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    RecurringPaymentReminder: {
        message: 'Your paid subscription with $1 will renew on $2. Please ensure that your active payment method is still valid.',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    MembershipAutoApprove: {
        message: '$1 has joined $2 via pre-approved signup link.',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    ParentMembershipRequested: {
        message: '$1 has requested to become a member of $2. View this request to confirm their membership.',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    ParentMembershipAccepted: {
        message: 'You have been verified as an member of $1.',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    ParentMembershipDeclined: {
        message: 'You have been declined as an member of $1',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    OrgPostReminder7Days: {
        message: 'It’s been a week since $1’s last post. Create a post to keep your community engaged.',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    OrgPostReminder14Days: {
        message: 'It’s been two weeks since $1’s last post and your community is waiting to hear from you. Create your next post now.',
        type: PushMessagePreferenceType.organisationsYouManage,
    },
    InactiveUserReminder: {
        message: '$1 $2 shared new posts recently. View the latest updates in your feed.',
        type: PushMessagePreferenceType.posts,
    },
    InboxMessage: {
        message: '$1: $2',
        type: PushMessagePreferenceType.messages,
    },
    NewAchievement: {
        message: 'Congratulations! You have unlocked the $1 achievement',
        type: PushMessagePreferenceType.messages,
    },
    HighFiveAchievement: {
        message: '$1 at $2 gave you a High Five!',
        type: PushMessagePreferenceType.messages,
    },
    NewOrganisationAchievement: {
        message: 'Congratulations! You’ve unlocked the $1 $2 achievement!',
        type: PushMessagePreferenceType.messages,
    },
    MonthlyAchievementSummary: {
        message: 'Great job! You’ve unlocked new Organisation Achievements',
        type: PushMessagePreferenceType.messages,
    },
};
(0, graphql_1.registerEnumType)(exports.NotificationMessage, { name: 'NotificationMessage' });
//# sourceMappingURL=notifications.args.js.map