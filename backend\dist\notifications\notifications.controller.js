"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsController = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const sequelize_1 = require("sequelize");
const error_1 = require("../common/helpers/error");
const notifications_service_1 = require("../notifications/notifications.service");
const gcp_auth_guard_1 = require("../common/guards/gcp-auth.guard");
const config_1 = __importDefault(require("../config/config"));
let NotificationsController = class NotificationsController {
    async orgPostReminders() {
        var _a;
        try {
            return await this.notificationsService.orgPostReminders();
        }
        catch (e) {
            this.logger.error(`OrganisationsController.orgPostReminders Error: ${e.message}`, (_a = e.response) === null || _a === void 0 ? void 0 : _a.body);
        }
    }
    async orgPostRemindersBeta() {
        var _a;
        try {
            if (config_1.default.NODE_ENV === 'development' || config_1.default.SERVICE === 'api-beta') {
                return await this.notificationsService.orgPostReminders();
            }
            else {
                throw new Error('only beta env allowed');
            }
        }
        catch (e) {
            this.logger.error(`OrganisationsController.orgPostReminders Error: ${e.message}`, (_a = e.response) === null || _a === void 0 ? void 0 : _a.body);
        }
    }
    async inactiveUserReminder() {
        return await this.notificationsService.findInactiveUsersByTimezone();
    }
    async inactiveUserReminderBeta() {
        try {
            if (config_1.default.NODE_ENV === 'development' || config_1.default.SERVICE === 'api-beta') {
                return await this.notificationsService.findInactiveUsersByTimezone();
            }
            else {
                throw new Error('only beta env allowed');
            }
        }
        catch (err) {
            this.errorHelper.throwHttpException(`OrganisationsController.inactiveReminderBeta`, err.message);
        }
    }
};
exports.NotificationsController = NotificationsController;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => notifications_service_1.NotificationsService)),
    __metadata("design:type", notifications_service_1.NotificationsService)
], NotificationsController.prototype, "notificationsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", common_1.Logger)
], NotificationsController.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", sequelize_1.Sequelize)
], NotificationsController.prototype, "sequelize", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], NotificationsController.prototype, "errorHelper", void 0);
__decorate([
    (0, common_1.UseGuards)(gcp_auth_guard_1.GcpAuthGuard),
    (0, common_1.Get)('/orgPostReminders'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "orgPostReminders", null);
__decorate([
    (0, common_1.Get)('/orgPostReminders-beta'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "orgPostRemindersBeta", null);
__decorate([
    (0, common_1.UseGuards)(gcp_auth_guard_1.GcpAuthGuard),
    (0, common_1.Get)('/inactiveUserReminder'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "inactiveUserReminder", null);
__decorate([
    (0, common_1.Get)('/inactiveUserReminder-beta'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "inactiveUserReminderBeta", null);
exports.NotificationsController = NotificationsController = __decorate([
    (0, common_1.Controller)('notifications')
], NotificationsController);
//# sourceMappingURL=notifications.controller.js.map