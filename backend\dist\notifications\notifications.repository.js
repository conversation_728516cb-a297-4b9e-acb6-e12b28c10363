"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsRepository = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const notification_model_1 = require("./models/notification.model");
const pagination_1 = require("../common/helpers/pagination");
const sequelize_2 = require("sequelize");
const profile_model_1 = require("../profiles/models/profile.model");
const organisation_model_1 = require("../organisations/models/organisation.model");
const event_invitation_model_1 = require("../event-invitations/models/event-invitation.model");
const incentive_participant_model_1 = require("../incentive-participants/models/incentive-participant.model");
const incentive_model_1 = require("../incentives/models/incentive.model");
const webinar_participant_model_1 = require("../webinar-participants/models/webinar-participant.model");
const webinar_model_1 = require("../webinars/models/webinar.model");
const partnership_request_model_1 = require("../partnership-requests/models/partnership-request.model");
const membership_model_1 = require("../memberships/models/membership.model");
const follower_model_1 = require("../followers/models/follower.model");
const event_model_1 = require("../events/models/event.model");
let NotificationsRepository = class NotificationsRepository {
    async setStatus(fields, options) {
        const queryFilter = {
            ownerProfileId: options.currentProfileId,
        };
        if (options.notificationIds) {
            queryFilter['id'] = {
                [sequelize_2.Op.in]: options.notificationIds,
            };
        }
        await this.notificationModel.update(Object.assign({}, fields), {
            where: queryFilter,
            transaction: options === null || options === void 0 ? void 0 : options.transaction,
        });
        return this.notificationModel.findAll({
            where: queryFilter,
            transaction: options === null || options === void 0 ? void 0 : options.transaction,
        });
    }
    findNotifications(profileId, filter, pagination) {
        const extraQueryParams = {
            ownerProfileId: profileId,
        };
        if ((filter === null || filter === void 0 ? void 0 : filter.isRead) === true || (filter === null || filter === void 0 ? void 0 : filter.isRead) === false) {
            extraQueryParams.isRead = filter === null || filter === void 0 ? void 0 : filter.isRead;
        }
        const includeParams = [
            {
                model: profile_model_1.Profile,
                as: 'profile',
                attributes: ['id', 'name', 'image'],
            },
            {
                model: profile_model_1.Profile,
                as: 'ownerProfile',
                attributes: ['id', 'name', 'image'],
            },
            {
                model: organisation_model_1.Organisation,
                as: 'organisation',
                attributes: ['name', 'id', 'vanityId', 'image'],
            },
            {
                model: event_invitation_model_1.EventInvitation,
                as: 'eventInvitation',
                include: [
                    {
                        model: event_model_1.Event,
                        as: 'event',
                        include: [
                            {
                                model: organisation_model_1.Organisation,
                                as: 'organisation',
                                attributes: ['name', 'id', 'vanityId', 'image'],
                            },
                        ],
                    },
                    {
                        model: profile_model_1.Profile,
                        as: 'profile',
                        attributes: ['id', 'name', 'image'],
                    },
                    {
                        model: profile_model_1.Profile,
                        as: 'inviterProfile',
                        attributes: ['id', 'name', 'image'],
                    },
                ],
            },
            {
                model: incentive_participant_model_1.IncentiveParticipant,
                as: 'incentiveParticipant',
                include: [
                    {
                        model: incentive_model_1.Incentive,
                        as: 'incentive',
                        include: [
                            {
                                model: organisation_model_1.Organisation,
                                as: 'organisation',
                                attributes: ['name', 'id', 'vanityId', 'image'],
                            },
                        ],
                    },
                    {
                        model: profile_model_1.Profile,
                        as: 'profile',
                        attributes: ['id', 'name', 'image'],
                    },
                    {
                        model: profile_model_1.Profile,
                        as: 'inviterProfile',
                        attributes: ['id', 'name', 'image'],
                    },
                ],
            },
            {
                model: webinar_participant_model_1.WebinarParticipant,
                as: 'webinarParticipant',
                include: [
                    {
                        model: webinar_model_1.Webinar,
                        as: 'webinar',
                        include: [
                            {
                                model: organisation_model_1.Organisation,
                                as: 'organisation',
                                attributes: ['name', 'id', 'vanityId', 'image'],
                            },
                        ],
                    },
                    {
                        model: profile_model_1.Profile,
                        as: 'profile',
                        attributes: ['id', 'name', 'image'],
                    },
                    {
                        model: profile_model_1.Profile,
                        as: 'inviterProfile',
                        attributes: ['id', 'name', 'image'],
                    },
                ],
            },
            {
                model: partnership_request_model_1.PartnershipRequest,
                as: 'partnershipRequest',
                include: [
                    {
                        model: organisation_model_1.Organisation,
                        as: 'senderOrganisation',
                        attributes: ['name', 'id', 'vanityId', 'image'],
                    },
                    {
                        model: organisation_model_1.Organisation,
                        as: 'receiverOrganisation',
                        attributes: ['name', 'id', 'vanityId', 'image'],
                    },
                ],
            },
            {
                model: membership_model_1.Membership,
                as: 'membership',
                attributes: [
                    'id',
                    'position',
                    'isPrimary',
                    'organisationName',
                    'organisationId',
                    'status',
                    'profileId',
                ],
                include: [
                    {
                        model: organisation_model_1.Organisation,
                        as: 'organisation',
                        attributes: ['name', 'id', 'vanityId', 'image'],
                    },
                    {
                        model: profile_model_1.Profile,
                        as: 'profile',
                        attributes: ['id', 'name', 'image'],
                    },
                ],
            },
            {
                model: follower_model_1.Follower,
                as: 'follower',
                include: [
                    {
                        model: organisation_model_1.Organisation,
                        as: 'organisation',
                        attributes: ['name', 'id', 'vanityId', 'image'],
                    },
                    {
                        model: profile_model_1.Profile,
                        as: 'profile',
                        attributes: ['id', 'name', 'image'],
                    },
                ],
            },
        ];
        return new pagination_1.PaginationHelper().getPaginatedResults({
            model: notification_model_1.Notification,
            pagination,
            extraQueryParams,
            excludeIds: [],
            includeParams,
        });
    }
};
exports.NotificationsRepository = NotificationsRepository;
__decorate([
    (0, sequelize_1.InjectModel)(notification_model_1.Notification),
    __metadata("design:type", Object)
], NotificationsRepository.prototype, "notificationModel", void 0);
exports.NotificationsRepository = NotificationsRepository = __decorate([
    (0, common_1.Injectable)()
], NotificationsRepository);
//# sourceMappingURL=notifications.repository.js.map