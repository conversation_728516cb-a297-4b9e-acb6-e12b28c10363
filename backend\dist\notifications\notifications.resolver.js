"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsResolver = void 0;
const moment_1 = __importDefault(require("moment"));
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const schedule_1 = require("@nestjs/schedule");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const notification_model_1 = require("./models/notification.model");
const notifications_service_1 = require("./notifications.service");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const notifications_args_1 = require("./args/notifications.args");
const paginated_result_1 = require("../common/args/paginated-result");
const profile_model_1 = require("../profiles/models/profile.model");
const profiles_service_1 = require("../profiles/profiles.service");
const graphql_subscriptions_1 = require("graphql-subscriptions");
const memberships_service_1 = require("../memberships/memberships.service");
const membership_model_1 = require("../memberships/models/membership.model");
const event_invitation_model_1 = require("../event-invitations/models/event-invitation.model");
const event_invitations_service_1 = require("../event-invitations/event-invitations.service");
const organisation_model_1 = require("../organisations/models/organisation.model");
const organisations_service_1 = require("../organisations/organisations.service");
const partnership_request_model_1 = require("../partnership-requests/models/partnership-request.model");
const partnership_requests_service_1 = require("../partnership-requests/partnership-requests.service");
const follower_model_1 = require("../followers/models/follower.model");
const followers_service_1 = require("../followers/followers.service");
const pubsub_1 = require("../common/helpers/pubsub");
const sequelize_1 = require("sequelize");
const incentive_participant_model_1 = require("../incentive-participants/models/incentive-participant.model");
const incentive_participants_service_1 = require("../incentive-participants/incentive-participants.service");
const webinar_participant_model_1 = require("../webinar-participants/models/webinar-participant.model");
const webinar_participants_service_1 = require("../webinar-participants/webinar-participants.service");
let NotificationsResolver = class NotificationsResolver {
    onModuleInit() {
        this._onlineProfileIds = {};
    }
    notification(id) {
        this.logger.verbose('NotificationsResolver.notification (query)', {
            id,
        });
        return this.notificationsService.findById(id);
    }
    notifications(user, notificationArgs) {
        var _a;
        this.logger.verbose('NotificationsResolver.notifications (query)', {
            user: user.toLogObject(),
            notificationArgs,
        });
        return this.notificationsService.findNotifications(user.profileId, {
            isRead: (_a = notificationArgs === null || notificationArgs === void 0 ? void 0 : notificationArgs.filter) === null || _a === void 0 ? void 0 : _a.isRead,
        }, {
            first: notificationArgs === null || notificationArgs === void 0 ? void 0 : notificationArgs.first,
            after: notificationArgs === null || notificationArgs === void 0 ? void 0 : notificationArgs.after,
            sortBy: notificationArgs === null || notificationArgs === void 0 ? void 0 : notificationArgs.sortBy,
            sortOrder: notificationArgs === null || notificationArgs === void 0 ? void 0 : notificationArgs.sortOrder,
        });
    }
    unSeenNotifications(user) {
        return this.notificationsService.count({
            ownerProfileId: user.profileId,
            isSeen: false,
        });
    }
    setNotificationStatus(user, fields, notificationIds) {
        this.logger.verbose('NotificationsResolver.setNotificationStatus (mutation)', {
            user: user.toLogObject(),
            fields,
            notificationIds,
        });
        return this.notificationsService.setStatus(fields, {
            currentProfileId: user.profileId,
            notificationIds,
        });
    }
    async profile(notification) {
        if (notification.profile)
            return notification.profile;
        this.logger.verbose('NotificationsResolver.profile (field resolver)', {
            notificationId: notification.id,
        });
        return this.profilesService.findById(notification.profileId);
    }
    async membership(notification) {
        if (notification.membership)
            return notification.membership;
        if (!notification.membershipId)
            return null;
        this.logger.verbose('NotificationsResolver.membership (field resolver)', {
            notificationId: notification.id,
        });
        return this.membershipsService.findById(notification.membershipId, {
            useCache: true,
        });
    }
    async follower(notification) {
        if (notification.follower)
            return notification.follower;
        if (!notification.followerId)
            return null;
        this.logger.verbose('NotificationsResolver.follower (field resolver)', {
            notificationId: notification.id,
        });
        return this.followersService.findById(notification.followerId, {
            useCache: true,
        });
    }
    async eventInvitation(notification) {
        if (notification.eventInvitation)
            return notification.eventInvitation;
        if (!notification.eventInvitationId)
            return null;
        this.logger.verbose('NotificationsResolver.eventInvitation (field resolver)', {
            notificationId: notification.id,
        });
        return this.eventInvitationsService.findById(notification.eventInvitationId, { useCache: true });
    }
    async organisation(notification) {
        if (notification.organisation)
            return notification.organisation;
        if (!notification.organisationId)
            return null;
        this.logger.verbose('NotificationsResolver.organisation (field resolver)', {
            notificationId: notification.id,
        });
        return this.organisationsService.findById(notification.organisationId, {
            useCache: true,
        });
    }
    async partnershipRequest(notification) {
        if (notification.partnershipRequest)
            return notification.partnershipRequest;
        if (!notification.partnershipRequestId)
            return null;
        this.logger.verbose('NotificationsResolver.partnershipRequest (field resolver)', {
            notificationId: notification.id,
        });
        return this.partnershipRequestsService.findById(notification.partnershipRequestId, {
            useCache: true,
        });
    }
    async incentiveParticipant(notification) {
        if (notification.incentiveParticipant)
            return notification.incentiveParticipant;
        if (!notification.incentiveParticipantId)
            return null;
        this.logger.verbose('NotificationsResolver.incentiveParticipant (field resolver)', {
            notificationId: notification.id,
        });
        return this.incentiveParticipantsService.findById(notification.incentiveParticipantId, { useCache: true });
    }
    async webinarParticipant(notification) {
        if (notification.webinarParticipant)
            return notification.webinarParticipant;
        if (!notification.webinarParticipantId)
            return null;
        this.logger.verbose('NotificationsResolver.webinarParticipant (field resolver)', {
            notificationId: notification.id,
        });
        return this.webinarParticipantsService.findById(notification.webinarParticipantId, { useCache: true });
    }
    async data(notification) {
        const notifData = notification.data;
        if (notifData && notifData.users) {
            const updUsers = [];
            for (let user of notifData.users) {
                const profile = await this.profilesService.findById(user.profileId || '');
                let profileData = profile;
                if (!profileData)
                    profileData = user;
                profileData.profileId = user.profileId;
                updUsers.push(profileData);
            }
            notifData.users = updUsers;
        }
        if (notifData && notifData.organisationId) {
            const orgData = await this.organisationsService.findById(notifData.organisationId);
            notifData.organisation = orgData;
        }
        if (notifData && notifData.senderOrgId) {
            const senderOrg = await this.organisationsService.findById(notifData.senderOrgId);
            if (senderOrg) {
                notifData.senderOrgImage = senderOrg.image || null;
            }
        }
        if (notifData && notifData.receiverOrgId) {
            const receiverOrg = await this.organisationsService.findById(notifData.receiverOrgId);
            if (receiverOrg) {
                notifData.receiverOrgImage = receiverOrg.image || null;
            }
        }
        return notifData;
    }
    async newNotification(user) {
        this.logger.verbose(`${user.name} (${user.email}, ${user.profileId}) connected to receive notifications`);
        this._onlineProfileIds[user.profileId] = true;
        await this.profilesService.updateById(user.profileId, {
            lastOnlineAt: (0, moment_1.default)().toDate(),
        });
        return (0, pubsub_1.withCancel)(this.pubSub.asyncIterator('newNotification'), async () => {
            this.logger.verbose(`${user.name} (${user.email}, ${user.profileId}) disconnected`);
            delete this._onlineProfileIds[user.profileId];
            await this.profilesService.updateById(user.profileId, {
                lastOnlineAt: (0, moment_1.default)().add(-2, 'minutes').toDate(),
            });
        });
    }
    async handleInterval() {
        if (Object.keys(this._onlineProfileIds).length > 0) {
            await this.profilesService.update({
                where: {
                    id: {
                        [sequelize_1.Op.in]: Object.keys(this._onlineProfileIds),
                    },
                },
                update: {
                    lastOnlineAt: (0, moment_1.default)().toDate(),
                },
            });
        }
    }
};
exports.NotificationsResolver = NotificationsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => notifications_service_1.NotificationsService)),
    __metadata("design:type", notifications_service_1.NotificationsService)
], NotificationsResolver.prototype, "notificationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], NotificationsResolver.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], NotificationsResolver.prototype, "membershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => event_invitations_service_1.EventInvitationsService)),
    __metadata("design:type", event_invitations_service_1.EventInvitationsService)
], NotificationsResolver.prototype, "eventInvitationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentive_participants_service_1.IncentiveParticipantsService)),
    __metadata("design:type", incentive_participants_service_1.IncentiveParticipantsService)
], NotificationsResolver.prototype, "incentiveParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinar_participants_service_1.WebinarParticipantsService)),
    __metadata("design:type", webinar_participants_service_1.WebinarParticipantsService)
], NotificationsResolver.prototype, "webinarParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], NotificationsResolver.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => partnership_requests_service_1.PartnershipRequestsService)),
    __metadata("design:type", partnership_requests_service_1.PartnershipRequestsService)
], NotificationsResolver.prototype, "partnershipRequestsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => followers_service_1.FollowersService)),
    __metadata("design:type", followers_service_1.FollowersService)
], NotificationsResolver.prototype, "followersService", void 0);
__decorate([
    (0, common_1.Inject)('PUB_SUB'),
    __metadata("design:type", graphql_subscriptions_1.PubSubEngine)
], NotificationsResolver.prototype, "pubSub", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], NotificationsResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Query)(() => notification_model_1.Notification),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)('id', { nullable: false })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], NotificationsResolver.prototype, "notification", null);
__decorate([
    (0, graphql_1.Query)(() => paginated_result_1.NotificationsResult),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, notifications_args_1.NotificationsArgs]),
    __metadata("design:returntype", Promise)
], NotificationsResolver.prototype, "notifications", null);
__decorate([
    (0, graphql_1.Query)(() => graphql_1.Int),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], NotificationsResolver.prototype, "unSeenNotifications", null);
__decorate([
    (0, graphql_1.Mutation)(() => [notification_model_1.Notification]),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('fields')),
    __param(2, (0, graphql_1.Args)('notificationIds', { type: () => [String], nullable: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, notification_model_1.ChangeFields, Array]),
    __metadata("design:returntype", Promise)
], NotificationsResolver.prototype, "setNotificationStatus", null);
__decorate([
    (0, graphql_1.ResolveField)('profile', () => profile_model_1.Profile),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [notification_model_1.Notification]),
    __metadata("design:returntype", Promise)
], NotificationsResolver.prototype, "profile", null);
__decorate([
    (0, graphql_1.ResolveField)('membership', () => membership_model_1.Membership, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [notification_model_1.Notification]),
    __metadata("design:returntype", Promise)
], NotificationsResolver.prototype, "membership", null);
__decorate([
    (0, graphql_1.ResolveField)('follower', () => follower_model_1.Follower, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [notification_model_1.Notification]),
    __metadata("design:returntype", Promise)
], NotificationsResolver.prototype, "follower", null);
__decorate([
    (0, graphql_1.ResolveField)('eventInvitation', () => event_invitation_model_1.EventInvitation, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [notification_model_1.Notification]),
    __metadata("design:returntype", Promise)
], NotificationsResolver.prototype, "eventInvitation", null);
__decorate([
    (0, graphql_1.ResolveField)('organisation', () => organisation_model_1.Organisation, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [notification_model_1.Notification]),
    __metadata("design:returntype", Promise)
], NotificationsResolver.prototype, "organisation", null);
__decorate([
    (0, graphql_1.ResolveField)('partnershipRequest', () => partnership_request_model_1.PartnershipRequest, {
        nullable: true,
    }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [notification_model_1.Notification]),
    __metadata("design:returntype", Promise)
], NotificationsResolver.prototype, "partnershipRequest", null);
__decorate([
    (0, graphql_1.ResolveField)('incentiveParticipant', () => incentive_participant_model_1.IncentiveParticipant, {
        nullable: true,
    }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [notification_model_1.Notification]),
    __metadata("design:returntype", Promise)
], NotificationsResolver.prototype, "incentiveParticipant", null);
__decorate([
    (0, graphql_1.ResolveField)('webinarParticipant', () => webinar_participant_model_1.WebinarParticipant, {
        nullable: true,
    }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [notification_model_1.Notification]),
    __metadata("design:returntype", Promise)
], NotificationsResolver.prototype, "webinarParticipant", null);
__decorate([
    (0, graphql_1.ResolveField)('data', () => notifications_args_1.NotificationData, {
        nullable: true,
    }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [notification_model_1.Notification]),
    __metadata("design:returntype", Promise)
], NotificationsResolver.prototype, "data", null);
__decorate([
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    (0, graphql_1.Subscription)(() => notification_model_1.Notification, {
        filter: (payload, variables, context) => {
            const currentUser = context.connection.context.user;
            const notification = payload.newNotification;
            if (currentUser.profileId === notification.ownerProfileId) {
                return true;
            }
            return false;
        },
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], NotificationsResolver.prototype, "newNotification", null);
__decorate([
    (0, schedule_1.Interval)(60000),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], NotificationsResolver.prototype, "handleInterval", null);
exports.NotificationsResolver = NotificationsResolver = __decorate([
    (0, graphql_1.Resolver)(() => notification_model_1.Notification)
], NotificationsResolver);
//# sourceMappingURL=notifications.resolver.js.map