{"version": 3, "file": "notifications.resolver.js", "sourceRoot": "", "sources": ["../../src/notifications/notifications.resolver.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,oDAA4B;AAC5B,2CAA6E;AAC7E,6CASyB;AACzB,+CAA4C;AAC5C,+CAAuD;AACvD,qCAAiC;AAEjC,oEAAyE;AACzE,mEAA+D;AAC/D,oEAA2D;AAC3D,wFAGqD;AACrD,kEAAgF;AAChF,sEAAsE;AACtE,oEAA2D;AAC3D,mEAA+D;AAC/D,iEAAqD;AACrD,4EAAwE;AACxE,6EAAoE;AACpE,+FAAqF;AACrF,8FAAyF;AACzF,mFAA0E;AAC1E,kFAA8E;AAC9E,wGAA8F;AAC9F,uGAAkG;AAClG,uEAA8D;AAC9D,sEAAkE;AAClE,qDAAsD;AACtD,yCAA+B;AAC/B,8GAAoG;AACpG,6GAAwG;AACxG,wGAA8F;AAC9F,uGAAkG;AAG3F,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IA0BhC,YAAY;QACV,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;IAC9B,CAAC;IAID,YAAY,CACuB,EAAU;QAE3C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,4CAA4C,EAAE;YAChE,EAAE;SACH,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAChD,CAAC;IAID,aAAa,CACI,IAAkB,EACzB,gBAAmC;;QAE3C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,6CAA6C,EAAE;YACjE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE;YACxB,gBAAgB;SACjB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAChD,IAAI,CAAC,SAAS,EACd;YACE,MAAM,EAAE,MAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,MAAM,0CAAE,MAAM;SACzC,EACD;YACE,KAAK,EAAE,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,KAAK;YAC9B,KAAK,EAAE,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,KAAK;YAC9B,MAAM,EAAE,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,MAAM;YAChC,SAAS,EAAE,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,SAAS;SACvC,CACF,CAAC;IACJ,CAAC;IAID,mBAAmB,CAAgB,IAAkB;QACnD,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;YACrC,cAAc,EAAE,IAAI,CAAC,SAAS;YAC9B,MAAM,EAAE,KAAK;SACd,CAAC,CAAC;IACL,CAAC;IAID,qBAAqB,CACJ,IAAkB,EACjB,MAAoB,EAEpC,eAAiC;QAEjC,IAAI,CAAC,MAAM,CAAC,OAAO,CACjB,wDAAwD,EACxD;YACE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE;YACxB,MAAM;YACN,eAAe;SAChB,CACF,CAAC;QACF,OAAO,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,MAAM,EAAE;YACjD,gBAAgB,EAAE,IAAI,CAAC,SAAS;YAChC,eAAe;SAChB,CAAC,CAAC;IACL,CAAC;IAGK,AAAN,KAAK,CAAC,OAAO,CAAW,YAA0B;QAChD,IAAI,YAAY,CAAC,OAAO;YAAE,OAAO,YAAY,CAAC,OAAO,CAAC;QAEtD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gDAAgD,EAAE;YACpE,cAAc,EAAE,YAAY,CAAC,EAAE;SAChC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IAC/D,CAAC;IAGK,AAAN,KAAK,CAAC,UAAU,CAAW,YAA0B;QACnD,IAAI,YAAY,CAAC,UAAU;YAAE,OAAO,YAAY,CAAC,UAAU,CAAC;QAC5D,IAAI,CAAC,YAAY,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC;QAE5C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mDAAmD,EAAE;YACvE,cAAc,EAAE,YAAY,CAAC,EAAE;SAChC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,YAAY,CAAC,YAAY,EAAE;YACjE,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;IACL,CAAC;IAGK,AAAN,KAAK,CAAC,QAAQ,CAAW,YAA0B;QACjD,IAAI,YAAY,CAAC,QAAQ;YAAE,OAAO,YAAY,CAAC,QAAQ,CAAC;QACxD,IAAI,CAAC,YAAY,CAAC,UAAU;YAAE,OAAO,IAAI,CAAC;QAE1C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iDAAiD,EAAE;YACrE,cAAc,EAAE,YAAY,CAAC,EAAE;SAChC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,YAAY,CAAC,UAAU,EAAE;YAC7D,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;IACL,CAAC;IAGK,AAAN,KAAK,CAAC,eAAe,CACT,YAA0B;QAEpC,IAAI,YAAY,CAAC,eAAe;YAAE,OAAO,YAAY,CAAC,eAAe,CAAC;QACtE,IAAI,CAAC,YAAY,CAAC,iBAAiB;YAAE,OAAO,IAAI,CAAC;QAEjD,IAAI,CAAC,MAAM,CAAC,OAAO,CACjB,wDAAwD,EACxD;YACE,cAAc,EAAE,YAAY,CAAC,EAAE;SAChC,CACF,CAAC;QAEF,OAAO,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAC1C,YAAY,CAAC,iBAAiB,EAC9B,EAAE,QAAQ,EAAE,IAAI,EAAE,CACnB,CAAC;IACJ,CAAC;IAGK,AAAN,KAAK,CAAC,YAAY,CACN,YAA0B;QAEpC,IAAI,YAAY,CAAC,YAAY;YAAE,OAAO,YAAY,CAAC,YAAY,CAAC;QAChE,IAAI,CAAC,YAAY,CAAC,cAAc;YAAE,OAAO,IAAI,CAAC;QAE9C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qDAAqD,EAAE;YACzE,cAAc,EAAE,YAAY,CAAC,EAAE;SAChC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,YAAY,CAAC,cAAc,EAAE;YACrE,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;IACL,CAAC;IAKK,AAAN,KAAK,CAAC,kBAAkB,CACZ,YAA0B;QAEpC,IAAI,YAAY,CAAC,kBAAkB;YAAE,OAAO,YAAY,CAAC,kBAAkB,CAAC;QAC5E,IAAI,CAAC,YAAY,CAAC,oBAAoB;YAAE,OAAO,IAAI,CAAC;QAEpD,IAAI,CAAC,MAAM,CAAC,OAAO,CACjB,2DAA2D,EAC3D;YACE,cAAc,EAAE,YAAY,CAAC,EAAE;SAChC,CACF,CAAC;QAEF,OAAO,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAC7C,YAAY,CAAC,oBAAoB,EACjC;YACE,QAAQ,EAAE,IAAI;SACf,CACF,CAAC;IACJ,CAAC;IAKK,AAAN,KAAK,CAAC,oBAAoB,CACd,YAA0B;QAEpC,IAAI,YAAY,CAAC,oBAAoB;YACnC,OAAO,YAAY,CAAC,oBAAoB,CAAC;QAC3C,IAAI,CAAC,YAAY,CAAC,sBAAsB;YAAE,OAAO,IAAI,CAAC;QAEtD,IAAI,CAAC,MAAM,CAAC,OAAO,CACjB,6DAA6D,EAC7D;YACE,cAAc,EAAE,YAAY,CAAC,EAAE;SAChC,CACF,CAAC;QAEF,OAAO,IAAI,CAAC,4BAA4B,CAAC,QAAQ,CAC/C,YAAY,CAAC,sBAAsB,EACnC,EAAE,QAAQ,EAAE,IAAI,EAAE,CACnB,CAAC;IACJ,CAAC;IAKK,AAAN,KAAK,CAAC,kBAAkB,CACZ,YAA0B;QAEpC,IAAI,YAAY,CAAC,kBAAkB;YAAE,OAAO,YAAY,CAAC,kBAAkB,CAAC;QAC5E,IAAI,CAAC,YAAY,CAAC,oBAAoB;YAAE,OAAO,IAAI,CAAC;QAEpD,IAAI,CAAC,MAAM,CAAC,OAAO,CACjB,2DAA2D,EAC3D;YACE,cAAc,EAAE,YAAY,CAAC,EAAE;SAChC,CACF,CAAC;QAEF,OAAO,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAC7C,YAAY,CAAC,oBAAoB,EACjC,EAAE,QAAQ,EAAE,IAAI,EAAE,CACnB,CAAC;IACJ,CAAC;IAKK,AAAN,KAAK,CAAC,IAAI,CAAW,YAA0B;QAC7C,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC;QACpC,IAAI,SAAS,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;YACjC,MAAM,QAAQ,GAAG,EAAE,CAAC;YACpB,KAAK,IAAI,IAAI,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;gBACjC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CACjD,IAAI,CAAC,SAAS,IAAI,EAAE,CACrB,CAAC;gBACF,IAAI,WAAW,GAAG,OAAiC,CAAC;gBACpD,IAAI,CAAC,WAAW;oBAAE,WAAW,GAAG,IAAI,CAAC;gBACrC,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;gBACvC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC7B,CAAC;YACD,SAAS,CAAC,KAAK,GAAG,QAAQ,CAAC;QAC7B,CAAC;QACD,IAAI,SAAS,IAAI,SAAS,CAAC,cAAc,EAAE,CAAC;YAC1C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CACtD,SAAS,CAAC,cAAc,CACzB,CAAC;YACF,SAAS,CAAC,YAAY,GAAG,OAAO,CAAC;QACnC,CAAC;QAGD,IAAI,SAAS,IAAI,SAAS,CAAC,WAAW,EAAE,CAAC;YACvC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAClF,IAAI,SAAS,EAAE,CAAC;gBACd,SAAS,CAAC,cAAc,GAAG,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC;YACrD,CAAC;QACH,CAAC;QAED,IAAI,SAAS,IAAI,SAAS,CAAC,aAAa,EAAE,CAAC;YACzC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACtF,IAAI,WAAW,EAAE,CAAC;gBAChB,SAAS,CAAC,gBAAgB,GAAG,WAAW,CAAC,KAAK,IAAI,IAAI,CAAC;YACzD,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAeK,AAAN,KAAK,CAAC,eAAe,CAAgB,IAAkB;QACrD,IAAI,CAAC,MAAM,CAAC,OAAO,CACjB,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,SAAS,sCAAsC,CACrF,CAAC;QAEF,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;QAC9C,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE;YACpD,YAAY,EAAE,IAAA,gBAAM,GAAE,CAAC,MAAM,EAAE;SAChC,CAAC,CAAC;QAEH,OAAO,IAAA,mBAAU,EACf,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,iBAAiB,CAAC,EAC5C,KAAK,IAAI,EAAE;YACT,IAAI,CAAC,MAAM,CAAC,OAAO,CACjB,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,SAAS,gBAAgB,CAC/D,CAAC;YAEF,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE9C,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE;gBACpD,YAAY,EAAE,IAAA,gBAAM,GAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,MAAM,EAAE;aACnD,CAAC,CAAC;QACL,CAAC,CACF,CAAC;IACJ,CAAC;IAGK,AAAN,KAAK,CAAC,cAAc;QAClB,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;gBAChC,KAAK,EAAE;oBACL,EAAE,EAAE;wBACF,CAAC,cAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC;qBAC7C;iBACF;gBACD,MAAM,EAAE;oBACN,YAAY,EAAE,IAAA,gBAAM,GAAE,CAAC,MAAM,EAAE;iBAChC;aACF,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF,CAAA;AAnVY,sDAAqB;AAEf;IADhB,IAAA,eAAM,EAAC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,4CAAoB,CAAC,CAAC;8BACR,4CAAoB;mEAAC;AAE3C;IADhB,IAAA,eAAM,EAAC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,kCAAe,CAAC,CAAC;8BACR,kCAAe;8DAAC;AAEjC;IADhB,IAAA,eAAM,EAAC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,wCAAkB,CAAC,CAAC;8BACR,wCAAkB;iEAAC;AAEvC;IADhB,IAAA,eAAM,EAAC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,mDAAuB,CAAC,CAAC;8BACR,mDAAuB;sEAAC;AAEjD;IADhB,IAAA,eAAM,EAAC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,6DAA4B,CAAC,CAAC;8BACR,6DAA4B;2EAAC;AAE3D;IADhB,IAAA,eAAM,EAAC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,yDAA0B,CAAC,CAAC;8BACR,yDAA0B;yEAAC;AAEvD;IADhB,IAAA,eAAM,EAAC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,4CAAoB,CAAC,CAAC;8BACR,4CAAoB;mEAAC;AAE3C;IADhB,IAAA,eAAM,EAAC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,yDAA0B,CAAC,CAAC;8BACR,yDAA0B;yEAAC;AAEvD;IADhB,IAAA,eAAM,EAAC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,oCAAgB,CAAC,CAAC;8BACR,oCAAgB;+DAAC;AAE5C;IADP,IAAA,eAAM,EAAC,SAAS,CAAC;8BACF,oCAAY;qDAAC;AAEZ;IADhB,IAAA,eAAM,EAAC,sCAAuB,CAAC;8BACP,gBAAM;qDAAC;AAUhC;IAFC,IAAA,eAAK,EAAC,GAAG,EAAE,CAAC,iCAAY,CAAC;IACzB,IAAA,kBAAS,EAAC,iCAAY,CAAC;IAErB,WAAA,IAAA,cAAI,EAAC,IAAI,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAA;;;;yDAOjC;AAID;IAFC,IAAA,eAAK,EAAC,GAAG,EAAE,CAAC,sCAAmB,CAAC;IAChC,IAAA,kBAAS,EAAC,iCAAY,CAAC;IAErB,WAAA,IAAA,oCAAW,GAAE,CAAA;IACb,WAAA,IAAA,cAAI,GAAE,CAAA;;6CAAmB,sCAAiB;;0DAmB5C;AAID;IAFC,IAAA,eAAK,EAAC,GAAG,EAAE,CAAC,aAAG,CAAC;IAChB,IAAA,kBAAS,EAAC,iCAAY,CAAC;IACH,WAAA,IAAA,oCAAW,GAAE,CAAA;;;;gEAKjC;AAID;IAFC,IAAA,kBAAQ,EAAC,GAAG,EAAE,CAAC,CAAC,iCAAY,CAAC,CAAC;IAC9B,IAAA,kBAAS,EAAC,iCAAY,CAAC;IAErB,WAAA,IAAA,oCAAW,GAAE,CAAA;IACb,WAAA,IAAA,cAAI,EAAC,QAAQ,CAAC,CAAA;IACd,WAAA,IAAA,cAAI,EAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAA;;6CAD1C,iCAAY;;kEAgBrC;AAGK;IADL,IAAA,sBAAY,EAAC,SAAS,EAAE,GAAG,EAAE,CAAC,uBAAO,CAAC;IACxB,WAAA,IAAA,gBAAM,GAAE,CAAA;;qCAAe,iCAAY;;oDAQjD;AAGK;IADL,IAAA,sBAAY,EAAC,YAAY,EAAE,GAAG,EAAE,CAAC,6BAAU,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC/C,WAAA,IAAA,gBAAM,GAAE,CAAA;;qCAAe,iCAAY;;uDAWpD;AAGK;IADL,IAAA,sBAAY,EAAC,UAAU,EAAE,GAAG,EAAE,CAAC,yBAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC7C,WAAA,IAAA,gBAAM,GAAE,CAAA;;qCAAe,iCAAY;;qDAWlD;AAGK;IADL,IAAA,sBAAY,EAAC,iBAAiB,EAAE,GAAG,EAAE,CAAC,wCAAe,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAExE,WAAA,IAAA,gBAAM,GAAE,CAAA;;qCAAe,iCAAY;;4DAgBrC;AAGK;IADL,IAAA,sBAAY,EAAC,cAAc,EAAE,GAAG,EAAE,CAAC,iCAAY,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAElE,WAAA,IAAA,gBAAM,GAAE,CAAA;;qCAAe,iCAAY;;yDAYrC;AAKK;IAHL,IAAA,sBAAY,EAAC,oBAAoB,EAAE,GAAG,EAAE,CAAC,8CAAkB,EAAE;QAC5D,QAAQ,EAAE,IAAI;KACf,CAAC;IAEC,WAAA,IAAA,gBAAM,GAAE,CAAA;;qCAAe,iCAAY;;+DAkBrC;AAKK;IAHL,IAAA,sBAAY,EAAC,sBAAsB,EAAE,GAAG,EAAE,CAAC,kDAAoB,EAAE;QAChE,QAAQ,EAAE,IAAI;KACf,CAAC;IAEC,WAAA,IAAA,gBAAM,GAAE,CAAA;;qCAAe,iCAAY;;iEAiBrC;AAKK;IAHL,IAAA,sBAAY,EAAC,oBAAoB,EAAE,GAAG,EAAE,CAAC,8CAAkB,EAAE;QAC5D,QAAQ,EAAE,IAAI;KACf,CAAC;IAEC,WAAA,IAAA,gBAAM,GAAE,CAAA;;qCAAe,iCAAY;;+DAgBrC;AAKK;IAHL,IAAA,sBAAY,EAAC,MAAM,EAAE,GAAG,EAAE,CAAC,qCAAgB,EAAE;QAC5C,QAAQ,EAAE,IAAI;KACf,CAAC;IACU,WAAA,IAAA,gBAAM,GAAE,CAAA;;qCAAe,iCAAY;;iDAsC9C;AAeK;IAbL,IAAA,kBAAS,EAAC,iCAAY,CAAC;IACvB,IAAA,sBAAY,EAAC,GAAG,EAAE,CAAC,iCAAY,EAAE;QAChC,MAAM,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;YACtC,MAAM,WAAW,GAAiB,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC;YAClE,MAAM,YAAY,GAAiB,OAAO,CAAC,eAAe,CAAC;YAE3D,IAAI,WAAW,CAAC,SAAS,KAAK,YAAY,CAAC,cAAc,EAAE,CAAC;gBAC1D,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IACqB,WAAA,IAAA,oCAAW,GAAE,CAAA;;;;4DAwBnC;AAGK;IADL,IAAA,mBAAQ,EAAC,KAAK,CAAC;;;;2DAcf;gCAlVU,qBAAqB;IADjC,IAAA,kBAAQ,EAAC,GAAG,EAAE,CAAC,iCAAY,CAAC;GAChB,qBAAqB,CAmVjC"}