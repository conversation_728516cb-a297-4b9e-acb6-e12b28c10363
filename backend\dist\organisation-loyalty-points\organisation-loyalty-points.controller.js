"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganisationLoyaltyPointsController = void 0;
const common_1 = require("@nestjs/common");
const gcp_auth_guard_1 = require("../common/guards/gcp-auth.guard");
const error_1 = require("../common/helpers/error");
const config_1 = __importDefault(require("../config/config"));
const organisation_loyalty_points_service_1 = require("./organisation-loyalty-points.service");
let OrganisationLoyaltyPointsController = class OrganisationLoyaltyPointsController {
    runActivityDataMigration() {
        void this.organisationLoyaltyPointsService.addZeroLoyaltyPointsToOrganisations();
        return;
    }
    runActivityDataMigrationBeta() {
        try {
            if (config_1.default.NODE_ENV === 'development' || config_1.default.SERVICE === 'api-beta') {
                void this.organisationLoyaltyPointsService.addZeroLoyaltyPointsToOrganisations();
                return;
            }
            else {
                throw new Error('only beta env allowed');
            }
        }
        catch (err) {
            this.errorHelper.throwHttpException(`ActivitiesService.runJobBeta`, err.message);
        }
    }
};
exports.OrganisationLoyaltyPointsController = OrganisationLoyaltyPointsController;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisation_loyalty_points_service_1.OrganisationLoyaltyPointsService)),
    __metadata("design:type", organisation_loyalty_points_service_1.OrganisationLoyaltyPointsService)
], OrganisationLoyaltyPointsController.prototype, "organisationLoyaltyPointsService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], OrganisationLoyaltyPointsController.prototype, "errorHelper", void 0);
__decorate([
    (0, common_1.UseGuards)(gcp_auth_guard_1.GcpAuthGuard),
    (0, common_1.Get)('run-addZeroLoyaltyPoints'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OrganisationLoyaltyPointsController.prototype, "runActivityDataMigration", null);
__decorate([
    (0, common_1.Get)('run-beta-addZeroLoyaltyPoints'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OrganisationLoyaltyPointsController.prototype, "runActivityDataMigrationBeta", null);
exports.OrganisationLoyaltyPointsController = OrganisationLoyaltyPointsController = __decorate([
    (0, common_1.Controller)('organisation-loyalty-points')
], OrganisationLoyaltyPointsController);
//# sourceMappingURL=organisation-loyalty-points.controller.js.map