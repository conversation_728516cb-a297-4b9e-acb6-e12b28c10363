"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganisationLoyaltyPointsResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const paginated_result_1 = require("../common/args/paginated-result");
const organisation_loyalty_points_args_1 = require("./args/organisation-loyalty-points.args");
const organisation_loyalty_points_service_1 = require("./organisation-loyalty-points.service");
const organisation_loyalty_points_model_1 = require("./models/organisation-loyalty-points.model");
let OrganisationLoyaltyPointsResolver = class OrganisationLoyaltyPointsResolver {
    async getOrgLoyaltyPointsList(organisationId, organisationLoyaltyPointsArgs) {
        this.logger.verbose('OrganisationLoyaltyPointsResolver.getOrgLoyaltyPointsList (query)', {
            organisationId,
            organisationLoyaltyPointsArgs,
        });
        return this.organisationLoyaltyPointsService.getOrgLoyaltyPointsList({
            organisationId,
            organisationLoyaltyPointsArgs,
        });
    }
};
exports.OrganisationLoyaltyPointsResolver = OrganisationLoyaltyPointsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisation_loyalty_points_service_1.OrganisationLoyaltyPointsService)),
    __metadata("design:type", organisation_loyalty_points_service_1.OrganisationLoyaltyPointsService)
], OrganisationLoyaltyPointsResolver.prototype, "organisationLoyaltyPointsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], OrganisationLoyaltyPointsResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Query)(() => paginated_result_1.OrganisationLoyaltyPointsResult),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)('organisationId')),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, organisation_loyalty_points_args_1.OrganisationLoyaltyPointsArgs]),
    __metadata("design:returntype", Promise)
], OrganisationLoyaltyPointsResolver.prototype, "getOrgLoyaltyPointsList", null);
exports.OrganisationLoyaltyPointsResolver = OrganisationLoyaltyPointsResolver = __decorate([
    (0, graphql_1.Resolver)(() => organisation_loyalty_points_model_1.OrganisationLoyaltyPoint)
], OrganisationLoyaltyPointsResolver);
//# sourceMappingURL=organisation-loyalty-points.resolver.js.map