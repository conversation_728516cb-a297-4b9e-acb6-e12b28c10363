"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var OrganisationLoyaltyPointsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganisationLoyaltyPointsService = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const moment_timezone_1 = __importDefault(require("moment-timezone"));
const nest_winston_1 = require("nest-winston");
const sequelize_2 = require("sequelize");
const sequelize_typescript_1 = require("sequelize-typescript");
const winston_1 = require("winston");
const activities_args_1 = require("../activities/args/activities.args");
const base_service_1 = require("../common/base.service");
const error_1 = require("../common/helpers/error");
const notifications_service_1 = require("../notifications/notifications.service");
const profiles_service_1 = require("../profiles/profiles.service");
const organisation_loyalty_points_repository_1 = require("./organisation-loyalty-points.repository");
const organisation_loyalty_points_model_1 = require("./models/organisation-loyalty-points.model");
const memberships_service_1 = require("../memberships/memberships.service");
const organisations_service_1 = require("../organisations/organisations.service");
let OrganisationLoyaltyPointsService = OrganisationLoyaltyPointsService_1 = class OrganisationLoyaltyPointsService extends (0, base_service_1.BaseService)(organisation_loyalty_points_model_1.OrganisationLoyaltyPoint) {
    constructor(organisationLoyaltyPointModel) {
        super();
        this.organisationLoyaltyPointModel = organisationLoyaltyPointModel;
    }
    async getOrgLoyaltyPointsList({ organisationId, organisationLoyaltyPointsArgs, }) {
        this.logger.verbose('OrganisationLoyaltyPointsService.getOrgLoyaltyPointsList', {
            organisationId,
            organisationLoyaltyPointsArgs,
        });
        const { records, totalCount } = await this.organisationLoyaltyPointsRepository.findOrgLoyaltyPoints({
            organisationId,
            organisationLoyaltyPointsArgs,
        });
        return {
            records,
            totalCount,
        };
    }
    async sumPointsByType({ timezone, organisationId, type, }) {
        const startOfDay = moment_timezone_1.default.tz(timezone).startOf('day').toDate();
        const endOfDay = moment_timezone_1.default.tz(timezone).endOf('day').toDate();
        let totalPointsEntry;
        if (type === 'OwnerPostCommentLike' || type === 'OwnerPostCommentReply') {
            totalPointsEntry = await this.findAll({
                organisationId,
                createdAt: {
                    [sequelize_2.Op.between]: [startOfDay, endOfDay],
                },
                type: {
                    [sequelize_2.Op.in]: ['OwnerPostCommentLike', 'OwnerPostCommentReply'],
                },
            });
        }
        else if (type === 'CreatePost' || type === 'RemovePost') {
            totalPointsEntry = await this.findAll({
                organisationId,
                createdAt: {
                    [sequelize_2.Op.between]: [startOfDay, endOfDay],
                },
                type: {
                    [sequelize_2.Op.in]: ['CreatePost', 'RemovePost'],
                },
            });
        }
        else {
            totalPointsEntry = await this.findAll({
                type: type,
                organisationId: organisationId,
                createdAt: {
                    [sequelize_2.Op.between]: [startOfDay, endOfDay],
                },
            });
        }
        const totalPoints = totalPointsEntry.reduce((acc, entry) => acc + entry.points, 0);
        return totalPoints || 0;
    }
    async hasActivityRecordedThisMonth({ organisationId, type, timezone, }) {
        const startOfMonth = moment_timezone_1.default.tz(timezone).startOf('month').toDate();
        const endOfMonth = moment_timezone_1.default.tz(timezone).endOf('month').toDate();
        const pointsPerMonth = await this.findAll({
            type: type,
            organisationId: organisationId,
            createdAt: {
                [sequelize_2.Op.between]: [startOfMonth, endOfMonth],
            },
        });
        return pointsPerMonth.length || 0;
    }
    async addPoints({ organisationId, type, placeholders, transaction, variableRewardPoints, parentOrgId, }) {
        var _a, _b, _c, _d, _e;
        this.logger.verbose('OrganisationLoyaltyPointsService.addPoints', {
            organisationId,
            type,
            placeholders,
            variableRewardPoints,
            parentOrgId,
        });
        try {
            const findMostRecentValidEntry = entries => {
                if (!entries || entries.length === 0)
                    return null;
                for (const entry of entries) {
                    if (entry.points > 0)
                        return entry;
                }
                return null;
            };
            const getLastValidPoints = (parentOrganisationId, orgEntries, orgAsParentEntries, parentOrgEntries) => {
                let validEntry = findMostRecentValidEntry(orgEntries);
                const orgAsParentEntry = findMostRecentValidEntry(orgAsParentEntries);
                if (orgAsParentEntry &&
                    (!validEntry ||
                        new Date(orgAsParentEntry.createdAt) >
                            new Date(validEntry.createdAt))) {
                    validEntry = orgAsParentEntry;
                }
                if (parentOrganisationId) {
                    const parentValid = findMostRecentValidEntry(parentOrgEntries);
                    if (parentValid &&
                        (!validEntry ||
                            new Date(parentValid.createdAt) > new Date(validEntry.createdAt))) {
                        validEntry = parentValid;
                    }
                }
                return {
                    rollingPoints: validEntry ? validEntry.rollingPoints : 0,
                    lifeTimePoints: validEntry ? validEntry.lifeTimePoints : 0,
                };
            };
            const getLastValidPointsAsync = async (parentOrganisationId, orgEntries, orgAsParentEntries, parentOrgEntries) => {
                let validEntry = findMostRecentValidEntry(orgEntries);
                const orgAsParentEntry = findMostRecentValidEntry(orgAsParentEntries);
                if (orgAsParentEntry &&
                    (!validEntry ||
                        new Date(orgAsParentEntry.createdAt) >
                            new Date(validEntry.createdAt))) {
                    validEntry = orgAsParentEntry;
                }
                if (parentOrganisationId) {
                    const parentValid = findMostRecentValidEntry(parentOrgEntries);
                    if (parentValid &&
                        (!validEntry ||
                            new Date(parentValid.createdAt) > new Date(validEntry.createdAt))) {
                        validEntry = parentValid;
                    }
                }
                if (!validEntry && parentOrganisationId) {
                    try {
                        const childEntries = await this.organisationLoyaltyPointModel.findAll({
                            where: { parentOrganisationId: organisationId },
                            order: [['createdAt', 'DESC']],
                            limit: 5,
                            transaction,
                        });
                        const childValid = findMostRecentValidEntry(childEntries);
                        if (childValid &&
                            (!validEntry ||
                                new Date(childValid.createdAt) > new Date(validEntry.createdAt))) {
                            validEntry = childValid;
                        }
                    }
                    catch (error) {
                        this.logger.error('Error fetching child entries for loyalty points', {
                            error: error.message,
                            organisationId,
                            parentOrganisationId,
                        });
                    }
                }
                return {
                    rollingPoints: validEntry ? validEntry.rollingPoints : 0,
                    lifeTimePoints: validEntry ? validEntry.lifeTimePoints : 0,
                };
            };
            const organisation = await this.organisationsService.findById(organisationId, {
                transaction,
            });
            if (!organisation) {
                this.logger.error(`Organisation not found for id ${organisationId}`);
                return {
                    id: null,
                };
            }
            let parentOrganisationId = parentOrgId || null;
            if (!parentOrganisationId && ((_a = organisation.parentOrganisations) === null || _a === void 0 ? void 0 : _a.length) > 0) {
                parentOrganisationId = organisation.parentOrganisations[0].id;
            }
            const pointsConfig = OrganisationLoyaltyPointsService_1.POINTS[type];
            if (!pointsConfig) {
                this.logger.error(`Invalid activity type ${type}`);
                this.errorHelper.throwHttpException('LoyaltyPointsService.addPoints', 'Invalid activity type');
            }
            const points = variableRewardPoints !== null && variableRewardPoints !== void 0 ? variableRewardPoints : pointsConfig.points;
            if (type === activities_args_1.ActivityType.CreatePost) {
                const orgPointsToday = await this.findAll({
                    organisationId,
                    type: activities_args_1.ActivityType.CreatePost,
                    points: { [sequelize_2.Op.gt]: 0 },
                    createdAt: {
                        [sequelize_2.Op.between]: [
                            (0, moment_timezone_1.default)().startOf('day').toDate(),
                            (0, moment_timezone_1.default)().endOf('day').toDate(),
                        ],
                    },
                });
                let parentPointsToday = [];
                if (parentOrganisationId) {
                    parentPointsToday = await this.findAll({
                        organisationId: parentOrganisationId,
                        type: activities_args_1.ActivityType.CreatePost,
                        points: { [sequelize_2.Op.gt]: 0 },
                        createdAt: {
                            [sequelize_2.Op.between]: [
                                (0, moment_timezone_1.default)().startOf('day').toDate(),
                                (0, moment_timezone_1.default)().endOf('day').toDate(),
                            ],
                        },
                    });
                }
                const orgAsParentPointsToday = await this.findAll({
                    parentOrganisationId: organisationId,
                    type: activities_args_1.ActivityType.CreatePost,
                    points: { [sequelize_2.Op.gt]: 0 },
                    createdAt: {
                        [sequelize_2.Op.between]: [
                            (0, moment_timezone_1.default)().startOf('day').toDate(),
                            (0, moment_timezone_1.default)().endOf('day').toDate(),
                        ],
                    },
                });
                const orgEntries = await this.organisationLoyaltyPointModel.findAll({
                    where: { organisationId },
                    order: [['createdAt', 'DESC']],
                    limit: 5,
                    transaction,
                });
                const orgAsParentEntries = await this.organisationLoyaltyPointModel.findAll({
                    where: { parentOrganisationId: organisationId },
                    order: [['createdAt', 'DESC']],
                    limit: 5,
                    transaction,
                });
                let parentOrgEntries = [];
                if (parentOrganisationId) {
                    parentOrgEntries = await this.organisationLoyaltyPointModel.findAll({
                        where: { organisationId: parentOrganisationId },
                        order: [['createdAt', 'DESC']],
                        limit: 5,
                        transaction,
                    });
                }
                if (orgPointsToday.length > 0) {
                    const { rollingPoints, lifeTimePoints } = await getLastValidPointsAsync(parentOrganisationId, orgEntries, orgAsParentEntries, parentOrgEntries);
                    return await this.create({
                        organisationId,
                        parentOrganisationId: parentOrganisationId || null,
                        type,
                        points: OrganisationLoyaltyPointsService_1.POINTS[activities_args_1.ActivityType.ZeroLoyaltyPoints].points,
                        rollingPoints,
                        lifeTimePoints,
                        tier: '',
                        placeholders: Object.assign(Object.assign({}, placeholders), { dailyLimitReached: true }),
                    }, { transaction });
                }
                if (parentPointsToday.length > 0) {
                    const { rollingPoints, lifeTimePoints } = await getLastValidPointsAsync(parentOrganisationId, orgEntries, orgAsParentEntries, parentOrgEntries);
                    return await this.create({
                        organisationId,
                        parentOrganisationId,
                        type,
                        points: OrganisationLoyaltyPointsService_1.POINTS[activities_args_1.ActivityType.ZeroLoyaltyPoints].points,
                        rollingPoints,
                        lifeTimePoints,
                        tier: '',
                        placeholders: Object.assign(Object.assign({}, placeholders), { parentDailyLimitReached: true }),
                    }, { transaction });
                }
                if (orgAsParentPointsToday.length > 0) {
                    const { rollingPoints, lifeTimePoints } = await getLastValidPointsAsync(parentOrganisationId, orgEntries, orgAsParentEntries, parentOrgEntries);
                    return await this.create({
                        organisationId,
                        parentOrganisationId: parentOrganisationId || null,
                        type,
                        points: OrganisationLoyaltyPointsService_1.POINTS[activities_args_1.ActivityType.ZeroLoyaltyPoints].points,
                        rollingPoints,
                        lifeTimePoints,
                        tier: '',
                        placeholders: Object.assign(Object.assign({}, placeholders), { dailyLimitReached: true }),
                    }, { transaction });
                }
            }
            else {
                const totalPoints = await this.sumPointsByType({
                    organisationId,
                    type,
                    timezone: organisation.timezone || 'UTC',
                });
                const hasActivityRecordedThisMonth = await this.hasActivityRecordedThisMonth({
                    organisationId,
                    type,
                    timezone: organisation.timezone || 'UTC',
                });
                const orgEntries = await this.organisationLoyaltyPointModel.findAll({
                    where: { organisationId },
                    order: [['createdAt', 'DESC']],
                    limit: 10,
                    transaction,
                });
                const orgAsParentEntries = await this.organisationLoyaltyPointModel.findAll({
                    where: { parentOrganisationId: organisationId },
                    order: [['createdAt', 'DESC']],
                    limit: 10,
                    transaction,
                });
                let parentOrgEntries = [];
                if (parentOrganisationId) {
                    parentOrgEntries = await this.organisationLoyaltyPointModel.findAll({
                        where: {
                            [sequelize_2.Op.or]: [
                                { organisationId: parentOrganisationId },
                                { parentOrganisationId: parentOrganisationId },
                            ],
                        },
                        order: [['createdAt', 'DESC']],
                        limit: 10,
                        transaction,
                    });
                }
                let validPointEntry = null;
                let basePoints = 0;
                let baseRollingPoints = 0;
                let baseLifeTimePoints = 0;
                validPointEntry = findMostRecentValidEntry(orgEntries);
                if (!validPointEntry) {
                    validPointEntry = findMostRecentValidEntry(orgAsParentEntries);
                }
                if (validPointEntry) {
                    basePoints = validPointEntry.points;
                    baseRollingPoints = validPointEntry.rollingPoints;
                    baseLifeTimePoints = validPointEntry.lifeTimePoints;
                }
                if (parentOrganisationId) {
                    const parentValidEntry = findMostRecentValidEntry(parentOrgEntries);
                    if (parentValidEntry &&
                        (!validPointEntry ||
                            new Date(parentValidEntry.createdAt) >
                                new Date(validPointEntry.createdAt))) {
                        validPointEntry = parentValidEntry;
                        basePoints = parentValidEntry.points;
                        baseRollingPoints = parentValidEntry.rollingPoints;
                        baseLifeTimePoints = parentValidEntry.lifeTimePoints;
                    }
                }
                let lastLoyaltyPointEntry = null;
                const allEntries = [...orgEntries, ...orgAsParentEntries];
                if (allEntries.length > 0) {
                    allEntries.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
                    lastLoyaltyPointEntry = allEntries[0];
                }
                if (!lastLoyaltyPointEntry) {
                    let initialRollingPoints = points;
                    let initialLifetimePoints = points;
                    if (parentOrganisationId) {
                        const parentLatestEntry = await this.findOne({ organisationId: parentOrganisationId }, { order: [['createdAt', 'DESC']] });
                        if (parentLatestEntry) {
                            initialRollingPoints = parentLatestEntry.rollingPoints + points;
                            initialLifetimePoints = parentLatestEntry.lifeTimePoints + points;
                        }
                    }
                    let newEntryPlaceholder = placeholders;
                    if (pointsConfig.groupConsecutiveEntries) {
                        newEntryPlaceholder = Object.assign(Object.assign({}, placeholders), { consecutivePostsCount: 1 });
                    }
                    const newEntryData = {
                        organisationId,
                        parentOrganisationId: parentOrganisationId || null,
                        type,
                        points,
                        rollingPoints: initialRollingPoints,
                        lifeTimePoints: initialLifetimePoints,
                        tier: '',
                        placeholders: newEntryPlaceholder,
                    };
                    const newEntry = await this.create(newEntryData, { transaction });
                    return newEntry;
                }
                if (String(type) === String(activities_args_1.ActivityType.CreatePost)) {
                    if (lastLoyaltyPointEntry &&
                        new Date(lastLoyaltyPointEntry.createdAt).toDateString() ===
                            new Date().toDateString() &&
                        (pointsConfig === null || pointsConfig === void 0 ? void 0 : pointsConfig.maxPoints) &&
                        totalPoints + points > (pointsConfig === null || pointsConfig === void 0 ? void 0 : pointsConfig.maxPoints)) {
                        this.logger.info(`Organisation id ${organisationId} has already reached the max points for posts today`, {
                            organisationId,
                            totalPoints,
                            maxPoints: pointsConfig === null || pointsConfig === void 0 ? void 0 : pointsConfig.maxPoints,
                        });
                        const { rollingPoints, lifeTimePoints } = getLastValidPoints(parentOrganisationId, orgEntries, orgAsParentEntries, parentOrgEntries);
                        return await this.create(Object.assign(Object.assign({}, ((_a) => {
                            var { id, createdAt } = _a, rest = __rest(_a, ["id", "createdAt"]);
                            return rest;
                        })(lastLoyaltyPointEntry.toJSON())), { type, points: OrganisationLoyaltyPointsService_1.POINTS[activities_args_1.ActivityType.ZeroLoyaltyPoints].points, rollingPoints,
                            lifeTimePoints, placeholders: Object.assign(Object.assign({}, placeholders), { dailyLimitReached: true }) }));
                    }
                }
                else if (lastLoyaltyPointEntry) {
                    if ((new Date(lastLoyaltyPointEntry.createdAt).toDateString() ===
                        new Date().toDateString() &&
                        (pointsConfig === null || pointsConfig === void 0 ? void 0 : pointsConfig.maxPoints) &&
                        totalPoints + points > (pointsConfig === null || pointsConfig === void 0 ? void 0 : pointsConfig.maxPoints)) ||
                        ((pointsConfig === null || pointsConfig === void 0 ? void 0 : pointsConfig.maxPointsPerMonth) &&
                            hasActivityRecordedThisMonth + points >
                                (pointsConfig === null || pointsConfig === void 0 ? void 0 : pointsConfig.maxPointsPerMonth))) {
                        this.logger.info(`Organisation id ${organisationId} has already reached the max points for activity type ${type}`, {
                            organisationId,
                            type,
                            totalPoints,
                            hasActivityRecordedThisMonth,
                            maxPoints: pointsConfig === null || pointsConfig === void 0 ? void 0 : pointsConfig.maxPoints,
                            maxPointsPerMonth: pointsConfig === null || pointsConfig === void 0 ? void 0 : pointsConfig.maxPointsPerMonth,
                        });
                        const limitKey = (pointsConfig === null || pointsConfig === void 0 ? void 0 : pointsConfig.maxPoints)
                            ? 'dailyLimitReached'
                            : 'monthlyLimitReached';
                        const { rollingPoints, lifeTimePoints } = getLastValidPoints(parentOrganisationId, orgEntries, orgAsParentEntries, parentOrgEntries);
                        return await this.create(Object.assign(Object.assign({}, ((_a) => {
                            var { id, createdAt } = _a, rest = __rest(_a, ["id", "createdAt"]);
                            return rest;
                        })(lastLoyaltyPointEntry.toJSON())), { type, points: OrganisationLoyaltyPointsService_1.POINTS[activities_args_1.ActivityType.ZeroLoyaltyPoints].points, rollingPoints,
                            lifeTimePoints, placeholders: Object.assign(Object.assign({}, placeholders), { [limitKey]: true }) }));
                    }
                }
                let newEntryPlaceholder = placeholders;
                let updateEntry = false;
                if (pointsConfig.groupConsecutiveEntries &&
                    lastLoyaltyPointEntry.type === type) {
                    const lastEntryDate = (0, moment_timezone_1.default)(lastLoyaltyPointEntry.createdAt).startOf('day');
                    const currentDate = (0, moment_timezone_1.default)().startOf('day');
                    const isConsecutiveDay = lastEntryDate.isSame(currentDate, 'day');
                    if (isConsecutiveDay) {
                        newEntryPlaceholder = Object.assign(Object.assign({}, placeholders), { consecutivePostsCount: ((_b = lastLoyaltyPointEntry.placeholders) === null || _b === void 0 ? void 0 : _b.consecutivePostsCount) + 1 });
                        updateEntry = true;
                    }
                    else {
                        newEntryPlaceholder = Object.assign(Object.assign({}, placeholders), { consecutivePostsCount: 1 });
                    }
                }
                else if (pointsConfig.groupConsecutiveEntries) {
                    newEntryPlaceholder = Object.assign(Object.assign({}, placeholders), { consecutivePostsCount: 1 });
                }
                const lastEntryMonth = lastLoyaltyPointEntry
                    ? (0, moment_timezone_1.default)(lastLoyaltyPointEntry.createdAt).month()
                    : (0, moment_timezone_1.default)().month();
                const currentMonth = (0, moment_timezone_1.default)().month();
                const resetRollingPoints = lastEntryMonth !== currentMonth;
                const { streak: lastEntryStreak = 0, currentTierIndex: lastEntryCurrentTierIndex = 0, nextTierIndex: lastEntryNextTierIndex = 1, previousMonthTierIndex: lastEntryPreviousMonthTierIndex = 0, streakUpdatedAt: lastEntryStreakUpdatedAt = new Date(), } = lastLoyaltyPointEntry || {};
                let rollingPoints = baseRollingPoints + points;
                let nextTierIndex = lastEntryNextTierIndex;
                let currentTierIndex = lastEntryCurrentTierIndex;
                let previousMonthTierIndex = lastEntryPreviousMonthTierIndex;
                let streak = lastEntryStreak;
                let streakUpdatedAt = lastEntryStreakUpdatedAt;
                const lifeTimePoints = baseLifeTimePoints + points;
                const totalTiers = OrganisationLoyaltyPointsService_1.TIERS.length - 1;
                const lastRollingPointsTierIndex = baseRollingPoints >
                    OrganisationLoyaltyPointsService_1.TIERS[totalTiers].maxPoints
                    ? totalTiers
                    : OrganisationLoyaltyPointsService_1.TIERS.findIndex(t => t.minPoints <= baseRollingPoints &&
                        t.maxPoints >= baseRollingPoints);
                const rollingPointsTierIndex = OrganisationLoyaltyPointsService_1.TIERS.findIndex(t => t.minPoints <= rollingPoints && t.maxPoints >= rollingPoints);
                if (resetRollingPoints) {
                    previousMonthTierIndex = currentTierIndex;
                    nextTierIndex = currentTierIndex;
                    rollingPoints = points;
                    currentTierIndex = 0;
                    if (baseRollingPoints <
                        OrganisationLoyaltyPointsService_1.TIERS[totalTiers].minPoints) {
                        streak = 0;
                    }
                    if (lastRollingPointsTierIndex < lastEntryCurrentTierIndex - 1) {
                        previousMonthTierIndex = lastEntryCurrentTierIndex - 1;
                        nextTierIndex = lastEntryCurrentTierIndex - 1;
                    }
                    if (lastEntryPreviousMonthTierIndex > lastEntryCurrentTierIndex) {
                        previousMonthTierIndex = lastEntryPreviousMonthTierIndex - 1;
                        nextTierIndex = lastEntryPreviousMonthTierIndex - 1;
                    }
                    if (nextTierIndex < 1) {
                        nextTierIndex = 1;
                    }
                }
                else {
                    if (rollingPoints >
                        ((_c = OrganisationLoyaltyPointsService_1.TIERS[lastRollingPointsTierIndex]) === null || _c === void 0 ? void 0 : _c.maxPoints) &&
                        lastRollingPointsTierIndex === totalTiers - 1) {
                        streak += 1;
                        streakUpdatedAt = new Date();
                    }
                    else if (points < 0 &&
                        lastEntryStreak > 0 &&
                        rollingPoints <
                            OrganisationLoyaltyPointsService_1.TIERS[lastRollingPointsTierIndex]
                                .minPoints) {
                        const today = (0, moment_timezone_1.default)().startOf('day').toDate();
                        const lastStreakDate = (0, moment_timezone_1.default)(streakUpdatedAt).toDate();
                        if ((0, moment_timezone_1.default)(today).isSame(lastStreakDate, 'month')) {
                            streak -= 1;
                            streakUpdatedAt = new Date();
                        }
                    }
                    if (rollingPoints >
                        OrganisationLoyaltyPointsService_1.TIERS[currentTierIndex].maxPoints) {
                        currentTierIndex += 1;
                        if (currentTierIndex > totalTiers) {
                            currentTierIndex -= 1;
                        }
                        nextTierIndex = currentTierIndex + 1;
                        if (nextTierIndex > totalTiers) {
                            nextTierIndex = totalTiers;
                        }
                    }
                    else if (points < 0 &&
                        lastEntryStreak > 0 &&
                        lastRollingPointsTierIndex === totalTiers &&
                        rollingPoints <
                            OrganisationLoyaltyPointsService_1.TIERS[currentTierIndex].minPoints) {
                        const startOfMonthDate = (0, moment_timezone_1.default)().startOf('month').toDate();
                        const lastCurrentTierEntry = await this.findOne({
                            organisationId,
                            currentTierIndex: currentTierIndex - 1,
                            createdAt: {
                                [sequelize_2.Op.gte]: startOfMonthDate,
                            },
                        });
                        if (lastCurrentTierEntry) {
                            currentTierIndex -= 1;
                            if (currentTierIndex < 0) {
                                currentTierIndex = 0;
                            }
                            if (!(lastEntryStreak > 0)) {
                                nextTierIndex = currentTierIndex + 1;
                                if (nextTierIndex > totalTiers) {
                                    nextTierIndex = totalTiers;
                                }
                            }
                        }
                    }
                    else if (rollingPointsTierIndex === currentTierIndex &&
                        currentTierIndex === nextTierIndex) {
                        nextTierIndex = currentTierIndex + 1;
                        if (nextTierIndex > totalTiers) {
                            nextTierIndex = totalTiers;
                        }
                    }
                }
                const activeTier = currentTierIndex >= previousMonthTierIndex
                    ? currentTierIndex
                    : previousMonthTierIndex;
                const loyaltyPointEntry = updateEntry
                    ? await this.updateById(lastLoyaltyPointEntry.id, {
                        points: lastLoyaltyPointEntry.points + points,
                        type,
                        rollingPoints,
                        lifeTimePoints,
                        organisationId,
                        parentOrganisationId: parentOrganisationId || null,
                        streak,
                        tier: '',
                        placeholders: newEntryPlaceholder,
                        currentTierIndex,
                        nextTierIndex,
                        previousMonthTierIndex,
                        activeTier,
                        streakUpdatedAt,
                    }, {
                        transaction,
                    })
                    : await this.create({
                        points,
                        type,
                        rollingPoints,
                        lifeTimePoints,
                        organisationId,
                        parentOrganisationId: parentOrganisationId || null,
                        streak,
                        tier: '',
                        placeholders: newEntryPlaceholder,
                        currentTierIndex,
                        nextTierIndex,
                        previousMonthTierIndex,
                        activeTier,
                        streakUpdatedAt,
                    }, {
                        transaction,
                    });
                this.logger.verbose('OrganisationLoyaltyPointsService.addPoints (points added)', {
                    organisationId,
                    type,
                    points,
                });
                let isTierUpgraded = currentTierIndex > lastEntryCurrentTierIndex;
                let isTierRetained = isTierUpgraded
                    ? false
                    : rollingPointsTierIndex === currentTierIndex &&
                        rollingPointsTierIndex > lastRollingPointsTierIndex;
                if (isTierUpgraded || isTierRetained) {
                    const currentTier = OrganisationLoyaltyPointsService_1.TIERS[currentTierIndex];
                    const nextTier = currentTierIndex !== nextTierIndex
                        ? OrganisationLoyaltyPointsService_1.TIERS[nextTierIndex]
                        : null;
                    this.logger.info(`Organisation ${organisationId} has been ${isTierUpgraded ? 'upgraded' : 'retained'} to ${currentTier.name} tier.`);
                }
                return loyaltyPointEntry;
            }
            const totalPoints = await this.sumPointsByType({
                organisationId,
                type,
                timezone: organisation.timezone || 'UTC',
            });
            const hasActivityRecordedThisMonth = await this.hasActivityRecordedThisMonth({
                organisationId,
                type,
                timezone: organisation.timezone || 'UTC',
            });
            let lastLoyaltyPointEntry = await this.findOne({
                organisationId,
                parentOrganisationId: parentOrganisationId || { [sequelize_2.Op.is]: null },
            }, {
                order: [['createdAt', 'DESC']],
                transaction,
            });
            if (!lastLoyaltyPointEntry) {
                lastLoyaltyPointEntry = await this.findOne({
                    organisationId,
                }, {
                    order: [['createdAt', 'DESC']],
                    transaction,
                });
            }
            if (!lastLoyaltyPointEntry) {
                let newEntryPlaceholder = placeholders;
                if (pointsConfig.groupConsecutiveEntries) {
                    newEntryPlaceholder = Object.assign(Object.assign({}, placeholders), { consecutivePostsCount: 1 });
                }
                const newEntryData = {
                    organisationId,
                    parentOrganisationId: parentOrganisationId || null,
                    type,
                    points,
                    rollingPoints: points,
                    lifeTimePoints: points,
                    tier: '',
                    placeholders: newEntryPlaceholder,
                };
                const newEntry = await this.create(newEntryData, { transaction });
                return newEntry;
            }
            if (lastLoyaltyPointEntry &&
                ((new Date(lastLoyaltyPointEntry.createdAt).toDateString() ===
                    new Date().toDateString() &&
                    (pointsConfig === null || pointsConfig === void 0 ? void 0 : pointsConfig.maxPoints) &&
                    totalPoints + points > (pointsConfig === null || pointsConfig === void 0 ? void 0 : pointsConfig.maxPoints)) ||
                    ((pointsConfig === null || pointsConfig === void 0 ? void 0 : pointsConfig.maxPointsPerMonth) &&
                        hasActivityRecordedThisMonth + points >
                            (pointsConfig === null || pointsConfig === void 0 ? void 0 : pointsConfig.maxPointsPerMonth)))) {
                this.logger.info(`Organisation id ${organisationId} has already reached the max points for activity type ${type}`);
                const limitKey = (pointsConfig === null || pointsConfig === void 0 ? void 0 : pointsConfig.maxPoints)
                    ? 'dailyLimitReached'
                    : 'monthlyLimitReached';
                return await this.create(Object.assign(Object.assign({}, ((_a) => {
                    var { id, createdAt } = _a, rest = __rest(_a, ["id", "createdAt"]);
                    return rest;
                })(lastLoyaltyPointEntry.toJSON())), { type, points: OrganisationLoyaltyPointsService_1.POINTS[activities_args_1.ActivityType.ZeroLoyaltyPoints].points, placeholders: Object.assign(Object.assign({}, placeholders), { [limitKey]: true }) }));
            }
            let newEntryPlaceholder = placeholders;
            let updateEntry = false;
            if (pointsConfig.groupConsecutiveEntries &&
                lastLoyaltyPointEntry.type === type) {
                const lastEntryDate = (0, moment_timezone_1.default)(lastLoyaltyPointEntry.createdAt).startOf('day');
                const currentDate = (0, moment_timezone_1.default)().startOf('day');
                const isConsecutiveDay = lastEntryDate.isSame(currentDate, 'day');
                if (isConsecutiveDay) {
                    newEntryPlaceholder = Object.assign(Object.assign({}, placeholders), { consecutivePostsCount: ((_d = lastLoyaltyPointEntry.placeholders) === null || _d === void 0 ? void 0 : _d.consecutivePostsCount) + 1 });
                    updateEntry = true;
                }
                else {
                    newEntryPlaceholder = Object.assign(Object.assign({}, placeholders), { consecutivePostsCount: 1 });
                }
            }
            else if (pointsConfig.groupConsecutiveEntries) {
                newEntryPlaceholder = Object.assign(Object.assign({}, placeholders), { consecutivePostsCount: 1 });
            }
            const lastEntryMonth = (0, moment_timezone_1.default)(lastLoyaltyPointEntry.createdAt).month();
            const currentMonth = (0, moment_timezone_1.default)().month();
            const resetRollingPoints = lastEntryMonth !== currentMonth;
            const { rollingPoints: lastEntryRollingPoints, lifeTimePoints: lastEntryLifeTimePoints, streak: lastEntryStreak, currentTierIndex: lastEntryCurrentTierIndex, nextTierIndex: lastEntryNextTierIndex, previousMonthTierIndex: lastEntryPreviousMonthTierIndex, streakUpdatedAt: lastEntryStreakUpdatedAt, } = lastLoyaltyPointEntry;
            let rollingPoints = lastEntryRollingPoints + points;
            let nextTierIndex = lastEntryNextTierIndex;
            let currentTierIndex = lastEntryCurrentTierIndex;
            let previousMonthTierIndex = lastEntryPreviousMonthTierIndex;
            let streak = lastEntryStreak;
            let streakUpdatedAt = lastEntryStreakUpdatedAt;
            const lifeTimePoints = lastEntryLifeTimePoints + points;
            const totalTiers = OrganisationLoyaltyPointsService_1.TIERS.length - 1;
            const lastRollingPointsTierIndex = lastEntryRollingPoints >
                OrganisationLoyaltyPointsService_1.TIERS[totalTiers].maxPoints
                ? totalTiers
                : OrganisationLoyaltyPointsService_1.TIERS.findIndex(t => t.minPoints <= lastEntryRollingPoints &&
                    t.maxPoints >= lastEntryRollingPoints);
            const rollingPointsTierIndex = OrganisationLoyaltyPointsService_1.TIERS.findIndex(t => t.minPoints <= rollingPoints && t.maxPoints >= rollingPoints);
            if (resetRollingPoints) {
                previousMonthTierIndex = currentTierIndex;
                nextTierIndex = currentTierIndex;
                rollingPoints = points;
                currentTierIndex = 0;
                if (lastEntryRollingPoints <
                    OrganisationLoyaltyPointsService_1.TIERS[totalTiers].minPoints) {
                    streak = 0;
                }
                if (lastRollingPointsTierIndex < lastEntryCurrentTierIndex - 1) {
                    previousMonthTierIndex = lastEntryCurrentTierIndex - 1;
                    nextTierIndex = lastEntryCurrentTierIndex - 1;
                }
                if (lastEntryPreviousMonthTierIndex > lastEntryCurrentTierIndex) {
                    previousMonthTierIndex = lastEntryPreviousMonthTierIndex - 1;
                    nextTierIndex = lastEntryPreviousMonthTierIndex - 1;
                }
                if (nextTierIndex < 1) {
                    nextTierIndex = 1;
                }
            }
            else {
                if (rollingPoints >
                    ((_e = OrganisationLoyaltyPointsService_1.TIERS[lastRollingPointsTierIndex]) === null || _e === void 0 ? void 0 : _e.maxPoints) &&
                    lastRollingPointsTierIndex === totalTiers - 1) {
                    streak += 1;
                    streakUpdatedAt = new Date();
                }
                else if (points < 0 &&
                    lastEntryStreak > 0 &&
                    rollingPoints <
                        OrganisationLoyaltyPointsService_1.TIERS[lastRollingPointsTierIndex]
                            .minPoints) {
                    const today = (0, moment_timezone_1.default)().startOf('day').toDate();
                    const lastStreakDate = (0, moment_timezone_1.default)(streakUpdatedAt).toDate();
                    if ((0, moment_timezone_1.default)(today).isSame(lastStreakDate, 'month')) {
                        streak -= 1;
                        streakUpdatedAt = new Date();
                    }
                }
                if (rollingPoints >
                    OrganisationLoyaltyPointsService_1.TIERS[currentTierIndex].maxPoints) {
                    currentTierIndex += 1;
                    if (currentTierIndex > totalTiers) {
                        currentTierIndex -= 1;
                    }
                    nextTierIndex = currentTierIndex + 1;
                    if (nextTierIndex > totalTiers) {
                        nextTierIndex = totalTiers;
                    }
                }
                else if (points < 0 &&
                    lastEntryStreak > 0 &&
                    lastRollingPointsTierIndex === totalTiers &&
                    rollingPoints <
                        OrganisationLoyaltyPointsService_1.TIERS[currentTierIndex].minPoints) {
                    const startOfMonthDate = (0, moment_timezone_1.default)().startOf('month').toDate();
                    const lastCurrentTierEntry = await this.findOne({
                        organisationId,
                        currentTierIndex: currentTierIndex - 1,
                        createdAt: {
                            [sequelize_2.Op.gte]: startOfMonthDate,
                        },
                    });
                    if (lastCurrentTierEntry) {
                        currentTierIndex -= 1;
                        if (currentTierIndex < 0) {
                            currentTierIndex = 0;
                        }
                        if (!(lastEntryStreak > 0)) {
                            nextTierIndex = currentTierIndex + 1;
                            if (nextTierIndex > totalTiers) {
                                nextTierIndex = totalTiers;
                            }
                        }
                    }
                }
                else if (rollingPointsTierIndex === currentTierIndex &&
                    currentTierIndex === nextTierIndex) {
                    nextTierIndex = currentTierIndex + 1;
                    if (nextTierIndex > totalTiers) {
                        nextTierIndex = totalTiers;
                    }
                }
            }
            const activeTier = currentTierIndex >= previousMonthTierIndex
                ? currentTierIndex
                : previousMonthTierIndex;
            const loyaltyPointEntry = updateEntry
                ? await this.updateById(lastLoyaltyPointEntry.id, {
                    points: lastLoyaltyPointEntry.points + points,
                    type,
                    rollingPoints,
                    lifeTimePoints,
                    organisationId,
                    parentOrganisationId: parentOrganisationId || null,
                    streak,
                    tier: '',
                    placeholders: newEntryPlaceholder,
                    currentTierIndex,
                    nextTierIndex,
                    previousMonthTierIndex,
                    activeTier,
                    streakUpdatedAt,
                }, {
                    transaction,
                })
                : await this.create({
                    points,
                    type,
                    rollingPoints,
                    lifeTimePoints,
                    organisationId,
                    parentOrganisationId: parentOrganisationId || null,
                    streak,
                    tier: '',
                    placeholders: newEntryPlaceholder,
                    currentTierIndex,
                    nextTierIndex,
                    previousMonthTierIndex,
                    activeTier,
                    streakUpdatedAt,
                }, {
                    transaction,
                });
            this.logger.verbose('OrganisationLoyaltyPointsService.addPoints (points added)', {
                organisationId,
                type,
                points,
            });
            let isTierUpgraded = currentTierIndex > lastEntryCurrentTierIndex;
            let isTierRetained = isTierUpgraded
                ? false
                : rollingPointsTierIndex === currentTierIndex &&
                    rollingPointsTierIndex > lastRollingPointsTierIndex;
            if (isTierUpgraded || isTierRetained) {
                const currentTier = OrganisationLoyaltyPointsService_1.TIERS[currentTierIndex];
                const nextTier = currentTierIndex !== nextTierIndex
                    ? OrganisationLoyaltyPointsService_1.TIERS[nextTierIndex]
                    : null;
                this.logger.info(`Organisation ${organisationId} has been ${isTierUpgraded ? 'upgraded' : 'retained'} to ${currentTier.name} tier.`);
            }
            return loyaltyPointEntry;
        }
        catch (error) {
            this.logger.error('Error in addPoints:', error);
            this.errorHelper.throwHttpException(`LoyaltyPointsService.addPoints`, error.message);
        }
    }
    async getOrganisationLoyaltyPoints({ organisationId, }) {
        const lastLoyaltyPointEntry = await this.findOne({
            organisationId,
        }, {
            order: [['createdAt', 'DESC']],
        });
        let rollingPoints = 0;
        let lifeTimePoints = 0;
        let streak = 0;
        let activeTier = OrganisationLoyaltyPointsService_1.TIERS[0];
        let currentTier = OrganisationLoyaltyPointsService_1.TIERS[0];
        let nextTier = OrganisationLoyaltyPointsService_1.TIERS[1];
        let previousMonthTier = OrganisationLoyaltyPointsService_1.TIERS[0];
        if (lastLoyaltyPointEntry) {
            const organisation = await this.organisationsService.findById(organisationId);
            if (!organisation) {
                this.logger.error(`Organisation not found for id ${organisationId}`);
                return {
                    rollingPoints,
                    lifeTimePoints,
                    streak,
                    currentTier,
                    nextTier,
                    previousMonthTier,
                    activeTier,
                };
            }
            const lastEntryMonth = (0, moment_timezone_1.default)(lastLoyaltyPointEntry.createdAt).month();
            const currentMonth = (0, moment_timezone_1.default)().month();
            const resetRollingPoints = lastEntryMonth !== currentMonth;
            lifeTimePoints = lastLoyaltyPointEntry.lifeTimePoints;
            previousMonthTier =
                OrganisationLoyaltyPointsService_1.TIERS[lastLoyaltyPointEntry.previousMonthTierIndex];
            if (resetRollingPoints) {
                rollingPoints = 0;
                nextTier =
                    OrganisationLoyaltyPointsService_1.TIERS[lastLoyaltyPointEntry.currentTierIndex];
            }
            else {
                rollingPoints = lastLoyaltyPointEntry.rollingPoints;
                nextTier =
                    OrganisationLoyaltyPointsService_1.TIERS[lastLoyaltyPointEntry.nextTierIndex];
                currentTier =
                    OrganisationLoyaltyPointsService_1.TIERS[lastLoyaltyPointEntry.currentTierIndex];
            }
            activeTier =
                OrganisationLoyaltyPointsService_1.TIERS[lastLoyaltyPointEntry.activeTier];
            streak = lastLoyaltyPointEntry.streak;
        }
        return {
            rollingPoints,
            lifeTimePoints,
            streak,
            currentTier,
            nextTier,
            previousMonthTier,
            activeTier,
        };
    }
    async findOneByOrganisationId(organisationId) {
        const OrganisationLoyaltyPoint = await this.organisationLoyaltyPointModel.findOne({
            where: { organisationId },
            order: [['createdAt', 'DESC']],
        });
        return OrganisationLoyaltyPoint;
    }
    getTierFromIndex(tierIndex) {
        const tier = OrganisationLoyaltyPointsService_1.TIERS[tierIndex];
        return tier ? tier.key : 'blue';
    }
    async addZeroLoyaltyPointsToOrganisations() {
        const allOrganisationIds = await this.organisationsService.findOrganisations('', {
            hasClubHabloSubscription: true,
        }, {});
        await Promise.all(allOrganisationIds.records.map(async (organisation) => {
            await this.addPoints({
                organisationId: organisation.id,
                type: activities_args_1.ActivityType.ZeroLoyaltyPoints,
            });
        }));
    }
};
exports.OrganisationLoyaltyPointsService = OrganisationLoyaltyPointsService;
OrganisationLoyaltyPointsService.POINTS = {
    [activities_args_1.ActivityType.CreatePost]: {
        points: 200,
        maxPoints: 200,
    },
    [activities_args_1.ActivityType.OwnerPostCommentLike]: {
        points: 10,
        maxPoints: 50,
    },
    [activities_args_1.ActivityType.OwnerPostCommentReply]: {
        points: 10,
        maxPoints: 50,
    },
    [activities_args_1.ActivityType.UserInvitedToEvent]: {
        points: 300,
        maxPointsPerMonth: 300,
    },
    [activities_args_1.ActivityType.UserInvitedToWebinar]: {
        points: 200,
        maxPointsPerMonth: 200,
    },
    [activities_args_1.ActivityType.UserInvitedToIncentive]: {
        points: 300,
        maxPointsPerMonth: 300,
    },
    [activities_args_1.ActivityType.ZeroLoyaltyPoints]: {
        points: 0,
    },
    [activities_args_1.ActivityType.RemovePost]: {
        points: -200,
    },
};
OrganisationLoyaltyPointsService.TIERS = [
    {
        key: 'blue',
        name: 'Blue',
        minPoints: 0,
        maxPoints: 649,
    },
    {
        key: 'silver',
        name: 'Silver',
        minPoints: 650,
        maxPoints: 1499,
    },
    {
        key: 'gold',
        name: 'Gold',
        minPoints: 1500,
        maxPoints: 999999,
    },
];
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", sequelize_typescript_1.Sequelize)
], OrganisationLoyaltyPointsService.prototype, "sequelize", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => notifications_service_1.NotificationsService)),
    __metadata("design:type", notifications_service_1.NotificationsService)
], OrganisationLoyaltyPointsService.prototype, "notificationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], OrganisationLoyaltyPointsService.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisation_loyalty_points_repository_1.OrganisationLoyaltyPointsRepository)),
    __metadata("design:type", organisation_loyalty_points_repository_1.OrganisationLoyaltyPointsRepository)
], OrganisationLoyaltyPointsService.prototype, "organisationLoyaltyPointsRepository", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], OrganisationLoyaltyPointsService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], OrganisationLoyaltyPointsService.prototype, "errorHelper", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], OrganisationLoyaltyPointsService.prototype, "membershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], OrganisationLoyaltyPointsService.prototype, "organisationsService", void 0);
exports.OrganisationLoyaltyPointsService = OrganisationLoyaltyPointsService = OrganisationLoyaltyPointsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, sequelize_1.InjectModel)(organisation_loyalty_points_model_1.OrganisationLoyaltyPoint)),
    __metadata("design:paramtypes", [Object])
], OrganisationLoyaltyPointsService);
//# sourceMappingURL=organisation-loyalty-points.service.js.map