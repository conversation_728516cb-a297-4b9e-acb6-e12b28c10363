"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganisationPrivacySettings = exports.OrganisationPrivacyAccessSettings = exports.OrganisationsArgs = exports.OrganisationsFilter = exports.Privacy = exports.OrganisationSize = exports.OrganisationPage = exports.OrganisationStatus = exports.OrganisationType = void 0;
const graphql_1 = require("@nestjs/graphql");
const pagination_args_1 = require("../../common/args/pagination.args");
var OrganisationType;
(function (OrganisationType) {
    OrganisationType["Destination"] = "Destination";
    OrganisationType["PrivateSector"] = "PrivateSector";
    OrganisationType["RepresentationAgency"] = "RepresentationAgency";
    OrganisationType["TourOperator"] = "TourOperator";
    OrganisationType["TravelAgency"] = "TravelAgency";
    OrganisationType["Community"] = "Community";
    OrganisationType["Association"] = "Association";
    OrganisationType["Consortia"] = "Consortia";
    OrganisationType["TMC"] = "TMC";
})(OrganisationType || (exports.OrganisationType = OrganisationType = {}));
(0, graphql_1.registerEnumType)(OrganisationType, { name: 'OrganisationType' });
var OrganisationStatus;
(function (OrganisationStatus) {
    OrganisationStatus["Active"] = "Active";
    OrganisationStatus["Pending"] = "Pending";
    OrganisationStatus["Removed"] = "Removed";
    OrganisationStatus["Suspended"] = "Suspended";
})(OrganisationStatus || (exports.OrganisationStatus = OrganisationStatus = {}));
(0, graphql_1.registerEnumType)(OrganisationStatus, { name: 'OrganisationStatus' });
var OrganisationPage;
(function (OrganisationPage) {
    OrganisationPage["Home"] = "Home";
    OrganisationPage["About"] = "About";
    OrganisationPage["Webinars"] = "Webinars";
    OrganisationPage["Events"] = "Events";
    OrganisationPage["Resources"] = "Resources";
    OrganisationPage["People"] = "People";
    OrganisationPage["Analytics"] = "Analytics";
})(OrganisationPage || (exports.OrganisationPage = OrganisationPage = {}));
(0, graphql_1.registerEnumType)(OrganisationPage, { name: 'OrganisationPage' });
var OrganisationSize;
(function (OrganisationSize) {
    OrganisationSize["OrgSize1"] = "1";
    OrganisationSize["OrgSize2_10"] = "2-10";
    OrganisationSize["OrgSize11_50"] = "11-50";
    OrganisationSize["OrgSize51_200"] = "51-200";
    OrganisationSize["OrgSize201_500"] = "201-500";
    OrganisationSize["OrgSize501_1000"] = "501-1000";
    OrganisationSize["OrgSize1001_5000"] = "1001-5000";
    OrganisationSize["OrgSize5001"] = "5001+";
})(OrganisationSize || (exports.OrganisationSize = OrganisationSize = {}));
(0, graphql_1.registerEnumType)(OrganisationSize, { name: 'OrganisationSize' });
var Privacy;
(function (Privacy) {
    Privacy["Public"] = "public";
    Privacy["Private"] = "private";
    Privacy["Protected"] = "protected";
})(Privacy || (exports.Privacy = Privacy = {}));
(0, graphql_1.registerEnumType)(Privacy, { name: 'Privacy' });
let OrganisationsFilter = class OrganisationsFilter {
};
exports.OrganisationsFilter = OrganisationsFilter;
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], OrganisationsFilter.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(() => [OrganisationType], { nullable: true, defaultValue: [] }),
    __metadata("design:type", Array)
], OrganisationsFilter.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], OrganisationsFilter.prototype, "searchText", void 0);
__decorate([
    (0, graphql_1.Field)(() => [String], { nullable: true }),
    __metadata("design:type", Array)
], OrganisationsFilter.prototype, "vanityId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true, defaultValue: false }),
    __metadata("design:type", Boolean)
], OrganisationsFilter.prototype, "isFuzzySearch", void 0);
exports.OrganisationsFilter = OrganisationsFilter = __decorate([
    (0, graphql_1.InputType)()
], OrganisationsFilter);
let OrganisationsArgs = class OrganisationsArgs extends pagination_args_1.PaginationArgs {
};
exports.OrganisationsArgs = OrganisationsArgs;
__decorate([
    (0, graphql_1.Field)(() => OrganisationsFilter, { nullable: true }),
    __metadata("design:type", OrganisationsFilter)
], OrganisationsArgs.prototype, "filter", void 0);
exports.OrganisationsArgs = OrganisationsArgs = __decorate([
    (0, graphql_1.ArgsType)()
], OrganisationsArgs);
let OrganisationPrivacyAccessSettings = class OrganisationPrivacyAccessSettings {
};
exports.OrganisationPrivacyAccessSettings = OrganisationPrivacyAccessSettings;
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], OrganisationPrivacyAccessSettings.prototype, "public", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], OrganisationPrivacyAccessSettings.prototype, "approvedFollowers", void 0);
exports.OrganisationPrivacyAccessSettings = OrganisationPrivacyAccessSettings = __decorate([
    (0, graphql_1.InputType)()
], OrganisationPrivacyAccessSettings);
let OrganisationPrivacySettings = class OrganisationPrivacySettings {
};
exports.OrganisationPrivacySettings = OrganisationPrivacySettings;
__decorate([
    (0, graphql_1.Field)(() => OrganisationPrivacyAccessSettings, { nullable: true }),
    __metadata("design:type", OrganisationPrivacyAccessSettings)
], OrganisationPrivacySettings.prototype, "postsAccess", void 0);
__decorate([
    (0, graphql_1.Field)(() => OrganisationPrivacyAccessSettings, { nullable: true }),
    __metadata("design:type", OrganisationPrivacyAccessSettings)
], OrganisationPrivacySettings.prototype, "eventsAccess", void 0);
__decorate([
    (0, graphql_1.Field)(() => OrganisationPrivacyAccessSettings, { nullable: true }),
    __metadata("design:type", OrganisationPrivacyAccessSettings)
], OrganisationPrivacySettings.prototype, "webinarsAccess", void 0);
__decorate([
    (0, graphql_1.Field)(() => OrganisationPrivacyAccessSettings, { nullable: true }),
    __metadata("design:type", OrganisationPrivacyAccessSettings)
], OrganisationPrivacySettings.prototype, "incentivesAccess", void 0);
__decorate([
    (0, graphql_1.Field)(() => OrganisationPrivacyAccessSettings, { nullable: true }),
    __metadata("design:type", OrganisationPrivacyAccessSettings)
], OrganisationPrivacySettings.prototype, "trainingsAccess", void 0);
exports.OrganisationPrivacySettings = OrganisationPrivacySettings = __decorate([
    (0, graphql_1.InputType)()
], OrganisationPrivacySettings);
//# sourceMappingURL=organisations.args.js.map