"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganisationModelHelper = void 0;
const organisations_args_1 = require("../args/organisations.args");
class OrganisationModelHelper {
    static getPages(organisationType) {
        switch (organisationType) {
            case organisations_args_1.OrganisationType.Destination:
            case organisations_args_1.OrganisationType.Community:
            case organisations_args_1.OrganisationType.TourOperator:
            case organisations_args_1.OrganisationType.TravelAgency:
            case organisations_args_1.OrganisationType.Association:
            case organisations_args_1.OrganisationType.Consortia:
            case organisations_args_1.OrganisationType.TMC:
                return [
                    organisations_args_1.OrganisationPage.Home,
                    organisations_args_1.OrganisationPage.About,
                    organisations_args_1.OrganisationPage.Webinars,
                    organisations_args_1.OrganisationPage.Events,
                    organisations_args_1.OrganisationPage.Resources,
                    organisations_args_1.OrganisationPage.People,
                    organisations_args_1.OrganisationPage.Analytics,
                ];
            case organisations_args_1.OrganisationType.PrivateSector:
            case organisations_args_1.OrganisationType.RepresentationAgency:
                return [
                    organisations_args_1.OrganisationPage.Home,
                    organisations_args_1.OrganisationPage.About,
                    organisations_args_1.OrganisationPage.People,
                    organisations_args_1.OrganisationPage.Resources,
                    organisations_args_1.OrganisationPage.Analytics,
                ];
            default:
                return [];
        }
    }
}
exports.OrganisationModelHelper = OrganisationModelHelper;
//# sourceMappingURL=organisation.model.helper.js.map