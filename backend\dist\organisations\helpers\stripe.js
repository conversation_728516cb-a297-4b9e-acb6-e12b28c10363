"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StripeHelper = void 0;
const common_1 = require("@nestjs/common");
const config_1 = __importDefault(require("../../config/config"));
const stripe_1 = __importDefault(require("stripe"));
const stripeConfig = config_1.default.STRIPE_SECRET_KEY;
const stripe = new stripe_1.default(stripeConfig, {
    apiVersion: null,
});
let StripeHelper = class StripeHelper {
    async createConnectAccount(data) {
        try {
            const { email, organisation_id } = data;
            const connect = await stripe.accounts.create({
                type: 'express',
                email: email,
                capabilities: {
                    card_payments: { requested: true },
                    transfers: { requested: true },
                    bacs_debit_payments: { requested: true },
                },
            });
            const accountLink = await stripe.accountLinks.create({
                account: connect.id,
                refresh_url: `${config_1.default.FRONTEND_URL}/organisation/${organisation_id}/connections/add`,
                return_url: `${config_1.default.FRONTEND_URL}/organisation/${organisation_id}/connections/add`,
                type: 'account_onboarding',
            });
            const configuration = await stripe.billingPortal.configurations.create({
                features: {
                    customer_update: {
                        allowed_updates: ['name', 'email', 'address', 'phone', 'tax_id'],
                        enabled: true,
                    },
                    payment_method_update: { enabled: true },
                    subscription_cancel: {
                        enabled: true,
                        cancellation_reason: {
                            enabled: true,
                            options: [
                                'too_expensive',
                                'unused',
                                'missing_features',
                                'other',
                            ],
                        },
                        mode: 'at_period_end',
                    },
                    invoice_history: { enabled: true },
                },
                business_profile: {
                    privacy_policy_url: 'https://join.myhablo.com/privacy/',
                    terms_of_service_url: 'https://join.myhablo.com/terms/',
                },
            }, {
                stripeAccount: connect.id,
            });
            return { connect, accountLink };
        }
        catch (error) {
            console.log('StripeHelper.createConnectAccount', error);
            throw new Error(`StripeHelper.createConnectAccount: ${error}`);
        }
    }
    async retrieveConnectAccount(data) {
        try {
            const { connect_id, organisation_id } = data;
            const connect = await stripe.accounts.retrieve(connect_id);
            let reponseData;
            if (!connect.charges_enabled) {
                const accountLink = await stripe.accountLinks.create({
                    account: connect.id,
                    refresh_url: `${config_1.default.FRONTEND_URL}/organisation/${organisation_id}/connections/add`,
                    return_url: `${config_1.default.FRONTEND_URL}/organisation/${organisation_id}/connections/add`,
                    type: 'account_onboarding',
                });
                reponseData = accountLink.url;
                return reponseData;
            }
            else {
                const reponseData = true;
                return reponseData;
            }
        }
        catch (error) {
            console.log('StripeHelper.retrieveConnectAccount', error);
            throw new Error(`StripeHelper.retrieveConnectAccount: ${error}`);
        }
    }
    async createCustomerPortalSession(data) {
        try {
            const { customer_id, connect_id } = data;
            const session = await stripe.billingPortal.sessions.create({
                customer: customer_id,
            }, {
                stripeAccount: connect_id,
            });
            return session.url;
        }
        catch (error) {
            console.log('StripeHelper.createCustomerPortalSession', error);
            throw new Error(`StripeHelper.createCustomerPortalSession: ${error}`);
        }
    }
    async createExpressLoginLink(data) {
        try {
            const { connect_id } = data;
            const loginLink = await stripe.accounts.createLoginLink(connect_id);
            return loginLink.url;
        }
        catch (error) {
            console.log('StripeHelper.createExpressLoginLink', error);
            throw new Error(`StripeHelper.createExpressLoginLink: ${error}`);
        }
    }
};
exports.StripeHelper = StripeHelper;
exports.StripeHelper = StripeHelper = __decorate([
    (0, common_1.Injectable)()
], StripeHelper);
//# sourceMappingURL=stripe.js.map