"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Organisation = exports.ParentOrganisationObj = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const graphql_1 = require("@nestjs/graphql");
const sequelize_1 = require("sequelize");
const graphql_type_json_1 = require("graphql-type-json");
const short_uuid_1 = __importDefault(require("short-uuid"));
const membership_model_1 = require("../../memberships/models/membership.model");
const achievements_model_1 = require("../../achievements/models/achievements.model");
const follower_model_1 = require("../../followers/models/follower.model");
const organisations_args_1 = require("../args/organisations.args");
const organisation_model_helper_1 = require("../helpers/organisation.model.helper");
const explore_page_model_1 = require("../../explore-pages/models/explore-page.model");
const partner_organisation_model_1 = require("../../partner-organisations/models/partner-organisation.model");
let ParentOrganisationObj = class ParentOrganisationObj {
};
exports.ParentOrganisationObj = ParentOrganisationObj;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], ParentOrganisationObj.prototype, "id", void 0);
exports.ParentOrganisationObj = ParentOrganisationObj = __decorate([
    (0, graphql_1.ObjectType)()
], ParentOrganisationObj);
let ParentOrganisationDetailsData = class ParentOrganisationDetailsData {
};
__decorate([
    (0, graphql_1.Field)({ nullable: false }),
    __metadata("design:type", String)
], ParentOrganisationDetailsData.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: false }),
    __metadata("design:type", String)
], ParentOrganisationDetailsData.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], ParentOrganisationDetailsData.prototype, "image", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], ParentOrganisationDetailsData.prototype, "vanityId", void 0);
ParentOrganisationDetailsData = __decorate([
    (0, graphql_1.ObjectType)()
], ParentOrganisationDetailsData);
let Organisation = class Organisation extends sequelize_typescript_1.Model {
    get pages() {
        return organisation_model_helper_1.OrganisationModelHelper.getPages(this.type);
    }
};
exports.Organisation = Organisation;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], Organisation.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({ allowNull: false }),
    __metadata("design:type", String)
], Organisation.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Organisation.prototype, "image", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Organisation.prototype, "backgroundImage", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_type_json_1.GraphQLJSON, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.JSON,
    }),
    __metadata("design:type", Object)
], Organisation.prototype, "location", void 0);
__decorate([
    (0, graphql_1.Field)(() => organisations_args_1.OrganisationType, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], Organisation.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)(() => organisations_args_1.OrganisationStatus, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], Organisation.prototype, "status", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({
        defaultValue: organisations_args_1.Privacy.Public,
    }),
    __metadata("design:type", String)
], Organisation.prototype, "privacy", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({
        defaultValue: null,
    }),
    __metadata("design:type", String)
], Organisation.prototype, "followingPrivacy", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({
        defaultValue: null,
    }),
    __metadata("design:type", String)
], Organisation.prototype, "peoplePrivacy", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({
        defaultValue: null,
    }),
    __metadata("design:type", String)
], Organisation.prototype, "additionalPrivacy", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Organisation.prototype, "website", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Organisation.prototype, "resourceUrl", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.TEXT,
    }),
    __metadata("design:type", String)
], Organisation.prototype, "resources", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.TEXT,
    }),
    __metadata("design:type", String)
], Organisation.prototype, "description", void 0);
__decorate([
    (0, graphql_1.Field)(() => organisations_args_1.OrganisationSize, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], Organisation.prototype, "size", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
        unique: true,
    }),
    __metadata("design:type", String)
], Organisation.prototype, "vanityId", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], Organisation.prototype, "seenAnyPostBy", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], Organisation.prototype, "stripeConnectAccount", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true, defaultValue: false }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Organisation.prototype, "isConnectOnboarded", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true, defaultValue: false }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Organisation.prototype, "isPaid", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true, defaultValue: false }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Organisation.prototype, "hasClubHabloSubscription", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true }),
    __metadata("design:type", Boolean)
], Organisation.prototype, "isAchieved", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: false, defaultValue: 0 }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Number)
], Organisation.prototype, "currentMonthPostCount", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_type_json_1.GraphQLJSON, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.JSON,
        defaultValue: [],
    }),
    __metadata("design:type", Object)
], Organisation.prototype, "preApprovedDomains", void 0);
__decorate([
    (0, graphql_1.Field)(() => [ParentOrganisationObj], { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.JSON),
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], Organisation.prototype, "parentOrganisations", void 0);
__decorate([
    (0, graphql_1.Field)(() => [String], { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], Organisation.prototype, "connectedOrganisations", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Organisation.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Organisation.prototype, "updatedAt", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Organisation.prototype, "isPublic", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => membership_model_1.Membership, 'organisationId'),
    __metadata("design:type", Array)
], Organisation.prototype, "memberships", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => follower_model_1.Follower, 'organisationId'),
    __metadata("design:type", Array)
], Organisation.prototype, "followers", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => achievements_model_1.Achievement),
    __metadata("design:type", Array)
], Organisation.prototype, "achievements", void 0);
__decorate([
    (0, graphql_1.Field)(() => [organisations_args_1.OrganisationPage]),
    (0, sequelize_typescript_1.Column)(sequelize_1.DataTypes.VIRTUAL),
    __metadata("design:type", Array),
    __metadata("design:paramtypes", [])
], Organisation.prototype, "pages", null);
__decorate([
    (0, graphql_1.Field)(type => [ParentOrganisationDetailsData], { nullable: true }),
    __metadata("design:type", Array)
], Organisation.prototype, "parentOrganisationDetails", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsToMany)(() => explore_page_model_1.DestinationPage, () => explore_page_model_1.DestinationPageOrgs),
    __metadata("design:type", Array)
], Organisation.prototype, "destinationPages", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    __metadata("design:type", String)
], Organisation.prototype, "activeTier", void 0);
__decorate([
    (0, graphql_1.Field)(() => Number, { nullable: true }),
    __metadata("design:type", Number)
], Organisation.prototype, "level", void 0);
__decorate([
    (0, graphql_1.Field)(() => Number, { nullable: true }),
    __metadata("design:type", Number)
], Organisation.prototype, "achievedCounts", void 0);
__decorate([
    (0, graphql_1.Field)(() => Number, { nullable: true }),
    __metadata("design:type", Number)
], Organisation.prototype, "totalCounts", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { defaultValue: false }),
    (0, sequelize_typescript_1.Column)({
        allowNull: false,
        defaultValue: false,
    }),
    __metadata("design:type", Boolean)
], Organisation.prototype, "isDMOSubscription", void 0);
__decorate([
    (0, graphql_1.Field)(() => Number, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 10,
    }),
    __metadata("design:type", Number)
], Organisation.prototype, "DMOMaxPartners", void 0);
__decorate([
    (0, graphql_1.Field)(() => Number, { nullable: true }),
    __metadata("design:type", Number)
], Organisation.prototype, "referralCount", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => partner_organisation_model_1.PartnerOrganisation, 'parentOrgId'),
    __metadata("design:type", Array)
], Organisation.prototype, "childPartners", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => partner_organisation_model_1.PartnerOrganisation, 'childOrgId'),
    __metadata("design:type", Array)
], Organisation.prototype, "parentPartner", void 0);
exports.Organisation = Organisation = __decorate([
    sequelize_typescript_1.Table,
    (0, graphql_1.ObjectType)()
], Organisation);
//# sourceMappingURL=organisation.model.js.map