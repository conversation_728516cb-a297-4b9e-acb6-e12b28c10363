"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganisationsModule = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const organisation_model_1 = require("./models/organisation.model");
const organisations_service_1 = require("./organisations.service");
const organisations_resolver_1 = require("./organisations.resolver");
const organisations_repository_1 = require("./organisations.repository");
const common_module_1 = require("../common/common.module");
const memberships_module_1 = require("../memberships/memberships.module");
const followers_module_1 = require("../followers/followers.module");
const partnerships_module_1 = require("../partnerships/partnerships.module");
const partnership_requests_module_1 = require("../partnership-requests/partnership-requests.module");
const notifications_module_1 = require("../notifications/notifications.module");
const incentives_module_1 = require("../incentives/incentives.module");
const incentive_participants_module_1 = require("../incentive-participants/incentive-participants.module");
const profiles_module_1 = require("../profiles/profiles.module");
const webinars_module_1 = require("../webinars/webinars.module");
const webinar_participants_module_1 = require("../webinar-participants/webinar-participants.module");
const events_module_1 = require("../events/events.module");
const event_invitations_module_1 = require("../event-invitations/event-invitations.module");
const experiences_module_1 = require("../experiences/experiences.module");
const stream_organisations_module_1 = require("../feeds-organisations/stream-organisations.module");
const explore_pages_module_1 = require("../explore-pages/explore-pages.module");
const stripe_1 = require("./helpers/stripe");
const subscriptions_module_1 = require("../subscriptions/subscriptions.module");
const payment_transactions_module_1 = require("../payment-transactions/payment-transactions.module");
const stream_followers_module_1 = require("../feeds-followers/stream-followers.module");
const organisation_loyalty_points_module_1 = require("../organisation-loyalty-points/organisation-loyalty-points.module");
const partner_organisations_module_1 = require("../partner-organisations/partner-organisations.module");
let OrganisationsModule = class OrganisationsModule {
};
exports.OrganisationsModule = OrganisationsModule;
exports.OrganisationsModule = OrganisationsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            sequelize_1.SequelizeModule.forFeature([organisation_model_1.Organisation]),
            (0, common_1.forwardRef)(() => common_module_1.CommonModule),
            (0, common_1.forwardRef)(() => memberships_module_1.MembershipsModule),
            (0, common_1.forwardRef)(() => followers_module_1.FollowersModule),
            (0, common_1.forwardRef)(() => partnerships_module_1.PartnershipsModule),
            (0, common_1.forwardRef)(() => partnership_requests_module_1.PartnershipRequestsModule),
            (0, common_1.forwardRef)(() => notifications_module_1.NotificationsModule),
            (0, common_1.forwardRef)(() => incentives_module_1.IncentivesModule),
            (0, common_1.forwardRef)(() => incentive_participants_module_1.IncentiveParticipantsModule),
            (0, common_1.forwardRef)(() => profiles_module_1.ProfilesModule),
            (0, common_1.forwardRef)(() => webinars_module_1.WebinarsModule),
            (0, common_1.forwardRef)(() => webinar_participants_module_1.WebinarParticipantsModule),
            (0, common_1.forwardRef)(() => events_module_1.EventsModule),
            (0, common_1.forwardRef)(() => event_invitations_module_1.EventsInvitationsModule),
            (0, common_1.forwardRef)(() => experiences_module_1.ExperiencesModule),
            (0, common_1.forwardRef)(() => stream_organisations_module_1.StreamOrganisationsModule),
            (0, common_1.forwardRef)(() => explore_pages_module_1.DestinationPagesModule),
            (0, common_1.forwardRef)(() => subscriptions_module_1.SubscriptionsModule),
            (0, common_1.forwardRef)(() => payment_transactions_module_1.PaymentTransactionsModule),
            (0, common_1.forwardRef)(() => stream_followers_module_1.StreamFollowersModule),
            (0, common_1.forwardRef)(() => organisation_loyalty_points_module_1.OrganisationLoyaltyPointsModule),
            (0, common_1.forwardRef)(() => partner_organisations_module_1.PartnerOrganisationsModule),
        ],
        providers: [
            organisations_resolver_1.OrganisationsResolver,
            organisations_service_1.OrganisationsService,
            organisations_repository_1.OrganisationsRepository,
            stripe_1.StripeHelper,
        ],
        exports: [organisations_service_1.OrganisationsService],
    })
], OrganisationsModule);
//# sourceMappingURL=organisations.module.js.map