"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganisationsRepository = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const nest_winston_1 = require("nest-winston");
const sequelize_2 = require("sequelize");
const winston_1 = require("winston");
const pagination_1 = require("../common/helpers/pagination");
const follower_model_1 = require("../followers/models/follower.model");
const membership_model_1 = require("../memberships/models/membership.model");
const organisations_args_1 = require("./args/organisations.args");
const organisation_model_1 = require("./models/organisation.model");
const achievements_model_1 = require("../achievements/models/achievements.model");
let OrganisationsRepository = class OrganisationsRepository {
    async findOrganisations(profileId, filter, pagination) {
        const extraQueryParams = {};
        const includeIds = [];
        if ((filter === null || filter === void 0 ? void 0 : filter.type) && filter.type.length > 0) {
            extraQueryParams.type = {
                [sequelize_2.Op.in]: filter.type,
            };
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.vanityId) && filter.vanityId.length > 0) {
            extraQueryParams.vanityId = {
                [sequelize_2.Op.in]: filter === null || filter === void 0 ? void 0 : filter.vanityId,
            };
        }
        if (!(filter === null || filter === void 0 ? void 0 : filter.id) && (filter === null || filter === void 0 ? void 0 : filter.searchText) && filter.isFuzzySearch) {
            const query = `
        SELECT set_limit(0.2);

        SELECT id, name FROM "Organisations"
        WHERE name % :searchText
        ORDER BY SIMILARITY(name, :searchText) DESC
        ${pagination && pagination.first ? `LIMIT :first` : ''}
      `;
            const options = {
                replacements: {
                    searchText: filter.searchText,
                    first: pagination === null || pagination === void 0 ? void 0 : pagination.first,
                },
                type: 'SELECT',
            };
            const fuzzyOrganisations = await organisation_model_1.Organisation.sequelize.query(query, options);
            fuzzyOrganisations.shift();
            if (fuzzyOrganisations.length) {
                const fuzzyOrganisationIds = fuzzyOrganisations.map((org) => org.id);
                includeIds.push(...fuzzyOrganisationIds);
            }
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.searchText) && !includeIds.length) {
            extraQueryParams.name = {
                [sequelize_2.Op.iLike]: `%${filter.searchText}%`,
            };
        }
        if (filter === null || filter === void 0 ? void 0 : filter.id) {
            includeIds.push(filter.id);
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.includeRemoved) !== true) {
            extraQueryParams['status'] = {
                [sequelize_2.Op.in]: [organisations_args_1.OrganisationStatus.Active],
            };
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.hasClubHabloSubscription) !== undefined) {
            extraQueryParams.hasClubHabloSubscription =
                filter.hasClubHabloSubscription;
        }
        const includeParams = [
            {
                model: follower_model_1.Follower,
                as: 'followers',
                where: {
                    profileId,
                },
                required: false,
            },
            {
                model: membership_model_1.Membership,
                as: 'memberships',
                where: {
                    profileId,
                },
                required: false,
            },
        ];
        const result = await new pagination_1.PaginationHelper().getPaginatedResults({
            model: organisation_model_1.Organisation,
            pagination,
            extraQueryParams,
            excludeIds: [],
            includeIds,
            includeParams,
            searchText: filter === null || filter === void 0 ? void 0 : filter.searchText,
        });
        if (includeIds.length && filter.isFuzzySearch) {
            result.records = this.rearrangeOrganisationRecords(result.records, includeIds);
        }
        return result;
    }
    rearrangeOrganisationRecords(records, includeIds) {
        const rearrangedRecords = [];
        includeIds.forEach(id => {
            const record = records.find(r => r.id === id);
            if (record) {
                rearrangedRecords.push(record);
            }
        });
        return rearrangedRecords;
    }
    async findHabloSubscriptionOrganisations(profileId) {
        return await this.organisationModel.findAll({
            where: {
                hasClubHabloSubscription: true,
            },
            include: [
                {
                    model: achievements_model_1.Achievement,
                    where: {
                        profileId: profileId,
                    },
                    required: false,
                },
            ],
        });
    }
};
exports.OrganisationsRepository = OrganisationsRepository;
__decorate([
    (0, sequelize_1.InjectModel)(organisation_model_1.Organisation),
    __metadata("design:type", Object)
], OrganisationsRepository.prototype, "organisationModel", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], OrganisationsRepository.prototype, "logger", void 0);
exports.OrganisationsRepository = OrganisationsRepository = __decorate([
    (0, common_1.Injectable)()
], OrganisationsRepository);
//# sourceMappingURL=organisations.repository.js.map