"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganisationsResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const sequelize_typescript_1 = require("sequelize-typescript");
const organisation_model_1 = require("./models/organisation.model");
const organisations_service_1 = require("./organisations.service");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const create_organisation_input_1 = require("./dto/create-organisation.input");
const membership_model_1 = require("../memberships/models/membership.model");
const memberships_service_1 = require("../memberships/memberships.service");
const paginated_result_1 = require("../common/args/paginated-result");
const organisations_args_1 = require("./args/organisations.args");
const followers_service_1 = require("../followers/followers.service");
const follower_model_1 = require("../followers/models/follower.model");
const update_organisation_input_1 = require("./dto/update-organisation.input");
const memberships_args_1 = require("../memberships/args/memberships.args");
const followers_args_1 = require("../followers/args/followers.args");
const partnerships_model_1 = require("../partnerships/models/partnerships.model");
const partnerships_service_1 = require("../partnerships/partnerships.service");
const partnership_request_model_1 = require("../partnership-requests/models/partnership-request.model");
const partnership_requests_service_1 = require("../partnership-requests/partnership-requests.service");
const incentives_args_1 = require("../incentives/args/incentives.args");
const incentives_service_1 = require("../incentives/incentives.service");
const webinars_service_1 = require("../webinars/webinars.service");
const events_service_1 = require("../events/events.service");
const events_args_1 = require("../events/args/events.args");
const subscription_model_1 = require("../subscriptions/models/subscription.model");
const payment_transaction_model_1 = require("../payment-transactions/models/payment-transaction.model");
const subscriptions_service_1 = require("../subscriptions/subscriptions.service");
const payment_transactions_service_1 = require("../payment-transactions/payment-transactions.service");
const add_pre_approved_domains_input_1 = require("./dto/add-pre-approved-domains.input");
const add_parent_organisation_1 = require("./dto/add-parent-organisation");
const organisation_loyalty_points_service_1 = require("../organisation-loyalty-points/organisation-loyalty-points.service");
let OrganisationsResolver = class OrganisationsResolver {
    async organisation(user, id, vanityId) {
        var _a;
        this.logger.verbose('OrganisationsResolver.organisation (query)', {
            user: user.toLogObject(),
            id,
            vanityId,
        });
        if (!id && !vanityId) {
            throw new Error(`Organisation id or vanityId should be set`);
        }
        if (!!vanityId && vanityId.startsWith('-removed!-')) {
            throw new Error(`Organisation removed`);
        }
        const organisation = await this.organisationsService.findOne(id
            ? { id, status: organisations_args_1.OrganisationStatus.Active }
            : sequelize_typescript_1.Sequelize.where(sequelize_typescript_1.Sequelize.fn('lower', sequelize_typescript_1.Sequelize.col('vanityId')), sequelize_typescript_1.Sequelize.fn('lower', vanityId)));
        const parentOrgData = [];
        if ((_a = organisation === null || organisation === void 0 ? void 0 : organisation.parentOrganisations) === null || _a === void 0 ? void 0 : _a.length) {
            for (const org of organisation.parentOrganisations) {
                const organisation = await this.organisationsService.findById(org.id);
                parentOrgData.push({
                    id: organisation.id,
                    name: organisation.name,
                    image: organisation.image,
                    vanityId: organisation.vanityId,
                });
            }
            organisation.parentOrganisationDetails = parentOrgData;
        }
        if (!organisation) {
            throw new Error(`Organisation not found`);
        }
        return organisation;
    }
    organisations(user, organisationArgs) {
        var _a, _b, _c, _d, _e;
        this.logger.verbose('OrganisationsResolver.organisations (query)', {
            user: user.toLogObject(),
            organisationArgs,
        });
        return this.organisationsService.findOrganisations(user.profileId, {
            id: (_a = organisationArgs === null || organisationArgs === void 0 ? void 0 : organisationArgs.filter) === null || _a === void 0 ? void 0 : _a.id,
            type: (_b = organisationArgs === null || organisationArgs === void 0 ? void 0 : organisationArgs.filter) === null || _b === void 0 ? void 0 : _b.type,
            searchText: (_c = organisationArgs === null || organisationArgs === void 0 ? void 0 : organisationArgs.filter) === null || _c === void 0 ? void 0 : _c.searchText,
            vanityId: (_d = organisationArgs === null || organisationArgs === void 0 ? void 0 : organisationArgs.filter) === null || _d === void 0 ? void 0 : _d.vanityId,
            isFuzzySearch: (_e = organisationArgs === null || organisationArgs === void 0 ? void 0 : organisationArgs.filter) === null || _e === void 0 ? void 0 : _e.isFuzzySearch,
        }, {
            first: organisationArgs === null || organisationArgs === void 0 ? void 0 : organisationArgs.first,
            after: organisationArgs === null || organisationArgs === void 0 ? void 0 : organisationArgs.after,
            sortBy: organisationArgs === null || organisationArgs === void 0 ? void 0 : organisationArgs.sortBy,
            sortOrder: organisationArgs === null || organisationArgs === void 0 ? void 0 : organisationArgs.sortOrder,
        });
    }
    createOrganisation(user, organisationData) {
        this.logger.verbose('OrganisationsResolver.createOrganisation (mutation)', {
            user: user.toLogObject(),
            organisationData,
        });
        return this.organisationsService.createOrganisation(user, organisationData);
    }
    updateOrganisation(user, organisationId, organisationData) {
        this.logger.verbose('OrganisationsResolver.updateOrganisation (mutation)', {
            user: user.toLogObject(),
            organisationData,
        });
        return this.organisationsService.updateOrganisation(organisationId, organisationData, {
            profileId: user.profileId,
        });
    }
    async adminPanelRemoveOrganisation(user, organisationId) {
        this.logger.verbose('OrganisationsResolver.removeOrganisation (mutation)', {
            user: user.toLogObject(),
            organisationId,
        });
        await this.organisationsService.adminPanelRemoveOrganisation(organisationId, {
            profileId: user.profileId,
        });
        return true;
    }
    async changeOrganisationOwnership(user, organisationId, newOwnerProfileId) {
        this.logger.verbose('OrganisationsResolver.changeOrganisationOwnership (mutation)', {
            user: user.toLogObject(),
            organisationId,
        });
        await this.organisationsService.changeOrganisationOwnership(organisationId, newOwnerProfileId, {
            currentUser: user,
        });
        return true;
    }
    async acceptOrganisationOwnership(user, organisationId) {
        this.logger.verbose('OrganisationsResolver.acceptOrganisationOwnership (mutation)', {
            organisationId,
        });
        await this.organisationsService.acceptOrganisationOwnership(organisationId, {
            currentUser: user,
        });
        return true;
    }
    async rejectOrganisationOwnership(user, organisationId) {
        this.logger.verbose('OrganisationsResolver.rejectOrganisationOwnership (mutation)', {
            organisationId,
        });
        await this.organisationsService.rejectOrganisationOwnership(organisationId, {
            currentUser: user,
        });
        return true;
    }
    memberships(user, organisation, membershipArgs) {
        var _a, _b, _c, _d, _e;
        this.logger.verbose('OrganisationsResolver.memberships (field resolver)', {
            organisationId: organisation.id,
            membershipArgs,
            organisationStatus: organisation.status,
        });
        if (organisation.status !== organisations_args_1.OrganisationStatus.Active) {
            return;
        }
        return this.membershipsService.findMemberships(user.profileId, {
            profileId: (_a = membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.filter) === null || _a === void 0 ? void 0 : _a.profileId,
            organisationId: organisation.id,
            status: (_b = membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.filter) === null || _b === void 0 ? void 0 : _b.status,
            permission: (_c = membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.filter) === null || _c === void 0 ? void 0 : _c.permission,
            isPrimary: (_d = membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.filter) === null || _d === void 0 ? void 0 : _d.isPrimary,
            includeActiveProfile: (_e = membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.filter) === null || _e === void 0 ? void 0 : _e.includeActiveProfile,
        }, {
            first: membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.first,
            after: membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.after,
            sortBy: membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.sortBy,
            sortOrder: membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.sortOrder,
        });
    }
    incentives(user, organisation, incentiveArgs) {
        var _a, _b, _c, _d, _e;
        this.logger.verbose('OrganisationsResolver.incentives (field resolver)', {
            organisationId: organisation.id,
            incentiveArgs,
        });
        return this.incentivesService.findIncentives(user.profileId, {
            incentiveParticipantStatus: (_a = incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.filter) === null || _a === void 0 ? void 0 : _a.incentiveParticipantStatus,
            organisationId: organisation.id,
            type: (_b = incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.filter) === null || _b === void 0 ? void 0 : _b.type,
            isPublic: (_c = incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.filter) === null || _c === void 0 ? void 0 : _c.isPublic,
            searchText: (_d = incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.filter) === null || _d === void 0 ? void 0 : _d.searchText,
            isEnded: (_e = incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.filter) === null || _e === void 0 ? void 0 : _e.isEnded,
        }, {
            first: incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.first,
            after: incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.after,
            sortBy: incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.sortBy,
            sortOrder: incentiveArgs === null || incentiveArgs === void 0 ? void 0 : incentiveArgs.sortOrder,
        });
    }
    followers(user, organisation, followerArgs) {
        var _a, _b, _c;
        this.logger.verbose('OrganisationsResolver.followers (field resolver)', {
            organisationId: organisation.id,
            followerArgs,
            organisationStatus: organisation.status,
        });
        if (organisation.status !== organisations_args_1.OrganisationStatus.Active) {
            return;
        }
        return this.followersService.findFollowers(user.profileId, {
            profileId: (_a = followerArgs === null || followerArgs === void 0 ? void 0 : followerArgs.filter) === null || _a === void 0 ? void 0 : _a.profileId,
            organisationId: organisation.id,
            status: (_b = followerArgs === null || followerArgs === void 0 ? void 0 : followerArgs.filter) === null || _b === void 0 ? void 0 : _b.status,
            includeActiveProfile: (_c = followerArgs === null || followerArgs === void 0 ? void 0 : followerArgs.filter) === null || _c === void 0 ? void 0 : _c.includeActiveProfile,
        }, {
            first: followerArgs === null || followerArgs === void 0 ? void 0 : followerArgs.first,
            after: followerArgs === null || followerArgs === void 0 ? void 0 : followerArgs.after,
            sortBy: followerArgs === null || followerArgs === void 0 ? void 0 : followerArgs.sortBy,
            sortOrder: followerArgs === null || followerArgs === void 0 ? void 0 : followerArgs.sortOrder,
        });
    }
    async partnerships(organisation, isActive) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v;
        this.logger.verbose('OrganisationsResolver.partnerships (field resolver)', {
            organisation,
            isActive,
        });
        const queryOptions = {
            partnershipOrganisationId: organisation.id,
        };
        const includeOptions = [
            {
                model: partnership_request_model_1.PartnershipRequest,
                as: 'partnershipRequest',
                where: {
                    status: isActive === undefined
                        ? [
                            partnership_request_model_1.PartnershipRequestStatus.Approved,
                            partnership_request_model_1.PartnershipRequestStatus.Declined,
                            partnership_request_model_1.PartnershipRequestStatus.Pending,
                            partnership_request_model_1.PartnershipRequestStatus.Disconnected,
                        ]
                        : isActive
                            ? [partnership_request_model_1.PartnershipRequestStatus.Approved]
                            : [partnership_request_model_1.PartnershipRequestStatus.Disconnected],
                },
                include: [
                    {
                        model: subscription_model_1.Subscription,
                        as: 'subscription',
                        where: {
                            status: isActive === undefined
                                ? [
                                    subscription_model_1.SubscriptionStatus.Active,
                                    subscription_model_1.SubscriptionStatus.Cancelled,
                                    subscription_model_1.SubscriptionStatus.Expired,
                                ]
                                : isActive
                                    ? [subscription_model_1.SubscriptionStatus.Active, subscription_model_1.SubscriptionStatus.Cancelled]
                                    : [subscription_model_1.SubscriptionStatus.Cancelled, subscription_model_1.SubscriptionStatus.Expired],
                        },
                        required: false,
                        include: [
                            {
                                model: payment_transaction_model_1.PaymentTransaction,
                                as: 'lastTransaction',
                            },
                        ],
                    },
                ],
            },
        ];
        const findAllOptions = {
            includeParams: includeOptions,
            useCache: true,
        };
        const partnerships = await this.partnershipsService.findAll(queryOptions, findAllOptions);
        for (const partnership of partnerships) {
            partnership.partnershipRequestData = partnership.partnershipRequest;
            if (partnership.partnershipRequest.subscription) {
                (_a = partnership === null || partnership === void 0 ? void 0 : partnership.partnershipRequestData) === null || _a === void 0 ? void 0 : _a.subscriptionData = {
                    subscriptionId: (_c = (_b = partnership === null || partnership === void 0 ? void 0 : partnership.partnershipRequest) === null || _b === void 0 ? void 0 : _b.subscription) === null || _c === void 0 ? void 0 : _c.id,
                    currency: (_e = (_d = partnership === null || partnership === void 0 ? void 0 : partnership.partnershipRequest) === null || _d === void 0 ? void 0 : _d.subscription) === null || _e === void 0 ? void 0 : _e.currency,
                    price: (_g = (_f = partnership === null || partnership === void 0 ? void 0 : partnership.partnershipRequest) === null || _f === void 0 ? void 0 : _f.subscription) === null || _g === void 0 ? void 0 : _g.price,
                    duration: (_j = (_h = partnership === null || partnership === void 0 ? void 0 : partnership.partnershipRequest) === null || _h === void 0 ? void 0 : _h.subscription) === null || _j === void 0 ? void 0 : _j.duration,
                    status: (_l = (_k = partnership === null || partnership === void 0 ? void 0 : partnership.partnershipRequest) === null || _k === void 0 ? void 0 : _k.subscription) === null || _l === void 0 ? void 0 : _l.status,
                    lastTransaction: ((_o = (_m = partnership === null || partnership === void 0 ? void 0 : partnership.partnershipRequest) === null || _m === void 0 ? void 0 : _m.subscription) === null || _o === void 0 ? void 0 : _o.lastTransaction)
                        ? (_r = (_q = (_p = partnership === null || partnership === void 0 ? void 0 : partnership.partnershipRequest) === null || _p === void 0 ? void 0 : _p.subscription) === null || _q === void 0 ? void 0 : _q.lastTransaction) === null || _r === void 0 ? void 0 : _r.startDate
                        : '',
                    endDate: (_t = (_s = partnership === null || partnership === void 0 ? void 0 : partnership.partnershipRequest) === null || _s === void 0 ? void 0 : _s.subscription) === null || _t === void 0 ? void 0 : _t.endDate,
                    canceledDate: (_v = (_u = partnership === null || partnership === void 0 ? void 0 : partnership.partnershipRequest) === null || _u === void 0 ? void 0 : _u.subscription) === null || _v === void 0 ? void 0 : _v.canceledDate,
                };
            }
        }
        return partnerships;
    }
    async receivedPartnershipRequests(organisation) {
        this.logger.verbose('OrganisationsResolver.receivedPartnershipRequests (field resolver)', {
            organisation,
        });
        const partnershipRequests = await this.partnershipRequestsService.findAll({
            receiverOrganisationId: organisation.id,
            status: partnership_request_model_1.PartnershipRequestStatus.Pending,
        }, { useCache: true });
        for (const partnershipRequest of partnershipRequests) {
            const subscription = await this.subscriptionsService.findById(partnershipRequest.subscriptionId);
            if (subscription) {
                const paymentTransaction = await this.paymentTransactionsService.findById(subscription.lastTransactionId);
                partnershipRequest.subscriptionData = {
                    subscriptionId: subscription === null || subscription === void 0 ? void 0 : subscription.id,
                    currency: subscription === null || subscription === void 0 ? void 0 : subscription.currency,
                    price: subscription === null || subscription === void 0 ? void 0 : subscription.price,
                    duration: subscription === null || subscription === void 0 ? void 0 : subscription.duration,
                    status: subscription === null || subscription === void 0 ? void 0 : subscription.status,
                    stripeCheckoutId: subscription === null || subscription === void 0 ? void 0 : subscription.stripeCheckoutId,
                    lastTransaction: paymentTransaction
                        ? paymentTransaction.startDate
                        : '',
                    endDate: subscription === null || subscription === void 0 ? void 0 : subscription.endDate,
                    canceledDate: subscription === null || subscription === void 0 ? void 0 : subscription.canceledDate,
                };
            }
        }
        return partnershipRequests;
    }
    async sentPartnershipRequests(organisation) {
        this.logger.verbose('OrganisationsResolver.sentPartnershipRequests (field resolver)', {
            organisation,
        });
        const partnershipRequests = await this.partnershipRequestsService.findAll({
            senderOrganisationId: organisation.id,
            status: partnership_request_model_1.PartnershipRequestStatus.Pending,
        }, { useCache: true });
        for (const partnershipRequest of partnershipRequests) {
            const subscription = await this.subscriptionsService.findById(partnershipRequest.subscriptionId);
            if (subscription) {
                const paymentTransaction = await this.paymentTransactionsService.findById(subscription.lastTransactionId);
                partnershipRequest.subscriptionData = {
                    subscriptionId: subscription === null || subscription === void 0 ? void 0 : subscription.id,
                    currency: subscription === null || subscription === void 0 ? void 0 : subscription.currency,
                    price: subscription === null || subscription === void 0 ? void 0 : subscription.price,
                    duration: subscription === null || subscription === void 0 ? void 0 : subscription.duration,
                    status: (subscription === null || subscription === void 0 ? void 0 : subscription.status) === subscription_model_1.SubscriptionStatus.Pending
                        ? 'Awaiting Response'
                        : subscription === null || subscription === void 0 ? void 0 : subscription.status,
                    stripeCheckoutId: subscription === null || subscription === void 0 ? void 0 : subscription.stripeCheckoutId,
                    lastTransaction: paymentTransaction
                        ? paymentTransaction.startDate
                        : '',
                    endDate: subscription === null || subscription === void 0 ? void 0 : subscription.endDate,
                    canceledDate: subscription === null || subscription === void 0 ? void 0 : subscription.canceledDate,
                };
            }
        }
        return partnershipRequests;
    }
    async followerStatus(user, organisation) {
        const follower = await this.followersService.findOne({
            organisationId: organisation.id,
            profileId: user.profileId,
        }, { useCache: true });
        return follower === null || follower === void 0 ? void 0 : follower.status;
    }
    getMembership(organisationId, profileId) {
        return this.membershipsService.findOne({
            organisationId,
            profileId,
            status: membership_model_1.MembershipStatus.Active,
        }, { useCache: true });
    }
    async isMember(user, organisation) {
        return !!(await this.getMembership(organisation.id, user.profileId));
    }
    async permissions(user, organisation) {
        const membership = await this.getMembership(organisation.id, user.profileId);
        return membership === null || membership === void 0 ? void 0 : membership.permissions;
    }
    async isOwner(user, organisation) {
        var _a;
        const membership = await this.getMembership(organisation.id, user.profileId);
        return (_a = membership === null || membership === void 0 ? void 0 : membership.permissions) === null || _a === void 0 ? void 0 : _a.includes(membership_model_1.MembershipPermission.Owner);
    }
    async activeMembership(user, organisation) {
        return this.getMembership(organisation.id, user.profileId);
    }
    async isAdmin(user, organisation) {
        var _a, _b;
        const membership = await this.getMembership(organisation.id, user.profileId);
        return (((_a = membership === null || membership === void 0 ? void 0 : membership.permissions) === null || _a === void 0 ? void 0 : _a.includes(membership_model_1.MembershipPermission.Admin)) ||
            ((_b = membership === null || membership === void 0 ? void 0 : membership.permissions) === null || _b === void 0 ? void 0 : _b.includes(membership_model_1.MembershipPermission.HiddenAdmin)));
    }
    async isManager(user, organisation) {
        var _a;
        const membership = await this.getMembership(organisation.id, user.profileId);
        return (_a = membership === null || membership === void 0 ? void 0 : membership.permissions) === null || _a === void 0 ? void 0 : _a.includes(membership_model_1.MembershipPermission.Manager);
    }
    async isEditor(user, organisation) {
        var _a;
        const membership = await this.getMembership(organisation.id, user.profileId);
        return (_a = membership === null || membership === void 0 ? void 0 : membership.permissions) === null || _a === void 0 ? void 0 : _a.includes(membership_model_1.MembershipPermission.Editor);
    }
    async followersActiveCount(organisation) {
        return this.followersService.count({
            organisationId: organisation.id,
            status: follower_model_1.FollowerStatus.Active,
        });
    }
    async followersPendingCount(organisation) {
        return this.followersService.count({
            organisationId: organisation.id,
            status: follower_model_1.FollowerStatus.Pending,
        });
    }
    async followersRejectedCount(organisation) {
        return this.followersService.count({
            organisationId: organisation.id,
            status: follower_model_1.FollowerStatus.Rejected,
        });
    }
    async webinarsCount(organisation) {
        return this.webinarsService.count({
            organisationId: organisation.id,
        });
    }
    async eventsCount(organisation) {
        return this.eventsService.count({
            organisationId: organisation.id,
            type: events_args_1.EventType.Event,
        });
    }
    async referralCount(organisation) {
        return this.organisationsService.getReferralCount(organisation.id);
    }
    async findConnectAccount(user, organisationId) {
        this.logger.verbose('OrganisationsResolver.findConnectAccount (mutation)', {
            organisationId,
        });
        const connectAccount = await this.organisationsService.findConnectAccount(organisationId, {
            currentUser: user,
        });
        if (connectAccount.stripeConnectAccount !== null ||
            connectAccount.stripeConnectAccount !== undefined ||
            connectAccount.stripeConnectAccount !== '') {
            return true;
        }
        else {
            return false;
        }
    }
    async createConnectAccount(user, organisationId, email) {
        this.logger.verbose('OrganisationsResolver.createConnectAccount (mutation)', {
            organisationId,
            email,
        });
        return await this.organisationsService.createConnectAccount(organisationId, email, {
            currentUser: user,
        });
    }
    async retrieveConnectAccount(user, organisationId) {
        this.logger.verbose('OrganisationsResolver.retrieveConnectAccount (mutation)', {
            organisationId,
        });
        return await this.organisationsService.retrieveConnectAccount(organisationId, {
            currentUser: user,
        });
    }
    async getCustomerPortal(user, partnershipId, organisationId) {
        this.logger.verbose('OrganisationsResolver.getCustomerPortal (mutation)', {
            partnershipId,
            organisationId,
        });
        return await this.organisationsService.getCustomerPortal(partnershipId, organisationId, {
            currentUser: user,
        });
    }
    async getStripeExpressLink(user, organisationId) {
        this.logger.verbose('OrganisationsResolver.getStripeExpressLink (mutation)', {
            organisationId,
        });
        return await this.organisationsService.getStripeExpressLink(organisationId, {
            currentUser: user,
        });
    }
    async addPreApprovedDomains(user, data) {
        this.logger.verbose('OrganisationsResolver.addPreApprovedDomains (mutation)', {
            data,
        });
        await this.organisationsService.addPreApprovedDomains(data, {
            currentUser: user,
        });
        return true;
    }
    async removePreApprovedDomains(user, data) {
        this.logger.verbose('OrganisationsResolver.removePreApprovedDomains (mutation)', {
            data,
        });
        await this.organisationsService.removePreApprovedDomains(data, {
            currentUser: user,
        });
        return true;
    }
    async addParentOrganisation(user, data) {
        this.logger.verbose('OrganisationsResolver.addParentOrganisation (mutation)', {
            data,
        });
        return await this.organisationsService.addParentOrganisation(data, {
            currentUser: user,
        });
    }
    async removeParentOrganisation(user, data) {
        this.logger.verbose('OrganisationsResolver.removeParentOrganisation (mutation)', {
            data,
        });
        return await this.organisationsService.removeParentOrganisation(data, {
            currentUser: user,
        });
    }
    async activeTier(organisation) {
        var _a;
        if (organisation.hasClubHabloSubscription !== true) {
            return null;
        }
        const organisationLoyaltyPoint = await this.organisationLoyaltyPointsService.findOneByOrganisationId(organisation.id);
        return this.organisationLoyaltyPointsService.getTierFromIndex((_a = organisationLoyaltyPoint === null || organisationLoyaltyPoint === void 0 ? void 0 : organisationLoyaltyPoint.activeTier) !== null && _a !== void 0 ? _a : 0);
    }
    async getClubHabloSubscriptionOrganisations(profileId) {
        const organisationsAcivements = await this.organisationsService.getClubHabloSubscriptionOrganisations(profileId);
        return organisationsAcivements;
    }
    async getHighFiveOrganisations(user, profileId) {
        this.logger.verbose('AchievementsResolver.getHighFiveOrganisations (query)', {
            user: user.toLogObject(),
        });
        return await this.organisationsService.getHighFiveOrganisations(user.profileId, profileId);
    }
};
exports.OrganisationsResolver = OrganisationsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], OrganisationsResolver.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], OrganisationsResolver.prototype, "membershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => followers_service_1.FollowersService)),
    __metadata("design:type", followers_service_1.FollowersService)
], OrganisationsResolver.prototype, "followersService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => partnerships_service_1.PartnershipsService)),
    __metadata("design:type", partnerships_service_1.PartnershipsService)
], OrganisationsResolver.prototype, "partnershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => partnership_requests_service_1.PartnershipRequestsService)),
    __metadata("design:type", partnership_requests_service_1.PartnershipRequestsService)
], OrganisationsResolver.prototype, "partnershipRequestsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentives_service_1.IncentivesService)),
    __metadata("design:type", incentives_service_1.IncentivesService)
], OrganisationsResolver.prototype, "incentivesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinars_service_1.WebinarsService)),
    __metadata("design:type", webinars_service_1.WebinarsService)
], OrganisationsResolver.prototype, "webinarsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => events_service_1.EventsService)),
    __metadata("design:type", events_service_1.EventsService)
], OrganisationsResolver.prototype, "eventsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => subscriptions_service_1.SubscriptionsService)),
    __metadata("design:type", subscriptions_service_1.SubscriptionsService)
], OrganisationsResolver.prototype, "subscriptionsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => payment_transactions_service_1.PaymentTransactionsService)),
    __metadata("design:type", payment_transactions_service_1.PaymentTransactionsService)
], OrganisationsResolver.prototype, "paymentTransactionsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], OrganisationsResolver.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisation_loyalty_points_service_1.OrganisationLoyaltyPointsService)),
    __metadata("design:type", organisation_loyalty_points_service_1.OrganisationLoyaltyPointsService)
], OrganisationsResolver.prototype, "organisationLoyaltyPointsService", void 0);
__decorate([
    (0, graphql_1.Query)(() => organisation_model_1.Organisation),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id', { nullable: true })),
    __param(2, (0, graphql_1.Args)('vanityId', { nullable: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "organisation", null);
__decorate([
    (0, graphql_1.Query)(() => paginated_result_1.OrganisationsResult),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, organisations_args_1.OrganisationsArgs]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "organisations", null);
__decorate([
    (0, graphql_1.Mutation)(() => organisation_model_1.Organisation),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_organisation_input_1.CreateOrganisationInput]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "createOrganisation", null);
__decorate([
    (0, graphql_1.Mutation)(() => organisation_model_1.Organisation),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId')),
    __param(2, (0, graphql_1.Args)('organisationData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, update_organisation_input_1.UpdateOrganisationInput]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "updateOrganisation", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "adminPanelRemoveOrganisation", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId')),
    __param(2, (0, graphql_1.Args)('newOwnerProfileId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "changeOrganisationOwnership", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "acceptOrganisationOwnership", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "rejectOrganisationOwnership", null);
__decorate([
    (0, graphql_1.ResolveField)('memberships', () => paginated_result_1.MembershipsResult),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Parent)()),
    __param(2, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, organisation_model_1.Organisation,
        memberships_args_1.MembershipsArgs]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "memberships", null);
__decorate([
    (0, graphql_1.ResolveField)('incentives', () => paginated_result_1.IncentivesResult),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Parent)()),
    __param(2, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, organisation_model_1.Organisation,
        incentives_args_1.IncentivesArgs]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "incentives", null);
__decorate([
    (0, graphql_1.ResolveField)('followers', () => paginated_result_1.FollowersResult),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Parent)()),
    __param(2, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, organisation_model_1.Organisation,
        followers_args_1.FollowersArgs]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "followers", null);
__decorate([
    (0, graphql_1.ResolveField)('partnerships', () => [partnerships_model_1.Partnership]),
    __param(0, (0, graphql_1.Parent)()),
    __param(1, (0, graphql_1.Args)('isActive', { nullable: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [organisation_model_1.Organisation, Boolean]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "partnerships", null);
__decorate([
    (0, graphql_1.ResolveField)('receivedPartnershipRequests', () => [partnership_request_model_1.PartnershipRequest]),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [organisation_model_1.Organisation]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "receivedPartnershipRequests", null);
__decorate([
    (0, graphql_1.ResolveField)('sentPartnershipRequests', () => [partnership_request_model_1.PartnershipRequest]),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [organisation_model_1.Organisation]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "sentPartnershipRequests", null);
__decorate([
    (0, graphql_1.ResolveField)('followerStatus', () => follower_model_1.FollowerStatus, { nullable: true }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, organisation_model_1.Organisation]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "followerStatus", null);
__decorate([
    (0, graphql_1.ResolveField)('isMember', () => Boolean, { nullable: true }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, organisation_model_1.Organisation]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "isMember", null);
__decorate([
    (0, graphql_1.ResolveField)('permissions', () => [membership_model_1.MembershipPermission], { nullable: true }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, organisation_model_1.Organisation]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "permissions", null);
__decorate([
    (0, graphql_1.ResolveField)('isOwner', () => Boolean, { nullable: true }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, organisation_model_1.Organisation]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "isOwner", null);
__decorate([
    (0, graphql_1.ResolveField)('activeMembership', () => membership_model_1.Membership, { nullable: true }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, organisation_model_1.Organisation]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "activeMembership", null);
__decorate([
    (0, graphql_1.ResolveField)('isAdmin', () => Boolean, { nullable: true }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, organisation_model_1.Organisation]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "isAdmin", null);
__decorate([
    (0, graphql_1.ResolveField)('isManager', () => Boolean, { nullable: true }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, organisation_model_1.Organisation]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "isManager", null);
__decorate([
    (0, graphql_1.ResolveField)('isEditor', () => Boolean, { nullable: true }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, organisation_model_1.Organisation]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "isEditor", null);
__decorate([
    (0, graphql_1.ResolveField)('followersActiveCount', () => graphql_1.Int, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [organisation_model_1.Organisation]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "followersActiveCount", null);
__decorate([
    (0, graphql_1.ResolveField)('followersPendingCount', () => graphql_1.Int, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [organisation_model_1.Organisation]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "followersPendingCount", null);
__decorate([
    (0, graphql_1.ResolveField)('followersRejectedCount', () => graphql_1.Int, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [organisation_model_1.Organisation]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "followersRejectedCount", null);
__decorate([
    (0, graphql_1.ResolveField)('webinarsCount', () => graphql_1.Int, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [organisation_model_1.Organisation]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "webinarsCount", null);
__decorate([
    (0, graphql_1.ResolveField)('eventsCount', () => graphql_1.Int, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [organisation_model_1.Organisation]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "eventsCount", null);
__decorate([
    (0, graphql_1.ResolveField)('referralCount', () => graphql_1.Int, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [organisation_model_1.Organisation]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "referralCount", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "findConnectAccount", null);
__decorate([
    (0, graphql_1.Mutation)(returns => String),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId')),
    __param(2, (0, graphql_1.Args)('email')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "createConnectAccount", null);
__decorate([
    (0, graphql_1.Mutation)(returns => String),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "retrieveConnectAccount", null);
__decorate([
    (0, graphql_1.Mutation)(returns => String),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('partnershipId')),
    __param(2, (0, graphql_1.Args)('organisationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "getCustomerPortal", null);
__decorate([
    (0, graphql_1.Mutation)(returns => String),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "getStripeExpressLink", null);
__decorate([
    (0, graphql_1.Mutation)(returns => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('data')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, add_pre_approved_domains_input_1.AddPreApprovedDomainInput]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "addPreApprovedDomains", null);
__decorate([
    (0, graphql_1.Mutation)(returns => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('data')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, add_pre_approved_domains_input_1.AddPreApprovedDomainInput]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "removePreApprovedDomains", null);
__decorate([
    (0, graphql_1.Mutation)(() => organisation_model_1.Organisation),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('data')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, add_parent_organisation_1.ParentOrganisationInput]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "addParentOrganisation", null);
__decorate([
    (0, graphql_1.Mutation)(() => organisation_model_1.Organisation),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('data')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, add_parent_organisation_1.ParentOrganisationInput]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "removeParentOrganisation", null);
__decorate([
    (0, graphql_1.ResolveField)('activeTier', () => String, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [organisation_model_1.Organisation]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "activeTier", null);
__decorate([
    (0, graphql_1.Query)(() => [organisation_model_1.Organisation]),
    __param(0, (0, graphql_1.Args)('profileId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "getClubHabloSubscriptionOrganisations", null);
__decorate([
    (0, graphql_1.Query)(() => [organisation_model_1.Organisation]),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('profileId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], OrganisationsResolver.prototype, "getHighFiveOrganisations", null);
exports.OrganisationsResolver = OrganisationsResolver = __decorate([
    (0, graphql_1.Resolver)(() => organisation_model_1.Organisation)
], OrganisationsResolver);
//# sourceMappingURL=organisations.resolver.js.map