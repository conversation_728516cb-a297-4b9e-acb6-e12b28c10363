"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganisationsService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const organisation_model_1 = require("./models/organisation.model");
const organisations_repository_1 = require("./organisations.repository");
const sequelize_typescript_1 = require("sequelize-typescript");
const sequelize_1 = require("sequelize");
const underscore_1 = require("../common/helpers/underscore");
const memberships_service_1 = require("../memberships/memberships.service");
const membership_model_1 = require("../memberships/models/membership.model");
const organisations_args_1 = require("./args/organisations.args");
const followers_service_1 = require("../followers/followers.service");
const base_service_1 = require("../common/base.service");
const error_1 = require("../common/helpers/error");
const memberships_service_rights_1 = require("../memberships/helpers/memberships.service.rights");
const notifications_service_1 = require("../notifications/notifications.service");
const notifications_args_1 = require("../notifications/args/notifications.args");
const profiles_service_1 = require("../profiles/profiles.service");
const event_invitations_service_1 = require("../event-invitations/event-invitations.service");
const events_service_1 = require("../events/events.service");
const webinar_participants_service_1 = require("../webinar-participants/webinar-participants.service");
const webinars_service_1 = require("../webinars/webinars.service");
const partnership_requests_service_1 = require("../partnership-requests/partnership-requests.service");
const partnerships_service_1 = require("../partnerships/partnerships.service");
const incentives_service_1 = require("../incentives/incentives.service");
const incentive_participants_service_1 = require("../incentive-participants/incentive-participants.service");
const experiences_service_1 = require("../experiences/experiences.service");
const stream_organisations_service_1 = require("../feeds-organisations/stream-organisations.service");
const follower_model_1 = require("../followers/models/follower.model");
const stripe_1 = require("./helpers/stripe");
const subscriptions_service_1 = require("../subscriptions/subscriptions.service");
const stream_followers_service_1 = require("../feeds-followers/stream-followers.service");
const notificationLayoutHelper_1 = require("../email/helpers/notificationLayoutHelper");
const achievements_args_1 = require("../achievements/args/achievements.args");
let OrganisationsService = class OrganisationsService extends (0, base_service_1.BaseService)(organisation_model_1.Organisation) {
    async createOrganisation(user, createOrganisationDto, options) {
        this.logger.info('OrganisationsService.createOrganisation', {
            user,
            createOrganisationDto,
        });
        if (createOrganisationDto.vanityId) {
            if (await this.isVanityIdExists(createOrganisationDto.vanityId, options === null || options === void 0 ? void 0 : options.transaction)) {
                this.errorHelper.throwHttpException(`OrganisationsService.createOrganisation`, `Custom URL unavailable`);
            }
        }
        if (!createOrganisationDto.status) {
            createOrganisationDto.status = organisations_args_1.OrganisationStatus.Active;
        }
        const transaction = (options === null || options === void 0 ? void 0 : options.transaction) || (await this.sequelize.transaction());
        try {
            if (createOrganisationDto.type === organisations_args_1.OrganisationType.TravelAgency &&
                !createOrganisationDto.privacy) {
                createOrganisationDto.privacy = organisations_args_1.Privacy.Private;
            }
            const organisation = await this.create(createOrganisationDto, {
                transaction,
            });
            const membership = await this.membershipsService.create({
                organisationId: organisation.id,
                profileId: user.profileId,
                permissions: [membership_model_1.MembershipPermission.Owner],
                status: membership_model_1.MembershipStatus.Active,
            }, {
                transaction,
            });
            const profile = await this.profilesService.findById(user.profileId, {
                transaction,
            });
            if (!profile.primaryMembershipId) {
                await this.profilesService.updateById(profile.id, {
                    primaryMembershipId: membership.id,
                }, { transaction });
                await this.membershipsService.updateById(membership.id, { isPrimary: true }, { transaction });
            }
            await this.followersService.addOrApproveFollower(organisation.id, user.profileId, { transaction });
            if (this.streamFollowersService.client) {
                await this.streamFollowersService.updatePrimaryOrganisation(user.profileId, null, organisation.id);
            }
            await transaction.commit();
            return organisation;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`OrganisationsService.createOrganisation`, e.message);
        }
    }
    async updateOrganisation(id, updateOrganisationInput, options) {
        this.logger.info('OrganisationsService.updateOrganisation', {
            id,
            updateOrganisationInput,
            profileId: options.profileId,
        });
        await this.membershipsServiceRights.checkRights(id, {
            currentProfileId: options.profileId,
            rights: [memberships_service_rights_1.MembershipRight.UpdateOrganisation],
        });
        const organisation = await this.findById(id, {
            includeParams: [
                {
                    model: membership_model_1.Membership,
                    as: 'memberships',
                    where: {
                        profileId: options.profileId,
                    },
                },
            ],
        });
        if (updateOrganisationInput.vanityId &&
            updateOrganisationInput.vanityId !== organisation.vanityId) {
            if (await this.isVanityIdExists(updateOrganisationInput.vanityId)) {
                this.errorHelper.throwHttpException(`OrganisationsService.updateOrganisation`, `Custom URL unavailable`);
            }
        }
        updateOrganisationInput.description &&
            (updateOrganisationInput.description =
                updateOrganisationInput.description.replace(/\n\s*\n\s*\n/g, '\n\n'));
        const updatedOrganisation = this.updateById(organisation.id, updateOrganisationInput);
        this.streamOrgService.updateOrganisation(id, updateOrganisationInput, {
            profileId: options.profileId,
        });
        if (updateOrganisationInput.privacy === 'public') {
            await this.followersService.acceptAllPendingFollowers(organisation, null, { currentProfileId: options.profileId });
        }
        return updatedOrganisation;
    }
    async removeOrganisation(id, options) {
        this.logger.info('OrganisationsService.removeOrganisation', {
            id,
            profileId: options.profileId,
        });
        await this.membershipsServiceRights.checkRights(id, {
            currentProfileId: options.profileId,
            rights: [memberships_service_rights_1.MembershipRight.RemoveOrganisation],
            transaction: options.transaction,
        });
        const organisation = await this.findById(id, {
            includeParams: [
                {
                    model: membership_model_1.Membership,
                    as: 'memberships',
                    where: {
                        profileId: options.profileId,
                    },
                },
            ],
            transaction: options.transaction,
        });
        if (!organisation) {
            this.errorHelper.throwHttpException(`OrganisationsService.removeOrganisation`, `Organisation can't be removed by the current user`);
        }
        const membership = await this.membershipsService.findOne({
            organisationId: id,
            profileId: options.profileId,
            status: membership_model_1.MembershipStatus.Active,
        }, { transaction: options.transaction });
        const isOwner = membership.permissions.includes(membership_model_1.MembershipPermission.Owner);
        if (!isOwner) {
            this.errorHelper.throwHttpException(`OrganisationsService.removeOrganisation`, `Organisation can't be removed by the current user (needs Owner permission)`);
        }
        const transaction = options.transaction || (await this.sequelize.transaction());
        try {
            await this.membershipsService.remove({
                where: { organisationId: organisation.id },
                transaction,
            });
            await this.followersService.remove({
                where: { organisationId: organisation.id },
                transaction,
            });
            await this.removeById(organisation.id, { transaction });
            await transaction.commit();
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`OrganisationsService.removeOrganisation`, e.message);
        }
    }
    async adminPanelRemoveOrganisation(organisationId, options) {
        this.logger.info('OrganisationsService.adminPanelRemoveOrganisation', {
            organisationId,
            profileId: options.profileId,
        });
        const habloSuperAdminUserIDs = [
            '5f5b44fc4ac650006fc9505c',
            '111149247624107403444',
        ];
        if (habloSuperAdminUserIDs.indexOf(options.profileId) === -1) {
            this.errorHelper.throwHttpException(`OrganisationsService.adminPanelRemoveOrganisation`, `Current user does not have admin privileges`);
            return;
        }
        const organisation = await this.findById(organisationId, {
            transaction: options.transaction,
        });
        if (!organisation) {
            this.errorHelper.throwHttpException(`OrganisationsService.adminPanelRemoveOrganisation`, `Organisation doesn't exist`);
        }
        if (organisation.status === organisations_args_1.OrganisationStatus.Removed) {
            this.errorHelper.throwHttpException(`OrganisationsService.adminPanelRemoveOrganisation`, `Organisation already removed`);
        }
        const transaction = options.transaction || (await this.sequelize.transaction());
        try {
            await this.updateById(organisation.id, {
                name: '(deleted organisation)',
                image: '',
                status: organisations_args_1.OrganisationStatus.Removed,
                vanityId: '-removed!-' + organisationId,
            }, { transaction });
            const memberships = await this.membershipsService.findAll({
                organisationId: organisation.id,
            }, { transaction });
            for (const membership of memberships) {
                if (membership.isPrimary) {
                    await this.membershipsService.updateById(membership.id, {
                        status: membership_model_1.MembershipStatus.Inactive,
                        isPrimary: false,
                    }, { transaction });
                    const newPrimaryMembership = await this.membershipsService.create({
                        isPrimary: true,
                        organisationName: organisation.name,
                        profileId: membership.profileId,
                        status: membership_model_1.MembershipStatus.Active,
                        permissions: [],
                        position: membership.position,
                    }, { transaction });
                    await this.profilesService.updateById(newPrimaryMembership.profileId, {
                        primaryMembershipId: newPrimaryMembership.id,
                    }, { transaction });
                }
                else {
                    await this.membershipsService.updateById(membership.id, {
                        status: membership_model_1.MembershipStatus.Inactive,
                    }, { transaction });
                }
                await this.notificationsService.remove({
                    where: {
                        membershipId: membership.id,
                    },
                    transaction,
                });
            }
            const events = await this.eventsService.findAll({
                organisationId: organisation.id,
            }, { transaction });
            for (const event of events) {
                this.logger.info('looking up event:', {
                    event,
                });
                const eventInvitations = await this.eventInvitationsService.findAll({
                    eventId: event.id,
                }, { transaction });
                for (const eventInvitation of eventInvitations) {
                    await this.notificationsService.remove({
                        where: {
                            eventInvitationId: eventInvitation.id,
                        },
                        transaction,
                    });
                }
            }
            const webinars = await this.webinarsService.findAll({
                organisationId: organisation.id,
            }, { transaction });
            for (const webinar of webinars) {
                const webinarParticipants = await this.webinarParticipantsService.findAll({
                    webinarId: webinar.id,
                }, { transaction });
                for (const webinarParticipant of webinarParticipants) {
                    await this.notificationsService.remove({
                        where: {
                            webinarParticipantId: webinarParticipant.id,
                        },
                        transaction,
                    });
                }
            }
            const incentives = await this.incentivesService.findAll({
                organisationId: organisation.id,
            }, { transaction });
            for (const incentive of incentives) {
                const incentiveParticipants = await this.incentiveParticipantsService.findAll({
                    incentiveId: incentive.id,
                }, { transaction });
                for (const incentiveParticipant of incentiveParticipants) {
                    await this.notificationsService.remove({
                        where: {
                            incentiveParticipantId: incentiveParticipant.id,
                        },
                        transaction,
                    });
                }
            }
            const now = Date.now();
            this.incentivesService.update({
                where: {
                    organisationId: organisation.id,
                    endDate: { [sequelize_1.Op.gt]: now },
                },
                update: {
                    endDate: now,
                },
                transaction,
            });
            const experiences = await this.experiencesService.findAll({
                organisationId: organisation.id,
            }, { transaction });
            for (const experience of experiences) {
                await this.experiencesService.updateById(experience.id, {
                    organisationId: null,
                    organisationName: organisation.name,
                }, { transaction });
            }
            const partnershipRequests = await this.partnershipRequestsService.findAll({
                [sequelize_1.Op.or]: [
                    { senderOrganisationId: organisation.id },
                    { receiverOrganisationId: organisation.id },
                ],
            }, { transaction });
            for (const partnershipRequest of partnershipRequests) {
                await this.partnershipRequestsService.removeById(partnershipRequest.id, {
                    transaction,
                });
                await this.notificationsService.remove({
                    where: {
                        partnershipRequestId: partnershipRequest.id,
                    },
                    transaction,
                });
            }
            await this.notificationsService.remove({
                where: {
                    organisationId: organisation.id,
                },
                transaction,
            });
            const followers = await this.followersService.findAll({
                organisationId: organisation.id,
                status: { [sequelize_1.Op.notLike]: follower_model_1.FollowerStatus.Inactive },
            });
            for (const follower of followers) {
                this.followersService.updateFollowerStatus(organisationId, follower.profileId, follower_model_1.FollowerActionType.Inactivate, {
                    currentProfileId: options.profileId,
                });
                await this.notificationsService.remove({
                    where: {
                        followerId: follower.id,
                    },
                    transaction,
                });
            }
            await transaction.commit();
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`OrganisationsService.adminPanelRemoveOrganisation`, e.message);
        }
    }
    async changeOrganisationOwnership(organisationId, newOwnerProfileId, options) {
        this.logger.info('OrganisationsService.changeOrganisationOwnership', {
            organisationId,
            currentUser: options.currentUser.toLogObject(),
        });
        await this.membershipsServiceRights.checkRights(organisationId, {
            currentProfileId: options.currentUser.profileId,
            rights: [memberships_service_rights_1.MembershipRight.ChangeOwner],
        });
        let newOwnerMembership = await this.membershipsService.findOne({
            organisationId,
            profileId: newOwnerProfileId,
        });
        const transaction = await this.sequelize.transaction();
        try {
            if (newOwnerMembership) {
                await this.membershipsService.updateById(newOwnerMembership.id, {
                    permissions: underscore_1.Underscore.uniq([
                        ...newOwnerMembership.permissions,
                        membership_model_1.MembershipPermission.OwnerPending,
                    ]),
                    status: membership_model_1.MembershipStatus.Active,
                }, { transaction });
            }
            else {
                newOwnerMembership = await this.membershipsService.create({
                    organisationId,
                    profileId: newOwnerProfileId,
                    permissions: [membership_model_1.MembershipPermission.OwnerPending],
                    status: membership_model_1.MembershipStatus.Active,
                }, { transaction });
            }
            await this.notificationsService.createNotification({
                ownerProfileId: newOwnerProfileId,
                profileId: options.currentUser.profileId,
                membershipId: newOwnerMembership.id,
                organisationId: newOwnerMembership.organisationId,
                type: notifications_args_1.NotificationType.OrganisationOwnershipRequested,
            }, {
                transaction,
            });
            await transaction.commit();
            const profileIds = [newOwnerProfileId];
            const replacementOrg = await this.findById(organisationId);
            const replacements = [options.currentUser.name, replacementOrg.name];
            await this.notificationsService.sendPushNotification({
                profileIds,
                replacements,
                messageType: notifications_args_1.NotificationMessage.OrganisationOwnershipRequested,
                route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.organisationSettingsUserRoles, {
                    organisationId: newOwnerMembership.organisationId,
                }),
            });
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`OrganisationsService.changeOrganisationOwnership`, e.message);
        }
    }
    async acceptOrganisationOwnership(organisationId, options) {
        this.logger.info('OrganisationsService.acceptOrganisationOwnership', {
            organisationId,
            currentUser: options.currentUser.toLogObject(),
        });
        const transaction = await this.sequelize.transaction();
        try {
            const newOwnerMembership = await this.membershipsService.findOne({
                organisationId,
                profileId: options.currentUser.profileId,
                permissions: {
                    [sequelize_1.Op.contains]: [membership_model_1.MembershipPermission.OwnerPending],
                },
            }, { transaction });
            if (!newOwnerMembership) {
                throw new Error('OwnerPending status not found');
            }
            const previousOwnerMembership = await this.membershipsService.findOne({
                organisationId,
                status: membership_model_1.MembershipStatus.Active,
                permissions: {
                    [sequelize_1.Op.contains]: [membership_model_1.MembershipPermission.Owner],
                },
            }, { transaction });
            if (!previousOwnerMembership) {
                throw new Error('Active Owner not found');
            }
            await this.membershipsService.updateById(previousOwnerMembership.id, {
                permissions: [membership_model_1.MembershipPermission.Admin],
            }, { transaction });
            await this.membershipsService.updateById(newOwnerMembership.id, {
                permissions: [membership_model_1.MembershipPermission.Owner],
            }, { transaction });
            await this.notificationsService.createNotification({
                ownerProfileId: previousOwnerMembership.profileId,
                profileId: options.currentUser.profileId,
                membershipId: newOwnerMembership.id,
                type: notifications_args_1.NotificationType.OrganisationOwnershipAccepted,
            }, {
                transaction,
            });
            await transaction.commit();
            const profileIds = [previousOwnerMembership.profileId];
            const replacementOrg = await this.findById(organisationId);
            const replacements = [options.currentUser.name, replacementOrg.name];
            await this.notificationsService.sendPushNotification({
                profileIds,
                replacements,
                messageType: notifications_args_1.NotificationMessage.OrganisationOwnershipAccepted,
                route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.organisationSettingsUserRoles, {
                    organisationId: replacementOrg === null || replacementOrg === void 0 ? void 0 : replacementOrg.id,
                }),
            });
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`OrganisationsService.acceptOrganisationOwnership`, e.message);
        }
    }
    async rejectOrganisationOwnership(organisationId, options) {
        this.logger.info('OrganisationsService.rejectOrganisationOwnership', {
            organisationId,
            currentUser: options.currentUser.toLogObject(),
        });
        const transaction = await this.sequelize.transaction();
        try {
            const newOwnerMembership = await this.membershipsService.findOne({
                organisationId,
                profileId: options.currentUser.profileId,
                permissions: {
                    [sequelize_1.Op.contains]: [membership_model_1.MembershipPermission.OwnerPending],
                },
            }, { transaction });
            if (!newOwnerMembership) {
                throw new Error('OwnerPending status not found');
            }
            const newPermissions = newOwnerMembership.permissions.filter(permission => permission !== membership_model_1.MembershipPermission.OwnerPending);
            if (newPermissions.length > 0) {
                await this.membershipsService.updateById(newOwnerMembership.id, {
                    permissions: newPermissions,
                }, { transaction });
            }
            else {
                await this.membershipsService.removeById(newOwnerMembership.id, {
                    transaction,
                });
            }
            await transaction.commit();
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`OrganisationsService.rejectOrganisationOwnership`, e.message);
        }
    }
    async findOrganisations(profileId, filter, pagination) {
        this.logger.verbose('OrganisationsService.findOrganisations', {
            profileId,
            filter,
            pagination,
        });
        const paginationOrganisationResult = await this.organisationsRepository.findOrganisations(profileId, filter, pagination);
        return {
            records: paginationOrganisationResult.records,
            totalCount: paginationOrganisationResult.totalCount,
        };
    }
    async isVanityIdExists(vanityId, transaction) {
        const organisation = await this.findOne(sequelize_typescript_1.Sequelize.where(sequelize_typescript_1.Sequelize.fn('lower', sequelize_typescript_1.Sequelize.col('vanityId')), sequelize_typescript_1.Sequelize.fn('lower', vanityId)), {
            transaction,
        });
        const reservedURLS = [
            'profile',
            'organisation',
            'inbox',
            'calendar',
            'home',
            'search',
            'incentives',
            'connect',
            'habloadmin',
            'explore',
            'search',
            'signup',
            'login',
            'logout',
            'auth',
            'networkerror',
            'dailyquiz',
            'home',
            'loyalty',
            'rewards',
            'dashboard',
            'post',
        ];
        const isReservedURL = reservedURLS.indexOf(vanityId.toLowerCase()) > -1;
        const containsHablo = vanityId.toLowerCase().indexOf('hablo') > -1;
        return !!organisation || isReservedURL || containsHablo;
    }
    async getOrganisationId(profileId, includeRemoved, withoutMemberShip) {
        const queryParams = {};
        if (includeRemoved !== true) {
            queryParams['status'] = {
                [sequelize_1.Op.in]: [organisations_args_1.OrganisationStatus.Active],
            };
        }
        const organisationsFollowed = await this.findAll(queryParams, {
            attributes: ['id', 'name'],
            includeParams: [
                {
                    model: follower_model_1.Follower,
                    as: 'followers',
                    where: {
                        profileId,
                        status: follower_model_1.FollowerStatus.Active,
                    },
                },
            ],
        });
        if (withoutMemberShip !== true) {
            const organisationsWithMembership = await this.findAll(queryParams, {
                attributes: ['id', 'name'],
                includeParams: [
                    {
                        model: membership_model_1.Membership,
                        as: 'memberships',
                        where: {
                            profileId,
                            status: membership_model_1.MembershipStatus.Active,
                        },
                    },
                ],
            });
            return {
                [sequelize_1.Op.in]: underscore_1.Underscore.uniq([
                    ...underscore_1.Underscore.map(organisationsWithMembership, 'id'),
                    ...underscore_1.Underscore.map(organisationsFollowed, 'id'),
                ]),
            };
        }
        else {
            return {
                [sequelize_1.Op.in]: [...underscore_1.Underscore.map(organisationsFollowed, 'id')],
            };
        }
    }
    async findConnectAccount(organisationId, options) {
        this.logger.info('OrganisationsService.findConnectAccount', {
            organisationId,
            currentUser: options.currentUser.toLogObject(),
        });
        try {
            const connectAccount = await this.findById(organisationId);
            if (!connectAccount) {
                throw new Error(`Organisation not found`);
            }
            return connectAccount.stripeConnectAccount;
        }
        catch (e) {
            this.errorHelper.throwHttpException(`OrganisationsService.findConnectAccount`, e.message);
        }
    }
    async createConnectAccount(organisationId, email, options) {
        this.logger.info('OrganisationsService.createConnectAccount', {
            email,
            currentUser: options.currentUser.toLogObject(),
        });
        const transaction = await this.sequelize.transaction();
        try {
            const organisation = await this.findById(organisationId);
            if (!organisation) {
                throw new Error(`Organisation not found`);
            }
            let connect;
            if (organisation.stripeConnectAccount) {
                connect = await this.retrieveConnectAccount(organisationId, {
                    currentUser: options === null || options === void 0 ? void 0 : options.currentUser,
                });
            }
            else {
                const newAccount = await this.stripeHelper.createConnectAccount({
                    email: email,
                    organisation_id: organisationId,
                });
                connect = newAccount.accountLink.url;
                await this.updateById(organisationId, {
                    stripeConnectAccount: newAccount.connect.id,
                    isConnectOnboarded: false,
                }, { transaction });
            }
            await transaction.commit();
            return connect;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`OrganisationsService.createConnectAccount`, e.message);
        }
    }
    async retrieveConnectAccount(organisationId, options) {
        this.logger.info('OrganisationsService.retrieveConnectAccount', {
            organisationId,
            currentUser: options.currentUser.toLogObject(),
        });
        try {
            const organisation = await this.findOne({
                id: organisationId,
            });
            if (!organisation) {
                throw new Error(`Organisation not found`);
            }
            if (organisation.stripeConnectAccount &&
                organisation.isConnectOnboarded === true) {
                return organisation.stripeConnectAccount;
            }
            else if (organisation.stripeConnectAccount &&
                (organisation.isConnectOnboarded === false ||
                    organisation.isConnectOnboarded === null)) {
                const connectAccount = await this.stripeHelper.retrieveConnectAccount({
                    connect_id: organisation.stripeConnectAccount,
                    organisation_id: organisationId,
                });
                if (typeof connectAccount === 'string') {
                    return connectAccount;
                }
                else {
                    await this.updateById(organisation.id, {
                        isConnectOnboarded: true,
                    });
                }
                return organisation.stripeConnectAccount;
            }
            else if (!organisation.stripeConnectAccount &&
                (organisation.isConnectOnboarded === false ||
                    organisation.isConnectOnboarded === null)) {
                throw new Error('Please set up a Payment Account to continue.');
            }
        }
        catch (e) {
            this.errorHelper.throwHttpException(`OrganisationsService.retrieveConnectAccount`, e.message);
        }
    }
    async getCustomerPortal(partnershipId, organisationId, options) {
        this.logger.info('OrganisationsService.getCustomerPortal', {
            partnershipId,
            organisationId,
            currentUser: options.currentUser.toLogObject(),
        });
        try {
            const organisation = await this.findById(organisationId);
            if (!organisation) {
                throw new Error(`Organisation not found`);
            }
            const partnership = await this.partnershipsService.findById(partnershipId);
            if (!partnership) {
                throw new Error(`Partnership not found`);
            }
            const partnershipRequest = await this.partnershipRequestsService.findById(partnership.partnershipRequestId);
            if (!partnershipRequest) {
                throw new Error(`PartnershipRequest not found`);
            }
            const subsciption = await this.subscriptionsService.findOne({
                partnershipRequestId: partnershipRequest.id,
            });
            if (!subsciption) {
                throw new Error(`Subscription not found`);
            }
            if (organisation.id === partnershipRequest.receiverOrganisationId) {
                const senderOrganisation = await this.findById(partnershipRequest.senderOrganisationId);
                const customerPortal = await this.stripeHelper.createExpressLoginLink({
                    connect_id: senderOrganisation.stripeConnectAccount,
                });
                return customerPortal;
            }
            else if (organisation.id === partnershipRequest.senderOrganisationId) {
                const customerPortal = await this.stripeHelper.createCustomerPortalSession({
                    customer_id: subsciption.stripeCustomerId,
                    connect_id: subsciption.stripeConnectId,
                });
                return customerPortal;
            }
        }
        catch (e) {
            this.errorHelper.throwHttpException(`OrganisationsService.getCustomerPortal`, e.message);
        }
    }
    async getStripeExpressLink(organisationId, options) {
        this.logger.info('OrganisationsService.getStripeExpressLink', {
            organisationId,
            currentUser: options.currentUser.toLogObject(),
        });
        try {
            const organisation = await this.findById(organisationId);
            if (!organisation) {
                throw new Error(`Organisation not found`);
            }
            if (!organisation.stripeConnectAccount) {
                return null;
            }
            const stripeExpressLink = await this.stripeHelper.createExpressLoginLink({
                connect_id: organisation.stripeConnectAccount,
            });
            return stripeExpressLink;
        }
        catch (e) {
            this.errorHelper.throwHttpException(`OrganisationsService.getStripeExpressLink`, e.message);
        }
    }
    async addPreApprovedDomains(data, options) {
        this.logger.info('OrganisationsService.addPreApprovedDomains', {
            data,
            currentUser: options.currentUser.toLogObject(),
        });
        const transaction = await this.sequelize.transaction();
        try {
            const organisation = await this.findById(data.organisationId);
            if (!organisation) {
                throw new Error(`Organisation not found`);
            }
            if (!data.domain) {
                throw new Error('Domain is not valid');
            }
            const checkExists = Array.isArray(organisation.preApprovedDomains)
                ? organisation.preApprovedDomains.includes(data.domain)
                : '';
            if (checkExists) {
                throw new Error('Domain already added');
            }
            organisation.preApprovedDomains =
                organisation.preApprovedDomains === null
                    ? []
                    : organisation.preApprovedDomains;
            const allDomains = organisation.preApprovedDomains.concat(data.domain);
            await this.updateById(organisation.id, {
                preApprovedDomains: allDomains,
            }, { transaction });
            await transaction.commit();
            return organisation;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`OrganisationsService.addPreApprovedDomains`, e.message);
        }
    }
    async removePreApprovedDomains(data, options) {
        this.logger.info('OrganisationsService.removePreApprovedDomains', {
            data,
            currentUser: options.currentUser.toLogObject(),
        });
        const transaction = await this.sequelize.transaction();
        try {
            const organisation = await this.findById(data.organisationId);
            if (!organisation) {
                throw new Error(`Organisation not found`);
            }
            const checkExists = Array.isArray(organisation.preApprovedDomains)
                ? organisation.preApprovedDomains.includes(data.domain)
                : '';
            if (!checkExists) {
                throw new Error('Domain not found');
            }
            const allDomains = organisation.preApprovedDomains;
            allDomains.splice(allDomains.indexOf(data.domain), 1);
            await this.updateById(organisation.id, {
                preApprovedDomains: allDomains,
            }, { transaction });
            await transaction.commit();
            return organisation;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`OrganisationsService.removePreApprovedDomains`, e.message);
        }
    }
    async addParentOrganisation(data, options) {
        this.logger.info('OrganisationsService.addParentOrganisation', {
            data,
            currentUser: options.currentUser.toLogObject(),
        });
        const transaction = await this.sequelize.transaction();
        try {
            const organisation = await this.findById(data.organisationId);
            if (!organisation) {
                throw new Error(`Organisation not found`);
            }
            const parentOrganisation = await this.findById(data.parentId);
            if (!parentOrganisation) {
                throw new Error(`Parent Organisation does not exist`);
            }
            const parentOrgs = organisation.parentOrganisations;
            if (parentOrgs.length === 1) {
                throw new Error(`Cannot add more than 1 parent organisation`);
            }
            const index = parentOrgs.findIndex(x => x.id === data.parentId);
            if (parentOrgs.length && index > -1) {
                throw new Error(`Parent Organisation already added`);
            }
            parentOrgs.push({ id: data.parentId });
            await this.updateById(organisation.id, {
                parentOrganisations: parentOrgs,
            }, { transaction });
            await this.addMembershipAndFollowersToParentOrg(data, {
                currentUser: options.currentUser,
            });
            await transaction.commit();
            return organisation;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`OrganisationsService.addParentOrganisation`, e.message);
        }
    }
    async removeParentOrganisation(data, options) {
        this.logger.info('OrganisationsService.removeParentOrganisation', {
            data,
            currentUser: options.currentUser.toLogObject(),
        });
        const transaction = await this.sequelize.transaction();
        try {
            const organisation = await this.findById(data.organisationId);
            if (!organisation) {
                throw new Error(`Organisation not found`);
            }
            const parentOrganisation = await this.findById(data.parentId);
            if (!parentOrganisation) {
                throw new Error(`Parent Organisation not exists`);
            }
            const parentOrgs = organisation.parentOrganisations;
            const index = parentOrgs.findIndex(x => x.id === data.parentId);
            if (parentOrgs.length && index < 0) {
                throw new Error(`Parent Organisation not found`);
            }
            parentOrgs.splice(index, 1);
            await this.updateById(organisation.id, {
                parentOrganisations: parentOrgs,
            }, { transaction });
            await this.removeMembershipAndFollowersFromParentOrg(data, {
                currentUser: options.currentUser,
            });
            await transaction.commit();
            return organisation;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`OrganisationsService.removeParentOrganisation`, e.message);
        }
    }
    async addMembershipAndFollowersToParentOrg(data, options) {
        this.logger.info('OrganisationsService.addMembershipAndFollowersToParentOrg', {
            data,
            currentUser: options.currentUser.toLogObject(),
        });
        const transaction = await this.sequelize.transaction();
        try {
            const childOrgMemberships = await this.membershipsService.findAll({
                organisationId: data.organisationId,
                isPrimary: true,
                status: membership_model_1.MembershipStatus.Active,
            });
            const parentOrgMemberships = await this.membershipsService.findAll({
                organisationId: data.parentId,
                isPrimary: true,
                status: membership_model_1.MembershipStatus.Active,
            });
            if (childOrgMemberships.length) {
                for (const membership of childOrgMemberships) {
                    await this.membershipsService.addMemberToParent(data.parentId, membership.profileId, data.organisationId, { transaction });
                }
            }
            if (parentOrgMemberships.length) {
                const childfollowsArray = [];
                for (const membership of parentOrgMemberships) {
                    await this.followersService.addOrApproveFollower(data.organisationId, membership.profileId, {
                        transaction,
                    });
                    const orgObj = {
                        source: 'user:' + membership.profileId,
                        target: 'organisations:' + data.organisationId,
                    };
                    childfollowsArray.push(orgObj);
                }
                await this.streamFollowersService.bulkFollowScript(childfollowsArray);
            }
            await transaction.commit();
            return true;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`OrganisationsService.addMembershipAndFollowersToParentOrg`, e.message);
        }
    }
    async removeMembershipAndFollowersFromParentOrg(data, options) {
        this.logger.info('OrganisationsService.removeMembershipAndFollowersFromParentOrg', {
            data,
            currentUser: options.currentUser.toLogObject(),
        });
        const transaction = await this.sequelize.transaction();
        try {
            const childOrgMemberships = await this.membershipsService.findAll({
                organisationId: data.organisationId,
                isPrimary: true,
                status: membership_model_1.MembershipStatus.Active,
            });
            const parentOrgMemberships = await this.membershipsService.findAll({
                organisationId: data.parentId,
                isPrimary: true,
                status: membership_model_1.MembershipStatus.Active,
            });
            if (childOrgMemberships.length) {
                for (const membership of childOrgMemberships) {
                    await this.membershipsService.removeMemberFromParent(data.parentId, membership.profileId, data.organisationId, { transaction });
                }
            }
            const childUnfollowsArray = [];
            if (parentOrgMemberships.length) {
                const parentMembershipIds = underscore_1.Underscore.uniq(parentOrgMemberships.map(membership => membership.profileId));
                const profileIds = underscore_1.Underscore.uniq([...parentMembershipIds]);
                await this.followersService.remove({
                    where: {
                        profileId: profileIds,
                        organisationId: data.organisationId,
                    },
                    transaction,
                });
                for (const profileId of profileIds) {
                    const orgObj = {
                        source: profileId,
                        target: data.organisationId,
                    };
                    childUnfollowsArray.push(orgObj);
                }
            }
            if (childUnfollowsArray.length) {
                await this.streamFollowersService.bulkUnfollowScript(childUnfollowsArray);
            }
            await transaction.commit();
            return true;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`OrganisationsService.removeMembershipAndFollowersFromParentOrg`, e.message);
        }
    }
    async addNewConnectedOrgToExistingParentChild(data, options) {
        this.logger.info('OrganisationsService.addNewConnectedOrgToExistingParentChild', {
            data,
        });
        const isTransactionSet = !!(options === null || options === void 0 ? void 0 : options.transaction);
        const transaction = (options === null || options === void 0 ? void 0 : options.transaction) || (await this.sequelize.transaction());
        try {
            const childOrgMemberships = await this.membershipsService.findAll({
                organisationId: data.organisationId,
                isPrimary: true,
                status: membership_model_1.MembershipStatus.Active,
            });
            const childfollowsArray = [];
            if (childOrgMemberships.length) {
                for (const member of childOrgMemberships) {
                    await this.followersService.addOrApproveFollower(data.parentId, member.profileId, { transaction });
                    const orgObj = {
                        source: 'user:' + member.profileId,
                        target: 'organisations:' + data.organisationId,
                    };
                    childfollowsArray.push(orgObj);
                }
            }
            if (!isTransactionSet) {
                await transaction.commit();
            }
            await this.streamFollowersService.bulkFollowScript(childfollowsArray);
            return true;
        }
        catch (e) {
            if (!isTransactionSet) {
                await transaction.rollback();
            }
            this.errorHelper.throwHttpException(`OrganisationsService.addNewConnectedOrgToExistingParentChild`, e.message);
        }
    }
    async removeConnectedOrgToExistingParentChild(data, options) {
        this.logger.info('OrganisationsService.removeConnectedOrgToExistingParentChild', {
            data,
        });
        const isTransactionSet = !!(options === null || options === void 0 ? void 0 : options.transaction);
        const transaction = (options === null || options === void 0 ? void 0 : options.transaction) || (await this.sequelize.transaction());
        try {
            const childOrgMemberships = await this.membershipsService.findAll({
                organisationId: data.organisationId,
                isPrimary: true,
                status: membership_model_1.MembershipStatus.Active,
            }, {
                transaction,
            });
            const childUnfollowsArray = [];
            if (childOrgMemberships.length) {
                const childOrgMemberIds = underscore_1.Underscore.uniq(childOrgMemberships.map(membership => membership.profileId));
                const profileIds = underscore_1.Underscore.uniq([...childOrgMemberIds]);
                if (profileIds.length > 0) {
                    await this.followersService.remove({
                        where: {
                            organisationId: data.parentId,
                            profileId: {
                                [sequelize_1.Op.in]: profileIds,
                            },
                        },
                        transaction,
                    });
                    for (const profile of profileIds) {
                        const orgObj = {
                            source: profile,
                            target: data.parentId,
                        };
                        childUnfollowsArray.push(orgObj);
                    }
                }
            }
            if (!isTransactionSet) {
                await transaction.commit();
            }
            await this.streamFollowersService.bulkUnfollowScript(childUnfollowsArray);
            return true;
        }
        catch (e) {
            if (!isTransactionSet) {
                await transaction.rollback();
            }
            this.errorHelper.throwHttpException(`OrganisationsService.removeConnectedOrgToExistingParentChild`, e.message);
        }
    }
    async getClubHabloSubscriptionOrganisations(profileId) {
        this.logger.verbose('Fetching all organisations with Hablo subscription');
        const organisationsAcivements = await this.organisationsRepository.findHabloSubscriptionOrganisations(profileId);
        return organisationsAcivements.map(org => {
            var _a, _b, _c, _d, _e, _f;
            org['isAchieved'] =
                (_b = (_a = org.achievements) === null || _a === void 0 ? void 0 : _a.some(achievement => achievement.profileId === profileId &&
                    achievement.type === achievements_args_1.AchievementType.Ambassador &&
                    achievement.isAchieved)) !== null && _b !== void 0 ? _b : false;
            const ambassador = (_c = org.achievements) === null || _c === void 0 ? void 0 : _c.find(achievement => achievement.profileId === profileId &&
                achievement.type === achievements_args_1.AchievementType.Ambassador);
            const achievedCounts = (_d = org.achievements) === null || _d === void 0 ? void 0 : _d.filter(achievement => achievement.profileId === profileId &&
                achievement.type !== achievements_args_1.AchievementType.Ambassador &&
                achievement.type !== achievements_args_1.AchievementType.HighFive &&
                achievement.isAchieved);
            org['level'] = (_e = ambassador === null || ambassador === void 0 ? void 0 : ambassador.number) !== null && _e !== void 0 ? _e : 0;
            org['achievedCounts'] = (_f = achievedCounts === null || achievedCounts === void 0 ? void 0 : achievedCounts.length) !== null && _f !== void 0 ? _f : 0;
            org['totalCounts'] = 3;
            return org;
        });
    }
    async getHighFiveOrganisations(currentUserId, profileId) {
        this.logger.verbose('OrganisationsService.getHighFiveOrganisations');
        const organisations = await this.findAll({
            hasClubHabloSubscription: true,
        }, {
            attributes: ['id', 'name', 'image', 'vanityId'],
            includeParams: [
                {
                    model: membership_model_1.Membership,
                    as: 'memberships',
                    where: {
                        profileId: currentUserId,
                        status: membership_model_1.MembershipStatus.Active,
                        permissions: {
                            [sequelize_1.Op.overlap]: [
                                membership_model_1.MembershipPermission.Owner,
                                membership_model_1.MembershipPermission.Admin,
                                membership_model_1.MembershipPermission.HiddenAdmin,
                                membership_model_1.MembershipPermission.Manager,
                                membership_model_1.MembershipPermission.Editor,
                            ],
                        },
                    },
                    attributes: [],
                    required: true,
                },
                {
                    model: follower_model_1.Follower,
                    as: 'followers',
                    where: {
                        profileId: profileId,
                        status: follower_model_1.FollowerStatus.Active,
                    },
                    attributes: [],
                    required: true,
                },
            ],
        });
        return organisations;
    }
    async getOrganisationIdbyVanityId(vanityId) {
        const organisation = await this.findOne(sequelize_typescript_1.Sequelize.where(sequelize_typescript_1.Sequelize.fn('lower', sequelize_typescript_1.Sequelize.col('vanityId')), sequelize_typescript_1.Sequelize.fn('lower', vanityId)));
        return organisation === null || organisation === void 0 ? void 0 : organisation.id;
    }
    async getReferralCount(organisationId) {
        this.logger.info('OrganisationsService.getReferralCount', {
            organisationId,
        });
        try {
            const count = await this.profilesService.count({
                referredByOrganisationId: organisationId,
            });
            return count;
        }
        catch (error) {
            this.logger.error(`Error in OrganisationsService.getReferralCount for organisationId ${organisationId}: ${error.message}`, { error });
            this.errorHelper.throwHttpException(`OrganisationsService.getReferralCount`, error.message);
        }
    }
};
exports.OrganisationsService = OrganisationsService;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_repository_1.OrganisationsRepository)),
    __metadata("design:type", organisations_repository_1.OrganisationsRepository)
], OrganisationsService.prototype, "organisationsRepository", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], OrganisationsService.prototype, "membershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_rights_1.MembershipsServiceRights)),
    __metadata("design:type", memberships_service_rights_1.MembershipsServiceRights)
], OrganisationsService.prototype, "membershipsServiceRights", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => notifications_service_1.NotificationsService)),
    __metadata("design:type", notifications_service_1.NotificationsService)
], OrganisationsService.prototype, "notificationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => followers_service_1.FollowersService)),
    __metadata("design:type", followers_service_1.FollowersService)
], OrganisationsService.prototype, "followersService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], OrganisationsService.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => events_service_1.EventsService)),
    __metadata("design:type", events_service_1.EventsService)
], OrganisationsService.prototype, "eventsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => event_invitations_service_1.EventInvitationsService)),
    __metadata("design:type", event_invitations_service_1.EventInvitationsService)
], OrganisationsService.prototype, "eventInvitationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinar_participants_service_1.WebinarParticipantsService)),
    __metadata("design:type", webinar_participants_service_1.WebinarParticipantsService)
], OrganisationsService.prototype, "webinarParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinars_service_1.WebinarsService)),
    __metadata("design:type", webinars_service_1.WebinarsService)
], OrganisationsService.prototype, "webinarsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentives_service_1.IncentivesService)),
    __metadata("design:type", incentives_service_1.IncentivesService)
], OrganisationsService.prototype, "incentivesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentive_participants_service_1.IncentiveParticipantsService)),
    __metadata("design:type", incentive_participants_service_1.IncentiveParticipantsService)
], OrganisationsService.prototype, "incentiveParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => partnerships_service_1.PartnershipsService)),
    __metadata("design:type", partnerships_service_1.PartnershipsService)
], OrganisationsService.prototype, "partnershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => partnership_requests_service_1.PartnershipRequestsService)),
    __metadata("design:type", partnership_requests_service_1.PartnershipRequestsService)
], OrganisationsService.prototype, "partnershipRequestsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => experiences_service_1.ExperiencesService)),
    __metadata("design:type", experiences_service_1.ExperiencesService)
], OrganisationsService.prototype, "experiencesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => stream_organisations_service_1.StreamOrganisationsService)),
    __metadata("design:type", stream_organisations_service_1.StreamOrganisationsService)
], OrganisationsService.prototype, "streamOrgService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => subscriptions_service_1.SubscriptionsService)),
    __metadata("design:type", subscriptions_service_1.SubscriptionsService)
], OrganisationsService.prototype, "subscriptionsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => stream_followers_service_1.StreamFollowersService)),
    __metadata("design:type", stream_followers_service_1.StreamFollowersService)
], OrganisationsService.prototype, "streamFollowersService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", sequelize_typescript_1.Sequelize)
], OrganisationsService.prototype, "sequelize", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], OrganisationsService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], OrganisationsService.prototype, "errorHelper", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", stripe_1.StripeHelper)
], OrganisationsService.prototype, "stripeHelper", void 0);
exports.OrganisationsService = OrganisationsService = __decorate([
    (0, common_1.Injectable)()
], OrganisationsService);
//# sourceMappingURL=organisations.service.js.map