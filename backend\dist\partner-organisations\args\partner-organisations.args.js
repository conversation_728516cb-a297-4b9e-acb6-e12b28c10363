"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartnerOrganisationStatus = void 0;
const graphql_1 = require("@nestjs/graphql");
var PartnerOrganisationStatus;
(function (PartnerOrganisationStatus) {
    PartnerOrganisationStatus["Pending"] = "pending";
    PartnerOrganisationStatus["Rejected"] = "rejected";
    PartnerOrganisationStatus["Disconnected"] = "disconnected";
    PartnerOrganisationStatus["Approved"] = "approved";
    PartnerOrganisationStatus["Cancelled"] = "cancelled";
})(PartnerOrganisationStatus || (exports.PartnerOrganisationStatus = PartnerOrganisationStatus = {}));
(0, graphql_1.registerEnumType)(PartnerOrganisationStatus, {
    name: 'PartnerOrganisationStatus',
});
//# sourceMappingURL=partner-organisations.args.js.map