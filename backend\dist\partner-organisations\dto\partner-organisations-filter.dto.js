"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartnerOrganisationsFilter = void 0;
const graphql_1 = require("@nestjs/graphql");
const class_validator_1 = require("class-validator");
const partner_organisations_args_1 = require("../args/partner-organisations.args");
let PartnerOrganisationsFilter = class PartnerOrganisationsFilter {
};
exports.PartnerOrganisationsFilter = PartnerOrganisationsFilter;
__decorate([
    (0, graphql_1.Field)(() => [partner_organisations_args_1.PartnerOrganisationStatus], { nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(partner_organisations_args_1.PartnerOrganisationStatus, { each: true }),
    __metadata("design:type", Array)
], PartnerOrganisationsFilter.prototype, "status", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PartnerOrganisationsFilter.prototype, "searchText", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PartnerOrganisationsFilter.prototype, "organisationId", void 0);
exports.PartnerOrganisationsFilter = PartnerOrganisationsFilter = __decorate([
    (0, graphql_1.InputType)()
], PartnerOrganisationsFilter);
//# sourceMappingURL=partner-organisations-filter.dto.js.map