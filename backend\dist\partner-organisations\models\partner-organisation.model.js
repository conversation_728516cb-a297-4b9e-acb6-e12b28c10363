"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartnerOrganisation = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const graphql_1 = require("@nestjs/graphql");
const sequelize_1 = require("sequelize");
const short_uuid_1 = __importDefault(require("short-uuid"));
const organisation_model_1 = require("../../organisations/models/organisation.model");
const partner_organisations_args_1 = require("../args/partner-organisations.args");
let PartnerOrganisation = class PartnerOrganisation extends sequelize_typescript_1.Model {
};
exports.PartnerOrganisation = PartnerOrganisation;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], PartnerOrganisation.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    (0, sequelize_typescript_1.Column)({
        allowNull: false,
        type: sequelize_1.DataTypes.UUID,
    }),
    __metadata("design:type", String)
], PartnerOrganisation.prototype, "parentOrgId", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    (0, sequelize_typescript_1.Column)({
        allowNull: false,
        type: sequelize_1.DataTypes.UUID,
        unique: true,
    }),
    __metadata("design:type", String)
], PartnerOrganisation.prototype, "childOrgId", void 0);
__decorate([
    (0, graphql_1.Field)(() => partner_organisations_args_1.PartnerOrganisationStatus),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ENUM(...Object.values(partner_organisations_args_1.PartnerOrganisationStatus)),
        allowNull: false,
        defaultValue: partner_organisations_args_1.PartnerOrganisationStatus.Pending,
    }),
    __metadata("design:type", String)
], PartnerOrganisation.prototype, "status", void 0);
__decorate([
    (0, graphql_1.Field)(() => Date, { nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], PartnerOrganisation.prototype, "connectionApprovedDate", void 0);
__decorate([
    (0, graphql_1.Field)(() => Date, { nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], PartnerOrganisation.prototype, "disconnectionDate", void 0);
__decorate([
    (0, graphql_1.Field)(() => Date, { nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], PartnerOrganisation.prototype, "connectionRejectionDate", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 2,
    }),
    __metadata("design:type", Number)
], PartnerOrganisation.prototype, "postsLimit", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], PartnerOrganisation.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], PartnerOrganisation.prototype, "updatedAt", void 0);
__decorate([
    (0, graphql_1.Field)(() => organisation_model_1.Organisation),
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, 'parentOrgId'),
    __metadata("design:type", organisation_model_1.Organisation)
], PartnerOrganisation.prototype, "parentOrganisation", void 0);
__decorate([
    (0, graphql_1.Field)(() => organisation_model_1.Organisation),
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, 'childOrgId'),
    __metadata("design:type", organisation_model_1.Organisation)
], PartnerOrganisation.prototype, "childOrganisation", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int),
    __metadata("design:type", Number)
], PartnerOrganisation.prototype, "postsUsedThisMonth", void 0);
exports.PartnerOrganisation = PartnerOrganisation = __decorate([
    sequelize_typescript_1.Table,
    (0, graphql_1.ObjectType)()
], PartnerOrganisation);
//# sourceMappingURL=partner-organisation.model.js.map