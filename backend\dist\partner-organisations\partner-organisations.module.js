"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartnerOrganisationsModule = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const partner_organisation_model_1 = require("./models/partner-organisation.model");
const partner_organisations_service_1 = require("./partner-organisations.service");
const partner_organisations_resolver_1 = require("./partner-organisations.resolver");
const partner_organisations_repository_1 = require("./partner-organisations.repository");
const organisations_module_1 = require("../organisations/organisations.module");
const notifications_module_1 = require("../notifications/notifications.module");
const memberships_module_1 = require("../memberships/memberships.module");
const posts_module_1 = require("../posts/posts.module");
let PartnerOrganisationsModule = class PartnerOrganisationsModule {
};
exports.PartnerOrganisationsModule = PartnerOrganisationsModule;
exports.PartnerOrganisationsModule = PartnerOrganisationsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            sequelize_1.SequelizeModule.forFeature([partner_organisation_model_1.PartnerOrganisation]),
            (0, common_1.forwardRef)(() => organisations_module_1.OrganisationsModule),
            (0, common_1.forwardRef)(() => notifications_module_1.NotificationsModule),
            (0, common_1.forwardRef)(() => memberships_module_1.MembershipsModule),
            (0, common_1.forwardRef)(() => posts_module_1.PostsModule),
        ],
        providers: [
            partner_organisations_service_1.PartnerOrganisationsService,
            partner_organisations_resolver_1.PartnerOrganisationsResolver,
            partner_organisations_repository_1.PartnerOrganisationsRepository,
        ],
        exports: [partner_organisations_service_1.PartnerOrganisationsService, partner_organisations_repository_1.PartnerOrganisationsRepository],
    })
], PartnerOrganisationsModule);
//# sourceMappingURL=partner-organisations.module.js.map