"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartnerOrganisationsResolver = exports.UpdatePartnerOrganisationByOrgsInput = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const moment_1 = __importDefault(require("moment"));
const partner_organisation_model_1 = require("./models/partner-organisation.model");
const partner_organisations_service_1 = require("./partner-organisations.service");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const create_partner_organisation_dto_1 = require("./dto/create-partner-organisation.dto");
const update_partner_organisation_dto_1 = require("./dto/update-partner-organisation.dto");
const partner_organisations_filter_dto_1 = require("./dto/partner-organisations-filter.dto");
const organisation_model_1 = require("../organisations/models/organisation.model");
const organisations_service_1 = require("../organisations/organisations.service");
const partner_organisations_args_1 = require("./args/partner-organisations.args");
const posts_service_1 = require("../posts/posts.service");
let UpdatePartnerOrganisationByOrgsInput = class UpdatePartnerOrganisationByOrgsInput {
};
exports.UpdatePartnerOrganisationByOrgsInput = UpdatePartnerOrganisationByOrgsInput;
__decorate([
    (0, graphql_1.Field)(() => String),
    __metadata("design:type", String)
], UpdatePartnerOrganisationByOrgsInput.prototype, "parentOrgId", void 0);
__decorate([
    (0, graphql_1.Field)(() => String),
    __metadata("design:type", String)
], UpdatePartnerOrganisationByOrgsInput.prototype, "childOrgId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Number)
], UpdatePartnerOrganisationByOrgsInput.prototype, "postsLimit", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    __metadata("design:type", String)
], UpdatePartnerOrganisationByOrgsInput.prototype, "status", void 0);
exports.UpdatePartnerOrganisationByOrgsInput = UpdatePartnerOrganisationByOrgsInput = __decorate([
    (0, graphql_1.InputType)()
], UpdatePartnerOrganisationByOrgsInput);
let PartnerOrganisationsResolver = class PartnerOrganisationsResolver {
    constructor(partnerOrganisationsService, organisationsService, postsService, logger) {
        this.partnerOrganisationsService = partnerOrganisationsService;
        this.organisationsService = organisationsService;
        this.postsService = postsService;
        this.logger = logger;
    }
    async partnerOrganisations(currentUser, filter) {
        return this.partnerOrganisationsService.findAllPartners(filter, currentUser);
    }
    async partnerOrganisation(currentUser, id) {
        return this.partnerOrganisationsService.findById(id);
    }
    async childPartnerOrganisations(currentUser, organisationId, filter) {
        return this.partnerOrganisationsService.findByParentOrgId(organisationId, filter);
    }
    async parentPartnerOrganisation(currentUser, organisationId) {
        return this.partnerOrganisationsService.findByChildOrgId(organisationId);
    }
    async createPartnerOrganisation(currentUser, data) {
        return this.partnerOrganisationsService.createPartnership(data, currentUser);
    }
    async updatePartnerOrganisation(currentUser, data) {
        return this.partnerOrganisationsService.updatePartnership(data.id, data, currentUser);
    }
    async deletePartnerOrganisation(currentUser, id) {
        return this.partnerOrganisationsService.removePartnership(id, currentUser);
    }
    async acceptPartnerOrganisation(currentUser, id) {
        return this.partnerOrganisationsService.accept(id, currentUser);
    }
    async declinePartnerOrganisation(currentUser, id) {
        return this.partnerOrganisationsService.decline(id, currentUser);
    }
    async disconnectPartnerOrganisation(currentUser, id) {
        return this.partnerOrganisationsService.disconnect(id, currentUser);
    }
    async updatePartnerOrganisationByOrgs(currentUser, data) {
        const partnership = await this.partnerOrganisationsService.findByParentAndChildIds(data.parentOrgId, data.childOrgId);
        if (!partnership) {
            throw new common_1.NotFoundException('Partnership not found');
        }
        if (data.status) {
            const status = data.status.toLowerCase();
            if (status === 'approved') {
                return this.partnerOrganisationsService.accept(partnership.id, currentUser);
            }
            else if (status === 'rejected') {
                return this.partnerOrganisationsService.decline(partnership.id, currentUser);
            }
            else if (status === 'disconnected') {
                return this.partnerOrganisationsService.disconnect(partnership.id, currentUser);
            }
        }
        return this.partnerOrganisationsService.updatePartnership(partnership.id, {
            id: partnership.id,
            postsLimit: data.postsLimit,
        }, currentUser);
    }
    async deletePartnerOrganisationByOrgs(currentUser, parentOrgId, childOrgId) {
        const partnership = await this.partnerOrganisationsService.findByParentAndChildIds(parentOrgId, childOrgId);
        if (!partnership) {
            throw new common_1.NotFoundException('Partnership not found');
        }
        if (partnership.status === partner_organisations_args_1.PartnerOrganisationStatus.Rejected ||
            (partnership.status === partner_organisations_args_1.PartnerOrganisationStatus.Pending &&
                partnership.parentOrgId === parentOrgId)) {
            return this.partnerOrganisationsService.removePartnership(partnership.id, currentUser);
        }
        throw new common_1.NotFoundException('Partnership cannot be deleted in its current state');
    }
    async parentOrganisation(partnerOrganisation) {
        if (partnerOrganisation.parentOrganisation) {
            return partnerOrganisation.parentOrganisation;
        }
        return this.organisationsService.findById(partnerOrganisation.parentOrgId);
    }
    async childOrganisation(partnerOrganisation) {
        if (partnerOrganisation.childOrganisation) {
            return partnerOrganisation.childOrganisation;
        }
        return this.organisationsService.findById(partnerOrganisation.childOrgId);
    }
    async postsUsedThisMonth(partnerOrganisation) {
        if (partnerOrganisation.status !== partner_organisations_args_1.PartnerOrganisationStatus.Approved) {
            return 0;
        }
        const startOfMonth = (0, moment_1.default)().startOf('month').toDate();
        const endOfMonth = (0, moment_1.default)().endOf('month').toDate();
        const count = await this.postsService.countPartnerPosts({
            childOrgId: partnerOrganisation.childOrgId,
            parentOrgId: partnerOrganisation.parentOrgId,
            startDate: startOfMonth,
            endDate: endOfMonth,
        });
        return count;
    }
};
exports.PartnerOrganisationsResolver = PartnerOrganisationsResolver;
__decorate([
    (0, graphql_1.Query)(() => [partner_organisation_model_1.PartnerOrganisation]),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('filter', { nullable: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, partner_organisations_filter_dto_1.PartnerOrganisationsFilter]),
    __metadata("design:returntype", Promise)
], PartnerOrganisationsResolver.prototype, "partnerOrganisations", null);
__decorate([
    (0, graphql_1.Query)(() => partner_organisation_model_1.PartnerOrganisation, { nullable: true }),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PartnerOrganisationsResolver.prototype, "partnerOrganisation", null);
__decorate([
    (0, graphql_1.Query)(() => [partner_organisation_model_1.PartnerOrganisation]),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId')),
    __param(2, (0, graphql_1.Args)('filter', { nullable: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, partner_organisations_filter_dto_1.PartnerOrganisationsFilter]),
    __metadata("design:returntype", Promise)
], PartnerOrganisationsResolver.prototype, "childPartnerOrganisations", null);
__decorate([
    (0, graphql_1.Query)(() => partner_organisation_model_1.PartnerOrganisation, { nullable: true }),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PartnerOrganisationsResolver.prototype, "parentPartnerOrganisation", null);
__decorate([
    (0, graphql_1.Mutation)(() => partner_organisation_model_1.PartnerOrganisation),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('data')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_partner_organisation_dto_1.CreatePartnerOrganisationInput]),
    __metadata("design:returntype", Promise)
], PartnerOrganisationsResolver.prototype, "createPartnerOrganisation", null);
__decorate([
    (0, graphql_1.Mutation)(() => partner_organisation_model_1.PartnerOrganisation),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('data')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, update_partner_organisation_dto_1.UpdatePartnerOrganisationInput]),
    __metadata("design:returntype", Promise)
], PartnerOrganisationsResolver.prototype, "updatePartnerOrganisation", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PartnerOrganisationsResolver.prototype, "deletePartnerOrganisation", null);
__decorate([
    (0, graphql_1.Mutation)(() => partner_organisation_model_1.PartnerOrganisation),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PartnerOrganisationsResolver.prototype, "acceptPartnerOrganisation", null);
__decorate([
    (0, graphql_1.Mutation)(() => partner_organisation_model_1.PartnerOrganisation),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PartnerOrganisationsResolver.prototype, "declinePartnerOrganisation", null);
__decorate([
    (0, graphql_1.Mutation)(() => partner_organisation_model_1.PartnerOrganisation),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PartnerOrganisationsResolver.prototype, "disconnectPartnerOrganisation", null);
__decorate([
    (0, graphql_1.Mutation)(() => partner_organisation_model_1.PartnerOrganisation),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('data')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, UpdatePartnerOrganisationByOrgsInput]),
    __metadata("design:returntype", Promise)
], PartnerOrganisationsResolver.prototype, "updatePartnerOrganisationByOrgs", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('parentOrgId', { type: () => String })),
    __param(2, (0, graphql_1.Args)('childOrgId', { type: () => String })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], PartnerOrganisationsResolver.prototype, "deletePartnerOrganisationByOrgs", null);
__decorate([
    (0, graphql_1.ResolveField)(() => organisation_model_1.Organisation),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [partner_organisation_model_1.PartnerOrganisation]),
    __metadata("design:returntype", Promise)
], PartnerOrganisationsResolver.prototype, "parentOrganisation", null);
__decorate([
    (0, graphql_1.ResolveField)(() => organisation_model_1.Organisation),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [partner_organisation_model_1.PartnerOrganisation]),
    __metadata("design:returntype", Promise)
], PartnerOrganisationsResolver.prototype, "childOrganisation", null);
__decorate([
    (0, graphql_1.ResolveField)(() => graphql_1.Int),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [partner_organisation_model_1.PartnerOrganisation]),
    __metadata("design:returntype", Promise)
], PartnerOrganisationsResolver.prototype, "postsUsedThisMonth", null);
exports.PartnerOrganisationsResolver = PartnerOrganisationsResolver = __decorate([
    (0, graphql_1.Resolver)(() => partner_organisation_model_1.PartnerOrganisation),
    __param(0, (0, common_1.Inject)((0, common_1.forwardRef)(() => partner_organisations_service_1.PartnerOrganisationsService))),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService))),
    __param(2, (0, common_1.Inject)((0, common_1.forwardRef)(() => posts_service_1.PostsService))),
    __param(3, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [partner_organisations_service_1.PartnerOrganisationsService,
        organisations_service_1.OrganisationsService,
        posts_service_1.PostsService,
        winston_1.Logger])
], PartnerOrganisationsResolver);
//# sourceMappingURL=partner-organisations.resolver.js.map