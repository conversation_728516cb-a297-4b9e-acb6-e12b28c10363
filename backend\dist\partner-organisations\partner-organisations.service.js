"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartnerOrganisationsService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const sequelize_typescript_1 = require("sequelize-typescript");
const sequelize_1 = require("sequelize");
const partner_organisation_model_1 = require("./models/partner-organisation.model");
const partner_organisations_args_1 = require("./args/partner-organisations.args");
const organisations_service_1 = require("../organisations/organisations.service");
const notifications_service_1 = require("../notifications/notifications.service");
const notifications_args_1 = require("../notifications/args/notifications.args");
const partner_organisations_repository_1 = require("./partner-organisations.repository");
const base_service_1 = require("../common/base.service");
const memberships_service_1 = require("../memberships/memberships.service");
const membership_model_1 = require("../memberships/models/membership.model");
const notifications_args_2 = require("../notifications/args/notifications.args");
const organisations_args_1 = require("../organisations/args/organisations.args");
let PartnerOrganisationsService = class PartnerOrganisationsService extends (0, base_service_1.BaseService)(partner_organisation_model_1.PartnerOrganisation) {
    constructor(partnerOrganisationsRepository, organisationsService, notificationsService, membershipsService, logger, sequelize) {
        super();
        this.partnerOrganisationsRepository = partnerOrganisationsRepository;
        this.organisationsService = organisationsService;
        this.notificationsService = notificationsService;
        this.membershipsService = membershipsService;
        this.logger = logger;
        this.sequelize = sequelize;
    }
    async createPartnership(data, currentUser) {
        const parentOrg = await this.organisationsService.findOne({
            id: data.parentOrgId,
        });
        if (!parentOrg) {
            throw new common_1.NotFoundException('Parent organisation not found');
        }
        const childOrg = await this.organisationsService.findOne({
            id: data.childOrgId,
        });
        if (!childOrg) {
            throw new common_1.NotFoundException('Child organisation not found');
        }
        if (childOrg.hasClubHabloSubscription === true) {
            throw new common_1.BadRequestException('Can not create partnership with a paid (Hablo subscription) organization');
        }
        const membership = await this.membershipsService.findOne({
            organisationId: data.parentOrgId,
            profileId: currentUser.profileId,
            status: membership_model_1.MembershipStatus.Active,
        });
        if (!membership) {
            throw new common_1.BadRequestException('You do not have permission to create this partnership request');
        }
        const hasAdminPermission = membership.permissions.some(permission => [
            membership_model_1.MembershipPermission.Owner,
            membership_model_1.MembershipPermission.Admin,
            membership_model_1.MembershipPermission.HiddenAdmin,
        ].includes(permission));
        if (!hasAdminPermission) {
            throw new common_1.BadRequestException('Only users with Admin permission can create partner requests');
        }
        if (parentOrg.type !== organisations_args_1.OrganisationType.Destination) {
            throw new common_1.BadRequestException('Only Destination organizations can create partnerships');
        }
        if (!parentOrg.isDMOSubscription) {
            throw new common_1.BadRequestException('Parent organization must have a DMO subscription to create partnerships');
        }
        if (childOrg.type !== organisations_args_1.OrganisationType.PrivateSector &&
            childOrg.type !== organisations_args_1.OrganisationType.Destination) {
            throw new common_1.BadRequestException('Child organization must be of type Private Sector or Destination');
        }
        const existingPartnership = await this.findByParentAndChildIds(data.parentOrgId, data.childOrgId);
        if (existingPartnership) {
            if (existingPartnership.status === partner_organisations_args_1.PartnerOrganisationStatus.Pending) {
                throw new common_1.BadRequestException('A pending partnership request already exists between these organizations');
            }
            if (existingPartnership.status === partner_organisations_args_1.PartnerOrganisationStatus.Approved) {
                throw new common_1.BadRequestException('These organizations are already partners');
            }
        }
        const existingPartner = await this.findByChildOrgId(data.childOrgId);
        if (existingPartner &&
            existingPartner.status === partner_organisations_args_1.PartnerOrganisationStatus.Approved &&
            existingPartner.parentOrgId !== data.parentOrgId) {
            throw new common_1.BadRequestException('Child organisation is already a partner of another organisation');
        }
        await this.validatePartnerLimit(data.parentOrgId);
        const partnerOrganisation = await this.create({
            parentOrgId: data.parentOrgId,
            childOrgId: data.childOrgId,
            postsLimit: data.postsLimit || 2,
            status: partner_organisations_args_1.PartnerOrganisationStatus.Pending,
        });
        const childOrgMemberships = await this.membershipsService.findAll({
            organisationId: data.childOrgId,
            permissions: {
                [sequelize_1.Op.overlap]: [
                    membership_model_1.MembershipPermission.Owner,
                    membership_model_1.MembershipPermission.Admin,
                    membership_model_1.MembershipPermission.HiddenAdmin,
                ],
            },
            status: membership_model_1.MembershipStatus.Active,
        });
        const transaction = await this.sequelize.transaction();
        try {
            for (const membership of childOrgMemberships) {
                await this.notificationsService.createNotification({
                    ownerProfileId: membership.profileId,
                    profileId: currentUser.profileId,
                    organisationId: data.childOrgId,
                    type: notifications_args_1.NotificationType.OrgPartnerRequestReceived,
                    data: {
                        partnerOrganisationId: partnerOrganisation.id,
                        senderOrgId: data.parentOrgId,
                        senderOrgName: parentOrg.name,
                        receiverOrgId: data.childOrgId,
                        receiverOrgName: childOrg.name,
                    },
                }, {
                    transaction,
                });
            }
            await transaction.commit();
            const profileIds = childOrgMemberships.map(membership => membership.profileId);
            const replacements = [parentOrg.name, childOrg.name];
            await this.notificationsService.sendPushNotification({
                profileIds,
                replacements,
                messageType: notifications_args_2.NotificationMessage.OrgPartnerRequestReceived,
            });
        }
        catch (e) {
            await transaction.rollback();
            this.logger.error('Error creating notifications for partnership request', e);
        }
        return partnerOrganisation;
    }
    async findAllPartners(filter, currentUser) {
        var _a;
        const where = {};
        if ((_a = filter === null || filter === void 0 ? void 0 : filter.status) === null || _a === void 0 ? void 0 : _a.length) {
            where.status = { [sequelize_1.Op.in]: filter.status };
        }
        let organisationId = (filter === null || filter === void 0 ? void 0 : filter.organisationId) || null;
        if (!organisationId && currentUser) {
            const membership = await this.membershipsService.findOne({
                profileId: currentUser.profileId,
                status: membership_model_1.MembershipStatus.Active,
            });
            if (membership) {
                organisationId = membership.organisationId;
            }
        }
        if (organisationId) {
            where[sequelize_1.Op.or] = [
                { parentOrgId: organisationId },
                { childOrgId: organisationId }
            ];
        }
        return this.findAll(where, {
            includeParams: ['parentOrganisation', 'childOrganisation'],
        });
    }
    async findByParentOrgId(parentOrgId, filter) {
        var _a;
        const where = { parentOrgId };
        if ((_a = filter === null || filter === void 0 ? void 0 : filter.status) === null || _a === void 0 ? void 0 : _a.length) {
            where.status = { [sequelize_1.Op.in]: filter.status };
        }
        return this.findAll(where, {
            includeParams: ['parentOrganisation', 'childOrganisation'],
        });
    }
    async findByChildOrgId(childOrgId) {
        const where = {
            childOrgId,
            status: {
                [sequelize_1.Op.in]: [
                    partner_organisations_args_1.PartnerOrganisationStatus.Pending,
                    partner_organisations_args_1.PartnerOrganisationStatus.Approved,
                ],
            },
        };
        return this.findOne(where, {
            includeParams: ['parentOrganisation', 'childOrganisation'],
        });
    }
    async updatePartnership(id, data, currentUser) {
        const partnerOrganisation = await this.findById(id, {
            includeParams: ['parentOrganisation', 'childOrganisation'],
        });
        if (!partnerOrganisation) {
            throw new common_1.NotFoundException('Partner organisation not found');
        }
        return this.updateById(id, data);
    }
    async removePartnership(id, currentUser) {
        const result = await this.removeById(id);
        return result > 0;
    }
    async accept(id, currentUser) {
        var _a;
        const partnerOrganisation = await this.findById(id, {
            includeParams: ['parentOrganisation', 'childOrganisation'],
        });
        if (!partnerOrganisation) {
            throw new common_1.NotFoundException('Partner organisation not found');
        }
        if (partnerOrganisation.status !== partner_organisations_args_1.PartnerOrganisationStatus.Pending) {
            throw new common_1.BadRequestException('Partner organisation is not in pending status');
        }
        const membership = await this.membershipsService.findOne({
            organisationId: partnerOrganisation.childOrgId,
            profileId: currentUser.profileId,
            status: membership_model_1.MembershipStatus.Active,
        });
        if (!membership) {
            throw new common_1.BadRequestException('You do not have permission to accept this partnership request');
        }
        const hasAdminPermission = membership.permissions.some(permission => [
            membership_model_1.MembershipPermission.Owner,
            membership_model_1.MembershipPermission.Admin,
            membership_model_1.MembershipPermission.HiddenAdmin,
        ].includes(permission));
        if (!hasAdminPermission) {
            throw new common_1.BadRequestException('Only users with Admin permission can accept partner requests');
        }
        const transaction = await this.sequelize.transaction();
        try {
            const childOrg = await this.organisationsService.findById(partnerOrganisation.childOrgId, { transaction });
            if (((_a = childOrg === null || childOrg === void 0 ? void 0 : childOrg.connectedOrganisations) === null || _a === void 0 ? void 0 : _a.length) > 0) {
                this.logger.info('Disconnecting connected organizations from child organization', {
                    childOrgId: partnerOrganisation.childOrgId,
                    connectedOrganisations: childOrg.connectedOrganisations,
                });
                for (const connectedOrgId of childOrg.connectedOrganisations) {
                    try {
                        const partnershipRequests = (await this.sequelize.query(`SELECT pr.id FROM "PartnershipRequests" pr 
               WHERE ((pr."senderOrganisationId" = :childOrgId AND pr."receiverOrganisationId" = :connectedOrgId) 
               OR (pr."senderOrganisationId" = :connectedOrgId AND pr."receiverOrganisationId" = :childOrgId))
               AND pr."status" IN ('Approved', 'Pending')`, {
                            replacements: {
                                childOrgId: partnerOrganisation.childOrgId,
                                connectedOrgId: connectedOrgId,
                            },
                            type: sequelize_1.QueryTypes.SELECT,
                            transaction,
                        }));
                        if (partnershipRequests.length === 0)
                            continue;
                        for (const request of partnershipRequests) {
                            await this.sequelize.query(`UPDATE "PartnershipRequests" 
                 SET "status" = 'Disconnected', 
                     "updatedAt" = NOW()
                 WHERE "id" = :requestId`, {
                                replacements: { requestId: request.id },
                                type: sequelize_1.QueryTypes.UPDATE,
                                transaction,
                            });
                            this.logger.info(`Updated partnership request ${request.id} status to Disconnected between ${partnerOrganisation.childOrgId} and ${connectedOrgId}`, {
                                requestId: request.id,
                                childOrgId: partnerOrganisation.childOrgId,
                                connectedOrgId,
                            });
                        }
                    }
                    catch (error) {
                        this.logger.error(`Error disconnecting organization ${connectedOrgId} from child organization ${partnerOrganisation.childOrgId}`, error);
                    }
                }
                await this.organisationsService.updateById(partnerOrganisation.childOrgId, {
                    connectedOrganisations: [],
                }, { transaction });
            }
            const updatedPartnerOrg = await this.updateById(id, {
                status: partner_organisations_args_1.PartnerOrganisationStatus.Approved,
                connectionApprovedDate: new Date(),
            }, { transaction });
            await this.sequelize.query(`UPDATE "Organisations" SET "isPublic" = true, "privacy" = 'public' WHERE "id" = :childOrgId`, {
                replacements: { childOrgId: partnerOrganisation.childOrgId },
                transaction,
            });
            try {
                const streamOrgService = this.organisationsService['streamOrgService'];
                if (streamOrgService) {
                    await streamOrgService.updateOrganisation(partnerOrganisation.childOrgId, {
                        isPublic: true,
                        privacy: 'public',
                    }, {});
                    try {
                        const client = streamOrgService.client;
                        if (client) {
                            await client
                                .feed('organisations', partnerOrganisation.parentOrgId)
                                .follow('organisations', partnerOrganisation.childOrgId);
                            this.logger.info(`Parent organization feed now following child organization feed`, {
                                parentOrgId: partnerOrganisation.parentOrgId,
                                childOrgId: partnerOrganisation.childOrgId,
                            });
                        }
                    }
                    catch (followError) {
                        this.logger.error(`Error making parent org feed follow child org feed`, followError);
                    }
                }
            }
            catch (streamError) {
                this.logger.error('Error updating Stream organization', streamError);
            }
            const otherPendingPartnerships = await this.findAll({
                childOrgId: partnerOrganisation.childOrgId,
                status: partner_organisations_args_1.PartnerOrganisationStatus.Pending,
                id: { [sequelize_1.Op.ne]: id },
            });
            for (const pendingPartnership of otherPendingPartnerships) {
                await this.updateById(pendingPartnership.id, {
                    status: partner_organisations_args_1.PartnerOrganisationStatus.Rejected,
                    connectionRejectionDate: new Date(),
                }, { transaction });
            }
            const parentOrgMemberships = await this.membershipsService.findAll({
                organisationId: partnerOrganisation.parentOrgId,
                permissions: {
                    [sequelize_1.Op.overlap]: [
                        membership_model_1.MembershipPermission.Owner,
                        membership_model_1.MembershipPermission.Admin,
                        membership_model_1.MembershipPermission.HiddenAdmin,
                    ],
                },
                status: membership_model_1.MembershipStatus.Active,
            });
            const childOrgMemberships = await this.membershipsService.findAll({
                organisationId: partnerOrganisation.childOrgId,
                permissions: {
                    [sequelize_1.Op.overlap]: [
                        membership_model_1.MembershipPermission.Owner,
                        membership_model_1.MembershipPermission.Admin,
                        membership_model_1.MembershipPermission.HiddenAdmin,
                    ],
                },
                status: membership_model_1.MembershipStatus.Active,
            });
            for (const membership of parentOrgMemberships) {
                await this.notificationsService.createNotification({
                    ownerProfileId: membership.profileId,
                    profileId: currentUser.profileId,
                    organisationId: partnerOrganisation.parentOrgId,
                    type: notifications_args_1.NotificationType.OrgPartnerAcceptedSender,
                    data: {
                        partnerOrganisationId: partnerOrganisation.id,
                        senderOrgId: partnerOrganisation.parentOrgId,
                        senderOrgName: partnerOrganisation.parentOrganisation.name,
                        receiverOrgId: partnerOrganisation.childOrgId,
                        receiverOrgName: partnerOrganisation.childOrganisation.name,
                    },
                }, {
                    transaction,
                });
            }
            for (const membership of childOrgMemberships) {
                await this.notificationsService.createNotification({
                    ownerProfileId: membership.profileId,
                    profileId: currentUser.profileId,
                    organisationId: partnerOrganisation.childOrgId,
                    type: notifications_args_1.NotificationType.OrgPartnerAcceptedReceiver,
                    data: {
                        partnerOrganisationId: partnerOrganisation.id,
                        senderOrgId: partnerOrganisation.parentOrgId,
                        senderOrgName: partnerOrganisation.parentOrganisation.name,
                        receiverOrgId: partnerOrganisation.childOrgId,
                        receiverOrgName: partnerOrganisation.childOrganisation.name,
                    },
                }, {
                    transaction,
                });
            }
            for (const pendingPartnership of otherPendingPartnerships) {
                const rejectedParentOrg = await this.organisationsService.findOne({
                    id: pendingPartnership.parentOrgId,
                }, { transaction });
                if (rejectedParentOrg) {
                    const rejectedParentOrgMemberships = await this.membershipsService.findAll({
                        organisationId: pendingPartnership.parentOrgId,
                        permissions: {
                            [sequelize_1.Op.overlap]: [
                                membership_model_1.MembershipPermission.Owner,
                                membership_model_1.MembershipPermission.Admin,
                                membership_model_1.MembershipPermission.HiddenAdmin,
                            ],
                        },
                        status: membership_model_1.MembershipStatus.Active,
                    }, { transaction });
                    for (const membership of rejectedParentOrgMemberships) {
                        await this.notificationsService.createNotification({
                            ownerProfileId: membership.profileId,
                            profileId: currentUser.profileId,
                            organisationId: pendingPartnership.parentOrgId,
                            type: notifications_args_1.NotificationType.OrgPartnerRequestRejected,
                            data: {
                                partnerOrganisationId: pendingPartnership.id,
                                senderOrgId: pendingPartnership.parentOrgId,
                                senderOrgName: rejectedParentOrg.name,
                                receiverOrgId: partnerOrganisation.childOrgId,
                                receiverOrgName: partnerOrganisation.childOrganisation.name,
                            },
                        }, {
                            transaction,
                        });
                    }
                    const rejectedProfileIds = rejectedParentOrgMemberships.map(membership => membership.profileId);
                    if (rejectedProfileIds.length > 0) {
                        const rejectedReplacements = [
                            partnerOrganisation.childOrganisation.name,
                        ];
                        await this.notificationsService.sendPushNotification({
                            profileIds: rejectedProfileIds,
                            replacements: rejectedReplacements,
                            messageType: notifications_args_2.NotificationMessage.OrgPartnerRequestRejected,
                        });
                    }
                }
            }
            await transaction.commit();
            const parentProfileIds = parentOrgMemberships.map(membership => membership.profileId);
            const parentReplacements = [partnerOrganisation.childOrganisation.name];
            await this.notificationsService.sendPushNotification({
                profileIds: parentProfileIds,
                replacements: parentReplacements,
                messageType: notifications_args_2.NotificationMessage.OrgPartnerAcceptedSender,
            });
            const childProfileIds = childOrgMemberships.map(membership => membership.profileId);
            const childReplacements = [
                partnerOrganisation.parentOrganisation.name,
                partnerOrganisation.childOrganisation.name,
            ];
            await this.notificationsService.sendPushNotification({
                profileIds: childProfileIds,
                replacements: childReplacements,
                messageType: notifications_args_2.NotificationMessage.OrgPartnerAcceptedReceiver,
            });
            return updatedPartnerOrg;
        }
        catch (e) {
            await transaction.rollback();
            this.logger.error('Error processing partnership acceptance', e);
        }
    }
    async decline(id, currentUser) {
        const partnerOrganisation = await this.findById(id, {
            includeParams: ['parentOrganisation', 'childOrganisation'],
        });
        if (!partnerOrganisation) {
            throw new common_1.NotFoundException('Partner organisation not found');
        }
        if (partnerOrganisation.status !== partner_organisations_args_1.PartnerOrganisationStatus.Pending) {
            throw new common_1.BadRequestException('Partner organisation is not in pending status');
        }
        const membership = await this.membershipsService.findOne({
            organisationId: partnerOrganisation.childOrgId,
            profileId: currentUser.profileId,
            status: membership_model_1.MembershipStatus.Active,
        });
        if (!membership) {
            throw new common_1.BadRequestException('You do not have permission to decline this partnership request');
        }
        const hasAdminPermission = membership.permissions.some(permission => [
            membership_model_1.MembershipPermission.Owner,
            membership_model_1.MembershipPermission.Admin,
            membership_model_1.MembershipPermission.HiddenAdmin,
        ].includes(permission));
        if (!hasAdminPermission) {
            throw new common_1.BadRequestException('Only users with Admin permission can decline partner requests');
        }
        return this.updateById(id, {
            status: partner_organisations_args_1.PartnerOrganisationStatus.Rejected,
            connectionRejectionDate: new Date(),
        });
    }
    async disconnect(id, currentUser) {
        const partnerOrganisation = await this.findById(id, {
            includeParams: ['parentOrganisation', 'childOrganisation'],
        });
        if (!partnerOrganisation) {
            throw new common_1.NotFoundException('Partner organisation not found');
        }
        if (partnerOrganisation.status !== partner_organisations_args_1.PartnerOrganisationStatus.Approved) {
            throw new common_1.BadRequestException('Partner organisation is not in approved status');
        }
        const parentMembership = await this.membershipsService.findOne({
            organisationId: partnerOrganisation.parentOrgId,
            profileId: currentUser.profileId,
            status: membership_model_1.MembershipStatus.Active,
        });
        const childMembership = await this.membershipsService.findOne({
            organisationId: partnerOrganisation.childOrgId,
            profileId: currentUser.profileId,
            status: membership_model_1.MembershipStatus.Active,
        });
        const hasParentAdminPermission = parentMembership &&
            parentMembership.permissions.some(permission => [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
            ].includes(permission));
        const hasChildAdminPermission = childMembership &&
            childMembership.permissions.some(permission => [
                membership_model_1.MembershipPermission.Owner,
                membership_model_1.MembershipPermission.Admin,
                membership_model_1.MembershipPermission.HiddenAdmin,
            ].includes(permission));
        if (!hasParentAdminPermission && !hasChildAdminPermission) {
            throw new common_1.BadRequestException('Only users with Admin permission can disconnect partner organizations');
        }
        return this.updateById(id, {
            status: partner_organisations_args_1.PartnerOrganisationStatus.Disconnected,
            disconnectionDate: new Date(),
        });
    }
    async validatePartnerLimit(parentOrgId) {
        const parentOrg = await this.organisationsService.findOne({
            id: parentOrgId,
        });
        if (!parentOrg) {
            throw new common_1.NotFoundException('Parent organisation not found');
        }
        const maxAllowedPartners = parentOrg.DMOMaxPartners || 10;
        const existingPartners = await this.findAll({
            parentOrgId,
            status: {
                [sequelize_1.Op.in]: [
                    partner_organisations_args_1.PartnerOrganisationStatus.Pending,
                    partner_organisations_args_1.PartnerOrganisationStatus.Approved,
                ],
            },
        });
        if (existingPartners.length >= maxAllowedPartners) {
            throw new common_1.BadRequestException(`Maximum number of partners (${maxAllowedPartners}) reached for this organisation. Cannot create new partnership.`);
        }
    }
    async findByParentAndChildIds(parentOrgId, childOrgId) {
        return this.findOne({
            parentOrgId,
            childOrgId,
        }, {
            includeParams: ['parentOrganisation', 'childOrganisation'],
            order: [['updatedAt', 'DESC']],
        });
    }
};
exports.PartnerOrganisationsService = PartnerOrganisationsService;
exports.PartnerOrganisationsService = PartnerOrganisationsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)((0, common_1.forwardRef)(() => partner_organisations_repository_1.PartnerOrganisationsRepository))),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService))),
    __param(2, (0, common_1.Inject)((0, common_1.forwardRef)(() => notifications_service_1.NotificationsService))),
    __param(3, (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService))),
    __param(4, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [partner_organisations_repository_1.PartnerOrganisationsRepository,
        organisations_service_1.OrganisationsService,
        notifications_service_1.NotificationsService,
        memberships_service_1.MembershipsService,
        winston_1.Logger,
        sequelize_typescript_1.Sequelize])
], PartnerOrganisationsService);
//# sourceMappingURL=partner-organisations.service.js.map