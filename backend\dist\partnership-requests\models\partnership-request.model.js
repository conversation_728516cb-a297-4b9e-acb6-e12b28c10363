"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartnershipRequest = exports.PartnershipRequestStatus = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const notification_model_1 = require("../../notifications/models/notification.model");
const short_uuid_1 = __importDefault(require("short-uuid"));
const graphql_1 = require("@nestjs/graphql");
const organisation_model_1 = require("../../organisations/models/organisation.model");
const profile_model_1 = require("../../profiles/models/profile.model");
const sequelize_1 = require("sequelize");
const subscription_model_1 = require("../../subscriptions/models/subscription.model");
const graphql_type_json_1 = __importDefault(require("graphql-type-json"));
var PartnershipRequestStatus;
(function (PartnershipRequestStatus) {
    PartnershipRequestStatus["Pending"] = "Pending";
    PartnershipRequestStatus["Approved"] = "Approved";
    PartnershipRequestStatus["Declined"] = "Declined";
    PartnershipRequestStatus["Disconnected"] = "Disconnected";
})(PartnershipRequestStatus || (exports.PartnershipRequestStatus = PartnershipRequestStatus = {}));
(0, graphql_1.registerEnumType)(PartnershipRequestStatus, {
    name: 'PartnershipRequestStatus',
});
let PartnershipRequest = class PartnershipRequest extends sequelize_typescript_1.Model {
};
exports.PartnershipRequest = PartnershipRequest;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], PartnershipRequest.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(() => PartnershipRequestStatus, { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
        defaultValue: PartnershipRequestStatus.Pending,
    }),
    __metadata("design:type", String)
], PartnershipRequest.prototype, "status", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    (0, sequelize_typescript_1.Column)({ allowNull: false }),
    __metadata("design:type", String)
], PartnershipRequest.prototype, "senderProfileId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, 'senderProfileId'),
    __metadata("design:type", profile_model_1.Profile)
], PartnershipRequest.prototype, "senderProfile", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    (0, sequelize_typescript_1.Column)({ allowNull: false }),
    __metadata("design:type", String)
], PartnershipRequest.prototype, "senderOrganisationId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, 'senderOrganisationId'),
    __metadata("design:type", organisation_model_1.Organisation)
], PartnershipRequest.prototype, "senderOrganisation", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    (0, sequelize_typescript_1.Column)({ allowNull: false }),
    __metadata("design:type", String)
], PartnershipRequest.prototype, "receiverOrganisationId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, 'receiverOrganisationId'),
    __metadata("design:type", organisation_model_1.Organisation)
], PartnershipRequest.prototype, "receiverOrganisation", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    (0, sequelize_typescript_1.Column)({ allowNull: true }),
    __metadata("design:type", String)
], PartnershipRequest.prototype, "receiverProfileId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, 'receiverProfileId'),
    __metadata("design:type", profile_model_1.Profile)
], PartnershipRequest.prototype, "receiverProfile", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => subscription_model_1.Subscription),
    (0, sequelize_typescript_1.Column)({ allowNull: true }),
    __metadata("design:type", String)
], PartnershipRequest.prototype, "subscriptionId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => subscription_model_1.Subscription, {
        foreignKey: 'subscriptionId',
        onDelete: 'SET NULL',
    }),
    __metadata("design:type", subscription_model_1.Subscription)
], PartnershipRequest.prototype, "subscription", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_type_json_1.default, { nullable: true }),
    __metadata("design:type", Object)
], PartnershipRequest.prototype, "subscriptionData", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], PartnershipRequest.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], PartnershipRequest.prototype, "updatedAt", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], PartnershipRequest.prototype, "price", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], PartnershipRequest.prototype, "duration", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], PartnershipRequest.prototype, "currency", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], PartnershipRequest.prototype, "isSubscription", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => notification_model_1.Notification, {
        foreignKey: 'membershipId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", Array)
], PartnershipRequest.prototype, "notifications", void 0);
exports.PartnershipRequest = PartnershipRequest = __decorate([
    (0, graphql_1.ObjectType)(),
    sequelize_typescript_1.Table
], PartnershipRequest);
//# sourceMappingURL=partnership-request.model.js.map