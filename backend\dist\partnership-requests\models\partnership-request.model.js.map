{"version": 3, "file": "partnership-request.model.js", "sourceRoot": "", "sources": ["../../../src/partnership-requests/models/partnership-request.model.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,+DAO8B;AAC9B,sFAA6E;AAC7E,4DAA+B;AAC/B,6CAA0E;AAC1E,sFAA6E;AAC7E,uEAA8D;AAC9D,yCAAsC;AACtC,sFAA6E;AAC7E,0EAA4C;AAE5C,IAAY,wBAKX;AALD,WAAY,wBAAwB;IAClC,+CAAmB,CAAA;IACnB,iDAAqB,CAAA;IACrB,iDAAqB,CAAA;IACrB,yDAA6B,CAAA;AAC/B,CAAC,EALW,wBAAwB,wCAAxB,wBAAwB,QAKnC;AAED,IAAA,0BAAgB,EAAC,wBAAwB,EAAE;IACzC,IAAI,EAAE,0BAA0B;CACjC,CAAC,CAAC;AAiBI,IAAM,kBAAkB,GAAxB,MAAM,kBAAmB,SAAQ,4BAAyB;CAmFhE,CAAA;AAnFY,gDAAkB;AAQ7B;IAPC,IAAA,eAAK,EAAC,GAAG,EAAE,CAAC,YAAE,CAAC;IACf,IAAA,6BAAM,EAAC;QACN,UAAU,EAAE,IAAI;QAChB,MAAM,EAAE,IAAI;QACZ,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,oBAAK,CAAC,QAAQ;KAC7B,CAAC;;8CACS;AAOX;IALC,IAAA,eAAK,EAAC,GAAG,EAAE,CAAC,wBAAwB,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC1D,IAAA,6BAAM,EAAC;QACN,IAAI,EAAE,qBAAS,CAAC,MAAM;QACtB,YAAY,EAAE,wBAAwB,CAAC,OAAO;KAC/C,CAAC;;kDAC+B;AAIjC;IAFC,IAAA,iCAAU,EAAC,GAAG,EAAE,CAAC,uBAAO,CAAC;IACzB,IAAA,6BAAM,EAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;;2DACL;AAGxB;IADC,IAAA,gCAAS,EAAC,GAAG,EAAE,CAAC,uBAAO,EAAE,iBAAiB,CAAC;8BAC7B,uBAAO;yDAAC;AAIvB;IAFC,IAAA,iCAAU,EAAC,GAAG,EAAE,CAAC,iCAAY,CAAC;IAC9B,IAAA,6BAAM,EAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;;gEACA;AAG7B;IADC,IAAA,gCAAS,EAAC,GAAG,EAAE,CAAC,iCAAY,EAAE,sBAAsB,CAAC;8BAClC,iCAAY;8DAAC;AAIjC;IAFC,IAAA,iCAAU,EAAC,GAAG,EAAE,CAAC,iCAAY,CAAC;IAC9B,IAAA,6BAAM,EAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;;kEACE;AAG/B;IADC,IAAA,gCAAS,EAAC,GAAG,EAAE,CAAC,iCAAY,EAAE,wBAAwB,CAAC;8BAClC,iCAAY;gEAAC;AAInC;IAFC,IAAA,iCAAU,EAAC,GAAG,EAAE,CAAC,uBAAO,CAAC;IACzB,IAAA,6BAAM,EAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;;6DACF;AAG1B;IADC,IAAA,gCAAS,EAAC,GAAG,EAAE,CAAC,uBAAO,EAAE,mBAAmB,CAAE;8BAC9B,uBAAO;2DAAC;AAIzB;IAFC,IAAA,iCAAU,EAAC,GAAG,EAAE,CAAC,iCAAY,CAAC;IAC9B,IAAA,6BAAM,EAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;;0DACL;AAMvB;IAJC,IAAA,gCAAS,EAAC,GAAG,EAAE,CAAC,iCAAY,EAAE;QAC7B,UAAU,EAAE,gBAAgB;QAC5B,QAAQ,EAAE,UAAU;KACrB,CAAC;8BACY,iCAAY;wDAAC;AAG3B;IADC,IAAA,eAAK,EAAC,GAAG,EAAE,CAAC,2BAAW,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4DACJ;AAIzC;IAFC,IAAA,eAAK,GAAE;IACP,6BAAM;8BACI,IAAI;qDAAC;AAIhB;IAFC,IAAA,eAAK,GAAE;IACP,6BAAM;8BACI,IAAI;qDAAC;AAGhB;IADC,IAAA,eAAK,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BACnB,MAAM;iDAAC;AAGd;IADC,IAAA,eAAK,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BAChB,MAAM;oDAAC;AAGjB;IADC,IAAA,eAAK,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BAChB,MAAM;oDAAC;AAGjB;IADC,IAAA,eAAK,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;8BACV,OAAO;0DAAC;AAMxB;IAJC,IAAA,8BAAO,EAAC,GAAG,EAAE,CAAC,iCAAY,EAAE;QAC3B,UAAU,EAAE,cAAc;QAC1B,QAAQ,EAAE,SAAS;KACpB,CAAC;;yDAC4B;6BAlFnB,kBAAkB;IAF9B,IAAA,oBAAU,GAAE;IACZ,4BAAK;GACO,kBAAkB,CAmF9B"}