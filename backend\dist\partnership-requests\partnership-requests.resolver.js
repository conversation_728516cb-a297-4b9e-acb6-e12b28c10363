"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartnershipRequestsResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const partnership_request_model_1 = require("./models/partnership-request.model");
const partnership_request_model_2 = require("./models/partnership-request.model");
const partnership_requests_service_1 = require("./partnership-requests.service");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const organisation_model_1 = require("../organisations/models/organisation.model");
const organisations_service_1 = require("../organisations/organisations.service");
const create_partnership_request_dto_1 = require("./dto/create-partnership-request.dto");
const subscriptions_service_1 = require("../subscriptions/subscriptions.service");
let PartnershipRequestsResolver = class PartnershipRequestsResolver {
    async createPartnershipRequest(user, partnershipRequestData) {
        this.logger.verbose('PartnershipRequestsResolver.createPartnershipRequest (mutation)', {
            user: user.toLogObject(),
            partnershipRequestData,
        });
        await this.partnershipRequestsService.createPartnershipRequest({
            senderProfileId: user.profileId,
            senderOrganisationId: partnershipRequestData.senderOrganisationId,
            receiverOrganisationId: partnershipRequestData.receiverOrganisationId,
            status: partnership_request_model_1.PartnershipRequestStatus.Pending,
            isSubscription: partnershipRequestData.isSubscription,
            price: partnershipRequestData.price ? partnershipRequestData.price : '',
            currency: partnershipRequestData.currency
                ? partnershipRequestData.currency
                : '',
            duration: partnershipRequestData.duration
                ? partnershipRequestData.duration
                : '',
        }, {
            currentUser: user,
        });
        return true;
    }
    async initiatePayment(user, partnershipRequestId) {
        this.logger.verbose('PartnershipRequestsResolver.initiatePayment (mutation)', {
            user: user.toLogObject(),
            partnershipRequestId,
        });
        return this.partnershipRequestsService.initiatePayment(partnershipRequestId, {
            currentUser: user,
        });
    }
    async retryPayment(user, partnershipRequestId) {
        this.logger.verbose('PartnershipRequestsResolver.retryPayment (mutation)', {
            user: user.toLogObject(),
            partnershipRequestId,
        });
        return this.partnershipRequestsService.retryPayment(partnershipRequestId, {
            currentUser: user,
        });
    }
    approvePartnershipRequest(user, partnershipRequestId) {
        this.logger.verbose('PartnershipRequestsResolver.acceptPartnershipRequest (mutation)', {
            user: user.toLogObject(),
            partnershipRequestId,
        });
        return this.partnershipRequestsService.approve(partnershipRequestId, {
            currentUser: user,
        });
    }
    async declinePartnershipRequest(user, partnershipRequestId) {
        this.logger.verbose('PartnershipRequestsResolver.declinePartnershipRequest (mutation)', {
            user: user.toLogObject(),
            partnershipRequestId,
        });
        return this.partnershipRequestsService.decline(partnershipRequestId, {
            currentUser: user,
        });
    }
    async removePartnershipRequest(user, partnershipRequestId) {
        this.logger.verbose('PartnershipRequestsResolver.removePartnershipRequest (mutation)', {
            user: user.toLogObject(),
            partnershipRequestId,
        });
        await this.partnershipRequestsService.removePartnershipRequest(partnershipRequestId, {
            currentUser: user,
        });
        return true;
    }
    async senderOrganisation(partnershipRequest) {
        if (partnershipRequest.senderOrganisation)
            return partnershipRequest.senderOrganisation;
        this.logger.verbose('PartnershipRequestsResolver.organisation (field resolver)', {
            partnershipRequest,
        });
        if (!partnershipRequest.senderOrganisationId)
            return null;
        return this.organisationsService.findById(partnershipRequest.senderOrganisationId, {
            useCache: true,
        });
    }
    async receiverOrganisation(partnershipRequest) {
        if (partnershipRequest.receiverOrganisation)
            return partnershipRequest.receiverOrganisation;
        this.logger.verbose('PartnershipRequestsResolver.organisation (field resolver)', {
            partnershipRequest,
        });
        if (!partnershipRequest.receiverOrganisationId)
            return null;
        return this.organisationsService.findById(partnershipRequest.receiverOrganisationId, {
            useCache: true,
        });
    }
};
exports.PartnershipRequestsResolver = PartnershipRequestsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => partnership_requests_service_1.PartnershipRequestsService)),
    __metadata("design:type", partnership_requests_service_1.PartnershipRequestsService)
], PartnershipRequestsResolver.prototype, "partnershipRequestsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], PartnershipRequestsResolver.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => subscriptions_service_1.SubscriptionsService)),
    __metadata("design:type", subscriptions_service_1.SubscriptionsService)
], PartnershipRequestsResolver.prototype, "subscriptionsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], PartnershipRequestsResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('partnershipRequestData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_partnership_request_dto_1.CreatePartnershipRequestDto]),
    __metadata("design:returntype", Promise)
], PartnershipRequestsResolver.prototype, "createPartnershipRequest", null);
__decorate([
    (0, graphql_1.Mutation)(returns => String),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('partnershipRequestId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PartnershipRequestsResolver.prototype, "initiatePayment", null);
__decorate([
    (0, graphql_1.Mutation)(returns => String),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('partnershipRequestId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PartnershipRequestsResolver.prototype, "retryPayment", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('partnershipRequestId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PartnershipRequestsResolver.prototype, "approvePartnershipRequest", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('partnershipRequestId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PartnershipRequestsResolver.prototype, "declinePartnershipRequest", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('partnershipRequestId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PartnershipRequestsResolver.prototype, "removePartnershipRequest", null);
__decorate([
    (0, graphql_1.ResolveField)('senderOrganisation', () => organisation_model_1.Organisation, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [partnership_request_model_2.PartnershipRequest]),
    __metadata("design:returntype", Promise)
], PartnershipRequestsResolver.prototype, "senderOrganisation", null);
__decorate([
    (0, graphql_1.ResolveField)('receiverOrganisation', () => organisation_model_1.Organisation, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [partnership_request_model_2.PartnershipRequest]),
    __metadata("design:returntype", Promise)
], PartnershipRequestsResolver.prototype, "receiverOrganisation", null);
exports.PartnershipRequestsResolver = PartnershipRequestsResolver = __decorate([
    (0, graphql_1.Resolver)(() => partnership_request_model_2.PartnershipRequest)
], PartnershipRequestsResolver);
//# sourceMappingURL=partnership-requests.resolver.js.map