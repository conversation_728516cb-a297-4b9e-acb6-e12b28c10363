"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Partnership = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const organisation_model_1 = require("../../organisations/models/organisation.model");
const short_uuid_1 = __importDefault(require("short-uuid"));
const partnership_request_model_1 = require("../../partnership-requests/models/partnership-request.model");
const graphql_1 = require("@nestjs/graphql");
let Partnership = class Partnership extends sequelize_typescript_1.Model {
};
exports.Partnership = Partnership;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID, { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], Partnership.prototype, "id", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    (0, sequelize_typescript_1.Column)({ allowNull: false }),
    __metadata("design:type", String)
], Partnership.prototype, "organisationId", void 0);
__decorate([
    (0, graphql_1.Field)(() => organisation_model_1.Organisation),
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, 'organisationId'),
    __metadata("design:type", organisation_model_1.Organisation)
], Partnership.prototype, "organisation", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    (0, sequelize_typescript_1.Column)({ allowNull: false }),
    __metadata("design:type", String)
], Partnership.prototype, "partnershipOrganisationId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, 'partnershipOrganisationId'),
    __metadata("design:type", organisation_model_1.Organisation)
], Partnership.prototype, "partnershipOrganisation", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => partnership_request_model_1.PartnershipRequest),
    (0, sequelize_typescript_1.Column)({ allowNull: false }),
    __metadata("design:type", String)
], Partnership.prototype, "partnershipRequestId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => partnership_request_model_1.PartnershipRequest, 'partnershipRequestId'),
    __metadata("design:type", partnership_request_model_1.PartnershipRequest)
], Partnership.prototype, "partnershipRequest", void 0);
__decorate([
    (0, graphql_1.Field)(type => partnership_request_model_1.PartnershipRequest, { nullable: true }),
    __metadata("design:type", partnership_request_model_1.PartnershipRequest)
], Partnership.prototype, "partnershipRequestData", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Partnership.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Partnership.prototype, "updatedAt", void 0);
exports.Partnership = Partnership = __decorate([
    (0, graphql_1.ObjectType)(),
    sequelize_typescript_1.Table
], Partnership);
//# sourceMappingURL=partnerships.model.js.map