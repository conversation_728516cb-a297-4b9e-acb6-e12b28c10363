"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartnershipsService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const sequelize_typescript_1 = require("sequelize-typescript");
const partnership_requests_service_1 = require("../partnership-requests/partnership-requests.service");
const partnerships_model_1 = require("./models/partnerships.model");
const base_service_1 = require("../common/base.service");
const error_1 = require("../common/helpers/error");
const followers_service_1 = require("../followers/followers.service");
const memberships_service_1 = require("../memberships/memberships.service");
const membership_model_1 = require("../memberships/models/membership.model");
const follower_model_1 = require("../followers/models/follower.model");
const partnership_request_model_1 = require("../partnership-requests/models/partnership-request.model");
const stream_followers_service_1 = require("../feeds-followers/stream-followers.service");
const organisations_service_1 = require("../organisations/organisations.service");
let PartnershipsService = class PartnershipsService extends (0, base_service_1.BaseService)(partnerships_model_1.Partnership) {
    async createPartnership(organisationIds, partnershipRequestId, options) {
        var _a;
        this.logger.info('PartnershipsService.createPartnership', {
            organisationIds,
            partnershipRequestId,
        });
        if (organisationIds.length !== 2) {
            this.errorHelper.throwHttpException(`PartnershipsService.createPartnership`, 'CreatePartnershipDto requires organisationIds with 2 element');
        }
        const organisationId = organisationIds[0];
        const partnershipOrganisationId = organisationIds[1];
        const isTransactionSet = !!(options === null || options === void 0 ? void 0 : options.transaction);
        const transaction = (options === null || options === void 0 ? void 0 : options.transaction) || (await this.sequelize.transaction());
        try {
            const partnershipRequest = await this.partnershipRequestsService.findById(partnershipRequestId, { transaction });
            if (!partnershipRequest) {
                throw new Error('Partnership request not exists');
            }
            if (partnershipRequest.status !== partnership_request_model_1.PartnershipRequestStatus.Approved) {
                throw new Error('Partnership request status must be Approved');
            }
            let partnership = await this.create({
                organisationId,
                partnershipOrganisationId,
                partnershipRequestId,
            }, {
                transaction,
            });
            const organisation = await this.organisationsService.findById(organisationId);
            const orgConnections = organisation.connectedOrganisations;
            if (!orgConnections.includes(partnershipOrganisationId)) {
                orgConnections.push(partnershipOrganisationId);
                await this.organisationsService.updateById(organisation.id, {
                    connectedOrganisations: orgConnections,
                }, { transaction });
            }
            await this.create({
                organisationId: partnershipOrganisationId,
                partnershipOrganisationId: organisationId,
                partnershipRequestId,
            }, {
                transaction,
            });
            const partnershipOrganisation = await this.organisationsService.findById(partnershipOrganisationId);
            const partnershipOrgConnections = partnershipOrganisation.connectedOrganisations;
            if (!partnershipOrgConnections.includes(organisationId)) {
                partnershipOrgConnections.push(organisationId);
                await this.organisationsService.updateById(partnershipOrganisation.id, {
                    connectedOrganisations: partnershipOrgConnections,
                }, { transaction });
            }
            const organisationMemberships = await this.membershipsService.findAll({
                organisationId: organisationId,
                status: membership_model_1.MembershipStatus.Active,
            }, {
                transaction,
            });
            const partnershipOrganisationMemberships = await this.membershipsService.findAll({
                organisationId: partnershipOrganisationId,
                status: membership_model_1.MembershipStatus.Active,
            }, {
                transaction,
            });
            for (const membership of [
                ...organisationMemberships,
                ...partnershipOrganisationMemberships,
            ]) {
                const existingFollower = await this.followersService.findOne({
                    profileId: membership.profileId,
                    organisationId: membership.organisationId,
                }, { transaction });
                if (existingFollower) {
                    if (existingFollower.status !== follower_model_1.FollowerStatus.Active) {
                        await this.followersService.updateById(existingFollower.id, {
                            status: follower_model_1.FollowerStatus.Active,
                        }, { transaction });
                    }
                }
                else {
                    await this.followersService.create({
                        profileId: membership.profileId,
                        organisationId: membership.organisationId,
                        status: follower_model_1.FollowerStatus.Active,
                    }, { transaction });
                    try {
                        await this.streamFollowersService.follow({ profileId: membership.profileId }, membership.organisationId);
                    }
                    catch (e) {
                        this.logger.error(`PartnershipsService.createPartnership Error: ${e.message}`, (_a = e.response) === null || _a === void 0 ? void 0 : _a.body);
                    }
                }
            }
            const allOrganisations = await this.organisationsService.findAll();
            const partnershipOrgChild = allOrganisations.filter(org => {
                return org.parentOrganisations.some(parent => parent.id === organisationId);
            });
            if (partnershipOrgChild.length) {
                for (const childOrg of partnershipOrgChild) {
                    await this.organisationsService.addNewConnectedOrgToExistingParentChild({
                        organisationId: childOrg.id,
                        parentId: partnershipOrganisationId,
                    }, { transaction });
                }
            }
            const oppositePartnershipOrgChild = allOrganisations.filter(org => {
                return org.parentOrganisations.some(parent => parent.id === partnershipOrganisationId);
            });
            if (oppositePartnershipOrgChild.length) {
                for (const childOrg of oppositePartnershipOrgChild) {
                    await this.organisationsService.addNewConnectedOrgToExistingParentChild({
                        organisationId: childOrg.id,
                        parentId: organisationId,
                    }, { transaction });
                }
            }
            partnership = await this.findById(partnership.id, { transaction });
            if (!isTransactionSet) {
                await transaction.commit();
            }
            return partnership;
        }
        catch (e) {
            if (!isTransactionSet) {
                await transaction.rollback();
            }
            this.errorHelper.throwHttpException(`PartnershipsService.createPartnership`, e.message);
        }
    }
    async removePartnership(partnershipId) {
        this.logger.info('PartnershipsService.removePartnership', {
            partnershipId,
        });
        const partnership = await this.findById(partnershipId);
        if (!partnership) {
            this.errorHelper.throwHttpException(`PartnershipsService.removePartnership`, 'Partnership not found', common_1.HttpStatus.NOT_FOUND);
        }
        const oppositePartnership = await this.findOne({
            organisationId: partnership.partnershipOrganisationId,
            partnershipOrganisationId: partnership.organisationId,
        });
        if (!oppositePartnership) {
            this.errorHelper.throwHttpException(`PartnershipsService.removePartnership`, 'Opposite partnership not found', common_1.HttpStatus.NOT_FOUND);
        }
        const transaction = await this.sequelize.transaction();
        const streamUnfollowsArray = [];
        try {
            const organisationMemberships = await this.membershipsService.findAll({
                organisationId: partnership.organisationId,
                status: membership_model_1.MembershipStatus.Active,
            }, { transaction });
            for (const organisationMembership of organisationMemberships) {
                const existingFollower = await this.followersService.findOne({
                    profileId: organisationMembership.profileId,
                    organisationId: partnership.partnershipOrganisationId,
                }, { transaction });
                if (existingFollower) {
                    await this.followersService.removeById(existingFollower.id, {
                        transaction,
                    });
                    streamUnfollowsArray.push({
                        source: organisationMembership.profileId,
                        target: partnership.partnershipOrganisationId,
                    });
                }
            }
            const partnershipOrganisationMemberships = await this.membershipsService.findAll({
                organisationId: partnership.partnershipOrganisationId,
                status: membership_model_1.MembershipStatus.Active,
            }, { transaction });
            for (const partnershipOrganisationMembership of partnershipOrganisationMemberships) {
                const existingFollower = await this.followersService.findOne({
                    profileId: partnershipOrganisationMembership.profileId,
                    organisationId: partnership.organisationId,
                }, { transaction });
                if (existingFollower) {
                    await this.followersService.removeById(existingFollower.id, {
                        transaction,
                    });
                    streamUnfollowsArray.push({
                        source: partnershipOrganisationMembership.profileId,
                        target: partnership.organisationId,
                    });
                }
            }
            const allOrganisations = await this.organisationsService.findAll();
            const partnershipOrgChild = allOrganisations.filter(org => {
                return org.parentOrganisations.some(parent => parent.id === partnership.organisationId);
            });
            if (partnershipOrgChild.length) {
                for (const childOrg of partnershipOrgChild) {
                    await this.organisationsService.removeConnectedOrgToExistingParentChild({
                        organisationId: childOrg.id,
                        parentId: partnership.partnershipOrganisationId,
                    }, { transaction });
                }
            }
            const oppositePartnershipOrgChild = allOrganisations.filter(org => {
                return org.parentOrganisations.some(parent => parent.id === partnership.partnershipOrganisationId);
            });
            if (oppositePartnershipOrgChild.length) {
                for (const childOrg of oppositePartnershipOrgChild) {
                    await this.organisationsService.removeConnectedOrgToExistingParentChild({
                        organisationId: childOrg.id,
                        parentId: partnership.organisationId,
                    }, { transaction });
                }
            }
            const organisation = await this.organisationsService.findById(partnership.organisationId);
            const orgConnections = organisation.connectedOrganisations;
            const index = orgConnections.findIndex(x => x === partnership.partnershipOrganisationId);
            if (index > -1) {
                orgConnections.splice(index, 1);
                await this.organisationsService.updateById(organisation.id, {
                    connectedOrganisations: orgConnections,
                }, { transaction });
            }
            const partnershipOrganisation = await this.organisationsService.findById(oppositePartnership.organisationId);
            const partnershipConnectedOrgs = partnershipOrganisation.connectedOrganisations;
            const index2 = partnershipConnectedOrgs.findIndex(x => x === oppositePartnership.partnershipOrganisationId);
            if (index2 > -1) {
                partnershipConnectedOrgs.splice(index2, 1);
                await this.organisationsService.updateById(partnershipOrganisation.id, {
                    connectedOrganisations: partnershipConnectedOrgs,
                }, { transaction });
            }
            await this.partnershipRequestsService.removePartnershipRequest(partnership.partnershipRequestId, { transaction });
            await transaction.commit();
            await this.streamFollowersService.bulkUnfollowScript(streamUnfollowsArray);
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`PartnershipsService.removePartnership`, e.message);
        }
        return true;
    }
    async addFollowersForMembership(membership, options) {
        const isTransactionSet = !!(options === null || options === void 0 ? void 0 : options.transaction);
        const transaction = (options === null || options === void 0 ? void 0 : options.transaction) || (await this.sequelize.transaction());
        try {
            const partnerships = await this.findAll({
                organisationId: membership.organisationId,
            }, {
                attributes: ['partnershipOrganisationId'],
                includeParams: [
                    {
                        model: partnership_request_model_1.PartnershipRequest,
                        as: 'partnershipRequest',
                        where: {
                            status: partnership_request_model_1.PartnershipRequestStatus.Approved,
                        },
                    },
                ],
                transaction,
            });
            let follows = [];
            for (const partnership of partnerships) {
                this.followersService.addOrApproveFollower(partnership.partnershipOrganisationId, membership.profileId, { transaction });
                const followPartner = {
                    source: 'user:' + membership.profileId,
                    target: 'organisations:' + partnership.partnershipOrganisationId,
                };
                follows.push(followPartner);
            }
            if (!isTransactionSet) {
                await transaction.commit();
            }
            if (follows.length)
                await this.streamFollowersService.bulkFollowScript(follows);
        }
        catch (e) {
            if (!isTransactionSet) {
                await transaction.rollback();
            }
            this.errorHelper.throwHttpException(`PartnershipsService.setFollowersForMembership`, e.message);
        }
    }
};
exports.PartnershipsService = PartnershipsService;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => partnership_requests_service_1.PartnershipRequestsService)),
    __metadata("design:type", partnership_requests_service_1.PartnershipRequestsService)
], PartnershipsService.prototype, "partnershipRequestsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], PartnershipsService.prototype, "membershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => followers_service_1.FollowersService)),
    __metadata("design:type", followers_service_1.FollowersService)
], PartnershipsService.prototype, "followersService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => stream_followers_service_1.StreamFollowersService)),
    __metadata("design:type", stream_followers_service_1.StreamFollowersService)
], PartnershipsService.prototype, "streamFollowersService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], PartnershipsService.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", sequelize_typescript_1.Sequelize)
], PartnershipsService.prototype, "sequelize", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], PartnershipsService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], PartnershipsService.prototype, "errorHelper", void 0);
exports.PartnershipsService = PartnershipsService = __decorate([
    (0, common_1.Injectable)()
], PartnershipsService);
//# sourceMappingURL=partnerships.service.js.map