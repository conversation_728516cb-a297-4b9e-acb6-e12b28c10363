"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentTransaction = exports.TransactionStatus = exports.TransactionType = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const short_uuid_1 = __importDefault(require("short-uuid"));
const graphql_1 = require("@nestjs/graphql");
const sequelize_1 = require("sequelize");
const subscription_model_1 = require("../../subscriptions/models/subscription.model");
var TransactionType;
(function (TransactionType) {
    TransactionType["Checkout"] = "Checkout";
    TransactionType["Recurring"] = "Recurring";
})(TransactionType || (exports.TransactionType = TransactionType = {}));
(0, graphql_1.registerEnumType)(TransactionType, {
    name: 'TransactionType',
});
var TransactionStatus;
(function (TransactionStatus) {
    TransactionStatus["InProgress"] = "In_progress";
    TransactionStatus["Completed"] = "Completed";
    TransactionStatus["Failed"] = "Failed";
})(TransactionStatus || (exports.TransactionStatus = TransactionStatus = {}));
(0, graphql_1.registerEnumType)(TransactionStatus, {
    name: 'TransactionStatus',
});
let PaymentTransaction = class PaymentTransaction extends sequelize_typescript_1.Model {
};
exports.PaymentTransaction = PaymentTransaction;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], PaymentTransaction.prototype, "id", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => subscription_model_1.Subscription),
    (0, sequelize_typescript_1.Column)({ allowNull: false }),
    __metadata("design:type", String)
], PaymentTransaction.prototype, "subscriptionId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => subscription_model_1.Subscription, {
        foreignKey: 'subscriptionId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", subscription_model_1.Subscription)
], PaymentTransaction.prototype, "subscription", void 0);
__decorate([
    (0, graphql_1.Field)(() => TransactionType, { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
        defaultValue: TransactionType.Checkout,
    }),
    __metadata("design:type", String)
], PaymentTransaction.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], PaymentTransaction.prototype, "stripeCheckoutId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], PaymentTransaction.prototype, "stripeInvoiceId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], PaymentTransaction.prototype, "startDate", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], PaymentTransaction.prototype, "endDate", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], PaymentTransaction.prototype, "paymentStatus", void 0);
__decorate([
    (0, graphql_1.Field)(() => TransactionStatus, { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
        defaultValue: TransactionStatus.InProgress,
    }),
    __metadata("design:type", String)
], PaymentTransaction.prototype, "transactionStatus", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], PaymentTransaction.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], PaymentTransaction.prototype, "updatedAt", void 0);
exports.PaymentTransaction = PaymentTransaction = __decorate([
    (0, graphql_1.ObjectType)(),
    sequelize_typescript_1.Table
], PaymentTransaction);
//# sourceMappingURL=payment-transaction.model.js.map