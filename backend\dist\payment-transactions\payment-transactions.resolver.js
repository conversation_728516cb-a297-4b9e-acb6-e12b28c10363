"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentTransactionsResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const payment_transactions_service_1 = require("./payment-transactions.service");
const payment_transaction_model_1 = require("./models/payment-transaction.model");
let PaymentTransactionsResolver = class PaymentTransactionsResolver {
};
exports.PaymentTransactionsResolver = PaymentTransactionsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => payment_transactions_service_1.PaymentTransactionsService)),
    __metadata("design:type", payment_transactions_service_1.PaymentTransactionsService)
], PaymentTransactionsResolver.prototype, "paymentTransactionsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], PaymentTransactionsResolver.prototype, "logger", void 0);
exports.PaymentTransactionsResolver = PaymentTransactionsResolver = __decorate([
    (0, graphql_1.Resolver)(() => payment_transaction_model_1.PaymentTransaction)
], PaymentTransactionsResolver);
//# sourceMappingURL=payment-transactions.resolver.js.map