"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Post = exports.MultipleImageDetails = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const graphql_1 = require("@nestjs/graphql");
const short_uuid_1 = __importDefault(require("short-uuid"));
const profile_model_1 = require("../../profiles/models/profile.model");
const posts_args_1 = require("../args/posts.args");
const sequelize_1 = require("sequelize");
const organisation_model_1 = require("../../organisations/models/organisation.model");
const event_model_1 = require("../../events/models/event.model");
const incentive_model_1 = require("../../incentives/models/incentive.model");
const webinar_model_1 = require("../../webinars/models/webinar.model");
const graphql_type_json_1 = __importDefault(require("graphql-type-json"));
let MultipleImageDetails = class MultipleImageDetails {
};
exports.MultipleImageDetails = MultipleImageDetails;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], MultipleImageDetails.prototype, "image", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], MultipleImageDetails.prototype, "imageWidth", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Number)
], MultipleImageDetails.prototype, "imageHeight", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], MultipleImageDetails.prototype, "imageFormat", void 0);
exports.MultipleImageDetails = MultipleImageDetails = __decorate([
    (0, graphql_1.ObjectType)()
], MultipleImageDetails);
let Post = class Post extends sequelize_typescript_1.Model {
    get views() {
        return (this.seenBy || []).length;
    }
};
exports.Post = Post;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], Post.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(() => posts_args_1.PostType, { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        allowNull: false,
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], Post.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Post.prototype, "image", void 0);
__decorate([
    (0, graphql_1.Field)(() => Number, { nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Number)
], Post.prototype, "mediaWidth", void 0);
__decorate([
    (0, graphql_1.Field)(() => Number, { nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Number)
], Post.prototype, "mediaHeight", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Post.prototype, "mediaFormat", void 0);
__decorate([
    (0, graphql_1.Field)(() => [MultipleImageDetails], { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.JSON),
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], Post.prototype, "multipleImages", void 0);
__decorate([
    (0, graphql_1.Field)(() => posts_args_1.PostImageCategory, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        allowNull: true,
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], Post.prototype, "imageCategory", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Post.prototype, "video", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Post.prototype, "document", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Post.prototype, "text", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Post.prototype, "profileId", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, 'profileId'),
    __metadata("design:type", profile_model_1.Profile)
], Post.prototype, "profile", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Post.prototype, "organisationId", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, 'organisationId'),
    __metadata("design:type", organisation_model_1.Organisation)
], Post.prototype, "organisation", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID, { nullable: true }),
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    (0, sequelize_typescript_1.Column)({
        allowNull: true,
        type: sequelize_1.DataTypes.UUID,
    }),
    __metadata("design:type", String)
], Post.prototype, "parentOrgId", void 0);
__decorate([
    (0, graphql_1.Field)(() => organisation_model_1.Organisation, { nullable: true }),
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, 'parentOrgId'),
    __metadata("design:type", organisation_model_1.Organisation)
], Post.prototype, "parentOrganisation", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => event_model_1.Event),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Post.prototype, "eventId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => event_model_1.Event, 'eventId'),
    __metadata("design:type", event_model_1.Event)
], Post.prototype, "event", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => incentive_model_1.Incentive),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Post.prototype, "incentiveId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => incentive_model_1.Incentive, 'incentiveId'),
    __metadata("design:type", incentive_model_1.Incentive)
], Post.prototype, "incentive", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => webinar_model_1.Webinar),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Post.prototype, "webinarId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => webinar_model_1.Webinar, 'webinarId'),
    __metadata("design:type", webinar_model_1.Webinar)
], Post.prototype, "webinar", void 0);
__decorate([
    (0, graphql_1.Field)(() => [String], { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], Post.prototype, "seenBy", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int),
    (0, sequelize_typescript_1.Column)({
        defaultValue: 0,
    }),
    __metadata("design:type", Number)
], Post.prototype, "totalViews", void 0);
__decorate([
    (0, graphql_1.Field)(() => posts_args_1.PostStatus, { nullable: true, defaultValue: posts_args_1.PostStatus.Live }),
    (0, sequelize_typescript_1.Column)({
        allowNull: true,
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], Post.prototype, "status", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({ type: sequelize_1.DataTypes.DATE }),
    __metadata("design:type", Date)
], Post.prototype, "scheduledAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Post.prototype, "createdAt", void 0);
__decorate([
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Post.prototype, "updatedAt", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int),
    (0, sequelize_typescript_1.Column)(sequelize_1.DataTypes.VIRTUAL),
    __metadata("design:type", Number),
    __metadata("design:paramtypes", [])
], Post.prototype, "views", null);
__decorate([
    (0, graphql_1.Field)(() => graphql_type_json_1.default, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.JSON,
    }),
    __metadata("design:type", Object)
], Post.prototype, "postAudience", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true }),
    __metadata("design:type", Boolean)
], Post.prototype, "isPartnerPost", void 0);
exports.Post = Post = __decorate([
    sequelize_typescript_1.Table,
    (0, graphql_1.ObjectType)()
], Post);
//# sourceMappingURL=post.model.js.map