"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostsModule = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("@nestjs/sequelize");
const post_model_1 = require("./models/post.model");
const posts_service_1 = require("./posts.service");
const posts_resolver_1 = require("./posts.resolver");
const posts_repository_1 = require("./posts.repository");
const profiles_module_1 = require("../profiles/profiles.module");
const organisations_module_1 = require("../organisations/organisations.module");
const memberships_module_1 = require("../memberships/memberships.module");
const common_module_1 = require("../common/common.module");
const activities_module_1 = require("../activities/activities.module");
const event_invitations_module_1 = require("../event-invitations/event-invitations.module");
const events_module_1 = require("../events/events.module");
const notifications_module_1 = require("../notifications/notifications.module");
const incentives_module_1 = require("../incentives/incentives.module");
const incentive_participants_module_1 = require("../incentive-participants/incentive-participants.module");
const webinars_module_1 = require("../webinars/webinars.module");
const webinar_participants_module_1 = require("../webinar-participants/webinar-participants.module");
const stream_updates_module_1 = require("../feeds-updates/stream-updates.module");
const posts_controller_1 = require("./posts.controller");
const stream_posts_module_1 = require("../feeds-posts/stream-posts.module");
const organisation_loyalty_points_module_1 = require("../organisation-loyalty-points/organisation-loyalty-points.module");
const achievements_module_1 = require("../achievements/achievements.module");
const partner_organisations_module_1 = require("../partner-organisations/partner-organisations.module");
let PostsModule = class PostsModule {
};
exports.PostsModule = PostsModule;
exports.PostsModule = PostsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            sequelize_1.SequelizeModule.forFeature([post_model_1.Post]),
            (0, common_1.forwardRef)(() => common_module_1.CommonModule),
            (0, common_1.forwardRef)(() => profiles_module_1.ProfilesModule),
            (0, common_1.forwardRef)(() => organisations_module_1.OrganisationsModule),
            (0, common_1.forwardRef)(() => events_module_1.EventsModule),
            (0, common_1.forwardRef)(() => event_invitations_module_1.EventsInvitationsModule),
            (0, common_1.forwardRef)(() => memberships_module_1.MembershipsModule),
            (0, common_1.forwardRef)(() => activities_module_1.ActivitiesModule),
            (0, common_1.forwardRef)(() => notifications_module_1.NotificationsModule),
            (0, common_1.forwardRef)(() => incentives_module_1.IncentivesModule),
            (0, common_1.forwardRef)(() => incentive_participants_module_1.IncentiveParticipantsModule),
            (0, common_1.forwardRef)(() => webinars_module_1.WebinarsModule),
            (0, common_1.forwardRef)(() => webinar_participants_module_1.WebinarParticipantsModule),
            (0, common_1.forwardRef)(() => stream_updates_module_1.StreamUpdatesModule),
            (0, common_1.forwardRef)(() => stream_posts_module_1.StreamPostsModule),
            (0, common_1.forwardRef)(() => organisation_loyalty_points_module_1.OrganisationLoyaltyPointsModule),
            (0, common_1.forwardRef)(() => achievements_module_1.AchievementsModule),
            (0, common_1.forwardRef)(() => partner_organisations_module_1.PartnerOrganisationsModule),
        ],
        providers: [posts_resolver_1.PostsResolver, posts_service_1.PostsService, posts_repository_1.PostsRepository, posts_controller_1.PostsController],
        exports: [posts_service_1.PostsService, posts_repository_1.PostsRepository, posts_controller_1.PostsController],
    })
], PostsModule);
//# sourceMappingURL=posts.module.js.map