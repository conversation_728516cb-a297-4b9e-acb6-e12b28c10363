{"version": 3, "file": "posts.module.js", "sourceRoot": "", "sources": ["../../src/posts/posts.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAoD;AACpD,iDAAoD;AACpD,oDAA2C;AAC3C,mDAA+C;AAC/C,qDAAiD;AACjD,yDAAqD;AACrD,iEAA6D;AAC7D,gFAA4E;AAC5E,0EAAsE;AACtE,2DAAuD;AACvD,uEAAmE;AACnE,4FAAwF;AACxF,2DAAuD;AACvD,gFAA4E;AAC5E,uEAAmE;AACnE,2GAAsG;AACtG,iEAA6D;AAC7D,qGAAgG;AAChG,kFAA6E;AAC7E,yDAAqD;AACrD,4EAAuE;AACvE,0HAAoH;AACpH,6EAAyE;AACzE,wGAAmG;AA0B5F,IAAM,WAAW,GAAjB,MAAM,WAAW;CAAG,CAAA;AAAd,kCAAW;sBAAX,WAAW;IAxBvB,IAAA,eAAM,EAAC;QACN,OAAO,EAAE;YACP,2BAAe,CAAC,UAAU,CAAC,CAAC,iBAAI,CAAC,CAAC;YAClC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,4BAAY,CAAC;YAC9B,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,gCAAc,CAAC;YAChC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,0CAAmB,CAAC;YACrC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,4BAAY,CAAC;YAC9B,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,kDAAuB,CAAC;YACzC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,sCAAiB,CAAC;YACnC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,oCAAgB,CAAC;YAClC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,0CAAmB,CAAC;YACrC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,oCAAgB,CAAC;YAClC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,2DAA2B,CAAC;YAC7C,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,gCAAc,CAAC;YAChC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,uDAAyB,CAAC;YAC3C,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,2CAAmB,CAAC;YACrC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,uCAAiB,CAAC;YACnC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,oEAA+B,CAAC;YACjD,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,wCAAkB,CAAC;YACpC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,yDAA0B,CAAC;SAC7C;QACD,SAAS,EAAE,CAAC,8BAAa,EAAE,4BAAY,EAAE,kCAAe,EAAE,kCAAe,CAAC;QAC1E,OAAO,EAAE,CAAC,4BAAY,EAAE,kCAAe,EAAE,kCAAe,CAAC;KAC1D,CAAC;GACW,WAAW,CAAG"}