"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostsResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const post_model_1 = require("./models/post.model");
const posts_service_1 = require("./posts.service");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const posts_args_1 = require("./args/posts.args");
const paginated_result_1 = require("../common/args/paginated-result");
const profile_model_1 = require("../profiles/models/profile.model");
const profiles_service_1 = require("../profiles/profiles.service");
const organisations_service_1 = require("../organisations/organisations.service");
const organisation_model_1 = require("../organisations/models/organisation.model");
const event_model_1 = require("../events/models/event.model");
const create_post_input_1 = require("./dto/create-post.input");
const memberships_service_rights_1 = require("../memberships/helpers/memberships.service.rights");
const update_post_input_1 = require("./dto/update-post.input");
const error_1 = require("../common/helpers/error");
const events_service_1 = require("../events/events.service");
const incentive_model_1 = require("../incentives/models/incentive.model");
const incentives_service_1 = require("../incentives/incentives.service");
const webinar_model_1 = require("../webinars/models/webinar.model");
const webinars_service_1 = require("../webinars/webinars.service");
const update_post_schedule_date_input_1 = require("./dto/update-post-schedule-date.input");
let PostsResolver = class PostsResolver {
    async createPost(user, postData) {
        this.logger.verbose('PostsResolver.createPost (mutation)', {
            user: user.toLogObject(),
            postData,
        });
        const { organisationId } = postData;
        await this.membershipsServiceRights.checkRights(organisationId, {
            currentProfileId: user.profileId,
            rights: [memberships_service_rights_1.MembershipRight.CreatePost],
        });
        return this.postsService.createPost(Object.assign(Object.assign({}, postData), { profileId: user.profileId }), {
            currentUser: user,
        });
    }
    async updatePost(user, postId, postData) {
        this.logger.verbose('PostsResolver.updatePost (mutation)', {
            user: user.toLogObject(),
            postData,
        });
        const post = await this.postsService.findById(postId);
        await this.membershipsServiceRights.checkRights(post.organisationId, {
            currentProfileId: user.profileId,
            rights: [memberships_service_rights_1.MembershipRight.UpdatePost],
        });
        return this.postsService.updatePost(postId, postData, {
            profileId: user.profileId,
        });
    }
    async updatePostScheduleTime(user, postId, postData) {
        this.logger.verbose('PostsResolver.updatePostScheduleTime (mutation)', {
            user: user.toLogObject(),
            postData,
        });
        const post = await this.postsService.findById(postId);
        await this.membershipsServiceRights.checkRights(post.organisationId, {
            currentProfileId: user.profileId,
            rights: [memberships_service_rights_1.MembershipRight.UpdatePost],
        });
        return this.postsService.updatePostScheduleTime(postId, postData, {
            profileId: user.profileId,
        });
    }
    async removePost(user, postId) {
        this.logger.verbose('PostsResolver.removePost (mutation)', {
            user: user.toLogObject(),
            postId,
        });
        const post = await this.postsService.findById(postId);
        if (!post)
            return true;
        await this.membershipsServiceRights.checkRights(post.organisationId, {
            currentProfileId: user.profileId,
            rights: [memberships_service_rights_1.MembershipRight.RemovePost],
        });
        await this.postsService.removePost(postId, {
            profileId: user.profileId,
        });
        return true;
    }
    seenPost(user, id) {
        this.logger.verbose('PostsResolver.seenPost (mutation)', {
            user: user.toLogObject(),
            id,
        });
        return this.postsService.seenPost(id, {
            profileId: user.profileId,
        });
    }
    async post(user, id) {
        this.logger.verbose('PostsResolver.post (query)', {
            id,
        });
        const post = await this.postsService.findById(id);
        if (!post) {
            this.errorHelper.throwHttpException(`PostsResolver.event`, `Post not exists`);
        }
        return post;
    }
    async posts(user, postArgs) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        this.logger.verbose('PostsResolver.posts (query)', {
            user: user.toLogObject(),
            postArgs,
        });
        return this.postsService.findPosts(user.profileId, {
            type: (_a = postArgs.filter) === null || _a === void 0 ? void 0 : _a.type,
            organisationId: (_b = postArgs.filter) === null || _b === void 0 ? void 0 : _b.organisationId,
            parentOrgId: (_c = postArgs.filter) === null || _c === void 0 ? void 0 : _c.parentOrgId,
            eventId: (_d = postArgs.filter) === null || _d === void 0 ? void 0 : _d.eventId,
            incentiveId: (_e = postArgs.filter) === null || _e === void 0 ? void 0 : _e.incentiveId,
            webinarId: (_f = postArgs.filter) === null || _f === void 0 ? void 0 : _f.webinarId,
            updates: (_g = postArgs.filter) === null || _g === void 0 ? void 0 : _g.updates,
            searchText: (_h = postArgs.filter) === null || _h === void 0 ? void 0 : _h.searchText,
        }, {
            first: postArgs.first,
            after: postArgs.after,
            offset: postArgs.offset,
            sortBy: postArgs.sortBy,
            sortOrder: postArgs.sortOrder,
        });
    }
    async scheduledPostList(user, organisationId, parentOrganisationId) {
        this.logger.verbose('PostsResolver.scheduledPostList (query)', {
            user: user.toLogObject(),
        });
        const posts = await this.postsService.scheduledPostList({
            currentUser: user,
        }, organisationId, parentOrganisationId);
        const data = {
            records: posts,
            totalCount: posts.length,
        };
        return data;
    }
    async profile(post) {
        this.logger.verbose('PostsResolver.profile (field resolver)', {
            postId: post.id,
        });
        return this.profilesService.findById(post.profileId);
    }
    async organisation(post) {
        this.logger.verbose('PostsResolver.organisation (field resolver)', {
            postId: post.id,
        });
        if (!post.organisationId)
            return null;
        return this.organisationsService.findById(post.organisationId, {
            useCache: true,
        });
    }
    isPartnerPost(post) {
        return !!post.parentOrgId;
    }
    async parentOrganisation(post) {
        if (!post.parentOrgId)
            return null;
        return this.organisationsService.findById(post.parentOrgId);
    }
    async event(post) {
        if (!post.eventId)
            return null;
        return this.eventsService.findById(post.eventId, {
            useCache: true,
        });
    }
    async incentive(post) {
        if (!post.incentiveId)
            return null;
        return this.incentivesService.findById(post.incentiveId, {
            useCache: true,
        });
    }
    async webinar(post) {
        if (!post.webinarId)
            return null;
        return this.webinarsService.findById(post.webinarId, {
            useCache: true,
        });
    }
    isSeen(user, post) {
        return post.seenBy.includes(user.profileId);
    }
};
exports.PostsResolver = PostsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => posts_service_1.PostsService)),
    __metadata("design:type", posts_service_1.PostsService)
], PostsResolver.prototype, "postsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], PostsResolver.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => events_service_1.EventsService)),
    __metadata("design:type", events_service_1.EventsService)
], PostsResolver.prototype, "eventsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentives_service_1.IncentivesService)),
    __metadata("design:type", incentives_service_1.IncentivesService)
], PostsResolver.prototype, "incentivesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinars_service_1.WebinarsService)),
    __metadata("design:type", webinars_service_1.WebinarsService)
], PostsResolver.prototype, "webinarsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], PostsResolver.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_rights_1.MembershipsServiceRights)),
    __metadata("design:type", memberships_service_rights_1.MembershipsServiceRights)
], PostsResolver.prototype, "membershipsServiceRights", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], PostsResolver.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], PostsResolver.prototype, "errorHelper", void 0);
__decorate([
    (0, graphql_1.Mutation)(() => post_model_1.Post),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('postData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_post_input_1.CreatePostInput]),
    __metadata("design:returntype", Promise)
], PostsResolver.prototype, "createPost", null);
__decorate([
    (0, graphql_1.Mutation)(() => post_model_1.Post),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('postId')),
    __param(2, (0, graphql_1.Args)('postData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, update_post_input_1.UpdatePostInput]),
    __metadata("design:returntype", Promise)
], PostsResolver.prototype, "updatePost", null);
__decorate([
    (0, graphql_1.Mutation)(() => post_model_1.Post),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('postId')),
    __param(2, (0, graphql_1.Args)('postData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, update_post_schedule_date_input_1.UpdatePostScheduleDateInput]),
    __metadata("design:returntype", Promise)
], PostsResolver.prototype, "updatePostScheduleTime", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('postId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PostsResolver.prototype, "removePost", null);
__decorate([
    (0, graphql_1.Mutation)(() => post_model_1.Post),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PostsResolver.prototype, "seenPost", null);
__decorate([
    (0, graphql_1.Query)(() => post_model_1.Post),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id', { nullable: false })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PostsResolver.prototype, "post", null);
__decorate([
    (0, graphql_1.Query)(() => paginated_result_1.PostsResult),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, posts_args_1.PostsArgs]),
    __metadata("design:returntype", Promise)
], PostsResolver.prototype, "posts", null);
__decorate([
    (0, graphql_1.Query)(() => paginated_result_1.PostsResult),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId')),
    __param(2, (0, graphql_1.Args)('parentOrganisationId', { nullable: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], PostsResolver.prototype, "scheduledPostList", null);
__decorate([
    (0, graphql_1.ResolveField)('profile', () => profile_model_1.Profile),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [post_model_1.Post]),
    __metadata("design:returntype", Promise)
], PostsResolver.prototype, "profile", null);
__decorate([
    (0, graphql_1.ResolveField)(() => organisation_model_1.Organisation, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [post_model_1.Post]),
    __metadata("design:returntype", Promise)
], PostsResolver.prototype, "organisation", null);
__decorate([
    (0, graphql_1.ResolveField)('isPartnerPost', () => Boolean, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [post_model_1.Post]),
    __metadata("design:returntype", Boolean)
], PostsResolver.prototype, "isPartnerPost", null);
__decorate([
    (0, graphql_1.ResolveField)('parentOrganisation', () => organisation_model_1.Organisation, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [post_model_1.Post]),
    __metadata("design:returntype", Promise)
], PostsResolver.prototype, "parentOrganisation", null);
__decorate([
    (0, graphql_1.ResolveField)('event', () => event_model_1.Event, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [post_model_1.Post]),
    __metadata("design:returntype", Promise)
], PostsResolver.prototype, "event", null);
__decorate([
    (0, graphql_1.ResolveField)('incentive', () => incentive_model_1.Incentive, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [post_model_1.Post]),
    __metadata("design:returntype", Promise)
], PostsResolver.prototype, "incentive", null);
__decorate([
    (0, graphql_1.ResolveField)('webinar', () => webinar_model_1.Webinar, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [post_model_1.Post]),
    __metadata("design:returntype", Promise)
], PostsResolver.prototype, "webinar", null);
__decorate([
    (0, graphql_1.ResolveField)('isSeen', () => Boolean, { nullable: true }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, post_model_1.Post]),
    __metadata("design:returntype", Boolean)
], PostsResolver.prototype, "isSeen", null);
exports.PostsResolver = PostsResolver = __decorate([
    (0, graphql_1.Resolver)(() => post_model_1.Post)
], PostsResolver);
//# sourceMappingURL=posts.resolver.js.map