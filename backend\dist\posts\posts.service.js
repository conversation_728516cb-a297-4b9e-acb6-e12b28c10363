"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostsService = void 0;
const common_1 = require("@nestjs/common");
const moment_1 = __importDefault(require("moment"));
const nest_winston_1 = require("nest-winston");
const sequelize_1 = require("sequelize");
const sequelize_typescript_1 = require("sequelize-typescript");
const winston_1 = require("winston");
const activities_service_1 = require("../activities/activities.service");
const activities_args_1 = require("../activities/args/activities.args");
const base_service_1 = require("../common/base.service");
const error_1 = require("../common/helpers/error");
const underscore_1 = require("../common/helpers/underscore");
const notificationLayoutHelper_1 = require("../email/helpers/notificationLayoutHelper");
const event_invitations_args_1 = require("../event-invitations/args/event-invitations.args");
const event_invitations_service_1 = require("../event-invitations/event-invitations.service");
const events_service_1 = require("../events/events.service");
const stream_posts_service_1 = require("../feeds-posts/stream-posts.service");
const stream_updates_service_1 = require("../feeds-updates/stream-updates.service");
const incentive_participants_args_1 = require("../incentive-participants/args/incentive-participants.args");
const incentive_participants_service_1 = require("../incentive-participants/incentive-participants.service");
const incentives_service_1 = require("../incentives/incentives.service");
const memberships_service_1 = require("../memberships/memberships.service");
const notifications_args_1 = require("../notifications/args/notifications.args");
const notifications_service_1 = require("../notifications/notifications.service");
const organisations_service_1 = require("../organisations/organisations.service");
const profiles_service_1 = require("../profiles/profiles.service");
const webinar_participants_args_1 = require("../webinar-participants/args/webinar-participants.args");
const webinar_participants_service_1 = require("../webinar-participants/webinar-participants.service");
const webinars_service_1 = require("../webinars/webinars.service");
const posts_args_1 = require("./args/posts.args");
const post_model_1 = require("./models/post.model");
const posts_repository_1 = require("./posts.repository");
const organisation_loyalty_points_service_1 = require("../organisation-loyalty-points/organisation-loyalty-points.service");
const achievements_service_1 = require("../achievements/achievements.service");
const achievements_args_1 = require("../achievements/args/achievements.args");
const organisation_activity_model_1 = require("../activities/models/organisation-activity.model");
const partner_organisations_service_1 = require("../partner-organisations/partner-organisations.service");
const partner_organisations_args_1 = require("../partner-organisations/args/partner-organisations.args");
let PostsService = class PostsService extends (0, base_service_1.BaseService)(post_model_1.Post) {
    async createPost(createPostDto, options) {
        var _a;
        this.logger.info('PostsService.createPost', {
            currentUser: (_a = options.currentUser) === null || _a === void 0 ? void 0 : _a.toLogObject(),
            createPostDto,
        });
        if (createPostDto.parentOrgId) {
            const partnership = await this.partnerOrganisationsService.findByParentAndChildIds(createPostDto.parentOrgId, createPostDto.organisationId);
            if (!partnership) {
                throw new common_1.BadRequestException('No partnership exists between these organizations');
            }
            if (partnership.status !== partner_organisations_args_1.PartnerOrganisationStatus.Approved) {
                throw new common_1.BadRequestException('Partnership is not in approved status');
            }
            const startOfMonth = (0, moment_1.default)().startOf('month').toDate();
            const endOfMonth = (0, moment_1.default)().endOf('month').toDate();
            const postsCount = await this.countPartnerPosts({
                childOrgId: createPostDto.organisationId,
                parentOrgId: createPostDto.parentOrgId,
                startDate: startOfMonth,
                endDate: endOfMonth,
            });
            if (postsCount >= partnership.postsLimit) {
                throw new common_1.BadRequestException(`Monthly post limit of ${partnership.postsLimit} has been reached for this partnership. The limit will reset on ${(0, moment_1.default)()
                    .endOf('month')
                    .add(1, 'day')
                    .format('MMMM D, YYYY')}.`);
            }
        }
        const isTransactionSet = !!options.transaction;
        const transaction = options.transaction || (await this.sequelize.transaction());
        try {
            createPostDto.text = createPostDto.text.replace(/\n\s*\n\s*\n/g, '\n\n');
            const post = await this.create(Object.assign(Object.assign({}, createPostDto), { seenBy: [options.currentUser.profileId] }), { transaction });
            if (createPostDto.eventId) {
                const eventInvitations = await this.eventInvitationsService.findAll({
                    eventId: createPostDto.eventId,
                    status: {
                        [sequelize_1.Op.in]: [
                            event_invitations_args_1.EventInvitationStatus.Interested,
                            event_invitations_args_1.EventInvitationStatus.Attending,
                        ],
                    },
                }, { transaction });
                for (const eventInvitation of eventInvitations) {
                    await this.notificationsService.createNotification({
                        ownerProfileId: eventInvitation.profileId,
                        profileId: options.currentUser.profileId,
                        eventInvitationId: eventInvitation.id,
                        type: notifications_args_1.NotificationType.NewEventUpdate,
                        data: {
                            postId: post.id,
                        },
                    }, {
                        transaction,
                    });
                }
                const profileIds = eventInvitations.map(user => user.profileId);
                const replacementOrg = await this.organisationsService.findById(createPostDto.organisationId);
                const eventDetails = await this.eventsService.findById(createPostDto.eventId);
                const replacements = [replacementOrg.name, eventDetails.name];
                await this.notificationsService.sendPushNotification({
                    profileIds,
                    replacements,
                    messageType: notifications_args_1.NotificationMessage.NewEventUpdate,
                    route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.organisationEvent, {
                        vanityId: replacementOrg === null || replacementOrg === void 0 ? void 0 : replacementOrg.vanityId,
                        eventId: eventDetails === null || eventDetails === void 0 ? void 0 : eventDetails.id,
                    }),
                });
            }
            if (createPostDto.incentiveId) {
                const incentiveParticipants = await this.incentiveParticipantsService.findAll({
                    incentiveId: createPostDto.incentiveId,
                    status: incentive_participants_args_1.IncentiveParticipantStatus.Registered,
                }, { transaction });
                for (const incentiveParticipant of incentiveParticipants) {
                    await this.notificationsService.createNotification({
                        ownerProfileId: incentiveParticipant.profileId,
                        profileId: options.currentUser.profileId,
                        incentiveParticipantId: incentiveParticipant.id,
                        type: notifications_args_1.NotificationType.NewIncentiveUpdate,
                        data: {
                            postId: post.id,
                        },
                    }, {
                        transaction,
                    });
                }
                const profileIds = incentiveParticipants.map(user => user.profileId);
                const replacementOrg = await this.organisationsService.findById(createPostDto.organisationId);
                const incentiveDetails = await this.incentivesService.findById(createPostDto.incentiveId);
                const replacements = [replacementOrg.name, incentiveDetails.name];
                await this.notificationsService.sendPushNotification({
                    profileIds,
                    replacements,
                    messageType: notifications_args_1.NotificationMessage.NewIncentiveUpdate,
                    route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.organisationIncentive, {
                        vanityId: replacementOrg.vanityId,
                        incentiveId: incentiveDetails === null || incentiveDetails === void 0 ? void 0 : incentiveDetails.id,
                    }),
                });
            }
            if (createPostDto.webinarId) {
                const webinarParticipants = await this.webinarParticipantsService.findAll({
                    webinarId: createPostDto.webinarId,
                    status: {
                        [sequelize_1.Op.in]: [
                            webinar_participants_args_1.WebinarParticipantStatus.Registered,
                            webinar_participants_args_1.WebinarParticipantStatus.Speaker,
                            webinar_participants_args_1.WebinarParticipantStatus.Host,
                            webinar_participants_args_1.WebinarParticipantStatus.HostAdmin,
                            webinar_participants_args_1.WebinarParticipantStatus.HiddenHost,
                        ],
                    },
                }, { transaction });
                for (const webinarParticipant of webinarParticipants) {
                    await this.notificationsService.createNotification({
                        ownerProfileId: webinarParticipant.profileId,
                        profileId: options.currentUser.profileId,
                        webinarParticipantId: webinarParticipant.id,
                        type: notifications_args_1.NotificationType.NewWebinarUpdate,
                        data: {
                            postId: post.id,
                        },
                    }, {
                        transaction,
                    });
                }
                const profileIds = webinarParticipants.map(user => user.profileId);
                const replacementOrg = await this.organisationsService.findById(createPostDto.organisationId);
                const webinarDetails = await this.webinarsService.findById(createPostDto.webinarId);
                const replacements = [replacementOrg.name, webinarDetails.name];
                await this.notificationsService.sendPushNotification({
                    profileIds,
                    replacements,
                    messageType: notifications_args_1.NotificationMessage.NewWebinarUpdate,
                    route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.organisationWebinar, {
                        vanityId: replacementOrg.vanityId,
                        id: webinarDetails === null || webinarDetails === void 0 ? void 0 : webinarDetails.id,
                    }),
                });
            }
            if (createPostDto.eventId ||
                createPostDto.incentiveId ||
                createPostDto.webinarId) {
                const streamUpdateObj = {
                    organisationId: createPostDto.organisationId,
                    postId: post.id,
                };
                if (createPostDto.text) {
                    streamUpdateObj['text'] = createPostDto.text;
                }
                if (createPostDto.eventId) {
                    streamUpdateObj['eventId'] = createPostDto.eventId;
                }
                if (createPostDto.incentiveId) {
                    streamUpdateObj['incentiveId'] = createPostDto.incentiveId;
                }
                if (createPostDto.webinarId) {
                    streamUpdateObj['webinarId'] = createPostDto.webinarId;
                }
                await this.streamUpdatesService.addUpdate(createPostDto.profileId, streamUpdateObj);
            }
            if (post.status === posts_args_1.PostStatus.Live) {
                const createdByProfile = await this.profilesService.findById(options.currentUser.profileId);
                const currentDayPostActivity = await this.organisationLoyaltyPointsService.findAll({
                    organisationId: createPostDto.organisationId,
                    [sequelize_1.Op.and]: [
                        sequelize_typescript_1.Sequelize.literal(`DATE_TRUNC('day', "createdAt") = CURRENT_DATE`),
                    ],
                });
                let createdPostsPoints = 0;
                let removedPostsPoints = 0;
                if (currentDayPostActivity.length) {
                    currentDayPostActivity.forEach(activity => {
                        if (activity.type === activities_args_1.ActivityType.RemovePost) {
                            const postCreatedAtDate = activity.placeholders.postCreatedAt;
                            if (postCreatedAtDate &&
                                new Date(postCreatedAtDate).toISOString().split('T')[0] ===
                                    new Date().toISOString().split('T')[0]) {
                                removedPostsPoints += activity.points;
                            }
                        }
                        else if (activity.type === activities_args_1.ActivityType.CreatePost) {
                            createdPostsPoints += activity.points;
                        }
                    });
                }
                if (createdPostsPoints + removedPostsPoints >= 0) {
                    const isParentOrg = await this.isParentOrganization(createPostDto.organisationId);
                    const loyaltyPointEntry = await this.organisationLoyaltyPointsService.addPoints({
                        type: activities_args_1.ActivityType.CreatePost,
                        organisationId: createPostDto.organisationId,
                        parentOrgId: createPostDto.parentOrgId,
                        transaction,
                        placeholders: {
                            postId: post.id,
                            postContent: createPostDto.text,
                            streamActivityId: post.id,
                            organisationId: createPostDto.organisationId,
                            createdById: createdByProfile.id,
                            name: createdByProfile.name,
                            isParentOrg
                        },
                    });
                    await this.activitiesService.create({
                        profileId: options.currentUser.profileId,
                        data: loyaltyPointEntry,
                        type: 'CreatePost',
                    }, { transaction });
                }
            }
            const orgActivityData = {
                createdById: options.currentUser.profileId,
                organisationId: createPostDto.organisationId,
                parentOrgId: createPostDto.parentOrgId,
                type: activities_args_1.ActivityType.CreatePost,
                postId: post.id,
                data: {
                    postId: post.id,
                    postContent: createPostDto.text,
                    parentOrgId: createPostDto.parentOrgId
                },
            };
            await this.activitiesService.createOrganisationActivity(orgActivityData, {
                transaction,
            });
            const organisation = await this.organisationsService.findById(createPostDto.organisationId);
            if (!organisation) {
                throw new common_1.HttpException('Organisation not found', common_1.HttpStatus.BAD_REQUEST);
            }
            const currentMonth = (0, moment_1.default)().month();
            const lastPost = await this.findAll({
                organisationId: organisation.id,
            }, { order: [['createdAt', 'DESC']] });
            const lastPostMonth = lastPost.length
                ? (0, moment_1.default)(lastPost[0].createdAt).month()
                : -1;
            const monthEndThreeDaysAgo = (0, moment_1.default)()
                .endOf('month')
                .subtract(3, 'days')
                .toDate();
            if (organisation &&
                organisation.hasClubHabloSubscription &&
                post.createdAt < monthEndThreeDaysAgo &&
                (post.status === posts_args_1.PostStatus.Live || post.status === posts_args_1.PostStatus.Repost)) {
                if (currentMonth !== lastPostMonth) {
                    organisation.currentMonthPostCount = 0;
                }
                const currentMonthPosts = await this.getCurrentMonthPosts(organisation.id, monthEndThreeDaysAgo);
                await Promise.all([
                    this.organisationsService.updateById(organisation.id, {
                        currentMonthPostCount: currentMonthPosts.length + 1,
                    }, { transaction }),
                    this.achievementsService.update({
                        where: {
                            organisationId: organisation.id,
                            type: achievements_args_1.AchievementType.PowerScroller,
                        },
                        update: { steps: currentMonthPosts.length + 1 },
                        transaction,
                    }),
                ]);
            }
            if (!isTransactionSet) {
                await transaction.commit();
            }
            return post;
        }
        catch (e) {
            console.log(e);
            if (!isTransactionSet) {
                await transaction.rollback();
            }
            this.errorHelper.throwHttpException(`PostsService.createPost`, e.message);
        }
    }
    updatePost(id, updatePostInput, options) {
        this.logger.info('PostsService.updatePost', {
            id,
            updatePostInput,
            profileId: options.profileId,
        });
        return this.updateById(id, updatePostInput);
    }
    async updatePostScheduleTime(id, updatePostInput, options) {
        this.logger.info('PostsService.updatePostScheduleTime', {
            id,
            updatePostInput,
            profileId: options.profileId,
        });
        await this.streamPostsService.updateStreamPostScheduleTime(id, options.profileId, updatePostInput.scheduledAt);
        return this.updateById(id, updatePostInput);
    }
    async removePost(id, options) {
        this.logger.info('PostsService.removePost', {
            id,
            profileId: options.profileId,
        });
        const post = await this.findById(id);
        const isTransactionSet = !!options.transaction;
        const transaction = options.transaction || (await this.sequelize.transaction());
        try {
            if (post.eventId || post.incentiveId || post.webinarId) {
                await this.notificationsService.remove({
                    where: {
                        data: {
                            postId: post.id,
                        },
                    },
                });
            }
            await this.remove({ where: { id }, transaction: options.transaction });
            const startOfMonth = (0, moment_1.default)().startOf('month').toDate();
            const monthEndThreeDaysAgo = (0, moment_1.default)()
                .endOf('month')
                .subtract(3, 'days')
                .toDate();
            let postCreatedAt = new Date(post.createdAt);
            if (post.scheduledAt) {
                postCreatedAt = new Date(post.scheduledAt);
            }
            const postCreatedThisMonth = postCreatedAt >= startOfMonth && postCreatedAt <= monthEndThreeDaysAgo;
            const organisation = await this.organisationsService.findById(post.organisationId);
            if (organisation &&
                organisation.currentMonthPostCount > 0 &&
                postCreatedThisMonth) {
                organisation.currentMonthPostCount -= 1;
                await this.organisationsService.updateById(organisation.id, {
                    currentMonthPostCount: organisation.currentMonthPostCount,
                });
                const achievementsToUpdate = await this.achievementsService.findAll({
                    organisationId: organisation.id,
                    type: achievements_args_1.AchievementType.PowerScroller,
                });
                await Promise.all(achievementsToUpdate.map(async (achievement) => {
                    if (achievement.steps > 0) {
                        await this.achievementsService.decrementStepsComplete({
                            achievement,
                            post: post,
                        });
                    }
                }));
            }
            const isPartnerPost = post.parentOrgId !== null && post.parentOrgId !== undefined;
            const createdByProfile = await this.profilesService.findById(options.profileId);
            if (isPartnerPost) {
                const childOrgResult = await this.organisationLoyaltyPointsService.findOne({
                    where: {
                        organisationId: organisation.id,
                        parentOrganisationId: post.parentOrgId,
                        [sequelize_1.Op.and]: [
                            sequelize_typescript_1.Sequelize.where(sequelize_typescript_1.Sequelize.fn('jsonb_extract_path_text', sequelize_typescript_1.Sequelize.col('placeholders'), 'postId'), post.id),
                        ],
                    },
                });
                if (childOrgResult) {
                    const variableRewardPoints = childOrgResult && (childOrgResult === null || childOrgResult === void 0 ? void 0 : childOrgResult.points) === 200 ? -200 : 0;
                    await this.organisationLoyaltyPointsService.addPoints({
                        type: activities_args_1.ActivityType.RemovePost,
                        organisationId: organisation.id,
                        parentOrgId: post.parentOrgId,
                        transaction,
                        variableRewardPoints,
                        placeholders: {
                            postId: post.id,
                            deleted: true,
                            postCreatedAt: post.createdAt,
                            deletedAt: new Date(),
                            name: createdByProfile.name,
                            createdById: createdByProfile.id,
                            parentOrgId: post.parentOrgId,
                            isPartnerPost: true
                        },
                    });
                }
            }
            else {
                const result = await this.organisationLoyaltyPointsService.findOne({
                    where: {
                        organisationId: organisation.id,
                        [sequelize_1.Op.and]: [
                            sequelize_typescript_1.Sequelize.where(sequelize_typescript_1.Sequelize.fn('jsonb_extract_path_text', sequelize_typescript_1.Sequelize.col('placeholders'), 'postId'), post.id),
                        ],
                    },
                });
                if (result) {
                    const variableRewardPoints = result && (result === null || result === void 0 ? void 0 : result.points) === 200 ? -200 : 0;
                    await this.organisationLoyaltyPointsService.addPoints({
                        type: activities_args_1.ActivityType.RemovePost,
                        organisationId: organisation.id,
                        transaction,
                        variableRewardPoints,
                        placeholders: {
                            postId: post.id,
                            deleted: true,
                            postCreatedAt: post.createdAt,
                            deletedAt: new Date(),
                            name: createdByProfile.name,
                            createdById: createdByProfile.id
                        },
                    });
                }
            }
            if (isPartnerPost) {
                await organisation_activity_model_1.OrganisationActivity.create({
                    createdById: options.profileId,
                    organisationId: organisation.id,
                    parentOrganisationId: post.parentOrgId,
                    data: {
                        postId: post.id,
                        deleted: true,
                        name: createdByProfile.name,
                        isPartnerPost: true,
                        parentOrgId: post.parentOrgId
                    },
                    type: 'RemovePost',
                }, { transaction });
            }
            else {
                await organisation_activity_model_1.OrganisationActivity.create({
                    createdById: options.profileId,
                    organisationId: organisation.id,
                    parentOrganisationId: null,
                    data: {
                        postId: post.id,
                        deleted: true,
                        name: createdByProfile.name
                    },
                    type: 'RemovePost',
                }, { transaction });
            }
            if (!isTransactionSet) {
                await transaction.commit();
            }
        }
        catch (e) {
            if (!isTransactionSet) {
                await transaction.rollback();
            }
            this.errorHelper.throwHttpException(`PostsService.removePost`, e.message);
        }
    }
    async seenPost(id, options) {
        this.logger.info('PostsService.seenPost', {
            id,
            profileId: options.profileId,
        });
        const isTransactionSet = !!(options === null || options === void 0 ? void 0 : options.transaction);
        const transaction = options.transaction || (await this.sequelize.transaction());
        try {
            const post = await this.findById(id, { transaction });
            if (!post.seenBy.includes(options.profileId)) {
                const updatedPost = await this.updateById(id, {
                    totalViews: (post.totalViews || 0) + 1,
                    seenBy: underscore_1.Underscore.uniq([...post.seenBy, options.profileId]),
                }, { transaction });
                await this.activitiesService.create({
                    type: activities_args_1.ActivityType.PostSeen,
                    profileId: options.profileId,
                    organisationId: post.organisationId,
                    postId: updatedPost.id,
                }, {
                    transaction,
                });
                const organisation = await this.organisationsService.findById(post.organisationId, {
                    transaction,
                });
                if (!organisation.seenAnyPostBy.includes(options.profileId)) {
                    await this.organisationsService.updateById(post.organisationId, {
                        seenAnyPostBy: underscore_1.Underscore.uniq([
                            ...organisation.seenAnyPostBy,
                            options.profileId,
                        ]),
                    }, { transaction });
                }
                if (!isTransactionSet) {
                    await transaction.commit();
                }
                return updatedPost;
            }
            else {
                const updatedPost = await this.updateById(id, {
                    totalViews: (post.totalViews || 0) + 1,
                }, { transaction });
                if (!isTransactionSet) {
                    await transaction.commit();
                }
                return updatedPost;
            }
        }
        catch (e) {
            if (!isTransactionSet) {
                await transaction.rollback();
            }
        }
    }
    async findPosts(profileId, filter, pagination) {
        this.logger.verbose('PostsService.findPosts', {
            profileId,
            filter,
            pagination,
        });
        const paginationPostResult = await this.postsRepository.findPosts(profileId, filter, pagination);
        return {
            records: paginationPostResult.records,
            totalCount: paginationPostResult.totalCount,
        };
    }
    async scheduledPostList(options, organisationId, parentOrganisationId) {
        var _a;
        this.logger.info('PostsService.scheduledPostList', {
            currentUser: (_a = options.currentUser) === null || _a === void 0 ? void 0 : _a.toLogObject(),
        });
        let where = {
            status: posts_args_1.PostStatus.Scheduled,
        };
        if (parentOrganisationId && organisationId) {
            where = Object.assign(Object.assign({}, where), { organisationId: organisationId, parentOrgId: parentOrganisationId });
        }
        else if (organisationId) {
            where = Object.assign(Object.assign({}, where), { organisationId: organisationId, parentOrgId: null });
        }
        try {
            return await this.findAll(where, {
                order: [['scheduledAt', 'ASC']],
            });
        }
        catch (e) {
            this.errorHelper.throwHttpException(`PostsService.scheduledPostList`, e.message);
        }
    }
    async schedulePostsEveryMinScript() {
        this.logger.info('PostsService.schedulePostsEveryMinScript', {});
        const now = (0, moment_1.default)().format('YYYY-MM-DDTHH:mm:ss');
        const posts = await this.findAll({
            status: posts_args_1.PostStatus.Scheduled,
            scheduledAt: {
                [sequelize_1.Op.and]: {
                    [sequelize_1.Op.lt]: (0, moment_1.default)(now).startOf('minute').add(1, 'minute').toDate(),
                },
            },
        });
        const transaction = await this.sequelize.transaction();
        try {
            if (posts.length) {
                for (const post of posts) {
                    const currPost = await this.streamPostsService.getStreamPostById(post.id);
                    const membership = await this.membershipsService.findMembership(post.organisationId, post.profileId);
                    if (currPost) {
                        await this.streamPostsService.publishPost(post.profileId, currPost, membership);
                        const updatedPost = await this.updateById(post.id, {
                            status: posts_args_1.PostStatus.Live,
                        }, { transaction });
                        const organisation = await this.organisationsService.findById(updatedPost.organisationId);
                        if (organisation && organisation.hasClubHabloSubscription) {
                            const currentMonth = (0, moment_1.default)().month();
                            const lastPost = await this.findAll({
                                organisationId: organisation.id,
                            }, { order: [['createdAt', 'DESC']] });
                            const lastPostMonth = lastPost.length
                                ? (0, moment_1.default)(lastPost[0].createdAt).month()
                                : -1;
                            const scheduledAt = (0, moment_1.default)(post.scheduledAt);
                            const monthEndThreeDaysAgo = (0, moment_1.default)()
                                .endOf('month')
                                .subtract(3, 'days')
                                .toDate();
                            const isLastThreeDaysOfMonth = scheduledAt.isSameOrAfter(monthEndThreeDaysAgo);
                            const isNextMonth = scheduledAt.month() !== currentMonth;
                            if (!isLastThreeDaysOfMonth && !isNextMonth) {
                                if (currentMonth !== lastPostMonth) {
                                    organisation.currentMonthPostCount = 0;
                                }
                                const currentMonthPosts = await this.getCurrentMonthPosts(organisation.id, monthEndThreeDaysAgo);
                                await Promise.all([
                                    this.organisationsService.updateById(organisation.id, {
                                        currentMonthPostCount: currentMonthPosts.length + 1,
                                    }, { transaction }),
                                    this.achievementsService.update({
                                        where: {
                                            organisationId: organisation.id,
                                            type: achievements_args_1.AchievementType.PowerScroller,
                                        },
                                        update: { steps: currentMonthPosts.length + 1 },
                                        transaction,
                                    }),
                                ]);
                            }
                        }
                        const createdByProfile = await this.profilesService.findById(post.profileId);
                        const loyaltyPointEntry = await this.organisationLoyaltyPointsService.addPoints({
                            type: activities_args_1.ActivityType.CreatePost,
                            organisationId: updatedPost.organisationId,
                            placeholders: {
                                postId: updatedPost.id,
                                postContent: updatedPost.text,
                                streamActivityId: updatedPost.id,
                                createdById: createdByProfile.id,
                                name: createdByProfile.name,
                            },
                        });
                        await this.activitiesService.create({
                            profileId: post.profileId,
                            data: loyaltyPointEntry,
                            type: 'CreatePost',
                        });
                    }
                }
            }
            await transaction.commit();
            return true;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`PostsService.postsScheduled`, e.message);
        }
    }
    async getCurrentMonthPosts(organisationId, monthEndThreeDaysAgo) {
        const currentMonthPost = await this.findAll({
            organisationId,
            [sequelize_1.Op.or]: [
                {
                    [sequelize_1.Op.and]: [
                        {
                            scheduledAt: {
                                [sequelize_1.Op.gte]: (0, moment_1.default)().startOf('month').toDate(),
                                [sequelize_1.Op.lte]: new Date() && monthEndThreeDaysAgo,
                            },
                        },
                        {
                            status: posts_args_1.PostStatus.Live,
                        },
                    ],
                },
                {
                    [sequelize_1.Op.and]: [
                        {
                            status: {
                                [sequelize_1.Op.or]: [posts_args_1.PostStatus.Live, posts_args_1.PostStatus.Repost],
                            },
                        },
                        {
                            createdAt: {
                                [sequelize_1.Op.gte]: (0, moment_1.default)().startOf('month').toDate(),
                                [sequelize_1.Op.lte]: monthEndThreeDaysAgo,
                            },
                        },
                    ],
                },
            ],
        });
        return currentMonthPost.map(post => post.id);
    }
    async countPartnerPosts(options) {
        this.logger.info('PostsService.countPartnerPosts', options);
        try {
            const count = await this.count({
                organisationId: options.childOrgId,
                parentOrgId: options.parentOrgId,
                createdAt: {
                    [sequelize_1.Op.between]: [options.startDate, options.endDate],
                },
            });
            return count;
        }
        catch (error) {
            this.logger.error('Error counting partner posts', error);
            return 0;
        }
    }
    async isParentOrganization(organisationId) {
        try {
            const childOrgs = await this.partnerOrganisationsService.findAll({
                parentOrganisationId: organisationId,
                status: partner_organisations_args_1.PartnerOrganisationStatus.Approved,
            });
            return childOrgs.length > 0;
        }
        catch (error) {
            this.logger.error('Error checking if organization is a parent organization', {
                organisationId,
                error,
            });
            return false;
        }
    }
};
exports.PostsService = PostsService;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => posts_repository_1.PostsRepository)),
    __metadata("design:type", posts_repository_1.PostsRepository)
], PostsService.prototype, "postsRepository", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => activities_service_1.ActivitiesService)),
    __metadata("design:type", activities_service_1.ActivitiesService)
], PostsService.prototype, "activitiesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], PostsService.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => notifications_service_1.NotificationsService)),
    __metadata("design:type", notifications_service_1.NotificationsService)
], PostsService.prototype, "notificationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => event_invitations_service_1.EventInvitationsService)),
    __metadata("design:type", event_invitations_service_1.EventInvitationsService)
], PostsService.prototype, "eventInvitationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentive_participants_service_1.IncentiveParticipantsService)),
    __metadata("design:type", incentive_participants_service_1.IncentiveParticipantsService)
], PostsService.prototype, "incentiveParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinar_participants_service_1.WebinarParticipantsService)),
    __metadata("design:type", webinar_participants_service_1.WebinarParticipantsService)
], PostsService.prototype, "webinarParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => stream_updates_service_1.StreamUpdatesService)),
    __metadata("design:type", stream_updates_service_1.StreamUpdatesService)
], PostsService.prototype, "streamUpdatesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], PostsService.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => events_service_1.EventsService)),
    __metadata("design:type", events_service_1.EventsService)
], PostsService.prototype, "eventsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinars_service_1.WebinarsService)),
    __metadata("design:type", webinars_service_1.WebinarsService)
], PostsService.prototype, "webinarsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentives_service_1.IncentivesService)),
    __metadata("design:type", incentives_service_1.IncentivesService)
], PostsService.prototype, "incentivesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => stream_posts_service_1.StreamPostsService)),
    __metadata("design:type", stream_posts_service_1.StreamPostsService)
], PostsService.prototype, "streamPostsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], PostsService.prototype, "membershipsService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", sequelize_typescript_1.Sequelize)
], PostsService.prototype, "sequelize", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], PostsService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], PostsService.prototype, "errorHelper", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisation_loyalty_points_service_1.OrganisationLoyaltyPointsService)),
    __metadata("design:type", organisation_loyalty_points_service_1.OrganisationLoyaltyPointsService)
], PostsService.prototype, "organisationLoyaltyPointsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => achievements_service_1.AchievementsService)),
    __metadata("design:type", achievements_service_1.AchievementsService)
], PostsService.prototype, "achievementsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => partner_organisations_service_1.PartnerOrganisationsService)),
    __metadata("design:type", partner_organisations_service_1.PartnerOrganisationsService)
], PostsService.prototype, "partnerOrganisationsService", void 0);
exports.PostsService = PostsService = __decorate([
    (0, common_1.Injectable)()
], PostsService);
//# sourceMappingURL=posts.service.js.map