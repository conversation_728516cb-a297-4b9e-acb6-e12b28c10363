"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfilesArgs = exports.ProfilesFilter = exports.DeviceLogInCount = exports.OnlineStatus = exports.Region = exports.ProfileTypeOfHoliday = exports.ProfileResponsibility = exports.ProfileConnectionStatus = void 0;
const graphql_1 = require("@nestjs/graphql");
const pagination_args_1 = require("../../common/args/pagination.args");
const membership_model_1 = require("../../memberships/models/membership.model");
const follower_model_1 = require("../../followers/models/follower.model");
var ProfileConnectionStatus;
(function (ProfileConnectionStatus) {
    ProfileConnectionStatus["Connection"] = "Connection";
    ProfileConnectionStatus["InvitationSent"] = "InvitationSent";
    ProfileConnectionStatus["InvitationReceived"] = "InvitationReceived";
    ProfileConnectionStatus["InvitationSentRejected"] = "InvitationSentRejected";
    ProfileConnectionStatus["InvitationReceivedRejected"] = "InvitationReceivedRejected";
    ProfileConnectionStatus["None"] = "None";
})(ProfileConnectionStatus || (exports.ProfileConnectionStatus = ProfileConnectionStatus = {}));
(0, graphql_1.registerEnumType)(ProfileConnectionStatus, { name: 'ProfileConnectionStatus' });
var ProfileResponsibility;
(function (ProfileResponsibility) {
    ProfileResponsibility["Events"] = "Events";
    ProfileResponsibility["Marketing"] = "Marketing";
    ProfileResponsibility["Partnerships"] = "Partnerships";
    ProfileResponsibility["ProductAndContracting"] = "Product & Contracting";
    ProfileResponsibility["PublicRelations"] = "Public Relations";
    ProfileResponsibility["Sales"] = "Sales";
    ProfileResponsibility["SalesAgent"] = "SalesAgent";
    ProfileResponsibility["SalesManager"] = "Sales Manager";
    ProfileResponsibility["SeniorManagement"] = "Senior Management";
    ProfileResponsibility["SocialAndDigital"] = "Social & Digital";
    ProfileResponsibility["Training"] = "Training";
    ProfileResponsibility["Other"] = "Other";
})(ProfileResponsibility || (exports.ProfileResponsibility = ProfileResponsibility = {}));
(0, graphql_1.registerEnumType)(ProfileResponsibility, { name: 'ProfileResponsibility' });
var ProfileTypeOfHoliday;
(function (ProfileTypeOfHoliday) {
    ProfileTypeOfHoliday["Business"] = "Business";
    ProfileTypeOfHoliday["CityBreaks"] = "City Breaks";
    ProfileTypeOfHoliday["Cruises"] = "Cruises";
    ProfileTypeOfHoliday["Educational"] = "Educational";
    ProfileTypeOfHoliday["EscortedTours"] = "Escorted Tours";
    ProfileTypeOfHoliday["Expedition"] = "Expedition";
    ProfileTypeOfHoliday["FlyDrive"] = "Fly Drive";
    ProfileTypeOfHoliday["Groups"] = "Groups";
    ProfileTypeOfHoliday["Leisure"] = "Leisure";
    ProfileTypeOfHoliday["Luxury"] = "Luxury";
    ProfileTypeOfHoliday["Niche"] = "Niche";
    ProfileTypeOfHoliday["PackageHolidays"] = "Package Holidays";
    ProfileTypeOfHoliday["Rail"] = "Rail";
    ProfileTypeOfHoliday["RoundTheWorld"] = "Round the World";
    ProfileTypeOfHoliday["Solo"] = "Solo";
})(ProfileTypeOfHoliday || (exports.ProfileTypeOfHoliday = ProfileTypeOfHoliday = {}));
(0, graphql_1.registerEnumType)(ProfileTypeOfHoliday, { name: 'ProfileTypeOfHoliday' });
var Region;
(function (Region) {
    Region["UnitedStatesOfAmerica"] = "United States of America";
    Region["Africa"] = "Africa";
    Region["Asia"] = "Asia";
    Region["Canada"] = "Canada";
    Region["TheCaribbean"] = "The Caribbean";
    Region["CentralAmerica"] = "Central America";
    Region["Europe"] = "Europe";
    Region["Mexico"] = "Mexico";
    Region["MiddleEast"] = "Middle East";
    Region["Oceania"] = "Oceania (Australia & New Zealand)";
    Region["SouthAmerica"] = "SouthAmerica";
    Region["All"] = "All Users";
})(Region || (exports.Region = Region = {}));
(0, graphql_1.registerEnumType)(Region, { name: 'Region' });
var OnlineStatus;
(function (OnlineStatus) {
    OnlineStatus["Online"] = "Online";
    OnlineStatus["Offline"] = "Offline";
    OnlineStatus["Away"] = "Away";
})(OnlineStatus || (exports.OnlineStatus = OnlineStatus = {}));
(0, graphql_1.registerEnumType)(OnlineStatus, { name: 'OnlineStatus' });
var DeviceLogInCount;
(function (DeviceLogInCount) {
    DeviceLogInCount["Increment"] = "Increment";
    DeviceLogInCount["Decrement"] = "Decrement";
})(DeviceLogInCount || (exports.DeviceLogInCount = DeviceLogInCount = {}));
(0, graphql_1.registerEnumType)(DeviceLogInCount, { name: 'DeviceLogInCount' });
let ProfilesFilter = class ProfilesFilter {
};
exports.ProfilesFilter = ProfilesFilter;
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], ProfilesFilter.prototype, "organisationId", void 0);
__decorate([
    (0, graphql_1.Field)(() => [follower_model_1.FollowerStatus], { nullable: true }),
    __metadata("design:type", Array)
], ProfilesFilter.prototype, "followerStatus", void 0);
__decorate([
    (0, graphql_1.Field)(() => [membership_model_1.MembershipStatus], { nullable: true }),
    __metadata("design:type", Array)
], ProfilesFilter.prototype, "membershipStatus", void 0);
__decorate([
    (0, graphql_1.Field)(() => [membership_model_1.MembershipPermission], { nullable: true }),
    __metadata("design:type", Array)
], ProfilesFilter.prototype, "membershipPermissions", void 0);
__decorate([
    (0, graphql_1.Field)(() => [ProfileConnectionStatus], { nullable: true, defaultValue: [] }),
    __metadata("design:type", Array)
], ProfilesFilter.prototype, "status", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], ProfilesFilter.prototype, "searchText", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], ProfilesFilter.prototype, "includeOwnProfile", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], ProfilesFilter.prototype, "searchLocationText", void 0);
__decorate([
    (0, graphql_1.Field)(() => [ProfileResponsibility], { nullable: true }),
    __metadata("design:type", Array)
], ProfilesFilter.prototype, "responsibilities", void 0);
__decorate([
    (0, graphql_1.Field)(() => [ProfileResponsibility], { nullable: true }),
    __metadata("design:type", Array)
], ProfilesFilter.prototype, "typesOfHoliday", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], ProfilesFilter.prototype, "connectedOrgProfiles", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], ProfilesFilter.prototype, "searchOrgNameAndJob", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true, defaultValue: false }),
    __metadata("design:type", Boolean)
], ProfilesFilter.prototype, "isFuzzySearch", void 0);
exports.ProfilesFilter = ProfilesFilter = __decorate([
    (0, graphql_1.InputType)()
], ProfilesFilter);
let ProfilesArgs = class ProfilesArgs extends pagination_args_1.PaginationArgs {
};
exports.ProfilesArgs = ProfilesArgs;
__decorate([
    (0, graphql_1.Field)(() => ProfilesFilter, { nullable: true }),
    __metadata("design:type", ProfilesFilter)
], ProfilesArgs.prototype, "filter", void 0);
exports.ProfilesArgs = ProfilesArgs = __decorate([
    (0, graphql_1.ArgsType)()
], ProfilesArgs);
//# sourceMappingURL=profiles.args.js.map