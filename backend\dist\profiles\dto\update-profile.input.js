"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateProfileInput = void 0;
const class_validator_1 = require("class-validator");
const graphql_1 = require("@nestjs/graphql");
const graphql_type_json_1 = require("graphql-type-json");
const profiles_args_1 = require("../args/profiles.args");
let UpdateProfileInput = class UpdateProfileInput {
};
exports.UpdateProfileInput = UpdateProfileInput;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    (0, class_validator_1.MaxLength)(128),
    __metadata("design:type", String)
], UpdateProfileInput.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], UpdateProfileInput.prototype, "image", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], UpdateProfileInput.prototype, "backgroundImage", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Date)
], UpdateProfileInput.prototype, "dateOfBirth", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], UpdateProfileInput.prototype, "gender", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], UpdateProfileInput.prototype, "bio", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], UpdateProfileInput.prototype, "headline", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)(() => [profiles_args_1.Region], { nullable: true }),
    __metadata("design:type", Array)
], UpdateProfileInput.prototype, "regions", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)(() => [profiles_args_1.ProfileResponsibility], { nullable: true }),
    __metadata("design:type", Array)
], UpdateProfileInput.prototype, "responsibilities", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)(() => graphql_type_json_1.GraphQLJSON, { nullable: true }),
    __metadata("design:type", Object)
], UpdateProfileInput.prototype, "location", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)(() => [profiles_args_1.ProfileTypeOfHoliday], { nullable: true }),
    __metadata("design:type", Array)
], UpdateProfileInput.prototype, "typesOfHoliday", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], UpdateProfileInput.prototype, "phoneNumber", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], UpdateProfileInput.prototype, "timezone", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)(() => graphql_type_json_1.GraphQLJSON, { nullable: true }),
    __metadata("design:type", Object)
], UpdateProfileInput.prototype, "onboarding", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], UpdateProfileInput.prototype, "receiveNotificationEmails", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], UpdateProfileInput.prototype, "receiveNewMessagesEmails", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], UpdateProfileInput.prototype, "useAutofollow", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)(() => graphql_type_json_1.GraphQLJSON, { nullable: true }),
    __metadata("design:type", Object)
], UpdateProfileInput.prototype, "notificationPreference", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({
        nullable: true,
    }),
    __metadata("design:type", Boolean)
], UpdateProfileInput.prototype, "sellHolidays", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({
        nullable: true,
    }),
    __metadata("design:type", Date)
], UpdateProfileInput.prototype, "showGettingStartedFeedAt", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, graphql_1.Field)({
        nullable: true,
    }),
    __metadata("design:type", String)
], UpdateProfileInput.prototype, "primaryMembershipId", void 0);
exports.UpdateProfileInput = UpdateProfileInput = __decorate([
    (0, graphql_1.InputType)()
], UpdateProfileInput);
//# sourceMappingURL=update-profile.input.js.map