"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfilesRepositoryHelper = void 0;
const sequelize_1 = require("sequelize");
const common_1 = require("@nestjs/common");
const underscore_1 = require("../../common/helpers/underscore");
const memberships_service_1 = require("../../memberships/memberships.service");
const followers_service_1 = require("../../followers/followers.service");
const organisations_service_1 = require("../../organisations/organisations.service");
const organisations_args_1 = require("../../organisations/args/organisations.args");
let ProfilesRepositoryHelper = class ProfilesRepositoryHelper {
    async getMembershipProfileIds(organisationId, filters) {
        const queryParams = { organisationId };
        const organisation = await this.organisationsService.findById(organisationId);
        if (organisation.status !== organisations_args_1.OrganisationStatus.Active) {
            return [];
        }
        if (filters === null || filters === void 0 ? void 0 : filters.status) {
            queryParams.status = {
                [sequelize_1.Op.in]: filters === null || filters === void 0 ? void 0 : filters.status,
            };
        }
        if ((filters === null || filters === void 0 ? void 0 : filters.permissions) && (filters === null || filters === void 0 ? void 0 : filters.permissions.length) > 0) {
            queryParams.permissions = {
                [sequelize_1.Op.overlap]: filters === null || filters === void 0 ? void 0 : filters.permissions,
            };
        }
        const memberships = await this.membershipsService.findAll(queryParams, {
            attributes: ['profileId'],
            useCache: true,
        });
        return underscore_1.Underscore.map(memberships, 'profileId');
    }
    async getFollowerProfileIds(organisationId, filters) {
        const queryParams = { organisationId };
        const organisation = await this.organisationsService.findById(organisationId);
        if (organisation.status !== organisations_args_1.OrganisationStatus.Active) {
            return [];
        }
        if (filters === null || filters === void 0 ? void 0 : filters.status) {
            queryParams.status = {
                [sequelize_1.Op.in]: filters === null || filters === void 0 ? void 0 : filters.status,
            };
        }
        const followers = await this.followersService.findAll(queryParams, {
            attributes: ['profileId'],
        });
        return underscore_1.Underscore.map(followers, 'profileId');
    }
};
exports.ProfilesRepositoryHelper = ProfilesRepositoryHelper;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], ProfilesRepositoryHelper.prototype, "membershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => followers_service_1.FollowersService)),
    __metadata("design:type", followers_service_1.FollowersService)
], ProfilesRepositoryHelper.prototype, "followersService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], ProfilesRepositoryHelper.prototype, "organisationsService", void 0);
exports.ProfilesRepositoryHelper = ProfilesRepositoryHelper = __decorate([
    (0, common_1.Injectable)()
], ProfilesRepositoryHelper);
//# sourceMappingURL=profiles.repository.helper.js.map