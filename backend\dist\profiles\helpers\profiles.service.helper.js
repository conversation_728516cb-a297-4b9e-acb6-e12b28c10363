"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfilesServiceHelper = void 0;
const sequelize_1 = require("sequelize");
const connections_service_1 = require("../../connections/connections.service");
const connection_request_model_1 = require("../../connection-requests/models/connection-request.model");
const common_1 = require("@nestjs/common");
const underscore_1 = require("../../common/helpers/underscore");
const connection_requests_service_1 = require("../../connection-requests/connection-requests.service");
const winston_1 = require("winston");
const nest_winston_1 = require("nest-winston");
let ProfilesServiceHelper = class ProfilesServiceHelper {
    async getConnectionProfileIds(profileId) {
        const connections = await this.connectionsService.findAll({ profileId }, { attributes: ['id', 'connectionProfileId'] });
        return underscore_1.Underscore.map(connections, 'connectionProfileId');
    }
    async getInvitationSentProfileIds(profileId) {
        const connectionRequests = await this.connectionRequestsService.findAll({
            status: connection_request_model_1.ConnectionRequestStatus.Pending,
            senderProfileId: profileId,
        }, { attributes: ['id', 'receiverProfileId'] });
        return underscore_1.Underscore.map(connectionRequests, 'receiverProfileId');
    }
    async getInvitationSentRejectedProfileIds(profileId) {
        const connectionRequests = await this.connectionRequestsService.findAll({
            status: connection_request_model_1.ConnectionRequestStatus.Rejected,
            senderProfileId: profileId,
        }, { attributes: ['id', 'receiverProfileId'] });
        return underscore_1.Underscore.map(connectionRequests, 'receiverProfileId');
    }
    async getInvitationReceivedProfileIds(profileId) {
        const connectionRequests = await this.connectionRequestsService.findAll({
            status: connection_request_model_1.ConnectionRequestStatus.Pending,
            receiverProfileId: profileId,
        }, { attributes: ['id', 'senderProfileId'] });
        return underscore_1.Underscore.map(connectionRequests, 'senderProfileId');
    }
    async getAllInvitationProfileIds(profileId) {
        const receivedConnectionRequests = await this.connectionRequestsService.findAll({
            status: {
                [sequelize_1.Op.in]: [
                    connection_request_model_1.ConnectionRequestStatus.Pending,
                    connection_request_model_1.ConnectionRequestStatus.Rejected,
                ],
            },
            receiverProfileId: profileId,
        }, { attributes: ['id', 'senderProfileId'] });
        const sentConnectionRequests = await this.connectionRequestsService.findAll({
            status: {
                [sequelize_1.Op.in]: [
                    connection_request_model_1.ConnectionRequestStatus.Pending,
                    connection_request_model_1.ConnectionRequestStatus.Rejected,
                ],
            },
            senderProfileId: profileId,
        }, { attributes: ['id', 'receiverProfileId'] });
        return [
            ...underscore_1.Underscore.map(receivedConnectionRequests, 'senderProfileId'),
            ...underscore_1.Underscore.map(sentConnectionRequests, 'receiverProfileId'),
        ];
    }
    async getInvitationReceivedRejectedProfileIds(profileId) {
        const connectionRequests = await this.connectionRequestsService.findAll({
            status: connection_request_model_1.ConnectionRequestStatus.Rejected,
            receiverProfileId: profileId,
        }, { attributes: ['id', 'senderProfileId'] });
        return underscore_1.Underscore.map(connectionRequests, 'senderProfileId');
    }
};
exports.ProfilesServiceHelper = ProfilesServiceHelper;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => connections_service_1.ConnectionsService)),
    __metadata("design:type", connections_service_1.ConnectionsService)
], ProfilesServiceHelper.prototype, "connectionsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => connection_requests_service_1.ConnectionRequestsService)),
    __metadata("design:type", connection_requests_service_1.ConnectionRequestsService)
], ProfilesServiceHelper.prototype, "connectionRequestsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], ProfilesServiceHelper.prototype, "logger", void 0);
exports.ProfilesServiceHelper = ProfilesServiceHelper = __decorate([
    (0, common_1.Injectable)()
], ProfilesServiceHelper);
//# sourceMappingURL=profiles.service.helper.js.map