"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfileWithConnectionsOnly = void 0;
const organisation_model_1 = require("./../../organisations/models/organisation.model");
const membership_model_1 = require("./../../memberships/models/membership.model");
const sequelize_typescript_1 = require("sequelize-typescript");
const graphql_1 = require("@nestjs/graphql");
const connection_model_1 = require("../../connections/models/connection.model");
const profile_model_1 = require("./profile.model");
const sequelize_typescript_2 = require("sequelize-typescript");
let ProfileWithConnectionsOnly = class ProfileWithConnectionsOnly extends sequelize_typescript_2.Model {
};
exports.ProfileWithConnectionsOnly = ProfileWithConnectionsOnly;
ProfileWithConnectionsOnly.ConnectionsIncludeRule = {
    model: connection_model_1.Connection,
    as: 'connections',
    include: [
        {
            model: profile_model_1.Profile,
            as: 'profile',
            include: [
                {
                    as: 'memberships',
                    model: membership_model_1.Membership,
                    attributes: [
                        'id',
                        'position',
                        'isPrimary',
                        'organisationName',
                        'organisationId',
                        'status',
                    ],
                    include: [
                        {
                            as: 'organisation',
                            model: organisation_model_1.Organisation,
                            attributes: [
                                'name',
                                'id',
                                'vanityId',
                                'privacy',
                                'additionalPrivacy',
                                'followingPrivacy',
                                'peoplePrivacy',
                                'image',
                            ],
                        },
                    ],
                },
            ],
        },
    ],
};
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    __metadata("design:type", String)
], ProfileWithConnectionsOnly.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(() => [connection_model_1.Connection]),
    (0, sequelize_typescript_1.HasMany)(() => connection_model_1.Connection, 'connectionProfileId'),
    __metadata("design:type", Array)
], ProfileWithConnectionsOnly.prototype, "connections", void 0);
exports.ProfileWithConnectionsOnly = ProfileWithConnectionsOnly = __decorate([
    (0, graphql_1.ObjectType)()
], ProfileWithConnectionsOnly);
//# sourceMappingURL=profile-only-with-connetions.js.map