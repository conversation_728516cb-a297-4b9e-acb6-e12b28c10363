"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var Profile_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.Profile = exports.ProfileParentOrganisationObj = void 0;
const moment_1 = __importDefault(require("moment"));
const sequelize_typescript_1 = require("sequelize-typescript");
const connection_model_1 = require("../../connections/models/connection.model");
const notification_model_1 = require("../../notifications/models/notification.model");
const graphql_1 = require("@nestjs/graphql");
const profiles_args_1 = require("../args/profiles.args");
const sequelize_1 = require("sequelize");
const graphql_type_json_1 = require("graphql-type-json");
const membership_model_1 = require("../../memberships/models/membership.model");
const organisation_model_1 = require("../../organisations/models/organisation.model");
const getting_started_step_model_1 = require("../../getting-started-steps/models/getting-started-step.model");
let ProfileParentOrganisationObj = class ProfileParentOrganisationObj {
};
exports.ProfileParentOrganisationObj = ProfileParentOrganisationObj;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], ProfileParentOrganisationObj.prototype, "id", void 0);
exports.ProfileParentOrganisationObj = ProfileParentOrganisationObj = __decorate([
    (0, graphql_1.ObjectType)()
], ProfileParentOrganisationObj);
let Profile = Profile_1 = class Profile extends sequelize_typescript_1.Model {
    get isComplete() {
        const fields = ['email', 'name', 'dateOfBirth', 'gender', 'location'];
        for (const field of fields) {
            const dataValue = this.getDataValue(field);
            if (!dataValue) {
                return false;
            }
        }
        return true;
    }
    get onlineStatus() {
        const seconds = this.lastOnlineAt
            ? (0, moment_1.default)().diff(this.lastOnlineAt, 'seconds')
            : undefined;
        if (seconds < 70) {
            return profiles_args_1.OnlineStatus.Online;
        }
        else if (seconds < 900) {
            return profiles_args_1.OnlineStatus.Away;
        }
        else {
            return profiles_args_1.OnlineStatus.Offline;
        }
    }
};
exports.Profile = Profile;
Profile.ConnectionsIncludeRule = {
    model: connection_model_1.Connection,
    as: 'connections',
    include: [
        {
            model: Profile_1,
            as: 'profile',
            include: [
                {
                    as: 'memberships',
                    model: membership_model_1.Membership,
                    attributes: [
                        'id',
                        'position',
                        'isPrimary',
                        'organisationName',
                        'organisationId',
                        'hasClubHabloSubscription',
                        'status',
                    ],
                    include: [
                        {
                            as: 'organisation',
                            model: organisation_model_1.Organisation,
                        },
                    ],
                },
            ],
        },
    ],
};
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
    }),
    __metadata("design:type", String)
], Profile.prototype, "id", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ unique: true, allowNull: false }),
    __metadata("design:type", String)
], Profile.prototype, "authZeroUserId", void 0);
__decorate([
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Profile.prototype, "streamUserId", void 0);
__decorate([
    sequelize_typescript_1.Column,
    __metadata("design:type", Number)
], Profile.prototype, "suggestedFollowCount", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({ allowNull: false }),
    __metadata("design:type", String)
], Profile.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({ unique: true, allowNull: false }),
    __metadata("design:type", String)
], Profile.prototype, "email", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Profile.prototype, "image", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Profile.prototype, "backgroundImage", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.DATEONLY,
    }),
    __metadata("design:type", Date)
], Profile.prototype, "dateOfBirth", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Profile.prototype, "gender", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.TEXT,
    }),
    __metadata("design:type", String)
], Profile.prototype, "bio", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Profile.prototype, "headline", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_type_json_1.GraphQLJSON, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.JSON,
    }),
    __metadata("design:type", Object)
], Profile.prototype, "location", void 0);
__decorate([
    (0, graphql_1.Field)(() => [profiles_args_1.Region], { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
    }),
    __metadata("design:type", Array)
], Profile.prototype, "regions", void 0);
__decorate([
    (0, graphql_1.Field)(() => [profiles_args_1.ProfileResponsibility], { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
    }),
    __metadata("design:type", Array)
], Profile.prototype, "responsibilities", void 0);
__decorate([
    (0, graphql_1.Field)(() => [profiles_args_1.ProfileTypeOfHoliday], { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
    }),
    __metadata("design:type", Array)
], Profile.prototype, "typesOfHoliday", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Profile.prototype, "phoneNumber", void 0);
__decorate([
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Profile.prototype, "lastActivityAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.DATE,
        defaultValue: new Date(),
    }),
    __metadata("design:type", Date)
], Profile.prototype, "lastOnlineAt", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        defaultValue: 0,
    }),
    __metadata("design:type", Number)
], Profile.prototype, "timezoneOffset", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Profile.prototype, "timezone", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_type_json_1.GraphQLJSON, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.JSON,
    }),
    __metadata("design:type", Object)
], Profile.prototype, "onboarding", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)(sequelize_1.DataTypes.VIRTUAL),
    __metadata("design:type", Boolean),
    __metadata("design:paramtypes", [])
], Profile.prototype, "isComplete", null);
__decorate([
    (0, graphql_1.Field)(() => profiles_args_1.OnlineStatus),
    (0, sequelize_typescript_1.Column)(sequelize_1.DataTypes.VIRTUAL),
    __metadata("design:type", String),
    __metadata("design:paramtypes", [])
], Profile.prototype, "onlineStatus", null);
__decorate([
    (0, graphql_1.Field)(() => [ProfileParentOrganisationObj], { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.JSON),
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], Profile.prototype, "parentOrganisations", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({ defaultValue: 0 }),
    __metadata("design:type", Number)
], Profile.prototype, "noOfMobileDevicesLoggedIn", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_type_json_1.GraphQLJSON, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.JSONB,
        defaultValue: {
            events: ['push', 'sms', 'desktop', 'email'],
            messages: ['push', 'sms', 'desktop', 'email'],
            webinars: ['push', 'sms', 'desktop', 'email'],
            incentives: ['push', 'sms', 'desktop', 'email'],
            connections: ['push', 'sms', 'desktop', 'email'],
            interaction: ['push', 'sms', 'desktop', 'email'],
            invitations: ['push', 'sms', 'desktop', 'email'],
            mentionsInPosts: ['push', 'sms', 'desktop', 'email'],
            followSuggestions: ['push', 'sms', 'desktop', 'email'],
            organisationsYouManage: ['push', 'sms', 'desktop', 'email'],
            posts: ['push', 'sms', 'desktop', 'email'],
        },
    }),
    __metadata("design:type", Object)
], Profile.prototype, "notificationPreference", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Profile.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Profile.prototype, "updatedAt", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Profile.prototype, "isEmailVerified", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Profile.prototype, "receiveNotificationEmails", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Profile.prototype, "receiveNewMessagesEmails", void 0);
__decorate([
    (0, graphql_1.Field)(() => profiles_args_1.ProfileConnectionStatus, { nullable: true }),
    __metadata("design:type", String)
], Profile.prototype, "status", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
    }),
    __metadata("design:type", Array)
], Profile.prototype, "profileIdsConnections", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
    }),
    __metadata("design:type", Array)
], Profile.prototype, "profileIdsInvitationSent", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
    }),
    __metadata("design:type", Array)
], Profile.prototype, "profileIdsInvitationSentRejected", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
    }),
    __metadata("design:type", Array)
], Profile.prototype, "profileIdsInvitationReceived", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
    }),
    __metadata("design:type", Array)
], Profile.prototype, "profileIdsInvitationReceivedRejected", void 0);
__decorate([
    (0, graphql_1.Field)({
        nullable: true,
    }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Profile.prototype, "sellHolidays", void 0);
__decorate([
    (0, graphql_1.Field)({
        nullable: true,
    }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
    }),
    __metadata("design:type", Date)
], Profile.prototype, "showGettingStartedFeedAt", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_type_json_1.GraphQLJSON, {
        nullable: true,
    }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.JSONB,
    }),
    __metadata("design:type", Object)
], Profile.prototype, "migrationFlags", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => connection_model_1.Connection, 'connectionProfileId'),
    __metadata("design:type", Array)
], Profile.prototype, "connections", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({ defaultValue: 0 }),
    __metadata("design:type", Number)
], Profile.prototype, "connectionsCount", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => notification_model_1.Notification, 'ownerProfileId'),
    __metadata("design:type", Array)
], Profile.prototype, "notifications", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => membership_model_1.Membership, 'profileId'),
    __metadata("design:type", Array)
], Profile.prototype, "memberships", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => getting_started_step_model_1.GettingStartedStep, 'profileId'),
    __metadata("design:type", Array)
], Profile.prototype, "gettingStartedSteps", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => membership_model_1.Membership),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Profile.prototype, "primaryMembershipId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => membership_model_1.Membership, 'primaryMembershipId'),
    __metadata("design:type", membership_model_1.Membership)
], Profile.prototype, "primaryMembership", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
        allowNull: true,
        references: { model: 'Organisations', key: 'id' },
    }),
    __metadata("design:type", String)
], Profile.prototype, "referredByOrganisationId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, 'referredByOrganisationId'),
    __metadata("design:type", organisation_model_1.Organisation)
], Profile.prototype, "referringOrganisation", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    __metadata("design:type", String)
], Profile.prototype, "activeTier", void 0);
exports.Profile = Profile = Profile_1 = __decorate([
    sequelize_typescript_1.Table,
    (0, graphql_1.ObjectType)()
], Profile);
//# sourceMappingURL=profile.model.js.map