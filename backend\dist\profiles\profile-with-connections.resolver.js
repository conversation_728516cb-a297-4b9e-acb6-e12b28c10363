"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfilesResolverConnectionsOnly = void 0;
const profiles_service_1 = require("./profiles.service");
const graphql_auth_guard_1 = require("./../authz/graphql-auth.guard");
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const profile_only_with_connetions_1 = require("./models/profile-only-with-connetions");
const paginated_result_1 = require("./args/paginated-result");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const connection_args_1 = require("../connections/args/connection.args");
const winston_1 = require("winston");
const connections_service_1 = require("../connections/connections.service");
let ProfilesResolverConnectionsOnly = class ProfilesResolverConnectionsOnly {
    async profileConnectionsOnly(user, id) {
        this.logger.verbose('ProfilesResolver.profile (query)', {
            user: user.toLogObject(),
            id,
        });
        if (id) {
            return this.profilesService.findById(id, {
                includeParams: [profile_only_with_connetions_1.ProfileWithConnectionsOnly.ConnectionsIncludeRule],
            });
        }
        return this.profilesService.findByCurrentUser(user);
    }
    async connectionsViaPagination(user, profile, connectionArgs) {
        var _a;
        this.logger.verbose('ProfilesResolver.connectionsViaPagination (field resolver)', {
            profileId: profile.id,
        });
        return await this.connectionsService.findConnections(user.profileId, {
            profileId: (_a = connectionArgs === null || connectionArgs === void 0 ? void 0 : connectionArgs.filter) === null || _a === void 0 ? void 0 : _a.profileId,
            connectionProfileId: profile.id,
        }, {
            first: connectionArgs === null || connectionArgs === void 0 ? void 0 : connectionArgs.first,
            after: connectionArgs === null || connectionArgs === void 0 ? void 0 : connectionArgs.after,
            sortBy: connectionArgs === null || connectionArgs === void 0 ? void 0 : connectionArgs.sortBy,
            sortOrder: connectionArgs === null || connectionArgs === void 0 ? void 0 : connectionArgs.sortOrder,
        });
    }
};
exports.ProfilesResolverConnectionsOnly = ProfilesResolverConnectionsOnly;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], ProfilesResolverConnectionsOnly.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => connections_service_1.ConnectionsService)),
    __metadata("design:type", connections_service_1.ConnectionsService)
], ProfilesResolverConnectionsOnly.prototype, "connectionsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], ProfilesResolverConnectionsOnly.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Query)(() => profile_only_with_connetions_1.ProfileWithConnectionsOnly),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id', { nullable: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ProfilesResolverConnectionsOnly.prototype, "profileConnectionsOnly", null);
__decorate([
    (0, graphql_1.ResolveField)('connectionsViaPagination', () => paginated_result_1.ConnectionsResult),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Parent)()),
    __param(2, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, profile_only_with_connetions_1.ProfileWithConnectionsOnly,
        connection_args_1.connectionArgs]),
    __metadata("design:returntype", Promise)
], ProfilesResolverConnectionsOnly.prototype, "connectionsViaPagination", null);
exports.ProfilesResolverConnectionsOnly = ProfilesResolverConnectionsOnly = __decorate([
    (0, graphql_1.Resolver)(() => profile_only_with_connetions_1.ProfileWithConnectionsOnly)
], ProfilesResolverConnectionsOnly);
//# sourceMappingURL=profile-with-connections.resolver.js.map