"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfilesRepository = void 0;
const common_1 = require("@nestjs/common");
const profile_model_1 = require("./models/profile.model");
const profiles_args_1 = require("./args/profiles.args");
const pagination_1 = require("../common/helpers/pagination");
const profiles_repository_helper_1 = require("./helpers/profiles.repository.helper");
const underscore_1 = require("../common/helpers/underscore");
const sequelize_1 = require("sequelize");
const membership_model_1 = require("../memberships/models/membership.model");
const partnerships_service_1 = require("../partnerships/partnerships.service");
const organisation_model_1 = require("../organisations/models/organisation.model");
const partnership_request_model_1 = require("../partnership-requests/models/partnership-request.model");
let ProfilesRepository = class ProfilesRepository {
    async findProfiles(profileId, filter, pagination) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
        let filteredProfileIds = [];
        const extraQueryParams = {};
        const orQueryFilters = [];
        if ((_a = filter.status) === null || _a === void 0 ? void 0 : _a.includes(profiles_args_1.ProfileConnectionStatus.Connection)) {
            orQueryFilters.push({
                profileIdsConnections: {
                    [sequelize_1.Op.contains]: [profileId],
                },
            });
        }
        if ((_b = filter.status) === null || _b === void 0 ? void 0 : _b.includes(profiles_args_1.ProfileConnectionStatus.InvitationSent)) {
            orQueryFilters.push({
                profileIdsInvitationReceived: {
                    [sequelize_1.Op.contains]: [profileId],
                },
            });
        }
        if ((_c = filter.status) === null || _c === void 0 ? void 0 : _c.includes(profiles_args_1.ProfileConnectionStatus.InvitationSentRejected)) {
            orQueryFilters.push({
                profileIdsInvitationReceivedRejected: {
                    [sequelize_1.Op.contains]: [profileId],
                },
            });
        }
        if ((_d = filter.status) === null || _d === void 0 ? void 0 : _d.includes(profiles_args_1.ProfileConnectionStatus.InvitationReceived)) {
            orQueryFilters.push({
                profileIdsInvitationSent: {
                    [sequelize_1.Op.contains]: [profileId],
                },
            });
        }
        if ((_e = filter.status) === null || _e === void 0 ? void 0 : _e.includes(profiles_args_1.ProfileConnectionStatus.InvitationReceivedRejected)) {
            orQueryFilters.push({
                profileIdsInvitationSentRejected: {
                    [sequelize_1.Op.contains]: [profileId],
                },
            });
        }
        if ((_f = filter.status) === null || _f === void 0 ? void 0 : _f.includes(profiles_args_1.ProfileConnectionStatus.None)) {
            const andQueryFilters = [];
            if (!((_g = filter.status) === null || _g === void 0 ? void 0 : _g.includes(profiles_args_1.ProfileConnectionStatus.Connection))) {
                andQueryFilters.push({
                    [sequelize_1.Op.not]: {
                        profileIdsConnections: {
                            [sequelize_1.Op.contains]: [profileId],
                        },
                    },
                });
            }
            if (!((_h = filter.status) === null || _h === void 0 ? void 0 : _h.includes(profiles_args_1.ProfileConnectionStatus.InvitationSent))) {
                andQueryFilters.push({
                    [sequelize_1.Op.not]: {
                        profileIdsInvitationReceived: {
                            [sequelize_1.Op.contains]: [profileId],
                        },
                    },
                });
            }
            if (!((_j = filter.status) === null || _j === void 0 ? void 0 : _j.includes(profiles_args_1.ProfileConnectionStatus.InvitationSentRejected))) {
                andQueryFilters.push({
                    [sequelize_1.Op.not]: {
                        profileIdsInvitationReceivedRejected: {
                            [sequelize_1.Op.contains]: [profileId],
                        },
                    },
                });
            }
            if (!((_k = filter.status) === null || _k === void 0 ? void 0 : _k.includes(profiles_args_1.ProfileConnectionStatus.InvitationReceived))) {
                andQueryFilters.push({
                    [sequelize_1.Op.not]: {
                        profileIdsInvitationSent: {
                            [sequelize_1.Op.contains]: [profileId],
                        },
                    },
                });
            }
            if (!((_l = filter.status) === null || _l === void 0 ? void 0 : _l.includes(profiles_args_1.ProfileConnectionStatus.InvitationReceivedRejected))) {
                andQueryFilters.push({
                    [sequelize_1.Op.not]: {
                        profileIdsInvitationSentRejected: {
                            [sequelize_1.Op.contains]: [profileId],
                        },
                    },
                });
            }
            orQueryFilters.push({ [sequelize_1.Op.and]: andQueryFilters });
        }
        if (orQueryFilters.length > 0) {
            extraQueryParams[sequelize_1.Op.or] = orQueryFilters;
        }
        if (filter.organisationId) {
            if ((!underscore_1.Underscore.isEmpty(filter.membershipStatus) ||
                !underscore_1.Underscore.isEmpty(filter.membershipPermissions)) &&
                !underscore_1.Underscore.isEmpty(filter.followerStatus)) {
                throw new Error(`membershipStatus | membershipPermissions and followerStatus filters can't bu used at the same time`);
            }
            if (!underscore_1.Underscore.isEmpty(filter.membershipStatus) ||
                !underscore_1.Underscore.isEmpty(filter.membershipPermissions)) {
                filteredProfileIds = await this.helper.getMembershipProfileIds(filter.organisationId, {
                    status: filter.membershipStatus,
                    permissions: filter.membershipPermissions,
                });
            }
            if (!underscore_1.Underscore.isEmpty(filter.followerStatus)) {
                filteredProfileIds = await this.helper.getFollowerProfileIds(filter.organisationId, {
                    status: filter.followerStatus,
                });
            }
            if (filteredProfileIds.length === 0) {
                filteredProfileIds = ['non-existing-id'];
            }
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.organisationId) &&
            (filter === null || filter === void 0 ? void 0 : filter.connectedOrgProfiles) &&
            (filter === null || filter === void 0 ? void 0 : filter.connectedOrgProfiles) === true) {
            const partnershipOrgs = await this.partnershipsService.findAll({
                organisationId: filter === null || filter === void 0 ? void 0 : filter.organisationId,
            }, {
                attributes: ['partnershipOrganisationId'],
                includeParams: [
                    {
                        model: partnership_request_model_1.PartnershipRequest,
                        as: 'partnershipRequest',
                        where: {
                            status: partnership_request_model_1.PartnershipRequestStatus.Approved,
                        },
                    },
                ],
                useCache: true,
            });
            if (partnershipOrgs.length !== 0) {
                for (const org of partnershipOrgs) {
                    const allProfiles = await this.helper.getMembershipProfileIds(org.partnershipOrganisationId, {
                        status: filter.membershipStatus
                            ? filter.membershipStatus
                            : [membership_model_1.MembershipStatus.Active],
                        permissions: filter.membershipPermissions,
                    });
                    filteredProfileIds.push(...allProfiles);
                }
            }
        }
        if (filter.searchLocationText) {
            extraQueryParams['location'] = sequelize_1.Sequelize.where(sequelize_1.Sequelize.fn('text', sequelize_1.Sequelize.col('Profile.location')), {
                [sequelize_1.Op.iLike]: `%${filter.searchLocationText.split(' ').join('%')}%`,
            });
        }
        if (filter.responsibilities) {
            extraQueryParams['responsibilities'] = {
                [sequelize_1.Op.overlap]: filter.responsibilities,
            };
        }
        if (filter.typesOfHoliday) {
            extraQueryParams['typesOfHoliday'] = {
                [sequelize_1.Op.overlap]: filter.typesOfHoliday,
            };
        }
        if (filter.sellHolidays) {
            extraQueryParams['sellHolidays'] =
                filter.sellHolidays;
        }
        const fuzzyResults = {
            count: 0,
            isFuzzySearch: false,
        };
        if (filter === null || filter === void 0 ? void 0 : filter.searchText) {
            fuzzyResults.isFuzzySearch = true;
            let query = `
        SELECT set_limit(0.4);

        SELECT id
        FROM "Profiles"
        WHERE 
            name % :searchText
            AND id != :profileId
        ORDER BY SIMILARITY(name, :searchText) DESC,
        id ASC
      `;
            if (filter === null || filter === void 0 ? void 0 : filter.searchOrgNameAndJob) {
                query = `
          SELECT set_limit(0.4);

          SELECT p.id
          FROM "Profiles" p
          LEFT JOIN "Memberships" m ON p."primaryMembershipId" = m.id
          LEFT JOIN "Organisations" o ON m."organisationId" = o.id
          WHERE 
              (p.name % :searchText OR
              m."organisationName" % :searchText OR
              m.position % :searchText OR
              o.name % :searchText)
              AND p.id != :profileId
          ORDER BY 
              GREATEST(
                  SIMILARITY(p.name, :searchText),
                  SIMILARITY(m."organisationName", :searchText),
                  SIMILARITY(m.position, :searchText),
                  SIMILARITY(o.name, :searchText)
              ) DESC,
              p.id ASC
        `;
            }
            let options = {
                replacements: {
                    searchText: filter.searchText,
                    profileId,
                },
                type: 'SELECT',
            };
            const nonPaginatedProfileIds = await profile_model_1.Profile.sequelize.query(query, options);
            nonPaginatedProfileIds.shift();
            if (nonPaginatedProfileIds.length) {
                const nonPaginatedFuzzyProfileIds = nonPaginatedProfileIds.map((profile) => profile.id);
                fuzzyResults.count = nonPaginatedFuzzyProfileIds.length;
            }
            const paginatedQuery = `${query}
        ${pagination && pagination.first ? `LIMIT :first` : ''}
      `;
            options = {
                replacements: {
                    searchText: filter.searchText,
                    profileId,
                    first: pagination === null || pagination === void 0 ? void 0 : pagination.first,
                },
                type: 'SELECT',
            };
            const profileIds = await profile_model_1.Profile.sequelize.query(paginatedQuery, options);
            profileIds.shift();
            if (profileIds.length) {
                const fuzzyProfileIds = profileIds.map((profile) => profile.id);
                filteredProfileIds.push(...fuzzyProfileIds);
            }
            else {
                return {
                    records: [],
                    totalCount: 0,
                };
            }
        }
        const result = await new pagination_1.PaginationHelper().getPaginatedResults({
            model: profile_model_1.Profile,
            pagination,
            includeIds: filteredProfileIds,
            excludeIds: filter.includeOwnProfile
                ? ['_deleted-user_']
                : [profileId, '_deleted-user_'],
            extraQueryParams,
            searchText: filter === null || filter === void 0 ? void 0 : filter.searchText,
            includeParams: [
                {
                    as: 'primaryMembership',
                    model: membership_model_1.Membership,
                    include: [
                        {
                            as: 'organisation',
                            model: organisation_model_1.Organisation,
                        },
                    ],
                },
            ],
        });
        if (!fuzzyResults.isFuzzySearch) {
            return result;
        }
        const sortedProfiles = result.records.sort((a, b) => {
            const indexA = filteredProfileIds.indexOf(a.id);
            const indexB = filteredProfileIds.indexOf(b.id);
            if (indexA === -1 && indexB === -1) {
                return 0;
            }
            else if (indexA === -1) {
                return 1;
            }
            else if (indexB === -1) {
                return -1;
            }
            else {
                return indexA - indexB;
            }
        });
        return Object.assign(Object.assign({}, result), { records: sortedProfiles, totalCount: fuzzyResults.count });
    }
};
exports.ProfilesRepository = ProfilesRepository;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_repository_helper_1.ProfilesRepositoryHelper)),
    __metadata("design:type", profiles_repository_helper_1.ProfilesRepositoryHelper)
], ProfilesRepository.prototype, "helper", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => partnerships_service_1.PartnershipsService)),
    __metadata("design:type", partnerships_service_1.PartnershipsService)
], ProfilesRepository.prototype, "partnershipsService", void 0);
exports.ProfilesRepository = ProfilesRepository = __decorate([
    (0, common_1.Injectable)()
], ProfilesRepository);
//# sourceMappingURL=profiles.repository.js.map