"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfilesResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const paginated_result_1 = require("../common/args/paginated-result");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const connection_args_1 = require("../connections/args/connection.args");
const connections_service_1 = require("../connections/connections.service");
const connection_model_1 = require("../connections/models/connection.model");
const notificationLayoutHelper_1 = require("../email/helpers/notificationLayoutHelper");
const experiences_service_1 = require("../experiences/experiences.service");
const experience_model_1 = require("../experiences/models/experience.model");
const followers_args_1 = require("../followers/args/followers.args");
const followers_service_1 = require("../followers/followers.service");
const memberships_args_1 = require("../memberships/args/memberships.args");
const memberships_service_helper_1 = require("../memberships/helpers/memberships.service.helper");
const memberships_service_1 = require("../memberships/memberships.service");
const membership_model_1 = require("../memberships/models/membership.model");
const notifications_args_1 = require("../notifications/args/notifications.args");
const notifications_service_1 = require("../notifications/notifications.service");
const organisations_service_1 = require("../organisations/organisations.service");
const paginated_result_2 = require("./args/paginated-result");
const profiles_args_1 = require("./args/profiles.args");
const add_profile_parent_organisation_1 = require("./dto/add-profile-parent-organisation");
const mobile_devices_logged_in_count_input_1 = require("./dto/mobile-devices-logged-in-count-input");
const update_profile_input_1 = require("./dto/update-profile.input");
const profile_model_1 = require("./models/profile.model");
const profiles_service_1 = require("./profiles.service");
const log_daily_activity_input_1 = require("./dto/log-daily-activity-input");
const loyalty_points_service_1 = require("../loyalty-points/loyalty-points.service");
const sequelize_1 = require("sequelize");
const update_profile_referring_organisation_1 = require("./dto/update-profile-referring-organisation");
let ProfilesResolver = class ProfilesResolver {
    async getMemberships(profile, membershipArgs) {
        var _a;
        if (profile.memberships && profile.memberships.length > 0) {
            return memberships_service_helper_1.MembershipsServiceHelper.filter(profile.memberships, membershipArgs);
        }
        this.logger.verbose('ProfilesResolver.getMemberships (field resolver)', {
            profileId: profile.id,
            membershipArgs,
        });
        if (((_a = membershipArgs === null || membershipArgs === void 0 ? void 0 : membershipArgs.filter) === null || _a === void 0 ? void 0 : _a.isPrimary) === true) {
            const primaryMembership = await this.primaryMembership(profile);
            if (!primaryMembership)
                return [];
            return memberships_service_helper_1.MembershipsServiceHelper.filter([primaryMembership], membershipArgs);
        }
        return this.membershipsService.findAll(Object.assign({ profileId: profile.id }, memberships_service_helper_1.MembershipsServiceHelper.getQueryParams(membershipArgs)), { useCache: true });
    }
    async profile(user, id) {
        this.logger.verbose('ProfilesResolver.profile (query)', {
            user: user.toLogObject(),
            id,
        });
        if (id) {
            return this.profilesService.findById(id);
        }
        return this.profilesService.findByCurrentUser(user);
    }
    profiles(user, profileArgs) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
        this.logger.verbose('ProfilesResolver.profiles (query)', {
            user: user.toLogObject(),
            profileArgs,
        });
        return this.profilesService.findProfiles(user.profileId, {
            organisationId: (_a = profileArgs === null || profileArgs === void 0 ? void 0 : profileArgs.filter) === null || _a === void 0 ? void 0 : _a.organisationId,
            followerStatus: (_b = profileArgs === null || profileArgs === void 0 ? void 0 : profileArgs.filter) === null || _b === void 0 ? void 0 : _b.followerStatus,
            membershipStatus: (_c = profileArgs === null || profileArgs === void 0 ? void 0 : profileArgs.filter) === null || _c === void 0 ? void 0 : _c.membershipStatus,
            membershipPermissions: (_d = profileArgs === null || profileArgs === void 0 ? void 0 : profileArgs.filter) === null || _d === void 0 ? void 0 : _d.membershipPermissions,
            status: (_e = profileArgs === null || profileArgs === void 0 ? void 0 : profileArgs.filter) === null || _e === void 0 ? void 0 : _e.status,
            searchText: (_f = profileArgs === null || profileArgs === void 0 ? void 0 : profileArgs.filter) === null || _f === void 0 ? void 0 : _f.searchText,
            includeOwnProfile: ((_g = profileArgs === null || profileArgs === void 0 ? void 0 : profileArgs.filter) === null || _g === void 0 ? void 0 : _g.includeOwnProfile) || false,
            searchLocationText: (_h = profileArgs === null || profileArgs === void 0 ? void 0 : profileArgs.filter) === null || _h === void 0 ? void 0 : _h.searchLocationText,
            responsibilities: (_j = profileArgs === null || profileArgs === void 0 ? void 0 : profileArgs.filter) === null || _j === void 0 ? void 0 : _j.responsibilities,
            typesOfHoliday: (_k = profileArgs === null || profileArgs === void 0 ? void 0 : profileArgs.filter) === null || _k === void 0 ? void 0 : _k.typesOfHoliday,
            connectedOrgProfiles: (_l = profileArgs === null || profileArgs === void 0 ? void 0 : profileArgs.filter) === null || _l === void 0 ? void 0 : _l.connectedOrgProfiles,
            searchOrgNameAndJob: true,
            isFuzzySearch: (_m = profileArgs === null || profileArgs === void 0 ? void 0 : profileArgs.filter) === null || _m === void 0 ? void 0 : _m.isFuzzySearch,
        }, {
            first: profileArgs === null || profileArgs === void 0 ? void 0 : profileArgs.first,
            after: profileArgs === null || profileArgs === void 0 ? void 0 : profileArgs.after,
            sortBy: profileArgs === null || profileArgs === void 0 ? void 0 : profileArgs.sortBy,
            sortOrder: profileArgs === null || profileArgs === void 0 ? void 0 : profileArgs.sortOrder,
            offset: profileArgs === null || profileArgs === void 0 ? void 0 : profileArgs.offset,
        });
    }
    async updateProfile(user, profileData) {
        this.logger.verbose('ProfilesResolver.updateProfile (mutation)', {
            user: user.toLogObject(),
            profileData,
        });
        return this.profilesService.updateProfile(user.profileId, profileData);
    }
    removeProfile(user) {
        this.logger.verbose('ProfilesResolver.removeProfile (mutation)', {
            user: user.toLogObject(),
        });
        return this.profilesService.removeProfile(user.profileId);
    }
    async removeOtherProfile(user, profileId) {
        this.logger.verbose('ProfilesResolver.removeOtherProfile (mutation)', {
            user: user.toLogObject(),
        });
        const membershipDetails = await this.membershipsService.findOne({
            profileId: user.profileId,
            organisationId: {
                [sequelize_1.Op.in]: ['aJtx73K8SpeNpyz5MNm7bL', 'sTDUCYXXaBpjrBHDUExgpE'],
            },
        });
        if (membershipDetails) {
            const profile = await this.profilesService.findById(profileId);
            if (!profile) {
                this.logger.error(`Profile not found for id ${profileId}`);
                throw new Error('Profile not found.');
            }
            return this.profilesService.removeProfile(profileId);
        }
        else {
            this.logger.error(`You are not authorized to Delete the user with Id ${profileId}.`);
            throw new Error('You are not authorized to Delete the user.');
        }
    }
    setPrimaryOrganisation(user, organisationId, organisationName, position, preApprove) {
        this.logger.verbose('ProfilesResolver.setPrimaryOrganisation (mutation)', {
            user: user.toLogObject(),
            organisationId,
            organisationName,
            position,
            preApprove,
        });
        return this.profilesService.setPrimaryOrganisation(user.profileId, organisationId, organisationName, position, preApprove);
    }
    sendEmailVerification(user) {
        this.logger.verbose('ProfilesResolver.setPrimaryOrganisation (mutation)', {
            user: user.toLogObject(),
        });
        return this.profilesService.sendEmailVerification(user.profileId);
    }
    changeEmail(user, newEmail) {
        this.logger.verbose('ProfilesResolver.changeEmail (mutation)', {
            user: user.toLogObject(),
        });
        return this.profilesService.changeEmail(user.profileId, newEmail.toLowerCase());
    }
    async connections(profile) {
        if (profile.connections)
            return profile.connections;
        this.logger.verbose('ProfilesResolver.connections (field resolver)', {
            profileId: profile.id,
        });
        if (profile.isComplete === true) {
            await this.profilesService.checkForEmailInvitations(profile.email, profile.id);
        }
        return this.connectionsService.findAll({
            connectionProfileId: profile.id,
        }, { useCache: true });
    }
    async connectionsViaPagination(user, profile, connectionArgs) {
        var _a;
        this.logger.verbose('ProfilesResolver.connectionsViaPagination (field resolver)', {
            profileId: profile.id,
        });
        return await this.connectionsService.findConnections(user.profileId, {
            profileId: (_a = connectionArgs === null || connectionArgs === void 0 ? void 0 : connectionArgs.filter) === null || _a === void 0 ? void 0 : _a.profileId,
            connectionProfileId: profile.id,
        }, {
            first: connectionArgs === null || connectionArgs === void 0 ? void 0 : connectionArgs.first,
            after: connectionArgs === null || connectionArgs === void 0 ? void 0 : connectionArgs.after,
            sortBy: connectionArgs === null || connectionArgs === void 0 ? void 0 : connectionArgs.sortBy,
            sortOrder: connectionArgs === null || connectionArgs === void 0 ? void 0 : connectionArgs.sortOrder,
        });
    }
    experiences(profile) {
        this.logger.verbose('ProfilesResolver.experiences (field resolver)', {
            profileId: profile.id,
        });
        return this.experiencesService.findAll({
            profileId: profile.id,
        }, { useCache: true });
    }
    async memberships(profile, membershipArgs) {
        return this.getMemberships(profile, membershipArgs);
    }
    async activeMemberships(profile, membershipArgs) {
        return this.getMemberships(profile, membershipArgs);
    }
    async nonPrimaryMemberships(profile, membershipArgs) {
        membershipArgs.filter.isPrimary = false;
        return this.getMemberships(profile, membershipArgs);
    }
    following(profile, followerArgs) {
        var _a, _b, _c, _d;
        this.logger.verbose('ProfilesResolver.following (field resolver)', {
            profileId: profile.id,
            followerArgs,
        });
        return this.followersService.findFollowers(profile.id, {
            profileId: (_b = (_a = followerArgs === null || followerArgs === void 0 ? void 0 : followerArgs.filter) === null || _a === void 0 ? void 0 : _a.profileId) !== null && _b !== void 0 ? _b : profile.id,
            status: (_c = followerArgs === null || followerArgs === void 0 ? void 0 : followerArgs.filter) === null || _c === void 0 ? void 0 : _c.status,
            includeActiveProfile: (_d = followerArgs === null || followerArgs === void 0 ? void 0 : followerArgs.filter) === null || _d === void 0 ? void 0 : _d.includeActiveProfile,
        }, {
            first: followerArgs === null || followerArgs === void 0 ? void 0 : followerArgs.first,
            after: followerArgs === null || followerArgs === void 0 ? void 0 : followerArgs.after,
            sortBy: followerArgs === null || followerArgs === void 0 ? void 0 : followerArgs.sortBy,
            sortOrder: followerArgs === null || followerArgs === void 0 ? void 0 : followerArgs.sortOrder,
        });
    }
    isEmailVerified(profile) {
        this.logger.verbose('ProfilesResolver.isEmailVerified (field resolver)', {
            profileId: profile.id,
        });
        return this.profilesService.isEmailVerified(profile);
    }
    async streamChannelId(user, profile) {
        if (!profile.profileIdsConnections.includes(user.profileId)) {
            return null;
        }
        this.logger.verbose('ProfilesResolver.streamChannelId (field resolver)', {
            profileId: profile.id,
        });
        const connection = await this.connectionsService.findOne({
            profileId: user.profileId,
            connectionProfileId: profile.id,
        }, {
            attributes: ['streamChannelId'],
            useCache: true,
        });
        return connection === null || connection === void 0 ? void 0 : connection.streamChannelId;
    }
    async status(user, profile) {
        var _a, _b, _c, _d, _e;
        const connection = (_a = profile.profileIdsConnections) === null || _a === void 0 ? void 0 : _a.find(id => id === user.profileId);
        const invitationSent = (_b = profile.profileIdsInvitationSent) === null || _b === void 0 ? void 0 : _b.find(id => id === user.profileId);
        const invitationSentRejected = (_c = profile.profileIdsInvitationSentRejected) === null || _c === void 0 ? void 0 : _c.find(id => id === user.profileId);
        const invitationReceived = (_d = profile.profileIdsInvitationReceived) === null || _d === void 0 ? void 0 : _d.find(id => id === user.profileId);
        const invitationReceivedRejected = (_e = profile.profileIdsInvitationReceivedRejected) === null || _e === void 0 ? void 0 : _e.find(id => id === user.profileId);
        if (connection) {
            return profiles_args_1.ProfileConnectionStatus.Connection;
        }
        else if (invitationSent) {
            return profiles_args_1.ProfileConnectionStatus.InvitationReceived;
        }
        else if (invitationSentRejected) {
            return profiles_args_1.ProfileConnectionStatus.InvitationReceivedRejected;
        }
        else if (invitationReceived) {
            return profiles_args_1.ProfileConnectionStatus.InvitationSent;
        }
        else if (invitationReceivedRejected) {
            return profiles_args_1.ProfileConnectionStatus.InvitationSentRejected;
        }
        return profiles_args_1.ProfileConnectionStatus.None;
    }
    async primaryMembership(profile) {
        if (!profile.primaryMembershipId)
            return null;
        if (profile.primaryMembership)
            return profile.primaryMembership;
        this.logger.verbose('ProfilesResolver.primaryMembership (field resolver)', {
            profileId: profile.id,
        });
        return this.membershipsService.findById(profile.primaryMembershipId);
    }
    async addProfileParentOrganisation(user, data) {
        this.logger.verbose('ProfilesResolver.addProfileParentOrganisation (mutation)', {
            data,
        });
        return await this.profilesService.addProfileParentOrganisation(data);
    }
    async removeProfileParentOrganisation(user, data) {
        this.logger.verbose('ProfilesResolver.removeProfileParentOrganisation (mutation)', {
            data,
        });
        const profile = await this.profilesService.removeProfileParentOrganisation(data);
        await this.profilesService.removeMembershipAndFollowersFromParentOrg(data);
        return profile;
    }
    async acceptMemberByParentAdmin(user, data) {
        this.logger.verbose('ProfilesResolver.acceptMemberByParentAdmin (mutation)', {
            data,
        });
        return await this.profilesService.addMembershipAndFollowersToParentOrg(data, true);
    }
    async declineMemberByParentAdmin(user, data) {
        this.logger.verbose('ProfilesResolver.declineMemberByParentAdmin (mutation)', {
            data,
        });
        const parent = await this.profilesService.removeProfileParentOrganisation(data);
        await this.notificationsService.createNotification({
            ownerProfileId: data.profileId,
            profileId: data.profileId,
            organisationId: data.parentId,
            type: notifications_args_1.NotificationType.ParentMembershipDeclined,
        });
        const profileIds = [data.profileId];
        const replacementOrg = await this.organisationsService.findById(data.parentId);
        const replacements = [replacementOrg.name];
        await this.notificationsService.sendPushNotification({
            profileIds,
            replacements,
            messageType: notifications_args_1.NotificationMessage.ParentMembershipDeclined,
            route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.profile),
        });
        return parent;
    }
    async mobileDevicesLoggedInCount(user, data) {
        this.logger.verbose('ProfilesResolver.mobileDevicesLoggedInCount (mutation)', {
            data,
        });
        return await this.profilesService.mobileDevicesLoggedInCount(data);
    }
    async logActiveSession(user, data) {
        this.logger.verbose('ProfilesResolver.logActiveSession (mutation)', Object.assign({ profileId: user.profileId }, data));
        await this.profilesService.logActiveSession({
            profileId: user.profileId,
            data,
        });
        return true;
    }
    async activeTier(profile) {
        var _a;
        const loyaltyPoint = await this.loyaltyPointsService.findOneByProfileId(profile.id);
        return this.loyaltyPointsService.getTierFromIndex((_a = loyaltyPoint === null || loyaltyPoint === void 0 ? void 0 : loyaltyPoint.activeTier) !== null && _a !== void 0 ? _a : 0);
    }
    async updateProfileReferringOrganisation(user, data) {
        this.logger.verbose('ProfilesResolver.updateProfileReferringOrganisation (mutation)', {
            user: user.toLogObject(),
            data,
        });
        if (user.profileId !== data.profileId) {
            throw new Error('You can only update your own profile');
        }
        return this.profilesService.updateProfileReferringOrganisation(data.profileId, data.referringOrganisationId);
    }
};
exports.ProfilesResolver = ProfilesResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], ProfilesResolver.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => connections_service_1.ConnectionsService)),
    __metadata("design:type", connections_service_1.ConnectionsService)
], ProfilesResolver.prototype, "connectionsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => experiences_service_1.ExperiencesService)),
    __metadata("design:type", experiences_service_1.ExperiencesService)
], ProfilesResolver.prototype, "experiencesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], ProfilesResolver.prototype, "membershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => followers_service_1.FollowersService)),
    __metadata("design:type", followers_service_1.FollowersService)
], ProfilesResolver.prototype, "followersService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => notifications_service_1.NotificationsService)),
    __metadata("design:type", notifications_service_1.NotificationsService)
], ProfilesResolver.prototype, "notificationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], ProfilesResolver.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], ProfilesResolver.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => loyalty_points_service_1.LoyaltyPointsService)),
    __metadata("design:type", loyalty_points_service_1.LoyaltyPointsService)
], ProfilesResolver.prototype, "loyaltyPointsService", void 0);
__decorate([
    (0, graphql_1.Query)(() => profile_model_1.Profile),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id', { nullable: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "profile", null);
__decorate([
    (0, graphql_1.Query)(() => paginated_result_1.ProfilesResult),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, profiles_args_1.ProfilesArgs]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "profiles", null);
__decorate([
    (0, graphql_1.Mutation)(() => profile_model_1.Profile),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('profileData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, update_profile_input_1.UpdateProfileInput]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "updateProfile", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "removeProfile", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('profileId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "removeOtherProfile", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('organisationId', { nullable: true })),
    __param(2, (0, graphql_1.Args)('organisationName', { nullable: true })),
    __param(3, (0, graphql_1.Args)('position', { nullable: true })),
    __param(4, (0, graphql_1.Args)('preApprove', { nullable: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String, Boolean]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "setPrimaryOrganisation", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "sendEmailVerification", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('newEmail')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "changeEmail", null);
__decorate([
    (0, graphql_1.ResolveField)('connections', () => [connection_model_1.Connection]),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [profile_model_1.Profile]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "connections", null);
__decorate([
    (0, graphql_1.ResolveField)('connectionsViaPagination', () => paginated_result_2.ConnectionsResult),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Parent)()),
    __param(2, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, profile_model_1.Profile,
        connection_args_1.connectionArgs]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "connectionsViaPagination", null);
__decorate([
    (0, graphql_1.ResolveField)('experiences', () => [experience_model_1.Experience]),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [profile_model_1.Profile]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "experiences", null);
__decorate([
    (0, graphql_1.ResolveField)('memberships', () => [membership_model_1.Membership]),
    __param(0, (0, graphql_1.Parent)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [profile_model_1.Profile,
        memberships_args_1.MembershipsArgs]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "memberships", null);
__decorate([
    (0, graphql_1.ResolveField)('activeMemberships', () => [membership_model_1.Membership]),
    __param(0, (0, graphql_1.Parent)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [profile_model_1.Profile,
        memberships_args_1.MembershipsArgs]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "activeMemberships", null);
__decorate([
    (0, graphql_1.ResolveField)('nonPrimaryMemberships', () => [membership_model_1.Membership]),
    __param(0, (0, graphql_1.Parent)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [profile_model_1.Profile,
        memberships_args_1.MembershipsArgs]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "nonPrimaryMemberships", null);
__decorate([
    (0, graphql_1.ResolveField)('following', () => paginated_result_1.FollowersResult),
    __param(0, (0, graphql_1.Parent)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [profile_model_1.Profile,
        followers_args_1.FollowersArgs]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "following", null);
__decorate([
    (0, graphql_1.ResolveField)('isEmailVerified', () => Boolean),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [profile_model_1.Profile]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "isEmailVerified", null);
__decorate([
    (0, graphql_1.ResolveField)('streamChannelId', () => String, { nullable: true }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, profile_model_1.Profile]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "streamChannelId", null);
__decorate([
    (0, graphql_1.ResolveField)('status', () => profiles_args_1.ProfileConnectionStatus, { nullable: true }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, profile_model_1.Profile]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "status", null);
__decorate([
    (0, graphql_1.ResolveField)('primaryMembership', () => membership_model_1.Membership, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [profile_model_1.Profile]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "primaryMembership", null);
__decorate([
    (0, graphql_1.Mutation)(() => profile_model_1.Profile),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('data')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, add_profile_parent_organisation_1.ProfileParentOrganisationInput]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "addProfileParentOrganisation", null);
__decorate([
    (0, graphql_1.Mutation)(() => profile_model_1.Profile),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('data')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, add_profile_parent_organisation_1.ProfileParentOrganisationInput]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "removeProfileParentOrganisation", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('data')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, add_profile_parent_organisation_1.ProfileParentOrganisationInput]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "acceptMemberByParentAdmin", null);
__decorate([
    (0, graphql_1.Mutation)(() => profile_model_1.Profile),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('data')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, add_profile_parent_organisation_1.ProfileParentOrganisationInput]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "declineMemberByParentAdmin", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('data')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, mobile_devices_logged_in_count_input_1.MobileDevicesLoggedInCountInput]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "mobileDevicesLoggedInCount", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('data')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, log_daily_activity_input_1.LogActiveSessionInput]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "logActiveSession", null);
__decorate([
    (0, graphql_1.ResolveField)('activeTier', () => String),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [profile_model_1.Profile]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "activeTier", null);
__decorate([
    (0, graphql_1.Mutation)(() => profile_model_1.Profile),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('data')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, update_profile_referring_organisation_1.UpdateProfileReferringOrganisationInput]),
    __metadata("design:returntype", Promise)
], ProfilesResolver.prototype, "updateProfileReferringOrganisation", null);
exports.ProfilesResolver = ProfilesResolver = __decorate([
    (0, graphql_1.Resolver)(() => profile_model_1.Profile)
], ProfilesResolver);
//# sourceMappingURL=profiles.resolver.js.map