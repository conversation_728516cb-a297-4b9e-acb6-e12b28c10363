"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfilesService = void 0;
const common_1 = require("@nestjs/common");
const get_timezone_offset_1 = __importDefault(require("get-timezone-offset"));
const lodash_1 = require("lodash");
const moment_timezone_1 = __importDefault(require("moment-timezone"));
const nest_winston_1 = require("nest-winston");
const sequelize_1 = require("sequelize");
const sequelize_typescript_1 = require("sequelize-typescript");
const create_user_activity_data_dto_1 = require("../activities/dto/create-user-activity-data.dto");
const winston_1 = require("winston");
const activities_service_1 = require("../activities/activities.service");
const activities_args_1 = require("../activities/args/activities.args");
const authz_service_1 = require("../authz/authz.service");
const autofollow_service_1 = require("../autofollow/autofollow.service");
const chat_service_1 = require("../chat/chat.service");
const base_service_1 = require("../common/base.service");
const error_1 = require("../common/helpers/error");
const underscore_1 = require("../common/helpers/underscore");
const config_1 = __importDefault(require("../config/config"));
const connection_requests_service_1 = require("../connection-requests/connection-requests.service");
const connection_request_model_1 = require("../connection-requests/models/connection-request.model");
const connections_service_1 = require("../connections/connections.service");
const notificationLayoutHelper_1 = require("../email/helpers/notificationLayoutHelper");
const event_invitations_service_1 = require("../event-invitations/event-invitations.service");
const experiences_service_1 = require("../experiences/experiences.service");
const stream_followers_service_1 = require("../feeds-followers/stream-followers.service");
const followers_service_1 = require("../followers/followers.service");
const follower_model_1 = require("../followers/models/follower.model");
const getting_started_steps_args_1 = require("../getting-started-steps/args/getting-started-steps.args");
const getting_started_steps_service_1 = require("../getting-started-steps/getting-started-steps.service");
const incentive_bookings_service_1 = require("../incentive-bookings/incentive-bookings.service");
const incentive_participants_service_1 = require("../incentive-participants/incentive-participants.service");
const memberships_service_1 = require("../memberships/memberships.service");
const membership_model_1 = require("../memberships/models/membership.model");
const notifications_args_1 = require("../notifications/args/notifications.args");
const notifications_service_1 = require("../notifications/notifications.service");
const organisations_service_1 = require("../organisations/organisations.service");
const partnership_requests_service_1 = require("../partnership-requests/partnership-requests.service");
const partnerships_service_1 = require("../partnerships/partnerships.service");
const posts_service_1 = require("../posts/posts.service");
const webinar_participants_service_1 = require("../webinar-participants/webinar-participants.service");
const email_invitations_service_1 = require("./../email-invitations/email-invitations.service");
const profiles_args_1 = require("./args/profiles.args");
const profiles_service_helper_1 = require("./helpers/profiles.service.helper");
const profile_model_1 = require("./models/profile.model");
const profiles_repository_1 = require("./profiles.repository");
const achievements_service_1 = require("../achievements/achievements.service");
const stream_service_1 = require("../feeds/stream.service");
let ProfilesService = class ProfilesService extends (0, base_service_1.BaseService)(profile_model_1.Profile) {
    async findByCurrentUser(user) {
        var _a, _b;
        this.logger.verbose('ProfilesService.findByCurrentUser', {
            user: user.toLogObject(),
        });
        let profile = await this.findById(user.profileId);
        const updateDto = {
            lastActivityAt: new Date(),
        };
        if (!profile) {
            const splitSubId = user.sub.split('|');
            const getUserId = splitSubId[0].toLowerCase() === 'apple'
                ? splitSubId[1].split('.')[1]
                : splitSubId[1];
            profile = await this.create({
                id: getUserId,
                authZeroUserId: user.sub,
                email: user.email,
                image: user.picture,
                name: ((_a = user.name) === null || _a === void 0 ? void 0 : _a.indexOf('@')) > -1 ? '' : user.name || '',
            });
            if (config_1.default.SERVICE === 'api') {
                const hablo = await this.organisationsService.findById('aJtx73K8SpeNpyz5MNm7bL');
                if (hablo) {
                    await this.followersService.create({
                        profileId: profile.id,
                        organisationId: hablo.id,
                        status: follower_model_1.FollowerStatus.Active,
                    });
                    try {
                        await this.streamFollowersService.follow({ profileId: profile.id }, hablo.id);
                    }
                    catch (e) {
                        this.logger.error(`ProfilesService.findByCurrentUser Error: ${e.message}`, (_b = e.response) === null || _b === void 0 ? void 0 : _b.body);
                    }
                    await this.activitiesService.createUserActivity({
                        type: activities_args_1.ActivityType.OrganisationFollower,
                        schema: create_user_activity_data_dto_1.organisationFollowActivityDataDto,
                        data: {
                            follow: true,
                        },
                        organisationId: hablo.id,
                        profileId: profile.id,
                        addLoyaltyPoints: true,
                        placeholders: {
                            name: hablo.name,
                            id: hablo.id,
                            vanityId: hablo.vanityId,
                        },
                    });
                }
            }
        }
        profile = await this.updateById(profile.id, updateDto);
        if (!(profile === null || profile === void 0 ? void 0 : profile.streamUserId)) {
            await this.chatService.upsertUser(profile.id);
        }
        return profile;
    }
    async findProfiles(profileId, filter, pagination) {
        this.logger.verbose('ProfilesService.findProfiles', {
            profileId,
            filter,
            pagination,
        });
        return this.profilesRepository.findProfiles(profileId, filter, pagination);
    }
    async removeProfile(profileId) {
        this.logger.info('ProfilesService.removeProfile', {
            profileId,
        });
        const profile = await this.findById(profileId);
        if (!profile)
            return true;
        const deleteduserId = '_deleted-user_';
        const deleteduser = await this.findById(deleteduserId);
        if (!deleteduser) {
            await this.create({
                id: deleteduserId,
                authZeroUserId: deleteduserId,
                email: '<EMAIL>',
                name: 'A deleted user',
            });
        }
        const ownerships = await this.membershipsService.findAll({
            profileId: {
                [sequelize_1.Op.eq]: profile.id,
            },
            permissions: {
                [sequelize_1.Op.overlap]: [membership_model_1.MembershipPermission.Owner],
            },
            status: membership_model_1.MembershipStatus.Active,
        });
        if (ownerships.length) {
            this.errorHelper.throwHttpException(`ProfilesService.removeProfile`, 'Cannot remove user whilst owner of an organisation');
        }
        const transaction = await this.sequelize.transaction();
        try {
            await this.notificationsService.update({
                where: {
                    profileId: profile.id,
                    type: {
                        [sequelize_1.Op.notIn]: [
                            notifications_args_1.NotificationType.InvitationAccepted,
                            notifications_args_1.NotificationType.InvitationReceived,
                            notifications_args_1.NotificationType.MembershipRequested,
                        ],
                    },
                },
                update: {
                    profileId: deleteduserId,
                },
                transaction,
            });
            await this.notificationsService.remove({
                where: { ownerProfileId: profile.id },
                transaction,
            });
            await this.notificationsService.remove({
                where: { profileId: profile.id },
                transaction,
            });
            await this.followersService.remove({
                where: { profileId: profile.id },
                transaction,
            });
            await this.connectionsService.remove({
                where: { profileId: profile.id },
                transaction,
            });
            await this.connectionsService.remove({
                where: { connectionProfileId: profile.id },
                transaction,
            });
            await this.connectionRequestsService.remove({
                where: { senderProfileId: profile.id },
                transaction,
            });
            await this.connectionRequestsService.remove({
                where: { receiverProfileId: profile.id },
                transaction,
            });
            await this.eventInvitationsService.remove({
                where: { profileId: profile.id },
                transaction,
            });
            await this.eventInvitationsService.update({
                where: { inviterProfileId: profile.id },
                update: {
                    inviterProfileId: deleteduserId,
                },
                transaction,
            });
            await this.experiencesService.remove({
                where: { profileId: profile.id },
                transaction,
            });
            await this.incentiveBookingsService.remove({
                where: { profileId: profile.id },
                transaction,
            });
            await this.incentiveParticipantsService.remove({
                where: { profileId: profile.id },
                transaction,
            });
            await this.incentiveParticipantsService.update({
                where: { inviterProfileId: profile.id },
                update: {
                    inviterProfileId: deleteduserId,
                },
                transaction,
            });
            await this.webinarParticipantsService.remove({
                where: { profileId: profile.id },
                transaction,
            });
            await this.webinarParticipantsService.update({
                where: { inviterProfileId: profile.id },
                update: {
                    inviterProfileId: deleteduserId,
                },
                transaction,
            });
            await this.membershipsService.remove({
                where: { profileId: profile.id },
                transaction,
            });
            await this.partnershipRequestsService.update({
                where: { senderProfileId: profile.id },
                update: {
                    senderProfileId: deleteduserId,
                },
                transaction,
            });
            await this.postsService.update({
                where: { profileId: profile.id },
                update: {
                    profileId: deleteduserId,
                },
                transaction,
            });
            await this.removeById(profile.id, { transaction });
            await transaction.commit();
            await this.authZeroService.removeUser(profile.authZeroUserId);
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`ProfilesService.removeProfile`, e.message);
        }
        return true;
    }
    async setPrimaryOrganisation(profileId, organisationId, organisationName, position, preApprove) {
        var _a;
        this.logger.info('ProfilesService.setPrimaryOrganisation', {
            profileId,
            organisationId,
            organisationName,
            position,
        });
        if (!organisationName && !organisationId) {
            this.errorHelper.throwHttpException('ProfilesService.setPrimaryOrganisation', 'OrganisationId or OrganisationName should be set');
        }
        if (organisationName && organisationId) {
            this.errorHelper.throwHttpException('ProfilesService.setPrimaryOrganisation', `OrganisationId and OrganisationName can't be set at the same time`);
        }
        let oldPrimaryMembershipOrganisationId = null;
        let newActivePrimaryMembershipOrganisationId = null;
        const transaction = await this.sequelize.transaction();
        try {
            let primaryMembership = await this.membershipsService.findOne({
                profileId,
                isPrimary: true,
            }, { transaction });
            if (organisationId) {
                const existingMembership = await this.membershipsService.findOne({
                    profileId,
                    organisationId,
                }, { transaction });
                if (existingMembership &&
                    existingMembership.status === membership_model_1.MembershipStatus.Rejected) {
                    throw new Error(`You can't set this primary Organisation, you've been rejected earlier`);
                }
                if (primaryMembership &&
                    primaryMembership.organisationId !== organisationId) {
                    if (!primaryMembership.permissions.includes(membership_model_1.MembershipPermission.Owner)) {
                        await this.membershipsService.removeById(primaryMembership.id, {
                            transaction,
                        });
                    }
                    else {
                        await this.membershipsService.updateById(primaryMembership.id, { isPrimary: false }, {
                            transaction,
                        });
                    }
                    if (primaryMembership.organisationId) {
                        const organisation = await this.organisationsService.findById(primaryMembership.organisationId, { transaction });
                        if (organisation.parentOrganisations.length) {
                            for (const parent of organisation.parentOrganisations) {
                                await this.removeMembershipAndFollowersFromParentOrg({
                                    profileId,
                                    parentId: parent.id,
                                });
                            }
                        }
                        await this.membershipsService.removeExistingMemberships(profileId, {
                            transaction,
                        });
                        oldPrimaryMembershipOrganisationId =
                            primaryMembership.organisationId;
                    }
                    primaryMembership = null;
                }
                if (primaryMembership) {
                    await this.membershipsService.updateById(primaryMembership.id, {
                        position: position || primaryMembership.position || '',
                    }, { transaction });
                }
                else {
                    if (existingMembership) {
                        await this.membershipsService.updateById(existingMembership.id, {
                            isPrimary: true,
                            position: position || existingMembership.position || '',
                        }, { transaction });
                        newActivePrimaryMembershipOrganisationId =
                            existingMembership.organisationId;
                        const userParents = await this.findById(profileId);
                        if (userParents.parentOrganisations.length) {
                            for (const parent of userParents.parentOrganisations) {
                                await this.removeProfileParentOrganisation({
                                    profileId,
                                    parentId: parent.id,
                                });
                                await this.removeMembershipAndFollowersFromParentOrg({
                                    profileId,
                                    parentId: parent.id,
                                });
                            }
                        }
                        if (existingMembership.organisationId) {
                            const organisation = await this.organisationsService.findById(existingMembership.organisationId, { transaction });
                            if (organisation.parentOrganisations.length) {
                                for (const parent of organisation.parentOrganisations) {
                                    await this.addMembershipAndFollowersToParentOrg({
                                        profileId,
                                        parentId: parent.id,
                                    }, false);
                                }
                            }
                        }
                    }
                    else {
                        const organisation = await this.organisationsService.findById(organisationId, { transaction });
                        await this.membershipsService.create({
                            isPrimary: true,
                            organisationId,
                            profileId,
                            status: membership_model_1.MembershipStatus.Pending,
                            permissions: [membership_model_1.MembershipPermission.Linked],
                            position,
                            isAutoApprove: preApprove ? true : false,
                        }, { transaction });
                        if (organisation.isPublic) {
                            await this.followersService.addOrApproveFollower(organisationId, profileId, undefined);
                            const userParents = await this.findById(profileId);
                            if (userParents.parentOrganisations.length) {
                                for (const parent of userParents.parentOrganisations) {
                                    await this.removeProfileParentOrganisation({
                                        profileId,
                                        parentId: parent.id,
                                    });
                                    await this.removeMembershipAndFollowersFromParentOrg({
                                        profileId,
                                        parentId: parent.id,
                                    });
                                }
                            }
                            if (organisation.parentOrganisations.length) {
                                for (const parent of organisation.parentOrganisations) {
                                    await this.addMembershipAndFollowersToParentOrg({
                                        profileId,
                                        parentId: parent.id,
                                    }, false);
                                }
                            }
                        }
                        const organisationMemberships = await this.membershipsService.findAll({
                            organisationId,
                            status: membership_model_1.MembershipStatus.Active,
                            permissions: {
                                [sequelize_1.Op.overlap]: [
                                    membership_model_1.MembershipPermission.Owner,
                                    membership_model_1.MembershipPermission.Admin,
                                    membership_model_1.MembershipPermission.HiddenAdmin,
                                    membership_model_1.MembershipPermission.Manager,
                                ],
                            },
                        }, { transaction });
                        for (const membership of organisationMemberships) {
                            await this.notificationsService.createNotification({
                                ownerProfileId: membership.profileId,
                                profileId: profileId,
                                membershipId: membership.id,
                                type: preApprove
                                    ? notifications_args_1.NotificationType.MembershipAutoApprove
                                    : notifications_args_1.NotificationType.MembershipRequested,
                            }, {
                                transaction,
                            });
                        }
                        const profileIds = organisationMemberships.map(user => user.profileId);
                        const organisationDetails = await this.organisationsService.findById(organisationId);
                        const profileDetails = await this.findById(profileId);
                        const message = preApprove
                            ? `${profileDetails.name} has joined ${organisationDetails.name} using a signup invite link.`
                            : `${profileDetails.name} has requested to join ${organisationDetails.name}. View this request to confirm their employment.`;
                        await this.notificationsService.sendPushNotification({
                            profileIds,
                            messageType: preApprove
                                ? notifications_args_1.NotificationMessage.MembershipAutoApprove
                                : notifications_args_1.NotificationMessage.MembershipRequested,
                            messageContent: message,
                            route: preApprove
                                ? ''
                                : (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.organisationSettingsEmployees, {
                                    organisationId: organisationDetails === null || organisationDetails === void 0 ? void 0 : organisationDetails.id,
                                }),
                        });
                    }
                }
            }
            else {
                if (primaryMembership && primaryMembership.organisationId) {
                    if (!primaryMembership.permissions.includes(membership_model_1.MembershipPermission.Owner)) {
                        await this.membershipsService.removeById(primaryMembership.id, {
                            transaction,
                        });
                    }
                    else {
                        await this.membershipsService.updateById(primaryMembership.id, { isPrimary: false }, {
                            transaction,
                        });
                    }
                    const organisation = await this.organisationsService.findById(primaryMembership.organisationId, { transaction });
                    if (organisation.parentOrganisations.length) {
                        for (const parent of organisation.parentOrganisations) {
                            await this.removeMembershipAndFollowersFromParentOrg({
                                profileId,
                                parentId: parent.id,
                            });
                        }
                    }
                    oldPrimaryMembershipOrganisationId = primaryMembership.organisationId;
                    if (primaryMembership.organisationId) {
                        await this.membershipsService.removeExistingMemberships(profileId, {
                            transaction,
                        });
                    }
                    primaryMembership = null;
                }
                if (primaryMembership) {
                    await this.membershipsService.updateById(primaryMembership.id, {
                        position: position || primaryMembership.position || '',
                        organisationName,
                    }, { transaction });
                }
                else {
                    await this.membershipsService.create({
                        isPrimary: true,
                        organisationName,
                        profileId,
                        status: membership_model_1.MembershipStatus.Active,
                        permissions: [],
                        position,
                    }, { transaction });
                }
            }
            await transaction.commit();
        }
        catch (e) {
            await transaction.rollback();
            oldPrimaryMembershipOrganisationId = null;
            newActivePrimaryMembershipOrganisationId = null;
            this.errorHelper.throwHttpException(`ProfilesService.setPrimaryOrganisation`, e.message);
        }
        const primaryMembership = await this.membershipsService.findOne({
            profileId,
            isPrimary: true,
        });
        await this.updateById(profileId, {
            primaryMembershipId: primaryMembership ? primaryMembership.id : null,
        });
        try {
            await this.streamFollowersService.updatePrimaryOrganisation(profileId, oldPrimaryMembershipOrganisationId, newActivePrimaryMembershipOrganisationId);
        }
        catch (e) {
            this.logger.error(`StreamFollowersService Error: ${e.message}`, (_a = e.response) === null || _a === void 0 ? void 0 : _a.body);
        }
        return true;
    }
    async sendEmailVerification(profileId) {
        this.logger.verbose('ProfilesService.sendEmailVerification', {
            profileId,
        });
        const profile = await this.findById(profileId);
        return this.authZeroService.sendEmailVerification(profile.authZeroUserId);
    }
    async changeEmail(profileId, newEmail) {
        this.logger.verbose('ProfilesService.changeEmail', {
            profileId,
        });
        const existingProfile = await this.findOne({
            email: newEmail,
        });
        if (existingProfile) {
            this.errorHelper.throwHttpException(`ProfilesService.changeEmail`, `The new email you specified is already in use by another Hablo user account.`);
        }
        const authZeroUsers = await this.authZeroService.getUsersByEmail(newEmail);
        if (authZeroUsers.length > 0) {
            this.errorHelper.throwHttpException(`ProfilesService.changeEmail`, `There is a duplicate email in our authentication system (Auth0). <NAME_EMAIL> for assistance.`);
        }
        const profile = await this.findById(profileId);
        if (profile.email === newEmail) {
            this.errorHelper.throwHttpException(`ProfilesService.changeEmail`, `The new email specified is the same as the email you already have set.`);
        }
        await this.authZeroService.changeEmail(profile.authZeroUserId, newEmail);
        await this.updateById(profileId, {
            email: newEmail,
            isEmailVerified: false,
        });
        return true;
    }
    async isEmailVerified(profile) {
        this.logger.verbose('ProfilesService.isEmailVerified', {
            profileId: profile.id,
        });
        if (profile.isEmailVerified) {
            return true;
        }
        const authZeroUser = await this.authZeroService.getAuthZeroUser(profile.authZeroUserId);
        if (authZeroUser['email_verified']) {
            this.logger.verbose('ProfilesService.isEmailVerified email verified by auth0');
            await this.updateById(profile.id, { isEmailVerified: true });
            const transaction = await this.sequelize.transaction();
            try {
                const primaryMembership = await this.membershipsService.findOne({
                    profileId: profile.id,
                    isPrimary: true,
                });
                if (primaryMembership) {
                    if (primaryMembership.status === membership_model_1.MembershipStatus.Pending) {
                        if (primaryMembership.isAutoApprove) {
                            const organisation = await this.organisationsService.findById(primaryMembership.organisationId);
                            const user = await this.findById(profile.id);
                            const getDomain = user.email.split('@');
                            const emailDomain = getDomain[1];
                            const findDomain = Array.isArray(organisation.preApprovedDomains)
                                ? organisation.preApprovedDomains.includes(emailDomain)
                                : '';
                            if (findDomain) {
                                await this.membershipsService.updateMembershipStatus(primaryMembership.organisationId, profile.id, membership_model_1.MembershipActionType.Accept, { currentProfileId: profile.id, transaction });
                            }
                        }
                    }
                }
                await transaction.commit();
            }
            catch (e) {
                await transaction.rollback();
                this.errorHelper.throwHttpException(`ProfilesService.isEmailVerified`, e.message);
            }
            return true;
        }
        return false;
    }
    async updateProfile(id, updateProfileInput) {
        this.logger.info('ProfilesService.updatePost', {
            id,
            updateProfileInput,
        });
        const profile = await this.findById(id);
        const updateProfileDto = (0, lodash_1.omit)(updateProfileInput, 'useAutofollow');
        const useAutofollow = updateProfileInput.useAutofollow;
        if (updateProfileInput.timezone) {
            updateProfileDto.timezoneOffset = (0, get_timezone_offset_1.default)(updateProfileInput.timezone);
        }
        updateProfileDto.bio &&
            (updateProfileDto.bio = updateProfileDto.bio.replace(/\n\s*\n\s*\n/g, '\n\n'));
        if (!profile.notificationPreference) {
            let preference;
            if (profile.receiveNotificationEmails) {
                preference = {
                    events: ['push', 'sms', 'desktop', 'email'],
                    messages: profile.receiveNewMessagesEmails
                        ? ['push', 'sms', 'desktop', 'email']
                        : ['push', 'sms', 'desktop'],
                    webinars: ['push', 'sms', 'desktop', 'email'],
                    incentives: ['push', 'sms', 'desktop', 'email'],
                    connections: ['push', 'sms', 'desktop', 'email'],
                    interaction: ['push', 'sms', 'desktop', 'email'],
                    invitations: ['push', 'sms', 'desktop', 'email'],
                    mentionsInPosts: ['push', 'sms', 'desktop', 'email'],
                    followSuggestions: ['push', 'sms', 'desktop', 'email'],
                    organisationsYouManage: ['push', 'sms', 'desktop', 'email'],
                };
            }
            else {
                preference = {
                    events: ['push', 'sms', 'desktop'],
                    messages: profile.receiveNewMessagesEmails
                        ? ['push', 'sms', 'desktop', 'email']
                        : ['push', 'sms', 'desktop'],
                    webinars: ['push', 'sms', 'desktop'],
                    incentives: ['push', 'sms', 'desktop'],
                    connections: ['push', 'sms', 'desktop'],
                    interaction: ['push', 'sms', 'desktop'],
                    invitations: ['push', 'sms', 'desktop'],
                    mentionsInPosts: ['push', 'sms', 'desktop'],
                    followSuggestions: ['push', 'sms', 'desktop'],
                    organisationsYouManage: ['push', 'sms', 'desktop'],
                };
            }
            updateProfileDto.notificationPreference = preference;
        }
        const updatedProfile = await this.updateById(id, updateProfileDto);
        if (updatedProfile.email &&
            updatedProfile.name &&
            updatedProfile.dateOfBirth &&
            updatedProfile.gender &&
            updatedProfile.location) {
            await this.gettingStartedStepsService.createGettingStartedStep({
                step: getting_started_steps_args_1.GettingStartedStepEnum.FillBasicDetails,
                profileId: updatedProfile.id,
            });
        }
        if (updatedProfile.headline) {
            await this.gettingStartedStepsService.createGettingStartedStep({
                step: getting_started_steps_args_1.GettingStartedStepEnum.WriteHeadlineBio,
                profileId: updatedProfile.id,
            });
        }
        if (updatedProfile.image &&
            !updatedProfile.image.includes('gravatar.com')) {
            await this.gettingStartedStepsService.createGettingStartedStep({
                step: getting_started_steps_args_1.GettingStartedStepEnum.AddProfilePic,
                profileId: updatedProfile.id,
            });
        }
        if (useAutofollow) {
            await this.autofollowService.followRegionOrgs(updatedProfile.id, updateProfileInput.regions);
        }
        if (updateProfileInput.name || updateProfileInput.image) {
            await this.chatService.upsertUser(updatedProfile.id);
        }
        if (updatedProfile.streamUserId &&
            (updateProfileInput.primaryMembershipId ||
                updateProfileInput.name ||
                updateProfileInput.image))
            await this.streamService.updateUser(updatedProfile.id);
        if (updatedProfile.isComplete === true) {
            await this.checkForEmailInvitations(updatedProfile.email, updatedProfile.id);
            await this.gettingStartedStepsService.createGettingStartedStep({
                step: getting_started_steps_args_1.GettingStartedStepEnum.AddWork,
                profileId: updatedProfile.id,
            });
        }
        await this.achievementsService.addProfilePerfectAchievement({
            profileId: updatedProfile.id,
        });
        return updatedProfile;
    }
    async addToProfileIdArray(fieldName, id, options) {
        const profile = await this.findById(options.profileId, {
            transaction: options.transaction,
        });
        if (!profile)
            return;
        await this.updateById(profile.id, {
            [fieldName]: underscore_1.Underscore.uniq([...profile[fieldName], id]),
        }, { transaction: options.transaction });
    }
    async removeFromProfileIdArray(fieldName, id, options) {
        const profile = await this.findById(options.profileId, {
            transaction: options.transaction,
        });
        if (!profile)
            return;
        await this.updateById(profile.id, {
            [fieldName]: underscore_1.Underscore.uniq(profile[fieldName].filter(savedId => savedId !== id)),
        }, { transaction: options.transaction });
    }
    async onApplicationBootstrap() {
        if (process.env.NODE_ENV !== 'development') {
            await this.updatePrimaryMembershipIds();
            await this.updateProfileIds();
        }
    }
    async updatePrimaryMembershipIds() {
        const profiles = await this.findAll({
            primaryMembershipId: null,
            id: {
                [sequelize_1.Op.notLike]: 'fake_%',
            },
        });
        for (const profile of profiles) {
            await this.updatePrimaryMembershipId(profile);
        }
    }
    async updatePrimaryMembershipId(profile, transaction) {
        const primaryMembership = await this.membershipsService.findOne({
            profileId: profile.id,
            isPrimary: true,
        }, { transaction });
        if (primaryMembership) {
            await this.updateById(profile.id, {
                primaryMembershipId: primaryMembership.id,
            }, { transaction });
        }
    }
    async updateProfileIds() {
        const profiles = await this.findAll({
            profileIdsConnections: [],
            profileIdsInvitationSent: [],
            profileIdsInvitationSentRejected: [],
            profileIdsInvitationReceived: [],
            profileIdsInvitationReceivedRejected: [],
            id: {
                [sequelize_1.Op.notLike]: 'fake_%',
            },
        });
        for (const profile of profiles) {
            const profileIdsConnections = await this.helper.getConnectionProfileIds(profile.id);
            const profileIdsInvitationSent = await this.helper.getInvitationSentProfileIds(profile.id);
            const profileIdsInvitationSentRejected = await this.helper.getInvitationSentRejectedProfileIds(profile.id);
            const profileIdsInvitationReceived = await this.helper.getInvitationReceivedProfileIds(profile.id);
            const profileIdsInvitationReceivedRejected = await this.helper.getInvitationReceivedRejectedProfileIds(profile.id);
            await this.updateById(profile.id, {
                profileIdsConnections,
                profileIdsInvitationSent,
                profileIdsInvitationSentRejected,
                profileIdsInvitationReceived,
                profileIdsInvitationReceivedRejected,
            });
        }
    }
    async checkForEmailInvitations(invitedEmail, profileId) {
        this.logger.verbose('ProfileService.helper.checkForEmailInvitations', {
            profileId,
            invitedEmail,
        });
        const invites = await this.emailInvitationsService.findAll({
            senderProfileId: {
                [sequelize_1.Op.ne]: profileId,
            },
            invitedEmail,
            sentConnectionRequestId: {
                [sequelize_1.Op.eq]: null,
            },
        });
        const profile = await this.findById(profileId);
        for (const invite of invites) {
            const existingConnectionRequest = await this.connectionRequestsService.findOne({
                [sequelize_1.Op.or]: [
                    {
                        receiverProfileId: profileId,
                        senderProfileId: invite.senderProfileId,
                    },
                    {
                        receiverProfileId: invite.senderProfileId,
                        senderProfileId: profileId,
                    },
                ],
            });
            if (!existingConnectionRequest) {
                const connectionRequest = await this.connectionRequestsService.connect({
                    senderProfileId: invite.senderProfileId,
                    receiverProfileId: profileId,
                    status: connection_request_model_1.ConnectionRequestStatus.Pending,
                });
                await this.emailInvitationsService.updateById(invite.id, {
                    sentConnectionRequestId: connectionRequest.id,
                });
                const previousActivity = await this.activitiesService.findOne({
                    type: activities_args_1.ActivityType.EmailInvitationRegistration,
                    profileId: invite.senderProfileId,
                    data: {
                        [sequelize_1.Op.contains]: {
                            receiverProfileId: profileId,
                            senderProfileId: invite.senderProfileId,
                        },
                    },
                });
                await this.activitiesService.createUserActivity({
                    type: activities_args_1.ActivityType.EmailInvitationRegistration,
                    schema: create_user_activity_data_dto_1.emailInvitationRegistrationActivityDataDto,
                    data: {
                        senderProfileId: invite.senderProfileId,
                        receiverProfileId: profileId,
                    },
                    profileId: invite.senderProfileId,
                    addLoyaltyPoints: !previousActivity,
                    placeholders: {
                        email: invitedEmail,
                        name: profile.name,
                        userId: profile.id,
                    },
                });
                await this.achievementsService.upsertIndustryInfluencerAchievement({
                    profileId: invite.senderProfileId,
                });
            }
            else {
                await this.emailInvitationsService.updateById(invite.id, {
                    sentConnectionRequestId: existingConnectionRequest.id,
                });
            }
        }
    }
    async addProfileParentOrganisation(data) {
        this.logger.info('ProfileService.addProfileParentOrganisation', {
            data,
        });
        const transaction = await this.sequelize.transaction();
        try {
            const profile = await this.findById(data.profileId);
            if (!profile) {
                throw new Error(`Profile not found`);
            }
            const profileMembership = await this.membershipsService.findOne({
                profileId: data.profileId,
                isPrimary: true,
                status: membership_model_1.MembershipStatus.Active,
                organisationId: {
                    [sequelize_1.Op.ne]: null,
                },
            });
            if (profileMembership) {
                throw new Error(`You cannot add your parent as you are assigned to an organisation.`);
            }
            const existingMembership = await this.membershipsService.findOne({
                profileId: data.profileId,
                status: membership_model_1.MembershipStatus.Active,
                organisationId: data.parentId,
            });
            if (existingMembership) {
                return profile;
            }
            const parentOrganisation = await this.organisationsService.findById(data.parentId);
            if (!parentOrganisation) {
                throw new Error(`Parent Organisation does not exist`);
            }
            const parentOrgs = profile.parentOrganisations;
            if (parentOrgs.length === 1) {
                throw new Error(`Cannot add more than 1 parent organisation`);
            }
            const index = parentOrgs.findIndex(x => x.id === data.parentId);
            if (parentOrgs.length && index > -1) {
                throw new Error(`Parent Organisation already added`);
            }
            parentOrgs.push({ id: data.parentId });
            await this.updateById(profile.id, {
                parentOrganisations: parentOrgs,
            }, { transaction });
            await this.membershipsService.create({
                isPrimary: false,
                organisationId: data.parentId,
                profileId: data.profileId,
                status: membership_model_1.MembershipStatus.Pending,
                permissions: [membership_model_1.MembershipPermission.Member],
            }, { transaction });
            const organisationMemberships = await this.membershipsService.findAll({
                organisationId: data.parentId,
                status: membership_model_1.MembershipStatus.Active,
                permissions: {
                    [sequelize_1.Op.overlap]: [
                        membership_model_1.MembershipPermission.Owner,
                        membership_model_1.MembershipPermission.Admin,
                        membership_model_1.MembershipPermission.HiddenAdmin,
                        membership_model_1.MembershipPermission.Manager,
                    ],
                },
            }, { transaction });
            for (const membership of organisationMemberships) {
                await this.notificationsService.createNotification({
                    ownerProfileId: membership.profileId,
                    profileId: data.profileId,
                    membershipId: membership.id,
                    type: notifications_args_1.NotificationType.ParentMembershipRequested,
                }, {
                    transaction,
                });
            }
            await transaction.commit();
            const profileIds = organisationMemberships.map(user => user.profileId);
            const replacements = [profile.name, parentOrganisation.name];
            await this.notificationsService.sendPushNotification({
                profileIds,
                replacements,
                messageType: notifications_args_1.NotificationMessage.ParentMembershipRequested,
                route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.organisationSettingsMembers, {
                    organisationId: data.parentId,
                }),
            });
            return profile;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`ProfileService.addProfileParentOrganisation`, e.message);
        }
    }
    async removeProfileParentOrganisation(data) {
        this.logger.info('ProfileService.removeProfileParentOrganisation', {
            data,
        });
        const transaction = await this.sequelize.transaction();
        try {
            const profile = await this.findById(data.profileId);
            if (!profile) {
                throw new Error(`Profile not found`);
            }
            const parentOrganisation = await this.organisationsService.findById(data.parentId);
            if (!parentOrganisation) {
                throw new Error(`Parent Organisation not exists`);
            }
            const parentOrgs = profile.parentOrganisations;
            const index = parentOrgs.findIndex(x => x.id === data.parentId);
            if (parentOrgs.length && index < 0) {
                throw new Error(`Parent Organisation not found`);
            }
            parentOrgs.splice(index, 1);
            await this.updateById(profile.id, {
                parentOrganisations: parentOrgs,
            }, { transaction });
            await this.membershipsService.remove({
                where: {
                    profileId: data.profileId,
                    organisationId: data.parentId,
                },
                transaction: transaction,
            });
            await transaction.commit();
            return profile;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`ProfileService.removeProfileParentOrganisation`, e.message);
        }
    }
    async addMembershipAndFollowersToParentOrg(data, sendNotification) {
        this.logger.info('ProfileService.addMembershipAndFollowersToParentOrg', {
            data,
        });
        const transaction = await this.sequelize.transaction();
        try {
            await this.membershipsService.addMemberToParent(data.parentId, data.profileId, null, { transaction });
            if (sendNotification) {
                const membership = await this.membershipsService.findOne({
                    organisationId: data.parentId,
                    profileId: data.profileId,
                });
                await this.notificationsService.createNotification({
                    ownerProfileId: membership.profileId,
                    profileId: data.profileId,
                    membershipId: membership.id,
                    type: notifications_args_1.NotificationType.ParentMembershipAccepted,
                }, {
                    transaction,
                });
                const profileIds = [membership.profileId];
                const replacementOrg = await this.organisationsService.findById(data.parentId);
                const replacements = [replacementOrg.name];
                await this.notificationsService.sendPushNotification({
                    profileIds,
                    replacements,
                    messageType: notifications_args_1.NotificationMessage.ParentMembershipAccepted,
                    route: (0, notificationLayoutHelper_1.encodePathParams)(notificationLayoutHelper_1.Routes.profile),
                });
            }
            await transaction.commit();
            return true;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`ProfileService.addMembershipAndFollowersToParentOrg`, e.message);
        }
    }
    async removeMembershipAndFollowersFromParentOrg(data) {
        this.logger.info('ProfileService.removeMembershipAndFollowersFromParentOrg', {
            data,
        });
        const transaction = await this.sequelize.transaction();
        try {
            await this.membershipsService.removeMemberFromParent(data.parentId, data.profileId, null, { transaction });
            await transaction.commit();
            return true;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`ProfileService.removeMembershipAndFollowersFromParentOrg`, e.message);
        }
    }
    async mobileDevicesLoggedInCount(data) {
        this.logger.info('ProfilesService.mobileDevicesLoggedInCount', {
            data,
        });
        const transaction = await this.sequelize.transaction();
        try {
            const profile = await this.findById(data.profileId);
            if (!profile) {
                throw new Error(`Profile not found`);
            }
            if (data.type === profiles_args_1.DeviceLogInCount.Increment) {
                await this.updateById(data.profileId, {
                    noOfMobileDevicesLoggedIn: profile.noOfMobileDevicesLoggedIn + 1,
                });
            }
            else if (data.type === profiles_args_1.DeviceLogInCount.Decrement &&
                profile.noOfMobileDevicesLoggedIn > 0) {
                await this.updateById(data.profileId, {
                    noOfMobileDevicesLoggedIn: profile.noOfMobileDevicesLoggedIn - 1,
                });
            }
            await transaction.commit();
            return true;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`ProfilesService.mobileDevicesLoggedInCount`, e.message);
        }
    }
    async logActiveSession({ profileId, data, }) {
        this.logger.info('ProfilesService.logActiveSession', Object.assign({ profileId }, data));
        const { notificationEnabled, variableMinutes, variableRewardPoints } = data;
        const profile = await this.findById(profileId);
        const timezone = profile.timezone || 'UTC';
        const startOfDay = moment_timezone_1.default.tz(timezone).startOf('day').utc().toDate();
        const endOfDay = moment_timezone_1.default.tz(timezone).endOf('day').utc().toDate();
        if (!variableMinutes && !variableRewardPoints) {
            const existingLoginActivity = await this.activitiesService.findOne({
                profileId,
                type: activities_args_1.ActivityType.ActiveSession,
                createdAt: {
                    [sequelize_1.Op.between]: [startOfDay, endOfDay],
                },
            });
            if (!existingLoginActivity) {
                await this.achievementsService.addHotStreakAchievement({
                    profileId,
                });
                const lastLoginActivity = await this.activitiesService.findOne({
                    profileId,
                    type: activities_args_1.ActivityType.ActiveSession,
                }, {
                    order: [['createdAt', 'DESC']],
                });
                let newStreakCount = 1;
                if (lastLoginActivity) {
                    const createdAt = lastLoginActivity.createdAt;
                    const startOfLastActivity = (0, moment_timezone_1.default)(createdAt)
                        .tz(timezone)
                        .startOf('day')
                        .utc();
                    const diffDays = (0, moment_timezone_1.default)(startOfDay).diff(startOfLastActivity, 'days');
                    if (diffDays > 1) {
                        newStreakCount = 1;
                    }
                    else {
                        newStreakCount = lastLoginActivity.data.streakCount + 1;
                    }
                }
                await this.activitiesService.createUserActivity({
                    type: activities_args_1.ActivityType.ActiveSession,
                    profileId,
                    data: {
                        streakCount: newStreakCount,
                    },
                    schema: create_user_activity_data_dto_1.emptyActivityDataDto,
                });
                const streakNotificationCount = [
                    3, 5, 10, 20, 30, 50, 100, 200, 300, 365,
                ];
                const streakCount = await this.activitiesService.getUserLoginStreak({
                    profileId,
                });
                if (streakNotificationCount.includes(streakCount)) {
                    await this.notificationsService.createNotification({
                        ownerProfileId: profileId,
                        type: notifications_args_1.NotificationType.DailyLoginStreak,
                        profileId,
                        data: {
                            streakCount,
                        },
                    }, {});
                }
            }
        }
        else if (profile.sellHolidays &&
            variableMinutes &&
            variableRewardPoints) {
            const existingLoginActivity = await this.activitiesService.findOne({
                profileId,
                type: activities_args_1.ActivityType.VariableLoginReward,
                createdAt: {
                    [sequelize_1.Op.between]: [startOfDay, endOfDay],
                },
            });
            if (!existingLoginActivity) {
                await this.activitiesService.createUserActivity({
                    type: activities_args_1.ActivityType.VariableLoginReward,
                    profileId,
                    data: {
                        minutes: variableMinutes,
                    },
                    schema: create_user_activity_data_dto_1.variableLoginRewardActivityDataDto,
                    addLoyaltyPoints: true,
                    variableRewardPoints,
                });
            }
        }
        if (notificationEnabled) {
            await this.activitiesService.createUserActivity({
                type: activities_args_1.ActivityType.NotificationEnabled,
                profileId,
                data: {},
                schema: create_user_activity_data_dto_1.emptyActivityDataDto,
                addLoyaltyPoints: true,
                checkForUniqueEntry: true,
            });
        }
    }
    async incrementConnectionsCount(ids, by, transaction) {
        await this.getModel().increment('connectionsCount', {
            by,
            where: { id: { [sequelize_1.Op.in]: ids } },
            transaction,
        });
    }
    async findProfilesByTimezoneOffset({ maxOffset, minOffset, attributes, }) {
        this.logger.verbose('ProfilesService.findProfilesByTimezoneOffset', {
            minOffset,
            maxOffset,
        });
        return this.findAll({
            timezoneOffset: {
                [sequelize_1.Op.gte]: minOffset,
                [sequelize_1.Op.lte]: maxOffset,
            },
            sellHolidays: true,
        }, {
            attributes,
        });
    }
    async updateProfileReferringOrganisation(profileId, referringOrganisationId) {
        this.logger.info('ProfilesService.updateProfileReferringOrganisation', {
            profileId,
            referringOrganisationId,
        });
        const profile = await this.findById(profileId);
        if (!profile) {
            throw new Error(`Profile not found for profileId: ${profileId}`);
        }
        const organisation = await this.organisationsService.findById(referringOrganisationId);
        if (!organisation) {
            throw new Error(`Organisation not found for id: ${referringOrganisationId}`);
        }
        const updatedProfile = await this.updateById(profileId, {
            referredByOrganisationId: referringOrganisationId,
        });
        this.logger.info(`Successfully updated profile ${profileId} with referring organisation ${referringOrganisationId}`);
        return updatedProfile;
    }
};
exports.ProfilesService = ProfilesService;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_repository_1.ProfilesRepository)),
    __metadata("design:type", profiles_repository_1.ProfilesRepository)
], ProfilesService.prototype, "profilesRepository", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_helper_1.ProfilesServiceHelper)),
    __metadata("design:type", profiles_service_helper_1.ProfilesServiceHelper)
], ProfilesService.prototype, "helper", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], ProfilesService.prototype, "membershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => autofollow_service_1.AutofollowService)),
    __metadata("design:type", autofollow_service_1.AutofollowService)
], ProfilesService.prototype, "autofollowService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => connections_service_1.ConnectionsService)),
    __metadata("design:type", connections_service_1.ConnectionsService)
], ProfilesService.prototype, "connectionsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => connection_requests_service_1.ConnectionRequestsService)),
    __metadata("design:type", connection_requests_service_1.ConnectionRequestsService)
], ProfilesService.prototype, "connectionRequestsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => followers_service_1.FollowersService)),
    __metadata("design:type", followers_service_1.FollowersService)
], ProfilesService.prototype, "followersService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], ProfilesService.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => notifications_service_1.NotificationsService)),
    __metadata("design:type", notifications_service_1.NotificationsService)
], ProfilesService.prototype, "notificationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => activities_service_1.ActivitiesService)),
    __metadata("design:type", activities_service_1.ActivitiesService)
], ProfilesService.prototype, "activitiesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => chat_service_1.ChatService)),
    __metadata("design:type", chat_service_1.ChatService)
], ProfilesService.prototype, "chatService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => authz_service_1.AuthZeroService)),
    __metadata("design:type", authz_service_1.AuthZeroService)
], ProfilesService.prototype, "authZeroService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => event_invitations_service_1.EventInvitationsService)),
    __metadata("design:type", event_invitations_service_1.EventInvitationsService)
], ProfilesService.prototype, "eventInvitationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => experiences_service_1.ExperiencesService)),
    __metadata("design:type", experiences_service_1.ExperiencesService)
], ProfilesService.prototype, "experiencesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentive_bookings_service_1.IncentiveBookingsService)),
    __metadata("design:type", incentive_bookings_service_1.IncentiveBookingsService)
], ProfilesService.prototype, "incentiveBookingsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => incentive_participants_service_1.IncentiveParticipantsService)),
    __metadata("design:type", incentive_participants_service_1.IncentiveParticipantsService)
], ProfilesService.prototype, "incentiveParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinar_participants_service_1.WebinarParticipantsService)),
    __metadata("design:type", webinar_participants_service_1.WebinarParticipantsService)
], ProfilesService.prototype, "webinarParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => partnership_requests_service_1.PartnershipRequestsService)),
    __metadata("design:type", partnership_requests_service_1.PartnershipRequestsService)
], ProfilesService.prototype, "partnershipRequestsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => posts_service_1.PostsService)),
    __metadata("design:type", posts_service_1.PostsService)
], ProfilesService.prototype, "postsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => email_invitations_service_1.EmailInvitationsService)),
    __metadata("design:type", email_invitations_service_1.EmailInvitationsService)
], ProfilesService.prototype, "emailInvitationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => stream_followers_service_1.StreamFollowersService)),
    __metadata("design:type", stream_followers_service_1.StreamFollowersService)
], ProfilesService.prototype, "streamFollowersService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => partnerships_service_1.PartnershipsService)),
    __metadata("design:type", partnerships_service_1.PartnershipsService)
], ProfilesService.prototype, "partnershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => getting_started_steps_service_1.GettingStartedStepsService)),
    __metadata("design:type", getting_started_steps_service_1.GettingStartedStepsService)
], ProfilesService.prototype, "gettingStartedStepsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => achievements_service_1.AchievementsService)),
    __metadata("design:type", achievements_service_1.AchievementsService)
], ProfilesService.prototype, "achievementsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => stream_service_1.StreamService)),
    __metadata("design:type", stream_service_1.StreamService)
], ProfilesService.prototype, "streamService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", sequelize_typescript_1.Sequelize)
], ProfilesService.prototype, "sequelize", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], ProfilesService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], ProfilesService.prototype, "errorHelper", void 0);
exports.ProfilesService = ProfilesService = __decorate([
    (0, common_1.Injectable)()
], ProfilesService);
//# sourceMappingURL=profiles.service.js.map