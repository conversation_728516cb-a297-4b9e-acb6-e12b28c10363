"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PubSubModule = void 0;
const common_1 = require("@nestjs/common");
const apollo_server_express_1 = require("apollo-server-express");
const graphql_redis_subscriptions_1 = require("graphql-redis-subscriptions");
const ioredis_1 = __importDefault(require("ioredis"));
const config_1 = __importDefault(require("../config/config"));
let PubSubModule = class PubSubModule {
};
exports.PubSubModule = PubSubModule;
exports.PubSubModule = PubSubModule = __decorate([
    (0, common_1.Module)({
        providers: ['development', 'production'].includes(config_1.default.NODE_ENV)
            ? [
                {
                    provide: 'PUB_SUB',
                    useFactory: () => {
                        const options = {
                            host: config_1.default.REDIS_HOST,
                            port: config_1.default.REDIS_PORT,
                        };
                        return new graphql_redis_subscriptions_1.RedisPubSub({
                            publisher: new ioredis_1.default(options),
                            subscriber: new ioredis_1.default(options),
                        });
                    },
                },
            ]
            : [
                {
                    provide: 'PUB_SUB',
                    useClass: apollo_server_express_1.PubSub,
                },
            ],
        exports: ['PUB_SUB'],
    })
], PubSubModule);
//# sourceMappingURL=pubsub.module.js.map