"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionWebhook = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const short_uuid_1 = __importDefault(require("short-uuid"));
const graphql_1 = require("@nestjs/graphql");
const sequelize_1 = require("sequelize");
const graphql_type_json_1 = __importDefault(require("graphql-type-json"));
let SubscriptionWebhook = class SubscriptionWebhook extends sequelize_typescript_1.Model {
};
exports.SubscriptionWebhook = SubscriptionWebhook;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], SubscriptionWebhook.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: false }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], SubscriptionWebhook.prototype, "stripeSubscriptionId", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_type_json_1.default, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.JSON,
    }),
    __metadata("design:type", Object)
], SubscriptionWebhook.prototype, "data", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: false }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], SubscriptionWebhook.prototype, "webhookType", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], SubscriptionWebhook.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], SubscriptionWebhook.prototype, "updatedAt", void 0);
exports.SubscriptionWebhook = SubscriptionWebhook = __decorate([
    (0, graphql_1.ObjectType)(),
    sequelize_typescript_1.Table
], SubscriptionWebhook);
//# sourceMappingURL=subscription-webhook.model.js.map