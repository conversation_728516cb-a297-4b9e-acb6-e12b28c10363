"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionWebhookController = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const subscription_webhook_model_1 = require("./models/subscription-webhook.model");
const error_1 = require("../common/helpers/error");
const subscriptions_service_1 = require("../subscriptions/subscriptions.service");
const subscription_model_1 = require("../subscriptions/models/subscription.model");
const payment_transaction_model_1 = require("../payment-transactions/models/payment-transaction.model");
const sequelize_typescript_1 = require("sequelize-typescript");
const payment_transactions_service_1 = require("../payment-transactions/payment-transactions.service");
const organisations_service_1 = require("../organisations/organisations.service");
const subscription_webhook_service_1 = require("./subscription-webhook.service");
const stripe_1 = require("../subscriptions/helpers/stripe");
const partnerships_service_1 = require("../partnerships/partnerships.service");
const partnership_requests_service_1 = require("../partnership-requests/partnership-requests.service");
let SubscriptionWebhookController = class SubscriptionWebhookController {
    async handleStripeWebhook(req, res, body) {
        var _a, _b, _c, _d;
        this.logger.info('handleStripeWebhook triggered');
        if ((_b = (_a = req.body.data) === null || _a === void 0 ? void 0 : _a.object) === null || _b === void 0 ? void 0 : _b.subscription) {
            const newWebhook = new subscription_webhook_model_1.SubscriptionWebhook();
            newWebhook.stripeSubscriptionId = (_d = (_c = req.body.data) === null || _c === void 0 ? void 0 : _c.object) === null || _d === void 0 ? void 0 : _d.subscription;
            newWebhook.data = req.body;
            newWebhook.webhookType = req.body.type;
            await newWebhook.save();
        }
        setTimeout(() => {
            switch (req.body.type) {
                case 'account.updated':
                    this.updateConnectAccountStatus(req.body.data.object);
                    break;
                case 'checkout.session.completed':
                    this.checkoutCompleted(req.body);
                    break;
                case 'invoice.payment_succeeded':
                    this.successSubscriptionPayment(req.body);
                    break;
                case 'invoice.payment_failed':
                    this.failedSubscriptionPayment(req.body);
                    break;
                case 'customer.subscription.updated':
                    this.subscriptionUpdated(req.body);
                    break;
            }
        }, 5000);
        this.logger.info('handleStripeWebhook finished');
        res.sendStatus(200);
    }
    async updateConnectAccountStatus(data) {
        const transaction = await this.sequelize.transaction();
        try {
            const connectAcc = await this.organisationsService.findOne({
                stripeConnectAccount: data.id,
            });
            if (!connectAcc) {
                throw new Error('Connect Account not found.');
            }
            if (data.charges_enabled === true) {
                await this.organisationsService.updateById(connectAcc.id, {
                    isConnectOnboarded: true,
                });
            }
            await transaction.commit();
            return;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`SubscriptionWebhookController.updateConnectAccountStatus`, e.message);
        }
    }
    async checkoutCompleted(webhookData) {
        var _a, _b, _c, _d;
        const transaction = await this.sequelize.transaction();
        try {
            const subscription = await this.subscriptionsService.findOne({
                stripeCheckoutId: (_b = (_a = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _a === void 0 ? void 0 : _a.object) === null || _b === void 0 ? void 0 : _b.id,
            });
            if (subscription) {
                if (subscription.status === subscription_model_1.SubscriptionStatus.Pending) {
                    if (((_d = (_c = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _c === void 0 ? void 0 : _c.object) === null || _d === void 0 ? void 0 : _d.payment_status) === 'unpaid') {
                        await this.subscriptionsService.updateById(subscription.id, {
                            status: subscription_model_1.SubscriptionStatus.Processing,
                        });
                    }
                }
            }
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`SubscriptionWebhookController.checkoutCompleted`, e.message);
        }
    }
    async successSubscriptionPayment(webhookData) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z;
        const transaction = await this.sequelize.transaction();
        try {
            const subscription = await this.subscriptionsService.findOne({
                stripeProductId: (_d = (_c = (_b = (_a = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _a === void 0 ? void 0 : _a.object) === null || _b === void 0 ? void 0 : _b.lines) === null || _c === void 0 ? void 0 : _c.data[0]) === null || _d === void 0 ? void 0 : _d.price.product,
            });
            if (!subscription) {
                throw new Error('Subscription not found');
            }
            let subscriptionData = await this.stripeHelper.retrieveSubscription({
                stripe_subscription_id: (_f = (_e = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _e === void 0 ? void 0 : _e.object) === null || _f === void 0 ? void 0 : _f.subscription,
                connect_id: subscription.stripeConnectId,
            });
            const paymentTransaction = await this.paymentTransactionsService.findOne({
                subscriptionId: subscription.id,
                type: payment_transaction_model_1.TransactionType.Checkout,
                transactionStatus: payment_transaction_model_1.TransactionStatus.Completed,
            });
            if (!paymentTransaction) {
                const partnershipRequest = await this.partnershipRequestsService.findOne({
                    id: subscription.partnershipRequestId,
                });
                if (((_h = (_g = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _g === void 0 ? void 0 : _g.object) === null || _h === void 0 ? void 0 : _h.status) === 'paid') {
                    await this.subscriptionsService.updateById(subscription.id, {
                        stripeSubscriptionId: (_k = (_j = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _j === void 0 ? void 0 : _j.object) === null || _k === void 0 ? void 0 : _k.subscription,
                        stripeCustomerId: (_m = (_l = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _l === void 0 ? void 0 : _l.object) === null || _m === void 0 ? void 0 : _m.customer,
                        status: subscription_model_1.SubscriptionStatus.Active,
                        startedOn: new Date(),
                        stripeCheckoutId: null,
                        checkoutExpiry: null,
                    });
                    let paymentTransaction = await this.paymentTransactionsService.findById(subscription.lastTransactionId);
                    if (!paymentTransaction) {
                        paymentTransaction = new payment_transaction_model_1.PaymentTransaction();
                        paymentTransaction.subscriptionId = subscription.id;
                        paymentTransaction.type = payment_transaction_model_1.TransactionType.Checkout;
                        paymentTransaction.stripeCheckoutId = null;
                        paymentTransaction.stripeInvoiceId = (_p = (_o = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _o === void 0 ? void 0 : _o.object) === null || _p === void 0 ? void 0 : _p.id;
                        paymentTransaction.startDate = new Date(subscriptionData.current_period_start * 1000);
                        paymentTransaction.endDate = new Date(subscriptionData.current_period_end * 1000);
                        paymentTransaction.paymentStatus =
                            (_r = (_q = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _q === void 0 ? void 0 : _q.object) === null || _r === void 0 ? void 0 : _r.status;
                        paymentTransaction.transactionStatus = payment_transaction_model_1.TransactionStatus.Completed;
                        await paymentTransaction.save();
                        await this.subscriptionsService.updateById(subscription.id, {
                            lastTransactionId: paymentTransaction.id,
                        });
                    }
                    else {
                        await this.paymentTransactionsService.updateById(paymentTransaction.id, {
                            stripeCheckoutId: null,
                            stripeInvoiceId: (_t = (_s = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _s === void 0 ? void 0 : _s.object) === null || _t === void 0 ? void 0 : _t.id,
                            startDate: new Date(subscriptionData.current_period_start * 1000),
                            endDate: new Date(subscriptionData.current_period_end * 1000),
                            paymentStatus: (_v = (_u = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _u === void 0 ? void 0 : _u.object) === null || _v === void 0 ? void 0 : _v.status,
                            transactionStatus: payment_transaction_model_1.TransactionStatus.Completed,
                        });
                    }
                    const user = {
                        sub: '',
                        profileId: partnershipRequest.receiverProfileId,
                        picture: '',
                        email: '',
                        name: '',
                        idToken: '',
                        toLogObject: () => { },
                    };
                    await this.partnershipRequestsService.approve(subscription.partnershipRequestId, { currentUser: user });
                }
            }
            else {
                const logged = await this.paymentTransactionsService.findOne({
                    startDate: subscriptionData.current_period_start * 1000,
                    endDate: subscriptionData.current_period_end * 1000,
                    paymentStatus: 'paid',
                    transactionStatus: payment_transaction_model_1.TransactionStatus.Completed,
                });
                if (!logged) {
                    const paymentTransaction = new payment_transaction_model_1.PaymentTransaction();
                    paymentTransaction.subscriptionId = subscription.id;
                    paymentTransaction.type = payment_transaction_model_1.TransactionType.Recurring;
                    paymentTransaction.stripeCheckoutId = null;
                    paymentTransaction.stripeInvoiceId = (_x = (_w = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _w === void 0 ? void 0 : _w.object) === null || _x === void 0 ? void 0 : _x.id;
                    paymentTransaction.startDate = new Date(subscriptionData.current_period_start * 1000);
                    paymentTransaction.endDate = new Date(subscriptionData.current_period_end * 1000);
                    paymentTransaction.paymentStatus = (_z = (_y = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _y === void 0 ? void 0 : _y.object) === null || _z === void 0 ? void 0 : _z.status;
                    paymentTransaction.transactionStatus = payment_transaction_model_1.TransactionStatus.Completed;
                    await paymentTransaction.save();
                    await this.subscriptionsService.updateById(subscription.id, {
                        lastTransactionId: paymentTransaction.id,
                    });
                }
            }
            await transaction.commit();
            return;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`SubscriptionWebhookController.successSubscriptionPayment`, e.message);
        }
    }
    async failedSubscriptionPayment(webhookData) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v;
        const transaction = await this.sequelize.transaction();
        try {
            const subscription = await this.subscriptionsService.findOne({
                stripeCheckoutId: (_b = (_a = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _a === void 0 ? void 0 : _a.object) === null || _b === void 0 ? void 0 : _b.subscription,
            });
            if (!subscription) {
                throw new Error('Subscription not found');
            }
            const paymentTransaction = await this.paymentTransactionsService.findOne({
                subscriptionId: subscription.id,
                type: payment_transaction_model_1.TransactionType.Checkout,
                transactionStatus: payment_transaction_model_1.TransactionStatus.Completed,
            });
            if (paymentTransaction) {
                let currentDate = new Date(((_g = (_f = (_e = (_d = (_c = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _c === void 0 ? void 0 : _c.object) === null || _d === void 0 ? void 0 : _d.lines) === null || _e === void 0 ? void 0 : _e.data[0]) === null || _f === void 0 ? void 0 : _f.period) === null || _g === void 0 ? void 0 : _g.start) * 1000);
                currentDate.setDate(currentDate.getDate() - 7);
                const fromDate = new Date(((_m = (_l = (_k = (_j = (_h = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _h === void 0 ? void 0 : _h.object) === null || _j === void 0 ? void 0 : _j.lines) === null || _k === void 0 ? void 0 : _k.data[0]) === null || _l === void 0 ? void 0 : _l.period) === null || _m === void 0 ? void 0 : _m.start) * 1000);
                const toDate = new Date(currentDate);
                const transactionAttempts = await this.paymentTransactionsService.count({
                    where: {
                        subscriptionId: subscription.id,
                    },
                    order: [['createdAt', 'DESC']],
                });
                let subscriptionData = await this.stripeHelper.retrieveSubscription({
                    stripe_subscription_id: (_p = (_o = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _o === void 0 ? void 0 : _o.object) === null || _p === void 0 ? void 0 : _p.subscription,
                    connect_id: subscription.stripeConnectId,
                });
                if (transactionAttempts + 1 > 0 && transactionAttempts + 1 < 7) {
                    const paymentTransaction = new payment_transaction_model_1.PaymentTransaction();
                    paymentTransaction.subscriptionId = subscription.id;
                    paymentTransaction.type = payment_transaction_model_1.TransactionType.Recurring;
                    paymentTransaction.stripeCheckoutId = null;
                    paymentTransaction.stripeInvoiceId = (_r = (_q = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _q === void 0 ? void 0 : _q.object) === null || _r === void 0 ? void 0 : _r.id;
                    paymentTransaction.startDate = new Date(subscriptionData.current_period_start * 1000);
                    paymentTransaction.endDate = new Date(subscriptionData.current_period_end * 1000);
                    paymentTransaction.paymentStatus = 'failed';
                    paymentTransaction.transactionStatus = payment_transaction_model_1.TransactionStatus.Failed;
                    if (!['', undefined, null].includes((_t = (_s = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _s === void 0 ? void 0 : _s.object) === null || _t === void 0 ? void 0 : _t.hosted_invoice_url)) {
                        paymentTransaction.transactionStatus = payment_transaction_model_1.TransactionStatus.InProgress;
                        paymentTransaction.paymentStatus = 'in_progress';
                    }
                    await paymentTransaction.save();
                    await this.subscriptionsService.updateById(subscription.id, {
                        lastTransactionId: paymentTransaction.id,
                    });
                }
                else if (transactionAttempts + 1 === 7) {
                    await this.subscriptionsService.removeSubscription(subscription.partnershipRequestId);
                    const paymentTransaction = new payment_transaction_model_1.PaymentTransaction();
                    paymentTransaction.subscriptionId = subscription.id;
                    paymentTransaction.type = payment_transaction_model_1.TransactionType.Recurring;
                    paymentTransaction.stripeCheckoutId = null;
                    paymentTransaction.stripeInvoiceId = (_v = (_u = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _u === void 0 ? void 0 : _u.object) === null || _v === void 0 ? void 0 : _v.id;
                    paymentTransaction.startDate = new Date(subscriptionData.current_period_start * 1000);
                    paymentTransaction.endDate = new Date(subscriptionData.current_period_end * 1000);
                    paymentTransaction.paymentStatus = 'failed';
                    paymentTransaction.transactionStatus = payment_transaction_model_1.TransactionStatus.Failed;
                    await paymentTransaction.save();
                    await this.subscriptionsService.updateById(subscription.id, {
                        lastTransactionId: paymentTransaction.id,
                    });
                }
            }
            await transaction.commit();
            return;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`SubscriptionWebhookController.failedSubscriptionPayment`, e.message);
        }
    }
    async subscriptionUpdated(webhookData) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
        const transaction = await this.sequelize.transaction();
        try {
            const subscription = await this.subscriptionsService.findOne({
                stripeSubscriptionId: (_b = (_a = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _a === void 0 ? void 0 : _a.object) === null || _b === void 0 ? void 0 : _b.id,
            });
            if (!subscription) {
                throw new Error('Subscription not found');
            }
            if (((_d = (_c = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _c === void 0 ? void 0 : _c.object) === null || _d === void 0 ? void 0 : _d.cancel_at_period_end) &&
                ((_g = (_f = (_e = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _e === void 0 ? void 0 : _e.object) === null || _f === void 0 ? void 0 : _f.cancellation_details) === null || _g === void 0 ? void 0 : _g.reason) ===
                    'cancellation_requested') {
                const endDate = new Date(((_j = (_h = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _h === void 0 ? void 0 : _h.object) === null || _j === void 0 ? void 0 : _j.cancel_at) * 1000);
                const canceledDate = new Date(((_l = (_k = webhookData === null || webhookData === void 0 ? void 0 : webhookData.data) === null || _k === void 0 ? void 0 : _k.object) === null || _l === void 0 ? void 0 : _l.canceled_at) * 1000);
                subscription.status = subscription_model_1.SubscriptionStatus.Cancelled;
                subscription.canceledDate = canceledDate;
                subscription.endDate = endDate;
                await subscription.save();
            }
            await transaction.commit();
            return;
        }
        catch (e) {
            await transaction.rollback();
            this.errorHelper.throwHttpException(`SubscriptionWebhookController.subscriptionUpdated`, e.message);
        }
    }
};
exports.SubscriptionWebhookController = SubscriptionWebhookController;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => subscription_webhook_service_1.SubscriptionWebhookService)),
    __metadata("design:type", subscription_webhook_service_1.SubscriptionWebhookService)
], SubscriptionWebhookController.prototype, "subscriptionWebhookService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => subscriptions_service_1.SubscriptionsService)),
    __metadata("design:type", subscriptions_service_1.SubscriptionsService)
], SubscriptionWebhookController.prototype, "subscriptionsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => payment_transactions_service_1.PaymentTransactionsService)),
    __metadata("design:type", payment_transactions_service_1.PaymentTransactionsService)
], SubscriptionWebhookController.prototype, "paymentTransactionsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], SubscriptionWebhookController.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => partnerships_service_1.PartnershipsService)),
    __metadata("design:type", partnerships_service_1.PartnershipsService)
], SubscriptionWebhookController.prototype, "partnershipsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => partnership_requests_service_1.PartnershipRequestsService)),
    __metadata("design:type", partnership_requests_service_1.PartnershipRequestsService)
], SubscriptionWebhookController.prototype, "partnershipRequestsService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", stripe_1.StripeHelper)
], SubscriptionWebhookController.prototype, "stripeHelper", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], SubscriptionWebhookController.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", sequelize_typescript_1.Sequelize)
], SubscriptionWebhookController.prototype, "sequelize", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], SubscriptionWebhookController.prototype, "errorHelper", void 0);
__decorate([
    (0, common_1.Post)('/hook'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], SubscriptionWebhookController.prototype, "handleStripeWebhook", null);
exports.SubscriptionWebhookController = SubscriptionWebhookController = __decorate([
    (0, common_1.Controller)('stripe')
], SubscriptionWebhookController);
//# sourceMappingURL=subscription-webhook.controller.js.map