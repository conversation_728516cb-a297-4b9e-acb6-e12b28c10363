"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionWebhookService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const sequelize_typescript_1 = require("sequelize-typescript");
const base_service_1 = require("../common/base.service");
const error_1 = require("../common/helpers/error");
const subscription_webhook_model_1 = require("./models/subscription-webhook.model");
let SubscriptionWebhookService = class SubscriptionWebhookService extends (0, base_service_1.BaseService)(subscription_webhook_model_1.SubscriptionWebhook) {
};
exports.SubscriptionWebhookService = SubscriptionWebhookService;
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", sequelize_typescript_1.Sequelize)
], SubscriptionWebhookService.prototype, "sequelize", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], SubscriptionWebhookService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], SubscriptionWebhookService.prototype, "errorHelper", void 0);
exports.SubscriptionWebhookService = SubscriptionWebhookService = __decorate([
    (0, common_1.Injectable)()
], SubscriptionWebhookService);
//# sourceMappingURL=subscription-webhook.service.js.map