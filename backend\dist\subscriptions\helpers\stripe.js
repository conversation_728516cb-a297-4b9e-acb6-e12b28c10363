"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StripeHelper = void 0;
const common_1 = require("@nestjs/common");
const config_1 = __importDefault(require("../../config/config"));
const stripe_1 = __importDefault(require("stripe"));
const subscription_model_1 = require("../models/subscription.model");
const stripeConfig = config_1.default.STRIPE_SECRET_KEY;
const stripe = new stripe_1.default(stripeConfig, {
    apiVersion: null,
});
let StripeHelper = class StripeHelper {
    createProduct(data) {
        try {
            const { name, connect_id } = data;
            return stripe.products.create({
                name: name,
            }, {
                stripeAccount: connect_id,
            });
        }
        catch (error) {
            console.log('StripeHelper.createProduct', error);
            throw new Error(`StripeHelper.createProduct: ${error}`);
        }
    }
    createCheckout(data) {
        try {
            const { product_id, currency, connect_id, duration, price, receiver_organisation_id, } = data;
            const paymentMethods = currency === 'GBP' ? ['card', 'bacs_debit'] : ['card'];
            const recurringValue = duration === subscription_model_1.SubscriptionDuration.Quarterly
                ? {
                    interval: 'month',
                    interval_count: 3,
                }
                : {
                    interval: duration === subscription_model_1.SubscriptionDuration.Monthly
                        ? 'month'
                        : duration === subscription_model_1.SubscriptionDuration.Annual && 'year',
                };
            return stripe.checkout.sessions.create({
                mode: 'subscription',
                line_items: [
                    {
                        price_data: {
                            unit_amount: price * 100,
                            currency: currency,
                            product: product_id,
                            recurring: recurringValue,
                        },
                        quantity: 1,
                    },
                ],
                subscription_data: {
                    application_fee_percent: 15,
                },
                payment_method_types: paymentMethods,
                success_url: `${config_1.default.FRONTEND_URL}/organisation/${receiver_organisation_id}/connections/active`,
                cancel_url: `${config_1.default.FRONTEND_URL}/organisation/${receiver_organisation_id}/connections/pending`,
            }, {
                stripeAccount: connect_id,
            });
        }
        catch (error) {
            console.log('StripeHelper.createCheckout', error);
            throw new Error(`StripeHelper.createCheckout: ${error}`);
        }
    }
    retrieveCheckout(data) {
        try {
            const { stripe_checkout_id, connect_id } = data;
            return stripe.checkout.sessions.retrieve(stripe_checkout_id, {
                stripeAccount: connect_id,
            });
        }
        catch (error) {
            console.log('StripeHelper.retrieveCheckout', error);
            throw new Error(`StripeHelper.retrieveCheckout: ${error}`);
        }
    }
    updateSubscription(data) {
        try {
            const { stripe_subscription_id, connect_id } = data;
            return stripe.subscriptions.update(stripe_subscription_id, {
                cancel_at_period_end: true,
            }, {
                stripeAccount: connect_id,
            });
        }
        catch (error) {
            console.log('StripeHelper.updateSubscription', error);
            throw new Error(`StripeHelper.updateSubscription: ${error}`);
        }
    }
    retrieveSubscription(data) {
        try {
            const { stripe_subscription_id, connect_id } = data;
            return stripe.subscriptions.retrieve(stripe_subscription_id, {
                stripeAccount: connect_id,
            });
        }
        catch (error) {
            console.log('StripeHelper.retrieveSubscription', error);
            throw new Error(`StripeHelper.retrieveSubscription: ${error}`);
        }
    }
    deleteProduct(product_id) {
        try {
            return stripe.products.del(product_id);
        }
        catch (error) {
            console.log('StripeHelper.createProduct', error);
            throw new Error(`StripeHelper.createProduct: ${error}`);
        }
    }
};
exports.StripeHelper = StripeHelper;
exports.StripeHelper = StripeHelper = __decorate([
    (0, common_1.Injectable)()
], StripeHelper);
//# sourceMappingURL=stripe.js.map