"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Subscription = exports.SubscriptionStatus = exports.SubscriptionDuration = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const short_uuid_1 = __importDefault(require("short-uuid"));
const graphql_1 = require("@nestjs/graphql");
const sequelize_1 = require("sequelize");
const partnership_request_model_1 = require("../../partnership-requests/models/partnership-request.model");
const payment_transaction_model_1 = require("../../payment-transactions/models/payment-transaction.model");
var SubscriptionDuration;
(function (SubscriptionDuration) {
    SubscriptionDuration["Monthly"] = "Monthly";
    SubscriptionDuration["Quarterly"] = "Quarterly";
    SubscriptionDuration["Annual"] = "Annual";
})(SubscriptionDuration || (exports.SubscriptionDuration = SubscriptionDuration = {}));
(0, graphql_1.registerEnumType)(SubscriptionDuration, {
    name: 'SubscriptionDuration',
});
var SubscriptionStatus;
(function (SubscriptionStatus) {
    SubscriptionStatus["Active"] = "Active";
    SubscriptionStatus["Pending"] = "Pending";
    SubscriptionStatus["Cancelled"] = "Cancelled";
    SubscriptionStatus["Expired"] = "Expired";
    SubscriptionStatus["Processing"] = "Processing";
})(SubscriptionStatus || (exports.SubscriptionStatus = SubscriptionStatus = {}));
(0, graphql_1.registerEnumType)(SubscriptionStatus, {
    name: 'SubscriptionStatus',
});
let Subscription = class Subscription extends sequelize_typescript_1.Model {
};
exports.Subscription = Subscription;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], Subscription.prototype, "id", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => partnership_request_model_1.PartnershipRequest),
    (0, sequelize_typescript_1.Column)({ allowNull: false }),
    __metadata("design:type", String)
], Subscription.prototype, "partnershipRequestId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => partnership_request_model_1.PartnershipRequest, 'partnershipRequestId'),
    __metadata("design:type", partnership_request_model_1.PartnershipRequest)
], Subscription.prototype, "partnershipRequest", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Subscription.prototype, "stripeSubscriptionId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Subscription.prototype, "stripeCheckoutId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Subscription.prototype, "checkoutExpiry", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: false }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Subscription.prototype, "stripeConnectId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Subscription.prototype, "stripeCustomerId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: false }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Subscription.prototype, "price", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: false }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Subscription.prototype, "currency", void 0);
__decorate([
    (0, graphql_1.Field)(() => SubscriptionDuration, { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
        defaultValue: SubscriptionDuration.Monthly,
    }),
    __metadata("design:type", String)
], Subscription.prototype, "duration", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Subscription.prototype, "stripeProductId", void 0);
__decorate([
    (0, graphql_1.Field)(() => SubscriptionStatus, { nullable: false }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
        defaultValue: SubscriptionStatus.Pending,
    }),
    __metadata("design:type", String)
], Subscription.prototype, "status", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => payment_transaction_model_1.PaymentTransaction),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Subscription.prototype, "lastTransactionId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => payment_transaction_model_1.PaymentTransaction, 'lastTransactionId'),
    __metadata("design:type", payment_transaction_model_1.PaymentTransaction)
], Subscription.prototype, "lastTransaction", void 0);
__decorate([
    (0, graphql_1.Field)(type => payment_transaction_model_1.PaymentTransaction, { nullable: true }),
    __metadata("design:type", payment_transaction_model_1.PaymentTransaction)
], Subscription.prototype, "lastTransactionData", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Subscription.prototype, "startedOn", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Subscription.prototype, "canceledDate", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Subscription.prototype, "endDate", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Subscription.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Subscription.prototype, "updatedAt", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => partnership_request_model_1.PartnershipRequest, {
        foreignKey: 'subscriptionId',
        onDelete: 'SET NULL',
    }),
    __metadata("design:type", Subscription)
], Subscription.prototype, "subscription", void 0);
exports.Subscription = Subscription = __decorate([
    (0, graphql_1.ObjectType)(),
    sequelize_typescript_1.Table
], Subscription);
//# sourceMappingURL=subscription.model.js.map