"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionsResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const subscription_model_1 = require("./models/subscription.model");
const subscriptions_service_1 = require("./subscriptions.service");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
let SubscriptionsResolver = class SubscriptionsResolver {
    async removeSubscription(user, partnershipRequestId) {
        this.logger.verbose('PartnershipRequestsResolver.removeSubscription (mutation)', {
            user: user.toLogObject(),
            partnershipRequestId,
        });
        await this.subscriptionsService.removeSubscription(partnershipRequestId);
        return true;
    }
};
exports.SubscriptionsResolver = SubscriptionsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => subscriptions_service_1.SubscriptionsService)),
    __metadata("design:type", subscriptions_service_1.SubscriptionsService)
], SubscriptionsResolver.prototype, "subscriptionsService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], SubscriptionsResolver.prototype, "logger", void 0);
__decorate([
    (0, graphql_1.Mutation)(returns => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('partnershipRequestId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], SubscriptionsResolver.prototype, "removeSubscription", null);
exports.SubscriptionsResolver = SubscriptionsResolver = __decorate([
    (0, graphql_1.Resolver)(() => subscription_model_1.Subscription)
], SubscriptionsResolver);
//# sourceMappingURL=subscriptions.resolver.js.map