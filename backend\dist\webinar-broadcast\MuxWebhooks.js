"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isAssetEventHook = exports.isLiveEventHook = exports.MuxWebhookEventType = exports.MuxEvents = void 0;
var MuxEvents;
(function (MuxEvents) {
    MuxEvents["VideoLiveStreamCreated"] = "video.live_stream.created";
    MuxEvents["VideoLiveStreamConnected"] = "video.live_stream.connected";
    MuxEvents["VideoLiveStreamRecording"] = "video.live_stream.recording";
    MuxEvents["VideoLiveStreamActive"] = "video.live_stream.active";
    MuxEvents["VideoLiveStreamDisconnected"] = "video.live_stream.disconnected";
    MuxEvents["VideoLiveStreamIdle"] = "video.live_stream.idle";
    MuxEvents["VideoLiveStreamUpdated"] = "video.live_stream.updated";
    MuxEvents["VideoLiveStreamEnabled"] = "video.live_stream.enabled";
    MuxEvents["VideoLiveStreamDeleted"] = "video.live_stream.deleted";
    MuxEvents["VideoUploadAssetCreated"] = "video.upload.asset_created";
    MuxEvents["VideoUploadCancelled"] = "video.upload.cancelled";
    MuxEvents["VideoUploadCreated"] = "video.upload.created";
    MuxEvents["VideoUploadErrored"] = "video.upload.errored";
    MuxEvents["VideoAssetCreated"] = "video.asset.created";
    MuxEvents["VideoAssetReady"] = "video.asset.ready";
    MuxEvents["VideoAssetErrored"] = "video.asset.errored";
    MuxEvents["VideoAssetUpdated"] = "video.asset.updated";
    MuxEvents["VideoAssetDeleted"] = "video.asset.deleted";
    MuxEvents["VideoAssetLiveStreamCompleted"] = "video.asset.live_stream_completed";
    MuxEvents["VideoAssetStaticRenditionsReady"] = "video.asset.static_renditions.ready";
    MuxEvents["VideoAssetStaticRenditionsPreparing"] = "video.asset.static_renditions.preparing";
    MuxEvents["VideoAssetStaticRenditionsDeleted"] = "video.asset.static_renditions.deleted";
    MuxEvents["VideoAssetStaticRenditionsErrored"] = "video.asset.static_renditions.errored";
    MuxEvents["VideoAssetMasterReady"] = "video.asset.master.ready";
    MuxEvents["VideoAssetMasterPreparing"] = "video.asset.master.preparing";
    MuxEvents["VideoAssetMasterDeleted"] = "video.asset.master.deleted";
    MuxEvents["VideoAssetMasterErrored"] = "video.asset.master.errored";
    MuxEvents["VideoAssetTrackCreated"] = "video.asset.track.created";
    MuxEvents["VideoAssetTrackReady"] = "video.asset.track.ready";
    MuxEvents["VideoAssetTrackErrored"] = "video.asset.track.errored";
    MuxEvents["VideoAssetTrackDeleted"] = "video.asset.track.deleted";
})(MuxEvents || (exports.MuxEvents = MuxEvents = {}));
var MuxWebhookEventType;
(function (MuxWebhookEventType) {
    MuxWebhookEventType["live"] = "live";
    MuxWebhookEventType["asset"] = "asset";
    MuxWebhookEventType["track"] = "track";
})(MuxWebhookEventType || (exports.MuxWebhookEventType = MuxWebhookEventType = {}));
const isLiveEventHook = (e) => e.object.type === MuxWebhookEventType.live;
exports.isLiveEventHook = isLiveEventHook;
const isAssetEventHook = (e) => e.object.type === MuxWebhookEventType.asset;
exports.isAssetEventHook = isAssetEventHook;
//# sourceMappingURL=MuxWebhooks.js.map