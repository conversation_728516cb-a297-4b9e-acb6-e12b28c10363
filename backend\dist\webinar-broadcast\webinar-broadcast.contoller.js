"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebinarsController = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const MuxWebhooks_1 = require("../webinar-broadcast/MuxWebhooks");
const webinar_broadcast_service_1 = require("./webinar-broadcast.service");
let WebinarsController = class WebinarsController {
    getEventStreamId(event) {
        if ((0, MuxWebhooks_1.isAssetEventHook)(event)) {
            return event.data.live_stream_id;
        }
        return event.data.id;
    }
    async broadcastHooks(event) {
        const streamId = this.getEventStreamId(event);
        this.logger.info(`WebinarsController.broadcastHooks ${event.type}`, {
            streamId,
        });
        if ((event.type === MuxWebhooks_1.MuxEvents.VideoLiveStreamRecording ||
            event.type === MuxWebhooks_1.MuxEvents.VideoLiveStreamActive) &&
            (0, MuxWebhooks_1.isLiveEventHook)(event)) {
            await this.webinarBroadcastService.startBroadcastForViewers(streamId);
        }
        if (event.type === MuxWebhooks_1.MuxEvents.VideoAssetLiveStreamCompleted &&
            (0, MuxWebhooks_1.isAssetEventHook)(event)) {
            this.logger.info(`WebinarsController.broadcastHooks ${event.type} event`, { event });
            await this.webinarBroadcastService.stopBroadcastForViewers(streamId);
            await this.webinarBroadcastService.updateWebinarRecordingId(streamId, event.data.id);
        }
        return true;
    }
};
exports.WebinarsController = WebinarsController;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinar_broadcast_service_1.WebinarBroadcastService)),
    __metadata("design:type", webinar_broadcast_service_1.WebinarBroadcastService)
], WebinarsController.prototype, "webinarBroadcastService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], WebinarsController.prototype, "logger", void 0);
__decorate([
    (0, common_1.Post)('/hook'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebinarsController.prototype, "broadcastHooks", null);
exports.WebinarsController = WebinarsController = __decorate([
    (0, common_1.Controller)('webinar-broadcast')
], WebinarsController);
//# sourceMappingURL=webinar-broadcast.contoller.js.map