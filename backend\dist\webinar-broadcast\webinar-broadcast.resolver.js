"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebinarBroadcastResolver = exports.PreRecordedResponse = exports.DirectUploadResponse = exports.ConferenceAuthTokenResponse = exports.CreateStreamBroadcastResponse = void 0;
const error_1 = require("./../common/helpers/error");
const webinars_service_1 = require("./../webinars/webinars.service");
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const webinar_broadcast_service_1 = require("./webinar-broadcast.service");
const mux_node_1 = __importDefault(require("@mux/mux-node"));
let CreateStreamBroadcastResponse = class CreateStreamBroadcastResponse {
};
exports.CreateStreamBroadcastResponse = CreateStreamBroadcastResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CreateStreamBroadcastResponse.prototype, "streamKey", void 0);
exports.CreateStreamBroadcastResponse = CreateStreamBroadcastResponse = __decorate([
    (0, graphql_1.ObjectType)()
], CreateStreamBroadcastResponse);
let ConferenceAuthTokenResponse = class ConferenceAuthTokenResponse {
};
exports.ConferenceAuthTokenResponse = ConferenceAuthTokenResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], ConferenceAuthTokenResponse.prototype, "authToken", void 0);
exports.ConferenceAuthTokenResponse = ConferenceAuthTokenResponse = __decorate([
    (0, graphql_1.ObjectType)()
], ConferenceAuthTokenResponse);
let DirectUploadResponse = class DirectUploadResponse {
};
exports.DirectUploadResponse = DirectUploadResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], DirectUploadResponse.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], DirectUploadResponse.prototype, "url", void 0);
exports.DirectUploadResponse = DirectUploadResponse = __decorate([
    (0, graphql_1.ObjectType)()
], DirectUploadResponse);
let PreRecordedResponse = class PreRecordedResponse {
};
exports.PreRecordedResponse = PreRecordedResponse;
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], PreRecordedResponse.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], PreRecordedResponse.prototype, "assetId", void 0);
exports.PreRecordedResponse = PreRecordedResponse = __decorate([
    (0, graphql_1.ObjectType)()
], PreRecordedResponse);
let WebinarBroadcastResolver = class WebinarBroadcastResolver {
    async getConferenceAuthToken(user) {
        this.logger.verbose('WebinarBroadcastResolver.getConferenceAuthToken (query)', {
            user: user.toLogObject(),
        });
        const authToken = await this.broadcastService.getConferenceAuthToken();
        return {
            authToken,
        };
    }
    async createBroadcastStream(user) {
        this.logger.verbose('WebinarBroadcastResolver.createBroadcastStream (mutation)', {
            user: user.toLogObject(),
        });
        await this.broadcastService.createBroadcastStream();
        return true;
    }
    async startBroadcast(user, webinarId, webinarConferenceId) {
        this.logger.verbose('WebinarBroadcastResolver.startBroadcast (mutation)', {
            user: user.toLogObject(),
        });
        await this.broadcastService.startBroadcast(webinarId, webinarConferenceId);
        return true;
    }
    async stopBroadcast(user, webinarId, webinarConferenceId) {
        this.logger.verbose('WebinarBroadcastResolver.stopBroadcast (mutation)', {
            user: user.toLogObject(),
        });
        await this.broadcastService.stopBroadcast(webinarId, webinarConferenceId);
        return true;
    }
    async getDirectUpload() {
        this.logger.verbose('WebinarBroadcastResolver.getDirectUpload (query)');
        const directUpload = await this.broadcastService.directUpload();
        return directUpload;
    }
    async getPlaybackId(UploadId) {
        this.logger.verbose('WebinarBroadcastResolver.getPlaybackId (query)');
        const { Video } = new mux_node_1.default(process.env.MUX_API_KEY, process.env.MUX_SECRET);
        try {
            const delay = (delayInMs) => {
                return new Promise(resolve => {
                    setTimeout(resolve, delayInMs);
                });
            };
            await delay(1000);
            const updatedUpload = await Video.Uploads.get(UploadId);
            const assetInfo = await Video.Assets.get(updatedUpload['asset_id']);
            if (assetInfo.errors) {
                throw new Error(assetInfo.errors.messages[0]);
            }
            const playbackId = await (await Video.Assets.get(updatedUpload.asset_id))
                .playback_ids[0];
            return { id: playbackId.id, assetId: updatedUpload.asset_id };
        }
        catch (error) {
            this.errorHelper.throwHttpException(`WebinarBroadcastResolver.getPreRecordedVideoUrl`, error.message);
        }
    }
};
exports.WebinarBroadcastResolver = WebinarBroadcastResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinar_broadcast_service_1.WebinarBroadcastService)),
    __metadata("design:type", webinar_broadcast_service_1.WebinarBroadcastService)
], WebinarBroadcastResolver.prototype, "broadcastService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinars_service_1.WebinarsService)),
    __metadata("design:type", webinars_service_1.WebinarsService)
], WebinarBroadcastResolver.prototype, "webinarservice", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], WebinarBroadcastResolver.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], WebinarBroadcastResolver.prototype, "errorHelper", void 0);
__decorate([
    (0, graphql_1.Query)(() => ConferenceAuthTokenResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebinarBroadcastResolver.prototype, "getConferenceAuthToken", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebinarBroadcastResolver.prototype, "createBroadcastStream", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)({ name: 'webinarId' })),
    __param(2, (0, graphql_1.Args)({ name: 'webinarConferenceId' })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], WebinarBroadcastResolver.prototype, "startBroadcast", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)({ name: 'webinarId' })),
    __param(2, (0, graphql_1.Args)({ name: 'webinarConferenceId' })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], WebinarBroadcastResolver.prototype, "stopBroadcast", null);
__decorate([
    (0, graphql_1.Query)(() => DirectUploadResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], WebinarBroadcastResolver.prototype, "getDirectUpload", null);
__decorate([
    (0, graphql_1.Query)(() => PreRecordedResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)({ name: 'UploadId' })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], WebinarBroadcastResolver.prototype, "getPlaybackId", null);
exports.WebinarBroadcastResolver = WebinarBroadcastResolver = __decorate([
    (0, graphql_1.Resolver)()
], WebinarBroadcastResolver);
//# sourceMappingURL=webinar-broadcast.resolver.js.map