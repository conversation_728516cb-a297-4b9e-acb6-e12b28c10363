"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebinarBroadcastService = void 0;
const common_1 = require("@nestjs/common");
const mux_node_1 = __importDefault(require("@mux/mux-node"));
const axios_1 = __importDefault(require("axios"));
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const short_uuid_1 = __importStar(require("short-uuid"));
const webinars_service_1 = require("../webinars/webinars.service");
const chat_service_1 = require("../chat/chat.service");
const ChannelTypes_1 = require("../common/enums/ChannelTypes");
const error_1 = require("../common/helpers/error");
const config_1 = __importDefault(require("../config/config"));
let WebinarBroadcastService = class WebinarBroadcastService {
    onModuleInit() {
        this.muxClient = new mux_node_1.default(process.env.MUX_API_KEY, process.env.MUX_SECRET);
        this.voxeetAuthToken = Buffer.from(encodeURI(process.env.DOLBY_VOXEET_KEY) +
            ':' +
            encodeURI(process.env.DOLBY_VOXEET_SECRET)).toString('base64');
    }
    async getConferenceAuthToken() {
        this.logger.info('WebinarBroadcastService.getConferenceAuthToken');
        const tokenURL = 'https://session.voxeet.com/v1/oauth2/token';
        const response = await axios_1.default.post(tokenURL, {
            grant_type: 'client_credentials',
        }, {
            headers: this.getVoxeetHeaders(),
        });
        return response.data.access_token;
    }
    async createBroadcastStream() {
        var _a;
        const playbackPolicy = 'public';
        const reducedLatency = true;
        const liveStream = await this.muxClient.Video.LiveStreams.create({
            playback_policy: playbackPolicy,
            reduced_latency: reducedLatency,
            test: config_1.default.SERVICE !== 'api' && config_1.default.SERVICE !== 'api-beta',
        });
        return {
            liveStreamId: liveStream.id,
            liveStreamPrivateKey: liveStream.stream_key,
            liveStreamPlaybackId: ((_a = liveStream.playback_ids.find(playbackIds => playbackIds.policy === playbackPolicy)) === null || _a === void 0 ? void 0 : _a.id) || '',
        };
    }
    async startBroadcast(webinarId, webinarConferenceId) {
        const webinar = await this.webinarsService.findById(webinarId);
        if (!webinar) {
            this.errorHelper.throwHttpException(`WebinarBroadcastService.startBroadcast`, 'Webinar does not exist');
        }
        const channels = await this.chatService.client.queryChannels({
            type: ChannelTypes_1.ChannelTypes.webinar,
            id: webinar.chatChannelId,
        });
        const channel = channels[0];
        if (channel) {
            await channel.update({
                streamStartRequestAt: new Date().toISOString(),
            });
            await axios_1.default.post(`https://session.voxeet.com/v1/api/conferences/mix/${webinarConferenceId}/live/start`, {
                uri: `rtmp://global-live.mux.com:5222/app/${webinar.liveStreamPrivateKey}`,
            }, {
                headers: this.getVoxeetHeaders(),
            });
        }
        else {
            this.errorHelper.throwHttpException(`WebinarBroadcastService.startBroadcast`, 'Stream Channel for webinar does not exist');
        }
    }
    async startBroadcastForViewers(liveStreamId) {
        const webinar = await this.webinarsService.findOne({ liveStreamId });
        if (!webinar) {
            this.logger.info(`WebinarBroadcastService.startBroadcastForViewers no webinar found for liveStreamId:${liveStreamId}`);
            return;
        }
        await this.webinarsService.updateById(webinar.id, {
            liveStreamStartedAt: new Date().toISOString(),
        });
        const channel = (await this.chatService.client.queryChannels({
            id: webinar.chatChannelId,
            type: ChannelTypes_1.ChannelTypes.webinar,
        })).shift();
        if (channel) {
            await channel.update({
                streamStartRequestAt: channel.data.streamStartRequestAt,
                streamStartedAt: new Date().toISOString(),
            });
        }
    }
    async stopBroadcast(webinarId, webinarConferenceId) {
        const webinar = await this.webinarsService.findById(webinarId);
        if (!webinar) {
            this.errorHelper.throwHttpException(`WebinarBroadcastService.stopBroadcast`, 'Webinar does not exist');
        }
        const channel = (await this.chatService.client.queryChannels({
            id: webinar.chatChannelId,
            type: ChannelTypes_1.ChannelTypes.webinar,
        })).shift();
        if (channel) {
            await channel.update({
                streamStartedAt: channel.data.streamStartedAt,
                streamStartRequestAt: channel.data.streamStartRequestAt,
                streamStopRequestAt: new Date().toISOString(),
            });
        }
        else {
            this.errorHelper.throwHttpException(`WebinarBroadcastService.stopBroadcast`, 'Stream Channel for Webinar does not exist');
        }
        await axios_1.default.post(`https://session.voxeet.com/v1/api/conferences/mix/${webinarConferenceId}/live/stop`, {}, {
            headers: this.getVoxeetHeaders(),
        });
    }
    async stopBroadcastForViewers(liveStreamId) {
        const webinar = await this.webinarsService.findOne({ liveStreamId });
        if (!webinar) {
            this.logger.info(`WebinarBroadcastService.stopBroadcastForViewers no webinar found for liveStreamId:${liveStreamId}`);
            return;
        }
        await this.webinarsService.updateById(webinar.id, {
            liveStreamStoppedAt: new Date().toISOString(),
        });
        const channel = (await this.chatService.client.queryChannels({
            id: webinar.chatChannelId,
            type: ChannelTypes_1.ChannelTypes.webinar,
        })).shift();
        if (channel) {
            await channel.update({
                streamStartedAt: channel.data.streamStartedAt,
                streamStartRequestAt: channel.data.streamStartRequestAt,
                streamStopRequestAt: channel.data.streamStopRequestAt,
                streamStoppedAt: new Date().toISOString(),
            });
        }
    }
    async createWebinarChannel(profileId) {
        const channelId = short_uuid_1.default.generate();
        const channel = await this.chatService.client.channel('webinar', channelId, {
            created_by_id: profileId,
            members: [profileId],
            isWebinar: true,
        });
        await channel.create();
        await channel.addModerators([profileId]);
        return channelId;
    }
    async addToWebinarChannel(webinarId, profileIds, isModerator = false) {
        const webinar = await this.webinarsService.findById(webinarId);
        const channels = await this.chatService.client.queryChannels({
            type: ChannelTypes_1.ChannelTypes.webinar,
            id: webinar.chatChannelId,
        });
        const channel = channels[0];
        if (channel) {
            if (isModerator) {
                await channel.addModerators(profileIds);
            }
            else {
                await channel.addMembers(profileIds);
            }
        }
    }
    async removeFromWebinarChannel(webinarId, profileIds) {
        const webinar = await this.webinarsService.findById(webinarId);
        const channels = await this.chatService.client.queryChannels({
            type: ChannelTypes_1.ChannelTypes.webinar,
            id: webinar.chatChannelId,
        });
        const channel = channels[0];
        if (channel) {
            await channel.removeMembers(profileIds);
        }
        return true;
    }
    async updateWebinarRecordingId(liveStreamId, liveStreamRecordingAssetId) {
        const playbackAsset = await this.muxClient.Video.Assets.createPlaybackId(liveStreamRecordingAssetId, { policy: 'public' });
        await this.webinarsService.update({
            where: [{ liveStreamId }],
            update: { liveStreamRecordingId: playbackAsset.id },
        });
    }
    getVoxeetHeaders() {
        return {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
            Authorization: 'Basic ' + this.voxeetAuthToken,
        };
    }
    async directUpload() {
        const { Video } = new mux_node_1.default(process.env.MUX_API_KEY, process.env.MUX_SECRET);
        const id = (0, short_uuid_1.uuid)();
        const upload = await Video.Uploads.create({
            cors_origin: 'https://myhablo.com/',
            new_asset_settings: {
                playback_policy: 'public',
            },
        });
        return { id: upload.id, url: upload.url };
    }
};
exports.WebinarBroadcastService = WebinarBroadcastService;
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], WebinarBroadcastService.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinars_service_1.WebinarsService)),
    __metadata("design:type", webinars_service_1.WebinarsService)
], WebinarBroadcastService.prototype, "webinarsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => chat_service_1.ChatService)),
    __metadata("design:type", chat_service_1.ChatService)
], WebinarBroadcastService.prototype, "chatService", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], WebinarBroadcastService.prototype, "errorHelper", void 0);
exports.WebinarBroadcastService = WebinarBroadcastService = __decorate([
    (0, common_1.Injectable)()
], WebinarBroadcastService);
//# sourceMappingURL=webinar-broadcast.service.js.map