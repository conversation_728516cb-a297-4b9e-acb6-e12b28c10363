"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateWebinarParticipantsDto = void 0;
const class_validator_1 = require("class-validator");
const webinar_participants_args_1 = require("../args/webinar-participants.args");
class CreateWebinarParticipantsDto {
}
exports.CreateWebinarParticipantsDto = CreateWebinarParticipantsDto;
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", String)
], CreateWebinarParticipantsDto.prototype, "webinarId", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", String)
], CreateWebinarParticipantsDto.prototype, "inviterProfileId", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", String)
], CreateWebinarParticipantsDto.prototype, "organisationId", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", Array)
], CreateWebinarParticipantsDto.prototype, "profileIds", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    __metadata("design:type", String)
], CreateWebinarParticipantsDto.prototype, "status", void 0);
//# sourceMappingURL=create-webinar-participants.dto.js.map