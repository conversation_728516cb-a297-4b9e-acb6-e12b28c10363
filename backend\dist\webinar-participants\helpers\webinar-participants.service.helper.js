"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebinarParticipantsServiceHelper = void 0;
const sequelize_1 = require("sequelize");
const common_1 = require("@nestjs/common");
let WebinarParticipantsServiceHelper = class WebinarParticipantsServiceHelper {
    static getQueryParams(webinarParticipantArgs) {
        var _a, _b, _c, _d;
        const queryParams = {};
        if ((_a = webinarParticipantArgs === null || webinarParticipantArgs === void 0 ? void 0 : webinarParticipantArgs.filter) === null || _a === void 0 ? void 0 : _a.organisationId) {
            queryParams['organisationId'] =
                webinarParticipantArgs.filter.organisationId;
        }
        if ((_b = webinarParticipantArgs === null || webinarParticipantArgs === void 0 ? void 0 : webinarParticipantArgs.filter) === null || _b === void 0 ? void 0 : _b.profileId) {
            queryParams['profileId'] = webinarParticipantArgs.filter.profileId;
        }
        if (((_d = (_c = webinarParticipantArgs === null || webinarParticipantArgs === void 0 ? void 0 : webinarParticipantArgs.filter) === null || _c === void 0 ? void 0 : _c.status) === null || _d === void 0 ? void 0 : _d.length) > 0) {
            queryParams['status'] = {
                [sequelize_1.Op.or]: webinarParticipantArgs.filter.status,
            };
        }
        return queryParams;
    }
};
exports.WebinarParticipantsServiceHelper = WebinarParticipantsServiceHelper;
exports.WebinarParticipantsServiceHelper = WebinarParticipantsServiceHelper = __decorate([
    (0, common_1.Injectable)()
], WebinarParticipantsServiceHelper);
//# sourceMappingURL=webinar-participants.service.helper.js.map