"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebinarParticipant = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const graphql_1 = require("@nestjs/graphql");
const sequelize_1 = require("sequelize");
const short_uuid_1 = __importDefault(require("short-uuid"));
const profile_model_1 = require("../../profiles/models/profile.model");
const organisation_model_1 = require("../../organisations/models/organisation.model");
const webinar_participants_args_1 = require("../args/webinar-participants.args");
const webinar_model_1 = require("../../webinars/models/webinar.model");
let WebinarParticipant = class WebinarParticipant extends sequelize_typescript_1.Model {
};
exports.WebinarParticipant = WebinarParticipant;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], WebinarParticipant.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(() => webinar_participants_args_1.WebinarParticipantStatus, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], WebinarParticipant.prototype, "status", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => webinar_model_1.Webinar),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], WebinarParticipant.prototype, "webinarId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => webinar_model_1.Webinar, {
        foreignKey: 'webinarId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", webinar_model_1.Webinar)
], WebinarParticipant.prototype, "webinar", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], WebinarParticipant.prototype, "profileId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, {
        foreignKey: 'profileId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", profile_model_1.Profile)
], WebinarParticipant.prototype, "profile", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => profile_model_1.Profile),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], WebinarParticipant.prototype, "inviterProfileId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => profile_model_1.Profile, 'inviterProfileId'),
    __metadata("design:type", profile_model_1.Profile)
], WebinarParticipant.prototype, "inviterProfile", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], WebinarParticipant.prototype, "organisationId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, {
        foreignKey: 'organisationId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", organisation_model_1.Organisation)
], WebinarParticipant.prototype, "organisation", void 0);
__decorate([
    (0, graphql_1.Field)(() => webinar_participants_args_1.WebinarInvitationStatus, { nullable: true }),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.STRING,
    }),
    __metadata("design:type", String)
], WebinarParticipant.prototype, "invitationStatus", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], WebinarParticipant.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], WebinarParticipant.prototype, "updatedAt", void 0);
exports.WebinarParticipant = WebinarParticipant = __decorate([
    sequelize_typescript_1.Table,
    (0, graphql_1.ObjectType)()
], WebinarParticipant);
//# sourceMappingURL=webinar-participant.model.js.map