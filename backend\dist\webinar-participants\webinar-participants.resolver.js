"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebinarParticipantsResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const webinar_participant_model_1 = require("./models/webinar-participant.model");
const webinars_service_1 = require("../webinars/webinars.service");
const webinar_model_1 = require("../webinars/models/webinar.model");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const webinar_participants_service_1 = require("./webinar-participants.service");
const memberships_service_rights_1 = require("../memberships/helpers/memberships.service.rights");
const error_1 = require("../common/helpers/error");
const organisation_model_1 = require("../organisations/models/organisation.model");
const organisations_service_1 = require("../organisations/organisations.service");
const profile_model_1 = require("../profiles/models/profile.model");
const profiles_service_1 = require("../profiles/profiles.service");
const webinar_participants_args_1 = require("./args/webinar-participants.args");
const create_webinar_participants_input_1 = require("./dto/create-webinar-participants.input");
const create_webinar_participant_input_1 = require("./dto/create-webinar-participant.input");
const sequelize_1 = require("sequelize");
const activities_service_1 = require("../activities/activities.service");
const organisation_loyalty_points_service_1 = require("../organisation-loyalty-points/organisation-loyalty-points.service");
const activities_args_1 = require("../activities/args/activities.args");
const memberships_service_1 = require("../memberships/memberships.service");
const membership_model_1 = require("../memberships/models/membership.model");
let WebinarParticipantsResolver = class WebinarParticipantsResolver {
    async webinarParticipant(user, id) {
        this.logger.verbose('WebinarParticipantsResolver.webinarParticipant (query)', {
            user: user.toLogObject(),
            id,
        });
        const webinarParticipant = await this.webinarParticipantsService.findById(id);
        if (!webinarParticipant) {
            throw new Error(`WebinarParticipant not found`);
        }
        return webinarParticipant;
    }
    async inviteToWebinar(user, webinarParticipantsData) {
        this.logger.verbose('WebinarParticipantsResolver.inviteToWebinar (mutation)', {
            user: user.toLogObject(),
            webinarId: webinarParticipantsData.webinarId,
            profileIds: webinarParticipantsData.profileIds,
        });
        const webinar = await this.webinarsService.findById(webinarParticipantsData.webinarId);
        if (!webinar) {
            this.errorHelper.throwHttpException(`WebinarParticipantsResolver.inviteToWebinar`, 'Webinar not found');
        }
        const webinarParticipantStatus = await this.webinarParticipantsService.verifyInvitePermission(user, webinar);
        if (webinarParticipantStatus === webinar_participants_args_1.WebinarParticipantStatus.InvitedByParticipant) {
            let countInvites = await this.webinarParticipantsService.count({
                webinarId: webinarParticipantsData.webinarId,
                inviterProfileId: user.profileId,
                createdAt: new Date(),
            });
            if (countInvites >= 50) {
                this.errorHelper.throwHttpException(`WebinarParticipantsResolver.inviteToWebinar`, 'Max participant invite limit reached.');
            }
        }
        const participants = await this.webinarParticipantsService.createWebinarParticipants({
            profileIds: webinarParticipantsData.profileIds,
            inviterProfileId: user.profileId,
            webinarId: webinar.id,
            organisationId: webinar.organisationId,
            status: webinarParticipantStatus,
        }, user, {});
        const invitedByMembership = await this.membershipsService.findOne({
            profileId: user.profileId,
            organisationId: webinar.organisationId,
        });
        if (invitedByMembership.permissions.some(permission => [
            membership_model_1.MembershipPermission.Owner,
            membership_model_1.MembershipPermission.Admin,
            membership_model_1.MembershipPermission.HiddenAdmin,
            membership_model_1.MembershipPermission.Manager,
            membership_model_1.MembershipPermission.Editor,
        ].includes(permission))) {
            const inviterProfile = await this.profilesService.findById(user.profileId);
            await this.organisationLoyaltyPointsService.addPoints({
                type: activities_args_1.ActivityType.UserInvitedToWebinar,
                organisationId: webinar.organisationId,
                placeholders: {
                    invitedProfileId: webinarParticipantsData.profileIds[0],
                    createdById: inviterProfile.id,
                    createdByName: inviterProfile.name,
                    webinarId: webinar.id,
                    webinarName: webinar.name,
                },
            });
        }
        const orgActivityData = {
            invitedById: user.profileId,
            invitedProfileId: webinarParticipantsData.profileIds[0],
            organisationId: webinar.organisationId,
            webinarId: webinar.id,
            type: 'UserInvitedToWebinar',
            data: {
                eventId: webinar.id,
                invitedProfileId: webinarParticipantsData.profileIds[0],
            },
            createdById: user.profileId,
        };
        await this.activitiesService.createOrganisationActivity(orgActivityData);
        return participants;
    }
    async createWebinarParticipant(user, webinarParticipantData) {
        this.logger.verbose('WebinarParticipantsResolver.createParticipant (mutation)', {
            user: user.toLogObject(),
            webinarId: webinarParticipantData.webinarId,
            profileId: webinarParticipantData.profileId,
            role: webinarParticipantData.role,
        });
        const webinar = await this.webinarsService.findById(webinarParticipantData.webinarId);
        await this.membershipsServiceRights.checkRights(webinar.organisationId, {
            currentProfileId: user.profileId,
            rights: [memberships_service_rights_1.MembershipRight.UpdateWebinar],
        });
        if (!webinar) {
            this.errorHelper.throwHttpException(`WebinarParticipantsResolver.inviteToWebinar`, 'Webinar not found');
        }
        return await this.webinarParticipantsService.createWebinarParticipant({
            profileId: webinarParticipantData.profileId,
            inviterProfileId: user.profileId,
            webinarId: webinar.id,
            organisationId: webinar.organisationId,
            status: webinarParticipantData.role,
        });
    }
    async removeWebinarParticipant(user, id) {
        this.logger.verbose('WebinarParticipantsResolver.removeWebinarParticipant (mutation)', {
            user: user.toLogObject(),
            id,
        });
        const webinarParticipant = await this.webinarParticipantsService.findById(id, {
            includeParams: [
                {
                    model: webinar_model_1.Webinar,
                    as: 'webinar',
                },
            ],
        });
        if (!webinarParticipant) {
            this.errorHelper.throwHttpException(`WebinarParticipantsResolver.removeWebinarParticipant`, 'Webinar Participant not found');
        }
        await this.membershipsServiceRights.checkRights(webinarParticipant.webinar.organisationId, {
            currentProfileId: user.profileId,
            rights: [memberships_service_rights_1.MembershipRight.RemoveWebinarParticipant],
        });
        await this.webinarParticipantsService.remove({ where: { id } });
        return true;
    }
    updateWebinarParticipantStatus(user, id, status) {
        this.logger.verbose('WebinarParticipantsResolver.updateWebinarParticipantStatus (mutation)', {
            user: user.toLogObject(),
            id,
        });
        return this.webinarParticipantsService.updateWebinarParticipantStatus(user, id, status);
    }
    async webinar(webinarParticipant) {
        if (webinarParticipant.webinar)
            return webinarParticipant.webinar;
        this.logger.verbose('WebinarParticipantsResolver.webinar (field resolver)', {
            webinarParticipantId: webinarParticipant.id,
        });
        return this.webinarsService.findById(webinarParticipant.webinarId, {
            useCache: true,
        });
    }
    async organisation(webinarParticipant) {
        if (webinarParticipant.organisation)
            return webinarParticipant.organisation;
        if (!webinarParticipant.organisationId)
            return null;
        this.logger.verbose('WebinarParticipantsResolver.organisation (field resolver)', {
            webinarParticipantId: webinarParticipant.id,
        });
        return this.organisationsService.findById(webinarParticipant.organisationId, {
            useCache: true,
        });
    }
    async profile(webinarParticipant) {
        if (webinarParticipant.profile)
            return webinarParticipant.profile;
        this.logger.verbose('WebinarParticipantsResolver.profile (field resolver)', {
            webinarParticipantId: webinarParticipant.id,
        });
        return this.profilesService.findById(webinarParticipant.profileId);
    }
    async inviterProfile(webinarParticipant) {
        if (webinarParticipant.inviterProfile)
            return webinarParticipant.inviterProfile;
        if (!webinarParticipant.inviterProfile)
            return null;
        this.logger.verbose('WebinarParticipantsResolver.inviterProfile (field resolver)', {
            webinarParticipantId: webinarParticipant.id,
        });
        return this.profilesService.findById(webinarParticipant.inviterProfileId);
    }
    async webinarShareCount(user, webinarId) {
        this.logger.verbose('WebinarParticipantsResolver.webinarShareCount (mutation)', {
            user: user.toLogObject(),
            webinarId,
        });
        return await this.webinarParticipantsService.count({
            [sequelize_1.Op.and]: [
                sequelize_1.Sequelize.where(sequelize_1.Sequelize.fn('DATE', sequelize_1.Sequelize.col('createdAt')), sequelize_1.Sequelize.literal('CURRENT_DATE')),
                {
                    webinarId,
                    inviterProfileId: user.profileId,
                },
            ],
        });
    }
};
exports.WebinarParticipantsResolver = WebinarParticipantsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinar_participants_service_1.WebinarParticipantsService)),
    __metadata("design:type", webinar_participants_service_1.WebinarParticipantsService)
], WebinarParticipantsResolver.prototype, "webinarParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinars_service_1.WebinarsService)),
    __metadata("design:type", webinars_service_1.WebinarsService)
], WebinarParticipantsResolver.prototype, "webinarsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_rights_1.MembershipsServiceRights)),
    __metadata("design:type", memberships_service_rights_1.MembershipsServiceRights)
], WebinarParticipantsResolver.prototype, "membershipsServiceRights", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], WebinarParticipantsResolver.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService)),
    __metadata("design:type", profiles_service_1.ProfilesService)
], WebinarParticipantsResolver.prototype, "profilesService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], WebinarParticipantsResolver.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], WebinarParticipantsResolver.prototype, "errorHelper", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => activities_service_1.ActivitiesService)),
    __metadata("design:type", activities_service_1.ActivitiesService)
], WebinarParticipantsResolver.prototype, "activitiesService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisation_loyalty_points_service_1.OrganisationLoyaltyPointsService)),
    __metadata("design:type", organisation_loyalty_points_service_1.OrganisationLoyaltyPointsService)
], WebinarParticipantsResolver.prototype, "organisationLoyaltyPointsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], WebinarParticipantsResolver.prototype, "membershipsService", void 0);
__decorate([
    (0, graphql_1.Query)(() => webinar_participant_model_1.WebinarParticipant),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id', { nullable: false })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], WebinarParticipantsResolver.prototype, "webinarParticipant", null);
__decorate([
    (0, graphql_1.Mutation)(() => [webinar_participant_model_1.WebinarParticipant]),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('webinarParticipantsData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_webinar_participants_input_1.CreateWebinarParticipantsInput]),
    __metadata("design:returntype", Promise)
], WebinarParticipantsResolver.prototype, "inviteToWebinar", null);
__decorate([
    (0, graphql_1.Mutation)(() => webinar_participant_model_1.WebinarParticipant),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('webinarParticipantData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_webinar_participant_input_1.CreateWebinarParticipantInput]),
    __metadata("design:returntype", Promise)
], WebinarParticipantsResolver.prototype, "createWebinarParticipant", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], WebinarParticipantsResolver.prototype, "removeWebinarParticipant", null);
__decorate([
    (0, graphql_1.Mutation)(() => webinar_participant_model_1.WebinarParticipant),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id', { nullable: false })),
    __param(2, (0, graphql_1.Args)('status', { type: () => webinar_participants_args_1.WebinarParticipantStatus, nullable: false })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], WebinarParticipantsResolver.prototype, "updateWebinarParticipantStatus", null);
__decorate([
    (0, graphql_1.ResolveField)('webinar', () => webinar_model_1.Webinar),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [webinar_participant_model_1.WebinarParticipant]),
    __metadata("design:returntype", Promise)
], WebinarParticipantsResolver.prototype, "webinar", null);
__decorate([
    (0, graphql_1.ResolveField)('organisation', () => organisation_model_1.Organisation, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [webinar_participant_model_1.WebinarParticipant]),
    __metadata("design:returntype", Promise)
], WebinarParticipantsResolver.prototype, "organisation", null);
__decorate([
    (0, graphql_1.ResolveField)('profile', () => profile_model_1.Profile),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [webinar_participant_model_1.WebinarParticipant]),
    __metadata("design:returntype", Promise)
], WebinarParticipantsResolver.prototype, "profile", null);
__decorate([
    (0, graphql_1.ResolveField)('inviterProfile', () => profile_model_1.Profile, { nullable: true }),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [webinar_participant_model_1.WebinarParticipant]),
    __metadata("design:returntype", Promise)
], WebinarParticipantsResolver.prototype, "inviterProfile", null);
__decorate([
    (0, graphql_1.Query)(() => Number),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('webinarId', { nullable: false })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], WebinarParticipantsResolver.prototype, "webinarShareCount", null);
exports.WebinarParticipantsResolver = WebinarParticipantsResolver = __decorate([
    (0, graphql_1.Resolver)(() => webinar_participant_model_1.WebinarParticipant)
], WebinarParticipantsResolver);
//# sourceMappingURL=webinar-participants.resolver.js.map