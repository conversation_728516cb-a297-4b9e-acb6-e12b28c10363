"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebinarsArgs = exports.WebinarsFilter = exports.WebinarType = void 0;
const graphql_1 = require("@nestjs/graphql");
const pagination_args_1 = require("../../common/args/pagination.args");
const webinar_participants_args_1 = require("../../webinar-participants/args/webinar-participants.args");
var WebinarType;
(function (WebinarType) {
    WebinarType["PreRecorded"] = "PreRecorded";
    WebinarType["LiveStream"] = "LiveStream";
})(WebinarType || (exports.WebinarType = WebinarType = {}));
(0, graphql_1.registerEnumType)(WebinarType, { name: 'WebinarType' });
let WebinarsFilter = class WebinarsFilter {
};
exports.WebinarsFilter = WebinarsFilter;
__decorate([
    (0, graphql_1.Field)(() => [webinar_participants_args_1.WebinarParticipantStatus], {
        nullable: true,
        defaultValue: [],
    }),
    __metadata("design:type", Array)
], WebinarsFilter.prototype, "webinarParticipantStatus", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], WebinarsFilter.prototype, "organisationId", void 0);
__decorate([
    (0, graphql_1.Field)(() => [WebinarType], { nullable: true, defaultValue: [] }),
    __metadata("design:type", Array)
], WebinarsFilter.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true, defaultValue: null }),
    __metadata("design:type", Boolean)
], WebinarsFilter.prototype, "isPublic", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], WebinarsFilter.prototype, "searchText", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { nullable: true, defaultValue: null }),
    __metadata("design:type", Boolean)
], WebinarsFilter.prototype, "isEnded", void 0);
exports.WebinarsFilter = WebinarsFilter = __decorate([
    (0, graphql_1.InputType)()
], WebinarsFilter);
let WebinarsArgs = class WebinarsArgs extends pagination_args_1.PaginationArgs {
};
exports.WebinarsArgs = WebinarsArgs;
__decorate([
    (0, graphql_1.Field)(() => WebinarsFilter, { nullable: true }),
    __metadata("design:type", WebinarsFilter)
], WebinarsArgs.prototype, "filter", void 0);
exports.WebinarsArgs = WebinarsArgs = __decorate([
    (0, graphql_1.ArgsType)()
], WebinarsArgs);
//# sourceMappingURL=webinars.args.js.map