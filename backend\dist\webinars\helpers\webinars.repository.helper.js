"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebinarsRepositoryHelper = void 0;
const sequelize_1 = require("sequelize");
const common_1 = require("@nestjs/common");
const underscore_1 = require("../../common/helpers/underscore");
const webinar_participants_service_1 = require("../../webinar-participants/webinar-participants.service");
const membership_model_1 = require("../../memberships/models/membership.model");
const memberships_service_1 = require("../../memberships/memberships.service");
let WebinarsRepositoryHelper = class WebinarsRepositoryHelper {
    async getWebinarParticipantWebinarIds(profileId, filters) {
        const queryParams = { profileId };
        if (filters === null || filters === void 0 ? void 0 : filters.status) {
            queryParams.status = {
                [sequelize_1.Op.in]: filters.status,
            };
        }
        const webinarParticipants = await this.webinarParticipantsService.findAll(queryParams, {
            attributes: ['webinarId'],
        });
        return underscore_1.Underscore.map(webinarParticipants, 'webinarId');
    }
    async getHostOrganisationIds(profileId) {
        const memberships = await this.membershipsService.findAll({
            profileId,
            status: membership_model_1.MembershipStatus.Active,
            permissions: {
                [sequelize_1.Op.overlap]: [
                    membership_model_1.MembershipPermission.Owner,
                    membership_model_1.MembershipPermission.Admin,
                    membership_model_1.MembershipPermission.HiddenAdmin,
                    membership_model_1.MembershipPermission.Manager,
                ],
            },
        }, {
            attributes: ['organisationId'],
            useCache: true,
        });
        return underscore_1.Underscore.map(memberships, 'organisationId');
    }
    async getProfileMembershipOrganisationIds(profileId) {
        const memberships = await this.membershipsService.findAll({
            profileId,
            status: membership_model_1.MembershipStatus.Active,
        }, {
            attributes: ['organisationId'],
            useCache: true,
        });
        return underscore_1.Underscore.map(memberships, 'organisationId');
    }
};
exports.WebinarsRepositoryHelper = WebinarsRepositoryHelper;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinar_participants_service_1.WebinarParticipantsService)),
    __metadata("design:type", webinar_participants_service_1.WebinarParticipantsService)
], WebinarsRepositoryHelper.prototype, "webinarParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_1.MembershipsService)),
    __metadata("design:type", memberships_service_1.MembershipsService)
], WebinarsRepositoryHelper.prototype, "membershipsService", void 0);
exports.WebinarsRepositoryHelper = WebinarsRepositoryHelper = __decorate([
    (0, common_1.Injectable)()
], WebinarsRepositoryHelper);
//# sourceMappingURL=webinars.repository.helper.js.map