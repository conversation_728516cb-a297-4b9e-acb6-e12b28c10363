"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Webinar = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const sequelize_1 = require("sequelize");
const graphql_1 = require("@nestjs/graphql");
const short_uuid_1 = __importDefault(require("short-uuid"));
const organisation_model_1 = require("../../organisations/models/organisation.model");
const webinars_args_1 = require("../args/webinars.args");
const webinar_participant_model_1 = require("../../webinar-participants/models/webinar-participant.model");
const event_model_1 = require("../../events/models/event.model");
let Webinar = class Webinar extends sequelize_typescript_1.Model {
    get isEnded() {
        return ((this.endDate && this.endDate.getTime() < Date.now()) ||
            Boolean(this.liveStreamStoppedAt));
    }
};
exports.Webinar = Webinar;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    (0, sequelize_typescript_1.Column)({
        primaryKey: true,
        unique: true,
        allowNull: false,
        defaultValue: short_uuid_1.default.generate,
    }),
    __metadata("design:type", String)
], Webinar.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({ allowNull: false }),
    __metadata("design:type", String)
], Webinar.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)(() => webinars_args_1.WebinarType, { nullable: false }),
    (0, sequelize_typescript_1.Column)({ type: sequelize_1.DataTypes.STRING }),
    __metadata("design:type", String)
], Webinar.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({ allowNull: true }),
    __metadata("design:type", String)
], Webinar.prototype, "image", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, sequelize_typescript_1.Column)({ allowNull: false }),
    __metadata("design:type", String)
], Webinar.prototype, "description", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Webinar.prototype, "startDate", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Webinar.prototype, "endDate", void 0);
__decorate([
    (0, graphql_1.Field)(() => [String]),
    (0, sequelize_typescript_1.Column)({
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
    }),
    __metadata("design:type", Array)
], Webinar.prototype, "keywords", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { defaultValue: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Webinar.prototype, "isPublic", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { defaultValue: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Webinar.prototype, "isParticipantsCanInvite", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { defaultValue: true }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Webinar.prototype, "isParticipantListVisible", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean, { defaultValue: false }),
    sequelize_typescript_1.Column,
    __metadata("design:type", Boolean)
], Webinar.prototype, "isInternal", void 0);
__decorate([
    (0, graphql_1.Field)(() => Boolean),
    (0, sequelize_typescript_1.Column)(sequelize_1.DataTypes.VIRTUAL),
    __metadata("design:type", Boolean),
    __metadata("design:paramtypes", [])
], Webinar.prototype, "isEnded", null);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => organisation_model_1.Organisation),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Webinar.prototype, "organisationId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => organisation_model_1.Organisation, {
        foreignKey: 'organisationId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", organisation_model_1.Organisation)
], Webinar.prototype, "organisation", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => event_model_1.Event),
    sequelize_typescript_1.Column,
    __metadata("design:type", String)
], Webinar.prototype, "eventId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => event_model_1.Event, {
        foreignKey: 'eventId',
        onDelete: 'CASCADE',
    }),
    __metadata("design:type", event_model_1.Event)
], Webinar.prototype, "event", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => webinar_participant_model_1.WebinarParticipant, 'webinarId'),
    __metadata("design:type", Array)
], Webinar.prototype, "webinarParticipants", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Webinar.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    sequelize_typescript_1.Column,
    __metadata("design:type", Date)
], Webinar.prototype, "updatedAt", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({ allowNull: true }),
    __metadata("design:type", String)
], Webinar.prototype, "liveStreamId", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ allowNull: true }),
    __metadata("design:type", String)
], Webinar.prototype, "liveStreamPrivateKey", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({ allowNull: true }),
    __metadata("design:type", String)
], Webinar.prototype, "liveStreamPlaybackId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({ allowNull: true }),
    __metadata("design:type", Date)
], Webinar.prototype, "liveStreamStartedAt", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({ allowNull: true }),
    __metadata("design:type", Date)
], Webinar.prototype, "liveStreamStoppedAt", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({ allowNull: true }),
    __metadata("design:type", String)
], Webinar.prototype, "liveStreamRecordingId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({ allowNull: true }),
    __metadata("design:type", String)
], Webinar.prototype, "chatChannelId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    (0, sequelize_typescript_1.Column)({ allowNull: true }),
    __metadata("design:type", String)
], Webinar.prototype, "assetId", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], Webinar.prototype, "isViewed", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Date)
], Webinar.prototype, "viewedDate", void 0);
exports.Webinar = Webinar = __decorate([
    sequelize_typescript_1.Table,
    (0, graphql_1.ObjectType)()
], Webinar);
//# sourceMappingURL=webinar.model.js.map