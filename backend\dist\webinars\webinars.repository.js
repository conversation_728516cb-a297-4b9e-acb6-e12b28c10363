"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebinarsRepository = void 0;
const common_1 = require("@nestjs/common");
const webinar_model_1 = require("./models/webinar.model");
const pagination_1 = require("../common/helpers/pagination");
const sequelize_1 = require("sequelize");
const webinars_repository_helper_1 = require("./helpers/webinars.repository.helper");
const webinar_participants_args_1 = require("../webinar-participants/args/webinar-participants.args");
const fuse_js_1 = __importDefault(require("fuse.js"));
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const webinars_service_1 = require("./webinars.service");
const activities_args_1 = require("../activities/args/activities.args");
const activities_service_1 = require("../activities/activities.service");
let WebinarsRepository = class WebinarsRepository {
    async findWebinars(profileId, filter, pagination) {
        const webinarParticipantStatus = (filter === null || filter === void 0 ? void 0 : filter.webinarParticipantStatus) || [];
        const webinarParticipantWebinarIds = await this.helper.getWebinarParticipantWebinarIds(profileId, {
            status: webinarParticipantStatus.length > 0
                ? webinarParticipantStatus
                : [
                    webinar_participants_args_1.WebinarParticipantStatus.Registered,
                    webinar_participants_args_1.WebinarParticipantStatus.Host,
                    webinar_participants_args_1.WebinarParticipantStatus.HostAdmin,
                    webinar_participants_args_1.WebinarParticipantStatus.Speaker,
                    webinar_participants_args_1.WebinarParticipantStatus.HiddenHost,
                    webinar_participants_args_1.WebinarParticipantStatus.InvitedByHost,
                    webinar_participants_args_1.WebinarParticipantStatus.InvitedByParticipant,
                    webinar_participants_args_1.WebinarParticipantStatus.InvitedRegistered,
                ],
        });
        const hostOrganisationIds = await this.helper.getHostOrganisationIds(profileId);
        const membershipOrganisationIds = await this.helper.getProfileMembershipOrganisationIds(profileId);
        const internalWebinars = [];
        if (membershipOrganisationIds.length !== 0) {
            for (const org of membershipOrganisationIds) {
                const webinar = await this.webinarsService.findInternalWebinars(org);
                webinar && webinar.length !== 0
                    ? internalWebinars.push(...webinar)
                    : '';
            }
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.isPublic) === false &&
            webinarParticipantWebinarIds.length === 0 &&
            internalWebinars.length === 0) {
            return {
                records: [],
                totalCount: 0,
            };
        }
        const extraQueryParams = {
            [sequelize_1.Op.or]: [
                {
                    organisationId: {
                        [sequelize_1.Op.in]: hostOrganisationIds,
                    },
                },
                {
                    organisationId: {
                        [sequelize_1.Op.notIn]: hostOrganisationIds,
                    },
                    isPublic: true,
                },
                {
                    organisationId: {
                        [sequelize_1.Op.in]: membershipOrganisationIds,
                    },
                    isInternal: true,
                },
                {
                    id: {
                        [sequelize_1.Op.in]: webinarParticipantWebinarIds,
                    },
                },
            ],
        };
        let includeIds = [];
        if (webinarParticipantStatus.length > 0) {
            includeIds =
                webinarParticipantWebinarIds.length > 0
                    ? webinarParticipantWebinarIds
                    : ['non-existing-id'];
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.isPublic) === true || (filter === null || filter === void 0 ? void 0 : filter.isPublic) === false) {
            extraQueryParams.isPublic = filter.isPublic;
        }
        if (filter === null || filter === void 0 ? void 0 : filter.organisationId) {
            extraQueryParams.organisationId = filter.organisationId;
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.type) && filter.type.length > 0) {
            extraQueryParams.type = {
                [sequelize_1.Op.in]: filter.type,
            };
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.isEnded) === true) {
            extraQueryParams.endDate = {
                [sequelize_1.Op.lt]: new Date(),
            };
        }
        if ((filter === null || filter === void 0 ? void 0 : filter.isEnded) === false) {
            extraQueryParams.endDate = {
                [sequelize_1.Op.gte]: new Date(),
            };
        }
        const result = await new pagination_1.PaginationHelper().getPaginatedResults({
            model: webinar_model_1.Webinar,
            pagination,
            extraQueryParams,
            includeIds,
            excludeIds: [],
            searchText: filter === null || filter === void 0 ? void 0 : filter.searchText,
        });
        if (filter === null || filter === void 0 ? void 0 : filter.searchText) {
            const options = {
                includeScore: true,
                includeMatches: true,
                ignoreLocation: true,
                threshold: 0.4,
                keys: ['name', 'description'],
            };
            const fuse = new fuse_js_1.default(result.records, options);
            const filteringRecords = fuse.search(filter === null || filter === void 0 ? void 0 : filter.searchText);
            const filteredRecords = filteringRecords.filter(item => item.score < options.threshold);
            const splicedRecords = !!(pagination === null || pagination === void 0 ? void 0 : pagination.first)
                ? filteringRecords.splice((pagination === null || pagination === void 0 ? void 0 : pagination.offset) || 0, pagination === null || pagination === void 0 ? void 0 : pagination.first)
                : filteringRecords;
            const webinarIds = splicedRecords.map(record => record.item.id);
            const webinarCompletions = await this.activitiesService.findAll({
                profileId,
                webinarId: webinarIds,
                type: activities_args_1.ActivityType.WebinarCompleted,
            });
            const completionMap = new Map(webinarCompletions.map(completion => [
                completion.webinarId,
                completion,
            ]));
            const finalRecords = splicedRecords.map(record => {
                const webinar = record.item;
                const webinarCompletion = completionMap.get(webinar.id);
                return Object.assign(Object.assign({}, webinar.toJSON()), { isViewed: webinarCompletion ? true : null, viewedDate: webinarCompletion ? webinarCompletion.createdAt : null });
            });
            return {
                records: finalRecords,
                totalCount: filteredRecords.length,
            };
        }
        return result;
    }
};
exports.WebinarsRepository = WebinarsRepository;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinars_repository_helper_1.WebinarsRepositoryHelper)),
    __metadata("design:type", webinars_repository_helper_1.WebinarsRepositoryHelper)
], WebinarsRepository.prototype, "helper", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinars_service_1.WebinarsService)),
    __metadata("design:type", webinars_service_1.WebinarsService)
], WebinarsRepository.prototype, "webinarsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => activities_service_1.ActivitiesService)),
    __metadata("design:type", activities_service_1.ActivitiesService)
], WebinarsRepository.prototype, "activitiesService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], WebinarsRepository.prototype, "logger", void 0);
exports.WebinarsRepository = WebinarsRepository = __decorate([
    (0, common_1.Injectable)()
], WebinarsRepository);
//# sourceMappingURL=webinars.repository.js.map