"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebinarsResolver = exports.WebinarsAndKeywordsResponse = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const webinar_model_1 = require("./models/webinar.model");
const webinars_service_1 = require("./webinars.service");
const graphql_auth_guard_1 = require("../authz/graphql-auth.guard");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const create_webinar_input_1 = require("./dto/create-webinar.input");
const paginated_result_1 = require("../common/args/paginated-result");
const webinars_args_1 = require("./args/webinars.args");
const update_webinar_input_1 = require("./dto/update-webinar.input");
const memberships_service_rights_1 = require("../memberships/helpers/memberships.service.rights");
const organisations_service_1 = require("../organisations/organisations.service");
const organisation_model_1 = require("../organisations/models/organisation.model");
const webinar_participant_model_1 = require("../webinar-participants/models/webinar-participant.model");
const webinar_participants_service_1 = require("../webinar-participants/webinar-participants.service");
const webinar_participants_args_1 = require("../webinar-participants/args/webinar-participants.args");
const webinar_participants_service_helper_1 = require("../webinar-participants/helpers/webinar-participants.service.helper");
const error_1 = require("../common/helpers/error");
const activities_args_1 = require("../activities/args/activities.args");
const activities_service_1 = require("../activities/activities.service");
let WebinarsAndKeywordsResponse = class WebinarsAndKeywordsResponse {
};
exports.WebinarsAndKeywordsResponse = WebinarsAndKeywordsResponse;
__decorate([
    (0, graphql_1.Field)(() => [String]),
    __metadata("design:type", Array)
], WebinarsAndKeywordsResponse.prototype, "keywords", void 0);
__decorate([
    (0, graphql_1.Field)(() => [webinar_model_1.Webinar]),
    __metadata("design:type", Array)
], WebinarsAndKeywordsResponse.prototype, "webinars", void 0);
exports.WebinarsAndKeywordsResponse = WebinarsAndKeywordsResponse = __decorate([
    (0, graphql_1.ObjectType)()
], WebinarsAndKeywordsResponse);
let WebinarsResolver = class WebinarsResolver {
    async webinar(user, id) {
        this.logger.verbose('WebinarsResolver.webinar (query)', {
            user: user.toLogObject(),
            id,
        });
        const webinar = await this.webinarsService.findById(id);
        if (!webinar) {
            this.errorHelper.throwHttpException(`WebinarsResolver.webinar`, `Sorry, this webinar does not exist.`);
        }
        const webinarCompletion = await this.activitiesService.findOne({
            profileId: user.profileId,
            webinarId: id,
            type: activities_args_1.ActivityType.WebinarCompleted,
        });
        return Object.assign(webinar, {
            isViewed: webinarCompletion ? true : null,
            viewedDate: webinarCompletion ? webinarCompletion.createdAt : null,
        });
    }
    webinars(user, webinarArgs) {
        var _a, _b, _c, _d, _e, _f;
        this.logger.verbose('WebinarsResolver.webinars (query)', {
            user: user.toLogObject(),
            webinarArgs,
        });
        return this.webinarsService.findWebinars(user.profileId, {
            webinarParticipantStatus: (_a = webinarArgs === null || webinarArgs === void 0 ? void 0 : webinarArgs.filter) === null || _a === void 0 ? void 0 : _a.webinarParticipantStatus,
            organisationId: (_b = webinarArgs === null || webinarArgs === void 0 ? void 0 : webinarArgs.filter) === null || _b === void 0 ? void 0 : _b.organisationId,
            type: (_c = webinarArgs === null || webinarArgs === void 0 ? void 0 : webinarArgs.filter) === null || _c === void 0 ? void 0 : _c.type,
            isPublic: (_d = webinarArgs === null || webinarArgs === void 0 ? void 0 : webinarArgs.filter) === null || _d === void 0 ? void 0 : _d.isPublic,
            searchText: (_e = webinarArgs === null || webinarArgs === void 0 ? void 0 : webinarArgs.filter) === null || _e === void 0 ? void 0 : _e.searchText,
            isEnded: (_f = webinarArgs === null || webinarArgs === void 0 ? void 0 : webinarArgs.filter) === null || _f === void 0 ? void 0 : _f.isEnded,
        }, {
            first: webinarArgs === null || webinarArgs === void 0 ? void 0 : webinarArgs.first,
            after: webinarArgs === null || webinarArgs === void 0 ? void 0 : webinarArgs.after,
            sortBy: webinarArgs === null || webinarArgs === void 0 ? void 0 : webinarArgs.sortBy,
            sortOrder: webinarArgs === null || webinarArgs === void 0 ? void 0 : webinarArgs.sortOrder,
        });
    }
    async webinarsAndKeywords(organisationId, user) {
        this.logger.verbose('WebinarsResolver.webinarKeywords (query)', {
            user: user.toLogObject(),
            organisationId,
        });
        return await this.webinarsService.keywords(organisationId, user.profileId);
    }
    async createWebinar(user, webinarData) {
        this.logger.verbose('WebinarsResolver.createWebinar (mutation)', {
            user: user.toLogObject(),
            webinarData,
        });
        const { organisationId } = webinarData;
        await this.membershipsServiceRights.checkRights(organisationId, {
            currentProfileId: user.profileId,
            rights: [memberships_service_rights_1.MembershipRight.CreateWebinar],
        });
        if (webinarData.liveStreamRecordingId) {
            const muxData = webinarData.liveStreamRecordingId.split('|');
            webinarData.liveStreamRecordingId = muxData[0];
            return this.webinarsService.createWebinar(Object.assign({ assetId: muxData[1] }, webinarData), user.profileId, { currentUser: user });
        }
        return this.webinarsService.createWebinar(webinarData, user.profileId, {
            currentUser: user,
        });
    }
    async updateWebinar(user, webinarId, webinarData) {
        this.logger.verbose('WebinarsResolver.updateWebinar (mutation)', {
            user: user.toLogObject(),
            webinarData,
        });
        const webinar = await this.webinarsService.findById(webinarId);
        await this.membershipsServiceRights.checkRights(webinar.organisationId, {
            currentProfileId: user.profileId,
            rights: [memberships_service_rights_1.MembershipRight.UpdateWebinar],
        });
        return this.webinarsService.updateWebinar(user, webinarId, webinarData);
    }
    async removeWebinar(user, id) {
        this.logger.verbose('WebinarsResolver.removeWebinar (mutation)', {
            user: user.toLogObject(),
            id,
        });
        const webinar = await this.webinarsService.findById(id);
        if (!webinar)
            return true;
        await this.membershipsServiceRights.checkRights(webinar.organisationId, {
            currentProfileId: user.profileId,
            rights: [memberships_service_rights_1.MembershipRight.RemoveWebinar],
        });
        await this.webinarsService.removeWebinar(id);
        return true;
    }
    async registerToWebinar(user, webinarId) {
        this.logger.verbose('WebinarsResolver.registerToWebinar (mutation)', {
            user: user.toLogObject(),
            webinarId,
        });
        return this.webinarsService.register(user, webinarId);
    }
    async unregisterToWebinar(user, webinarId) {
        this.logger.verbose('WebinarsResolver.unregisterToWebinar (mutation)', {
            user: user.toLogObject(),
            webinarId,
        });
        return this.webinarsService.unregister(user, webinarId);
    }
    async organisation(webinar) {
        if (webinar.organisation)
            return webinar.organisation;
        this.logger.verbose('WebinarsResolver.organisation (field resolver)', {
            webinarId: webinar.id,
        });
        return this.organisationsService.findById(webinar.organisationId, {
            useCache: true,
        });
    }
    async participant(user, webinar) {
        this.logger.verbose('WebinarsResolver.participants (field resolver)', {
            webinarId: webinar.id,
        });
        return this.webinarParticipantsService.findOne({
            webinarId: webinar.id,
            profileId: user.profileId,
        }, { useCache: true });
    }
    async participants(webinar, webinarParticipantsArgs) {
        this.logger.verbose('WebinarsResolver.participants (field resolver)', {
            webinarId: webinar.id,
        });
        return this.webinarParticipantsService.findAll(Object.assign({ webinarId: webinar.id }, webinar_participants_service_helper_1.WebinarParticipantsServiceHelper.getQueryParams(webinarParticipantsArgs)), { useCache: false });
    }
};
exports.WebinarsResolver = WebinarsResolver;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinars_service_1.WebinarsService)),
    __metadata("design:type", webinars_service_1.WebinarsService)
], WebinarsResolver.prototype, "webinarsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinar_participants_service_1.WebinarParticipantsService)),
    __metadata("design:type", webinar_participants_service_1.WebinarParticipantsService)
], WebinarsResolver.prototype, "webinarParticipantsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => memberships_service_rights_1.MembershipsServiceRights)),
    __metadata("design:type", memberships_service_rights_1.MembershipsServiceRights)
], WebinarsResolver.prototype, "membershipsServiceRights", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => organisations_service_1.OrganisationsService)),
    __metadata("design:type", organisations_service_1.OrganisationsService)
], WebinarsResolver.prototype, "organisationsService", void 0);
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => activities_service_1.ActivitiesService)),
    __metadata("design:type", activities_service_1.ActivitiesService)
], WebinarsResolver.prototype, "activitiesService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], WebinarsResolver.prototype, "logger", void 0);
__decorate([
    (0, common_1.Inject)(),
    __metadata("design:type", error_1.ErrorHelper)
], WebinarsResolver.prototype, "errorHelper", void 0);
__decorate([
    (0, graphql_1.Query)(() => webinar_model_1.Webinar),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id', { nullable: false })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], WebinarsResolver.prototype, "webinar", null);
__decorate([
    (0, graphql_1.Query)(() => paginated_result_1.WebinarsResult),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, webinars_args_1.WebinarsArgs]),
    __metadata("design:returntype", Promise)
], WebinarsResolver.prototype, "webinars", null);
__decorate([
    (0, graphql_1.Query)(() => WebinarsAndKeywordsResponse),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, graphql_1.Args)('organisationId')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WebinarsResolver.prototype, "webinarsAndKeywords", null);
__decorate([
    (0, graphql_1.Mutation)(() => webinar_model_1.Webinar),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('webinarData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_webinar_input_1.CreateWebinarInput]),
    __metadata("design:returntype", Promise)
], WebinarsResolver.prototype, "createWebinar", null);
__decorate([
    (0, graphql_1.Mutation)(() => webinar_model_1.Webinar),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('webinarId')),
    __param(2, (0, graphql_1.Args)('webinarData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, update_webinar_input_1.UpdateWebinarInput]),
    __metadata("design:returntype", Promise)
], WebinarsResolver.prototype, "updateWebinar", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], WebinarsResolver.prototype, "removeWebinar", null);
__decorate([
    (0, graphql_1.Mutation)(() => webinar_participant_model_1.WebinarParticipant),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('webinarId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], WebinarsResolver.prototype, "registerToWebinar", null);
__decorate([
    (0, graphql_1.Mutation)(() => webinar_participant_model_1.WebinarParticipant),
    (0, common_1.UseGuards)(graphql_auth_guard_1.GqlAuthGuard),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Args)('webinarId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], WebinarsResolver.prototype, "unregisterToWebinar", null);
__decorate([
    (0, graphql_1.ResolveField)('organisation', () => organisation_model_1.Organisation),
    __param(0, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [webinar_model_1.Webinar]),
    __metadata("design:returntype", Promise)
], WebinarsResolver.prototype, "organisation", null);
__decorate([
    (0, graphql_1.ResolveField)('participant', () => webinar_participant_model_1.WebinarParticipant, { nullable: true }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, graphql_1.Parent)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, webinar_model_1.Webinar]),
    __metadata("design:returntype", Promise)
], WebinarsResolver.prototype, "participant", null);
__decorate([
    (0, graphql_1.ResolveField)('participants', () => [webinar_participant_model_1.WebinarParticipant]),
    __param(0, (0, graphql_1.Parent)()),
    __param(1, (0, graphql_1.Args)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [webinar_model_1.Webinar,
        webinar_participants_args_1.WebinarParticipantsArgs]),
    __metadata("design:returntype", Promise)
], WebinarsResolver.prototype, "participants", null);
exports.WebinarsResolver = WebinarsResolver = __decorate([
    (0, graphql_1.Resolver)(() => webinar_model_1.Webinar)
], WebinarsResolver);
//# sourceMappingURL=webinars.resolver.js.map