"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebinarsController = void 0;
const common_1 = require("@nestjs/common");
const raw_body_1 = __importDefault(require("raw-body"));
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const webinar_broadcast_service_1 = require("../webinar-broadcast/webinar-broadcast.service");
let WebinarsController = class WebinarsController {
    async leaveWebinarChannel(webinarId, req) {
        this.logger.verbose('WebinarsController.leaveWebinarChannel (REST)');
        try {
            if (req.readable) {
                const raw = await (0, raw_body_1.default)(req);
                const text = raw.toString().trim();
                const leaveWebinarDto = JSON.parse(text);
                return this.webinarBroadcastService.removeFromWebinarChannel(webinarId, [leaveWebinarDto.profileId]);
            }
            return false;
        }
        catch (err) {
            this.logger.error(`WebinarsController.leaveWebinarChannel`, err.message);
            return false;
        }
    }
};
exports.WebinarsController = WebinarsController;
__decorate([
    (0, common_1.Inject)((0, common_1.forwardRef)(() => webinar_broadcast_service_1.WebinarBroadcastService)),
    __metadata("design:type", webinar_broadcast_service_1.WebinarBroadcastService)
], WebinarsController.prototype, "webinarBroadcastService", void 0);
__decorate([
    (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER),
    __metadata("design:type", winston_1.Logger)
], WebinarsController.prototype, "logger", void 0);
__decorate([
    (0, common_1.Post)('/:webinarId/leave'),
    __param(0, (0, common_1.Param)('webinarId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WebinarsController.prototype, "leaveWebinarChannel", null);
exports.WebinarsController = WebinarsController = __decorate([
    (0, common_1.Controller)('webinars')
], WebinarsController);
//# sourceMappingURL=webinarsController.js.map