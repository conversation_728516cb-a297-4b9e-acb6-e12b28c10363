'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Profiles', 'referredByOrganisationId', {
      type: Sequelize.STRING,
      allowNull: true,
      references: {
        model: 'Organisations',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('Profiles', 'referredByOrganisationId');
  },
};
