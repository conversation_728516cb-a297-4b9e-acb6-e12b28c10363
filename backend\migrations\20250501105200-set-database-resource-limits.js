'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // // Get current database name and environment
    // const [dbResult] = await queryInterface.sequelize.query(
    //   'SELECT current_database() as db_name',
    // );
    // const currentDb = dbResult[0].db_name;
    // const isProduction = process.env.NODE_ENV === 'production';

    // // Set resource limits based on database name AND environment
    // if (currentDb === 'hablo' && !isProduction) {
    //   // Local development defaults
    //   await queryInterface.sequelize.query(`
    //     ALTER DATABASE "${currentDb}"
    //       SET work_mem = '12MB'
    //     , SET maintenance_work_mem = '384MB'
    //     , SET effective_cache_size = '12GB'
    //     , SET max_parallel_workers_per_gather = 2;
    //   `);
    // }
  },

  async down(queryInterface, Sequelize) {
    // Reset to defaults for current database
    const [result] = await queryInterface.sequelize.query(
      'SELECT current_database() as db_name',
    );
    const currentDb = result[0].db_name;

    await queryInterface.sequelize.query(`
      ALTER DATABASE "${currentDb}"
        SET work_mem = DEFAULT
      , SET maintenance_work_mem = DEFAULT
      , SET effective_cache_size = DEFAULT
      , SET max_parallel_workers_per_gather = DEFAULT;
    `);
  },
};
