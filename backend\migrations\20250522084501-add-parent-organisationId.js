'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Add parentOrganisationId to Organisations table
    await queryInterface.addColumn('OrganisationLoyaltyPoints', 'parentOrganisationId', {
      type: Sequelize.DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Organisations',
        key: 'id',
      },
    });

    // Add parentOrganisationId to OrganisationActivities table
    await queryInterface.addColumn('OrganisationActivities', 'parentOrganisationId',{
        type: Sequelize.DataTypes.STRING,
        allowNull: true,
        references: {
          model: 'Organisations',
          key: 'id',
        },
      },
    );
  },

  down: async (queryInterface, Sequelize) => {
    // Remove parentOrganisationId from OrganisationActivities table
    await queryInterface.removeColumn('OrganisationActivities', 'parentOrganisationId');

    // Remove parentOrganisationId from Organisations table
    await queryInterface.removeColumn('OrganisationLoyaltyPoints', 'parentOrganisationId');
  },
};
