{"name": "backend", "version": "0.0.3", "description": "", "scripts": {"start": "sequelize-cli db:migrate && ts-node --transpile-only ./src/main.ts", "dev": "./run start", "migrate": "./run migrate", "test": "./run test", "test:watch": "./run test --watch", "test:cov": "./run test --coverage", "test:debug": "node --max_old_space_size=4096 --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand"}, "repository": {"type": "git", "url": "git+https://github.com/habloteam/backend.git"}, "engines": {"node": "20"}, "author": "", "license": "UNLICENSED", "dependencies": {"@google-cloud/logging-winston": "^6.0.0", "@mux/mux-node": "^3.0.4", "@nestjs/bull": "^0.1.2", "@nestjs/common": "^7.1.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^7.1.1", "@nestjs/graphql": "^7.7.0", "@nestjs/passport": "^7.0.0", "@nestjs/platform-express": "^7.1.1", "@nestjs/schedule": "^0.4.0", "@nestjs/sequelize": "^0.1.0", "@ntegral/nestjs-sendgrid": "^1.0.0", "@ntegral/nestjs-sentry": "^2.0.5", "@sendgrid/mail": "^7.2.3", "@sentry/node": "^9.3.0", "@sentry/tracing": "^6.2.0", "@types/lodash": "^4.14.155", "apollo-server-express": "^2.14.3", "async": "^3.2.5", "auth0": "^4.3.1", "axios": "^0.19.2", "bottleneck": "^2.19.5", "bull": "^3.16.0", "class-transformer": "^0.2.3", "class-validator": "^0.12.2", "cloudinary": "^1.22.0", "dotenv": "^8.2.0", "express-rate-limit": "^5.1.3", "fuse.js": "^6.6.2", "get-timezone-offset": "^1.0.4", "getstream": "6.0.0", "graphql": "^14.5.0", "graphql-query-complexity": "^0.6.0", "graphql-redis-subscriptions": "^2.2.2", "graphql-subscriptions": "^1.1.0", "graphql-tools": "^6.2.4", "graphql-type-json": "^0.3.2", "helmet": "^4.2.0", "ioredis": "^5.5.0", "jwks-rsa": "^1.8.0", "lodash": "^4.17.21", "moment": "^2.27.0", "moment-timezone": "^0.5.45", "nest-winston": "^1.3.5", "nestjs-redis": "^1.2.8", "object-hash": "^2.0.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.2.1", "pg-hstore": "^2.3.3", "raw-body": "^2.4.1", "reflect-metadata": "^0.1.13", "request": "^2.88.2", "request-promise": "^4.2.6", "rimraf": "^3.0.2", "rxjs": "^6.5.5", "sequelize": "^5.21.12", "sequelize-cli": "^5.5.1", "sequelize-typescript": "^1.1.0", "short-uuid": "4.2.0", "stream-chat": "2.5.0", "stripe": "^12.9.0", "ts-node": "^8.10.2", "typescript": "^5.8.2", "winston": "^3.3.3"}, "devDependencies": {"@nestjs/cli": "^7.2.0", "@nestjs/schematics": "^7.0.0", "@nestjs/testing": "^7.1.1", "@types/async": "^3.2.24", "@types/bluebird": "^3.5.32", "@types/bull": "^3.14.0", "@types/cron": "^1.7.3", "@types/express": "^4.17.6", "@types/faker": "^5.1.3", "@types/jest": "^29.5.14", "@types/node": "^14.0.6", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^2.0.9", "@types/validator": "^13.0.0", "@typescript-eslint/eslint-plugin": "^3.0.2", "@typescript-eslint/parser": "^3.0.2", "eslint": "^7.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.20.2", "faker": "^5.1.0", "jest": "^29.7.0", "node-duration": "^1.0.4", "prettier": "^2.0.5", "supertest": "^4.0.2", "testcontainers": "^10.2.1", "ts-jest": "^29.2.6", "ts-loader": "^7.0.5", "tsconfig-paths": "^3.9.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}