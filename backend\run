#!/usr/bin/env bash
set -eo pipefail

export GOOGLE_PROJECT_ID=${GOOGLE_PROJECT_ID:="hablo-279710"}
export GOOGLE_COMPUTE_ZONE=${GOOGLE_COMPUTE_ZONE:="europe-west1"}
export PROJECT_NAME=${PROJECT_NAME:="hablo"}
export CLUSTER_NAME=${CLUSTER_NAME:="dev1"}
export BRANCH=${GITHUB_REF:=$(git branch --show-current)}
export NODE_OPTIONS=${NODE_OPTIONS:="--max-old-space-size=4096"}

ENV_KEY_PRODUCTION=.env.production
ENV_KEY_DEVELOPMENT=.env.development

runCommands() {
  if [[ -z "${CI}" ]]; then
    source "./scripts/checkDevRequirements.sh"
  fi

  if [[ "${1}" == "install" ]]; then
    install
  elif [[ "${1}" == "start" ]]; then
    start
  elif [[ "${1}" == "start:prod" ]]; then
    startProd
  elif [[ "${1}" == "migrate" ]]; then
    migrate
  elif [[ "${1}" == "dependencies" ]]; then
    dependencies
  elif [[ "${1}" == "sqlProxy" ]]; then
    # SQL PORT is not default so you don't accidentally connect to it thinking it was local DB.
    sqlProxy 5431
  elif [[ "${1}" == "test" ]]; then
    test "${2}" "${@:2}"
  elif [[ "${1}" == "build" ]]; then
    build
  elif [[ "${1}" == "deploy:beta" ]]; then
    export DB_NAME='hablo-beta'
    deployToCloudRun api-beta
  elif [[ "${1}" == "deploy:e2e" ]]; then
    export DB_NAME='hablo-e2e'
    deployToCloudRun api-e2e
  elif [[ "${1}" == "deploy:production" ]]; then
    export DB_NAME='hablo'
    deployToCloudRun api 2
  elif [[ "${1}" == "replicateDatabaseTo:beta" ]]; then
    replicateDatabaseTo hablo-beta
  elif [[ "${1}" == "encrypt" ]]; then
    encrypt ${ENV_KEY_PRODUCTION}
    encrypt ${ENV_KEY_DEVELOPMENT}
  elif [[ "${1}" == "decrypt" ]]; then
    decrypt ${ENV_KEY_PRODUCTION}
    decrypt ${ENV_KEY_DEVELOPMENT}
  elif [[ "${1}" == "docker" ]]; then
    runDocker
  else
    echo "Please provide one of the following commands: install, start, start:prod, dev, migrate, dependencies, sqlProxy, test, build, deploy:e2e, deploy:production, encrypt, decrypt, docker"
    exit 1
  fi
}

sqlProxy() {
  CLOUD_SQL_PROXY=./scripts/cloud_sql_proxy
  if [ ! -f ${CLOUD_SQL_PROXY} ]; then
    if [[ -n "${CI}" ]]; then
      wget https://dl.google.com/cloudsql/cloud_sql_proxy.linux.amd64 -O ${CLOUD_SQL_PROXY}
    else
      curl -o ${CLOUD_SQL_PROXY} https://dl.google.com/cloudsql/cloud_sql_proxy.darwin.amd64
    fi
    chmod +x ${CLOUD_SQL_PROXY}
  fi

  ${CLOUD_SQL_PROXY} -instances="hablo-279710:europe-west1:hablo=tcp:${1}"
}

install() {
  if [[ -n "${CI}" ]]; then
    yarn install
  else
    echo "Setting correct node version..."
    nvm install

    DEPENDENCIES_CHECKSUM=.dependencies-checksum
    if [[ ! -f ${DEPENDENCIES_CHECKSUM} || $(cat ${DEPENDENCIES_CHECKSUM}) != $(md5 -q yarn.lock) ]]; then
      echo "Installing dependencies..."
      yarn install --silent
      md5 -q yarn.lock >${DEPENDENCIES_CHECKSUM}
    fi
  fi
}

migrate() {
  yarn sequelize-cli db:migrate
}

start() {
  install
  rm -rf dist
  export NODE_ENV=development
  decrypt ${ENV_KEY_DEVELOPMENT}
  export NODE_OPTIONS=--max-old-space-size=4096

  migrate
  yarn --silent nest start --watch
}

startProd() {
  install
  rm -rf dist
  export NODE_ENV=production
  export NODE_ENV_LOCAL=true
  export NODE_OPTIONS=--max-old-space-size=4096
  decrypt ${ENV_KEY_PRODUCTION}

  migrate
  yarn --silent nest start --watch
}

dependencies() {
  docker compose up
}

test() {
  export NODE_ENV=test
  install
  yarn jest --clear-cache
  yarn jest --no-cache --bail --maxWorkers=2 --testTimeout 300000 "${@:2}"
}

runDocker() {
  decrypt ${ENV_KEY_DEVELOPMENT}
  docker build . -t ${PROJECT_NAME}
  docker run --env-file ${ENV_KEY_DEVELOPMENT} --network host -ti ${PROJECT_NAME}
}

auth() {
  if [[ -n "${CI}" ]]; then
    gcloud --quiet config set project "${GOOGLE_PROJECT_ID}"
    gcloud --quiet config set compute/region "${GOOGLE_COMPUTE_ZONE}"
  fi
}

build() {
  auth 
  # Build and push to Artifact Registry
  gcloud builds submit --tag "${GOOGLE_COMPUTE_ZONE}-docker.pkg.dev/${GOOGLE_PROJECT_ID}/${PROJECT_NAME}/${BRANCH}" ./
}

deployToCloudRun() {
  if [[ -z "${1}" ]]; then
    echo No service name passed in!
  else
    MIN_INSTANCE=${4:-0}
    auth
    decrypt ${ENV_KEY_PRODUCTION}
    gcloud run deploy \
      --project ${GOOGLE_PROJECT_ID} \
      --region ${GOOGLE_COMPUTE_ZONE} \
      --platform managed \
      --cpu 2 \
      --memory 8Gi \
      --min-instances ${MIN_INSTANCE} \
      --vpc-connector "europe-west1" \
      --add-cloudsql-instances "${GOOGLE_PROJECT_ID}:europe-west1:hablo" \
      --image "${GOOGLE_COMPUTE_ZONE}-docker.pkg.dev/${GOOGLE_PROJECT_ID}/${PROJECT_NAME}/${BRANCH}" \
      --set-env-vars "$(tr '\n' ',' < ${ENV_KEY_PRODUCTION})" \
      --set-env-vars "DB_NAME=${DB_NAME}" \
      --set-env-vars "SERVICE=${1}" \
      --allow-unauthenticated \
      "${1}"
  fi
}

deployToAppEngine() {
  if [[ -z "${1}" ]]; then
    echo No service name passed in!
  else
    auth
    # gcloud app deploy doesn't like having Dockerfile in dir
    rm -rf ./Dockerfile
    decrypt ${ENV_KEY_PRODUCTION}
    gcloud -q app deploy "${1}"
    gcloud -q app deploy dispatch.yaml
    git checkout -- ./Dockerfile
    deleteOldVersions api
  fi
}

deleteOldVersions() {
  # Need to remove old versions because GCP GAE has a limit
  COUNT=0
  VERSIONS=$(gcloud app versions list --service "${1}" --sort-by "~version" --format "value(version.id)") || ""
  for VERSION in ${VERSIONS}; do
    ((COUNT+=1))
    if [[ ${COUNT} -gt 5 ]]; then
      gcloud -q app versions delete --service "${1}" "${VERSION}" || true
    fi
  done
}

replicateDatabaseTo() {
  INSTANCE=hablo
  BUCKET=gs://db.myhablo.com/hablo/production/database.gz
  auth
  gcloud -q sql export sql ${INSTANCE} ${BUCKET} --database=hablo --offload
  gcloud -q sql databases delete "${1}" --instance=${INSTANCE}
  gcloud -q sql databases create "${1}" --instance=${INSTANCE}
  gcloud -q sql import sql ${INSTANCE} ${BUCKET} --database="${1}" --user=postgres

  install
  decrypt ${ENV_KEY_PRODUCTION}
  export NODE_ENV=production
  export DB_NAME=${1}
  export DB_HOST=localhost
  migrate
  echo "Database has been replicated and migrations run"
}

decrypt() {
  echo "Decrypting ${1}"
  gcloud kms decrypt --project=${GOOGLE_PROJECT_ID} --location=global --keyring=ci --key=${PROJECT_NAME} --plaintext-file="${1}" --ciphertext-file="${1}".enc
}

encrypt() {
  echo "Encrypting ${1}"
  if [[ ! -f ${1} ]]; then
    echo "No ${1} to encrypt!!!"
  else
    gcloud kms encrypt --project=${GOOGLE_PROJECT_ID} --location=global --keyring=ci --key=${PROJECT_NAME} --plaintext-file="${1}" --ciphertext-file="${1}".enc
  fi
}

runCommands "${@}"
