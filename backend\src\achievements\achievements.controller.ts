import { Controller, Get, Inject, Query, UseGuards, forwardRef } from '@nestjs/common';
import { ErrorHelper } from '../common/helpers/error';
import { GcpAuthGuard } from '../common/guards/gcp-auth.guard';
import { AchievementsService } from './achievements.service';
import config from '../config/config';

@Controller('achievements')
export class AchievementsController {
  @Inject()
  private readonly errorHelper: ErrorHelper;
  @Inject(forwardRef(() => AchievementsService))
  private readonly achievementsService: AchievementsService;

  @UseGuards(GcpAuthGuard)
  @Get('run-achievement-addWellbeingBadgeAchievement')
  runActivityDataMigration(): Promise<void> {
    void this.achievementsService.addWellbeingBadgeAchievement(null);
    return;
  }

  @Get('run-beta-achievement-addWellbeingBadgeAchievement')
  runActivityDataMigrationBeta(): Promise<void> {
    try {
      if (config.NODE_ENV === 'development' || config.SERVICE === 'api-beta') {
        void this.achievementsService.addWellbeingBadgeAchievement(null);
        return;
      } else {
        throw new Error('only beta env allowed');
      }
    } catch (err) {
      this.errorHelper.throwHttpException(
        `ActivitiesService.runJobBeta`,
        err.message,
      );
    }
  }

  @Get('run-beta-achievement-addWellbeingBadgeAchievementAllWeeks')
  async runBetaAddWellbeingBadgeAchievementAllWeeks(
    @Query('startDate') startDate?: string | string[]
  ): Promise<{ success: boolean }> {
    try {
      if (config.NODE_ENV !== 'development' && config.SERVICE !== 'api-beta') {
        throw new Error('only beta env allowed');
      }
      // Normalize startDate to array of strings (ISO dates)
      let weekStartDates: string[] = [];
      if (Array.isArray(startDate)) {
        weekStartDates = startDate;
      } else if (typeof startDate === 'string') {
        // Try to parse JSON array or fallback to single string
        try {
          const parsed = JSON.parse(startDate);
          weekStartDates = Array.isArray(parsed) ? parsed : [startDate];
        } catch {
          weekStartDates = [startDate];
        }
      }
      void this.processAchievementsInBackground(weekStartDates);
      return { success: true };
    } catch (err) {
      this.errorHelper.throwHttpException(
        `ActivitiesService.runBetaAddWellbeingBadgeAchievementAllWeeks`,
        err.message,
      );
    }
  }

  @Get('run-achievement-addWellbeingBadgeAchievementAllWeeks')
  async runAddWellbeingBadgeAchievementAllWeeks(
    @Query('startDate') startDate?: string | string[]
  ): Promise<{ success: boolean }> {
    try {
      // Normalize startDate to array of strings (ISO dates)
      let weekStartDates: string[] = [];
      if (Array.isArray(startDate)) {
        weekStartDates = startDate;
      } else if (typeof startDate === 'string') {
        // Try to parse JSON array or fallback to single string
        try {
          const parsed = JSON.parse(startDate);
          weekStartDates = Array.isArray(parsed) ? parsed : [startDate];
        } catch {
          weekStartDates = [startDate];
        }
      }
      void this.processAchievementsInBackground(weekStartDates);
      return { success: true };
    } catch (err) {
      this.errorHelper.throwHttpException(
        `ActivitiesService.runAddWellbeingBadgeAchievementAllWeeks`,
        err.message,
      );
    }
  }

private async processAchievementsInBackground(
  weekStartDates: string[]
): Promise<void> {
  try {
    for (const weekStart of weekStartDates) {
      console.log(`Processing week: ${weekStart}`);
      const startDate = new Date(weekStart);
      await this.achievementsService.addWellbeingBadgeAchievement(startDate);
      console.log(`Processed week: ${weekStart}`);
    }
  } catch (err) {
    this.errorHelper.throwHttpException(
      `ActivitiesService.processAchievementsInBackground`,
      err.message,
    );
  }
}

  @UseGuards(GcpAuthGuard)
  @Get('run-addOrganisationAchievements')
  runActivityDataMigrationPowerScroller(): Promise<void> {
    void this.achievementsService.addOrganisationAchievements();
    return;
  }

  @Get('run-beta-addOrganisationAchievements')
  runActivityDataMigrationBetaPowerScroller(): Promise<void> {
    try {
      if (config.NODE_ENV === 'development' || config.SERVICE === 'api-beta') {
        void this.achievementsService.addOrganisationAchievements();
        return;
      } else {
        throw new Error('only beta env allowed');
      }
    } catch (err) {
      this.errorHelper.throwHttpException(
        `ActivitiesService.runJobBeta`,
        err.message,
      );
    }
  }

  @UseGuards(GcpAuthGuard)
  @Get('run-resetDailyQuizStreakAchievement')
  runActivityDataMigrationADailyQuiz(): Promise<void> {
    void this.achievementsService.resetDailyQuizStreakAchievement();
    return;
  }

  @Get('run-beta-resetDailyQuizStreakAchievement')
  runActivityDataMigrationBetaDailyQuiz(): Promise<void> {
    try {
      if (config.NODE_ENV === 'development' || config.SERVICE === 'api-beta') {
        void this.achievementsService.resetDailyQuizStreakAchievement();
        return;
      } else {
        throw new Error('only beta env allowed');
      }
    } catch (err) {
      this.errorHelper.throwHttpException(
        `ActivitiesService.resetDailyQuizStreakAchievement`,
        err.message,
      );
    }
  }

  // @Get('run-missingPowerScrollerMigration')
  // runMissingPowerScrollerMigration(): Promise<void> {
  //   void this.achievementsService.runMissingPowerScrollerMigration();
  //   return;
  // }

  // @Get('run-beta-missingPowerScrollerMigration')
  // runMissingPowerScrollerMigrationBeta(): Promise<void> {
  //   try {
  //     if (config.NODE_ENV === 'development' || config.SERVICE === 'api-beta') {
  //       void this.achievementsService.runMissingPowerScrollerMigration();
  //       return;
  //     } else {
  //       throw new Error('only beta env allowed');
  //     }
  //   } catch (err) {
  //     this.errorHelper.throwHttpException(
  //       `AchievementsService.runMissingPowerScrollerMigration`,
  //       err.message,
  //     );
  //   }
  // }

  // @Get('run-addAmbassadorAchievement')
  // runAddAmbassadorAchievement(): Promise<void> {
  //   void this.achievementsService.addAmbassadorAchievement();
  //   return;
  // }

  // @Get('run-beta-addAmbassadorAchievement')
  // runAddAmbassadorAchievementBeta(): Promise<void> {
  //   try {
  //     if (config.NODE_ENV === 'development' || config.SERVICE === 'api-beta') {
  //       void this.achievementsService.addAmbassadorAchievement();
  //       return;
  //     } else {
  //       throw new Error('only beta env allowed');
  //     }
  //   } catch (err) {
  //     this.errorHelper.throwHttpException(
  //       `AchievementsService.runAddAmbassadorAchievementBeta`,
  //       err.message,
  //     );
  //   }
  // }
}
