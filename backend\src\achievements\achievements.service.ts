import { forwardRef, Inject, Injectable } from '@nestjs/common';
import moment from 'moment-timezone';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Sequelize } from 'sequelize-typescript';
import { Logger } from 'winston';
import { BaseService } from '../common/base.service';
import { <PERSON>rror<PERSON>elper } from '../common/helpers/error';
import {
  encodePathParams,
  Routes,
} from '../email/helpers/notificationLayoutHelper';
import {
  NotificationMessage,
  NotificationType,
} from '../notifications/args/notifications.args';
import { NotificationsService } from '../notifications/notifications.service';
import { ProfilesService } from '../profiles/profiles.service';
import { AchievementName, AchievementType } from './args/achievements.args';
import { RunAchievementMigrationsInput } from './args/run-achievement-migrations-input';
import { Achievement } from './models/achievements.model';
import { Op } from 'sequelize';
import { WebinarsService } from '../webinars/webinars.service';
import { PostsService } from '../posts/posts.service';
import { OrganisationsService } from '../organisations/organisations.service';
import { CreateActivityInput } from '../activities/dto/create-activity.input';
import { FollowersService } from '../followers/followers.service';
import { ActivitiesService } from '../activities/activities.service';
import { ActivityType } from '../activities/args/activities.args';
import { floor } from 'lodash';
import { Underscore as _ } from '../common/helpers/underscore';
import {
  emptyActivityDataDto,
  highFiveActivityDataDto,
} from '../activities/dto/create-user-activity-data.dto';
import { FollowerStatus } from '../followers/models/follower.model';
import { Organisation } from '../organisations/models/organisation.model';
import { ICurrentUser } from '../common/decorators/current-user.decorator';
import { Post } from '../posts/models/post.model';
import { profile } from 'console';

@Injectable()
export class AchievementsService extends BaseService(Achievement) {
  @Inject()
  private readonly sequelize: Sequelize;
  @Inject(WINSTON_MODULE_PROVIDER)
  private readonly logger: Logger;
  @Inject(forwardRef(() => ProfilesService))
  private readonly profilesService: ProfilesService;
  @Inject()
  private readonly errorHelper: ErrorHelper;
  @Inject(forwardRef(() => NotificationsService))
  private readonly notificationsService: NotificationsService;
  @Inject(forwardRef(() => WebinarsService))
  private readonly webinarsService: WebinarsService;
  @Inject(forwardRef(() => OrganisationsService))
  private readonly organisationsService: OrganisationsService;
  @Inject(forwardRef(() => FollowersService))
  private readonly followersService: FollowersService;
  @Inject(forwardRef(() => PostsService))
  private readonly postsService: PostsService;
  @Inject(forwardRef(() => ActivitiesService))
  private readonly activitiesService: ActivitiesService;

  async getAchievements({
    profileId,
  }: {
    profileId: string;
  }): Promise<Achievement[]> {
    try {
      this.logger.verbose('AchievementsResolver.getAchievements', {
        profileId,
      });

      return await this.findAll(
        {
          profileId,
          organisationId: null,
        },
        {
          includeParams: [
            {
              model: Organisation,
              as: 'organisation',
              attributes: ['name', 'id', 'vanityId', 'image'],
            },
          ],
          order: [['createdAt', 'DESC']],
        },
      );
    } catch (error) {
      this.errorHelper.throwHttpException(
        'AchievementsService.getAchievements',
        error.message,
      );
    }
  }

  async getOrgAchievements({
    profileId,
  }: {
    profileId: string;
  }): Promise<Achievement[]> {
    try {
      this.logger.verbose('AchievementsResolver.getOrgAchievements', {
        profileId,
      });

      const achievements = await this.findAll(
        {
          profileId,
          organisationId: {
            [Op.ne]: null,
          },
        },
        {
          includeParams: [
            {
              model: Organisation,
              as: 'organisation',
              attributes: ['name', 'id', 'vanityId', 'image'],
            },
          ],
          order: [['createdAt', 'DESC']],
        },
      );

      const levelMapping = {
        AvidViewer: [
          { steps: 175, level: 9 },
          { steps: 150, level: 8 },
          { steps: 125, level: 7 },
          { steps: 100, level: 6 },
          { steps: 75, level: 5 },
          { steps: 50, level: 4 },
          { steps: 25, level: 3 },
          { steps: 10, level: 2 },
          { steps: 5, level: 1 },
        ],
        HighFive: [
          { steps: 100, level: 10 },
          { steps: 50, level: 9 },
          { steps: 35, level: 8 },
          { steps: 25, level: 7 },
          { steps: 20, level: 6 },
          { steps: 15, level: 5 },
          { steps: 10, level: 4 },
          { steps: 5, level: 3 },
          { steps: 3, level: 2 },
          { steps: 1, level: 1 },
        ],
      };

      return achievements.map(achievement => {
        let level = 0;

        if (
          achievement.type === AchievementType.AvidViewer ||
          achievement.type === AchievementType.HighFive
        ) {
          level =
            levelMapping[achievement.type].find(
              ({ steps }) => achievement.stepsComplete >= steps,
            )?.level || null;
        } else if (achievement.type === AchievementType.IncentiveWarrior) {
          level = achievement.stepsComplete;
        } else if (
          achievement.type === AchievementType.Ambassador ||
          achievement.type === AchievementType.PowerScroller
        ) {
          level = achievement.number;
        } else {
          level = null;
        }

        return {
          ...achievement.toJSON(),
          level,
        } as Achievement & { level: number };
      });
    } catch (error) {
      this.errorHelper.throwHttpException(
        'AchievementsService.getOrgAchievements',
        error.message,
      );
    }
  }

  async runAchievementMigrations({
    profileId,
    data,
  }: {
    profileId: string;
    data: RunAchievementMigrationsInput;
  }): Promise<void> {
    this.logger.verbose('AchievementsResolver.runAchievementMigrations', {
      profileId,
      data,
    });

    try {
      const migrationFlags = {};

      const migrations = data.migrations;
      for (const migration of migrations) {
        this.logger.verbose(`Running migration: ${migration}`);

        switch (migration) {
          case 'profile-perfect':
            await this.addProfilePerfectAchievement({
              profileId,
            });
            migrationFlags['profilePerfectMigration'] = true;
            break;
          case 'hablo-founder':
            await this.addHabloFounderAchievement({
              profileId,
            });
            migrationFlags['habloFounderMigration'] = true;
            break;
          default:
            this.logger.warn(`Unknown migration: ${migration}`);
            break;
        }
      }

      await this.profilesService.updateById(profileId, {
        migrationFlags,
      });
    } catch (error) {
      this.errorHelper.throwHttpException(
        'AchievementsService.runAchievementMigrations',
        error.message,
      );
    }
  }

  async sendExistingAchievementsNotifications({
    profileId,
  }: {
    profileId: string;
  }): Promise<boolean> {
    try {
      const achievementNotifications = await this.notificationsService.count({
        ownerProfileId: profileId,
        type: NotificationType.NewAchievement,
      });

      if (achievementNotifications) {
        this.logger.info(
          'AchievementsService.sendExistingAchievementsNotifications',
          {
            profileId,
            message: 'Achievement notifications already sent.',
          },
        );
        return true;
      }

      /**
       * Find achievements for the profile where if there is a value for number check if stepsComplete is more or equal to number.
       * If number is null, check if stepsComplete is more than or equal to steps.
       */
      const achievements = await this.findAll({
        profileId,
        [Op.or]: [
          {
            number: {
              [Op.not]: null,
              [Op.lte]: Sequelize.col('stepsComplete'),
            },
          },
          {
            number: null,
            stepsComplete: {
              [Op.gte]: Sequelize.col('steps'),
            },
          },
        ],
      });

      for (const achievement of achievements) {
        await this.sendNotification({
          achievementType: AchievementName[achievement.type],
          profileId,
        });
      }

      return true;
    } catch (error) {
      this.logger.error(
        'AchievementsService.sendExistingAchievementsNotifications',
        {
          profileId,
          error: error.message,
        },
      );
    }
  }

  async addProfilePerfectAchievement({
    profileId,
  }: {
    profileId: string;
  }): Promise<Achievement | null> {
    this.logger.debug('AchievementsService.addProfilePerfectAchievement', {
      profileId,
    });

    try {
      const profile = await this.profilesService.findById(profileId);
      if (!profile) {
        this.logger.error(`Profile not found for id ${profileId}`);
        throw new Error('Profile not found.');
      }

      /**
        - has profile picture
        - has cover photo
        - has headline
        - bio has more than 30 words
        - has at least one experience
      */
      let completedSteps = 0;
      if (profile.image && !profile.image.includes('gravatar.com')) {
        completedSteps++;
      }
      if (profile.backgroundImage) {
        completedSteps++;
      }
      if (profile.headline) {
        completedSteps++;
      }
      if (profile.bio && profile.bio.split(' ').length > 30) {
        completedSteps++;
      }
      if (profile.primaryMembershipId) {
        completedSteps++;
      }

      const achievement = await this.findOne(
        {
          type: AchievementType.ProfilePerfect,
          profileId,
        },
        {
          order: [['createdAt', 'DESC']],
        },
      );

      if (achievement) {
        if (achievement.stepsComplete === completedSteps) {
          this.logger.info('ProfilePerfect achievement already up to date.');
          return achievement;
        }

        let updatedAchievement = await this.updateById(achievement.id, {
          stepsComplete: completedSteps,
        });

        if (completedSteps === 5 && profile.sellHolidays) {
          updatedAchievement = await this.updateById(achievement.id, {
            isAchieved: true,
            achievedDate: new Date(),
          });
          await this.activitiesService.createUserActivity({
            type: ActivityType.ProfileCompletion,
            schema: emptyActivityDataDto,
            data: {},
            profileId,
            addLoyaltyPoints: true,
            checkForUniqueEntry: true,
          });
          await this.sendNotification({
            achievementType: AchievementName.ProfilePerfect,
            profileId,
          });
        }

        return updatedAchievement;
      }

      if (!completedSteps) {
        this.logger.info('ProfilePerfect achievement not completed.');
        return null;
      }

      const newAchievement = await this.create({
        type: AchievementType.ProfilePerfect,
        steps: 5,
        stepsComplete: completedSteps,
        profileId,
        achievedDate: completedSteps === 5 ? new Date() : null,
      });

      if (completedSteps === 5 && profile.sellHolidays) {
        await this.activitiesService.createUserActivity({
          type: ActivityType.ProfileCompletion,
          schema: emptyActivityDataDto,
          data: {},
          profileId,
          addLoyaltyPoints: true,
          checkForUniqueEntry: true,
        });
        await this.sendNotification({
          achievementType: AchievementName.ProfilePerfect,
          profileId,
        });
      }

      return newAchievement;
    } catch (error) {
      this.errorHelper.throwHttpException(
        'AchievementsService.addProfilePerfectAchievement',
        error.message,
      );
    }
  }

  async addHabloFounderAchievement({
    profileId,
  }: {
    profileId: string;
  }): Promise<Achievement | null> {
    this.logger.debug('AchievementsService.addHabloFounderAchievement', {
      profileId,
    });

    try {
      const achievement = await this.findOne({
        type: AchievementType.HabloFounder,
        profileId,
      });

      if (achievement) {
        this.logger.info('HabloFounder achievement already exists.');
        return achievement;
      }

      const profile = await this.profilesService.findById(profileId);
      if (!profile) {
        this.logger.error(`Profile not found for id ${profileId}`);
        throw new Error('Profile not found.');
      }

      const timezone = profile.timezone || 'UTC';
      const profileCreatedAt = moment(profile.createdAt).tz(timezone);
      const habloLaunchDate = moment('2023-01-01').tz(timezone);

      if (profileCreatedAt.isAfter(habloLaunchDate)) {
        this.logger.info('Profile created after Hablo launch date.');
        return null;
      }

      const newAchievement = await this.create({
        type: AchievementType.HabloFounder,
        steps: 1,
        stepsComplete: 1,
        profileId,
        isAchieved: true,
        achievedDate: new Date(),
      });

      if (profile.sellHolidays) {
        await this.sendNotification({
          achievementType: AchievementName.HabloFounder,
          profileId,
        });
      }

      return newAchievement;
    } catch (error) {
      this.errorHelper.throwHttpException(
        'AchievementsService.addHabloFounderAchievement',
        error.message,
      );
    }
  }

  async addDailyQuizStreakAchievement({
    profileId,
  }: {
    profileId: string;
  }): Promise<Achievement> {
    this.logger.debug('AchievementsService.addDailyQuizStreakAchievement', {
      profileId,
    });

    try {
      const achievement = await this.findOne({
        type: AchievementType.DailyQuizStreak,
        profileId,
      });

      if (!achievement) {
        return await this.create({
          type: AchievementType.DailyQuizStreak,
          steps: 5, // Total required quizzes per week
          stepsComplete: 1, // First quiz attempt of the week
          profileId,
          number: 0, // Streak count starts at 0
          achievedDate: null,
        });
      }

      const profile = await this.profilesService.findById(profileId);
      if (!profile) {
        this.logger.error(`Profile not found for id ${profileId}`);
        throw new Error('Profile not found.');
      }
      const timezone = profile.timezone || 'UTC';
      const now = moment.tz(timezone);
      const today = now.startOf('day');
      const lastUpdatedDay = moment(achievement.updatedAt)
        .tz(timezone)
        .startOf('day');

      // If today's quiz is already counted, don't increase stepsComplete again
      if (
        today.isSame(lastUpdatedDay, 'day') &&
        achievement.stepsComplete !== 0
      ) {
        this.logger.info(
          'User has already completed today’s quiz. No update needed.',
        );
        return achievement;
      }

      // Ignore weekends
      if (now.day() === 0 || now.day() === 6) {
        this.logger.info('No progress updates on weekends.');
        return achievement;
      }

      // If user skips a day, reset progress for the week
      if (today.diff(lastUpdatedDay, 'days') > 1) {
        this.logger.info('DailyQuizStreak achievement streak broken.');
        return await this.updateById(achievement.id, {
          stepsComplete: 1,
        });
      }

      const newStepsComplete = achievement.stepsComplete + 1;
      let updatedAchievement = await this.updateById(achievement.id, {
        stepsComplete: newStepsComplete,
      });

      // If all 5 quizzes (Mon–Fri) are completed, increase streak
      if (newStepsComplete === 5) {
        updatedAchievement = await this.updateById(achievement.id, {
          number: achievement.number + 1, // Increase streak count
          achievedDate: new Date(),
          isAchieved: true,
        });

        if (profile.sellHolidays) {
          await this.sendNotification({
            achievementType: AchievementName.DailyQuizStreak,
            profileId,
          });
        }
      }

      return updatedAchievement;
    } catch (error) {
      this.errorHelper.throwHttpException(
        'AchievementsService.addDailyQuizStreakAchievement',
        error.message,
      );
    }
  }

  async addHotStreakAchievement({
    profileId,
  }: {
    profileId: string;
  }): Promise<Achievement> {
    this.logger.debug('AchievementsService.addHotStreakAchievement', {
      profileId,
    });

    try {
      const achievement = await this.findOne({
        type: AchievementType.HotStreak,
        profileId,
      });

      if (!achievement) {
        const newAchievement = await this.create({
          type: AchievementType.HotStreak,
          steps: 1,
          stepsComplete: 1,
          profileId,
          number: 5,
          achievedDate: null,
        });

        return newAchievement;
      }

      const profile = await this.profilesService.findById(profileId);
      if (!profile) {
        throw new Error('Profile not found.');
      }
      const timezone = profile.timezone || 'UTC';

      const lastEntry = moment(achievement.updatedAt)
        .tz(timezone)
        .startOf('day');
      const today = moment.tz(timezone).startOf('day');

      if (today.isSame(lastEntry, 'day')) {
        return achievement;
      }

      // Streak only increments when user is active daily
      if (today.diff(lastEntry, 'days') === 1) {
        const newStepsComplete = achievement.stepsComplete + 1;

        // Handles the user beating their old hot streak or getting their first hot streak of 5
        // (achievement.number starts as 5 when first created)
        if (
          newStepsComplete > achievement.number ||
          (newStepsComplete === 5 && achievement.number === 5)
        ) {
          await this.updateById(achievement.id, {
            stepsComplete: newStepsComplete,
            number: newStepsComplete,
            isAchieved: true,
            achievedDate: new Date(),
          });
        } else {
          //user increased their streak but hasn't beaten their old one
          await this.updateById(achievement.id, {
            stepsComplete: newStepsComplete,
          });
        }

        if (newStepsComplete === achievement.number && profile.sellHolidays) {
          await this.sendNotification({
            achievementType: AchievementName.HotStreak,
            profileId,
          });
        }

        return achievement;
      }

      // Streak breaks if they miss any day
      if (today.diff(lastEntry, 'days') > 1) {
        return await this.updateById(achievement.id, {
          stepsComplete: 1,
        });
      }

      return achievement;
    } catch (error) {
      this.errorHelper.throwHttpException(
        'AchievementsService.addHotStreakAchievement',
        error.message,
      );
    }
  }

  async addCommunityBeaconAchievement({
    profileId,
  }: {
    profileId: string;
  }): Promise<Achievement> {
    this.logger.debug('AchievementsService.addCommunityBeaconAchievement', {
      profileId,
    });

    try {
      const achievement = await this.findOne({
        type: AchievementType.CommunityBeacon,
        profileId,
      });

      if (!achievement) {
        const newAchievement = await this.create({
          type: AchievementType.CommunityBeacon,
          steps: 0,
          stepsComplete: 0,
          profileId,
          number: 5,
          achievedDate: null,
        });

        return newAchievement;
      }

      const profile = await this.profilesService.findById(profileId);
      if (!profile) {
        throw new Error('Profile not found.');
      }

      const ConnectionSequence = [5, 10, 25, 50, 100, 150, 250, 500, 1000];

      // Adjusted streak continuation logic to require login every day
      if (profile.connectionsCount) {
        const newStepsComplete = profile.connectionsCount + 1;

        // Check if newStepsComplete is 5 or more to set isAchieved to true
        const isAchieved = newStepsComplete >= 5;

        if (ConnectionSequence.includes(newStepsComplete)) {
          const updatedAchievement = await this.updateById(achievement.id, {
            stepsComplete: newStepsComplete,
            number: newStepsComplete,
            isAchieved,
            achievedDate: new Date(),
          });
          await this.sendNotification({
            achievementType: AchievementName.CommunityBeacon,
            profileId,
          });
          return updatedAchievement;
        }

        const updatedAchievement = await this.updateById(achievement.id, {
          stepsComplete: newStepsComplete,
          isAchieved,
        });

        return updatedAchievement;
      }

      return achievement;
    } catch (error) {
      this.errorHelper.throwHttpException(
        'AchievementsService.addCommunityBeaconAchievement',
        error.message,
      );
    }
  }

  async upsertIndustryInfluencerAchievement({
    profileId,
  }: {
    profileId: string;
  }): Promise<Achievement> {
    this.logger.debug(
      'AchievementsService.upsertIndustryInfluencerAchievement',
      {
        profileId,
      },
    );

    try {
      const achievement = await this.findOne({
        type: AchievementType.IndustryInfluencer,
        profileId,
      });

      if (!achievement) {
        const newAchievement = await this.create({
          type: AchievementType.IndustryInfluencer,
          steps: 10,
          stepsComplete: 1,
          profileId,
          achievedDate: null,
        });
        // Send notification after creating a new achievement
        await this.sendNotification({
          achievementType: AchievementName.IndustryInfluencer,
          profileId,
        });
        return newAchievement;
      } else {
        if (achievement.stepsComplete < achievement.steps) {
          const updatedAchievement = await this.updateById(achievement.id, {
            stepsComplete: achievement.stepsComplete + 1,
            isAchieved:
              achievement.steps === achievement.stepsComplete + 1
                ? true
                : false,
            achievedDate:
              achievement.steps === achievement.stepsComplete + 1
                ? new Date()
                : achievement.achievedDate,
          });
          // Send notification after updating the achievement
          if (updatedAchievement.isAchieved) {
            await this.sendNotification({
              achievementType: AchievementName.IndustryInfluencer,
              profileId,
            });
          }
          return updatedAchievement; // Return the updated achievement
        }
      }

      return achievement;
    } catch (error) {
      this.errorHelper.throwHttpException(
        'AchievementsService.addIndustryInfluencerAchievement',
        error.message,
      );
    }
  }

  async addWellbeingBadgeAchievement(
    weekStartDate?: Date,
  ): Promise<Achievement | null> {
    this.logger.debug('AchievementsService.addWellbeingBadgeAchievement');
    console.log('weekStartDate', weekStartDate);
    try {
      const staticOrganisation = await this.organisationsService.findOne(
        {
          name: 'Hablo Wellbeing | 𝐈𝐧𝐬𝐩𝐢𝐫𝐞𝐝 𝐛𝐲 𝐌𝐚𝐮𝐫𝐢𝐭𝐢𝐮𝐬',
        },
        {
          attributes: ['id'],
        },
      );

      const followers = await this.followersService.findAll(
        {
          organisationId: staticOrganisation.id,
        },
        {
          attributes: ['profileId'],
        },
      );

      const profileIds = _.uniq(_.map(followers, 'profileId'));

      let previousWeekStart;
      let previousWeekEnd;
      if (!weekStartDate) {
        previousWeekStart = moment()
          .subtract(1, 'week')
          .startOf('isoWeek')
          .toDate();
        previousWeekEnd = moment()
          .subtract(1, 'week')
          .endOf('isoWeek')
          .toDate();
      } else {
        // Use weekStartDate if provided, otherwise default to previous logic
        previousWeekStart = weekStartDate
          ? moment(weekStartDate).startOf('isoWeek').toDate()
          : moment().subtract(1, 'week').startOf('isoWeek').toDate();
        previousWeekEnd = weekStartDate
          ? moment(weekStartDate).endOf('isoWeek').toDate()
          : moment().subtract(1, 'week').endOf('isoWeek').toDate();
      }

      const webinars = await this.webinarsService.findAll(
        {
          organisationId: staticOrganisation.id,
          createdAt: {
            [Op.between]: [previousWeekStart, previousWeekEnd],
          },
        },
        {
          attributes: ['id'],
        },
      );

      // Extract webinar IDs from webinarData
      const webinarIds = _.uniq(_.map(webinars, 'id'));

      const posts = await this.postsService.findAll(
        {
          organisationId: staticOrganisation.id,
          createdAt: {
            [Op.between]: [previousWeekStart, previousWeekEnd],
          },
        },
        {
          attributes: ['id'],
        },
      );

      // Extract post IDs from webinarData
      const postIds = _.uniq(_.map(posts, 'id'));

      if (webinarIds.length === 0 && postIds.length === 0) {
        return null;
      }

      // Sequentially process followers to avoid DB connection pool exhaustion
      for (const follower of profileIds) {
        // Check if the follower has completed all webinars
        let webinarActivityData;
        if (webinarIds.length) {
          webinarActivityData = await this.activitiesService.findAll(
            {
              profileId: follower,
              type: ActivityType.WebinarCompleted,
              webinarId: {
                [Op.in]: webinarIds,
              },
            },
            {
              attributes: ['webinarId'],
            },
          );

          const webinarUniqueIds = _.uniq(
            _.map(webinarActivityData, 'webinarId'),
          );

          if (webinarUniqueIds?.length !== webinarIds.length) {
            continue; // Skip to the next follower
          }
        }

        // Check if the follower has viewed all posts
        let postActivityData;
        if (postIds.length) {
          postActivityData = await this.activitiesService.findAll(
            {
              profileId: follower,
              type: ActivityType.PostView,
              postId: {
                [Op.in]: postIds,
              },
            },
            {
              attributes: ['postId'],
            },
          );

          const postUniqueIds = _.uniq(_.map(postActivityData, 'postId'));

          if (postUniqueIds?.length !== postIds.length) {
            continue; // Skip to the next follower
          }
        }

        const achievement = await this.findOne({
          type: AchievementType.WellbeingBadge,
          profileId: follower,
        });

        // Award badge logic
        if (!achievement) {
          await this.create({
            type: AchievementType.WellbeingBadge,
            steps: 1,
            stepsComplete: 1,
            number: 1,
            profileId: follower,
            isAchieved: true,
            achievedDate: new Date(),
          });

          await this.sendNotification({
            achievementType: AchievementName.WellbeingBadge,
            profileId: follower,
          });
        } else {
          await this.updateById(achievement.id, {
            stepsComplete: achievement.stepsComplete + 1,
            number: achievement.stepsComplete + 1,
            isAchieved: true,
            achievedDate: new Date(),
          });
        }
      }

      return null;
    } catch (error) {
      console.error('error-----------', error);
      this.errorHelper.throwHttpException(
        'AchievementsService.addWellbeingBadgeAchievement',
        error.message,
      );
    }
  }

  async addPowerScrollerAchievement(): Promise<void> {
    this.logger.debug(
      'AchievementsService.addPowerScrollerAchievement started',
    );

    try {
      const batchSize = 3000;
      let offset = 0;
      let batchNumber = 1;
      let hasMoreRecords = true;

      const totalCount = await this.count({
        type: AchievementType.PowerScroller,
      });

      this.logger.info(
        `Total PowerScroller achievements to process: ${totalCount}`,
      );

      while (hasMoreRecords) {
        this.logger.info(
          `Processing batch ${batchNumber} (offset: ${offset}, limit: ${batchSize})`,
        );

        // Use model directly instead of BaseService for pagination
        const achievements = await Achievement.findAll({
          where: {
            type: AchievementType.PowerScroller,
          },
          offset,
          limit: batchSize,
          order: [['id', 'ASC']],
          include: [
            {
              model: Organisation,
              as: 'organisation',
            },
          ],
        });

        if (achievements.length === 0) {
          this.logger.info(
            `No more records found. Stopping migration at batch ${batchNumber}`,
          );
          hasMoreRecords = false;
          break;
        }

        for (const achievement of achievements) {
          try {
            // Check if steps and stepsComplete are the same
            if (
              achievement.steps === achievement.stepsComplete &&
              achievement.steps > 0
            ) {
              // Update the achievement to mark it as achieved
              await this.updateById(achievement.id, {
                number: achievement.number + 1,
                isAchieved: true,
                achievedDate: new Date(),
              });

              const organisation = achievement.organisation;
              if (organisation) {
                // Send notification for the achievement
                await this.sendNotification({
                  achievementType: AchievementName.PowerScroller,
                  profileId: achievement.profileId,
                  organisationDetails: {
                    id: organisation.id,
                    name: organisation.name,
                  },
                });
              }
            } else {
              await this.updateById(achievement.id, {
                isAchieved: false,
              });
            }

            // Reset steps and stepsComplete to 0 for the next month
            await this.updateById(achievement.id, {
              stepsComplete: 0,
              steps: 0,
            });
          } catch (err) {
            this.logger.error(
              `Error processing achievement ${achievement.id}:`,
              err,
            );
            // Continue with next achievement
            continue;
          }
        }

        await new Promise(resolve => setTimeout(resolve, 100));

        offset += batchSize;
        batchNumber++;
      }

      this.logger.info(
        'PowerScroller achievement processing completed successfully',
      );
    } catch (error) {
      this.logger.error(
        'Error in PowerScroller achievement processing:',
        error,
      );
      this.errorHelper.throwHttpException(
        'AchievementsService.addPowerScrollerAchievement',
        error.message,
      );
    }
  }

  // async runMissingPowerScrollerMigration(): Promise<void> {
  //   this.logger.debug('AchievementsService.runPowerScrollerMigration');

  //   try {
  //     const batchSize = 3000;
  //     let offset = 0;
  //     let batchNumber = 1;
  //     let hasMoreRecords = true;

  //     const totalCount = await this.count({
  //       type: AchievementType.PowerScroller,
  //     });

  //     this.logger.info(
  //       `Total PowerScroller achievements to migrate: ${totalCount}`,
  //     );

  //     while (hasMoreRecords) {
  //       this.logger.info(
  //         `Processing migration batch ${batchNumber} (offset: ${offset}, limit: ${batchSize})`,
  //       );

  //       // Use model directly instead of BaseService for pagination
  //       const achievements = await Achievement.findAll({
  //         where: {
  //           type: AchievementType.PowerScroller,
  //         },
  //         offset,
  //         limit: batchSize,
  //         order: [['id', 'ASC']],
  //         include: [
  //           {
  //             model: Organisation,
  //             as: 'organisation',
  //           },
  //         ],
  //       });

  //       if (achievements.length === 0) {
  //         this.logger.info(
  //           `No more records found. Stopping migration at batch ${batchNumber}`,
  //         );
  //         hasMoreRecords = false;
  //         break;
  //       }

  //       for (const achievement of achievements) {
  //         try {
  //           if (
  //             achievement.steps === achievement.stepsComplete &&
  //             achievement.stepsComplete > 0
  //           ) {
  //             await this.updateById(achievement.id, {
  //               number: achievement.number + 1,
  //               isAchieved: true,
  //               achievedDate: new Date(),
  //             });

  //             const organisation = achievement.organisation;
  //             if (!organisation) {
  //               continue;
  //             }
  //           } else {
  //             await this.updateById(achievement.id, {
  //               isAchieved: false,
  //             });
  //           }
  //         } catch (err) {
  //           this.logger.error(
  //             `Error processing migration for achievement ${achievement.id}:`,
  //             err,
  //           );
  //           continue;
  //         }
  //       }

  //       await new Promise(resolve => setTimeout(resolve, 100));

  //       offset += batchSize;
  //       batchNumber++;
  //     }

  //     this.logger.info('PowerScroller migration completed successfully');
  //   } catch (error) {
  //     this.logger.error('Error in Missing PowerScroller migration:', error);
  //     this.errorHelper.throwHttpException(
  //       'AchievementsService.runPowerScrollerMigration',
  //       error.message,
  //     );
  //   }
  // }

  async addAvidViewerAchievement({
    profileId,
    organisationId,
  }): Promise<Achievement> {
    this.logger.debug('AchievementsService.addAvidViewerAchievement', {
      profileId,
      organisationId,
    });
    try {
      const organisation = await this.organisationsService.findOne({
        id: organisationId,
        hasClubHabloSubscription: true,
      });

      if (!organisation) {
        return;
      }

      const followerDetails = await this.followersService.findOne({
        profileId,
        organisationId,
        status: FollowerStatus.Active,
      });

      if (!followerDetails) {
        return;
      }

      const achievement = await this.findOne({
        type: AchievementType.AvidViewer,
        profileId,
        organisationId,
      });

      if (!achievement) {
        this.logger.info('Avid Viewer achievement not found.');

        const newAchievement = await this.create({
          type: AchievementType.AvidViewer,
          steps: 5,
          stepsComplete: 1,
          profileId,
          organisationId,
          number: 5,
          achievedDate: null,
        });

        return newAchievement;
      }

      const webinarViewSequence = [5, 10, 25, 50, 75, 100, 125, 150, 175];

      this.logger.info('Avid Viewer achievement streak continued.');

      const newStepsComplete = achievement.stepsComplete + 1;

      const index = webinarViewSequence.indexOf(newStepsComplete);
      const nextNumber =
        index !== -1 && index < webinarViewSequence.length - 1
          ? webinarViewSequence[index + 1]
          : newStepsComplete; // Keep the same if it's the last in the sequence

      // Check if newStepsComplete is 5 or more to set isAchieved to true
      const isAchieved = newStepsComplete >= 5;

      if (webinarViewSequence.includes(newStepsComplete)) {
        const updatedAchievement = await this.updateById(achievement.id, {
          steps: nextNumber,
          stepsComplete: newStepsComplete,
          number: newStepsComplete,
          isAchieved,
          achievedDate: new Date(),
        });
        await this.sendNotification({
          achievementType: AchievementName.AvidViewer,
          profileId,
          organisationDetails: {
            id: organisation.id,
            name: organisation.name,
          },
        });
        return updatedAchievement;
      }

      const updatedAchievement = await this.updateById(achievement.id, {
        stepsComplete: newStepsComplete,
        isAchieved,
      });
      return updatedAchievement;
    } catch (error) {
      this.errorHelper.throwHttpException(
        'AchievementsService.addAvidViewerAchievement',
        error.message,
      );
    }
  }

  async incrementStepsComplete({
    profileId,
    organisationId,
    postId,
  }: {
    profileId: string;
    organisationId: string;
    postId: string;
  }): Promise<void> {
    try {
      // Check if the organisation is paid
      const organisation = await this.organisationsService.findById(
        organisationId,
      );

      if (!organisation || !organisation.hasClubHabloSubscription) {
        return; // Exit if the organisation is not paid
      }

      // Check if the user follows the organisation
      const follower = await this.followersService.findOne({
        profileId,
        organisationId,
        status: FollowerStatus.Active,
      });
      if (!follower) {
        return; // Exit if the user does not follow the organisation
      }

      let achievement = await this.findOne({
        profileId,
        organisationId,
        type: AchievementType.PowerScroller,
      });

      const currentMonthPosts = await this.postsService.getCurrentMonthPosts(
        organisation.id,
        moment().endOf('month').subtract(3, 'days').toDate(),
      );

      if (!achievement) {
        achievement = await this.create({
          type: AchievementType.PowerScroller,
          profileId,
          organisationId,
          steps: currentMonthPosts.length,
          stepsComplete: 0,
          isAchieved: false,
          achievedDate: null,
          number: 0,
        });
      }

      const post = await this.postsService.findById(postId);
      if (!post) {
        return; // Exit if the post does not exist
      }

      const postCreatedAt = moment(post?.scheduledAt || post.createdAt);
      const isPostCreatedThisMonth = postCreatedAt.isSame(moment(), 'month');

      if (isPostCreatedThisMonth) {
        if (achievement.stepsComplete < achievement.steps) {
          await this.updateById(achievement.id, {
            steps: currentMonthPosts.length,
            stepsComplete: achievement.stepsComplete + 1,
          });
        }
      }
    } catch (error) {
      this.logger.error('Error finding achievement', {
        error: error.message,
        profileId,
        organisationId,
      });
    }
  }

  async decrementStepsComplete({
    achievement,
    post,
  }: {
    achievement: Achievement;
    post: Post;
  }): Promise<void> {
    try {
      const startOfMonth = moment().startOf('month').toDate();
      const endOfMonth = moment().endOf('month').toDate();
      const monthEndThreeDaysAgo = moment()
        .endOf('month')
        .subtract(3, 'days')
        .toDate();
      const { profileId, organisationId } = achievement;

      const [activities, currentMonthPost] = await Promise.all([
        this.activitiesService.findAll({
          organisationId,
          profileId,
          type: ActivityType.PostView,
          createdAt: {
            [Op.between]: [startOfMonth, endOfMonth],
          },
        }),
        this.postsService.getCurrentMonthPosts(
          organisationId,
          monthEndThreeDaysAgo,
        ),
      ]);

      const uniquePostIds = _.uniq(activities.map(activity => activity.postId));
      const currentMonthPostIds = new Set(currentMonthPost);
      const viewedAndCreatedPostIds = Array.from(uniquePostIds).filter(postId =>
        currentMonthPostIds.has(postId),
      );

      const isCurrentPostViewedAndCreated = viewedAndCreatedPostIds.includes(
        post.id,
      );
      const isCurrentPostInMonth = currentMonthPostIds.has(post.id);

      const uniquePostViewCount =
        viewedAndCreatedPostIds.length -
        (isCurrentPostViewedAndCreated ? 1 : 0);
      const steps = currentMonthPost.length - (isCurrentPostInMonth ? 1 : 0);

      await this.updateById(achievement.id, {
        stepsComplete: uniquePostViewCount,
        steps,
      });
    } catch (error) {
      this.logger.error('Error decrementing stepsComplete', {
        error: error.message,
        achievement: achievement.id,
      });
    }
  }

  async addIncentiveWarriorAchievement({
    profileId,
    organisationId,
  }): Promise<Achievement> {
    this.logger.debug('AchievementsService.addIncentiveWarriorAchievement', {
      profileId,
      organisationId,
    });
    try {
      const organisation = await this.organisationsService.findOne({
        id: organisationId,
        hasClubHabloSubscription: true,
      });

      if (!organisation) {
        return;
      }

      const followerDetails = await this.followersService.findOne({
        profileId,
        organisationId,
        status: FollowerStatus.Active,
      });
      if (!followerDetails) {
        return;
      }

      let achievement = await this.findOne({
        type: AchievementType.IncentiveWarrior,
        profileId,
        organisationId,
      });

      if (!achievement) {
        achievement = await this.create({
          type: AchievementType.IncentiveWarrior,
          steps: 1,
          stepsComplete: 1,
          profileId,
          organisationId,
          number: 1,
          isAchieved: true,
          achievedDate: new Date(),
        });
      } else if (achievement) {
        await this.updateById(achievement.id, {
          stepsComplete: achievement.stepsComplete + 1,
          number: achievement.stepsComplete + 1,
          achievedDate: new Date(),
        });
      }

      await this.sendNotification({
        achievementType: AchievementName.IncentiveWarrior,
        profileId,
        organisationDetails: {
          id: organisation.id,
          name: organisation.name,
        },
      });

      return achievement;
    } catch (error) {
      this.errorHelper.throwHttpException(
        'AchievementsService.addIncentiveWarriorAchievement',
        error.message,
      );
    }
  }

  async addAmbassadorAchievement(): Promise<Achievement[]> {
    try {
      const allAchievements = (await this.findAll()).filter(
        achievement => achievement.organisationId !== null,
      );
      // Group achievements by profileId and organisationId
      const groupedAchievements = allAchievements.reduce((acc, achievement) => {
        const key = `${achievement.profileId}-${achievement.organisationId}`;
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(achievement);
        return acc;
      }, {});

      const achievementTypes = [
        AchievementType.AvidViewer,
        AchievementType.PowerScroller,
        AchievementType.IncentiveWarrior,
      ];

      // Process each group concurrently using Promise.all
      await Promise.all(
        Object.keys(groupedAchievements).map(async key => {
          const achievements = groupedAchievements[key];
          const [profileId, organisationId] = key.split('-');

          // Check if all 3 achievement types are present
          const groupedTypes = achievements.map(ach => ach.type);

          const missingTypes = achievementTypes.filter(
            type => !groupedTypes.includes(type),
          );

          if (missingTypes.length === 0) {
            // If all 3 achievement types are present, check the isAchieved flag
            const allAchieved = achievementTypes.every(type => {
              const achievement = achievements.find(ach => ach.type === type);
              return achievement && achievement.isAchieved;
            });

            // Check if Ambassador achievement exists
            const ambassador = await this.findOne({
              type: AchievementType.Ambassador,
              profileId,
              organisationId,
            });

            const organisation = await this.organisationsService.findById(
              organisationId,
            );
            if (!organisation) {
              return;
            }

            if (!ambassador) {
              await this.create({
                type: AchievementType.Ambassador,
                profileId,
                organisationId,
                isAchieved: allAchieved,
                achievedDate: allAchieved ? new Date() : null,
                steps: 1,
                stepsComplete: allAchieved ? 1 : 0,
                number: 1,
              });
              if (allAchieved) {
                await this.sendNotification({
                  achievementType: AchievementName.Ambassador,
                  profileId,
                  organisationDetails: {
                    id: organisation.id,
                    name: organisation.name,
                  },
                });
              }
            } else {
              if (allAchieved) {
                await this.updateById(ambassador.id, {
                  isAchieved: true,
                  achievedDate: new Date(),
                  number: ambassador.number + 1,
                  stepsComplete: ambassador.stepsComplete + 1,
                });
                await this.sendNotification({
                  achievementType: AchievementName.Ambassador,
                  profileId,
                  organisationDetails: {
                    id: organisation.id,
                    name: organisation.name,
                  },
                });
              } else if (!allAchieved) {
                await this.updateById(ambassador.id, {
                  isAchieved: false,
                });
              }
            }
          }
        }),
      );

      return [];
    } catch (error) {
      this.logger.error(
        'Error processing achievements for Ambassador',
        error.message,
      );
    }
  }

  async addHighFiveAchievement({
    profileId,
    organisationId,
    currentUser,
  }: {
    profileId: string;
    organisationId: string;
    currentUser: ICurrentUser;
  }): Promise<Achievement> {
    this.logger.debug('AchievementsService.addHighFiveAchievement', {
      profileId,
      organisationId,
    });

    try {
      // Check if the organisation has Club Hablo subscription
      const clubHabloOrganisation = await this.organisationsService.findOne(
        {
          id: organisationId,
          hasClubHabloSubscription: true,
        },
        {
          attributes: ['id', 'name', 'image', 'vanityId', 'type'],
        },
      );
      if (!clubHabloOrganisation) {
        return;
      }

      // Find existing achievement or create a new one
      let achievement = await this.findOne({
        type: AchievementType.HighFive,
        profileId,
        organisationId,
      });

      const highFiveSequence = [1, 3, 5, 10, 15, 20, 25, 35, 50, 100];

      if (!achievement) {
        achievement = await this.create({
          type: AchievementType.HighFive,
          steps: 3,
          stepsComplete: 1,
          profileId,
          organisationId,
          number: 1,
          isAchieved: true,
          achievedDate: new Date(),
        });
      } else if (
        new Date(achievement.updatedAt).toDateString() !=
        new Date().toDateString()
      ) {
        const newStepsComplete = achievement.stepsComplete + 1;
        const index = highFiveSequence.indexOf(newStepsComplete);
        const nextNumber =
          index !== -1 && index < highFiveSequence.length - 1
            ? highFiveSequence[index + 1] // Next number in sequence
            : highFiveSequence.find(step => step > newStepsComplete) ||
              newStepsComplete;

        achievement = await this.updateById(achievement.id, {
          steps: nextNumber,
          stepsComplete: newStepsComplete,
          number: newStepsComplete,
          achievedDate: new Date(),
        });
      } else {
        this.logger.error(`High Five limit reached for today.`);
        throw new Error(`High Five limit reached for today.`);
      }

      const currentUserDetails = await this.profilesService.findById(
        currentUser.profileId,
      );

      // Log the activity to prevent duplicate points
      await this.activitiesService.createUserActivity({
        profileId,
        organisationId,
        type: ActivityType.HighFive,
        data: {
          type: ActivityType.HighFive,
          organisationId: clubHabloOrganisation.id,
          organisationName: clubHabloOrganisation.name,
          userId: currentUser.profileId,
          userName: currentUserDetails.name,
        },
        addLoyaltyPoints: true,
        schema: highFiveActivityDataDto,
        placeholders: {
          organisationId: clubHabloOrganisation.id,
          organisationName: clubHabloOrganisation.name,
          vanityId: clubHabloOrganisation.vanityId,
          createdById: currentUser.profileId,
          name: currentUserDetails.name,
        },
      });

      await this.notificationsService.createNotification({
        ownerProfileId: profileId,
        type: NotificationType.HighFiveAchievement,
        profileId: currentUserDetails.id,
        organisationId: clubHabloOrganisation.id,
        data: {
          organisation: clubHabloOrganisation,
          user: {
            id: currentUserDetails.id,
            name: currentUserDetails.name,
            email: currentUserDetails.email,
            image: currentUserDetails.image,
          },
        },
      });

      await this.notificationsService.sendPushNotification({
        profileIds: [profileId],
        replacements: [currentUserDetails.name, clubHabloOrganisation.name],
        messageType: NotificationMessage.HighFiveAchievement,
        route: encodePathParams(Routes.clubHabloDashboard),
      });

      return achievement;
    } catch (error) {
      this.logger.error(
        'Error processing achievements for High Five',
        error.message,
      );
      this.errorHelper.throwHttpException(
        'AchievementsService.addHighFiveAchievement',
        error.message,
      );
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async addOrganisationAchievements(): Promise<void> {
    await this.addPowerScrollerAchievement();
    await this.delay(1 * 60 * 1000); // 1 minutes delay
    await this.addAmbassadorAchievement();

    // Fetch achievements
    const usersWithNewAchievements = await this.findAll(
      {
        type: {
          [Op.in]: [AchievementType.PowerScroller, AchievementType.Ambassador],
        },
        isAchieved: true,
        updatedAt: { [Op.gte]: new Date().setHours(0, 0, 0, 0) },
      },
      { attributes: ['organisationId', 'profileId'] },
    );

    // Use an object to store unique organisation counts per profileId
    const profileOrgMap: Record<string, Set<string>> = {};

    // Populate the profileOrgMap
    for (const { profileId, organisationId } of usersWithNewAchievements) {
      if (!profileOrgMap[profileId]) {
        profileOrgMap[profileId] = new Set();
      }
      profileOrgMap[profileId].add(organisationId);
    }

    // Send notifications with unique organisation count
    for (const [profileId, orgSet] of Object.entries(profileOrgMap)) {
      await this.notificationsService.createNotification({
        ownerProfileId: profileId,
        type: NotificationType.MonthlyAchievementSummary,
        profileId,
        data: { uniqueOrgCount: orgSet.size }, // Send count
      });

      await this.notificationsService.sendPushNotification({
        profileIds: [profileId],
        replacements: [`${orgSet.size}`],
        messageType: NotificationMessage.MonthlyAchievementSummary,
        route: encodePathParams(Routes.clubHabloDashboard),
      });
    }
  }

  async sendNotification({
    achievementType,
    profileId,
    organisationDetails,
  }: {
    profileId: string;
    achievementType: AchievementName;
    organisationDetails?: {
      id: string;
      name: string;
    };
  }): Promise<void> {
    try {
      const notificationType = organisationDetails
        ? NotificationType.NewOrganisationAchievement
        : NotificationType.NewAchievement;

      // Map AchievementName to AchievementType for database queries
      const achievementTypeMap = {
        [AchievementName.AvidViewer]: AchievementType.AvidViewer,
        [AchievementName.HighFive]: AchievementType.HighFive,
        [AchievementName.IncentiveWarrior]: AchievementType.IncentiveWarrior,
        [AchievementName.Ambassador]: AchievementType.Ambassador,
        [AchievementName.PowerScroller]: AchievementType.PowerScroller,
        [AchievementName.ProfilePerfect]: AchievementType.ProfilePerfect,
        [AchievementName.DailyQuizStreak]: AchievementType.DailyQuizStreak,
        [AchievementName.HotStreak]: AchievementType.HotStreak,
        [AchievementName.HabloFounder]: AchievementType.HabloFounder,
        [AchievementName.CommunityBeacon]: AchievementType.CommunityBeacon,
        [AchievementName.IndustryInfluencer]: AchievementType.IndustryInfluencer,
        [AchievementName.WellbeingBadge]: AchievementType.WellbeingBadge,
      };

      // Find the achievement to get the level information
      let level = null;
      if (organisationDetails?.id) {
        const achievement = await this.findOne({
          type: achievementTypeMap[achievementType],
          profileId,
          organisationId: organisationDetails.id,
        });

        if (achievement) {
          if (
            achievementType === AchievementName.AvidViewer ||
            achievementType === AchievementName.HighFive
          ) {
            const levelMapping = {
              [AchievementName.AvidViewer]: [
                { steps: 175, level: 9 },
                { steps: 150, level: 8 },
                { steps: 125, level: 7 },
                { steps: 100, level: 6 },
                { steps: 75, level: 5 },
                { steps: 50, level: 4 },
                { steps: 25, level: 3 },
                { steps: 10, level: 2 },
                { steps: 5, level: 1 },
              ],
              [AchievementName.HighFive]: [
                { steps: 100, level: 10 },
                { steps: 50, level: 9 },
                { steps: 35, level: 8 },
                { steps: 25, level: 7 },
                { steps: 20, level: 6 },
                { steps: 15, level: 5 },
                { steps: 10, level: 4 },
                { steps: 5, level: 3 },
                { steps: 3, level: 2 },
                { steps: 1, level: 1 },
              ],
            };

            level =
              levelMapping[achievementType]?.find(
                ({ steps }) => achievement.stepsComplete >= steps,
              )?.level || null;
          } else if (achievementType === AchievementName.IncentiveWarrior) {
            level = achievement.stepsComplete;
          } else if (
            achievementType === AchievementName.Ambassador ||
            achievementType === AchievementName.PowerScroller
          ) {
            level = achievement.number;
          }
        }
      }

      // Include level in the notification data
      const data = { achievementType, level };

      await this.notificationsService.createNotification({
        ownerProfileId: profileId,
        type: notificationType,
        profileId,
        organisationId: organisationDetails?.id,
        data,
      });

      const replacements = organisationDetails
        ? [organisationDetails.name, achievementType]
        : [achievementType];

      if (!organisationDetails?.id) {
        await this.notificationsService.sendPushNotification({
          profileIds: [profileId],
          replacements,
          messageType: NotificationMessage.NewAchievement,
          route: encodePathParams(Routes.clubHabloDashboard),
        });
      }
    } catch (error) {
      this.errorHelper.throwHttpException(
        'AchievementsService.sendNotification',
        error.message,
      );
    }
  }

  async resetDailyQuizStreakAchievement(): Promise<void> {
    this.logger.debug('AchievementsService.resetDailyQuizStreakAchievement');
    try {
      await this.update({
        where: {
          type: AchievementType.DailyQuizStreak,
        },
        update: { stepsComplete: 0 },
      });
      return;
    } catch (error) {
      this.errorHelper.throwHttpException(
        'AchievementsService.resetDailyQuizStreakAchievement',
        error.message,
      );
    }
  }
}
