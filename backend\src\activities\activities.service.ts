import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { connect } from 'getstream';
import { get } from 'lodash';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Op, QueryOptions, Transaction } from 'sequelize';
import { Sequelize } from 'sequelize-typescript';
import { Logger } from 'winston';
import { AchievementsService } from '../achievements/achievements.service';
import { BaseService } from '../common/base.service';
import { ErrorHelper } from '../common/helpers/error';
import validateData from '../common/helpers/validateJsonSchema';
import config from '../config/config';
import { EmailInvitations } from '../email-invitations/models/email-invitations.model';
import { FollowersService } from '../followers/followers.service';
import { FollowerStatus } from '../followers/models/follower.model';
import { IncentiveParticipantStatus } from '../incentive-participants/args/incentive-participants.args';
import { IncentiveParticipantsService } from '../incentive-participants/incentive-participants.service';
import { LoyaltyPointsService } from '../loyalty-points/loyalty-points.service';
import { OrganisationsService } from '../organisations/organisations.service';
import { ProfilesService } from '../profiles/profiles.service';
import { ActivityType } from './args/activities.args';
import { CreateActivityInput } from './dto/create-activity.input';
import { Activity } from './models/activity.model';
import { emptyActivityDataDto } from './dto/create-user-activity-data.dto';
import moment from 'moment-timezone';
import {
  createOrganisationActivityDataDto,
  createWebinarActivityDataDto,
  createIncentiveActivityDataDto,
  createEventActivityDataDto,
  eventInviteUserActivityDataDto,
  ownerPostCommentActivityDataDto,
  createPostActivityDataDto,
} from '../activities/dto/create-organisation-activity-data.dto'; // Import the new DTO
import { OrganisationActivity } from '../activities/models/organisation-activity.model';
import { OrganisationLoyaltyPointsService } from '../organisation-loyalty-points/organisation-loyalty-points.service';
import { AchievementType } from '../achievements/args/achievements.args';

type StreamParams = {
  limit: number;
  id_lt?: string;
  kind?: string;
  activity_id?: string;
};

@Injectable()
export class ActivitiesService extends BaseService(Activity) {
  @Inject(forwardRef(() => OrganisationsService))
  private readonly organisationsService: OrganisationsService;
  @Inject(forwardRef(() => LoyaltyPointsService))
  private readonly loyaltyPointsService: LoyaltyPointsService;
  @Inject(forwardRef(() => ProfilesService))
  private readonly profilesService: ProfilesService;
  @Inject(forwardRef(() => IncentiveParticipantsService))
  private readonly incentiveParticipantsService: IncentiveParticipantsService;
  @Inject(forwardRef(() => FollowersService))
  private readonly followersService: FollowersService;
  @Inject(forwardRef(() => AchievementsService))
  private readonly achievementsService: AchievementsService;
  @Inject()
  private readonly sequelize: Sequelize;
  @Inject(WINSTON_MODULE_PROVIDER)
  private readonly logger: Logger;
  @Inject()
  private readonly errorHelper: ErrorHelper;
  @Inject(forwardRef(() => OrganisationLoyaltyPointsService))
  private readonly organisationLoyaltyPointsService: OrganisationLoyaltyPointsService;

  client: any; // getstream client

  onModuleInit(): void {
    if (config.SERVICE === 'api-beta') {
      this.client = connect(config.STREAM_BETA_KEY, config.STREAM_BETA_SECRET);
    } else {
      this.client = connect(config.STREAM_KEY, config.STREAM_SECRET);
    }
  }

  async createGenericActivity({
    activityData,
    profileId,
  }: {
    profileId: string;
    activityData: CreateActivityInput;
  }): Promise<Activity> {
    this.logger.verbose('ActivitiesResolver.createActivity', {
      profileId,
      type: activityData.type,
    });

    const allowedTypes = [
      ActivityType.OrganisationPageView,
      ActivityType.WebinarVideoView,
      ActivityType.WebinarCompleted,
    ];
    const loyaltyPointsTypes = [ActivityType.WebinarCompleted];
    const uniqueEntryTypes = [ActivityType.WebinarCompleted];

    if (!allowedTypes.includes(activityData.type)) {
      this.errorHelper.throwHttpException(
        `ActivitiesResolver.createActivity`,
        `${activityData.type} activity is not allowed to be created via mutation`,
      );
    }

    const placeholders = activityData.data;
    if (activityData.type === ActivityType.WebinarCompleted) {
      const previousActivity = await this.findOne({
        profileId,
        type: ActivityType.WebinarCompleted,
        webinarId: activityData.webinarId,
      });

      if (previousActivity) {
        const loyaltyPointEntry = await this.loyaltyPointsService.addPoints({
          type: ActivityType.WebinarCompleted,
          profileId,
          organisationId: activityData.organisationId,
          placeholders: {
            ...placeholders,
            previouslyTracked: true,
          },
          variableRewardPoints: 0,
        });
        return await this.create({
          type: ActivityType.WebinarCompleted,
          data: {
            ...placeholders,
            previouslyTracked: true,
          },
          profileId,
          webinarId: activityData.webinarId,
          organisationId: activityData.organisationId,
          loyaltyPointId: loyaltyPointEntry.id,
        });
      } else if (!previousActivity) {
        await this.achievementsService.addAvidViewerAchievement({
          profileId,
          organisationId: activityData.organisationId,
        });
      }
    }

    return this.createUserActivity({
      ...activityData,
      organisationId: activityData.organisationId,
      data: activityData.data,
      type: activityData.type,
      profileId,
      schema: null,
      addLoyaltyPoints: loyaltyPointsTypes.includes(activityData.type),
      checkForUniqueEntry: uniqueEntryTypes.includes(activityData.type),
      placeholders,
    });
  }

  async createUserActivity<T>({
    profileId,
    type,
    data,
    schema,
    placeholders,
    transaction,
    addLoyaltyPoints,
    checkForUniqueEntry,
    variableRewardPoints,
    organisationId,
    postId,
    ...rest
  }: {
    type: ActivityType;
    data: Record<string, unknown>;
    schema: new () => T | null;
    profileId: string;
    placeholders?: any;
    transaction?: Transaction;
    addLoyaltyPoints?: boolean;
    checkForUniqueEntry?: boolean;
    variableRewardPoints?: number;
    organisationId?: string;
    postId?: string;
  }): Promise<Activity> {
    this.logger.verbose('ActivitiesService.createUserActivity', {
      profileId,
      type,
      streakCount: data?.streakCount,
      postId,
      organisationId,
      addLoyaltyPoints,
      checkForUniqueEntry,
      variableRewardPoints,
    });

    if (checkForUniqueEntry) {
      const webinarId = data['webinarId'];
      const existingActivity = await this.findOne({
        profileId,
        type,
        webinarId: webinarId ?? null,
      });
      if (existingActivity) {
        return existingActivity;
      }
    }

    if (schema) {
      await validateData(schema, data, this.logger);
    }

    let loyaltyPointId = null;
    if (addLoyaltyPoints) {
      const loyaltyPointEntry = await this.loyaltyPointsService.addPoints({
        type,
        profileId,
        placeholders,
        transaction,
        variableRewardPoints,
        organisationId,
      });
      loyaltyPointId = loyaltyPointEntry.id;
    }

    const newActivity = await this.create(
      {
        type,
        data,
        profileId,
        loyaltyPointId,
        organisationId,
        postId,
        ...rest,
      },
      {
        transaction,
      },
    );

    // Only create OrganisationActivity for Create type
    if (
      organisationId &&
      (type === ActivityType.CreatePost ||
        type === ActivityType.CreateWebinar ||
        type === ActivityType.CreateIncentive ||
        type === ActivityType.CreateEvent ||
        type === ActivityType.OwnerPostComment)
    ) {
      const orgActivityData:
        | createOrganisationActivityDataDto
        | createWebinarActivityDataDto
        | createIncentiveActivityDataDto
        | createEventActivityDataDto
        | ownerPostCommentActivityDataDto = {
        createdById: profileId,
        organisationId: organisationId,
        type: type,
        postId: postId,
        data: { postId: postId, ...data },
      };

      await this.createOrganisationActivity(orgActivityData, { transaction });
    }

    if (
      organisationId &&
      (type === ActivityType.CreatePost || type === ActivityType.PostView)
    ) {
      await this.achievementsService.incrementStepsComplete({
        profileId,
        organisationId,
        postId,
      });
    }

    return newActivity;
  }

  async getHomePageProfileRewardsData({
    profileId,
  }: {
    profileId: string;
  }): Promise<{
    loginDayStreak: number;
    habloPoints: number;
    lifeTimePoints: number;
    goldTierStreak: number;
    tier: string;
    nextTier: string;
    activeTier: string;
    previousMonthTier: string;
    nextTierPoints: number;
    achievements: number;
  }> {
    this.logger.verbose('ActivitiesService.getHomePageProfileRewardsData', {
      profileId,
    });

    const [
      {
        lifeTimePoints,
        rollingPoints,
        streak: goldTierStreak,
        currentTier,
        nextTier,
        previousMonthTier,
        activeTier,
      },
      loginDayStreak,
    ] = await Promise.all([
      this.loyaltyPointsService.getUserLoyaltyPoints({
        profileId,
      }),
      this.getUserLoginStreak({
        profileId,
      }),
    ]);

    const achievements = await this.achievementsService.count({
      [Op.or]: [
        {
          profileId,
          isAchieved: true,
          organisationId: null,
        },
        {
          profileId,
          isAchieved: true,
          type: AchievementType.Ambassador,
        },
      ],
    });

    /**
     * Based on the activity of the user, we need to give the following data to the user.
     * 'Day streak' that is to be calculated if the user has logged in constantly for n number of days once a day. We get this data from activity table.
     * 'Hablo points' that is to be calculated based on the activity of the user. We get this data from loyalty points table.
     * 'Life time points' that is to be calculated based on the activity of the user. We get this data from loyalty points table.
     * 'Tier' that is to be calculated based on the activity of the user. We get this data from loyalty points table.
     * 'Next tier' that is to be calculated based on the activity of the user. We get this data from loyalty points table.
     * 'Next tier points' that is to be calculated based on the activity of the user. We get this data from loyalty points table.
     */

    return {
      loginDayStreak,
      habloPoints: rollingPoints,
      lifeTimePoints,
      goldTierStreak,
      activeTier: activeTier.key,
      tier: currentTier.key,
      nextTier: nextTier ? nextTier.key : null,
      nextTierPoints: nextTier ? nextTier.minPoints : 0,
      previousMonthTier: previousMonthTier.key,
      achievements,
    };
  }

  async getOrganisationRewardsData({
    organisationId,
  }: {
    organisationId: string;
  }): Promise<{
    habloPoints: number;
    tier: string;
    nextTier: string;
    nextTierPoints: number;
    previousMonthTier: string;
    activeTier: string;
    lifeTimePoints: number;
    goldTierStreak: number;
  }> {
    this.logger.verbose('ActivitiesService.getOrganisationRewardsData', {
      organisationId,
    });
    const [
      {
        lifeTimePoints,
        rollingPoints,
        streak: goldTierStreak,
        currentTier,
        nextTier,
        previousMonthTier,
        activeTier,
      },
    ] = await Promise.all([
      this.organisationLoyaltyPointsService.getOrganisationLoyaltyPoints({
        organisationId,
      }),
    ]);

    return {
      habloPoints: rollingPoints,
      lifeTimePoints,
      goldTierStreak,
      tier: currentTier.key,
      nextTier: nextTier ? nextTier.key : null,
      nextTierPoints: nextTier ? nextTier.minPoints : 0,
      activeTier: activeTier.key,
      previousMonthTier: previousMonthTier.key,
    };
  }

  async getUserLoginStreak({
    profileId,
  }: {
    profileId: string;
  }): Promise<number> {
    const lastLoginActivity = await this.findOne(
      {
        profileId,
        type: ActivityType.ActiveSession,
      },
      {
        order: [['createdAt', 'DESC']],
      },
    );

    let streakCount = 0;
    if (lastLoginActivity) {
      const lastLoginDate = moment(lastLoginActivity.createdAt).startOf('day');
      const today = moment().startOf('day');

      const diffInDays = today.diff(lastLoginDate, 'days');

      streakCount =
        diffInDays > 1
          ? 0
          : get(lastLoginActivity.getDataValue('data'), 'streakCount', 0);
    }

    return streakCount;
  }

  async getPaginatedActivitiesForOrg(
    feed: any,
    prevActivityId?: string,
    prevResults?: any[],
  ): Promise<any[]> {
    const reqParams: StreamParams = {
      limit: 100,
    };
    if (prevActivityId) reqParams.id_lt = prevActivityId;
    const resultsCurrObj = await feed.get(reqParams);
    const resultsCurr = resultsCurrObj.results;
    let idx = 0;
    for (const activity of resultsCurr) {
      const views = await this.getPaginatedReactionsForActivity(
        activity.id,
        'view',
      );
      const comments = await this.getPaginatedReactionsForActivity(
        activity.id,
        'comment',
      );
      const likes = await this.getPaginatedReactionsForActivity(
        activity.id,
        'like',
      );
      // console.log('retrieved views for activity', activity.id, views.length);
      // console.log(
      //   'retrieved comments for activity',
      //   activity.id,
      //   comments.length,
      // );
      // console.log('retrieved likes for activity', activity.id, likes.length);

      resultsCurr[idx].views = views;
      resultsCurr[idx].comments = comments;
      resultsCurr[idx].likes = likes;
      idx++;
    }
    const results = [...(prevResults || []), ...resultsCurr];

    const nextActivityId = resultsCurr[resultsCurr.results.length - 1]?.id;
    if (resultsCurr.length === reqParams.limit && nextActivityId) {
      return this.getPaginatedActivitiesForOrg(feed, nextActivityId, results);
    }
    return results;
  }

  async getPaginatedReactionsForActivity(
    activityId: string,
    reactionType: string,
    prevReactionId?: string,
    prevResults?: any[],
  ): Promise<any[]> {
    const reqParams: StreamParams = {
      limit: 25,
      kind: reactionType,
      activity_id: activityId,
    };
    if (prevReactionId) reqParams.id_lt = prevReactionId;

    const resultsCurrObj = await this.client.reactions.filter(reqParams);
    const resultsCurr = resultsCurrObj.results;

    const nextReactionId = resultsCurr[resultsCurr.length - 1]?.id;

    const results = [...(prevResults || []), ...resultsCurr];

    if (resultsCurr.length === reqParams.limit && nextReactionId) {
      return this.getPaginatedReactionsForActivity(
        activityId,
        reactionType,
        nextReactionId,
        results,
      );
    }
    return results;
  }

  async migrateAnalyticsDataFromStream(): Promise<void> {
    const transaction = await this.sequelize.transaction();
    try {
      // todo: add check in db to run script only once!
      const allOrgs = await this.organisationsService.findAll();
      for (const org of allOrgs) {
        const feed = await this.client.feed('organisations', org.id);
        const activities = await this.getPaginatedActivitiesForOrg(feed);
        console.log('retrieved activities for org', org.id, activities.length);
        for (const activity of activities) {
          const organisationId =
            activity && activity.object.split('SO:organisations:')[1];
          const postId = activity && activity.post.split('SO:posts:')[1];
          const profileId = activity && activity.user && activity.user.id;
          console.log(
            'processing activity',
            activity.id,
            organisationId,
            postId,
            profileId,
          );
          // is repost
          if (activity.data.repostData) {
            console.log(
              'creating repost entry',
              profileId,
              organisationId,
              postId,
            );
            // create entry in db ! activityType PostInteractions
            await this.create(
              {
                type: ActivityType.PostInteractions,
                profileId: profileId,
                organisationId: organisationId,
                postId: postId,
                createdAt: activity.time,
              },
              {
                transaction,
              },
            );
          }
          for (const {} of activity.views) {
            // create entry in db ! activityType PostImpressions
            console.log(
              'creating views entry',
              profileId,
              organisationId,
              postId,
            );
            await this.create(
              {
                type: ActivityType.PostImpressions,
                profileId: profileId,
                organisationId: organisationId,
                postId: postId,
              },
              {
                transaction,
              },
            );
          }
          for (const comment of activity.comments) {
            // create entry in db ! activityType PostInteractions
            console.log(
              'creating comments entry',
              profileId,
              organisationId,
              postId,
            );
            await this.create(
              {
                type: ActivityType.PostInteractions,
                profileId: profileId,
                organisationId: organisationId,
                postId: postId,
                createdAt: comment.created_at,
              },
              {
                transaction,
              },
            );
          }
          for (const like of activity.likes) {
            console.log(
              'creating like entry',
              profileId,
              organisationId,
              postId,
            );
            await this.create(
              {
                type: ActivityType.PostInteractions,
                profileId: profileId,
                organisationId: organisationId,
                postId: postId,
                createdAt: like.created_at,
              },
              {
                transaction,
              },
            );
          }
        }
      }
      await transaction.commit();
      console.log('analytics sync done');
    } catch (err) {
      console.log('analytics sync err', err);
      await transaction.rollback();
    }
  }

  async activityLoyaltyPointsMigration({
    profileId,
  }: {
    profileId: string;
  }): Promise<boolean> {
    this.logger.info('ActivitiesService.activityLoyaltyPointsMigration', {
      profileId,
    });

    try {
      const profile = await this.profilesService.updateById(profileId, {
        sellHolidays: true,
      });

      const currentLoyaltyPoints =
        await this.loyaltyPointsService.getUserLoyaltyPoints({
          profileId,
        });
      if (currentLoyaltyPoints.lifeTimePoints > 0) {
        return true;
      }

      setTimeout(async () => {
        this.logger.info(
          'ActivitiesService.activityLoyaltyPointsMigration background job',
          {
            profileId,
          },
        );

        const transaction = await this.sequelize.transaction();

        try {
          // Check if the user has completed the profile
          if (profile.isComplete) {
            await this.createUserActivity({
              type: ActivityType.ProfileCompletion,
              schema: emptyActivityDataDto,
              data: {},
              profileId: profile.id,
              addLoyaltyPoints: true,
              checkForUniqueEntry: true,
              transaction,
            });
          }

          // Check if the user has registered for incentives
          let totalIncentives = await this.incentiveParticipantsService.count({
            status: {
              [Op.in]: [IncentiveParticipantStatus.Registered],
            },
            profileId: profile.id,
          });
          if (totalIncentives > 0) {
            if (totalIncentives > 10) {
              totalIncentives = 10;
            }
            await this.createUserActivity({
              type: ActivityType.IncentiveRegistrationMigration,
              schema: emptyActivityDataDto,
              data: {},
              profileId: profile.id,
              addLoyaltyPoints: true,
              placeholders: {
                id: profileId,
                count: totalIncentives,
              },
              variableRewardPoints: totalIncentives * 50,
              transaction,
            });
          }

          // Check for organisation followers
          let totalFollowedOrganisations = await this.followersService.count({
            profileId: profile.id,
            status: FollowerStatus.Active,
          });
          if (totalFollowedOrganisations > 0) {
            if (totalFollowedOrganisations > 30) {
              totalFollowedOrganisations = 30;
            }
            await this.createUserActivity({
              type: ActivityType.OrganisationFollowerMigration,
              schema: emptyActivityDataDto,
              data: {},
              profileId: profile.id,
              addLoyaltyPoints: true,
              placeholders: {
                id: profileId,
                count: totalFollowedOrganisations,
              },
              variableRewardPoints: totalFollowedOrganisations * 100,
              transaction,
            });
          }

          // Check for notifications
          const notificationActivity = await this.findOne(
            {
              profileId,
              type: ActivityType.NotificationEnabled,
            },
            {
              order: [['createdAt', 'DESC']],
              transaction,
            },
          );
          if (notificationActivity) {
            const loyaltyPointEntry = await this.loyaltyPointsService.addPoints(
              {
                type: ActivityType.NotificationEnabled,
                profileId,
                transaction,
              },
            );

            await this.updateById(
              notificationActivity.id,
              {
                loyaltyPointId: loyaltyPointEntry.id,
              },
              {
                transaction,
              },
            );
          }

          // Check for connection requests via email
          const query = `
        SELECT DISTINCT ON ("invitedEmail")
              "invitedEmail",
              "sentConnectionRequestId"
          FROM
              "EmailInvitations"
          WHERE
              "senderProfileId" = :profileId
          ORDER BY
              "invitedEmail",
              "createdAt" DESC
          LIMIT 100;
      `;
          const options: QueryOptions = {
            replacements: {
              profileId,
            },
            type: 'SELECT',
          };
          const emailInvitations = (await EmailInvitations.sequelize.query(
            query,
            options,
          )) as {
            invitedEmail: string;
            sentConnectionRequestId: string;
          }[];
          let entriesWithConnection = 0;
          let pointsFromConnection = 0;
          let entriesWithoutConnection = 0;
          let pointsFromNoConnection = 0;
          for (const invite of emailInvitations) {
            if (invite.sentConnectionRequestId) {
              entriesWithConnection += 1;
              pointsFromConnection += 1000;
            } else {
              entriesWithoutConnection += 1;
              pointsFromNoConnection += 10;
            }

            if (pointsFromConnection + pointsFromNoConnection >= 10000) {
              break;
            }
          }

          if (entriesWithConnection > 0) {
            await this.createUserActivity({
              type: ActivityType.EmailInvitationRegistrationMigration,
              schema: emptyActivityDataDto,
              data: {},
              profileId: profile.id,
              addLoyaltyPoints: true,
              placeholders: {
                id: profileId,
                count: entriesWithConnection,
              },
              variableRewardPoints: +pointsFromConnection,
              transaction,
            });
          }
          if (entriesWithoutConnection > 0) {
            await this.createUserActivity({
              type: ActivityType.InviteEmailContactMigration,
              schema: emptyActivityDataDto,
              data: {},
              profileId: profile.id,
              addLoyaltyPoints: true,
              placeholders: {
                id: profileId,
                count: entriesWithoutConnection,
              },
              variableRewardPoints: +pointsFromNoConnection,
              transaction,
            });
          }

          await transaction.commit();
        } catch (error) {
          await transaction.rollback();
          this.logger.error(
            `ActivitiesService.activityLoyaltyPointsMigration background job Error: ${error.message}`,
            error.message,
          );
        }
      }, 10);

      return true;
    } catch (error) {
      this.logger.error(
        `ActivitiesService.activityLoyaltyPointsMigration Error: ${error.message}`,
        error.message,
      );

      this.errorHelper.throwHttpException(
        `ActivitiesService.activityLoyaltyPointsMigration`,
        error.message,
      );
    }
  }

  async fixActivityDataMigration(): Promise<void> {
    try {
      /**
       * Check for records with null organisationId or postId or profileId and whose data field has either of these 3 values.
       * In those records, take value from a JSONB column data if available and set it to these columns.
       * If data column does not have these values, then set it as null.
       */
      const activitiesToUpdate = await this.findAll({
        [Op.or]: [
          {
            organisationId: null,
          },
          {
            postId: null,
          },
          {
            profileId: null,
          },
        ],
        data: {
          [Op.or]: [
            {
              organisationId: {
                [Op.not]: null,
              },
            },
            {
              postId: {
                [Op.not]: null,
              },
            },
            {
              profileId: {
                [Op.not]: null,
              },
            },
          ],
        },
      });

      this.logger.info(
        `Total Activities to update: ${activitiesToUpdate.length}`,
      );

      let activitiesUpdated = 0;
      let activitiesWithError = 0;

      for (const activity of activitiesToUpdate) {
        const data = activity.getDataValue('data');

        let type = activity.getDataValue('type');

        /**
         * Check if the type is 'PostLikeShare' and the data type is 'PostLike'
         * then update the type to 'PostInteractions'
         *
         * Check if the type is 'OrganisationUnFollower'
         * then update the type to 'OrganisationFollower'
         */
        if ((type as string) === 'PostLikeShare' && data.type === 'PostLike') {
          type = ActivityType.PostInteractions;
        } else if ((type as string) === 'OrganisationUnFollower') {
          type = ActivityType.OrganisationFollower;
        }

        console.log({
          ...data,
          type,
          id: activity.id,
        });

        try {
          await activity.update({
            type,
            organisationId:
              activity.organisationId || data?.organisationId || null,
            postId: activity.postId || data?.postId || null, // Update the 'postId' value with the value from the 'data' object
            profileId: activity.profileId || data?.profileId || null,
          });

          activitiesUpdated += 1;
        } catch (error) {
          this.logger.error(
            `ActivitiesService.fixActivityDataMigration Error`,
            error.message,
          );

          activitiesWithError += 1;
        }
      }

      this.logger.info('ActivitiesService.fixActivityDataMigration completed');
      this.logger.info(`Total Activities updated: ${activitiesUpdated}.`);
      this.logger.info(`Total Activities with error: ${activitiesWithError}`);
    } catch (error) {
      this.logger.error(
        `ActivitiesService.fixActivityDataMigration Error`,
        error.message,
      );
    }
  }

  async createOrganisationActivity(
    activityData:
      | createOrganisationActivityDataDto
      | createWebinarActivityDataDto
      | createIncentiveActivityDataDto
      | createEventActivityDataDto
      | eventInviteUserActivityDataDto
      | ownerPostCommentActivityDataDto
      | createPostActivityDataDto,
    options?: { transaction?: Transaction },
  ): Promise<OrganisationActivity> {
    this.logger.verbose('ActivitiesService.createOrganisationActivity', {
      activityData,
    });

    // Get parent organization if it exists
    let parentOrganisationId = null;
    
    // First check if parentOrgId is provided in the activity data
    if ('parentOrgId' in activityData && activityData.parentOrgId) {
      parentOrganisationId = activityData.parentOrgId;
    } 
    // If not, try to get it from the organisation
    else if ('organisationId' in activityData && activityData.organisationId) {
      const org = await this.organisationsService.findById(activityData.organisationId, {
        transaction: options?.transaction,
      });
      
      if (org?.parentOrganisations?.length > 0) {
        parentOrganisationId = org.parentOrganisations[0].id;
      }
    }

    // Create a new organisation activity record
    const newActivity = await OrganisationActivity.create(
      {
        type: activityData.type,
        data: 'data' in activityData ? activityData.data : {},
        organisationId:
          'organisationId' in activityData ? activityData.organisationId : null,
        createdById:
          'createdById' in activityData ? activityData.createdById : null,
        postId: 'postId' in activityData ? activityData.postId : null,
        webinarId: 'webinarId' in activityData ? activityData.webinarId : null,
        eventId: 'eventId' in activityData ? activityData.eventId : null,
        parentOrganisationId,
      },
      {
        transaction: options?.transaction,
      },
    );

    return newActivity;
  }

  async getEverydayActivity({ profileId }: { profileId: string }): Promise<{
    loginDayStreak: boolean;
    variableReward: boolean;
  }> {
    this.logger.verbose('ActivityService.getEverydayActivity', {
      profileId,
    });

    // Check if variable reward points have been processed today
    const profile = await this.profilesService.findById(profileId);
    if (!profile) {
      // Throw an error if the profile is not found
      this.logger.error(`Profile not found for profileId: ${profileId}`);
      throw new Error(`Profile not found for profileId: ${profileId}`);
    }

    const timezone = profile?.timezone || 'UTC';
    const startOfDay = moment.tz(timezone).startOf('day').utc().toDate();

    // Check if an ActiveSession entry exists for today
    const activeSessionEntry = await this.findOne(
      {
        type: ActivityType.ActiveSession,
        profileId,
        createdAt: {
          [Op.gt]: startOfDay,
        },
      },
      {
        useCache: false,
      },
    );

    // Check if a VariableLoginReward entry exists for today
    const variableLoginRewardEntry = await this.findOne(
      {
        type: ActivityType.VariableLoginReward,
        profileId,
        createdAt: {
          [Op.gt]: startOfDay,
        },
      },
      {
        useCache: false,
      },
    );

    const loginDayStreak = !!activeSessionEntry;
    let variableReward = !!variableLoginRewardEntry;

    // Sending variableReward as true for the user who are not club Hablo members
    if (!profile.sellHolidays) {
      variableReward = true;
    }

    return { loginDayStreak, variableReward };
  }
}
