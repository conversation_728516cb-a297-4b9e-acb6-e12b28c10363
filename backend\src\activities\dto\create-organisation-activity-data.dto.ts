import {
  IsString,
  <PERSON>NotEmpty,
  <PERSON>JSON,
  <PERSON>Optional,
  IsEnum,
} from 'class-validator';

export class createPostActivityDataDto {
  @IsString()
  createdById: string;

  @IsString()
  postId: string;

  @IsOptional()
  @IsString()
  postContent?: string;

  @IsOptional()
  @IsString()
  organisationId?: string;

  @IsOptional()
  @IsString()
  organisationName?: string;

  @IsOptional()
  @IsString()
  parentOrgId?: string;

  @IsJSON()
  data: any;

  @IsString()
  @IsNotEmpty()
  type: string;
}

export class createOrganisationActivityDataDto {
  @IsString()
  @IsNotEmpty()
  createdById: string;

  @IsString()
  @IsNotEmpty()
  organisationId: string;

  @IsString()
  @IsNotEmpty()
  postId: string;

  @IsString()
  @IsNotEmpty()
  type: string; // Type of activity, e.g., 'CreatePost'

  @IsJSON()
  data: any;
}

export class createWebinarActivityDataDto {
  @IsString()
  createdById: string;

  @IsString()
  webinarId: string;

  @IsOptional()
  @IsString()
  webinarContent?: string;

  @IsOptional()
  @IsString()
  organisationId?: string;

  @IsOptional()
  @IsString()
  organisationName?: string;

  @IsJSON()
  data: any;

  @IsString()
  @IsNotEmpty()
  type: string;
}

export class createIncentiveActivityDataDto {
  @IsString()
  createdById: string;

  @IsString()
  incentiveId: string;

  @IsOptional()
  @IsString()
  organisationId?: string;

  @IsString()
  @IsNotEmpty()
  type: string;

  @IsJSON()
  data: any;
}

export class createEventActivityDataDto {
  @IsString()
  createdById: string;

  @IsString()
  eventId: string;

  @IsOptional()
  @IsString()
  organisationId?: string;

  @IsOptional()
  @IsString()
  organisationName?: string;

  @IsJSON()
  data: any;

  @IsString()
  @IsNotEmpty()
  type: string;
}

export class eventInviteUserActivityDataDto {
  @IsString()
  @IsNotEmpty()
  invitedById: string;

  @IsString()
  @IsNotEmpty()
  invitedProfileId: string;

  @IsString()
  @IsNotEmpty()
  organisationId: string;

  @IsString()
  @IsNotEmpty()
  eventId: string;

  @IsString()
  @IsNotEmpty()
  type: string;

  @IsJSON()
  data: any;

  @IsString()
  createdById: string;
}

export class ownerPostCommentActivityDataDto {
  @IsString()
  commentById: string;

  @IsString()
  @IsNotEmpty()
  type: string;
}
