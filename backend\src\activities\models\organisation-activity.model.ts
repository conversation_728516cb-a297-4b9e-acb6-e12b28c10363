import {
  Column,
  Model,
  Table,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { DataTypes } from 'sequelize';
import { ObjectType, Field, ID } from '@nestjs/graphql';
import short from 'short-uuid';

import { Organisation } from '../../organisations/models/organisation.model';
import { Profile } from '../../profiles/models/profile.model';
import { Post } from '../../posts/models/post.model';
import { Event } from '../../events/models/event.model';
import { Webinar } from '../../webinars/models/webinar.model';
import GraphQLJSON from 'graphql-type-json';

@Table
@ObjectType()
export class OrganisationActivity extends Model<OrganisationActivity> {
  @Field(() => ID)
  @Column({
    primaryKey: true,
    unique: true,
    allowNull: false,
    defaultValue: short.generate,
  })
  id: string;

  @Field(() => String, {
    nullable: false,
  })
  @Column({
    type: DataTypes.STRING,
  })
  type: string; // Type of activity, e.g., 'CreatePost'

  @Field(() => GraphQLJSON, {
    nullable: true,
  })
  @Column({
    type: DataTypes.JSONB,
  })
  data: any; // Additional data related to the activity

  @ForeignKey(() => Organisation)
  @Column
  organisationId: string; // ID of the organisation

  @BelongsTo(() => Organisation, {
    foreignKey: 'organisationId',
    onDelete: 'CASCADE',
  })
  organisation: Organisation;

  @Field(() => String, { nullable: true })
  @ForeignKey(() => Organisation)
  @Column({
    allowNull: true,
  })
  parentOrganisationId: string;

  @BelongsTo(() => Organisation, {
    foreignKey: 'parentOrganisationId',
    onDelete: 'SET NULL',
  })
  parentOrganisation: Organisation;

  @ForeignKey(() => Profile)
  @Column
  createdById: string; // ID of the user who created the activity

  @BelongsTo(() => Profile, {
    foreignKey: 'createdById',
    onDelete: 'CASCADE',
  })
  createdBy: Profile;

  @ForeignKey(() => Post)
  @Column
  postId: string; // Optional, ID of the post related to the activity

  @BelongsTo(() => Post, {
    foreignKey: 'postId',
    onDelete: 'CASCADE',
  })
  post: Post;

  @ForeignKey(() => Event)
  @Column
  eventId: string; // Optional, ID of the event related to the activity

  @BelongsTo(() => Event, {
    foreignKey: 'eventId',
    onDelete: 'CASCADE',
  })
  event: Event;

  @ForeignKey(() => Webinar)
  @Column
  webinarId: string; // Optional, ID of the webinar related to the activity

  @BelongsTo(() => Webinar, {
    foreignKey: 'webinarId',
    onDelete: 'CASCADE',
  })
  webinar: Webinar;

  @Field()
  @Column
  createdAt: Date; // Timestamp for when the activity was created

  @Field()
  @Column
  updatedAt: Date; // Timestamp for when the activity was last updated
}
