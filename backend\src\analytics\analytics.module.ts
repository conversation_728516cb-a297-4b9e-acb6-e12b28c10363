import { Module, forwardRef } from '@nestjs/common';
import { AnalyticsResolver } from './analytics.resolver';
import { CommonModule } from '../common/common.module';
import { AnalyticsService } from './analytics.service';
import { ActivitiesModule } from '../activities/activities.module';
import { AnalyticsRepository } from './analytics.repository';
import { AnalyticsServiceHelper } from './helpers/analytics.service.helper';
import { PartnerOrganisationsModule } from '../partner-organisations/partner-organisations.module';

@Module({
  imports: [
    forwardRef(() => CommonModule), 
    forwardRef(() => ActivitiesModule), 
    forwardRef(() => PartnerOrganisationsModule)
  ],
  providers: [
    AnalyticsService,
    AnalyticsResolver,
    AnalyticsRepository,
    AnalyticsServiceHelper,
  ],
  exports: [AnalyticsService],
})
export class AnalyticsModule {}
