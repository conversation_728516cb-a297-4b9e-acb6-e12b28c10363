import { Injectable, Inject, forwardRef } from '@nestjs/common';
import {
  AnalyticPeriod,
  AnalyticsCalculationResult,
} from './args/analytics.args';
import { Op } from 'sequelize';
import { ActivitiesService } from '../activities/activities.service';
import { Sequelize } from 'sequelize-typescript';
import { ActivityType } from '../activities/args/activities.args';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';

@Injectable()
export class AnalyticsRepository {
  @Inject(forwardRef(() => ActivitiesService))
  private readonly activitiesService: ActivitiesService;
  @Inject()
  private readonly sequelize: Sequelize;
  @Inject(WINSTON_MODULE_PROVIDER)
  private readonly logger: Logger;

  async calculateAnalytics(
    startDate: Date,
    endDate: Date,
    organisationId: string,
    webinarId?: string,
    filter?: {
      type?: ActivityType[];
      period?: AnalyticPeriod;
    },
  ): Promise<AnalyticsCalculationResult[]> {
    const queryParams = webinarId
      ? {
          organisationId,
          webinarId,
          createdAt: {
            [Op.between]: [startDate, endDate],
          },
        }
      : {
          organisationId,
          createdAt: {
            [Op.between]: [startDate, endDate],
          },
        };

    if (filter?.type?.length > 0) {
      queryParams['type'] = {
        [Op.in]: filter.type,
      };
    }

    if (filter?.period) {
      queryParams['period'] = filter.period;
    }

    const results: any[] = await this.activitiesService.getModel().findAll({
      where: queryParams,
      attributes: [
        [Sequelize.literal(`DATE("createdAt")`), 'date'],
        'organisationId',
        'type',
        [Sequelize.fn('count', Sequelize.col('id')), 'sum'],
        [Sequelize.fn('count', Sequelize.json('data.unfollow')), 'unfollowSum'],
        [Sequelize.fn('count', Sequelize.json('data.follow')), 'followSum'],
      ],
      group: ['organisationId', 'type', 'date'],
      order: [[Sequelize.literal('date'), 'ASC']],
      raw: true,
    });

    return results.map(result => ({
      date: new Date(result.date),
      organisationId: result.organisationId,
      type: ActivityType[result.type],
      sum: parseInt(result.sum),
      followSum: parseInt(result.followSum),
      unfollowSum: parseInt(result.unfollowSum),
      webinarId: result.webinarId,
    }));
  }

  async calculateAggregatedAnalytics(
    startDate: Date,
    endDate: Date,
    parentOrganisationId: string,
    childOrganisationIds: string[],
    filter?: {
      type?: ActivityType[];
      period?: AnalyticPeriod;
    },
  ): Promise<AnalyticsCalculationResult[]> {
    // We need to handle two types of activities:
    // 1. Activities directly related to the parent organization
    // 2. Activities related to posts created by child organizations for the parent
    
    // First, get activities directly for the parent organization
    const parentActivitiesQuery = {
      organisationId: parentOrganisationId,
      createdAt: {
        [Op.between]: [startDate, endDate],
      },
    };
    
    if (filter?.type?.length > 0) {
      parentActivitiesQuery['type'] = {
        [Op.in]: filter.type,
      };
    }
    
    if (filter?.period) {
      parentActivitiesQuery['period'] = filter.period;
    }
    
    // Get activities for the parent organization
    const parentResults: any[] = await this.activitiesService.getModel().findAll({
      where: parentActivitiesQuery,
      attributes: [
        [Sequelize.literal(`DATE("createdAt")`), 'date'],
        'organisationId',
        'type',
        [Sequelize.fn('count', Sequelize.col('id')), 'sum'],
        [Sequelize.fn('count', Sequelize.json('data.unfollow')), 'unfollowSum'],
        [Sequelize.fn('count', Sequelize.json('data.follow')), 'followSum'],
        'webinarId',
      ],
      group: ['organisationId', 'type', 'date', 'webinarId'],
      order: [[Sequelize.literal('date'), 'ASC']],
      raw: true,
    });
    
    // Now, get activities related to partner posts
    // These are activities where:
    // 1. The activity is for a child organization
    // 2. The activity has a postId
    // 3. The post has the parent organization as its parentOrgId
    
    const partnerPostActivitiesQuery = `
      SELECT 
        DATE(a."createdAt") as date,
        a."organisationId",
        a.type,
        COUNT(a.id) as sum,
        COUNT(a.data->>'unfollow') as "unfollowSum",
        COUNT(a.data->>'follow') as "followSum",
        a."webinarId"
      FROM "Activities" a
      JOIN "Posts" p ON a."postId" = p.id
      WHERE a."organisationId" IN (:childOrgIds)
      AND p."parentOrgId" = :parentOrgId
      AND a."createdAt" BETWEEN :startDate AND :endDate
      ${filter?.type?.length ? 'AND a.type IN (:types)' : ''}
      ${filter?.period ? 'AND a.period = :period' : ''}
      GROUP BY DATE(a."createdAt"), a."organisationId", a.type, a."webinarId"
      ORDER BY DATE(a."createdAt") ASC
    `;
    
    const replacements = {
      childOrgIds: childOrganisationIds,
      parentOrgId: parentOrganisationId,
      startDate,
      endDate,
      ...(filter?.type?.length ? { types: filter.type } : {}),
      ...(filter?.period ? { period: filter.period } : {}),
    };
    
    let partnerPostResults = [];
    try {
      const queryResult = await this.sequelize.query(
        partnerPostActivitiesQuery,
        {
          replacements,
          type: 'SELECT',
          raw: true,
        }
      );
      
      partnerPostResults = Array.isArray(queryResult) ? queryResult : (queryResult ? [queryResult] : []);
    } catch (error) {
      this.logger.warn('Failed to fetch partner post activities, continuing with empty results', {
        parentOrganisationId,
        childOrganisationIds,
        error: error.message
      });
    }
  
    // Combine both result sets
    const allResults = [
      ...parentResults.map(result => ({
        date: new Date(result.date),
        organisationId: result.organisationId,
        type: ActivityType[result.type],
        sum: parseInt(result.sum),
        followSum: parseInt(result.followSum || '0'),
        unfollowSum: parseInt(result.unfollowSum || '0'),
        webinarId: result.webinarId
      })),
      ...partnerPostResults.map((result: any) => {
        let mappedType = result.type;
        
        if (result.type === 'PostView') {
          mappedType = 'PostSeen';
        }

        return {
          date: new Date(result.date),
          organisationId: parentOrganisationId,
          type: ActivityType[mappedType] || ActivityType[result.type],
          sum: parseInt(result.sum),
          followSum: parseInt(result.followSum || '0'),
          unfollowSum: parseInt(result.unfollowSum || '0'),
          webinarId: result.webinarId
        };
      })
    ];
    
    // Group and aggregate results by date and type
    const aggregatedResults = new Map();
    
    allResults.forEach(result => {
      const key = `${result.date.toISOString().split('T')[0]}_${result.type}_${result.webinarId || 'null'}`;
      
      if (!aggregatedResults.has(key)) {
        aggregatedResults.set(key, {
          date: result.date,
          organisationId: parentOrganisationId,
          type: result.type,
          sum: 0,
          followSum: 0,
          unfollowSum: 0,
          webinarId: result.webinarId
        });
      }
      
      const existing = aggregatedResults.get(key);
      existing.sum += result.sum;
      existing.followSum += result.followSum;
      existing.unfollowSum += result.unfollowSum;
    });
    
    return Array.from(aggregatedResults.values());
  }
}
