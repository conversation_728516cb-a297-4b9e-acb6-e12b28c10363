import { UseGuards, Inject, forwardRef } from '@nestjs/common';
import { Args, Resolver, Query } from '@nestjs/graphql';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';

import { AnalyticsService } from './analytics.service';
import { GqlAuthGuard } from '../authz/graphql-auth.guard';
import {
  CurrentUser,
  ICurrentUser,
} from '../common/decorators/current-user.decorator';
import {
  CalculateAnalyticsArgs,
  AnalyticsResponse,
} from './args/analytics.args';
import { ErrorHelper } from '../common/helpers/error';

@Resolver()
export class AnalyticsResolver {
  @Inject(forwardRef(() => AnalyticsService))
  private readonly analyticsService: AnalyticsService;
  @Inject(WINSTON_MODULE_PROVIDER)
  private readonly logger: Logger;
  @Inject()
  private readonly errorHelper: ErrorHelper;

  @Query(() => AnalyticsResponse)
  @UseGuards(GqlAuthGuard)
  analytics(
    @CurrentUser() user: ICurrentUser,
    @Args() calculateAnalyticArgs: CalculateAnalyticsArgs,
  ): Promise<AnalyticsResponse> {
    this.logger.verbose('AnalyticsResolver.analytics (query)', {
      user: user.toLogObject(),
      calculateAnalyticArgs,
    });

    return this.analyticsService.calculateAnalytics(
      calculateAnalyticArgs.startDate,
      calculateAnalyticArgs.endDate,
      calculateAnalyticArgs.organisationId,
      calculateAnalyticArgs.webinarId,
    );
  }

  @Query(() => AnalyticsResponse)
  @UseGuards(GqlAuthGuard)
  aggregatedAnalytics(
    @CurrentUser() user: ICurrentUser,
    @Args() calculateAnalyticArgs: CalculateAnalyticsArgs,
  ): Promise<AnalyticsResponse> {
    this.logger.verbose('AnalyticsResolver.aggregatedAnalytics (query)', {
      user: user.toLogObject(),
      calculateAnalyticArgs,
    });

    return this.analyticsService.calculateAggregatedAnalytics(
      calculateAnalyticArgs.startDate,
      calculateAnalyticArgs.endDate,
      calculateAnalyticArgs.organisationId,
    );
  }
}
