import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';

import { AnalyticsResponse } from './args/analytics.args';
import { AnalyticsRepository } from './analytics.repository';
import { AnalyticsServiceHelper } from '../analytics/helpers/analytics.service.helper';
import { PartnerOrganisationsService } from '../partner-organisations/partner-organisations.service';
import { PartnerOrganisationStatus } from '../partner-organisations/args/partner-organisations.args';

@Injectable()
export class AnalyticsService {
  @Inject(forwardRef(() => AnalyticsRepository))
  private readonly analyticsRepository: AnalyticsRepository;
  @Inject(forwardRef(() => AnalyticsServiceHelper))
  private readonly analyticsServiceHelper: AnalyticsServiceHelper;
  @Inject(forwardRef(() => PartnerOrganisationsService))
  private readonly partnerOrganisationsService: PartnerOrganisationsService;
  @Inject(WINSTON_MODULE_PROVIDER)
  private readonly logger: Logger;

  async calculateAnalytics(
    startDate: Date,
    endDate: Date,
    organisationId: string,
    webinarId?: string,
  ): Promise<AnalyticsResponse> {
    const calculationResults =
      await this.analyticsRepository.calculateAnalytics(
        startDate,
        endDate,
        organisationId,
        webinarId,
        {},
      );
    return this.analyticsServiceHelper.getAnalyticsResponse(
      startDate,
      endDate,
      calculationResults,
    );
  }

  async calculateAggregatedAnalytics(
    startDate: Date,
    endDate: Date,
    parentOrganisationId: string,
  ): Promise<AnalyticsResponse> {
    const partnerships = await this.partnerOrganisationsService.findByParentOrgId(
      parentOrganisationId,
      { status: [PartnerOrganisationStatus.Approved] }
    );
    
    const childOrgIds = partnerships.map(p => p.childOrgId);
    
    if (childOrgIds.length === 0) {
      this.logger.debug('No approved child organizations found for parent', { parentOrganisationId });
    }
    
    const calculationResults = await this.analyticsRepository.calculateAggregatedAnalytics(
      startDate,
      endDate,
      parentOrganisationId,
      childOrgIds
    );
    
    return this.analyticsServiceHelper.getAnalyticsResponse(
      startDate,
      endDate,
      calculationResults
    );
  }
}
