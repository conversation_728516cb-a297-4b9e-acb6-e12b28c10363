import { join } from 'path';
import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { GraphQLModule } from '@nestjs/graphql';
import { RedisModule } from 'nestjs-redis';
import {
  utilities as nestWinstonModuleUtilities,
  WinstonModule,
} from 'nest-winston';
import winston from 'winston';
import { LoggingWinston } from '@google-cloud/logging-winston';
import { SendGridModule } from '@ntegral/nestjs-sendgrid';
import { ScheduleModule } from '@nestjs/schedule';

import config from './config/config';
import { ProfilesModule } from './profiles/profiles.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthZeroModule } from './authz/authz.module';
import { CommonModule } from './common/common.module';
import { ConnectionsModule } from './connections/connections.module';
import { ConnectionRequestsModule } from './connection-requests/connection-requests.module';
import { NotificationsModule } from './notifications/notifications.module';
import { PubSubModule } from './pubsub/pubsub.module';
import { OrganisationsModule } from './organisations/organisations.module';
import { MembershipsModule } from './memberships/memberships.module';
import { FollowersModule } from './followers/followers.module';
import { CloudinaryModule } from './cloudinary/cloudinary.module';
import { ExperiencesModule } from './experiences/experiences.module';
import { ApolloComplexityPlugin } from './common/plugins/complexity.plugin';
import { PostsModule } from './posts/posts.module';
import { EventsModule } from './events/events.module';
import { EventsInvitationsModule } from './event-invitations/event-invitations.module';
import { ActivitiesModule } from './activities/activities.module';
import { AnalyticsModule } from './analytics/analytics.module';
import { EmailModule } from './email/email.module';
import { ChatModule } from './chat/chat.module';
import { PartnershipRequestsModule } from './partnership-requests/partnership-requests.module';
import { PartnershipsModule } from './partnerships/partnerships.module';
import { IncentivesModule } from './incentives/incentives.module';
import { IncentiveParticipantsModule } from './incentive-participants/incentive-participants.module';
import { IncentiveBookingsModule } from './incentive-bookings/incentive-bookings.module';
import { WebinarBroadcastModule } from './webinar-broadcast/webinar-broadcast.module';
import { WebinarParticipantsModule } from './webinar-participants/webinar-participants.module';
import { WebinarsModule } from './webinars/webinars.module';
import { EmailInvitationsResolver } from './email-invitations/email-invitations.resolver';
import { EmailInvitationsModule } from './email-invitations/email-invitations.module';
import { StreamModule } from './feeds/stream.module';
import { StreamFollowersModule } from './feeds-followers/stream-followers.module';
import { StreamOrganisationsModule } from './feeds-organisations/stream-organisations.module';
import { StreamPostsModule } from './feeds-posts/stream-posts.module';
import { StreamWebinarsModule } from './feeds-webinars/stream-webinars.module';
import { TasksService } from './app.cron';
import { StreamWebinarParticipantsModule } from './feeds-webinar-participants/stream-webinar-participants.module';
import { StreamEventsModule } from './feeds-events/stream-events.module';
import { StreamEventsInvitationsModule } from './feeds-event-invitations/stream-events-invitations.module';
import { StreamIncentivesModule } from './feeds-incentives/stream-incentives.module';
import { StreamIncentiveParticipantsModule } from './feeds-incentive-participants/stream-incentive-participants.module';
import { StreamUpdatesModule } from './feeds-updates/stream-updates.module';
import { DestinationPagesModule } from './explore-pages/explore-pages.module';
import { AutofollowModule } from './autofollow/autofollow.module';
import { SuggestFollowModule } from './suggest-follow/suggest-follow.module';
import { AutocompleteModule } from './autocomplete/autocomplete.module';
import { SubscriptionsModule } from './subscriptions/subscriptions.module';
import { PaymentTransactionsModule } from './payment-transactions/payment-transactions.module';
import { SubscriptionWebhookModule } from './subscription-webhooks/subscription-webhook.module';
import { SubscriptionWebhookController } from './subscription-webhooks/subscription-webhook.controller';
import { StripeHelper } from './subscriptions/helpers/stripe';
import { DocsbotAIModule } from './docsbot-ai/docsbot-ai.module';
import { WebinarParticipantController } from './webinar-participants/webinar-participants.controller';
import { IncentiveParticipantsController } from './incentive-participants/incentive-participants.controller';
import { EventInvitationsController } from './event-invitations/event-invitations.controller';
import { NotificationsController } from './notifications/notifications.controller';
import { PostsController } from './posts/posts.controller';
import { GettingStartedStepsModule } from './getting-started-steps/getting-started-steps.module';
import { LoyaltyPointsModule } from './loyalty-points/loyalty-points.module';
import { AchievementsModule } from './achievements/achievements.module';
import { MuxModule } from './mux/mux.module';
import { DatabaseMonitoringService } from './common/services/db-monitoring.service';
import { PartnerOrganisationsModule } from './partner-organisations/partner-organisations.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    GraphQLModule.forRoot({
      context: ({ req, res, payload, connection }) => ({
        req,
        res,
        payload,
        connection,
      }),
      subscriptions: {
        onConnect: (
          connectionParams: { [key: string]: any },
          websocket: { [key: string]: any },
        ) => ({
          headers: {
            ...websocket?.upgradeReq?.headers,
            authorization: connectionParams?.Authorization,
          },
        }),
      },
      installSubscriptionHandlers: true,
      autoSchemaFile: !!process.env.AUTO_SCHEMA_FILE
        ? true
        : join(process.cwd(), 'src/schema.gql'),
      fieldResolverEnhancers: ['interceptors'],
      plugins: [new ApolloComplexityPlugin(150)],
    }),
    SequelizeModule.forRoot({
      dialect: 'postgres',
      host: config.DB_HOST,
      port: config.DB_PORT,
      username: config.DB_USER,
      password: config.DB_PASS,
      database: config.DB_NAME,
      autoLoadModels: true,
      synchronize: false,
      logging: false,
      pool: {
        max: 75,
        min: 15,
        acquire: 60000,
        idle: 30000,
        evict: 1000,
      },
    }),
    WinstonModule.forRoot({
      level: config.LOG_LEVEL,
      transports:
        config.NODE_ENV === 'production'
          ? [new winston.transports.Console(), new LoggingWinston()]
          : [
              new winston.transports.Console({
                format: winston.format.combine(
                  winston.format.timestamp(),
                  nestWinstonModuleUtilities.format.nestLike(),
                ),
              }),
            ],
    }),
    SendGridModule.forRoot({
      apiKey: config.SENDGRID_API_KEY,
    }),
    RedisModule.register({
      url: `redis://${config.REDIS_HOST}:${config.REDIS_PORT}`,
      keyPrefix: config.DB_NAME,
    }),
    AuthZeroModule,
    ProfilesModule,
    ConnectionRequestsModule,
    ConnectionsModule,
    NotificationsModule,
    CommonModule,
    PubSubModule,
    OrganisationsModule,
    MembershipsModule,
    FollowersModule,
    CloudinaryModule,
    ExperiencesModule,
    PostsModule,
    EventsModule,
    EventsInvitationsModule,
    ActivitiesModule,
    ChatModule,
    AnalyticsModule,
    EmailModule,
    PartnershipRequestsModule,
    PartnershipsModule,
    IncentivesModule,
    IncentiveParticipantsModule,
    IncentiveBookingsModule,
    WebinarBroadcastModule,
    WebinarsModule,
    WebinarParticipantsModule,
    EmailInvitationsModule,
    StreamModule,
    StreamFollowersModule,
    StreamOrganisationsModule,
    StreamPostsModule,
    StreamWebinarsModule,
    StreamWebinarParticipantsModule,
    StreamEventsModule,
    StreamEventsInvitationsModule,
    StreamIncentivesModule,
    StreamIncentiveParticipantsModule,
    StreamUpdatesModule,
    DestinationPagesModule,
    AutofollowModule,
    SuggestFollowModule,
    AutocompleteModule,
    SubscriptionsModule,
    PaymentTransactionsModule,
    SubscriptionWebhookModule,
    StripeHelper,
    DocsbotAIModule,
    GettingStartedStepsModule,
    LoyaltyPointsModule,
    AchievementsModule,
    MuxModule,
    PartnerOrganisationsModule,
  ],
  controllers: [
    AppController,
    SubscriptionWebhookController,
    WebinarParticipantController,
    IncentiveParticipantsController,
    EventInvitationsController,
    NotificationsController,
    PostsController,
  ],
  providers: [
    AppService,
    TasksService,
    EmailInvitationsResolver,
    StripeHelper,
    DatabaseMonitoringService,
  ],
})
export class AppModule {
  constructor(private readonly dbMonitoringService: DatabaseMonitoringService) {
    // Start database monitoring when the app starts
    this.dbMonitoringService.setupMonitoring();
  }
}
