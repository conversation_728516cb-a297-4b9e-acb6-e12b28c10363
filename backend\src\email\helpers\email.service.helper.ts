import { EmailInvitationsService } from './../../email-invitations/email-invitations.service';
import moment from 'moment';
import { Op, Sequelize } from 'sequelize';
import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { InjectSendGrid, SendGridService } from '@ntegral/nestjs-sendgrid';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';

import { ProfilesService } from '../../profiles/profiles.service';
import { Notification } from '../../notifications/models/notification.model';
import { EmailTemplate, EmailStatus } from '../args/email.args';
import { ChatService, UserResult } from '../../chat/chat.service';
import { ConnectionsService } from '../../connections/connections.service';
import { Profile } from '../../profiles/models/profile.model';
import config from '../../config/config';
import { EmailService } from '../email.service';
import { <PERSON><PERSON>ataRequired } from '@sendgrid/mail/src/mail';
import { ICurrentUser } from '../../common/decorators/current-user.decorator';
import { NotificationType } from '../../notifications/args/notifications.args';
import { WebinarParticipant } from '../../webinar-participants/models/webinar-participant.model';
import { Webinar } from '../../webinars/models/webinar.model';
import { Organisation } from '../../organisations/models/organisation.model';
import { PartnershipRequest } from '../../partnership-requests/models/partnership-request.model';
import { Membership } from '../../memberships/models/membership.model';
import { Follower } from '../../followers/models/follower.model';
import { EventInvitation } from '../../event-invitations/models/event-invitation.model';
import { Event } from '../../events/models/event.model';
import { IncentiveParticipant } from '../../incentive-participants/models/incentive-participant.model';
import { Incentive } from '../../incentives/models/incentive.model';
import { NotificationHelper } from './notificationLayoutHelper';
import { NotificationsService } from '../../notifications/notifications.service';
import { OrganisationsService } from '../../organisations/organisations.service';

@Injectable()
export class EmailServiceHelper {
  @Inject(forwardRef(() => EmailService))
  private readonly emailService: EmailService;
  @Inject(forwardRef(() => ProfilesService))
  private readonly profilesService: ProfilesService;
  @Inject(forwardRef(() => ConnectionsService))
  private readonly connectionsService: ConnectionsService;
  @Inject(forwardRef(() => ChatService))
  private readonly chatService: ChatService;
  @Inject(forwardRef(() => EmailInvitationsService))
  private readonly emailInvitationsService: EmailInvitationsService;
  @Inject(forwardRef(() => NotificationsService))
  private readonly notificationsService: NotificationsService;
  @Inject(forwardRef(() => OrganisationsService))
  private readonly organisationsService: OrganisationsService;
  @Inject()
  private readonly sequelize: Sequelize;
  @InjectSendGrid() private readonly sendGridClient: SendGridService;
  @Inject(WINSTON_MODULE_PROVIDER)
  private readonly logger: Logger;

  getNotificationEmailHTML(notifications: any): string {
    const notificationHTML = notifications
      .sort((a: any, b: any) => {
        return (
          moment(b.createdAt).toDate().getTime() -
          moment(a.createdAt).toDate().getTime()
        );
      })
      .slice(0, 5)
      .map(el => {
        const notifData = NotificationHelper.getConfiguration(el);
        const layout = NotificationHelper.getNotificationLayout({
          ...notifData,
          createdAt: el.createdAt,
        });
        return layout;
      })
      .join('');

    return `
      <div style="display: flex; width: 100%;">
        <table style="max-width: 400px; margin: auto;">
          <tbody>
            ${notificationHTML!!}
          </tbody>
        </table>
      </div>
      `;
  }

  getSubject(notifications: any): string {
    const notification = notifications.sort((a: any, b: any) => {
      return (
        moment(b.createdAt).toDate().getTime() -
        moment(a.createdAt).toDate().getTime()
      );
    })[0];
    switch (notification.type) {
      case NotificationType.InvitationAccepted: {
        return `You are now connected with ${notification.profile?.name}`;
      }
      case NotificationType.InvitationReceived: {
        return `${notification.profile?.name} wants to connect with you`;
      }
      case NotificationType.OrganisationOwnershipRequested: {
        return `${notification.profile?.name} requested you take over as Owner of ${notification.membership?.organisation?.name}`;
      }
      case NotificationType.OrganisationOwnershipAccepted: {
        return `${notification.profile?.name} agreed to become Owner of ${notification.membership?.organisation?.name}`;
      }
      case NotificationType.PartnershipRequestReceived: {
        return `${notification.partnershipRequest?.senderOrganisation?.name} requested to become a Connected Organisation with ${notification.partnershipRequest?.receiverOrganisation?.name}`;
      }
      case NotificationType.PartnershipRequestApproved: {
        return `${notification.partnershipRequest?.receiverOrganisation?.name} approved a connection request from ${notification.partnershipRequest?.senderOrganisation?.name}`;
      }
      case NotificationType.PartnershipRequestApprovedByOrganisation: {
        return `${notification.partnershipRequest?.senderOrganisation?.name} is now a Connected Organisation with ${notification.partnershipRequest?.receiverOrganisation?.name}`;
      }
      case NotificationType.MembershipRequested: {
        return `${notification.profile?.name} requested to join ${notification.membership?.organisation?.name}`;
      }
      case NotificationType.MembershipAccepted: {
        return `You've been verified as an employee of ${notification.membership?.organisation?.name}`;
      }
      case NotificationType.FollowerAccepted: {
        return `${notification.follower?.organisation?.name} accepted your follow request`;
      }
      case NotificationType.MembershipPermissionsUpdated: {
        // You have been assigned as [role] of [organisation name].
        return `You've been assigned as ${(
          notification?.data?.permissions ||
          notification.membership?.permissions ||
          []
        ).join(', ')} of ${notification.membership?.organisation?.name}`;
      }
      case NotificationType.EventInvitationByGuest: {
        return `${notification.eventInvitation?.inviterProfile?.name} invited you to an event: ${notification.eventInvitation?.event?.name}`;
      }
      case NotificationType.EventInvitationByHosts: {
        return `${notification.eventInvitation?.event?.organisation?.name} invited you to an event: ${notification.eventInvitation?.event?.name}`;
      }
      case NotificationType.EventInvitationApproved: {
        return `${notification.eventInvitation?.event?.organisation?.name} accepted your request to attend ${notification.eventInvitation?.event?.name}`;
      }
      case NotificationType.NewEventUpdate: {
        return `${notification.eventInvitation?.event?.organisation?.name} posted an update in ${notification.eventInvitation?.event?.name}`;
      }
      case NotificationType.EventLocationChanged:
        return `${notification.eventInvitation?.event?.organisation?.name} updated the location of ${notification.eventInvitation?.event?.name}`;
      case NotificationType.EventDateTimeChanged:
        return `${notification.eventInvitation?.event?.organisation?.name} made a change to the date / time of ${notification.eventInvitation?.event?.name}`;
      case NotificationType.IncentiveDateChanged:
        return `${notification.incentiveParticipant?.incentive?.organisation?.name} made a change to the dates of ${notification.incentiveParticipant?.incentive?.name}`;
      case NotificationType.IncentiveInvitationByParticipant:
        return `${notification.incentiveParticipant?.inviterProfile?.name} invited you to an incentive: ${notification.incentiveParticipant?.incentive?.name}`;
      case NotificationType.IncentiveInvitationByHosts:
        return `${notification.incentiveParticipant?.incentive?.organisation?.name} invited you to an incentive: ${notification.incentiveParticipant?.incentive?.name}`;
      case NotificationType.IncentiveRegistrationRequested:
        return `${notification.incentiveParticipant?.profile?.name} requested to register for ${notification.incentiveParticipant?.incentive?.name}`;
      case NotificationType.IncentiveRegistrationApproved:
        return `${notification.incentiveParticipant?.incentive?.organisation?.name} accepted your request to register for ${notification.incentiveParticipant?.incentive?.name}`;
      case NotificationType.NewIncentiveUpdate: {
        return `${notification.incentiveParticipant?.incentive?.organisation?.name} posted an update in ${notification.incentiveParticipant?.incentive?.name}`;
      }
      case NotificationType.WebinarDateChanged:
        return `${notification.webinarParticipant?.webinar?.organisation?.name} made a change to the date / time of ${notification.webinarParticipant?.webinar?.name}`;
      case NotificationType.WebinarInvitationByParticipant:
        return `${notification.webinarParticipant?.inviterProfile?.name} invited you to a webinar: ${notification.webinarParticipant?.webinar?.name}`;
      case NotificationType.WebinarInvitationByHosts:
        return `${notification.webinarParticipant?.webinar?.organisation?.name} invited you to a webinar: ${notification.webinarParticipant?.webinar?.name}`;
      case NotificationType.WebinarRegistrationRequested:
        return `${notification.webinarParticipant?.profile?.name} requested to register for ${notification.webinarParticipant?.webinar?.name}`;
      case NotificationType.WebinarRegistrationApproved:
        return `${notification.webinarParticipant?.webinar?.organisation?.name} accepted your request to register for ${notification.webinarParticipant?.webinar?.name}`;
      case NotificationType.NewWebinarUpdate: {
        return `${notification.webinarParticipant?.webinar?.organisation?.name} posted an update in ${notification.webinarParticipant?.webinar?.name}`;
      }
      case NotificationType.WebinarParticipantAddedAsHostAdmin: {
        return `${notification.webinarParticipant?.webinar?.organisation?.name} assigned you as a Host Admin of ${notification.webinarParticipant?.webinar?.name}`;
      }
      case NotificationType.WebinarParticipantAddedAsHost: {
        return `${notification.webinarParticipant?.webinar?.organisation?.name} assigned you as a Host of ${notification.webinarParticipant?.webinar?.name}`;
      }
      case NotificationType.WebinarParticipantAddedAsSpeaker: {
        return `${notification.webinarParticipant?.webinar?.organisation?.name} assigned you as a Speaker at ${notification.webinarParticipant?.webinar?.name}`;
      }
      case NotificationType.SuggestFollow: {
        return `You've had a suggestion to follow some new organisations that travel industry professionals similar to you also follow`;
      }
      case NotificationType.PostComment: {
        const countOfUsers = notification.data?.users?.length;

        return `${notification.data?.users?.[0]?.name} ${
          countOfUsers === 1 ? '' : ' & others'
        } commented on your post`;
      }
      case NotificationType.CommentReact: {
        return `${
          notification.data?.users?.length && notification.data.users[0].name
        } reacted to your comment on ${
          notification?.organisation?.name ?? ''
        }'s post`;
      }
      case NotificationType.PostShared: {
        return `${notification.organisation?.name} reshared your post with their network`;
      }
      case NotificationType.CommentMention: {
        return `${notification.profile?.name} mentioned you in a comment`;
      }
      case NotificationType.PostMention: {
        return `${notification.organisation?.name} mentioned you in a post`;
      }
      case NotificationType.OrgPostMention: {
        return `${notification.organisation?.name} mentioned ${notification?.data?.organisation?.name} in a post`;
      }
      case NotificationType.OrgCommentMention: {
        return `${notification.profile?.name} mentioned ${notification?.data?.organisation?.name} in a comment`;
      }
      case NotificationType.NewFollower: {
        const countOfUsers = notification.data?.users?.length;

        return `${notification.data?.users?.[0]?.name} ${
          countOfUsers === 1 ? '' : ' & others'
        } requested to follow ${notification?.organisation?.name}`;
      }

      case NotificationType.NewFollower: {
        const usersFullNames = notification.data?.users?.map(
          ({ name }: { name: string }) => name,
        );
        const countOfUsers = notification.data?.users?.length;
        const listOfNames =
          usersFullNames[0] + countOfUsers > 1 ? `` : ` & others`;

        return ` ${listOfNames} requested to follow ${notification?.organisation?.name}`;
      }
      case NotificationType.RecurringPaymentReminder: {
        return `Your paid subscription with ${notification.partnershipRequest?.senderOrganisation?.name} will renew soon`;
      }
      case NotificationType.MembershipAutoApprove: {
        return `${notification.profile?.name} joined ${notification.membership?.organisation?.name} using a signup invite link`;
      }
      case NotificationType.ParentMembershipRequested: {
        return `${notification.profile?.name} requested to become a member of ${notification.membership?.organisation?.name}`;
      }
      case NotificationType.ParentMembershipAccepted: {
        return `You have been verified as a member of ${notification.membership?.organisation?.name}`;
      }
      case NotificationType.ParentMembershipDeclined: {
        return `You have been declined as a member of ${notification.organisation?.name}`;
      }
      case NotificationType.OrgPostReminder7Days: {
        return `It's been a week since ${notification.organisation?.name}'s last Hablo post. Keep your community engaged`;
      }
      case NotificationType.OrgPostReminder14Days: {
        return `${notification.organisation?.name}'s community on Hablo is waiting to hear from you`;
      }
      case NotificationType.InactiveUserReminder: {
        const organisationNames = notification.data?.map(
          ({ organisationName }: { organisationName: string }) =>
            organisationName,
        );
        const countOfOrganisations = organisationNames.length;
        const listOfNames =
          countOfOrganisations === 3
            ? organisationNames[0] +
              ', ' +
              organisationNames[1] +
              ' and ' +
              organisationNames[2]
            : countOfOrganisations === 2
            ? organisationNames[0] + ' and ' + organisationNames[1]
            : countOfOrganisations === 1
            ? organisationNames[0]
            : organisationNames[0];

        return `${listOfNames} shared new posts that you’ve missed. Login to view latest updates in your feed.`;
      }
      case NotificationType.InboxMessage: {
        return `You have a new message from ${notification.profile?.name}`;
      }
      case NotificationType.DailyLoginStreak: {
        return `You've hit a new daily streak!`;
      }
      case NotificationType.NewAchievement: {
        return `You've unlocked a new Achievement!`;
      }
      case NotificationType.NewTier: {
        return `Your Club Hablo tier has changed!`;
      }
      case NotificationType.NewOrganisationAchievement: {
        return `You've unlocked a new Organisation Achievement!`;
      }
      case NotificationType.HighFiveAchievement: {
        return `You received a High Five!`;
      }
      case NotificationType.PostReact: {
        return 'Someone reacted to your Post';
      }
      case NotificationType.WeeklySummary: {
        return 'Here`s how many points you earned this week';
      }
      case NotificationType.OrgPartnerRequestReceived:
        return `${notification.data?.senderOrgName} has requested to become a Connected Organisation with ${notification.data?.receiverOrgName}`;
      case NotificationType.OrgPartnerAcceptedSender:
        return `${notification.data?.receiverOrgName} has approved your request to become a Connected Organisation`;
      case NotificationType.OrgPartnerAcceptedReceiver:
        return `${notification.data?.senderOrgName} is now a Connected Organisation with ${notification.data?.receiverOrgName}`;
      default:
        return `You have new unread notifications`;
    }
  }

  async processNotificationEmails(): Promise<void> {
    const timezoneOffsets: number[] = [];

    for (let i = -12; i <= 13; i += 0.5) {
      timezoneOffsets.push(i * 60);
    }

    const utcNow = new Date();
    utcNow.setTime(utcNow.getTime() + utcNow.getTimezoneOffset() * 60000);

    timezoneOffsets.forEach(async tzOffset => {
      const tzNow = new Date();
      tzNow.setTime(utcNow.getTime() - tzOffset * 60000);

      const tzHours = tzNow.getHours();
      const tzMinutes = tzNow.getMinutes();

      if (tzHours === 9 && tzMinutes >= 30 && tzMinutes < 59) {
        this.logger.info('EmailService.sendEveryDay0930AMEmails');
        await this.sendEveryDay0930AMEmails(tzOffset);
      }

      if (tzHours === 12 && tzMinutes >= 30 && tzMinutes < 59) {
        this.logger.info('EmailService.sendEveryDay1230PMEmails');
        await this.sendEveryDay1230PMEmails(tzOffset);
      }

      if (tzHours === 15 && tzMinutes >= 30 && tzMinutes < 59) {
        this.logger.info('EmailService.sendEveryDay1530PMEmails');
        await this.sendEveryDay1530PMEmails(tzOffset);
      }
    });
  }

  async processUnreadMessagesEmails(): Promise<void> {
    const timezoneOffsets: number[] = [];

    for (let i = -12; i <= 13; i += 0.5) {
      timezoneOffsets.push(i * 60);
    }

    const utcNow = new Date();
    utcNow.setTime(utcNow.getTime() + utcNow.getTimezoneOffset() * 60000);

    const tzOffsets = [];

    timezoneOffsets.forEach(async tzOffset => {
      const tzNow = new Date();
      tzNow.setTime(utcNow.getTime() - tzOffset * 60000);

      const tzHours = tzNow.getHours();
      const tzMinutes = tzNow.getMinutes();

      if (tzHours >= 8 && tzHours < 20) {
        tzOffsets.push(tzOffset);
      }
    });

    await this.sendUnreadMessageEmails(tzOffsets);
  }

  private async sendUnreadMessageEmails(tzOffsets: number[]): Promise<void> {
    // during working hours of various timezones, get all profiles not active in last hour

    const profiles = await this.profilesService.findAll(
      {
        timezoneOffset: {
          [Op.in]: tzOffsets,
        },
        lastActivityAt: {
          [Op.lte]: moment().add(-1, 'hours').toDate(),
        },
        id: {
          [Op.notLike]: '%deleted%',
        },
      },
      {
        attributes: ['id', 'email', 'name', 'lastActivityAt'],
      },
    );

    this.logger.info(
      'EmailService.sendUnreadMessageEmails (Number of Profiles not active in last hour)',
      {
        numOfProfiles: profiles.length,
      },
    );

    if (profiles.length === 0) return;

    // for profiles found above, find 'new message' emails that have been sent today

    const emailsToday = await this.emailService.findAll(
      {
        [Op.and]: [
          {
            templateId: EmailTemplate.NewMessages,
            profileId: {
              [Op.in]: profiles.map(p => p.id),
            },
            createdAt: {
              [Op.gte]: moment().subtract(12, 'hours').toDate(),
            },
          },
        ],
      },
      {
        attributes: ['profileId', 'createdAt'],
      },
    );

    // Only include profiles that have received less than 4 'new message' emails today

    const emailFilteredProfiles = [];

    for (const profile of profiles) {
      const alreadySentEmails = emailsToday.filter(
        email => email.profileId === profile.id,
      );

      if (alreadySentEmails.length < 4) {
        emailFilteredProfiles.push(profile);
      }
    }

    this.logger.info(
      'EmailService.sendUnreadMessageEmails emailFilteredProfiles.length',
      {
        TotalEmailFilteredProfiles: emailFilteredProfiles.length,
      },
    );

    // Get unread channels info for email filtered profiles

    if (emailFilteredProfiles.length === 0) return;

    const userResults = await this.chatService.getUsers(emailFilteredProfiles);

    this.logger.info(
      'EmailService.sendUnreadMessageEmails userResults.length',
      {
        userResults: userResults.length,
      },
    );

    for (const userResult of userResults) {
      // only send email if unread messages

      if (userResult.unread_channels.count > 0) {
        // only send email if last message received after user last active
        // AND newest message received after last email about new messages

        const lastEmailToday = emailsToday
          .filter(email => email.profileId === userResult.profile.id)
          .pop();

        this.logger.info('EmailService.sendUnreadMessageEmails (consider)', {
          userId: userResult.profile.id,
          latestMessage: userResult.latest_message_created_at,
          lastActivityAt: userResult.profile.lastActivityAt,
          lastEmailAt: lastEmailToday?.createdAt,
        });

        if (
          userResult.latest_message_created_at >
            userResult.profile.lastActivityAt &&
          (lastEmailToday?.createdAt
            ? userResult.latest_message_created_at > lastEmailToday?.createdAt
            : true)
        ) {
          // this.logger.info(
          //   'EmailService.sendUnreadMessageEmails (pass timing + last email checks)',
          //   {
          //     userId: userResult.profile.id,
          //   },
          // );
          const text = await this.getNewMessageText(userResult);

          if (text.length > 0) {
            await this.sendEmail({
              profileId: userResult.profile.id,
              to: userResult.profile.email,
              templateId: EmailTemplate.NewMessages,
              dynamicTemplateData: {
                name: userResult.profile.name,
                text,
              },
            });
          }
        }
      }
    }
  }

  private getIncludeParams() {
    return [
      {
        model: WebinarParticipant,
        as: 'webinarParticipant',
        include: [
          {
            model: Webinar,
            as: 'webinar',
            include: [
              {
                model: Organisation,
                as: 'organisation',
                attributes: ['name', 'id', 'vanityId', 'image'],
              },
            ],
          },
          {
            model: Profile,
            as: 'profile',
            attributes: ['id', 'name', 'image', 'lastOnlineAt'],
          },
          {
            model: Profile,
            as: 'inviterProfile',
            attributes: ['id', 'name', 'image', 'lastOnlineAt'],
          },
        ],
      },
      {
        model: PartnershipRequest,
        as: 'partnershipRequest',
        include: [
          {
            model: Organisation,
            as: 'senderOrganisation',
            attributes: ['name', 'id', 'vanityId', 'image'],
          },
          {
            model: Organisation,
            as: 'receiverOrganisation',
            attributes: ['name', 'id', 'vanityId', 'image'],
          },
        ],
      },
      {
        model: Membership,
        as: 'membership',
        attributes: [
          'id',
          'position',
          'isPrimary',
          'organisationName',
          'organisationId',
          'status',
          'profileId',
        ],
        include: [
          {
            model: Organisation,
            as: 'organisation',
            attributes: ['name', 'id', 'vanityId', 'image'],
          },
          {
            model: Profile,
            as: 'profile',
            attributes: ['id', 'name', 'image', 'lastOnlineAt'],
          },
        ],
      },
      {
        model: EventInvitation,
        as: 'eventInvitation',
        include: [
          {
            model: Event,
            as: 'event',
            include: [
              {
                model: Organisation,
                as: 'organisation',
                attributes: ['name', 'id', 'vanityId', 'image'],
              },
            ],
          },
          {
            model: Profile,
            as: 'profile',
            attributes: ['id', 'name', 'image', 'lastOnlineAt'],
          },
          {
            model: Profile,
            as: 'inviterProfile',
            attributes: ['id', 'name', 'image', 'lastOnlineAt'],
          },
        ],
      },
      {
        model: IncentiveParticipant,
        as: 'incentiveParticipant',
        include: [
          {
            model: Incentive,
            as: 'incentive',
            include: [
              {
                model: Organisation,
                as: 'organisation',
                attributes: ['name', 'id', 'vanityId', 'image'],
              },
            ],
          },
          {
            model: Profile,
            as: 'profile',
            attributes: ['id', 'name', 'image', 'lastOnlineAt'],
          },
          {
            model: Profile,
            as: 'inviterProfile',
            attributes: ['id', 'name', 'image', 'lastOnlineAt'],
          },
        ],
      },
      {
        model: Follower,
        as: 'follower',
        include: [
          {
            model: Organisation,
            as: 'organisation',
            attributes: ['name', 'id', 'vanityId', 'image'],
          },
          {
            model: Profile,
            as: 'profile',
            attributes: ['id', 'name', 'image', 'lastOnlineAt'],
          },
        ],
      },
      {
        model: Organisation,
        as: 'organisation',
        attributes: ['name', 'id', 'vanityId', 'image'],
      },
      {
        model: Profile,
        as: 'profile',
        attributes: ['id', 'name', 'image', 'lastOnlineAt'],
      },
    ];
  }

  async sendEveryDay0930AMEmails(tzOffset: number): Promise<void> {
    const previousDay0430PM = moment()
      .add(-1, 'days')
      .set('hours', 16)
      .set('minutes', 30)
      .set('seconds', 0)
      .toDate();
    previousDay0430PM.setTime(
      previousDay0430PM.getTime() -
        (tzOffset - previousDay0430PM.getTimezoneOffset()) * 60000,
    );

    const emailsToday = await this.emailService.findAll(
      {
        [Op.and]: [
          Sequelize.where(
            Sequelize.fn('DATE', Sequelize.col('createdAt')),
            Sequelize.literal('CURRENT_DATE'),
          ),
          {
            templateId: EmailTemplate.NewNotifications,
          },
        ],
      },
      {
        attributes: ['profileId', 'createdAt'],
      },
    );

    //to do: check if received too many emails in last few weeks? or only email about a notification up to 3 times.

    const past6months = moment().add(-180, 'days').toDate();

    const profiles = await this.profilesService.findAll(
      {
        timezoneOffset: tzOffset,
        lastActivityAt: {
          [Op.lt]: previousDay0430PM,
          [Op.gt]: past6months,
        },
        id: {
          [Op.notIn]: emailsToday.map(email => email.profileId),
          [Op.notLike]: '%deleted%',
        },
      },
      {
        attributes: ['id', 'email', 'name'],
      },
    );

    for (const profile of profiles) {
      this.logger.info('EmailService.foundInactiveProfile', {
        profileId: profile.id,
      });

      const notifications = await this.getNotificationsWithOrganisations(
        profile,
      );
      if (notifications.length) {
        this.logger.info('EmailService.sendNewNotificationsEmail.930 ONE', {
          profileId: profile.id,
        });
        try {
          const subject = await this.getSubject(notifications);
          const html = await this.getNotificationEmailHTML(notifications);
          await this.sendEmail({
            profileId: profile.id,
            subject: subject,
            to: profile.email,
            templateId: EmailTemplate.NewNotifications,
            dynamicTemplateData: {
              name: profile.name,
              subject: subject,
              notificationHTML: html,
            },
          });
        } catch (e) {
          this.logger.error(
            `SendEmailConsumer Error: ${e.message}`,
            e.response?.body,
          );
        }
      }
    }
  }

  async sendEveryDay1230PMEmails(tzOffset: number): Promise<void> {
    const previousMidnight = new Date();
    previousMidnight.setHours(0);
    previousMidnight.setMinutes(0);
    previousMidnight.setSeconds(0);
    previousMidnight.setTime(
      previousMidnight.getTime() -
        (tzOffset - previousMidnight.getTimezoneOffset()) * 60000,
    );

    const emailsToday = await this.emailService.findAll(
      {
        [Op.and]: [
          Sequelize.where(
            Sequelize.fn('DATE', Sequelize.col('createdAt')),
            Sequelize.literal('CURRENT_DATE'),
          ),
          {
            templateId: EmailTemplate.NewNotifications,
          },
        ],
      },
      {
        attributes: ['profileId', 'createdAt'],
      },
    );

    const past6months = moment().add(-180, 'days').toDate();

    const profiles = await this.profilesService.findAll(
      {
        timezoneOffset: tzOffset,
        lastActivityAt: {
          [Op.lt]: previousMidnight,
          [Op.gt]: past6months,
        },
        id: {
          [Op.notIn]: emailsToday.map(email => email.profileId),
          [Op.notLike]: '%deleted%',
        },
      },
      {
        attributes: ['id', 'email', 'name'],
      },
    );

    for (const profile of profiles) {
      this.logger.info('EmailService.foundInactiveProfile', {
        profileId: profile.id,
      });

      const notifications = await this.getNotificationsWithOrganisations(
        profile,
      );
      if (notifications.length) {
        this.logger.info('EmailService.sendNewNotificationsEmail.930 ONE', {
          profileId: profile.id,
        });
        try {
          const subject = await this.getSubject(notifications);
          const html = await this.getNotificationEmailHTML(notifications);
          await this.sendEmail({
            profileId: profile.id,
            subject: subject,
            to: profile.email,
            templateId: EmailTemplate.NewNotifications,
            dynamicTemplateData: {
              name: profile.name,
              subject: subject,
              notificationHTML: html,
            },
          });
        } catch (e) {
          this.logger.error(
            `SendEmailConsumer Error: ${e.message}`,
            e.response?.body,
          );
        }
      }
    }
  }

  async sendEveryDay1530PMEmails(tzOffset: number): Promise<void> {
    const today1130AM = new Date();
    today1130AM.setHours(11);
    today1130AM.setMinutes(30);
    today1130AM.setSeconds(0);
    today1130AM.setTime(
      today1130AM.getTime() -
        (tzOffset - today1130AM.getTimezoneOffset()) * 60000,
    );

    const emailsToday = await this.emailService.findAll(
      {
        [Op.and]: [
          Sequelize.where(
            Sequelize.fn('DATE', Sequelize.col('createdAt')),
            Sequelize.literal('CURRENT_DATE'),
          ),
          {
            templateId: EmailTemplate.NewNotifications,
          },
        ],
      },
      {
        attributes: ['profileId', 'createdAt'],
      },
    );

    const past6months = moment().add(-180, 'days').toDate();

    const profiles = await this.profilesService.findAll(
      {
        timezoneOffset: tzOffset,
        lastActivityAt: {
          [Op.lt]: today1130AM,
          [Op.gt]: past6months,
        },
        id: {
          [Op.notIn]: emailsToday.map(email => email.profileId),
          [Op.notLike]: '%deleted%',
        },
      },
      {
        attributes: ['id', 'email', 'name'],
      },
    );

    for (const profile of profiles) {
      this.logger.info('EmailService.foundInactiveProfile', {
        profileId: profile.id,
      });

      const notifications = await this.getNotificationsWithOrganisations(
        profile,
      );
      if (notifications.length) {
        this.logger.info('EmailService.sendNewNotificationsEmail.930 ONE', {
          profileId: profile.id,
        });
        try {
          const subject = await this.getSubject(notifications);
          const html = await this.getNotificationEmailHTML(notifications);
          await this.sendEmail({
            profileId: profile.id,
            subject: subject,
            to: profile.email,
            templateId: EmailTemplate.NewNotifications,
            dynamicTemplateData: {
              name: profile.name,
              subject: subject,
              notificationHTML: html,
            },
          });
        } catch (e) {
          this.logger.error(
            `SendEmailConsumer Error: ${e.message}`,
            e.response?.body,
          );
        }
      }
    }
  }

  private async getNewMessageText(userResult: UserResult): Promise<string> {
    const profile = userResult.profile;
    const channelsCount = userResult.unread_channels.channelIds.length;

    const names = [];
    let groupsText = '';
    let peopleText = '';
    let unreadGroupChannels = 0;

    for (let i = 0; i < channelsCount; i++) {
      const connection = await this.connectionsService.findOne(
        {
          connectionProfileId: profile.id,
          streamChannelId: userResult.unread_channels.channelIds[i],
        },
        {
          includeParams: [
            {
              as: 'profile',
              model: Profile,
              attributes: ['name'],
            },
          ],
        },
      );

      if (connection) {
        names.push(connection.profile.name);
      } else {
        unreadGroupChannels++;
      }
    }

    if (unreadGroupChannels > 0) {
      groupsText = `${unreadGroupChannels} ${
        unreadGroupChannels > 1 ? 'unread group chats' : 'unread group chat'
      }`;
    }

    if (names.length > 0) {
      peopleText = `${names.length} ${
        names.length > 1 ? 'unread chats with' : 'unread chat with'
      } `;

      peopleText += names.join(', ');

      if (names.length > 1) {
        peopleText =
          peopleText.substring(0, peopleText.lastIndexOf(', ')) +
          ' and ' +
          names[names.length - 1];
      }
    }

    if (peopleText === '' && groupsText === '') return '';

    //let unreadMessageChannels = names.length + unreadGroupChannels;
    let andText = peopleText !== '' && groupsText !== '' ? ', plus ' : '';
    let result = `You have ${peopleText}${andText}${groupsText}`;

    return result + '.';
  }

  async sendConnectInvitations(
    user: Pick<ICurrentUser, 'name' | 'profileId'>,
    text: string,
    emails: string[],
  ) {
    try {
      const profile = await this.profilesService.findById(user.profileId);

      for (const email of emails) {
        if (email !== profile.email) {
          await this.sendEmail({
            profileId: user.profileId,
            to: email,
            templateId: EmailTemplate.NewInvitation,
            customFromName: user.name,
            customFromEmail: '<EMAIL>',
            dynamicTemplateData: {
              name: user.name,
              text,
            },
          });

          await this.emailInvitationsService.saveEmailInvitationInfo(
            user.profileId,
            email,
          );
        }
      }
    } catch (e) {
      this.logger.error(
        `SendEmailConsumer Error: ${e.message}`,
        e.response?.body,
      );
    }

    return true;
  }

  async sendEmail(options: {
    profileId: string;
    to: string;
    templateId: EmailTemplate;
    dynamicTemplateData?: any;
    customFromName?: string;
    customFromEmail?: string;
    subject?: string;
  }): Promise<void> {
    const sendGridOptions: MailDataRequired = {
      to: options.to,
      templateId: options.templateId,
      dynamicTemplateData: options.dynamicTemplateData,
      subject: options?.subject ?? '',
      from: {
        email: options.customFromEmail
          ? options.customFromEmail
          : config.SENDGRID_FROM,
        name: options.customFromName
          ? options.customFromName
          : config.SENDGRID_NAME,
      },
      replyTo: config.SENDGRID_REPLY_TO,
    };

    const whitelist = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ];

    this.logger.info('EmailService.sendEmail', {
      profileId: options.profileId,
      email: options.to,
      templateId: options.templateId,
      text: sendGridOptions.text,
    });

    const isWhitelisted = whitelist.includes(options.to);
    const isNewMessages = EmailTemplate.NewMessages === options.templateId;
    const isNewNotifications =
      EmailTemplate.NewNotifications === options.templateId;
    const isEmailInvitation =
      EmailTemplate.NewInvitation === options.templateId;
    const isSuggestFollowEmail =
      EmailTemplate.SuggestFollow === options.templateId;

    const profile = await this.profilesService.findById(options.profileId);

    if (
      !profile.receiveNewMessagesEmails &&
      options.templateId === EmailTemplate.NewMessages
    ) {
      return;
    }

    if (
      !profile.receiveNotificationEmails &&
      options.templateId === EmailTemplate.NewNotifications
    ) {
      return;
    }

    const transaction = await this.sequelize.transaction();

    try {
      const isAllowedToSend =
        isEmailInvitation ||
        ((isNewMessages || isNewNotifications) &&
          (config.SERVICE === 'api' ||
            (isWhitelisted &&
              (config.NODE_ENV === 'development' ||
                config.SERVICE === 'api-beta')))) ||
        (isSuggestFollowEmail &&
          (config.SERVICE === 'api' || config.NODE_ENV === 'development'));

      if (isAllowedToSend) {
        await this.sendGridClient.send(sendGridOptions);
      }

      await this.emailService.create(
        {
          profileId: options.profileId,
          templateId: options.templateId,
          status: isAllowedToSend ? EmailStatus.Sent : EmailStatus.Hold,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();

      this.logger.error(
        `SendEmailConsumer Error: ${e.message}`,
        e.response?.body,
      );
    }
  }

  async getNotificationsWithOrganisations(
    profile: Profile,
  ): Promise<Notification[]> {
    const notifications = await this.notificationsService.findAll(
      {
        ownerProfileId: profile.id,
        isSeen: false,
        createdAt: {
          [Op.gt]: moment().subtract(7, 'days').toDate(),
        },
      },
      {
        includeParams: this.getIncludeParams(),
      },
    );

    return await Promise.all(
      notifications.map(async notification => {
        if (notification?.data?.organisationId) {
          const organisationDetail = await this.organisationsService.findOne(
            { id: notification?.data?.organisationId },
            { attributes: ['name', 'id', 'vanityId', 'image'] },
          );
          notification.data.organisation = organisationDetail;
        }
        return notification;
      }),
    );
  }
}
