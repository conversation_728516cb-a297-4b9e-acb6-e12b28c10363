import { Notification } from '../../notifications/models/notification.model';
import { NotificationType } from '../../notifications/args/notifications.args';
import config from '../../config/config';
import moment from 'moment';
import { get } from 'lodash';

export const DATE_MONTH = 'D MMMM';
export const DATE_SHORT = 'D MMM YYYY';

const CLOUDINARY_URL = `https://res.cloudinary.com/${config.CLOUDINARY_CLOUD_NAME}/image/upload`;

const Icons = {
  calendar: `${CLOUDINARY_URL}/v1672224432/calendar_ldzj2s.png`,
  comment: `${CLOUDINARY_URL}/v1672224432/comment_olk8df.png`,
  explore: `${CLOUDINARY_URL}/v1672224432/explore_cx9cor.png`,
  incentives: `${CLOUDINARY_URL}/v1672224432/incentives_pozanz.png`,
  like: `${CLOUDINARY_URL}/v1672224432/like_bkoa2y.png`,
  posts: `${CLOUDINARY_URL}/v1672224432/posts_ismiyi.png`,
  security: `${CLOUDINARY_URL}/v1672224432/security_xp6ney.png`,
  time: `${CLOUDINARY_URL}/v1672224432/time_samgyp.png`,
  users: `${CLOUDINARY_URL}/v1672224432/users_tepktq.png`,
  webinar: `${CLOUDINARY_URL}/v1672224432/webinar_mkgkuo.png`,
};

export const encodePathParams = (
  url: string,
  data?: object,
  searchParams?: string,
): string => {
  try {
    const baseUrl = config.FRONTEND_URL;
    if (!data) return baseUrl;

    const basePath = `${baseUrl}${url}`;
    const pathResult = Object.keys(data).reduce((acc, key) => {
      const newPath = acc.replace(`:${key}`, data[key]);
      return searchParams && searchParams.length
        ? newPath + '?' + searchParams
        : newPath;
    }, basePath);

    return pathResult;
  } catch (e) {
    return '#';
  }
};

export const getFullPathFromRoute = (url: string): string => {
  const baseUrl = config.FRONTEND_URL;
  return `${baseUrl}${url}`;
};

export function FromNow(date) {
  if (moment(date, 'YYYY-MM-DD HH:mm:ss.SSSSSS +Z').isValid()) {
    date = moment(date, 'YYYY-MM-DD HH:mm:ss.SSSSSS +Z');
  }

  if (date.isAfter(moment())) return null;

  const seconds = moment().diff(date, 's');
  const minutes = Math.round(seconds / 60);
  const hours = Math.round(minutes / 60);
  const days = Math.round(hours / 24);
  const years = Math.round(days / 365);

  if (seconds < 60) {
    return `<span style="text-decoration:none">${seconds}s</span>`;
  }
  if (minutes < 60) {
    return `<span style="text-decoration:none">${minutes}m</span>`;
  }
  if (hours < 24) {
    return `<span style="text-decoration:none">${hours}h</span>`;
  }
  if (days === 1) {
    return `<span style="text-decoration:none">yesterday</span>`;
  }
  if (days <= 7) {
    return `<span style="text-decoration:none">${days}d</span>`;
  }
  if (years < 1) {
    return `<span style="text-decoration:none">${date.format(
      DATE_MONTH,
    )}</span>`;
  }
  return `<span style="text-decoration:none">${date.format(DATE_SHORT)}</span>`;
}

export interface NotificationConfiguration {
  image: string | undefined | null;
  route: string;
  text: any;
  icon: any;
  rightImage: string | undefined | null;
}

export enum Routes {
  // public
  login = `/login`,
  signUp = `/signup`,
  authCallback = `/auth/callback`,
  networkError = `/networkerror`,

  // private
  default = `/`,
  home = `/home`,
  calendar = `/calendar`,
  notifications = `/notifications`,
  search = `/search`,
  inbox = `/inbox`,
  inboxChat = `/inbox/chat/:chatId`,
  inboxChatNew = `/inbox/chat/new`,
  video = `/inbox/call/:callId`,
  explore = `/explore`,
  organisationChooseType = `/organisation/join`,
  organisationCreate = `/organisation/join/:type`,
  organisationSettings = `/organisation/:organisationId`,
  organisationSettingsInfo = `/organisation/:organisationId/info`,
  organisationSettingsEmployees = `/organisation/:organisationId/employees`,
  organisationSettingsUserRoles = `/organisation/:organisationId/user-roles`,
  organisationSettingsPrivacy = `/organisation/:organisationId/privacy`,
  organisationSettingsConnections = `/organisation/:organisationId/connections`,
  organisationSettingsInvitations = `/organisation/:organisationId/events`,
  connect = `/connect`,
  connectInviteContacts = `/connect/invite-contacts`,
  connectInviteContactsSent = `/connect/invite-contacts/sent`,
  incentives = `/incentives`,
  profile = `/profile`,
  profileSetup = `/profile/setup`,
  profileSettings = `/profile/settings`,
  profileSettingsAccount = `/profile/settings`,
  profileSettingsAccountDelete = `/profile/settings/account/delete`,
  profileSettingsPrivacy = `/profile/settings/privacy`,
  profileSettingsNotifications = `/profile/settings/notifications`,
  profileById = `/profile/:profileId`,
  habloAdmin = `/habloadmin`,
  habloAdminManageOrganisation = `/habloadmin/manage-organisation`,
  habloAdminManageExplorePages = `/habloadmin/manage-explore-pages`,
  habloAdminManageAutoFollows = `/habloadmin/manage-auto-follows`,
  organisationSettingsMembers = `/organisation/:organisationId/members`,

  // All unhandled routes will load an organisation
  organisationProfile = `/:vanityId`,
  organisationAbout = `/:vanityId/about`,
  organisationWebinars = `/:vanityId/webinars`,
  organisationWebinarBroadcast = `/:vanityId/webinars/:id/broadcast/`,
  organisationResources = `/:vanityId/resources`,
  organisationEvents = `/:vanityId/events`,
  organisationEvent = `/:vanityId/events/:eventId`,
  organisationIncentive = `/:vanityId/incentives/:incentiveId`,
  organisationWebinar = `/:vanityId/webinars/:id`,
  organisationPeople = `/:vanityId/people`,
  organisationAnalytics = `/:vanityId/analytics`,
  organisationPost = `/:vanityId/post/:id/userid/:userId`,
  userPost = `/:vanityId/post/:id`,
  organisationNewPost = `/home?create=post`,

  //exlore category

  exploreAllDestinations = `/explore/destination`,
  exploreDestination = `/explore/destination/:destinationId`,
  explorePrivateSector = `/explore/private-sector`,
  exploreTourOperator = `/explore/tour-operator`,
  exploreTravelAgent = `/explore/travel-agent`,
  exploreAgencies = `/explore/agencies`,
  exploreOrganisationsYouFollow = `/explore/organisations-you-follow`,

  //connected organisations

  addConnection = `/organisation/:organisationId/connections/add`,
  pendingConnections = `/organisation/:organisationId/connections/pending`,
  approvedConnections = `/organisation/:organisationId/connections/pending/success`,
  activeConnections = `/organisation/:organisationId/connections/active`,
  previousConnections = `/organisation/:organisationId/connections/previous`,
  clubHabloDashboard = '/club-hablo/dashboard',
}

export const getStringsForNotifications = (
  countOfUsers: number,
  usersFullNames: string[] = [],
) => {
  switch (countOfUsers) {
    case 1:
      return {
        usersFullNamesString: usersFullNames[0],
        other: '',
        has: 'has ',
      };

    case 2:
      return {
        usersFullNamesString: `${usersFullNames[0]} & ${usersFullNames[1]} `,
        other: '',
        has: 'have ',
      };

    case 3:
      return {
        usersFullNamesString: `${usersFullNames[0]}, ${usersFullNames[1]} & 1 `,
        other: 'other ',
        has: 'have ',
      };

    default:
      return {
        usersFullNamesString: `${usersFullNames[0]}, ${usersFullNames[1]} & ${countOfUsers} `,
        other: 'others ',
        has: 'have ',
      };
  }
};

export const getOrgNameStringsForNotifications = (
  countOfOrgs: number,
  organistionNames: string[] = [],
) => {
  switch (countOfOrgs) {
    case 1:
      return {
        organisationNamesString: organistionNames[0],
        has: 'has ',
      };

    case 2:
      return {
        organisationNamesString: `${organistionNames[0]} and ${organistionNames[1]} `,
        has: 'have ',
      };

    case 3:
      return {
        organisationNamesString: `${organistionNames[0]}, ${organistionNames[1]} and ${organistionNames[2]} `,
        has: 'have ',
      };

    default:
      return {
        organisationNamesString: `${organistionNames[0]} `,
        has: 'has ',
      };
  }
};

export class NotificationHelper {
  static getEmptyConfiguration() {
    return {
      image: '',
      route: '',
      text: '<div></div>',
      icon: Icons.time,
      rightImage: '',
    };
  }

  static getConfiguration(
    notification: Notification,
  ): NotificationConfiguration {
    return {
      image: this.getImage(notification),
      route: this.getRoute(notification),
      text: this.getText(notification),
      icon: this.getIcon(notification),
      rightImage: this.getRightImage(notification),
    };
  }

  private static getImage(
    notification: Notification,
  ): string | undefined | null {
    switch (notification.type) {
      case NotificationType.InvitationAccepted:
      case NotificationType.InvitationReceived:
        return notification.profile?.image;
      case NotificationType.PartnershipRequestReceived:
        return notification.partnershipRequest?.senderOrganisation?.image;
      case NotificationType.PartnershipRequestApproved:
        return notification.partnershipRequest?.receiverOrganisation?.image;
      case NotificationType.PartnershipRequestApprovedByOrganisation:
        return notification.partnershipRequest?.senderOrganisation?.image;
      case NotificationType.MembershipRequested:
      case NotificationType.MembershipAccepted:
      case NotificationType.ParentMembershipRequested:
      case NotificationType.ParentMembershipAccepted:
      case NotificationType.ParentMembershipDeclined:
        return notification.membership?.organisation?.image;
      case NotificationType.FollowerAccepted:
        return notification.follower?.organisation?.image;
      case NotificationType.MembershipPermissionsUpdated:
      case NotificationType.OrganisationOwnershipAccepted:
      case NotificationType.OrganisationOwnershipRequested:
        return notification.membership?.organisation?.image;
      case NotificationType.EventInvitationByGuest:
        return notification.eventInvitation?.inviterProfile?.image;
      case NotificationType.EventInvitationByHosts:
      case NotificationType.EventInvitationApproved:
      case NotificationType.NewEventUpdate:
      case NotificationType.EventLocationChanged:
      case NotificationType.EventDateTimeChanged:
        return notification.eventInvitation?.event?.organisation?.image;
      case NotificationType.IncentiveDateChanged:
      case NotificationType.IncentiveInvitationByHosts:
      case NotificationType.IncentiveRegistrationApproved:
      case NotificationType.IncentiveRegistrationRequested:
      case NotificationType.NewIncentiveUpdate:
        return notification.incentiveParticipant?.incentive?.organisation
          ?.image;
      case NotificationType.IncentiveInvitationByParticipant:
        return notification.incentiveParticipant?.inviterProfile?.image;
      case NotificationType.WebinarDateChanged:
      case NotificationType.WebinarInvitationByHosts:
      case NotificationType.WebinarRegistrationApproved:
      case NotificationType.WebinarRegistrationRequested:
      case NotificationType.WebinarParticipantAddedAsHostAdmin:
      case NotificationType.WebinarParticipantAddedAsHost:
      case NotificationType.WebinarParticipantAddedAsSpeaker:
      case NotificationType.NewWebinarUpdate:
        return notification.webinarParticipant?.webinar?.organisation?.image;
      case NotificationType.WebinarInvitationByParticipant:
        return notification.webinarParticipant?.inviterProfile?.image;
      case NotificationType.SuggestFollow:
        return 'jpwbh9kjy7an2w05jijd'; // hablo org logo
      case NotificationType.PostComment:
        return notification.data.users[0].image;
      case NotificationType.CommentReact:
        return notification.data.users[0].image;
      case NotificationType.PostReact:
      case NotificationType.NewFollower:
        return notification.data?.users?.[0].image;
      case NotificationType.PostShared:
      case NotificationType.PostMention:
        return notification.organisation?.image;
      case NotificationType.CommentMention:
        return notification.profile?.image;
      case NotificationType.OrgCommentMention:
        return notification.profile?.image;
      case NotificationType.RecurringPaymentReminder:
        return notification.partnershipRequest?.senderOrganisation?.image;
      case NotificationType.MembershipAutoApprove:
        return notification.membership?.profile?.image;
      case NotificationType.OrgPostReminder7Days:
      case NotificationType.OrgPostReminder14Days:
      case NotificationType.OrgPostMention:
      case NotificationType.HighFiveAchievement:
      case NotificationType.NewOrganisationAchievement:
        return notification.organisation?.image;
      case NotificationType.InactiveUserReminder:
        return notification.data?.[0]?.organisationImage;
      case NotificationType.InboxMessage:
      case NotificationType.NewTier:
      case NotificationType.WeeklySummary:
      case NotificationType.DailyLoginStreak:
      case NotificationType.NewAchievement:
        return notification.profile?.image;
      case NotificationType.OrgPartnerRequestReceived:
        return notification.data?.senderOrg?.image;
      case NotificationType.OrgPartnerAcceptedSender:
      case NotificationType.OrgPartnerAcceptedReceiver:
        return notification.data?.receiverOrg?.image;
      default: {
        return null;
      }
    }
  }

  private static getRightImage(
    notification: Notification,
  ): string | undefined | null {
    switch (notification.type) {
      case NotificationType.EventInvitationByGuest:
      case NotificationType.EventInvitationByHosts:
      case NotificationType.EventInvitationApproved:
      case NotificationType.NewEventUpdate:
      case NotificationType.EventLocationChanged:
      case NotificationType.EventDateTimeChanged:
        return notification.eventInvitation?.event?.image;
      case NotificationType.MembershipRequested:
      case NotificationType.ParentMembershipRequested:
      case NotificationType.ParentMembershipAccepted:
      case NotificationType.ParentMembershipDeclined:
        return notification.profile?.image;
      case NotificationType.IncentiveDateChanged:
      case NotificationType.IncentiveInvitationByHosts:
      case NotificationType.IncentiveInvitationByParticipant:
      case NotificationType.IncentiveRegistrationApproved:
      case NotificationType.NewIncentiveUpdate:
        return notification.incentiveParticipant?.incentive?.image;
      case NotificationType.IncentiveRegistrationRequested:
        return notification.incentiveParticipant?.profile?.image;
      case NotificationType.WebinarDateChanged:
      case NotificationType.WebinarInvitationByHosts:
      case NotificationType.WebinarInvitationByParticipant:
      case NotificationType.WebinarRegistrationApproved:
      case NotificationType.NewWebinarUpdate:
      case NotificationType.WebinarParticipantAddedAsHostAdmin:
      case NotificationType.WebinarParticipantAddedAsHost:
      case NotificationType.WebinarParticipantAddedAsSpeaker:
        return notification.webinarParticipant?.webinar?.image;
      case NotificationType.WebinarRegistrationRequested:
        return notification.webinarParticipant?.profile?.image;
      case NotificationType.PostComment:
        return notification.data.users[0].image;
      case NotificationType.CommentReact:
        return notification.data.users[0].image;
      case NotificationType.PostReact:
      case NotificationType.NewFollower:
        return notification.data?.users?.[0].image;
      case NotificationType.PostShared:
      case NotificationType.PostMention:
        return notification.organisation?.image;
      case NotificationType.CommentMention:
        return notification.profile?.image;
      case NotificationType.OrgPostMention:
      case NotificationType.NewOrganisationAchievement:
        return notification.organisation?.image;
      case NotificationType.OrgCommentMention:
        return notification.profile?.image;
      case NotificationType.RecurringPaymentReminder:
        return notification.partnershipRequest?.receiverOrganisation?.image;
      case NotificationType.MembershipAutoApprove:
        return notification.membership?.profile?.image;
      case NotificationType.OrgPostReminder7Days:
      case NotificationType.OrgPostReminder14Days:
        return notification.organisation?.image;
      case NotificationType.InactiveUserReminder:
        return notification.data?.[0]?.organisationImage;
      case NotificationType.InboxMessage:
        return notification.profile?.image;
      case NotificationType.NewTier:
        return notification.profile?.image;
      case NotificationType.WeeklySummary:
        return notification.profile?.image;
      case NotificationType.OrgPartnerRequestReceived:
        return notification.data?.receiverOrg?.image;
      case NotificationType.OrgPartnerAcceptedSender:
      case NotificationType.OrgPartnerAcceptedReceiver:
        return notification.data?.senderOrg?.image;
      default: {
        return null;
      }
    }
  }

  private static getRoute(notification: Notification): string {
    switch (notification.type) {
      case NotificationType.InvitationReceived:
        return getFullPathFromRoute(Routes.connect);
      case NotificationType.InvitationAccepted:
        return encodePathParams(Routes.profileById, {
          profileId: notification.profile?.id,
        });
      case NotificationType.PartnershipRequestReceived:
        return encodePathParams(Routes.pendingConnections, {
          organisationId:
            notification.partnershipRequest?.receiverOrganisation?.id,
        });
      case NotificationType.PartnershipRequestApproved:
        return encodePathParams(Routes.activeConnections, {
          organisationId:
            notification.partnershipRequest?.senderOrganisation?.id,
        });
      case NotificationType.PartnershipRequestApprovedByOrganisation:
        return encodePathParams(Routes.organisationProfile, {
          vanityId: notification.partnershipRequest?.senderOrganisation
            ?.vanityId as string,
        });
      case NotificationType.OrganisationOwnershipAccepted:
      case NotificationType.OrganisationOwnershipRequested:
        return encodePathParams(Routes.organisationSettingsUserRoles, {
          organisationId: notification?.membership?.organisation?.id,
        });
      case NotificationType.MembershipRequested:
        return encodePathParams(Routes.organisationSettingsEmployees, {
          organisationId: notification?.membership?.organisation?.id,
        });
      case NotificationType.MembershipAccepted:
      case NotificationType.ParentMembershipAccepted:
      case NotificationType.ParentMembershipDeclined:
        return encodePathParams(Routes.profile);
      case NotificationType.MembershipPermissionsUpdated:
        return encodePathParams(Routes.organisationSettingsUserRoles, {
          organisationId: notification?.membership?.organisation?.id,
        });
      case NotificationType.FollowerAccepted:
        return encodePathParams(Routes.organisationProfile, {
          vanityId: notification?.follower?.organisation?.vanityId as string,
        });
      case NotificationType.IncentiveDateChanged:
      case NotificationType.IncentiveInvitationByHosts:
      case NotificationType.IncentiveInvitationByParticipant:
      case NotificationType.IncentiveRegistrationApproved:
      case NotificationType.NewIncentiveUpdate:
        return encodePathParams(Routes.organisationIncentive, {
          vanityId:
            notification.incentiveParticipant?.incentive.organisation.vanityId,
          incentiveId: notification.incentiveParticipant?.incentive?.id,
        });
      case NotificationType.IncentiveRegistrationRequested:
        return encodePathParams(Routes.organisationIncentive, {
          vanityId:
            notification.incentiveParticipant?.incentive.organisation.vanityId,
          incentiveId: notification.incentiveParticipant?.incentive?.id,
        });
      case NotificationType.WebinarDateChanged:
      case NotificationType.WebinarInvitationByHosts:
      case NotificationType.WebinarInvitationByParticipant:
      case NotificationType.WebinarRegistrationApproved:
      case NotificationType.NewWebinarUpdate:
      case NotificationType.WebinarRegistrationRequested:
      case NotificationType.WebinarParticipantAddedAsHostAdmin:
      case NotificationType.WebinarParticipantAddedAsHost:
      case NotificationType.WebinarParticipantAddedAsSpeaker:
        return encodePathParams(Routes.organisationWebinar, {
          vanityId:
            notification.webinarParticipant?.webinar.organisation.vanityId,
          id: notification.webinarParticipant?.webinar?.id,
        });
      case NotificationType.SuggestFollow:
        return getFullPathFromRoute(Routes.exploreOrganisationsYouFollow);

      case NotificationType.PostComment:
        return encodePathParams(Routes.organisationPost, {
          vanityId: notification?.organisation?.id,
          id: notification?.data?.activityId,
          userId: notification?.organisation?.id,
        });
      case NotificationType.CommentReact:
        return encodePathParams(Routes.organisationPost, {
          vanityId: notification?.organisation?.id,
          id: notification?.data?.activityId,
          userId: notification?.organisation?.id,
        });
      case NotificationType.PostReact:
        return encodePathParams(Routes.organisationPost, {
          vanityId: notification?.organisation?.id,
          id: notification?.data?.activityId,
          userId: notification?.organisation?.id,
        });
      case NotificationType.PostShared:
        return encodePathParams(Routes.organisationPost, {
          vanityId: notification?.organisation?.id,
          id: notification?.data?.activityId,
          userId: notification?.organisation?.id,
        });
      case NotificationType.CommentMention:
        return encodePathParams(Routes.organisationPost, {
          vanityId: notification?.organisation?.id,
          id: notification?.data?.activityId,
          userId: notification?.organisation?.id,
        });
      case NotificationType.PostMention:
        return encodePathParams(Routes.organisationPost, {
          vanityId: notification?.organisation?.id,
          id: notification?.data?.activityId,
          userId: notification?.organisation?.id,
        });
      case NotificationType.OrgPostMention:
        return encodePathParams(Routes.organisationPost, {
          vanityId: notification?.organisation?.vanityId,
          id: notification?.data?.activityId,
          userId: notification?.organisation?.id,
        });
      case NotificationType.OrgCommentMention:
        return encodePathParams(Routes.organisationPost, {
          vanityId: notification?.organisation?.vanityId,
          id: notification?.data?.activityId,
          userId: notification?.organisation?.id,
        });
      case NotificationType.NewFollower:
        return encodePathParams(
          Routes.organisationProfile,
          {
            vanityId: notification?.organisation?.vanityId,
          },
          'modal=followers',
        );
      case NotificationType.EventInvitationByGuest:
      case NotificationType.EventInvitationByHosts:
      case NotificationType.EventInvitationApproved:
      case NotificationType.NewEventUpdate:
      case NotificationType.EventLocationChanged:
      case NotificationType.EventDateTimeChanged:
      default: {
        return encodePathParams(Routes.organisationEvent, {
          vanityId: notification?.eventInvitation?.event?.organisation
            ?.vanityId as string,
          eventId: notification?.eventInvitation?.event?.id,
        });
      }
      case NotificationType.ParentMembershipRequested:
        return encodePathParams(Routes.organisationSettingsMembers, {
          organisationId: notification?.membership?.organisation?.id,
        });
      case NotificationType.OrgPostReminder7Days:
      case NotificationType.OrgPostReminder14Days:
        return getFullPathFromRoute(Routes.organisationNewPost);
      case NotificationType.InactiveUserReminder:
        return getFullPathFromRoute(Routes.home);
      case NotificationType.InboxMessage:
        return encodePathParams(Routes.inboxChat, {
          chatId: notification?.data?.streamChannelId,
        });
      case NotificationType.DailyLoginStreak:
        return getFullPathFromRoute(Routes.home);
      case NotificationType.NewAchievement:
      case NotificationType.NewOrganisationAchievement:
        return getFullPathFromRoute(Routes.clubHabloDashboard);
      case NotificationType.OrgPartnerRequestReceived:
      case NotificationType.OrgPartnerAcceptedSender:
      case NotificationType.OrgPartnerAcceptedReceiver:
        return encodePathParams(Routes.organisationSettingsConnections, {
          organisationId: notification.data?.receiverOrgId,
        });
    }
  }

  private static getText(notification: Notification): any {
    switch (notification.type) {
      case NotificationType.InvitationAccepted: {
        // <b style="text-decoration:none">[requester full name]</b> wants to connect with you. View your pending connection invitations.
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${'You are now connected with'} </span>
            <b style="text-decoration:none">${notification.profile?.name}</b>
          </p>`;
      }
      case NotificationType.InvitationReceived: {
        // You are now connected with <b style="text-decoration:none">[approver full name]</b>.
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${notification.profile?.name}</b>
            <span style="text-decoration:none">
              ${' '}
              ${'wants to connect with you. View your pending connection invitations.'}
            </span>
          </p>`;
      }
      case NotificationType.OrganisationOwnershipRequested: {
        // <b style="text-decoration:none">[current owner name]</b> has requested you take over as Owner of <b style="text-decoration:none">[organisation name]</b>. View this request to accept or reject it.
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${notification.profile?.name}</b>
            <span style="text-decoration:none"> ${'has requested you take over as Owner of'} </span>
            <span style="text-decoration:none">${
              notification.membership?.organisation?.name
            }.</span>
            <span style="text-decoration:none"> ${'View this request to accept or reject it.'}</span>
          </p>`;
      }
      case NotificationType.OrganisationOwnershipAccepted: {
        // <b style="text-decoration:none">[new owner name]</b> has agreed to become Owner of <b style="text-decoration:none">[organisation name]</b>. You have reverted to an Admin role.
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${notification.profile?.name}</b>
            <span style="text-decoration:none"> ${'has agreed to become Owner of'} </span>
            <span style="text-decoration:none">${
              notification.membership?.organisation?.name
            }.</span>
            <span style="text-decoration:none"> ${'You have reverted to an Admin role.'}</span>
          </p>`;
      }
      case NotificationType.PartnershipRequestReceived: {
        // <b style="text-decoration:none">[requesting organisation]</b> has requested to become a Connected Organisation with <b style="text-decoration:none">[my requested organisation]</b>. View this request.
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.partnershipRequest?.senderOrganisation?.name
            }</b>
            <span style="text-decoration:none"> ${'has requested to become a Connected Organisation with'} </span>
            <b style="text-decoration:none">${
              notification.partnershipRequest?.receiverOrganisation?.name
            }</b>
            <span style="text-decoration:none"> ${'View this request.'}</span>
          </p>`;
      }
      case NotificationType.PartnershipRequestApproved: {
        // <b style="text-decoration:none">[approving organisation]</b> has approved a request to become Connected Organisation with <b style="text-decoration:none">[my requesting organisation]</b>.
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.partnershipRequest?.receiverOrganisation?.name
            }</b>
            <span style="text-decoration:none"> ${'approved a connection request from'} </span>
            <span style="text-decoration:none">${
              notification.partnershipRequest?.senderOrganisation?.name
            }.</span>
          </p>`;
      }
      case NotificationType.PartnershipRequestApprovedByOrganisation: {
        // <b style="text-decoration:none">[requesting organisation]</b> is now a Connected Organisation with [my approving organisation]
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.partnershipRequest?.senderOrganisation?.name
            }</b>
            <span style="text-decoration:none"> ${'is now a Connected Organisation with'} </span>
            <b style="text-decoration:none">${
              notification.partnershipRequest?.receiverOrganisation?.name
            }</b>
          </p>`;
      }
      case NotificationType.MembershipRequested: {
        // <b style="text-decoration:none">[requester full name]</b> has requested to join <b style="text-decoration:none">[organisation]</b>. View this request to confirm their employment.
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${notification.profile?.name}</b>
            <span style="text-decoration:none"> ${'has requested to join'} </span>
            <b style="text-decoration:none">${
              notification.membership?.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'View this request to confirm their employment'}.</span>
          </p>`;
      }
      case NotificationType.MembershipAccepted: {
        // You have been verified as an employee of <b style="text-decoration:none">[organisation name]</b>.
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${'You have been verified as an employee of'} </span>
            <b style="text-decoration:none">${
              notification.membership?.organisation?.name
            }</b>
          </p>`;
      }
      case NotificationType.FollowerAccepted: {
        // <b style="text-decoration:none">[organisation name]</b> has accepted your follow request. You can now explore their full page.
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.follower?.organisation?.name
            }</b>
            <span style="text-decoration:none">
              ${' '}
              ${'has accepted your follow request. You can now explore their full page'}.
            </span>
          </p>`;
      }
      case NotificationType.MembershipPermissionsUpdated: {
        // You have been assigned as [role] of [organisation name].
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              ${'You have been assigned as'}${(
          notification?.data?.permissions ||
          notification.membership?.permissions ||
          []
        ).join(', ')}${' of  '}
            </span>
            <b style="text-decoration:none">${
              notification.membership?.organisation?.name
            }</b>
          </p>`;
      }
      case NotificationType.EventInvitationByGuest: {
        // <b style="text-decoration:none">[name of inviting user]</b> has invited you to an event: <b style="text-decoration:none">[event name]</b>
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.eventInvitation?.inviterProfile?.name
            }</b>
            <span style="text-decoration:none"> ${'has invited you to an event:'} </span>
            <b style="text-decoration:none">${
              notification.eventInvitation?.event?.name
            }</b>
          </p>`;
      }
      case NotificationType.EventInvitationByHosts: {
        // <b style="text-decoration:none">[organisation name]</b> has invited you to an event: <b style="text-decoration:none">[event name]</b>
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.eventInvitation?.event?.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'has invited you to an event:'} </span>
            <b style="text-decoration:none">${
              notification.eventInvitation?.event?.name
            }</b>
          </p>`;
      }
      case NotificationType.EventInvitationApproved: {
        // <b style="text-decoration:none">[Org name]</b> has accepted your request to attend <b style="text-decoration:none">[event name]</b>
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.eventInvitation?.event?.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'has accepted your request to attend'} </span>
            <b style="text-decoration:none">${
              notification.eventInvitation?.event?.name
            }</b>
          </p>`;
      }
      case NotificationType.NewEventUpdate: {
        // <b style="text-decoration:none">[Org name]</b> has posted an update in <b style="text-decoration:none">[event name]</b>
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.eventInvitation?.event?.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'has posted an update in'} </span>
            <b style="text-decoration:none">${
              notification.eventInvitation?.event?.name
            }</b>
          </p>`;
      }
      case NotificationType.EventLocationChanged:
        // <b style="text-decoration:none">[Org name]</b> has updated the location of <b style="text-decoration:none">[event name]</b>
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.eventInvitation?.event?.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'has updated the location of'} </span>
            <b style="text-decoration:none">${
              notification.eventInvitation?.event?.name
            }</b>
          </p>`;
      case NotificationType.EventDateTimeChanged:
        // <b style="text-decoration:none">[Org name]</b> has updated the date / time of <b style="text-decoration:none">[event name]</b>
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.eventInvitation?.event?.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'has made a change to the date / time of'} </span>
            <b style="text-decoration:none">${
              notification.eventInvitation?.event?.name
            }</b>
          </p>`;
      case NotificationType.IncentiveDateChanged:
        // <b style="text-decoration:none">[Org name]</b> has made a change to the dates of <b style="text-decoration:none">[incentive title]</b>
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.incentiveParticipant?.incentive?.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'has made a change to the dates of'} </span>
            <b style="text-decoration:none">${
              notification.incentiveParticipant?.incentive?.name
            }</b>
          </p>`;
      case NotificationType.IncentiveInvitationByParticipant:
        // <b style="text-decoration:none">[name of inviting user]</b> has invited you to an incentive: <b style="text-decoration:none">[incentive title]</b>
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.incentiveParticipant?.inviterProfile?.name
            }</b>
            <span style="text-decoration:none"> ${'has invited you to an incentive:'} </span>
            <b style="text-decoration:none">${
              notification.incentiveParticipant?.incentive?.name
            }</b>
          </p>`;
      case NotificationType.IncentiveInvitationByHosts:
        // <b style="text-decoration:none">[organisation name]</b> has invited you to an incentive: <b style="text-decoration:none">[incentive title]</b>
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.incentiveParticipant?.incentive?.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'has invited you to an incentive:'} </span>
            <b style="text-decoration:none">${
              notification.incentiveParticipant?.incentive?.name
            }</b>
          </p>`;
      case NotificationType.IncentiveRegistrationRequested:
        // <b style="text-decoration:none">[requester full name]</b> has requested to register in <b style="text-decoration:none">[incentive title]</b>. View this request to confirm their participation.
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.incentiveParticipant?.profile?.name
            }</b>
            <span style="text-decoration:none"> ${'has requested to register in'} </span>
            <b style="text-decoration:none">${
              notification.incentiveParticipant?.incentive?.name
            }:</b>
            <span style="text-decoration:none"> ${'View this request to confirm their participation'}.</span>
          </p>`;
      case NotificationType.IncentiveRegistrationApproved:
        // <b style="text-decoration:none">[Org name]</b> has accepted your request to register <b style="text-decoration:none">[incentive title]</b>
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.incentiveParticipant?.incentive?.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'has accepted your request to register'} </span>
            <b style="text-decoration:none">${
              notification.incentiveParticipant?.incentive?.name
            }</b>
          </p>`;
      case NotificationType.NewIncentiveUpdate: {
        // <b style="text-decoration:none">[Org name]</b> has posted an update in <b style="text-decoration:none">[incentive title]</b>
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.incentiveParticipant?.incentive?.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'has posted an update in'} </span>
            <b style="text-decoration:none">${
              notification.incentiveParticipant?.incentive?.name
            }</b>
          </p>`;
      }
      case NotificationType.WebinarDateChanged:
        // <b style="text-decoration:none">[Org name]</b> has made a change to the date / time of <b style="text-decoration:none">[webinar title]</b>
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.webinarParticipant?.webinar?.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'has made a change to the date / time of'} </span>
            <b style="text-decoration:none">${
              notification.webinarParticipant?.webinar?.name
            }</b>
          </p>`;
      case NotificationType.WebinarInvitationByParticipant:
        // <b style="text-decoration:none">[name of inviting user]</b> has invited you to a webinar: <b style="text-decoration:none">[webinar title]</b>
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.webinarParticipant?.inviterProfile?.name
            }</b>
            <span style="text-decoration:none"> ${'has invited you to a webinar:'} </span>
            <b style="text-decoration:none">${
              notification.webinarParticipant?.webinar?.name
            }</b>
          </p>`;
      case NotificationType.WebinarInvitationByHosts:
        // <b style="text-decoration:none">[organisation name]</b> has invited you to a webinar: <b style="text-decoration:none">[webinar title]</b>
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.webinarParticipant?.webinar?.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'has invited you to a webinar:'} </span>
            <b style="text-decoration:none">${
              notification.webinarParticipant?.webinar?.name
            }</b>
          </p>`;
      case NotificationType.WebinarRegistrationRequested:
        // <b style="text-decoration:none">[requester full name]</b> has requested to register in <b style="text-decoration:none">[webinar title]</b>. View this request to confirm their participation.
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.webinarParticipant?.profile?.name
            }</b>
            <span style="text-decoration:none"> ${'has requested to register in'} </span>
            <b style="text-decoration:none">${
              notification.webinarParticipant?.webinar?.name
            }</b>
            <span style="text-decoration:none"> ${'View this request to confirm their participation'}.</span>
          </p>`;
      case NotificationType.WebinarRegistrationApproved:
        // <b style="text-decoration:none">[Org name]</b> has accepted your request to register <b style="text-decoration:none">[webinar title]</b>
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.webinarParticipant?.webinar?.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'has accepted your request to register'} </span>
            <b style="text-decoration:none">${
              notification.webinarParticipant?.webinar?.name
            }</b>
          </p>`;
      case NotificationType.NewWebinarUpdate: {
        // <b style="text-decoration:none">[Org name]</b> has posted an update in <b style="text-decoration:none">[webinar title]</b>
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.webinarParticipant?.webinar?.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'has posted an update in'} </span>
            <b style="text-decoration:none">${
              notification.webinarParticipant?.webinar?.name
            }</b>
          </p>`;
      }
      case NotificationType.WebinarParticipantAddedAsHostAdmin: {
        // <b style="text-decoration:none">[organisation name]</b> has assigned you as a [Host Admin of / Host of / Speaker at]: <b style="text-decoration:none">[webinar title]</b>
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.webinarParticipant?.webinar?.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'has assigned you as a Host Admin of'} </span>
            <b style="text-decoration:none">${
              notification.webinarParticipant?.webinar?.name
            }</b>
          </p>`;
      }
      case NotificationType.WebinarParticipantAddedAsHost: {
        // <b style="text-decoration:none">[organisation name]</b> has assigned you as a [Host Admin of / Host of / Speaker at]: <b style="text-decoration:none">[webinar title]</b>
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.webinarParticipant?.webinar?.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'has assigned you as a Host of'} </span>
            <b style="text-decoration:none">${
              notification.webinarParticipant?.webinar?.name
            }</b>
          </p>`;
      }
      case NotificationType.WebinarParticipantAddedAsSpeaker: {
        // <b style="text-decoration:none">[organisation name]</b> has assigned you as a [Host Admin of / Host of / Speaker at]: <b style="text-decoration:none">[webinar title]</b>
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.webinarParticipant?.webinar?.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'has assigned you as a Speaker at'} </span>
            <b style="text-decoration:none">${
              notification.webinarParticipant?.webinar?.name
            }</b>
          </p>`;
      }
      case NotificationType.SuggestFollow: {
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              ${'You’ve had a suggestion to follow some new organisations that travel industry professionals similar to you also follow. Click here to check them out. You can unfollow at any time.'}${' '}
            </span>
          </p>`;
      }
      case NotificationType.PostComment: {
        const usersFullNames = notification.data.users.map(
          ({ name }: { name: string }) => name,
        );
        const countOfUsers = notification.data.users.length;
        const objectOfStrings = getStringsForNotifications(
          countOfUsers,
          usersFullNames,
        );

        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              <strong style="text-transform: capitalize;">
                ${objectOfStrings.usersFullNamesString} ${objectOfStrings.other}
              </strong>
              ${objectOfStrings.has}
              ${
                ' ' +
                'commented on your post. Click here to reply or like their comment.'
              }
            </span>
          </p>`;
      }
      case NotificationType.CommentReact: {
        const usersFullNames = notification.data.users.map(
          ({ name }: { name: string }) => name,
        );
        const countOfUsers = notification.data.users.length;
        const objectOfStrings = getStringsForNotifications(
          countOfUsers,
          usersFullNames,
        );

        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              <strong style="text-transform: capitalize;">
                ${objectOfStrings.usersFullNamesString} ${objectOfStrings.other}
              </strong>
              ${objectOfStrings.has}
              ${
                ' ' +
                'reacted to your comment on ' +
                (notification?.organisation?.name ?? '') +
                '’s post.'
              }
            </span>
          </p>`;
      }
      case NotificationType.PostReact: {
        const usersFullNames = notification.data.users.map(
          ({ name }: { name: string }) => name,
        );
        const countOfUsers = notification.data.users.length;
        const objectOfStrings = getStringsForNotifications(
          countOfUsers,
          usersFullNames,
        );

        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              <strong style="text-transform: capitalize;">
                ${objectOfStrings.usersFullNamesString} ${objectOfStrings.other}
              </strong>
              ${objectOfStrings.has}
              ${' ' + 'reacted to your post. Click here to view your post.'}
            </span>
          </p>`;
      }
      case NotificationType.PostShared: {
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              <strong style="text-transform: capitalize;">${
                notification.organisation?.name
              }</strong>${' '}
              ${'has reshared your post with their network.'}
            </span>
          </p>`;
      }
      case NotificationType.NewFollower: {
        const usersFullNames = notification.data?.users?.map(
          ({ name }: { name: string }) => name,
        );
        const countOfUsers = notification.data?.users?.length;
        const objectOfStrings = getStringsForNotifications(
          countOfUsers,
          usersFullNames,
        );
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              <strong style="text-transform: capitalize;">
                ${objectOfStrings.usersFullNamesString} ${objectOfStrings.other}
              </strong>
              ${objectOfStrings.has}
              ${' ' + 'requested to follow '}
              <strong>${notification?.organisation?.name ?? ''}</strong>.
            </span>
          </p>`;
      }
      case NotificationType.CommentMention: {
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              <strong style="text-transform: capitalize;">${
                notification.profile?.name
              }</strong>${' '}
              ${'mentioned you in a comment. Click here to reply or like the comment.'}
            </span>
          </p>`;
      }
      case NotificationType.PostMention: {
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              <strong style="text-transform: capitalize;">${
                notification.organisation?.name
              }</strong>${' '}
              ${'mentioned you in a post. Click here to view the post.'}
            </span>
          </p>`;
      }
      case NotificationType.OrgPostMention: {
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              <strong style="text-transform: capitalize;">${
                notification.organisation?.name
              }</strong>${' mentioned '}
              <strong style="text-transform: capitalize;">${
                notification?.data?.organisation?.name
              }</strong>
              ${' in a post. Click here to view the post.'}
            </span>
          </p>`;
      }
      case NotificationType.OrgCommentMention: {
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              <strong style="text-transform: capitalize;">${
                notification.profile?.name
              }</strong>${' mentioned '}
              <strong style="text-transform: capitalize;">${
                notification?.data?.organisation?.name
              }</strong>
              ${' in a comment. Click here to view the post.'}
            </span>
          </p>`;
      }
      case NotificationType.RecurringPaymentReminder: {
        // Billing date for paid subscription with ${partnershipRequest.senderOrganisation.name} is due on Today. Please ensure that you have sufficient funds in your account.
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none"> 
            ${'Your paid subscription with' + ' '} </span>    
            <strong style="text-transform: capitalize;">${
              notification.partnershipRequest?.senderOrganisation?.name
            }</strong>${' '}
            <span style="text-decoration:none"> ${'will renew on' + ' '} </span>
            <b style="text-decoration:none">${
              notification.partnershipRequest?.subscription?.lastTransaction
                ?.endDate
            }</b>${' '}
            <span style="text-decoration:none"> ${'Please ensure that your active payment method is still valid.'} </span>
          </p>`;
      }
      case NotificationType.MembershipAutoApprove: {
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${
              notification.profile?.name
            }</b>${' '}
            <span style="text-decoration:none"> ${'has joined' + ' '} </span>
            <b style="text-decoration:none">${
              notification.membership?.organisation?.name
            }</b>${' '}
            <span style="text-decoration:none"> ${'using a signup invite link'}.</span>
          </p>`;
      }
      case NotificationType.ParentMembershipRequested: {
        // <b style="text-decoration:none">[requester full name]</b> has requested to become a member of <b style="text-decoration:none">[organisation]</b>. View this request to confirm their employment.
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">${notification.profile?.name}</b>
            <span style="text-decoration:none"> ${'has requested to become a member of'} </span>
            <b style="text-decoration:none">${
              notification.membership?.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'View this request to confirm their membership'}.</span>
          </p>`;
      }
      case NotificationType.ParentMembershipAccepted: {
        // You have been verified as a member of <b style="text-decoration:none">[organisation name]</b>.
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${'You have been verified as an member of'} </span>
            <b style="text-decoration:none">${
              notification.membership?.organisation?.name
            }</b>
          </p>`;
      }
      case NotificationType.ParentMembershipDeclined: {
        // You have been declined as a member of <b style="text-decoration:none">[organisation name]</b>.
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${'You have been declined as an member of'} </span>
            <b style="text-decoration:none">${
              notification.organisation?.name
            }</b>
          </p>`;
      }
      case NotificationType.OrgPostReminder7Days: {
        // It’s been a week since <b style="text-decoration:none">[organisation name]</b>’s last post. Create a post to keep your community engaged.
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${'It’s been a week since '} </span>
            <b style="text-decoration:none">${
              notification.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'’s last post. Create a post to keep your community engaged'}</span>
          </p>`;
      }
      case NotificationType.OrgPostReminder14Days: {
        // It’s been two weeks since <b style="text-decoration:none">[organisation name]</b>’s last post and your community is waiting to hear from you. Create your next post now.
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${'It’s been two weeks since '} </span>
            <b style="text-decoration:none">${
              notification.organisation?.name
            }</b>
            <span style="text-decoration:none"> ${'’s last post and your community is waiting to hear from you. Create your next post now'}</span>
          </p>`;
      }
      case NotificationType.InactiveUserReminder: {
        const organisationNames = notification.data?.map(
          ({ organisationName }: { organisationName: string }) =>
            organisationName,
        );
        const countOfOrgs = organisationNames.length;
        const objectOfStrings = getOrgNameStringsForNotifications(
          countOfOrgs,
          organisationNames,
        );

        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <b style="text-decoration:none">
              ${objectOfStrings.organisationNamesString} </b>
              ${objectOfStrings.has}
            <span style="text-decoration:none"> ${'shared new posts recently. View the latest updates in your feed'}.</span>
          </p>`;
      }
      case NotificationType.InboxMessage: {
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${
              notification.profile?.name
            } </span>
            <span style="text-decoration:none">${': '} </span>
            <b style="text-decoration:none">${notification.data?.text}</b>
          </p>`;
      }
      case NotificationType.DailyLoginStreak: {
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${"You're on a roll! You hit a daily streak of "} </span>
            <b style="text-decoration:none">${get(
              notification,
              'data.streakCount',
              0,
            )}</b>
            <span style="text-decoration:none"> ${' days.'}</span>
          </p>`;
      }
      case NotificationType.NewAchievement: {
        const achievementType = get(notification, 'data.achievementType', '');
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${"Congratulations! You've unlocked the "} </span>
            <b style="text-decoration:none">${achievementType}</b>
            <span style="text-decoration:none"> ${' achievement.'}</span>
          </p>`;
      }

      case NotificationType.NewOrganisationAchievement: {
        const achievementType = get(notification, 'data.achievementType', '');
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${"Congratulations! You've unlocked the "} </span>
            <b style="text-decoration:none">${
              notification.organisation.name
            } ${achievementType}</b>
            <span style="text-decoration:none"> ${' achievement.'}</span>
          </p>`;
      }

      case NotificationType.HighFiveAchievement: {
        const achievementType = get(notification, 'data.achievementType', '');
        return `<p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">${'Nice work! You received a '}
            <b style="text-decoration:none">
            ${'High Five'}</b>
            ${' from'} </span>
            <b style="text-decoration:none">${
              notification.organisation?.name
            }</b>.
          </p>`;
      }

      case NotificationType.NewTier: {
        const currentTier = get(notification, 'data.currentTier', '');
        const nextTier = get(notification, 'data.nextTier', '');
        const isTierRetained = get(notification, 'data.isTierRetained', '');

        if (currentTier === 'Blue') {
          return `
            <p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              Welcome to the <b>Club Hablo!</b> Keep earning Kudos to move up to Silver Tier! 🥈
            </span>
          </p>`;
        }
        if (isTierRetained) {
          return `
            <p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              Congratulations ${
                notification.profile?.name
              } you have retained ${currentTier} tier!${
            nextTier ? ` 🥳 Next stop ${nextTier}` : ''
          }
            </span>
          </p>`;
        }

        return `
          <p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              Congratulations ${
                notification.profile?.name
              } you are now a ${currentTier} tier member!${
          nextTier ? ` 🥳 Next stop ${nextTier}` : ''
        }
            </span>
          </p>`;
      }
      case NotificationType.WeeklySummary: {
        const pointsEarned = get(notification, 'data.pointsEarned', '');
        const rollingPoints = get(notification, 'data.rollingPoints', '');
        const nextTier = get(notification, 'data.nextTier', '');
        const nextTierPoints = get(notification, 'data.nextTierPoints', '');
        const userName = get(notification, 'profile.name', '');

        return `
          <p style="text-decoration:none;margin-top:0;margin-bottom:0;">
            <span style="text-decoration:none">
              Congratulations ${userName} you have earned ${pointsEarned} points last week.${
          nextTier
            ? ` You need ${
                nextTierPoints - rollingPoints
              } points to reach ${nextTier} tier.`
            : ''
        }
            </span>
          </p>`;
      }
      case NotificationType.OrgPartnerRequestReceived:
        return `${notification.data?.senderOrgName} has requested to become a Connected Organisation with ${notification.data?.receiverOrgName}. View this request.`;
      case NotificationType.OrgPartnerAcceptedSender:
        return `${notification.data?.receiverOrgName} has approved your request to become a Connected Organisation.`;
      case NotificationType.OrgPartnerAcceptedReceiver:
        return `${notification.data?.senderOrgName} is now a Connected Organisation with ${notification.data?.receiverOrgName}.`;
      default: {
        return `<div></div>`;
      }
    }
  }

  private static getIcon(notification: Notification) {
    switch (notification.type) {
      case NotificationType.InvitationAccepted:
      case NotificationType.InvitationReceived:
        return Icons.users;
      case NotificationType.PartnershipRequestReceived:
      case NotificationType.PartnershipRequestApproved:
      case NotificationType.PartnershipRequestApprovedByOrganisation:
      case NotificationType.MembershipRequested:
      case NotificationType.MembershipAutoApprove:
      case NotificationType.RecurringPaymentReminder:
      case NotificationType.ParentMembershipRequested:
        return Icons.security;
      case NotificationType.MembershipAccepted:
      case NotificationType.FollowerAccepted:
      case NotificationType.MembershipPermissionsUpdated:
      case NotificationType.OrganisationOwnershipAccepted:
      case NotificationType.OrganisationOwnershipRequested:
      case NotificationType.ParentMembershipAccepted:
        return Icons.explore;
      case NotificationType.EventInvitationByGuest:
      case NotificationType.EventInvitationByHosts:
      case NotificationType.EventInvitationApproved:
      case NotificationType.NewEventUpdate:
      case NotificationType.EventLocationChanged:
      case NotificationType.EventDateTimeChanged:
        return Icons.calendar;
      case NotificationType.IncentiveDateChanged:
      case NotificationType.IncentiveInvitationByHosts:
      case NotificationType.IncentiveInvitationByParticipant:
      case NotificationType.IncentiveRegistrationApproved:
      case NotificationType.IncentiveRegistrationRequested:
      case NotificationType.NewIncentiveUpdate:
        return Icons.incentives;
      case NotificationType.WebinarDateChanged:
      case NotificationType.WebinarInvitationByHosts:
      case NotificationType.WebinarInvitationByParticipant:
      case NotificationType.WebinarRegistrationApproved:
      case NotificationType.WebinarRegistrationRequested:
      case NotificationType.NewWebinarUpdate:
      case NotificationType.WebinarParticipantAddedAsHostAdmin:
      case NotificationType.WebinarParticipantAddedAsHost:
      case NotificationType.WebinarParticipantAddedAsSpeaker:
        return Icons.webinar;
      case NotificationType.PostShared:
        return Icons.posts;
      case NotificationType.PostComment:
      case NotificationType.CommentMention:
        return Icons.comment;
      case NotificationType.CommentReact:
        return Icons.like;
      case NotificationType.PostReact:
        return Icons.like;
      case NotificationType.OrgPartnerRequestReceived:
      case NotificationType.OrgPartnerAcceptedSender:
      case NotificationType.OrgPartnerAcceptedReceiver:
        return Icons.users;
      default: {
        return Icons.time;
      }
    }
  }

  private static getFullImageUrl(imgShortcode: string): string {
    const highImageSize = 'w_92';
    const placeholderImage = `${CLOUDINARY_URL}/${highImageSize},c_fill,q_90,b_rgb:E9EEF1,f_auto/pknnbg0x7jcg6cngpjkc`;
    if (!imgShortcode) return placeholderImage;
    if (imgShortcode.includes('http')) return imgShortcode;
    return `${CLOUDINARY_URL}/${highImageSize},c_fill,q_90,b_rgb:fff,f_auto/${imgShortcode}`;
  }

  public static getNotificationLayout(notification: any) {
    return `
      <tr>
        <td style="vertical-align: top;">
          <a href="${notification.route}"
            style="text-decoration:none !important; text-decoration:none; display: block;"
          >
            <img
              width="56px"
              height="56px"
              src="${this.getFullImageUrl(notification.image)}"
              style="border-radius:18px;overflow:hidden;z-index:5;border:none;"
             />
          </a>
        </td>
        <td style="vertical-align: top; padding-bottom: 15px;">
          <a href="${notification.route}"
            style="text-decoration:none !important; text-decoration:none;"
          >
            <div style="padding: 0 12px; color: #303030; text-align: left;line-height: 20px;">
              ${notification.text}
              <div style="display: inline; line-height: 16px; vertical-align: bottom;">
                <img src="${
                  notification.icon
                }" width="16px" height="16px" style="margin-top: 3px; margin-right: 3px; display: inline; vertical-align: bottom;" />
                <div style="color: #0093c7; font-size: 13px; display: inline; vertical-align: bottom;">${FromNow(
                  notification.createdAt,
                )}</div>
              </div>
            </div>
          </a>
        </td>
      </tr>
      `;
  }
}
