import {
  forwardRef,
  Inject,
  Injectable,
  OnApplicationBootstrap,
  OnModuleInit,
} from '@nestjs/common';
import { connect } from 'getstream';
import moment from 'moment';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Op, Sequelize, Transaction } from 'sequelize';
import { Logger } from 'winston';
import { ActivitiesService } from '../activities/activities.service';
import { ActivityType } from '../activities/args/activities.args';
import {
  commentLikeActivityDataDto,
  newCommentActivityDataDto,
  postLikeActivityDataDto,
} from '../activities/dto/create-user-activity-data.dto';
import { ICurrentUser } from '../common/decorators/current-user.decorator';
import config from '../config/config';
import {
  encodePathParams,
  getStringsForNotifications,
  Routes,
} from '../email/helpers/notificationLayoutHelper';
import { EventInvitationStatus } from '../event-invitations/args/event-invitations.args';
import { EventInvitationsService } from '../event-invitations/event-invitations.service';
import { EventsService } from '../events/events.service';
import { FollowersService } from '../followers/followers.service';
import { FollowerStatus } from '../followers/models/follower.model';
import { IncentiveParticipantStatus } from '../incentive-participants/args/incentive-participants.args';
import { IncentiveParticipantsService } from '../incentive-participants/incentive-participants.service';
import { IncentivesService } from '../incentives/incentives.service';
import { MembershipsService } from '../memberships/memberships.service';
import {
  Membership,
  MembershipPermission,
  MembershipStatus,
} from '../memberships/models/membership.model';
import {
  NotificationMessage,
  NotificationType,
} from '../notifications/args/notifications.args';
import { NotificationsService } from '../notifications/notifications.service';
import { OrganisationStatus } from '../organisations/args/organisations.args';
import { OrganisationsService } from '../organisations/organisations.service';
import { Organisation } from '../organisations/models/organisation.model';
import {
  PartnershipRequest,
  PartnershipRequestStatus,
} from '../partnership-requests/models/partnership-request.model';
import { PartnershipsService } from '../partnerships/partnerships.service';
import {
  PostImageCategory,
  PostStatus,
  PostType,
} from '../posts/args/posts.args';
import { PostsService } from '../posts/posts.service';
import { Profile } from '../profiles/models/profile.model';
import { ProfilesService } from '../profiles/profiles.service';
import { WebinarParticipantStatus } from '../webinar-participants/args/webinar-participants.args';
import { WebinarParticipantsService } from '../webinar-participants/webinar-participants.service';
import { WebinarsService } from '../webinars/webinars.service';
import { MultipleImageDetailsInput } from './dto/create-post.input';
import { GettingStartedStepsService } from '../getting-started-steps/getting-started-steps.service';
import { GettingStartedStepEnum } from '../getting-started-steps/args/getting-started-steps.args';
import { get } from 'lodash';
import { OrganisationLoyaltyPointsService } from '../organisation-loyalty-points/organisation-loyalty-points.service';

interface UpdatePostInput {
  text?: string;
  image?: string;
  imageWidth?: number;
  imageHeight?: number;
  imageFormat?: string;
  multipleImages?: MultipleImageDetailsInput[];
  imageCategory?: PostImageCategory;
  video?: string;
  videoWidth?: number;
  videoHeight?: number;
  videoFormat?: string;
  document?: string;
  documentUrl?: string;
  documentSize?: string;
  documentFormat?: string;
  documentName?: string;
  users?: any;
  organisations?: any;
  type?: PostType;
}

@Injectable()
export class StreamPostsService
  implements OnModuleInit, OnApplicationBootstrap
{
  @Inject(WINSTON_MODULE_PROVIDER)
  private readonly logger: Logger;
  @Inject(forwardRef(() => PostsService))
  private readonly postsService: PostsService;
  @Inject(forwardRef(() => MembershipsService))
  private readonly membershipsService: MembershipsService;
  @Inject(forwardRef(() => ProfilesService))
  private readonly profilesService: ProfilesService;
  @Inject(forwardRef(() => NotificationsService))
  private readonly notificationsService: NotificationsService;
  @Inject(forwardRef(() => OrganisationsService))
  private readonly organisationsService: OrganisationsService;
  @Inject(forwardRef(() => ActivitiesService))
  private readonly activitiesService: ActivitiesService;
  @Inject(forwardRef(() => IncentivesService))
  private readonly incentivesService: IncentivesService;
  @Inject(forwardRef(() => IncentiveParticipantsService))
  private readonly incentiveParticipantsService: IncentiveParticipantsService;
  @Inject(forwardRef(() => WebinarsService))
  private readonly webinarsService: WebinarsService;
  @Inject(forwardRef(() => WebinarParticipantsService))
  private readonly webinarParticipantsService: WebinarParticipantsService;
  @Inject(forwardRef(() => EventsService))
  private readonly eventsService: EventsService;
  @Inject(forwardRef(() => FollowersService))
  private readonly followersService: FollowersService;
  @Inject(forwardRef(() => PartnershipsService))
  private readonly partnershipsService: PartnershipsService;
  @Inject(forwardRef(() => EventInvitationsService))
  private readonly eventInvitationsService: EventInvitationsService;
  @Inject(forwardRef(() => GettingStartedStepsService))
  private readonly gettingStartedStepsService: GettingStartedStepsService;
  @Inject()
  private readonly sequelize: Sequelize;
  @Inject(forwardRef(() => OrganisationLoyaltyPointsService))
  private readonly organisationLoyaltyPointsService: OrganisationLoyaltyPointsService;

  client: any;

  onModuleInit(): void {
    if (config.SERVICE === 'api-beta') {
      this.client = connect(config.STREAM_BETA_KEY, config.STREAM_BETA_SECRET);
    } else {
      this.client = connect(config.STREAM_KEY, config.STREAM_SECRET);
    }
  }

  async onApplicationBootstrap(): Promise<void> {}

  async followNewTargetedMembersFeedsMigration(): Promise<void> {
    try {
      const organisations: any[] = await this.organisationsService.findAll({
        status: OrganisationStatus.Active,
      });

      let orgIndex = 0;
      for (const org of organisations) {
        setTimeout(
          async function () {
            try {
              // make the org_feed_members feed follow the existing organisations feed
              await this.client
                .feed('org_feed_members', org.id)
                .follow('organisations', org.id);

              this.logger.info(
                `Stream.Service Info - followNewTargetedMembersFeedsMigration: orgFeeds`,
                {
                  organisationId: org.id,
                },
              );
            } catch (err) {
              this.logger.error(
                `Stream.Service Error - followNewTargetedMembersFeedsMigration: ${err.message}`,
                err.response?.body,
              );
            }
          }.bind(this),
          orgIndex * 1500,
        );
        orgIndex++;
      }

      const memberships: any[] = await this.membershipsService.findAll({
        status: MembershipStatus.Active,
        isPrimary: true,
        organisationId: {
          [Op.ne]: null,
        },
      });

      let index = 0;
      for (const membership of memberships) {
        setTimeout(
          async function () {
            try {
              const follows = [
                {
                  source: `user:${membership.profileId}`,
                  target: `org_feed_members:${membership.organisationId}`,
                },
                {
                  source: `user:${membership.profileId}`,
                  target: `org_feed_targeted:${membership.organisationId}`,
                },
              ];
              await this.client.followMany(follows);
              this.logger.info(
                `Stream.Service Info - followNewTargetedMembersFeedsMigration: userFeeds`,
                {
                  profileId: membership.profileId,
                  organisationId: membership.organisationId,
                },
              );
            } catch (err) {
              this.logger.error(
                `Stream.Service Error - followNewTargetedMembersFeedsMigration: ${err.message}`,
                err.response?.body,
              );
            }
          }.bind(this),
          index * 1500,
        );
        index++;
      }
    } catch (e) {
      this.logger.error(
        `Stream.Service Error - followNewTargetedMembersFeedsMigration: ${e.message}`,
        e.response?.body,
      );
    }
  }

  async followNewIncentiveFeeds(): Promise<void> {
    try {
      const incentives = await this.incentivesService.findAll({
        endDate: { [Op.gte]: Date.now() },
      });

      for (const incentive of incentives) {
        const registeredParticipants =
          await this.incentiveParticipantsService.findAll({
            incentiveId: incentive.id,
            status: IncentiveParticipantStatus.Registered,
          });
        const incentiveParticipantFollows = [];

        for (const participant of registeredParticipants) {
          incentiveParticipantFollows.push({
            source: 'user:' + participant.profileId,
            target: 'incentives:' + incentive.id,
          });
        }
        try {
          this.logger.info(`Stream.Service Info - followNewIncentiveFeeds: `, {
            incentive: incentive.id,
            registeredParticipants: registeredParticipants,
            incentiveParticipantFollows: incentiveParticipantFollows,
          });

          incentiveParticipantFollows.length &&
            (await this.client.followMany(incentiveParticipantFollows));
        } catch (err) {
          this.logger.error(
            `Stream.Service Error - followNewIncentiveFeeds: ${err.message}`,
            err.response?.body,
          );
        }
      }
    } catch (err) {
      this.logger.error(
        `Stream.Service Error - followNewIncentiveFeeds: ${err.message}`,
        err.response?.body,
      );
    }
  }

  async followNewWebinarFeeds(): Promise<void> {
    try {
      const webinars = await this.webinarsService.findAll({
        startDate: { [Op.gte]: Date.now() },
      });

      for (const webinar of webinars) {
        const activeParticipants =
          await this.webinarParticipantsService.findAll({
            webinarId: webinar.id,
            status: {
              [Op.in]: [
                WebinarParticipantStatus.Registered,
                WebinarParticipantStatus.InvitedRegistered,
                WebinarParticipantStatus.Host,
                WebinarParticipantStatus.Speaker,
                WebinarParticipantStatus.HiddenHost,
                WebinarParticipantStatus.HostAdmin,
              ],
            },
          });
        const webinarParticipantFollows = [];

        for (const participant of activeParticipants) {
          webinarParticipantFollows.push({
            source: 'user:' + participant.profileId,
            target: 'webinars:' + webinar.id,
          });
        }
        try {
          this.logger.info(`Stream.Service Info - followNewWebinarFeeds: `, {
            webinarId: webinar.id,
            activeParticipants: activeParticipants,
            webinarParticipantFollows: webinarParticipantFollows,
          });

          webinarParticipantFollows.length &&
            (await this.client.followMany(webinarParticipantFollows));
        } catch (err) {
          this.logger.error(
            `Stream.Service Error - followNewWebinarFeeds: ${err.message}`,
            err.response?.body,
          );
        }
      }
    } catch (err) {
      this.logger.error(
        `Stream.Service Error - followNewWebinarFeeds: ${err.message}`,
        err.response?.body,
      );
    }
  }

  async followNewEventFeeds(): Promise<void> {
    try {
      const events = await this.eventsService.findAll({
        endDate: { [Op.gte]: Date.now() },
      });

      for (const event of events) {
        const attendees = await this.eventInvitationsService.findAll({
          eventId: event.id,
          status: {
            [Op.in]: [
              EventInvitationStatus.Attending,
              EventInvitationStatus.InvitedAttending,
              EventInvitationStatus.InvitedInterested,
              EventInvitationStatus.Interested,
            ],
          },
        });
        const eventAttendeeFollows = [];

        for (const attendee of attendees) {
          eventAttendeeFollows.push({
            source: 'user:' + attendee.profileId,
            target: 'events:' + event.id,
          });
        }
        try {
          this.logger.info(`Stream.Service Info - followNewEventFeeds: `, {
            eventId: event.id,
            attendees: attendees,
            eventAttendeeFollows: eventAttendeeFollows,
          });

          eventAttendeeFollows.length &&
            (await this.client.followMany(eventAttendeeFollows));
        } catch (err) {
          this.logger.error(
            `Stream.Service Error - followNewEventFeeds: ${err.message}`,
            err.response?.body,
          );
        }
      }
    } catch (err) {
      this.logger.error(
        `Stream.Service Error - followNewEventFeeds: ${err.message}`,
        err.response?.body,
      );
    }
  }

  async followPrimaryMembershipMissedMigration(): Promise<void> {
    try {
      const memberships: any[] = await this.membershipsService.findAll({
        status: MembershipStatus.Active,
        isPrimary: true,
        organisationId: {
          [Op.ne]: null,
        },
      });

      let follows = [];

      for (const membership of memberships) {
        try {
          // follow new feeds each confirmed employee

          this.logger.info(
            `Stream.Service Info - followPrimaryMembershipMissedMigration: found primary membership`,
            {
              profileId: membership.profileId,
              organisationId: membership.organisationId,
            },
          );

          const followsOrg = await this.followersService.findOne({
            profileId: membership.profileId,
            organisationId: membership.organisationId,
          });
          this.logger.info(
            `Stream.Service Info - followPrimaryMembershipMissedMigration: checking follow`,
          );
          //only follow public/external feed on stream if user follows org
          if (followsOrg) {
            follows.push({
              source: 'user:' + membership.profileId,
              target: 'organisations:' + membership.organisationId,
            });
          }
          //user should always follow internal/members & targeted feeds regardless of following status
          follows.push({
            source: 'user:' + membership.profileId,
            target: 'org_feed_members:' + membership.organisationId,
          });
          follows.push({
            source: 'user:' + membership.profileId,
            target: 'org_feed_targeted:' + membership.organisationId,
          });

          if (follows.length > 100) {
            const done = await this.client.followMany(follows);
            if (!!done) follows = [];
          }
        } catch (err) {
          this.logger.error(
            `Stream.Service Error - followPrimaryMembershipMissedMigration: ${err.message}`,
            err.response?.body,
          );
        }
      }
      if (follows.length) await this.client.followMany(follows);
    } catch (e) {
      this.logger.error(
        `Stream.Service Error - followNewTargetedMembersFeedsMigration: ${e.message}`,
        e.response?.body,
      );
    }
  }

  async followNonPrimaryMembershipMissedMigration(): Promise<void> {
    try {
      const memberships: any[] = await this.membershipsService.findAll({
        status: MembershipStatus.Active,
        isPrimary: false,
        organisationId: {
          [Op.ne]: null,
        },
      });

      let follows = [];

      for (const membership of memberships) {
        try {
          // follow new feeds each confirmed user role
          this.logger.info(
            `Stream.Service Info - followNonPrimaryMembershipMissedMigration: found a membership`,
            {
              profileId: membership.profileId,
              organisationId: membership.organisationId,
            },
          );

          const followsOrg = await this.followersService.findOne({
            profileId: membership.profileId,
            organisationId: membership.organisationId,
            status: FollowerStatus.Active,
          });
          if (followsOrg)
            this.logger.info(
              `Stream.Service Info - followNonPrimaryMembershipMissedMigration: found Follow`,
              {
                profileId: membership.profileId,
                organisationId: membership.organisationId,
              },
            );
          else {
            this.logger.info(
              `Stream.Service Info - followNonPrimaryMembershipMissedMigration: follow NOT found`,
              {
                profileId: membership.profileId,
                organisationId: membership.organisationId,
              },
            );
          }
          await new Promise(resolve => setTimeout(resolve, 120)); // 120 ms delay for ~500 requests per minute
          const userFeed = this.client.feed('user', membership.profileId);
          let streamResponse = await userFeed.following({
            offset: 0,
            limit: 1,
            filter: ['organisations:' + membership.organisationId],
          });

          //only follow public/external feed on stream if user follows org
          if (followsOrg && !streamResponse.results.length) {
            this.logger.warn(
              `Stream.Service warning - followNonPrimaryMembershipMissedMigration: MISSING FOLLOW on Stream, being added`,
              {
                profileId: membership.profileId,
                organisationId: membership.organisationId,
              },
            );
            follows.push({
              source: 'user:' + membership.profileId,
              target: 'organisations:' + membership.organisationId,
            });
          }

          if (follows.length > 100) {
            this.logger.info(
              'Stream.Service followNonPrimaryMembershipMissedMigration - Stream.followMany (100)',
            );
            const done = await this.client.followMany(follows);
            if (!!done) follows = [];
          }
        } catch (err) {
          this.logger.error(
            `Stream.Service Error - followNonPrimaryMembershipMissedMigration: ${err.message}`,
            err.response?.body,
          );
        }
      }
      if (follows.length) {
        this.logger.info(
          'Stream.Service followNonPrimaryMembershipMissedMigration - Stream.followMany (final batch)',
        );
        await this.client.followMany(follows);
        this.logger.info(
          'Stream.Service followNonPrimaryMembershipMissedMigration - Stream.followMany done',
        );
      }
    } catch (e) {
      this.logger.error(
        `Stream.Service Error - followNonPrimaryMembershipMissedMigration: ${e.message}`,
        e.response?.body,
      );
    }
  }

  async followConnectedOrgsMissedMigration(): Promise<void> {
    try {
      const partnerships: any[] = await this.partnershipsService.findAll(
        {},
        {
          includeParams: [
            {
              model: PartnershipRequest,
              as: 'partnershipRequest',
              where: {
                status: PartnershipRequestStatus.Approved,
              },
            },
          ],
        },
      );

      let follows = [];

      for (const partnership of partnerships) {
        try {
          // for each employee, check if they still follow the org, and if so add the stream follow.

          this.logger.info(
            `Stream.Service Info - followConnectedOrgsMissedMigration: found partnership`,
            {
              partnershipId: partnership.id,
              status: partnership.partnershipRequest.status,
            },
          );

          const organisationMemberships = await this.membershipsService.findAll(
            {
              organisationId: partnership.organisationId,
              status: MembershipStatus.Active,
            },
          );

          for (const organisationMembership of organisationMemberships) {
            const existingFollower = await this.followersService.findOne({
              profileId: organisationMembership.profileId,
              organisationId: partnership.partnershipOrganisationId,
              status: FollowerStatus.Active,
            });

            if (existingFollower) {
              this.logger.info(
                `Stream.Service Info - followConnectedOrgsMissedMigration: adding follow`,
                {
                  profileId: organisationMembership.profileId,
                  organisationId: partnership.partnershipOrganisationId,
                },
              );

              const orgObj = {
                source: 'user:' + organisationMembership.profileId,
                target:
                  'organisations:' + partnership.partnershipOrganisationId,
              };
              const feedMemberObj = {
                source: 'user:' + organisationMembership.profileId,
                target:
                  'org_feed_members:' + partnership.partnershipOrganisationId,
              };
              const feedTargetedObj = {
                source: 'user:' + organisationMembership.profileId,
                target:
                  'org_feed_targeted:' + partnership.partnershipOrganisationId,
              };

              follows.push(orgObj, feedMemberObj, feedTargetedObj);
            }
          }

          const partnerOrganisationMemberships =
            await this.membershipsService.findAll({
              organisationId: partnership.partnershipOrganisationId,
              status: MembershipStatus.Active,
            });

          for (const partnerOrganisationMembership of partnerOrganisationMemberships) {
            const existingFollower = await this.followersService.findOne({
              profileId: partnerOrganisationMembership.profileId,
              organisationId: partnership.organisationId,
              status: FollowerStatus.Active,
            });

            if (existingFollower) {
              this.logger.info(
                `Stream.Service Info - followConnectedOrgsMissedMigration: adding follow`,
                {
                  profileId: partnerOrganisationMembership.profileId,
                  organisationId: partnership.organisationId,
                },
              );

              const orgObj = {
                source: 'user:' + partnerOrganisationMembership.profileId,
                target: 'organisations:' + partnership.organisationId,
              };
              const feedMemberObj = {
                source: 'user:' + partnerOrganisationMembership.profileId,
                target: 'org_feed_members:' + partnership.organisationId,
              };
              const feedTargetedObj = {
                source: 'user:' + partnerOrganisationMembership.profileId,
                target: 'org_feed_targeted:' + partnership.organisationId,
              };

              follows.push(orgObj, feedMemberObj, feedTargetedObj);
            }
          }

          if (follows.length > 100) {
            const done = await this.client.followMany(follows);
            if (!!done) follows = [];
          }
        } catch (err) {
          this.logger.error(
            `Stream.Service Error - followConnectedOrgsMissedMigration: ${err.message}`,
            err.response?.body,
          );
        }
      }
      if (follows.length) await this.client.followMany(follows);
    } catch (e) {
      this.logger.error(
        `Stream.Service Error - followConnectedOrgsMissedMigration: ${e.message}`,
        e.response?.body,
      );
    }
  }

  async followDailyQuizFeedMigration(): Promise<void> {
    try {
      const incentiveParticipants: any[] =
        await this.incentiveParticipantsService.findAll({
          incentiveId: 'pbmfgzp6r7i1WzNMSjqoLm',
          status: IncentiveParticipantStatus.Registered,
        });

      let follows = [];

      for (const participant of incentiveParticipants) {
        try {
          // follow incentive feed on stream for all registered users (migrated participants directly in our DB)

          this.logger.info(
            `Stream.Service Info - followDailyQuizFeedMigration:`,
            {
              profileId: participant.profileId,
            },
          );

          this.logger.info(
            `Stream.Service Info - followDailyQuizFeedMigration: adding follow`,
          );
          follows.push({
            source: 'user:' + participant.profileId,
            target: 'incentives:' + participant.incentiveId,
          });

          if (follows.length > 100) {
            const done = await this.client.followMany(follows);
            if (!!done) follows = [];
          }
        } catch (err) {
          this.logger.error(
            `Stream.Service Error - followDailyQuizFeedMigration: ${err.message}`,
            err.response?.body,
          );
        }
      }
      if (follows.length) await this.client.followMany(follows);
    } catch (e) {
      this.logger.error(
        `Stream.Service Error - followDailyQuizFeedMigration: ${e.message}`,
        e.response?.body,
      );
    }
  }

  async createPost(
    profileId: string,
    event: object,
  ): Promise<{ success: boolean; activityId: string }> {
    this.logger.info('StreamPostsService.addPost', {
      event,
      profileId: profileId,
    });
    const organisationId = event['organisationId'];
    const postId = event['postId'];
    const users = event['users'];
    const organisations = event['organisations'];

    if (!postId)
      return {
        success: false,
        activityId: '',
      };

    const membership = await this.membershipsService.findMembership(
      organisationId,
      profileId,
    );

    const post = await this.client.collections.add(
      'posts',
      postId,
      Object.assign(
        {
          profileId: profileId,
          permission: membership ? membership.permissions : [],
          users: users,
          organisations: organisations,
          createdAt: new Date(),
          scheduledAt: event['scheduledAt'],
          id: postId,
        },
        event,
      ),
    );

    const postInDb = await this.postsService.findById(postId);

    let activityId = '';
    if (postInDb.status === PostStatus.Live) {
      const publishPostDetails = await this.publishPost(
        profileId,
        post,
        membership,
      );
      activityId = publishPostDetails.activityId;
      await this.organisationLoyaltyPointsService.update({
        where: {
          type: ActivityType.CreatePost,
          organisationId: organisationId,
          [Op.and]: [
            Sequelize.literal(
              `placeholders::text ILIKE '%"postId": "${postId}"%'`,
            ), // Convert JSONB to text for searching
          ],
        },
        update: {
          placeholders: Sequelize.literal(
            `jsonb_set(placeholders, '{streamActivityId}', '"${activityId}"', true)`,
          ),
        },
      });
    }

    return {
      success: true,
      activityId,
    };
  }

  async publishPost(
    profileId: string,
    post: any,
    membership: Membership,
  ): Promise<{ success: boolean; activityId: string }> {
    const organisationId = post.data.organisationId;
    const parentOrgId = post.data.parentOrgId;
    const postId = post.data.postId;
    const users = post.data.users;
    const organisations = post.data.organisations;
    this.logger.info('StreamPostsService.publishPost', {
      organisationId,
      parentOrgId,
      postId,
      profileId,
    });

    const profile = await this.profilesService.findById(profileId);

    const organisationRef = this.client.collections.entry(
      'organisations',
      organisationId,
    );

    let activity;
    let isPartnerPost = false;

    // Check if this is a partner post (has both parentOrgId and organisationId)
    if (parentOrgId) {
      isPartnerPost = true;

      const currentMonth = moment().startOf('month').toDate();
      const nextMonth = moment().endOf('month').toDate();

      const partnerOrgResults = await this.sequelize.query(
        `SELECT * FROM "PartnerOrganisations" WHERE "childOrgId" = ? AND "parentOrgId" = ? AND "status" = 'approved'`,
        {
          replacements: [organisationId, parentOrgId],
          type: 'SELECT',
          raw: true,
        },
      );

      const partnerOrgArray = partnerOrgResults as any[];

      if (!partnerOrgArray || partnerOrgArray.length === 0) {
        this.logger.error(
          `Stream.Service Error - publishPost: No valid partnership found between parent ${parentOrgId} and child ${organisationId}`,
        );
        return { success: false, activityId: '' };
      }

      const partnerOrgData = partnerOrgArray[0] as any;
      const postsLimit = partnerOrgData.postsLimit || 2;

      // Count how many partner posts were already made this month
      const countResults = await this.sequelize.query(
        `SELECT COUNT(*) as count FROM "Posts" WHERE "organisationId" = ? AND "parentOrgId" = ? AND "createdAt" BETWEEN ? AND ?`,
        {
          replacements: [organisationId, parentOrgId, currentMonth, nextMonth],
          type: 'SELECT',
          raw: true,
        },
      );

      const countData = countResults[0] as any;
      const count = parseInt(countData.count as string);

      if (count >= postsLimit) {
        this.logger.error(
          `Stream.Service Error - publishPost: Monthly limit of ${postsLimit} partner posts reached for ${organisationId} to ${parentOrgId}`,
        );
        return { success: false, activityId: '' };
      }

      // For partner posts, use enrichment to get parent organization details
      // First, add the parent organization reference to our collections
      const parentOrgRef = this.client.collections.entry(
        'organisations',
        parentOrgId,
      );

      // Get the actual organization data for enrichment
      const parentOrganisation = await this.organisationsService.findById(
        parentOrgId,
      );

      // Get the parent organisation feed
      const feedParentOrg = this.client.feed(
        'organisations',
        organisationId
      );

      // Use enrichment to include both parent and child organization details
      const params = {
        actor: 'SO:' + profileId,
        verb: 'organisations',
        // Set the object to the parent org reference
        object: parentOrgRef,
        post: post,
        foreign_id: `posts:${postId}`,
        time: new Date().toISOString(),
        user: {
          name: profile.name,
          image: profile.image,
          id: profile.id,
        },
        permission: membership ? membership.permissions : [],
        isPartnerPost: true,
        // Include the child org reference
        childOrganisation: organisationRef,
        // Set the origin to make it look like it's from the child org feed
        origin: `organisations:${organisationId}`,
      };

      // Add activity to the parent organization feed
      // This will make it appear in the parent feed with the child org reference
      activity = await feedParentOrg.addActivity(params);

      // Log the created partner post
      this.logger.info('StreamPostsService.publishPartnerPost', {
        childOrgId: organisationId,
        parentOrgId: parentOrgId,
        postId,
        activityId: activity.id,
      });
    } else if (post.data.postAudience.isMyOrganisation) {
      // for MyOrganisation
      const orgMembersFeed = this.client.feed(
        'org_feed_members',
        organisationId,
      );

      const params = {
        actor: 'SO:' + profileId,
        verb: 'org_feed_members',
        object: organisationRef,
        post: post,
        foreign_id: `posts:${postId}`,
        time: new Date().toISOString(),
        user: {
          name: profile.name,
          image: profile.image,
          id: profile.id,
        },
        permission: membership ? membership.permissions : [],
      };

      activity = await orgMembersFeed.addActivity(params);
    } else if (post.data.postAudience.connectedOrganisations?.length) {
      const listOfConnectedOrgs =
        post.data.postAudience.connectedOrganisations.map(
          orgId => `org_feed_targeted:${orgId}`,
        );

      const userFeed = this.client.feed('user', profileId);
      const params = {
        actor: 'SO:' + profileId,
        verb: 'org_feed_targeted',
        object: organisationRef,
        post: post,
        to: listOfConnectedOrgs,
        foreign_id: `posts:${postId}`,
        time: new Date().toISOString(),
        user: {
          name: profile.name,
          image: profile.image,
          id: profile.id,
        },
        permission: membership ? membership.permissions : [],
      };

      try {
        activity = await userFeed.addActivity(params);
      } catch (e) {
        this.logger.error(
          `Stream.Service Error - addPost: ${e.message}`,
          e.response?.body,
        );
      }
    } else {
      // for Everyone; (post.data.postAudience.isFollowers)
      const feedOrganisation = this.client.feed(
        'organisations',
        organisationId,
      );

      const params = {
        actor: 'SO:' + profileId,
        verb: 'organisations',
        object: organisationRef,
        post: post,
        foreign_id: `posts:${postId}`,
        time: new Date().toISOString(),
        user: {
          name: profile.name,
          image: profile.image,
          id: profile.id,
        },
        permission: membership ? membership.permissions : [],
      };

      activity = await feedOrganisation.addActivity(params);
    }

    if (users.length > 0) {
      for (const mentionUser of users) {
        if (
          !post.data?.postAudience?.isMyOrganisation &&
          !post.data?.postAudience?.connectedOrganisations?.length &&
          profile.id !== mentionUser.id
        ) {
          await this.notificationsService.createNotification({
            ownerProfileId: mentionUser.id,
            profileId: profile.id,
            organisationId: organisationId,
            type: NotificationType.PostMention,
            data: {
              postId,
              activityId: activity.id,
            },
          });
        }
      }

      const profileIds = users.map(user => user.id);
      const replacementOrg = await this.organisationsService.findById(
        organisationId,
      );
      const replacements = [replacementOrg.name];

      await this.notificationsService.sendPushNotification({
        profileIds,
        replacements,
        messageType: NotificationMessage.PostMention,
        route: encodePathParams(Routes.organisationPost, {
          vanityId: organisationId,
          id: activity.id,
          userId: organisationId,
        }),
      });
    }

    if (organisations && organisations.length > 0) {
      for (const mentionOrg of organisations) {
        const orgMemberships = await this.membershipsService.findAll(
          {
            organisationId: mentionOrg.id,
            permissions: {
              [Op.overlap]: [
                MembershipPermission.Owner,
                MembershipPermission.Admin,
                MembershipPermission.HiddenAdmin,
                MembershipPermission.Manager,
                MembershipPermission.Editor,
              ],
            },
            status: MembershipStatus.Active,
          },
          {
            includeParams: [
              {
                model: Profile,
                as: 'profile',
                attributes: ['id', 'name', 'image', 'lastOnlineAt'],
              },
            ],
          },
        );
        for (const orgMembership of orgMemberships) {
          const mentionOrgUser = orgMembership.profile;
          !post.data?.postAudience?.isMyOrganisation &&
            !post.data?.postAudience?.connectedOrganisations?.length &&
            (await this.notificationsService.createNotification({
              ownerProfileId: mentionOrgUser.id,
              profileId: profile.id,
              organisationId: organisationId,
              type: NotificationType.OrgPostMention,
              data: {
                postId,
                organisationId: mentionOrg.id,
                activityId: activity.id,
              },
            }));
        }

        const profileIds = orgMemberships.map(user => user.profile.id);
        const replacementOrg = await this.organisationsService.findById(
          organisationId,
        );
        const mentionedOrg = await this.organisationsService.findById(
          mentionOrg.id,
        );
        const replacements = [replacementOrg.name, mentionedOrg.name];

        await this.notificationsService.sendPushNotification({
          profileIds,
          replacements,
          messageType: NotificationMessage.OrgPostMention,
          route: encodePathParams(Routes.organisationPost, {
            vanityId: replacementOrg?.vanityId,
            id: activity.id,
            userId: organisationId,
          }),
        });
      }
    }

    return {
      success: true,
      activityId: activity.id,
    };
  }

  async addPost(
    profileId: string,
    event: object,
  ): Promise<{ success: boolean; activityId: string }> {
    this.logger.info('StreamPostsService.addPost', {
      event,
      profileId: profileId,
    });
    const organisationId = event['organisationId'];
    const postId = event['postId'];
    const users = event['users'];
    const mentionedOrganisations = event['organisations'];

    if (!postId)
      return {
        success: false,
        activityId: '',
      };

    const membership = await this.membershipsService.findMembership(
      organisationId,
      profileId,
    );
    const profile = await this.profilesService.findById(profileId);

    const post = await this.client.collections.add(
      'posts',
      postId,
      Object.assign(
        {
          profileId: profileId,
          permission: membership ? membership.permissions : [],
          users: users,
          organisations: mentionedOrganisations,
          createdAt: new Date(),
          id: postId,
        },
        event,
      ),
    );

    const organisationRef = this.client.collections.entry(
      'organisations',
      organisationId,
    );
    let activity;

    if (post.data.postAudience.isMyOrganisation) {
      // for MyOrganisation
      const orgMembersFeed = this.client.feed(
        'org_feed_members',
        organisationId,
      );

      const params = {
        actor: 'SO:' + profileId,
        verb: 'org_feed_members',
        object: organisationRef,
        post: post,
        foreign_id: `posts:${postId}`,
        time: new Date().toISOString(),
        user: {
          name: profile.name,
          image: profile.image,
          id: profile.id,
        },
        permission: membership ? membership.permissions : [],
      };

      activity = await orgMembersFeed.addActivity(params);
    } else if (post.data.postAudience.connectedOrganisations?.length) {
      const listOfConnectedOrgs =
        post.data.postAudience.connectedOrganisations.map(
          orgId => `org_feed_targeted:${orgId}`,
        );

      const userFeed = this.client.feed('user', profileId);
      const params = {
        actor: 'SO:' + profileId,
        verb: 'org_feed_targeted',
        object: organisationRef,
        post: post,
        to: listOfConnectedOrgs,
        foreign_id: `posts:${postId}`,
        time: new Date().toISOString(),
        user: {
          name: profile.name,
          image: profile.image,
          id: profile.id,
        },
        permission: membership ? membership.permissions : [],
      };

      try {
        activity = await userFeed.addActivity(params);
      } catch (e) {
        this.logger.error(
          `Stream.Service Error - addPost: ${e.message}`,
          e.response?.body,
        );
      }
    } else {
      // for Everyone; (post.data.postAudience.isFollowers)
      const feedOrganisation = this.client.feed(
        'organisations',
        organisationId,
      );

      const params = {
        actor: 'SO:' + profileId,
        verb: 'organisations',
        object: organisationRef,
        post: post,
        foreign_id: `posts:${postId}`,
        time: new Date().toISOString(),
        user: {
          name: profile.name,
          image: profile.image,
          id: profile.id,
        },
        permission: membership ? membership.permissions : [],
      };

      activity = await feedOrganisation.addActivity(params);
    }

    if (users.length > 0) {
      for (const mentionUser of users) {
        if (
          !post.data?.postAudience?.isMyOrganisation &&
          !post.data?.postAudience?.connectedOrganisations?.length &&
          profile.id !== mentionUser.id
        ) {
          await this.notificationsService.createNotification({
            ownerProfileId: mentionUser.id,
            profileId: profile.id,
            organisationId: organisationId,
            type: NotificationType.PostMention,
            data: {
              postId,
              activityId: activity.id,
            },
          });
        }
      }

      const profileIds = users.map(user => user.id);
      const replacementOrg = await this.organisationsService.findById(
        organisationId,
      );
      const replacements = [replacementOrg.name];

      await this.notificationsService.sendPushNotification({
        profileIds,
        replacements,
        messageType: NotificationMessage.PostMention,
        route: encodePathParams(Routes.organisationPost, {
          vanityId: organisationId,
          id: activity.id,
          userId: organisationId,
        }),
      });
    }

    if (mentionedOrganisations && mentionedOrganisations.length > 0) {
      for (const mentionOrg of mentionedOrganisations) {
        const orgMemberships = await this.membershipsService.findAll(
          {
            organisationId: mentionOrg.id,
            permissions: {
              [Op.overlap]: [
                MembershipPermission.Owner,
                MembershipPermission.Admin,
                MembershipPermission.HiddenAdmin,
                MembershipPermission.Manager,
                MembershipPermission.Editor,
              ],
            },
            status: MembershipStatus.Active,
          },
          {
            includeParams: [
              {
                model: Profile,
                as: 'profile',
                attributes: ['id', 'name', 'image', 'lastOnlineAt'],
              },
            ],
          },
        );
        for (let orgMembership of orgMemberships) {
          const mentionOrgUser = orgMembership.profile;
          !post.data?.postAudience?.isMyOrganisation &&
            !post.data?.postAudience?.connectedOrganisations?.length &&
            (await this.notificationsService.createNotification({
              ownerProfileId: mentionOrgUser.id,
              profileId: profile.id,
              organisationId: organisationId,
              type: NotificationType.OrgPostMention,
              data: {
                postId,
                organisationId: mentionOrg.id,
                activityId: activity.id,
              },
            }));
        }

        const profileIds = orgMemberships.map(user => user.profile.id);
        const postingOrg = await this.organisationsService.findById(
          organisationId,
        );
        const replacements = [postingOrg.name, mentionOrg.name];

        await this.notificationsService.sendPushNotification({
          profileIds,
          replacements,
          messageType: NotificationMessage.OrgPostMention,
          route: encodePathParams(Routes.organisationPost, {
            vanityId: postingOrg?.vanityId,
            id: activity.id,
            userId: organisationId,
          }),
        });
      }
    }

    return {
      success: true,
      activityId: activity.id,
    };
  }

  async removePost(
    postId: string,
    organisationId: string,
    profileId: string,
    isRepost: boolean,
    activityId?: string,
  ): Promise<boolean> {
    this.logger.info('StreamPostsService.removePost', {
      postId,
      activityId,
      organisationId,
      profileId,
    });

    const membership = await this.membershipsService.findMembership(
      organisationId,
      profileId,
    );
    let currPost;

    if (membership && membership.permissions) {
      try {
        currPost = await this.client.collections.get('posts', postId);
        await this.client.collections.delete('posts', postId);
      } catch (e) {
        this.logger.error(
          `Stream.Service Error - removePost: ${e.message}`,
          e.response?.body,
        );
      }

      //must remove activity from 'Origin feed' for it to fan-out to other feeds
      //https://support.getstream.io/hc/en-us/articles/4404359414551-Deleting-Activities-with-Stream-Feeds-API-

      let feed;
      // Check if currPost and postAudience exist before accessing properties
      if (currPost && currPost.data && currPost.data.postAudience && currPost.data.postAudience.isMyOrganisation === true) {
        feed = this.client.feed('org_feed_members', organisationId);
      } else if (
        currPost && currPost.data && currPost.data.postAudience && 
        currPost.data.postAudience.connectedOrganisations?.length
      ) {
        //delete from author feed (fans out to CO feeds)
        const authorUserId = currPost.data?.profileId;
        feed = this.client.feed('user', authorUserId);
      } else {
        // Default to organisations feed if other conditions don't match or data is missing
        feed = this.client.feed('organisations', organisationId);
      }
      if (activityId) {
        try {
          await feed.removeActivity(activityId);
          this.logger.info('StreamPostsService.removePost success', {
            postId,
            activityId,
            organisationId,
            profileId,
          });
          await this.postsService.removePost(postId, {
            profileId,
          });
          return true;
        } catch (e) {
          this.logger.error(
            `Stream.Service Error - removePost: ${e.message}`,
            e.response?.body,
          );
        }
      }
      try {
        await feed.removeActivity({
          foreignId: 'posts:' + postId,
        });
        this.logger.info('StreamPostsService.removePost success', {
          postId,
          activityId,
          organisationId,
          profileId,
        });
        await this.postsService.removePost(postId, {
          profileId,
        });
        return true;
      } catch (e) {
        this.logger.error(
          `Stream.Service Error - removePost: ${e.message}`,
          e.response?.body,
        );
        return false;
      }
    } else {
      this.logger.error('StreamPostsService.removePost user unauthorised', {
        postId,
        activityId,
        organisationId,
        profileId,
      });
      return false;
    }
  }

  async updatePost(
    postId: string,
    postData: any,
    profileId: string,
  ): Promise<boolean> {
    this.logger.info('StreamPostsService.updatePost', {
      postId,
      postData,
      profileId: profileId,
    });
    try {
      const currPost = await this.client.collections.get('posts', postId);
      const currPostData: UpdatePostInput = currPost.data || {};

      const updPostData: UpdatePostInput = {};

      const newPostData = {
        ...currPostData,
        ...updPostData,
      };

      postData.image
        ? (newPostData.image = postData.image as string)
        : postData.image === ''
        ? delete newPostData?.image
        : '';

      postData.imageHeight
        ? (newPostData.imageHeight = postData.imageHeight as number)
        : postData.imageHeight === 0
        ? delete newPostData?.imageHeight
        : '';

      postData.imageWidth
        ? (newPostData.imageWidth = postData.imageWidth as number)
        : postData.imageWidth === 0
        ? delete newPostData?.imageWidth
        : '';

      postData.imageFormat
        ? (newPostData.imageFormat = postData.imageFormat as string)
        : postData.imageFormat === ''
        ? delete newPostData?.imageFormat
        : '';

      postData.multipleImages
        ? (newPostData.multipleImages =
            postData.multipleImages as MultipleImageDetailsInput[])
        : postData.multipleImages === '' ||
          !postData.hasOwnProperty('multipleImages')
        ? delete newPostData?.multipleImages
        : '';

      postData.imageCategory
        ? (newPostData.imageCategory =
            postData.imageCategory as PostImageCategory)
        : postData.imageCategory === ''
        ? delete newPostData?.imageCategory
        : '';

      postData.video
        ? (newPostData.video = postData.video as string)
        : postData.video === ''
        ? delete newPostData.video
        : '';

      postData.videoHeight
        ? (newPostData.videoHeight = postData.videoHeight as number)
        : postData.videoHeight === 0
        ? delete newPostData?.videoHeight
        : '';

      postData.videoWidth
        ? (newPostData.videoWidth = postData.videoWidth as number)
        : postData.videoWidth === 0
        ? delete newPostData?.videoWidth
        : '';

      postData.videoFormat
        ? (newPostData.videoFormat = postData.videoFormat as string)
        : postData.videoFormat === ''
        ? delete newPostData?.videoFormat
        : '';

      postData.document
        ? (newPostData.document = postData.document as string)
        : postData.document === ''
        ? delete newPostData.document
        : '';

      postData.documentUrl
        ? (newPostData.documentUrl = postData.documentUrl as string)
        : postData.documentUrl === ''
        ? delete newPostData.documentUrl
        : '';

      postData.documentSize
        ? (newPostData.documentSize = postData.documentSize as string)
        : postData.documentSize === ''
        ? delete newPostData.documentSize
        : '';

      postData.documentFormat
        ? (newPostData.documentFormat = postData.documentFormat as string)
        : postData.documentFormat === ''
        ? delete newPostData.documentFormat
        : '';

      postData.documentName
        ? (newPostData.documentName = postData.documentName as string)
        : postData.documentName === ''
        ? delete newPostData.documentName
        : '';

      newPostData.text = postData.text as string;
      newPostData.type = postData.type as PostType;

      const streamPostData = { ...newPostData };

      if (postData.users) {
        streamPostData.users = postData.users;
      }

      if (postData.organisations) {
        streamPostData.organisations = postData.organisations;
      }

      await this.client.collections.update('posts', postId, streamPostData);

      const postDataDb = {
        ...newPostData,
      };

      if (newPostData.image) {
        postDataDb['mediaHeight'] = newPostData.imageHeight;
        postDataDb['mediaWidth'] = newPostData.imageWidth;
        postDataDb['mediaFormat'] = newPostData.imageFormat;
        delete postDataDb.imageHeight;
        delete postDataDb.imageWidth;
        delete postDataDb.imageFormat;
      }

      if (newPostData.video) {
        postDataDb['mediaHeight'] = newPostData.videoHeight;
        postDataDb['mediaWidth'] = newPostData.videoWidth;
        postDataDb['mediaFormat'] = newPostData.videoFormat;
        delete postDataDb.videoHeight;
        delete postDataDb.videoWidth;
        delete postDataDb.videoFormat;
      }

      const res = await this.postsService.updatePost(postId, postDataDb, {
        profileId,
      });

      return true;
    } catch (err) {
      return false;
    }
  }

  async updateStreamPostScheduleTime(
    postId: string,
    profileId: string,
    scheduledAt: Date,
  ): Promise<boolean> {
    this.logger.info('StreamPostsService.updatePostScheduleTime', {
      postId,
      profileId: profileId,
      scheduledAt,
    });

    try {
      const currPost = await this.client.collections.get('posts', postId);
      const currPostData: UpdatePostInput = currPost.data || {};

      const updPostData = {
        scheduledAt: scheduledAt,
      };

      const newPostData = {
        ...currPostData,
        ...updPostData,
      };

      await this.client.collections.update('posts', postId, newPostData);
      return true;
    } catch (err) {
      return false;
    }
  }

  async addPostLike(
    profileId: string,
    activityId: string,
    feedId: string,
    postId?: string,
  ): Promise<boolean> {
    this.logger.info('StreamPostsService.addPostLike', {
      profileId,
      activityId,
      feedId,
      postId,
    });
    try {
      const currPost =
        postId && (await this.client.collections.get('posts', postId));
      const feed = currPost?.data?.postAudience?.isMyOrganisation
        ? this.client.feed('org_feed_members', feedId)
        : this.client.feed('organisations', feedId);
      const activityData = await feed.get({ limit: 1, id_gte: activityId });
      const activity = activityData.results[0];

      this.client.activityPartialUpdate({
        id: activityId,
        set: {
          likes: {
            ...(activity.likes || {}),
            [profileId]: true,
          },
          likesCount: (activity.likesCount || 0) + 1,
        },
      });

      const organisationId =
        activity && activity.object.split('SO:organisations:')[1];
      postId =
        !postId &&
        activity != null &&
        activity.post.split('SO:posts:').length > 0
          ? activity.post.split('SO:posts:')[1]
          : postId;

      // Check if the user has already liked the post
      const repeatActivity = await this.activitiesService.findOne({
        type: ActivityType.PostInteractions,
        profileId,
        postId,
        data: {
          [Op.contains]: {
            type: 'PostLike',
          },
        },
      });
      const organisation = await this.organisationsService.findById(
        organisationId,
      );

      await this.activitiesService.createUserActivity({
        type: ActivityType.PostInteractions,
        schema: postLikeActivityDataDto,
        data: {
          likedById: profileId,
          type: 'PostLike',
        },
        profileId,
        addLoyaltyPoints: !repeatActivity,
        postId,
        organisationId,
        placeholders: {
          postActionType: 'PostLike',
          userId: profileId,
          vanityId: organisation.vanityId,
          streamActivityId: activityId,
          organisationId,
          organisationName: organisation.name,
        },
      });

      if (currPost.data.profileId !== profileId) {
        const profile = await this.profilesService.findById(profileId);
        const profileObj = profile.get({ plain: true }) as Profile;
        const usersArr = [
          {
            profileId: profileObj.id,
            id: profileObj.id,
            name: profileObj.name,
            image: profileObj.image,
          },
        ];

        //Feature Flag notifications for Post Reactions to Beta/Local for time being
        if (
          config.NODE_ENV === 'development' ||
          config.SERVICE === 'api-beta'
        ) {
          !currPost.data?.postAudience?.isMyOrganisation &&
            !currPost.data?.postAudience?.connectedOrganisations?.length &&
            (await this.notificationsService.createNotificationWithGrouping({
              ownerProfileId: currPost.data.profileId,
              profileId: currPost.data.profileId,
              type: NotificationType.PostReact,
              organisationId: organisationId,
              data: {
                users: usersArr,
                postId,
                activityId: activityId,
              },
            }));

          const usersFullNames = usersArr.map(
            ({ name }: { name: string }) => name,
          );
          const countOfUsers = usersArr.length;
          const objectOfStrings = getStringsForNotifications(
            countOfUsers,
            usersFullNames,
          );

          const profileIds = [currPost.data.profileId];
          const replacements = [
            objectOfStrings.usersFullNamesString,
            objectOfStrings.other,
            objectOfStrings.has,
          ];

          await this.notificationsService.sendPushNotification({
            profileIds,
            replacements,
            messageType: NotificationMessage.PostReact,
            route: encodePathParams(Routes.organisationPost, {
              vanityId: organisationId,
              id: activityId,
              userId: organisationId,
            }),
          });
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  async removePostLikeLegacy(reactionId: string): Promise<boolean> {
    try {
      await this.client.reactions.delete(reactionId);
      return true;
    } catch (e) {
      return false;
    }
  }

  async checkNewPost(profileId: string, activityId: string): Promise<boolean> {
    try {
      const feedUser = this.client.feed('user', profileId);
      const getLastActivity = await feedUser.get({ limit: 1 });
      if (!getLastActivity) return false;
      const activity = getLastActivity.results[0];
      if (activity.id !== activityId) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  async removePostLike(
    profileId: string,
    activityId: string,
    feedId: string,
    reactionId: string,
    postId?: string,
  ): Promise<boolean> {
    try {
      const currPost =
        postId && (await this.client.collections.get('posts', postId));
      const feed = currPost?.data?.postAudience?.isMyOrganisation
        ? this.client.feed('org_feed_members', feedId)
        : this.client.feed('organisations', feedId);

      const activityData = await feed.get({ limit: 1, id_gte: activityId });
      const activity = activityData.results[0];

      if (reactionId) {
        await this.removePostLikeLegacy(reactionId);
      }

      const newLikes = { ...(activity.likes || {}) };
      if (newLikes[profileId]) delete newLikes[profileId];

      const newLikesCount =
        (activity.likesCount || 0) - 1 >= 0
          ? (activity.likesCount || 0) - 1
          : 0;

      this.client.activityPartialUpdate({
        id: activityId,
        set: {
          likes: newLikes,
          likesCount: newLikesCount,
        },
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  async addCommentLike(
    profileId: string,
    activityId: string,
    commentId: string,
    postAuthorId: string,
    organisationId: string,
    postId: string,
  ): Promise<boolean> {
    try {
      const comment = await this.client.reactions.get(commentId);
      if (!comment) return false;

      const updData = { ...comment.data };
      if (!updData.likes) {
        updData.likes = {};
      }
      updData.likes[profileId] = true;
      updData.likesCount = (updData.likesCount || 0) + 1;
      await this.client.reactions.update(commentId, updData);

      const [profile, post] = await Promise.all([
        this.profilesService.findById(profileId, {
          includeParams: [
            {
              model: Membership,
              as: 'memberships',
              where: {
                isPrimary: true,
              },
              include: [
                {
                  model: Organisation,
                  as: 'organisation',
                  attributes: ['id', 'name'],
                },
              ],
            },
          ],
        }),
        this.client.collections.get('posts', postId),
      ]);

      const profileObj = profile.get({ plain: true }) as Profile;
      const usersArr = [
        {
          id: profileObj.id,
          name: profileObj.name,
          image: profileObj.image,
        },
      ];
      if (
        !post.data?.postAudience?.isMyOrganisation &&
        !post.data?.postAudience?.connectedOrganisations?.length &&
        comment.user_id !== profileId
      ) {
        await this.notificationsService.createNotificationWithGrouping({
          ownerProfileId: comment.user_id,
          profileId: profileId,
          type: NotificationType.CommentReact,
          organisationId: organisationId,
          data: {
            users: usersArr,
            postId,
            activityId: activityId,
          },
        });

        const usersFullNames = usersArr.map(
          ({ name }: { name: string }) => name,
        );
        const countOfUsers = usersArr.length;
        const objectOfStrings = getStringsForNotifications(
          countOfUsers,
          usersFullNames,
        );

        const profileIds = [comment.user_id];
        const replacementOrg = await this.organisationsService.findById(
          organisationId,
        );
        const replacements = [
          objectOfStrings.usersFullNamesString,
          objectOfStrings.other,
          objectOfStrings.has,
          replacementOrg.name,
        ];

        await this.notificationsService.sendPushNotification({
          profileIds,
          replacements,
          messageType: NotificationMessage.CommentReact,
          route: encodePathParams(Routes.organisationPost, {
            vanityId: organisationId,
            id: activityId,
            userId: organisationId,
          }),
        });
      }

      const [repeatActivity, organisationRole, organisation] =
        await Promise.all([
          // Check if the user has already liked the comment
          this.activitiesService.findOne({
            type: ActivityType.PostCommentLike,
            postId,
            data: {
              [Op.contains]: {
                likedById: profileId,
                commentId: comment.id,
              },
            },
          }),
          this.membershipsService.findOne({
            profileId: profile.id,
            organisationId: organisationId,
            status: MembershipStatus.Active,
            permissions: {
              [Op.overlap]: [
                MembershipPermission.Owner,
                MembershipPermission.Admin,
                MembershipPermission.HiddenAdmin,
                MembershipPermission.Manager,
                MembershipPermission.Editor,
              ],
            },
          }),
          this.organisationsService.findById(organisationId),
        ]);

      await this.activitiesService.createUserActivity({
        type: ActivityType.PostCommentLike,
        schema: commentLikeActivityDataDto,
        data: {
          likedById: profileId,
          postAuthorId,
          commentId: comment.id,
        },
        profileId: comment.user_id,
        postId,
        organisationId: organisation?.id,
        addLoyaltyPoints: !repeatActivity,
        placeholders: {
          id: profileId,
          createdById: profileId,
          name: profile.name,
          vanityId: organisation.vanityId,
          organisationId: organisation?.id,
          streamActivityId: activityId,
        },
      });

      if (!!organisationRole) {
        try {
          await this.activitiesService.createOrganisationActivity({
            type: ActivityType.OwnerPostCommentLike,
            data: {
              likedById: profileId,
              postAuthorId,
              commentId: comment.id,
            },
            postId,
            organisationId: organisation.id,
            createdById: postAuthorId,
          });
          if (profileId !== comment.user_id) {
            await this.organisationLoyaltyPointsService.addPoints({
              type: ActivityType.OwnerPostCommentLike,
              organisationId: organisation.id,
              placeholders: {
                createdById: profileId,
                name: profile.name,
                vanityId: organisation.vanityId,
                organisationId: organisation.id,
                streamActivityId: activityId,
                activityType: ActivityType.OwnerPostCommentLike,
              },
            });
          }
        } catch (error) {
          this.logger.error(
            'Error creating OwnerPostCommentLike activity:',
            error,
          );
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  async removeCommentLike(
    profileId: string,
    activityId: string,
    commentId: string,
    reactionId: string,
  ): Promise<boolean> {
    try {
      const comment = await this.client.reactions.get(commentId);
      if (!comment) return false;

      if (reactionId) {
        await this.removePostLikeLegacy(reactionId);
      }

      const updData = { ...comment.data };
      if (!updData.likes) {
        updData.likes = {};
      }
      if (updData.likes[profileId]) {
        delete updData.likes[profileId];
      }
      updData.likesCount =
        (updData.likesCount || 0) - 1 >= 0 ? (updData.likesCount || 0) - 1 : 0;
      await this.client.reactions.update(commentId, updData);
      return true;
    } catch (e) {
      return false;
    }
  }

  async createComment(
    activityId: string,
    commentData,
    user: ICurrentUser,
    postAuthorId: string,
    organisationId: string,
    postId: string,
  ): Promise<any> {
    const transaction = await this.sequelize.transaction();
    try {
      const userId = user.profileId;
      const [profile, post, organisationRole] = await Promise.all([
        this.profilesService.findById(userId, {
          includeParams: [
            {
              model: Membership,
              as: 'memberships',
              where: {
                isPrimary: true,
              },
              include: [
                {
                  model: Organisation,
                  as: 'organisation',
                  attributes: ['id', 'name'],
                },
              ],
            },
          ],
          transaction,
        }),
        this.client.collections.get('posts', postId),
        this.membershipsService.findOne(
          {
            profileId: userId,
            organisationId: organisationId,
            status: MembershipStatus.Active,
            permissions: {
              [Op.overlap]: [
                MembershipPermission.Owner,
                MembershipPermission.Admin,
                MembershipPermission.HiddenAdmin,
                MembershipPermission.Manager,
                MembershipPermission.Editor,
              ],
            },
          },
          { transaction },
        ),
      ]);

      const membership = profile.memberships[0];
      const userOrganisation = membership.organisation;
      //const postOrganisationId = post.data?.organisationId; // Get the post's organisationId
      const profilePosition =
        membership?.status === MembershipStatus.Active
          ? membership?.position
          : null;
      const profileOrganisationName =
        membership?.status === MembershipStatus.Active
          ? userOrganisation
            ? userOrganisation?.name
            : membership?.organisationName
          : null;

      let parentComment;
      if (commentData.parentId) {
        parentComment = await this.client.reactions.get(commentData.parentId);

        if (!parentComment) return false;

        await this.client.reactions.addChild(
          'comment',
          parentComment,
          Object.assign(
            {
              user: profile,
              position: profilePosition,
              organisationName: profileOrganisationName,
            },
            commentData,
          ),
          { userId },
        );
      } else {
        await this.client.reactions.add(
          'comment',
          activityId,
          Object.assign(
            {
              user: profile,
              position: profilePosition,
              organisationName: profileOrganisationName,
            },
            commentData,
          ),
          { userId },
        );
      }

      if (!!organisationRole) {
        try {
          const isCommentReply = !!commentData.parentId;
          const activityType = isCommentReply
            ? ActivityType.OwnerPostCommentReply
            : ActivityType.OwnerPostComment;

          await this.activitiesService.createOrganisationActivity({
            type: activityType,
            createdById: userId,
            data: {
              commentById: userId,
              type: activityType,
            },
            organisationId: organisationId,
            postId,
          });

          const userReplyingToSelf =
            parentComment && userId === parentComment.user_id;

          if (isCommentReply && !userReplyingToSelf) {
            await this.organisationLoyaltyPointsService.addPoints({
              type: ActivityType.OwnerPostCommentReply,
              organisationId: organisationId,
              placeholders: {
                createdById: userId,
                name: profile.name,
                vanityId: userOrganisation.vanityId,
                organisationId: organisationId,
                streamActivityId: activityId,
                postId,
                activityType: ActivityType.OwnerPostCommentReply,
              },
            });
          }
        } catch (error) {
          this.logger.error('Error creating OwnerPostComment activity:', error);
        }
      }

      await this.activitiesService.createUserActivity({
        type: ActivityType.PostInteractions,
        schema: newCommentActivityDataDto,
        data: {
          commentById: userId,
          type: 'NewComment',
        },
        organisationId: userOrganisation?.id,
        postId,
        profileId: userId,
      });

      // Add entry in Getting started table
      await this.gettingStartedStepsService.createGettingStartedStep({
        profileId: userId,
        step: GettingStartedStepEnum.PostComment,
        transaction,
      });

      //notify the post author about the new comment
      if (postAuthorId !== userId) {
        const limitedProfileData = [
          {
            id: profile.id,
            name: profile.name,
            image: profile.image,
          },
        ];
        !post.data?.postAudience?.isMyOrganisation &&
          !post.data?.postAudience?.connectedOrganisations?.length &&
          (await this.notificationsService.createNotificationWithGrouping(
            {
              ownerProfileId: postAuthorId,
              profileId: userId,
              organisationId: organisationId,
              type: NotificationType.PostComment,
              data: {
                users: limitedProfileData,
                postId: postId,
                activityId: activityId,
              },
            },
            {
              transaction,
            },
          ));

        const usersFullNames = limitedProfileData.map(
          ({ name }: { name: string }) => name,
        );
        const countOfUsers = limitedProfileData.length;
        const objectOfStrings = getStringsForNotifications(
          countOfUsers,
          usersFullNames,
        );

        const profileIds = [postAuthorId];
        const replacements = [
          objectOfStrings.usersFullNamesString,
          objectOfStrings.other,
          objectOfStrings.has,
        ];

        await this.notificationsService.sendPushNotification({
          profileIds,
          replacements,
          messageType: NotificationMessage.PostComment,
          route: encodePathParams(Routes.organisationPost, {
            vanityId: organisationId,
            id: activityId,
            userId: organisationId,
          }),
        });
      }

      //notify users mentioned in the comment
      if (commentData.users.length > 0) {
        for (const mentionUser of commentData.users) {
          if (
            !post.data?.postAudience?.isMyOrganisation &&
            !post.data?.postAudience?.connectedOrganisations?.length &&
            mentionUser.id !== userId
          ) {
            await this.notificationsService.createNotification(
              {
                ownerProfileId: mentionUser.id,
                profileId: userId,
                organisationId: organisationId,
                type: NotificationType.CommentMention,
                data: {
                  postId,
                  activityId: activityId,
                },
              },
              {
                transaction,
              },
            );
          }
        }

        const profileIds = commentData.users.map(user => user.id);
        const replacements = [profile.name];

        await this.notificationsService.sendPushNotification({
          profileIds,
          replacements,
          messageType: NotificationMessage.CommentMention,
          route: encodePathParams(Routes.organisationPost, {
            vanityId: organisationId,
            id: activityId,
            userId: organisationId,
          }),
        });
      }

      //notify organisations mentioned in the comment
      if (commentData.organisations && commentData.organisations.length > 0) {
        for (const mentionOrg of commentData.organisations) {
          const orgMemberships = await this.membershipsService.findAll(
            {
              organisationId: mentionOrg.id,
              permissions: {
                [Op.overlap]: [
                  MembershipPermission.Owner,
                  MembershipPermission.Admin,
                  MembershipPermission.HiddenAdmin,
                  MembershipPermission.Manager,
                  MembershipPermission.Editor,
                ],
              },
              status: MembershipStatus.Active,
            },
            {
              includeParams: [
                {
                  model: Profile,
                  as: 'profile',
                  attributes: ['id', 'name', 'image', 'lastOnlineAt'],
                },
              ],
            },
          );
          for (const orgMembership of orgMemberships) {
            const mentionOrgUser = orgMembership.profile;
            !post.data?.postAudience?.isMyOrganisation &&
              !post.data?.postAudience?.connectedOrganisations?.length &&
              (await this.notificationsService.createNotification({
                ownerProfileId: mentionOrgUser.id,
                profileId: userId,
                organisationId: organisationId,
                type: NotificationType.OrgCommentMention,
                data: {
                  postId,
                  organisationId: mentionOrg.id,
                  activityId: activityId,
                },
              }));
          }

          const profileIds = orgMemberships.map(user => user.profile.id);
          const orgFromPost = await this.organisationsService.findById(
            organisationId,
          );
          const replacements = [profile.name, mentionOrg.name];

          await this.notificationsService.sendPushNotification({
            profileIds,
            replacements,
            messageType: NotificationMessage.OrgCommentMention,
            route: encodePathParams(Routes.organisationPost, {
              vanityId: orgFromPost?.vanityId,
              id: activityId,
              userId: organisationId,
            }),
          });
        }
      }

      await transaction.commit();
      return 'true';
    } catch (e) {
      await transaction.rollback();
      return e;
    }
  }

  async removeComment(commentId: string): Promise<boolean> {
    try {
      await this.client.reactions.delete(commentId);
      return true;
    } catch (e) {
      this.logger.error(
        `StreamPostsService Error: ${e.message}`,
        e.response?.body,
      );
      return false;
    }
    return false;
  }

  async updateComment(
    commentId: string,
    activityId: string,
    commentData: any,
  ): Promise<boolean> {
    try {
      const commentObj = await this.client.reactions.get(commentId);
      if (!commentObj) throw new Error();

      const newCommentObj = {
        ...commentObj.data,
        text: commentData.text,
      };

      await this.client.reactions.update(commentId, newCommentObj);
      return true;
    } catch (e) {
      this.logger.error(
        `StreamPostsService Error: ${e.message}`,
        e.response?.body,
      );

      return false;
    }
    return false;
  }

  async repostPost(
    profileId: string, //user that reposted
    activityId: string,
    organisationId: string,
    postData: object,
    user: ICurrentUser,
    postAuthorId: string, //user of original post
    origOrganisationId: string,
    postId: string,
  ): Promise<boolean> {
    this.logger.info('StreamPostsService.repostPost', {
      postData,
      activityId,
      postAuthorId,
      organisationId,
      origOrganisationId,
      postId,
      profileId,
    });
    try {
      const profile = await this.profilesService.findById(profileId);

      const users = postData['users'];
      const organisations = postData['organisations'];
      const parentPost = await this.postsService.createPost(
        {
          text: postData['text'],
          type: postData['type'],
          organisationId: postData['organisationId'],
          profileId,
          status: PostStatus.Repost,
        },
        {
          currentUser: user,
        },
      );

      const parentPostStream = await this.client.collections.add(
        'posts',
        parentPost.id,
        Object.assign(
          {
            profileId: profileId,
            users: users,
            organisations: organisations,
            createdAt: new Date(),
            id: parentPost.id,
            postId: parentPost.id,
          },
          postData,
        ),
      );

      //check that the post still exists in the orig organisation's feed
      const origOrgFeed = this.client.feed('organisations', origOrganisationId);
      const activities = await origOrgFeed.get({
        limit: 1,
        id_gte: activityId,
      });
      const activity = activities.results[0];
      if (!activity) throw new Error();

      let activityNew;
      const organisation = await this.client.collections.get(
        'organisations',
        organisationId,
      );

      let params = {
        actor: 'SO:' + profileId,
        verb: 'repost',
        repostData: activity,
        post: parentPostStream,
        time: new Date().toISOString(),
        object: organisation,
        foreign_id: `repost:${postId}`,
        user: {
          name: profile.name,
          image: profile.image,
          id: profile.id,
        },
        to: null,
      };

      if (parentPostStream.data.postAudience.isMyOrganisation) {
        // for MyOrganisation
        const orgMembersFeed = this.client.feed(
          'org_feed_members',
          organisationId,
        );

        activityNew = await orgMembersFeed.addActivity(params);
      } else if (
        parentPostStream.data.postAudience.connectedOrganisations?.length
      ) {
        // Targeted to specific organisations
        const listOfConnectedOrgs =
          parentPostStream.data.postAudience.connectedOrganisations.map(
            orgId => `org_feed_targeted:${orgId}`,
          );
        params.to = listOfConnectedOrgs;
        const userFeed = this.client.feed('user', profileId);

        try {
          activityNew = await userFeed.addActivity(params);
        } catch (e) {
          this.logger.error(
            `Stream.Service Error - addPost: ${e.message}`,
            e.response?.body,
          );
        }
      } else {
        // for Everyone; (post.data.postAudience.isFollowers)
        const feedOrganisation = this.client.feed(
          'organisations',
          organisationId,
        );

        activityNew = await feedOrganisation.addActivity(params);
      }

      const route = encodePathParams(Routes.organisationPost, {
        vanityId: organisationId,
        id: activityNew.id,
        userId: organisationId,
      });

      if (
        parentPostStream.data.postAudience?.isFollowers &&
        postAuthorId !== profileId
      ) {
        await this.notificationsService.createNotification({
          ownerProfileId: activity.user.id,
          profileId: activity.user.id,
          organisationId: organisationId,
          type: NotificationType.PostShared,
          data: {
            postId: postId,
            activityId: activityNew.id,
          },
        });

        const profileIds = [activity.user.id];
        const replacementOrg = await this.organisationsService.findById(
          organisationId,
        );
        const replacements = [replacementOrg.name];

        await this.notificationsService.sendPushNotification({
          profileIds,
          replacements,
          messageType: NotificationMessage.PostShared,
          route: route,
        });
      }

      //notify users mentioned in the new post
      if (users.length > 0) {
        for (const user of users) {
          if (
            !parentPostStream.data?.postAudience?.isMyOrganisation &&
            !parentPostStream.data?.postAudience?.connectedOrganisations
              ?.length &&
            user.id !== profileId
          ) {
            await this.notificationsService.createNotification({
              ownerProfileId: user.id,
              profileId: profileId,
              organisationId: organisationId,
              type: NotificationType.CommentMention,
              data: {
                postId: postId,
                activityId: activityNew.id,
              },
            });
          }
        }
        const profileIds = users.map(user => user.id);
        const replacements = [profile.name];
        await this.notificationsService.sendPushNotification({
          profileIds,
          replacements,
          messageType: NotificationMessage.CommentMention,
          route: route,
        });
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  async getLast3PostsOrg(profileId: string, lastActive: Date): Promise<any> {
    const user = this.client.feed('user', profileId);
    const activites = await user.get({ limit: 30 });
    await new Promise(resolve => setTimeout(resolve, 100));

    const profileLastOnline = moment(lastActive).format();

    const organisationIds = activites.results
      .filter(obj => obj.post)
      .reduce((acc, obj) => {
        const postDate = moment(obj.time).format();

        if (profileLastOnline < postDate) {
          const orgId = obj.object.split('SO:organisations:')[1];
          acc.length < 3 && !acc.includes(orgId) ? acc.push(orgId) : '';
        }

        return acc;
      }, []);

    return organisationIds;
  }

  async getStreamPostById(postId: string): Promise<any> {
    return await this.client.collections.get('posts', postId);
  }

  async migratePostCommentGettingStartedRecord({
    profileId,
    transaction,
  }: {
    profileId: string;
    transaction?: Transaction;
  }): Promise<void> {
    try {
      const myComments = await this.client.reactions.filter({
        user_id: profileId,
        kind: 'comment',
        limit: 1,
      });

      const commentsCount = (get(myComments, 'results', []) as Array<any>)
        .length;
      if (commentsCount > 0) {
        await this.gettingStartedStepsService.createGettingStartedStep({
          step: GettingStartedStepEnum.PostComment,
          profileId,
          transaction,
        });
      }
    } catch (e) {
      this.logger.error(
        `ChatService.createChatGettingStartedStep Error: ${e.message}`,
        e.response?.body,
      );
    }
  }

  async savePost(
    profileId: string,
    activityId: string,
    feedId: string,
    feedName: string,
  ): Promise<boolean> {
    this.logger.info('Starting savePost operation', {
      profileId,
      activityId,
      feedId,
      feedName,
    });

    try {
      // Validate inputs
      if (!profileId || !activityId || !feedId || !feedName) {
        this.logger.warn('savePost called with missing parameters', {
          profileId,
          activityId,
          feedId,
          feedName,
        });
        return false;
      }

      // Get the original activity
      const feed = this.client.feed(feedName, feedId);
      const activity = await this.getActivityById(feed, activityId);

      if (!activity) {
        this.logger.warn('Original activity not found', {
          activityId,
          feedId,
          feedName,
        });
        return false;
      }

      // Create the target string for user_saved feed
      const targetFeed = `user_saved:${profileId}`;
      // Check if activity already has targeting
      const existingTargets = activity.to || [];

      // If already saved, no need to save again
      if (existingTargets.includes(targetFeed)) {
        this.logger.info('Post already saved', { profileId, activityId });
        return true;
      }

      // Add the user_saved feed as a new target
      // Using updateActivityToTargets with added_targets parameter
      const updateResponse = await feed.updateActivityToTargets(
        activity.foreign_id, // foreign_id is required
        activity.time, // time is required
        null, // new_targets (null as we're not replacing all)
        [targetFeed], // added_targets
        null, // removed_targets
      );

      this.logger.debug('Activity targeting updated', {
        activityId,
        updateResponse,
        targetFeed,
      });

      // Track activity for SavePost
      const activityLog = {
        type: ActivityType.SavePost,
        profileId: profileId,
        data: {
          activityId,
          feedId,
          feedName,
        },
      };
      this.logger.debug('Attempting to track activity', activityLog);
      // Track activity for SavePost
      await this.activitiesService.create(activityLog);

      this.logger.info('Post saved successfully via targeting', {
        activityId,
        profileId,
      });

      return true;
    } catch (error) {
      this.logger.error('Error saving post:', {
        error: {
          message: error.message,
          stack: error.stack,
          response: error.response?.body,
        },
        profileId,
        activityId,
        feedId,
        feedName,
      });
      return false;
    }
  }

  async unsavePost(
    profileId: string,
    activityId: string,
    feedId: string,
    feedName: string,
  ): Promise<boolean> {
    this.logger.info('Starting unsavePost operation', {
      profileId,
      activityId,
      feedId,
      feedName,
    });

    try {
      // Get the original activity
      const feed = this.client.feed(feedName, feedId);
      const activity = await this.getActivityById(feed, activityId);

      if (!activity) {
        this.logger.warn('Original activity not found', {
          activityId,
          feedId,
          feedName,
        });
        return false;
      }

      // Target to remove
      const targetToRemove = `user_saved:${profileId}`;

      // Check if activity already has targeting
      const existingTargets = activity.to || [];

      // check if post exists in targeting
      if (existingTargets.includes(targetToRemove)) {
        // Remove the user_saved feed from the targeting
        // Using updateActivityToTargets with removed_targets parameter
        const updateResponse = await feed.updateActivityToTargets(
          activity.foreign_id, // foreign_id is required
          activity.time, // time is required
          null, // new_targets (null as we're not replacing all)
          null, // added_targets
          [targetToRemove], // removed_targets
        );

        // Track activity for UnsavePost
        await this.activitiesService.create({
          type: ActivityType.UnsavePost,
          profileId: profileId,
          data: {
            activityId,
            feedId,
            feedName,
          },
        });
        this.logger.info('Post unsaved successfully', {
          updateResponse,
        });
        return true;
      } else {
        this.logger.info('Post is not saved for user', {
          profileId,
          activityId,
        });
        return false;
      }
    } catch (error) {
      this.logger.error('Error unsaving post:', {
        error: {
          message: error.message,
          stack: error.stack,
          response: error.response?.body,
        },
        profileId,
        activityId,
      });
      return false;
    }
  }

  async getSavedPostIds(profileId: string): Promise<string[]> {
    try {
      const userSavedFeed = this.client.feed('user_saved', profileId);
      const userSavedPosts = await this.fetchAllFeedPosts(userSavedFeed);

      // Extract the activity IDs directly
      return userSavedPosts.map(activity => activity.id);
    } catch (error) {
      this.logger.error('Error getting saved post IDs:', {
        error: {
          message: error.message,
          stack: error.stack,
          response: error.response?.body,
        },
        profileId,
      });
      return [];
    }
  }
  async getActivityById(feed, activityId: string): Promise<any> {
    try {
      const activities = await this.fetchAllFeedPosts(feed);
      return activities.find(activity => activity.id === activityId);
    } catch (error) {
      this.logger.error('Error getting activity by ID:', {
        error: {
          message: error.message,
          stack: error.stack,
          response: error.response?.body,
        },
        activityId,
      });
      return null;
    }
  }

  async fetchAllFeedPosts(
    feed,
    limit = 100,
    offset = 0,
    allPosts = [],
  ): Promise<any> {
    try {
      // Fetch posts from the GetStream feed
      const response = await feed.get({
        limit: limit, // Number of posts per request
        offset: offset, // Starting point for the next batch
      });

      const posts = response.results; // Array of posts from the current page
      allPosts = allPosts.concat(posts); // Append new posts to the accumulated list

      // Check if there are more posts to fetch
      // If the number of posts returned is equal to the limit, there might be more
      if (posts.length === limit) {
        return await this.fetchAllFeedPosts(
          feed,
          limit,
          offset + limit,
          allPosts,
        ); // Recursive call with updated offset
      } else {
        return allPosts; // No more posts to fetch, return the complete list
      }
    } catch (error) {
      console.error('Error fetching posts from GetStream:', error);
      throw error; // Rethrow the error to handle it upstream if needed
    }
  }
}
