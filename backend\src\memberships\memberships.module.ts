import { Module, forwardRef } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Membership } from './models/membership.model';
import { MembershipsService } from './memberships.service';
import { ProfilesModule } from '../profiles/profiles.module';
import { MembershipsResolver } from './memberships.resolver';
import { OrganisationsModule } from '../organisations/organisations.module';
import { MembershipsServiceHelper } from './helpers/memberships.service.helper';
import { NotificationsModule } from '../notifications/notifications.module';
import { MembershipsServiceRights } from './helpers/memberships.service.rights';
import { CommonModule } from '../common/common.module';
import { MembershipsResolverHelper } from './helpers/memberships.resolver.helper';
import { FollowersModule } from '../followers/followers.module';
import { PartnershipsModule } from '../partnerships/partnerships.module';
import { MembershipsRepository } from './memberships.repository';
import { StreamFollowersModule } from '../feeds-followers/stream-followers.module';
import { PartnershipRequestsModule } from '../partnership-requests/partnership-requests.module';
import { PartnerOrganisationsModule } from '../partner-organisations/partner-organisations.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Membership]),
    forwardRef(() => CommonModule),
    forwardRef(() => ProfilesModule),
    forwardRef(() => OrganisationsModule),
    forwardRef(() => NotificationsModule),
    forwardRef(() => PartnershipsModule),
    forwardRef(() => FollowersModule),
    forwardRef(() => StreamFollowersModule),
    forwardRef(() => PartnershipRequestsModule),
    forwardRef(() => PartnerOrganisationsModule),
  ],
  providers: [
    MembershipsService,
    MembershipsRepository,
    MembershipsResolverHelper,
    MembershipsServiceHelper,
    MembershipsServiceRights,
    MembershipsResolver,
  ],
  exports: [
    MembershipsService,
    MembershipsServiceHelper,
    MembershipsServiceRights,
  ],
})
export class MembershipsModule {}
