import {
  <PERSON>solver,
  ResolveField,
  Parent,
  Mutation,
  Args,
} from '@nestjs/graphql';
import { UseGuards, Inject, HttpStatus, forwardRef } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';

import {
  Membership,
  MembershipActionType,
  MembershipPermission,
  MembershipStatus,
} from './models/membership.model';
import { Profile } from '../profiles/models/profile.model';
import { ProfilesService } from '../profiles/profiles.service';
import { Organisation } from '../organisations/models/organisation.model';
import { OrganisationsService } from '../organisations/organisations.service';
import {
  CurrentUser,
  ICurrentUser,
} from '../common/decorators/current-user.decorator';
import { GqlAuthGuard } from '../authz/graphql-auth.guard';
import { MembershipsService } from './memberships.service';
import {
  MembershipsServiceRights,
  MembershipRight,
} from './helpers/memberships.service.rights';
import { MembershipsResolverHelper } from './helpers/memberships.resolver.helper';
import { ErrorHelper } from '../common/helpers/error';
import { PartnerOrganisationsService } from '../partner-organisations/partner-organisations.service';
import { PartnerOrganisation } from '../partner-organisations/models/partner-organisation.model';
import { PartnerOrganisationStatus } from '../partner-organisations/args/partner-organisations.args';

@Resolver(() => Membership)
export class MembershipsResolver {
  @Inject(forwardRef(() => MembershipsService))
  private readonly membershipsService: MembershipsService;
  @Inject(forwardRef(() => ProfilesService))
  private readonly profilesService: ProfilesService;
  @Inject(forwardRef(() => OrganisationsService))
  private readonly organisationsService: OrganisationsService;
  @Inject(forwardRef(() => MembershipsServiceRights))
  private readonly membershipsServiceRights: MembershipsServiceRights;
  @Inject(forwardRef(() => MembershipsResolverHelper))
  private readonly membershipsResolverHelper: MembershipsResolverHelper;
  @Inject(forwardRef(() => PartnerOrganisationsService))
  private readonly partnerOrganisationsService: PartnerOrganisationsService;
  @Inject(WINSTON_MODULE_PROVIDER)
  private readonly logger: Logger;
  @Inject()
  private readonly errorHelper: ErrorHelper;

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  async updateMembershipStatus(
    @CurrentUser() user: ICurrentUser,
    @Args('organisationId') organisationId: string,
    @Args('profileId') profileId: string,
    @Args('actionType', { type: () => MembershipActionType })
    actionType: MembershipActionType,
  ): Promise<boolean> {
    this.logger.verbose(
      'MembershipsResolver.updateMembershipStatus (mutation)',
      {
        user: user.toLogObject(),
        organisationId,
        profileId,
        actionType,
      },
    );

    await this.membershipsServiceRights.checkRights(organisationId, {
      currentProfileId: user.profileId,
      rights: [MembershipRight.UpdateStatus],
    });

    return this.membershipsService.updateMembershipStatus(
      organisationId,
      profileId,
      actionType,
      {
        currentProfileId: user.profileId,
      },
    );
  }

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  async removeMembership(
    @CurrentUser() user: ICurrentUser,
    @Args('organisationId') organisationId: string,
    @Args('profileId') profileId: string,
  ): Promise<boolean> {
    this.logger.verbose('MembershipsResolver.removeMembership (mutation)', {
      user: user.toLogObject(),
      organisationId,
      profileId,
    });

    const membership = await this.membershipsService.findOne({
      profileId,
      organisationId,
    });

    if (!membership) {
      this.errorHelper.throwHttpException(
        `OrganisationsService.removeMembership`,
        `Membership not found`,
        HttpStatus.NOT_FOUND,
      );
    }

    await this.membershipsServiceRights.checkRights(organisationId, {
      currentProfileId: user.profileId,
      rights: this.membershipsResolverHelper.getRequiredPermissionRemoveRights(
        membership.permissions,
      ),
    });

    return this.membershipsService.removeMembership(membership);
  }

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  async addMembershipPermissions(
    @CurrentUser() user: ICurrentUser,
    @Args('organisationId') organisationId: string,
    @Args('profileId') profileId: string,
    @Args('status', {
      type: () => MembershipStatus,
      nullable: true,
      defaultValue: MembershipStatus.Active,
    })
    status: MembershipStatus,
    @Args('permissions', { type: () => [MembershipPermission] })
    permissions: MembershipPermission[],
  ): Promise<boolean> {
    this.logger.verbose(
      'MembershipsResolver.addMembershipPermissions (mutation)',
      {
        user: user.toLogObject(),
        organisationId,
        profileId,
        permissions,
      },
    );

    await this.membershipsServiceRights.checkRights(organisationId, {
      currentProfileId: user.profileId,
      rights:
        this.membershipsResolverHelper.getRequiredPermissionUpdateRights(
          permissions,
        ),
    });

    return this.membershipsService.addMembershipPermissions(
      organisationId,
      profileId,
      permissions,
      status,
      {
        currentProfileId: user.profileId,
      },
    );
  }

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  async updateMembershipPermissions(
    @CurrentUser() user: ICurrentUser,
    @Args('organisationId') organisationId: string,
    @Args('profileId') profileId: string,
    @Args('status', {
      type: () => MembershipStatus,
      nullable: true,
      defaultValue: MembershipStatus.Active,
    })
    status: MembershipStatus,
    @Args('permissions', { type: () => [MembershipPermission] })
    permissions: MembershipPermission[],
  ): Promise<boolean> {
    this.logger.verbose(
      'MembershipsResolver.updateMembershipPermissions (mutation)',
      {
        user: user.toLogObject(),
        organisationId,
        profileId,
        permissions,
      },
    );

    await this.membershipsServiceRights.checkRights(organisationId, {
      currentProfileId: user.profileId,
      rights:
        this.membershipsResolverHelper.getRequiredPermissionUpdateRights(
          permissions,
        ),
    });

    return this.membershipsService.updateMembershipPermissions(
      organisationId,
      profileId,
      permissions,
      status,
      {
        currentProfileId: user.profileId,
      },
    );
  }

  @ResolveField('profile', () => Profile)
  async profile(@Parent() membership: Membership): Promise<Profile> {
    if (!membership.profileId) return null;
    if (membership.profile) return membership.profile;

    this.logger.verbose('MembershipsResolver.profile (field resolver)', {
      membershipId: membership.id,
    });

    return this.profilesService.findById(membership.profileId);
  }

  @ResolveField('organisation', () => Organisation, { nullable: true })
  async organisation(
    @Parent() membership: Membership,
  ): Promise<Organisation | any> {
    if (!membership.organisationId) return null;
    if (membership.organisation) return membership.organisation;

    this.logger.verbose('MembershipsResolver.organisation (field resolver)', {
      membershipId: membership.id,
    });

    return this.organisationsService.findById(membership.organisationId, {
      useCache: true,
    });
  }

  @ResolveField('partnerOrganisation', () => PartnerOrganisation, {
    nullable: true,
  })
  async partnerOrganisation(
    @Parent() membership: Membership,
  ): Promise<PartnerOrganisation | null> {
    if (!membership.organisationId) return null;

    this.logger.verbose(
      'MembershipsResolver.partnerOrganisation (field resolver)',
      {
        membershipId: membership.id,
      },
    );

    const partnerOrg = await this.partnerOrganisationsService.findByChildOrgId(
      membership.organisationId,
    );

    if (
      partnerOrg &&
      partnerOrg.status === PartnerOrganisationStatus.Approved
    ) {
      return partnerOrg;
    }

    return null;
  }
}
