import {
  ArgsType,
  Field,
  registerEnumType,
  InputType,
  ObjectType,
} from '@nestjs/graphql';
import { PaginationArgs } from '../../common/args/pagination.args';
import { Profile } from '../../profiles/models/profile.model';
import { Region } from '../../profiles/args/profiles.args';
import { Organisation } from '../../organisations/models/organisation.model';

export enum NotificationType {
  InvitationReceived = 'InvitationReceived',
  InvitationAccepted = 'InvitationAccepted',
  OrgPartnerRequestReceived = 'OrgPartnerRequestReceived',
  OrgPartnerAcceptedSender = 'OrgPartnerAcceptedSender',
  OrgPartnerAcceptedReceiver = 'OrgPartnerAcceptedReceiver',
  OrgPartnerRequestRejected = 'OrgPartnerRequestRejected',
  PartnershipRequestReceived = 'PartnershipRequestReceived',
  PartnershipRequestApproved = 'PartnershipRequestApproved',
  PartnershipRequestApprovedByOrganisation = 'PartnershipRequestApprovedByOrganisation',
  MembershipAccepted = 'MembershipAccepted',
  OrganisationOwnershipRequested = 'OrganisationOwnershipRequested',
  OrganisationOwnershipAccepted = 'OrganisationOwnershipAccepted',
  MembershipRequested = 'MembershipRequested',
  FollowerAccepted = 'FollowerAccepted',
  MembershipPermissionsUpdated = 'MembershipPermissionsUpdated',
  EventInvitationByGuest = 'EventInvitationByGuest',
  EventInvitationByHosts = 'EventInvitationByHosts',
  EventInvitationApproved = 'EventInvitationApproved',
  NewEventUpdate = 'NewEventUpdate',
  EventLocationChanged = 'EventLocationChanged',
  EventDateTimeChanged = 'EventDateTimeChanged',
  IncentiveDateChanged = 'IncentiveDateChanged',
  IncentiveInvitationByParticipant = 'IncentiveInvitationByParticipant',
  IncentiveInvitationByHosts = 'IncentiveInvitationByHosts',
  IncentiveRegistrationRequested = 'IncentiveRegistrationRequested',
  IncentiveRegistrationApproved = 'IncentiveRegistrationApproved',
  NewIncentiveUpdate = 'NewIncentiveUpdate',
  WebinarDateChanged = 'WebinarDateChanged',
  WebinarInvitationByParticipant = 'WebinarInvitationByParticipant',
  WebinarInvitationByHosts = 'WebinarInvitationByHosts',
  WebinarParticipantAddedAsHostAdmin = 'WebinarParticipantAddedAsHostAdmin',
  WebinarParticipantAddedAsHost = 'WebinarParticipantAddedAsHost',
  WebinarParticipantAddedAsSpeaker = 'WebinarParticipantAddedAsSpeaker',
  WebinarRegistrationRequested = 'WebinarRegistrationRequested',
  WebinarRegistrationApproved = 'WebinarRegistrationApproved',
  NewWebinarUpdate = 'NewWebinarUpdate',
  NewFollower = 'NewFollower',
  SuggestFollow = 'SuggestFollow',
  PostComment = 'PostComment',
  CommentMention = 'CommentMention',
  PostMention = 'PostMention',
  CommentReact = 'CommentReact',
  PostReact = 'PostReact',
  PostShared = 'PostShared',
  OrgPostMention = 'OrgPostMention',
  OrgCommentMention = 'OrgCommentMention',
  RecurringPaymentReminder = 'RecurringPaymentReminder',
  MembershipAutoApprove = 'MembershipAutoApprove',
  ParentMembershipRequested = 'ParentMembershipRequested',
  ParentMembershipAccepted = 'ParentMembershipAccepted',
  ParentMembershipDeclined = 'ParentMembershipDeclined',
  OrgPostReminder7Days = 'OrgPostReminder7Days',
  OrgPostReminder14Days = 'OrgPostReminder14Days',
  InactiveUserReminder = 'InactiveUserReminder',
  InboxMessage = 'InboxMessage',
  DailyLoginStreak = 'DailyLoginStreak',
  NewTier = 'NewTier',
  WeeklySummary = 'WeeklySummary',
  NewAchievement = 'NewAchievement',
  HighFiveAchievement = 'HighFiveAchievement',
  NewOrganisationAchievement = 'NewOrganisationAchievement',
  MonthlyAchievementSummary = 'MonthlyAchievementSummary',
}

registerEnumType(NotificationType, { name: 'NotificationType' });

export enum NotificationDeviceType {
  Web = 'Web',
  Mobile = 'Mobile',
}

registerEnumType(NotificationDeviceType, { name: 'NotificationDeviceType' });

@InputType()
export class NotificationsFilter {
  @Field({ nullable: true })
  isRead?: boolean;
}

@ArgsType()
export class NotificationsArgs extends PaginationArgs {
  @Field(() => NotificationsFilter, { nullable: true })
  filter?: NotificationsFilter;
}

@ObjectType()
export class NotificationData {
  users?: Profile[];
  regions?: Region[];
  activityId?: string;
  postId?: string;
  organisation?: Organisation;
}

@ObjectType()
export class PushMessageTypeData {
  message?: string;
  type?: string;
}

export enum PushMessagePreferenceType {
  connections = 'connections',
  invitations = 'invitations',
  events = 'events',
  incentives = 'incentives',
  webinars = 'webinars',
  mentionsInPosts = 'mentionsInPosts',
  interaction = 'interaction',
  organisationsYouManage = 'organisationsYouManage',
  followSuggestions = 'followSuggestions',
  posts = 'posts',
  messages = 'messages',
}

registerEnumType(PushMessagePreferenceType, {
  name: 'PushMessagePreferenceType',
});

export const NotificationMessage = {
  InvitationReceived: {
    message:
      '$1 wants to connect with you. View your pending connection invitations.',
    type: PushMessagePreferenceType.connections,
  },
  InvitationAccepted: {
    message: 'You are now connected with $1',
    type: PushMessagePreferenceType.connections,
  },
  OrgPartnerRequestReceived: {
    message:
      '$1 has requested to become a Connected Organisation with $2. View this request.',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  OrgPartnerAcceptedSender: {
    message: '$1 has approved your request to become a Connected Organisation.',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  OrgPartnerAcceptedReceiver: {
    message: '$1 is now a Connected Organisation with $2.',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  OrgPartnerRequestRejected: {
    message: 'Your request to connect with $1 has been rejected.',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  PartnershipRequestReceived: {
    message:
      '$1 has requested to become a Connected Organisation with $2. View this request.',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  PartnershipRequestApproved: {
    message: '$1 approved a connection request from $2',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  PartnershipRequestApprovedByOrganisation: {
    message: '$1 is now a Connected Organisation with $2',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  MembershipAccepted: {
    message: 'You have been verified as an employee of $1',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  OrganisationOwnershipRequested: {
    message: '$1 has requested you take over as Owner of $2.',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  OrganisationOwnershipAccepted: {
    message:
      '$1 has agreed to become Owner of $2. You have reverted to an Admin role.',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  MembershipRequested: {
    message: 'MembershipRequested',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  FollowerAccepted: {
    message:
      '$1 has accepted your follow request. You can now explore their full page.',
    type: PushMessagePreferenceType.connections,
  },
  MembershipPermissionsUpdated: {
    message: 'You have been assigned as $1 of $2',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  EventInvitationByGuest: {
    message: '$1 has invited you to an event: $2',
    type: PushMessagePreferenceType.invitations,
  },
  EventInvitationByHosts: {
    message: '$1 has invited you to an event: $2',
    type: PushMessagePreferenceType.invitations,
  },
  EventInvitationApproved: {
    message: '$1 has accepted your request to attend $2',
    type: PushMessagePreferenceType.invitations,
  },
  NewEventUpdate: {
    message: '$1 has posted an update in $2',
    type: PushMessagePreferenceType.events,
  },
  EventLocationChanged: {
    message: '$1 has updated the location of $2',
    type: PushMessagePreferenceType.events,
  },
  EventDateTimeChanged: {
    message: '$1 has made a change to the date / time of $2',
    type: PushMessagePreferenceType.events,
  },
  IncentiveDateChanged: {
    message: '$1 has made a change to the dates of $2',
    type: PushMessagePreferenceType.incentives,
  },
  IncentiveInvitationByParticipant: {
    message: '$1 has invited you to an incentive: $2',
    type: PushMessagePreferenceType.invitations,
  },
  IncentiveInvitationByHosts: {
    message: '$1 has invited you to an incentive: $2',
    type: PushMessagePreferenceType.invitations,
  },
  IncentiveRegistrationRequested: {
    message:
      '$1 has requested to register in $2. View this request to confirm their participation',
    type: PushMessagePreferenceType.incentives,
  },
  IncentiveRegistrationApproved: {
    message: '$1 has accepted your request to register $2',
    type: PushMessagePreferenceType.incentives,
  },
  NewIncentiveUpdate: {
    message: '$1 has posted an update in $2',
    type: PushMessagePreferenceType.incentives,
  },
  WebinarDateChanged: {
    message: '$1 has made a change to the date / time of $2',
    type: PushMessagePreferenceType.webinars,
  },
  WebinarInvitationByParticipant: {
    message: '$1 has invited you to a webinar: $2',
    type: PushMessagePreferenceType.invitations,
  },
  WebinarInvitationByHosts: {
    message: '$1 has invited you to a webinar: $2',
    type: PushMessagePreferenceType.invitations,
  },
  WebinarParticipantAddedAsHostAdmin: {
    message: '$1 has assigned you as a Host Admin of $2',
    type: PushMessagePreferenceType.invitations,
  },
  WebinarParticipantAddedAsHost: {
    message: '$1 has assigned you as a Host of $2',
    type: PushMessagePreferenceType.invitations,
  },
  WebinarParticipantAddedAsSpeaker: {
    message: '$1 has assigned you as a Speaker at $2',
    type: PushMessagePreferenceType.invitations,
  },
  WebinarRegistrationRequested: {
    message:
      '$1 has requested to register in $2. View this request to confirm their participation',
    type: PushMessagePreferenceType.webinars,
  },
  WebinarRegistrationApproved: {
    message: '$1 has accepted your request to register $2',
    type: PushMessagePreferenceType.webinars,
  },
  NewWebinarUpdate: {
    message: '$1 has posted an update in $2',
    type: PushMessagePreferenceType.webinars,
  },
  NewFollower: {
    message: '$1$2 $3 requested to follow $4',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  SuggestFollow: {
    message:
      'You’ve had a suggestion to follow some new organisations that travel industry professionals similar to you also follow.',
    type: PushMessagePreferenceType.followSuggestions,
  },
  PostComment: {
    message: '$1$2 $3commented on your post. Like or reply to their comment.',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  CommentMention: {
    message: '$1 mentioned you in a comment. Like or reply to their comment.',
    type: PushMessagePreferenceType.interaction,
  },
  PostMention: {
    message: '$1 mentioned you in a post. Click here to view the post.',
    type: PushMessagePreferenceType.mentionsInPosts,
  },
  CommentReact: {
    message: '$1$2 $3reacted to your comment on $4’s post.',
    type: PushMessagePreferenceType.interaction,
  },
  PostReact: {
    message: '$1$2 $3reacted to your post.',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  PostShared: {
    message: '$1 has reshared your post with their network.',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  OrgPostMention: {
    message: '$1 mentioned $2 in a post.',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  OrgCommentMention: {
    message: '$1 mentioned $2 in a post.',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  RecurringPaymentReminder: {
    message:
      'Your paid subscription with $1 will renew on $2. Please ensure that your active payment method is still valid.',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  MembershipAutoApprove: {
    message: '$1 has joined $2 via pre-approved signup link.',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  ParentMembershipRequested: {
    message:
      '$1 has requested to become a member of $2. View this request to confirm their membership.',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  ParentMembershipAccepted: {
    message: 'You have been verified as an member of $1.',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  ParentMembershipDeclined: {
    message: 'You have been declined as an member of $1',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  OrgPostReminder7Days: {
    message:
      'It’s been a week since $1’s last post. Create a post to keep your community engaged.',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  OrgPostReminder14Days: {
    message:
      'It’s been two weeks since $1’s last post and your community is waiting to hear from you. Create your next post now.',
    type: PushMessagePreferenceType.organisationsYouManage,
  },
  InactiveUserReminder: {
    message:
      '$1 $2 shared new posts recently. View the latest updates in your feed.',
    type: PushMessagePreferenceType.posts,
  },
  InboxMessage: {
    message: '$1: $2',
    type: PushMessagePreferenceType.messages,
  },
  NewAchievement: {
    message: 'Congratulations! You have unlocked the $1 achievement',
    type: PushMessagePreferenceType.messages,
  },
  HighFiveAchievement: {
    message: '$1 at $2 gave you a High Five!',
    type: PushMessagePreferenceType.messages,
  },
  NewOrganisationAchievement: {
    message: 'Congratulations! You’ve unlocked the $1 $2 achievement!',
    type: PushMessagePreferenceType.messages,
  },
  MonthlyAchievementSummary: {
    message: 'Great job! You’ve unlocked new Organisation Achievements',
    type: PushMessagePreferenceType.messages,
  },
};

registerEnumType(NotificationMessage, { name: 'NotificationMessage' });
