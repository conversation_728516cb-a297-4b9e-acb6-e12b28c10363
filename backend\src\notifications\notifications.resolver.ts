import moment from 'moment';
import { UseGuards, Inject, forwardRef, OnModuleInit } from '@nestjs/common';
import {
  Args,
  Query,
  Resolver,
  ResolveField,
  Parent,
  Subscription,
  Mutation,
  Int,
} from '@nestjs/graphql';
import { Interval } from '@nestjs/schedule';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';

import { ChangeFields, Notification } from './models/notification.model';
import { NotificationsService } from './notifications.service';
import { GqlAuthGuard } from '../authz/graphql-auth.guard';
import {
  CurrentUser,
  ICurrentUser,
} from '../common/decorators/current-user.decorator';
import { NotificationsArgs, NotificationData } from './args/notifications.args';
import { NotificationsResult } from '../common/args/paginated-result';
import { Profile } from '../profiles/models/profile.model';
import { ProfilesService } from '../profiles/profiles.service';
import { PubSubEngine } from 'graphql-subscriptions';
import { MembershipsService } from '../memberships/memberships.service';
import { Membership } from '../memberships/models/membership.model';
import { EventInvitation } from '../event-invitations/models/event-invitation.model';
import { EventInvitationsService } from '../event-invitations/event-invitations.service';
import { Organisation } from '../organisations/models/organisation.model';
import { OrganisationsService } from '../organisations/organisations.service';
import { PartnershipRequest } from '../partnership-requests/models/partnership-request.model';
import { PartnershipRequestsService } from '../partnership-requests/partnership-requests.service';
import { Follower } from '../followers/models/follower.model';
import { FollowersService } from '../followers/followers.service';
import { withCancel } from '../common/helpers/pubsub';
import { Op } from 'sequelize';
import { IncentiveParticipant } from '../incentive-participants/models/incentive-participant.model';
import { IncentiveParticipantsService } from '../incentive-participants/incentive-participants.service';
import { WebinarParticipant } from '../webinar-participants/models/webinar-participant.model';
import { WebinarParticipantsService } from '../webinar-participants/webinar-participants.service';

@Resolver(() => Notification)
export class NotificationsResolver implements OnModuleInit {
  @Inject(forwardRef(() => NotificationsService))
  private readonly notificationsService: NotificationsService;
  @Inject(forwardRef(() => ProfilesService))
  private readonly profilesService: ProfilesService;
  @Inject(forwardRef(() => MembershipsService))
  private readonly membershipsService: MembershipsService;
  @Inject(forwardRef(() => EventInvitationsService))
  private readonly eventInvitationsService: EventInvitationsService;
  @Inject(forwardRef(() => IncentiveParticipantsService))
  private readonly incentiveParticipantsService: IncentiveParticipantsService;
  @Inject(forwardRef(() => WebinarParticipantsService))
  private readonly webinarParticipantsService: WebinarParticipantsService;
  @Inject(forwardRef(() => OrganisationsService))
  private readonly organisationsService: OrganisationsService;
  @Inject(forwardRef(() => PartnershipRequestsService))
  private readonly partnershipRequestsService: PartnershipRequestsService;
  @Inject(forwardRef(() => FollowersService))
  private readonly followersService: FollowersService;
  @Inject('PUB_SUB')
  private pubSub: PubSubEngine;
  @Inject(WINSTON_MODULE_PROVIDER)
  private readonly logger: Logger;

  private _onlineProfileIds: any;

  onModuleInit(): void {
    this._onlineProfileIds = {};
  }

  @Query(() => Notification)
  @UseGuards(GqlAuthGuard)
  notification(
    @Args('id', { nullable: false }) id: string,
  ): Promise<Notification> {
    this.logger.verbose('NotificationsResolver.notification (query)', {
      id,
    });

    return this.notificationsService.findById(id);
  }

  @Query(() => NotificationsResult)
  @UseGuards(GqlAuthGuard)
  notifications(
    @CurrentUser() user: ICurrentUser,
    @Args() notificationArgs: NotificationsArgs,
  ): Promise<NotificationsResult> {
    this.logger.verbose('NotificationsResolver.notifications (query)', {
      user: user.toLogObject(),
      notificationArgs,
    });

    return this.notificationsService.findNotifications(
      user.profileId,
      {
        isRead: notificationArgs?.filter?.isRead,
      },
      {
        first: notificationArgs?.first,
        after: notificationArgs?.after,
        sortBy: notificationArgs?.sortBy,
        sortOrder: notificationArgs?.sortOrder,
      },
    );
  }

  @Query(() => Int)
  @UseGuards(GqlAuthGuard)
  unSeenNotifications(@CurrentUser() user: ICurrentUser): Promise<number> {
    return this.notificationsService.count({
      ownerProfileId: user.profileId,
      isSeen: false,
    });
  }

  @Mutation(() => [Notification])
  @UseGuards(GqlAuthGuard)
  setNotificationStatus(
    @CurrentUser() user: ICurrentUser,
    @Args('fields') fields: ChangeFields,
    @Args('notificationIds', { type: () => [String], nullable: true })
    notificationIds?: string[] | null,
  ): Promise<Notification[]> {
    this.logger.verbose(
      'NotificationsResolver.setNotificationStatus (mutation)',
      {
        user: user.toLogObject(),
        fields,
        notificationIds,
      },
    );
    return this.notificationsService.setStatus(fields, {
      currentProfileId: user.profileId,
      notificationIds,
    });
  }

  @ResolveField('profile', () => Profile)
  async profile(@Parent() notification: Notification): Promise<Profile> {
    if (notification.profile) return notification.profile;

    this.logger.verbose('NotificationsResolver.profile (field resolver)', {
      notificationId: notification.id,
    });

    return this.profilesService.findById(notification.profileId);
  }

  @ResolveField('membership', () => Membership, { nullable: true })
  async membership(@Parent() notification: Notification): Promise<Membership> {
    if (notification.membership) return notification.membership;
    if (!notification.membershipId) return null;

    this.logger.verbose('NotificationsResolver.membership (field resolver)', {
      notificationId: notification.id,
    });

    return this.membershipsService.findById(notification.membershipId, {
      useCache: true,
    });
  }

  @ResolveField('follower', () => Follower, { nullable: true })
  async follower(@Parent() notification: Notification): Promise<Follower> {
    if (notification.follower) return notification.follower;
    if (!notification.followerId) return null;

    this.logger.verbose('NotificationsResolver.follower (field resolver)', {
      notificationId: notification.id,
    });

    return this.followersService.findById(notification.followerId, {
      useCache: true,
    });
  }

  @ResolveField('eventInvitation', () => EventInvitation, { nullable: true })
  async eventInvitation(
    @Parent() notification: Notification,
  ): Promise<EventInvitation> {
    if (notification.eventInvitation) return notification.eventInvitation;
    if (!notification.eventInvitationId) return null;

    this.logger.verbose(
      'NotificationsResolver.eventInvitation (field resolver)',
      {
        notificationId: notification.id,
      },
    );

    return this.eventInvitationsService.findById(
      notification.eventInvitationId,
      { useCache: true },
    );
  }

  @ResolveField('organisation', () => Organisation, { nullable: true })
  async organisation(
    @Parent() notification: Notification,
  ): Promise<Organisation> {
    if (notification.organisation) return notification.organisation;
    if (!notification.organisationId) return null;

    this.logger.verbose('NotificationsResolver.organisation (field resolver)', {
      notificationId: notification.id,
    });

    return this.organisationsService.findById(notification.organisationId, {
      useCache: true,
    });
  }

  @ResolveField('partnershipRequest', () => PartnershipRequest, {
    nullable: true,
  })
  async partnershipRequest(
    @Parent() notification: Notification,
  ): Promise<PartnershipRequest> {
    if (notification.partnershipRequest) return notification.partnershipRequest;
    if (!notification.partnershipRequestId) return null;

    this.logger.verbose(
      'NotificationsResolver.partnershipRequest (field resolver)',
      {
        notificationId: notification.id,
      },
    );

    return this.partnershipRequestsService.findById(
      notification.partnershipRequestId,
      {
        useCache: true,
      },
    );
  }

  @ResolveField('incentiveParticipant', () => IncentiveParticipant, {
    nullable: true,
  })
  async incentiveParticipant(
    @Parent() notification: Notification,
  ): Promise<IncentiveParticipant> {
    if (notification.incentiveParticipant)
      return notification.incentiveParticipant;
    if (!notification.incentiveParticipantId) return null;

    this.logger.verbose(
      'NotificationsResolver.incentiveParticipant (field resolver)',
      {
        notificationId: notification.id,
      },
    );

    return this.incentiveParticipantsService.findById(
      notification.incentiveParticipantId,
      { useCache: true },
    );
  }

  @ResolveField('webinarParticipant', () => WebinarParticipant, {
    nullable: true,
  })
  async webinarParticipant(
    @Parent() notification: Notification,
  ): Promise<WebinarParticipant> {
    if (notification.webinarParticipant) return notification.webinarParticipant;
    if (!notification.webinarParticipantId) return null;

    this.logger.verbose(
      'NotificationsResolver.webinarParticipant (field resolver)',
      {
        notificationId: notification.id,
      },
    );

    return this.webinarParticipantsService.findById(
      notification.webinarParticipantId,
      { useCache: true },
    );
  }

  @ResolveField('data', () => NotificationData, {
    nullable: true,
  })
  async data(@Parent() notification: Notification): Promise<NotificationData> {
    const notifData = notification.data;
    if (notifData && notifData.users) {
      const updUsers = [];
      for (let user of notifData.users) {
        const profile = await this.profilesService.findById(
          user.profileId || '',
        );
        let profileData = profile as { profileId?: string };
        if (!profileData) profileData = user;
        profileData.profileId = user.profileId;
        updUsers.push(profileData);
      }
      notifData.users = updUsers;
    }
    if (notifData && notifData.organisationId) {
      const orgData = await this.organisationsService.findById(
        notifData.organisationId,
      );
      notifData.organisation = orgData;
    }
    
    // Add sender and receiver organization images
    if (notifData && notifData.senderOrgId) {
      const senderOrg = await this.organisationsService.findById(notifData.senderOrgId);
      if (senderOrg) {
        notifData.senderOrgImage = senderOrg.image || null;
      }
    }
    
    if (notifData && notifData.receiverOrgId) {
      const receiverOrg = await this.organisationsService.findById(notifData.receiverOrgId);
      if (receiverOrg) {
        notifData.receiverOrgImage = receiverOrg.image || null;
      }
    }
    
    return notifData;
  }

  @UseGuards(GqlAuthGuard)
  @Subscription(() => Notification, {
    filter: (payload, variables, context) => {
      const currentUser: ICurrentUser = context.connection.context.user;
      const notification: Notification = payload.newNotification;

      if (currentUser.profileId === notification.ownerProfileId) {
        return true;
      }

      return false;
    },
  })
  async newNotification(@CurrentUser() user: ICurrentUser): Promise<any> {
    this.logger.verbose(
      `${user.name} (${user.email}, ${user.profileId}) connected to receive notifications`,
    );

    this._onlineProfileIds[user.profileId] = true;
    await this.profilesService.updateById(user.profileId, {
      lastOnlineAt: moment().toDate(),
    });

    return withCancel(
      this.pubSub.asyncIterator('newNotification'),
      async () => {
        this.logger.verbose(
          `${user.name} (${user.email}, ${user.profileId}) disconnected`,
        );

        delete this._onlineProfileIds[user.profileId];

        await this.profilesService.updateById(user.profileId, {
          lastOnlineAt: moment().add(-2, 'minutes').toDate(),
        });
      },
    );
  }

  @Interval(60000)
  async handleInterval(): Promise<void> {
    if (Object.keys(this._onlineProfileIds).length > 0) {
      await this.profilesService.update({
        where: {
          id: {
            [Op.in]: Object.keys(this._onlineProfileIds),
          },
        },
        update: {
          lastOnlineAt: moment().toDate(),
        },
      });
    }
  }
}
