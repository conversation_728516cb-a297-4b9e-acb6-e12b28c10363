import { Field, ID, ObjectType } from '@nestjs/graphql';
import { DataTypes } from 'sequelize';
import {
  BelongsTo,
  Column,
  ForeignKey,
  HasMany,
  Model,
  Table,
} from 'sequelize-typescript';
import short from 'short-uuid';
import { ActivityType } from '../../activities/args/activities.args';
import GraphQLJSON from 'graphql-type-json';
import { Organisation } from '../../organisations/models/organisation.model';
import { Activity } from '../../activities/models/activity.model';

@Table
@ObjectType()
export class OrganisationLoyaltyPoint extends Model<OrganisationLoyaltyPoint> {
  @Field(() => ID)
  @Column({
    primaryKey: true,
    unique: true,
    allowNull: false,
    defaultValue: short.generate,
  })
  id: string;

  @Field(() => ActivityType, {
    nullable: false,
  })
  @Column({
    type: DataTypes.STRING,
  })
  type: ActivityType;

  @Field(() => Number)
  @Column({
    type: DataTypes.INTEGER,
    allowNull: false,
  })
  points: number;

  @Field(() => Number)
  @Column({
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
  })
  count: number;

  @Field()
  @Column({
    type: DataTypes.INTEGER,
    allowNull: false,
  })
  rollingPoints: number;

  @Field()
  @Column({
    type: DataTypes.INTEGER,
    allowNull: false,
  })
  lifeTimePoints: number;

  @Field()
  @Column({
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
  })
  previousMonthTierIndex: number;

  @Field()
  @Column({
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
  })
  currentTierIndex: number;

  @Field()
  @Column({
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
  })
  nextTierIndex: number;

  @Field()
  @Column({
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
  })
  streak: number;

  @Field()
  @Column({
    type: DataTypes.DATE,
    allowNull: true,
  })
  streakUpdatedAt: Date;

  @Field(() => GraphQLJSON, {
    nullable: true,
  })
  @Column({
    type: DataTypes.JSONB,
  })
  placeholders: any;

  @ForeignKey(() => Organisation)
  @Column({
    type: DataTypes.STRING,
    allowNull: false,
  })
  organisationId: string;

  @HasMany(() => Activity, 'organisationId')
  activities: Activity[];

  @BelongsTo(() => Organisation, 'organisationId')
  organisation?: Organisation;

  @Field()
  @Column({
    type: DataTypes.DATE,
  })
  createdAt: Date;

  @Field()
  @Column({
    type: DataTypes.DATE,
  })
  updatedAt: Date;

  @Field()
  @Column({
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
  })
  activeTier: number;

  @Field(() => String, { nullable: true })
  @ForeignKey(() => Organisation)
  @Column({
    type: DataTypes.STRING,
    allowNull: true,
  })
  parentOrganisationId: string;

  @Field(() => Organisation, { nullable: true })
  @BelongsTo(() => Organisation, {
    foreignKey: 'parentOrganisationId',
    onDelete: 'SET NULL',
  })
  parentOrganisation: Organisation;
}
