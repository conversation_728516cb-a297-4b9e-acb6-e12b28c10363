import { Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Op } from 'sequelize';
import { Logger } from 'winston';
import { PaginatedResult } from '../common/args/paginated-result';
import { PaginationSortOrder } from '../common/args/pagination.args';
import { PaginationHelper } from '../common/helpers/pagination';
import { OrganisationLoyaltyPointsArgs } from './args/organisation-loyalty-points.args';
import { OrganisationLoyaltyPoint } from './models/organisation-loyalty-points.model';
import { ActivityType } from '../activities/args/activities.args';
import { Organisation } from '../organisations/models/organisation.model';

@Injectable()
export class OrganisationLoyaltyPointsRepository {
  @InjectModel(OrganisationLoyaltyPoint)
  private readonly organisationLoyaltyPointModel: typeof OrganisationLoyaltyPoint;
  @Inject(WINSTON_MODULE_PROVIDER)
  private readonly logger: Logger;

  async findOrgLoyaltyPoints({
    organisationId,
    organisationLoyaltyPointsArgs,
  }: {
    organisationId: string;
    organisationLoyaltyPointsArgs: OrganisationLoyaltyPointsArgs;
  }): Promise<PaginatedResult<OrganisationLoyaltyPoint>> {
    // We need to include both direct organization activities and parent organization activities
    // Use Sequelize's OR operator to fetch both types of activities
    const extraQueryParams: any = {
      [Op.or]: [
        { organisationId }, // Direct organization activities
        { parentOrganisationId: organisationId } // Activities where this org is the parent
      ],
      type: { [Op.ne]: ActivityType.ZeroLoyaltyPoints },
      // points: {
      //   [Op.ne]: 0, // Exclude entries with zero points
      // },
    };

    const { endDate, startDate, ...pagination } = organisationLoyaltyPointsArgs;

    pagination.sortOrder = PaginationSortOrder.Descending;

    if (startDate) {
      extraQueryParams.createdAt = {
        [Op.gte]: startDate,
      };
    }

    if (endDate) {
      extraQueryParams.createdAt = {
        ...extraQueryParams.createdAt,
        [Op.lte]: endDate,
      };
    }

    return await new PaginationHelper().getPaginatedResults({
      model: OrganisationLoyaltyPoint,
      pagination,
      extraQueryParams,
      excludeIds: [],
      includeParams: [
        {
          model: Organisation,
          as: 'parentOrganisation',
          attributes: ['id', 'name'],
          required: false,
        },
      ],
    });
  }

  async sumPoints({
    organisationId,
    startOfWeek,
    endOfWeek,
  }: {
    organisationId: string;
    startOfWeek: Date;
    endOfWeek: Date;
  }): Promise<number> {
    this.logger.verbose('OrganisationLoyaltyPointsRepository.sumPoints', {
      organisationId,
      startOfWeek,
      endOfWeek,
    });

    const result = await this.organisationLoyaltyPointModel.sum('points', {
      where: {
        organisationId,
        createdAt: {
          [Op.gte]: startOfWeek,
          [Op.lte]: endOfWeek,
        },
      },
    });

    return result || 0;
  }
}
