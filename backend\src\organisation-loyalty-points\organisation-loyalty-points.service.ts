import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import moment from 'moment-timezone';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Op, Transaction } from 'sequelize';
import { Sequelize } from 'sequelize-typescript';
import { Logger, profile } from 'winston';
import { ActivityType } from '../activities/args/activities.args';
import { PaginatedResult } from '../common/args/paginated-result';
import { BaseService } from '../common/base.service';
import { ErrorHelper } from '../common/helpers/error';
import config from '../config/config';
import { NotificationType } from '../notifications/args/notifications.args';
import { NotificationsService } from '../notifications/notifications.service';
import { ProfilesService } from '../profiles/profiles.service';
import { OrganisationLoyaltyPointsArgs } from './args/organisation-loyalty-points.args';
import { OrganisationLoyaltyPointsRepository } from './organisation-loyalty-points.repository';
import { OrganisationLoyaltyPoint } from './models/organisation-loyalty-points.model';
import { MembershipsService } from '../memberships/memberships.service';
import { OrganisationsService } from '../organisations/organisations.service';

interface Tier {
  key: string;
  name: string;
  minPoints: number;
  maxPoints: number;
  maxPointsPerMonth?: number;
  isHidden?: boolean;
}

interface PointsConfig {
  points: number;
  maxPoints?: number;
  maxPointsPerMonth?: number;
  groupConsecutiveEntries?: boolean;
}

@Injectable()
export class OrganisationLoyaltyPointsService extends BaseService(
  OrganisationLoyaltyPoint,
) {
  @Inject()
  private readonly sequelize: Sequelize;
  @Inject(forwardRef(() => NotificationsService))
  private readonly notificationsService: NotificationsService;
  @Inject(forwardRef(() => ProfilesService))
  private readonly profilesService: ProfilesService;
  @Inject(forwardRef(() => OrganisationLoyaltyPointsRepository))
  private readonly organisationLoyaltyPointsRepository: OrganisationLoyaltyPointsRepository;
  @Inject(WINSTON_MODULE_PROVIDER)
  private readonly logger: Logger;
  @Inject()
  private readonly errorHelper: ErrorHelper;
  @Inject(forwardRef(() => MembershipsService))
  private readonly membershipsService: MembershipsService;
  @Inject(forwardRef(() => OrganisationsService))
  private readonly organisationsService: OrganisationsService;

  constructor(
    @InjectModel(OrganisationLoyaltyPoint)
    private organisationLoyaltyPointModel: typeof OrganisationLoyaltyPoint,
  ) {
    super();
  }

  private static POINTS: Partial<Record<ActivityType, PointsConfig>> = {
    [ActivityType.CreatePost]: {
      points: 200,
      maxPoints: 200,
    },
    [ActivityType.OwnerPostCommentLike]: {
      points: 10,
      maxPoints: 50,
    },
    [ActivityType.OwnerPostCommentReply]: {
      points: 10,
      maxPoints: 50,
    },
    [ActivityType.UserInvitedToEvent]: {
      points: 300,
      maxPointsPerMonth: 300,
    },
    [ActivityType.UserInvitedToWebinar]: {
      points: 200,
      maxPointsPerMonth: 200,
    },
    [ActivityType.UserInvitedToIncentive]: {
      points: 300,
      maxPointsPerMonth: 300,
    },
    [ActivityType.ZeroLoyaltyPoints]: {
      points: 0,
    },
    [ActivityType.RemovePost]: {
      points: -200,
    },
  };

  private static TIERS: Tier[] = [
    {
      key: 'blue',
      name: 'Blue',
      minPoints: 0,
      maxPoints: 649,
    },
    {
      key: 'silver',
      name: 'Silver',
      minPoints: 650,
      maxPoints: 1499,
    },
    {
      key: 'gold',
      name: 'Gold',
      minPoints: 1500,
      maxPoints: 999999,
    },
  ];

  async getOrgLoyaltyPointsList({
    organisationId,
    organisationLoyaltyPointsArgs,
  }: {
    organisationId: string;
    organisationLoyaltyPointsArgs: OrganisationLoyaltyPointsArgs;
  }): Promise<PaginatedResult<OrganisationLoyaltyPoint>> {
    this.logger.verbose(
      'OrganisationLoyaltyPointsService.getOrgLoyaltyPointsList',
      {
        organisationId,
        organisationLoyaltyPointsArgs,
      },
    );

    const { records, totalCount } =
      await this.organisationLoyaltyPointsRepository.findOrgLoyaltyPoints({
        organisationId,
        organisationLoyaltyPointsArgs,
      });

    return {
      records,
      totalCount,
    };
  }

  async sumPointsByType({
    timezone,
    organisationId,
    type,
  }: {
    timezone: string;
    type: string;
    organisationId: string;
  }): Promise<number> {
    const startOfDay = moment.tz(timezone).startOf('day').toDate();
    const endOfDay = moment.tz(timezone).endOf('day').toDate();

    let totalPointsEntry;

    // Check if the type is one of the combined types (OwnerPostCommentLike or OwnerPostCommentReply)
    if (type === 'OwnerPostCommentLike' || type === 'OwnerPostCommentReply') {
      // Sum points for both ActivityType.OwnerPostCommentLike and ActivityType.OwnerPostCommentReply
      totalPointsEntry = await this.findAll({
        organisationId,
        createdAt: {
          [Op.between]: [startOfDay, endOfDay],
        },
        type: {
          [Op.in]: ['OwnerPostCommentLike', 'OwnerPostCommentReply'],
        },
      });
    } else if (type === 'CreatePost' || type === 'RemovePost') {
      // Sum points for CreatePost and RemovePost
      totalPointsEntry = await this.findAll({
        organisationId,
        createdAt: {
          [Op.between]: [startOfDay, endOfDay],
        },
        type: {
          [Op.in]: ['CreatePost', 'RemovePost'],
        },
      });
    } else {
      // Regular case, just sum points for the specific type
      totalPointsEntry = await this.findAll({
        type: type,
        organisationId: organisationId,
        createdAt: {
          [Op.between]: [startOfDay, endOfDay],
        },
      });
    }

    // Sum the points for the entries found
    const totalPoints = totalPointsEntry.reduce(
      (acc, entry) => acc + entry.points,
      0,
    );
    return totalPoints || 0; // Return 0 if no points found
  }

  async hasActivityRecordedThisMonth({
    organisationId,
    type,
    timezone,
  }: {
    organisationId: string;
    type: string;
    timezone: string;
  }): Promise<number> {
    const startOfMonth = moment.tz(timezone).startOf('month').toDate();
    const endOfMonth = moment.tz(timezone).endOf('month').toDate();

    const pointsPerMonth = await this.findAll({
      type: type,
      organisationId: organisationId,
      createdAt: {
        [Op.between]: [startOfMonth, endOfMonth],
      },
    });

    return pointsPerMonth.length || 0;
  }

  async addPoints({
    organisationId,
    type,
    placeholders,
    transaction,
    variableRewardPoints,
    parentOrgId,
  }: {
    organisationId?: string;
    type: ActivityType;
    placeholders?: any;
    transaction?: Transaction;
    variableRewardPoints?: number;
    parentOrgId?: string;
  }): Promise<
    | OrganisationLoyaltyPoint
    | {
      id: null;
    }
  > {
    this.logger.verbose('OrganisationLoyaltyPointsService.addPoints', {
      organisationId,
      type,
      placeholders,
      variableRewardPoints,
      parentOrgId,
    });

    try {
      // --- Move helpers to top of try block so both if/else can use them ---
      // Helper to find the most recent valid (non-zero) points entry
      const findMostRecentValidEntry = entries => {
        if (!entries || entries.length === 0) return null;
        for (const entry of entries) {
          if (entry.points > 0) return entry;
        }
        return null;
      };
      // Helper to get last valid (non-zero) rolling and lifetime points
      const getLastValidPoints = (
        parentOrganisationId,
        orgEntries,
        orgAsParentEntries,
        parentOrgEntries,
      ) => {
        let validEntry = findMostRecentValidEntry(orgEntries);
        const orgAsParentEntry = findMostRecentValidEntry(orgAsParentEntries);

        // If we have a more recent entry where this org was a parent, use that
        if (
          orgAsParentEntry &&
          (!validEntry ||
            new Date(orgAsParentEntry.createdAt) >
              new Date(validEntry.createdAt))
        ) {
          validEntry = orgAsParentEntry;
        }

        // If we have a parent org and either:
        // 1. No valid entry found yet, OR
        // 2. Parent has a more recent entry
        if (parentOrganisationId) {
          const parentValid = findMostRecentValidEntry(parentOrgEntries);
          if (
            parentValid &&
            (!validEntry ||
              new Date(parentValid.createdAt) > new Date(validEntry.createdAt))
          ) {
            validEntry = parentValid;
          }
        }

        return {
          rollingPoints: validEntry ? validEntry.rollingPoints : 0,
          lifeTimePoints: validEntry ? validEntry.lifeTimePoints : 0,
        };
      };

      // Async version of getLastValidPoints that can fetch child entries
      const getLastValidPointsAsync = async (
        parentOrganisationId,
        orgEntries,
        orgAsParentEntries,
        parentOrgEntries,
      ) => {
        let validEntry = findMostRecentValidEntry(orgEntries);
        const orgAsParentEntry = findMostRecentValidEntry(orgAsParentEntries);

        // If we have a more recent entry where this org was a parent, use that
        if (
          orgAsParentEntry &&
          (!validEntry ||
            new Date(orgAsParentEntry.createdAt) >
              new Date(validEntry.createdAt))
        ) {
          validEntry = orgAsParentEntry;
        }

        // If we have a parent org and either:
        // 1. No valid entry found yet, OR
        // 2. Parent has a more recent entry
        if (parentOrganisationId) {
          const parentValid = findMostRecentValidEntry(parentOrgEntries);
          if (
            parentValid &&
            (!validEntry ||
              new Date(parentValid.createdAt) > new Date(validEntry.createdAt))
          ) {
            validEntry = parentValid;
          }
        }

        // If we still don't have a valid entry, check for any child entries
        if (!validEntry && parentOrganisationId) {
          try {
            const childEntries =
              await this.organisationLoyaltyPointModel.findAll({
                where: { parentOrganisationId: organisationId },
                order: [['createdAt', 'DESC']],
                limit: 5,
                transaction,
              });

            const childValid = findMostRecentValidEntry(childEntries);
            if (
              childValid &&
              (!validEntry ||
                new Date(childValid.createdAt) > new Date(validEntry.createdAt))
            ) {
              validEntry = childValid;
            }
          } catch (error) {
            this.logger.error(
              'Error fetching child entries for loyalty points',
              {
                error: error.message,
                organisationId,
                parentOrganisationId,
              },
            );
          }
        }

        return {
          rollingPoints: validEntry ? validEntry.rollingPoints : 0,
          lifeTimePoints: validEntry ? validEntry.lifeTimePoints : 0,
        };
      };
      // --- End helpers ---

      const organisation = await this.organisationsService.findById(
        organisationId,
        {
          transaction,
        },
      );
      if (!organisation) {
        this.logger.error(`Organisation not found for id ${organisationId}`);
        return {
          id: null,
        };
      }

      // First check if parentOrgId is provided in the parameters
      let parentOrganisationId = parentOrgId || null;

      // If not provided in parameters, try to get it from the organisation
      if (!parentOrganisationId && organisation.parentOrganisations?.length > 0) {
        parentOrganisationId = organisation.parentOrganisations[0].id;
      }

      const pointsConfig = OrganisationLoyaltyPointsService.POINTS[type as ActivityType];
      if (!pointsConfig) {
        this.logger.error(`Invalid activity type ${type}`);
        this.errorHelper.throwHttpException(
          'LoyaltyPointsService.addPoints',
          'Invalid activity type',
        );
      }

      const points = variableRewardPoints ?? pointsConfig.points;

      if (type === ActivityType.CreatePost) {
        // Check if the organisation has already received points today as an organisation
        const orgPointsToday = await this.findAll({
          organisationId,
          type: ActivityType.CreatePost,
          points: { [Op.gt]: 0 },
          createdAt: {
            [Op.between]: [
              moment().startOf('day').toDate(),
              moment().endOf('day').toDate(),
            ],
          },
        });

        // If this is a parent organisation, check if it has already received points today as a parent
        let parentPointsToday = [];
        if (parentOrganisationId) {
          parentPointsToday = await this.findAll({
            organisationId: parentOrganisationId,
            type: ActivityType.CreatePost,
            points: { [Op.gt]: 0 },
            createdAt: {
              [Op.between]: [
                moment().startOf('day').toDate(),
                moment().endOf('day').toDate(),
              ],
            },
          });
        }

        // Also check if the organisation has already been a parent for another organisation today
        const orgAsParentPointsToday = await this.findAll({
          parentOrganisationId: organisationId,
          type: ActivityType.CreatePost,
          points: { [Op.gt]: 0 },
          createdAt: {
            [Op.between]: [
              moment().startOf('day').toDate(),
              moment().endOf('day').toDate(),
            ],
          },
        });

        // Get entries where this organization is the main org (most recent first)
        const orgEntries = await this.organisationLoyaltyPointModel.findAll({
          where: { organisationId },
          order: [['createdAt', 'DESC']],
          limit: 5,
          transaction,
        });

        // Get entries where this organization was a parent for other orgs
        const orgAsParentEntries =
          await this.organisationLoyaltyPointModel.findAll({
            where: { parentOrganisationId: organisationId },
            order: [['createdAt', 'DESC']],
            limit: 5,
            transaction,
          });

        // If we have a parent organization involved in this transaction
        // also get that parent org's entries
        let parentOrgEntries = [];
        if (parentOrganisationId) {
          parentOrgEntries = await this.organisationLoyaltyPointModel.findAll({
            where: { organisationId: parentOrganisationId },
            order: [['createdAt', 'DESC']],
            limit: 5,
            transaction,
          });
        }

        // Now do the zero-point entry checks (after all variables are declared)
        if (orgPointsToday.length > 0) {
          // Return zero points entry for tracking purposes, but keep rolling/lifetime points from last valid
          const { rollingPoints, lifeTimePoints } =
            await getLastValidPointsAsync(
              parentOrganisationId,
              orgEntries,
              orgAsParentEntries,
              parentOrgEntries,
            );
          return await this.create(
            {
              organisationId,
              parentOrganisationId: parentOrganisationId || null,
              type,
              points:
                OrganisationLoyaltyPointsService.POINTS[
                  ActivityType.ZeroLoyaltyPoints
                ].points,
              rollingPoints,
              lifeTimePoints,
              tier: '',
              placeholders: {
                ...placeholders,
                dailyLimitReached: true,
              },
            },
            { transaction },
          );
        }

        if (parentPointsToday.length > 0) {
          // Return zero points entry for tracking purposes, but keep rolling/lifetime points from last valid
          const { rollingPoints, lifeTimePoints } =
            await getLastValidPointsAsync(
              parentOrganisationId,
              orgEntries,
              orgAsParentEntries,
              parentOrgEntries,
            );
          return await this.create(
            {
              organisationId,
              parentOrganisationId,
              type,
              points:
                OrganisationLoyaltyPointsService.POINTS[
                  ActivityType.ZeroLoyaltyPoints
                ].points,
              rollingPoints,
              lifeTimePoints,
              tier: '',
              placeholders: {
                ...placeholders,
                parentDailyLimitReached: true,
              },
            },
            { transaction },
          );
        }

        if (orgAsParentPointsToday.length > 0) {
          // Return zero points entry for tracking purposes, but keep rolling/lifetime points from last valid
          const { rollingPoints, lifeTimePoints } =
            await getLastValidPointsAsync(
              parentOrganisationId,
              orgEntries,
              orgAsParentEntries,
              parentOrgEntries,
            );
          return await this.create(
            {
              organisationId,
              parentOrganisationId: parentOrganisationId || null,
              type,
              points:
                OrganisationLoyaltyPointsService.POINTS[
                  ActivityType.ZeroLoyaltyPoints
                ].points,
              rollingPoints,
              lifeTimePoints,
              tier: '',
              placeholders: {
                ...placeholders,
                dailyLimitReached: true,
              },
            },
            { transaction },
          );
        }
      } else {
        // For CreatePost, we need to check if we've already awarded points for this activity type today
        // For posts, we check the total points awarded for all posts (both partner and direct)
        const totalPoints = await this.sumPointsByType({
          organisationId,
          type,
          timezone: (organisation as any).timezone || 'UTC',
        });

        const hasActivityRecordedThisMonth =
          await this.hasActivityRecordedThisMonth({
            organisationId,
            type,
            timezone: (organisation as any).timezone || 'UTC',
          });

        // Find the most recent entries for the organization in all its roles (as org and as parent)
        // These will be used to ensure correct point accumulation regardless of role changes

        // Get entries where this organization is the main org (most recent first)
        const orgEntries = await this.organisationLoyaltyPointModel.findAll({
          where: { organisationId },
          order: [['createdAt', 'DESC']],
          limit: 10, // Increased limit to ensure we catch all relevant entries
          transaction,
        });

        // Get entries where this organization was a parent for other orgs
        const orgAsParentEntries =
          await this.organisationLoyaltyPointModel.findAll({
            where: { parentOrganisationId: organisationId },
            order: [['createdAt', 'DESC']],
            limit: 10, // Increased limit to ensure we catch all relevant entries
            transaction,
          });

        // If we have a parent organization involved in this transaction
        // also get that parent org's entries
        let parentOrgEntries = [];
        if (parentOrganisationId) {
          parentOrgEntries = await this.organisationLoyaltyPointModel.findAll({
            where: {
              [Op.or]: [
                { organisationId: parentOrganisationId },
                { parentOrganisationId: parentOrganisationId },
              ],
            },
            order: [['createdAt', 'DESC']],
            limit: 10, // Increased limit to ensure we catch all relevant entries
            transaction,
          });
        }

        // Starting point values - will be updated based on what we find
        let validPointEntry = null;
        let basePoints = 0;
        let baseRollingPoints = 0;
        let baseLifeTimePoints = 0;

        // Find the most recent valid (non-zero) points entry for this organization
        validPointEntry = findMostRecentValidEntry(orgEntries);

        // If no valid entry as a main org, check entries where it was a parent
        if (!validPointEntry) {
          validPointEntry = findMostRecentValidEntry(orgAsParentEntries);
        }

        // If we found a valid entry, use its points as our base
        if (validPointEntry) {
          basePoints = validPointEntry.points;
          baseRollingPoints = validPointEntry.rollingPoints;
          baseLifeTimePoints = validPointEntry.lifeTimePoints;
        }

        // If parent is involved, ensure we consider parent's points too
        if (parentOrganisationId) {
          const parentValidEntry = findMostRecentValidEntry(parentOrgEntries);
          if (
            parentValidEntry &&
            (!validPointEntry ||
              new Date(parentValidEntry.createdAt) >
                new Date(validPointEntry.createdAt))
          ) {
            // If parent's valid entry is more recent, use it instead
            validPointEntry = parentValidEntry;
            basePoints = parentValidEntry.points;
            baseRollingPoints = parentValidEntry.rollingPoints;
            baseLifeTimePoints = parentValidEntry.lifeTimePoints;
          }
        }

        // Find the most recent entry for metadata (streak, tier, etc.)
        let lastLoyaltyPointEntry = null;
        const allEntries = [...orgEntries, ...orgAsParentEntries];
        if (allEntries.length > 0) {
          allEntries.sort(
            (a, b) =>
              new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
          );
          lastLoyaltyPointEntry = allEntries[0];
        }

        /**
         * If any entry does not exist for loyalty point, create a new entry with appropriate points.
         * Set rolling points and life time points equal to the points.
         */
        if (!lastLoyaltyPointEntry) {
          let initialRollingPoints = points;
          let initialLifetimePoints = points;

          // If we have a parent org, check for its latest entry to inherit points
          if (parentOrganisationId) {
            const parentLatestEntry = await this.findOne(
              { organisationId: parentOrganisationId },
              { order: [['createdAt', 'DESC']] },
            );

            if (parentLatestEntry) {
              initialRollingPoints = parentLatestEntry.rollingPoints + points;
              initialLifetimePoints = parentLatestEntry.lifeTimePoints + points;
            }
          }

          let newEntryPlaceholder = placeholders;
          if (pointsConfig.groupConsecutiveEntries) {
            newEntryPlaceholder = {
              ...placeholders,
              consecutivePostsCount: 1,
            };
          }

          const newEntryData = {
            organisationId,
            parentOrganisationId: parentOrganisationId || null,
            type,
            points,
            rollingPoints: initialRollingPoints,
            lifeTimePoints: initialLifetimePoints,
            tier: '',
            placeholders: newEntryPlaceholder,
          };

          const newEntry = await this.create(newEntryData, { transaction });

          return newEntry;
        }

        // Handle CreatePost with daily limit check
        if (String(type) === String(ActivityType.CreatePost)) {
          if (
            lastLoyaltyPointEntry &&
            new Date(lastLoyaltyPointEntry.createdAt).toDateString() ===
              new Date().toDateString() &&
            pointsConfig?.maxPoints &&
            totalPoints + points > pointsConfig?.maxPoints
          ) {
            this.logger.info(
              `Organisation id ${organisationId} has already reached the max points for posts today`,
              {
                organisationId,
                totalPoints,
                maxPoints: pointsConfig?.maxPoints,
              },
            );

            // Use last valid points for zero-point entry
            const { rollingPoints, lifeTimePoints } = getLastValidPoints(
              parentOrganisationId,
              orgEntries,
              orgAsParentEntries,
              parentOrgEntries,
            );
            return await this.create({
              ...(({ id, createdAt, ...rest }: any) => rest)(
                lastLoyaltyPointEntry.toJSON(),
              ),
              type,
              points:
                OrganisationLoyaltyPointsService.POINTS[
                  ActivityType.ZeroLoyaltyPoints
                ].points,
              rollingPoints,
              lifeTimePoints,
              placeholders: {
                ...placeholders,
                dailyLimitReached: true,
              },
            });
          }
        }
        // Handle other activity types with original logic
        else if (lastLoyaltyPointEntry) {
          if (
            (new Date(lastLoyaltyPointEntry.createdAt).toDateString() ===
              new Date().toDateString() &&
              pointsConfig?.maxPoints &&
              totalPoints + points > pointsConfig?.maxPoints) ||
            (pointsConfig?.maxPointsPerMonth &&
              hasActivityRecordedThisMonth + points >
                pointsConfig?.maxPointsPerMonth)
          ) {
            this.logger.info(
              `Organisation id ${organisationId} has already reached the max points for activity type ${type}`,
              {
                organisationId,
                type,
                totalPoints,
                hasActivityRecordedThisMonth,
                maxPoints: pointsConfig?.maxPoints,
                maxPointsPerMonth: pointsConfig?.maxPointsPerMonth,
              },
            );

            const limitKey = pointsConfig?.maxPoints
              ? 'dailyLimitReached'
              : 'monthlyLimitReached';

            // Use last valid points for zero-point entry
            const { rollingPoints, lifeTimePoints } = getLastValidPoints(
              parentOrganisationId,
              orgEntries,
              orgAsParentEntries,
              parentOrgEntries,
            );
            return await this.create({
              ...(({ id, createdAt, ...rest }: any) => rest)(
                lastLoyaltyPointEntry.toJSON(),
              ),
              type,
              points:
                OrganisationLoyaltyPointsService.POINTS[
                  ActivityType.ZeroLoyaltyPoints
                ].points,
              rollingPoints,
              lifeTimePoints,
              placeholders: {
                ...placeholders,
                [limitKey]: true,
              },
            });
          }
        }

        let newEntryPlaceholder = placeholders;
        let updateEntry = false;
        /**
         * 1. If the last entry is one day before and the current entry then set the consecutivePostsCount to 1 and create new entry.
         * 2. If the last entry is the same day as the current entry and the last entry type is same as the current entry then increment the consecutivePostsCount by 1 and update the previous entry.
         * 3. If the last entry is the same day as the current entry and the last entry type is different from the current entry then set the consecutivePostsCount to 1 and create new entry.
         */
        if (
          pointsConfig.groupConsecutiveEntries &&
          lastLoyaltyPointEntry.type === type
        ) {
          const lastEntryDate = moment(lastLoyaltyPointEntry.createdAt).startOf(
            'day',
          );
          const currentDate = moment().startOf('day');
          const isConsecutiveDay = lastEntryDate.isSame(currentDate, 'day');

          if (isConsecutiveDay) {
            newEntryPlaceholder = {
              ...placeholders,
              consecutivePostsCount:
                lastLoyaltyPointEntry.placeholders?.consecutivePostsCount + 1,
            };
            updateEntry = true;
          } else {
            newEntryPlaceholder = {
              ...placeholders,
              consecutivePostsCount: 1,
            };
          }
        } else if (pointsConfig.groupConsecutiveEntries) {
          newEntryPlaceholder = {
            ...placeholders,
            consecutivePostsCount: 1,
          };
        }

        // For rolling points, if the month has changed then reset the points.
        const lastEntryMonth = lastLoyaltyPointEntry
          ? moment(lastLoyaltyPointEntry.createdAt).month()
          : moment().month();
        const currentMonth = moment().month();
        const resetRollingPoints = lastEntryMonth !== currentMonth;

        // Use the most recent valid points entry we found as our baseline instead of just the last entry
        // This ensures continuity when organizations switch roles
        const {
          streak: lastEntryStreak = 0,
          currentTierIndex: lastEntryCurrentTierIndex = 0,
          nextTierIndex: lastEntryNextTierIndex = 1,
          previousMonthTierIndex: lastEntryPreviousMonthTierIndex = 0,
          streakUpdatedAt: lastEntryStreakUpdatedAt = new Date(),
        } = lastLoyaltyPointEntry || {};

        // Use the points from our valid entry tracking we did above
        // This ensures we're always building on the last real points, not zero-point entries
        let rollingPoints = baseRollingPoints + points;
        let nextTierIndex = lastEntryNextTierIndex;
        let currentTierIndex = lastEntryCurrentTierIndex;
        let previousMonthTierIndex = lastEntryPreviousMonthTierIndex;
        let streak = lastEntryStreak;
        let streakUpdatedAt = lastEntryStreakUpdatedAt;
        const lifeTimePoints = baseLifeTimePoints + points;
        const totalTiers = OrganisationLoyaltyPointsService.TIERS.length - 1;

        const lastRollingPointsTierIndex =
          baseRollingPoints >
          OrganisationLoyaltyPointsService.TIERS[totalTiers].maxPoints
            ? totalTiers
            : OrganisationLoyaltyPointsService.TIERS.findIndex(
                t =>
                  t.minPoints <= baseRollingPoints &&
                  t.maxPoints >= baseRollingPoints,
              );
        const rollingPointsTierIndex =
          OrganisationLoyaltyPointsService.TIERS.findIndex(
            t => t.minPoints <= rollingPoints && t.maxPoints >= rollingPoints,
          );

        if (resetRollingPoints) {
          previousMonthTierIndex = currentTierIndex;
          nextTierIndex = currentTierIndex;
          rollingPoints = points;
          currentTierIndex = 0;

          if (
            baseRollingPoints <
            OrganisationLoyaltyPointsService.TIERS[totalTiers].minPoints
          ) {
            streak = 0;
          }

          if (lastRollingPointsTierIndex < lastEntryCurrentTierIndex - 1) {
            previousMonthTierIndex = lastEntryCurrentTierIndex - 1;
            nextTierIndex = lastEntryCurrentTierIndex - 1;
          }

          if (lastEntryPreviousMonthTierIndex > lastEntryCurrentTierIndex) {
            previousMonthTierIndex = lastEntryPreviousMonthTierIndex - 1;
            nextTierIndex = lastEntryPreviousMonthTierIndex - 1;
          }

          // Ensure nextTierIndex is always at least 1
          if (nextTierIndex < 1) {
            nextTierIndex = 1;
          }
        } else {
          // Manage streak
          if (
            rollingPoints >
              OrganisationLoyaltyPointsService.TIERS[lastRollingPointsTierIndex]
                ?.maxPoints &&
            lastRollingPointsTierIndex === totalTiers - 1
          ) {
            streak += 1;
            streakUpdatedAt = new Date();
          } else if (
            points < 0 &&
            lastEntryStreak > 0 &&
            rollingPoints <
              OrganisationLoyaltyPointsService.TIERS[lastRollingPointsTierIndex]
                .minPoints
          ) {
            const today = moment().startOf('day').toDate();
            const lastStreakDate = moment(streakUpdatedAt).toDate();
            if (moment(today).isSame(lastStreakDate, 'month')) {
              streak -= 1;
              streakUpdatedAt = new Date();
            }
          }

          // Manage current tier index
          if (
            rollingPoints >
            OrganisationLoyaltyPointsService.TIERS[currentTierIndex].maxPoints
          ) {
            currentTierIndex += 1;
            if (currentTierIndex > totalTiers) {
              currentTierIndex -= 1;
            }

            nextTierIndex = currentTierIndex + 1;
            if (nextTierIndex > totalTiers) {
              nextTierIndex = totalTiers;
            }
          } else if (
            points < 0 &&
            lastEntryStreak > 0 &&
            lastRollingPointsTierIndex === totalTiers &&
            rollingPoints <
              OrganisationLoyaltyPointsService.TIERS[currentTierIndex].minPoints
          ) {
            const startOfMonthDate = moment().startOf('month').toDate();
            const lastCurrentTierEntry = await this.findOne({
              organisationId,
              currentTierIndex: currentTierIndex - 1,
              createdAt: {
                [Op.gte]: startOfMonthDate,
              },
            });

            if (lastCurrentTierEntry) {
              currentTierIndex -= 1;

              if (currentTierIndex < 0) {
                currentTierIndex = 0;
              }

              if (!(lastEntryStreak > 0)) {
                nextTierIndex = currentTierIndex + 1;
                if (nextTierIndex > totalTiers) {
                  nextTierIndex = totalTiers;
                }
              }
            }
          } else if (
            rollingPointsTierIndex === currentTierIndex &&
            currentTierIndex === nextTierIndex
          ) {
            nextTierIndex = currentTierIndex + 1;
            if (nextTierIndex > totalTiers) {
              nextTierIndex = totalTiers;
            }
          }
        }

        const activeTier =
          currentTierIndex >= previousMonthTierIndex
            ? currentTierIndex
            : previousMonthTierIndex;

        const loyaltyPointEntry = updateEntry
          ? await this.updateById(
              lastLoyaltyPointEntry.id,
              {
                points: lastLoyaltyPointEntry.points + points,
                type,
                rollingPoints,
                lifeTimePoints,
                organisationId,
                parentOrganisationId: parentOrganisationId || null,
                streak,
                tier: '',
                placeholders: newEntryPlaceholder,
                currentTierIndex,
                nextTierIndex,
                previousMonthTierIndex,
                activeTier,
                streakUpdatedAt,
              },
              {
                transaction,
              },
            )
          : await this.create(
              {
                points,
                type,
                rollingPoints,
                lifeTimePoints,
                organisationId,
                parentOrganisationId: parentOrganisationId || null,
                streak,
                tier: '',
                placeholders: newEntryPlaceholder,
                currentTierIndex,
                nextTierIndex,
                previousMonthTierIndex,
                activeTier,
                streakUpdatedAt,
              },
              {
                transaction,
              },
            );

        this.logger.verbose(
          'OrganisationLoyaltyPointsService.addPoints (points added)',
          {
            organisationId,
            type,
            points,
          },
        );

        let isTierUpgraded = currentTierIndex > lastEntryCurrentTierIndex;
        let isTierRetained = isTierUpgraded
          ? false
          : rollingPointsTierIndex === currentTierIndex &&
            rollingPointsTierIndex > lastRollingPointsTierIndex;

        if (isTierUpgraded || isTierRetained) {
          const currentTier =
            OrganisationLoyaltyPointsService.TIERS[currentTierIndex];
          const nextTier =
            currentTierIndex !== nextTierIndex
              ? OrganisationLoyaltyPointsService.TIERS[nextTierIndex]
              : null;

          // if (loyaltyPointEntry.streak > 11) {
          //   currentTier.name = 'Platinum';
          // }

          // if (loyaltyPointEntry.streak === 12) {
          //   isTierUpgraded = true;
          //   isTierRetained = false;
          // }

          this.logger.info(
            `Organisation ${organisationId} has been ${
              isTierUpgraded ? 'upgraded' : 'retained'
            } to ${currentTier.name} tier.`,
          );
        }

        return loyaltyPointEntry;
      }

      const totalPoints = await this.sumPointsByType({
        organisationId,
        type,
        timezone: (organisation as any).timezone || 'UTC',
      });
      const hasActivityRecordedThisMonth =
        await this.hasActivityRecordedThisMonth({
          organisationId,
          type,
          timezone: (organisation as any).timezone || 'UTC',
        });

      // Check if any entry exists for the given profile.
      // First try to find an entry with the same organisationId and parentOrganisationId
      let lastLoyaltyPointEntry = await this.findOne(
        {
          organisationId,
          parentOrganisationId: parentOrganisationId || { [Op.is]: null },
        },
        {
          order: [['createdAt', 'DESC']],
          transaction,
        },
      );

      // If not found, try to find any entry for the organisation
      if (!lastLoyaltyPointEntry) {
        lastLoyaltyPointEntry = await this.findOne(
          {
            organisationId,
          },
          {
            order: [['createdAt', 'DESC']],
            transaction,
          },
        );
      }

      /**
       * If any entry does not exist for loyalty point, create a new entry with appropriate points.
       * Set rolling points and life time points equal to the points.
       */
      if (!lastLoyaltyPointEntry) {
        let newEntryPlaceholder = placeholders;
        if (pointsConfig.groupConsecutiveEntries) {
          newEntryPlaceholder = {
            ...placeholders,
            consecutivePostsCount: 1,
          };
        }

        const newEntryData = {
          organisationId,
          parentOrganisationId: parentOrganisationId || null,
          type,
          points,
          rollingPoints: points,
          lifeTimePoints: points,
          tier: '',
          placeholders: newEntryPlaceholder,
        };

        const newEntry = await this.create(newEntryData, { transaction });

        return newEntry;
      }

      // Check if the total points for the activity type exceed the max points per month
      if (
        lastLoyaltyPointEntry &&
        ((new Date(lastLoyaltyPointEntry.createdAt).toDateString() ===
          new Date().toDateString() &&
          pointsConfig?.maxPoints &&
          totalPoints + points > pointsConfig?.maxPoints) ||
          (pointsConfig?.maxPointsPerMonth &&
            hasActivityRecordedThisMonth + points >
              pointsConfig?.maxPointsPerMonth))
      ) {
        this.logger.info(
          `Organisation id ${organisationId} has already reached the max points for activity type ${type}`,
        );

        const limitKey = pointsConfig?.maxPoints
          ? 'dailyLimitReached'
          : 'monthlyLimitReached';

        return await this.create({
          ...(({ id, createdAt, ...rest }: any) => rest)(
            lastLoyaltyPointEntry.toJSON(),
          ), // Exclude the id & createdAt field
          type,
          points:
            OrganisationLoyaltyPointsService.POINTS[
              ActivityType.ZeroLoyaltyPoints
            ].points,
          placeholders: {
            ...placeholders,
            [limitKey]: true,
          },
        });
      }

      let newEntryPlaceholder = placeholders;
      let updateEntry = false;
      /**
       * 1. If the last entry is one day before and the current entry then set the consecutivePostsCount to 1 and create new entry.
       * 2. If the last entry is the same day as the current entry and the last entry type is same as the current entry then increment the consecutivePostsCount by 1 and update the previous entry.
       * 3. If the last entry is the same day as the current entry and the last entry type is different from the current entry then set the consecutivePostsCount to 1 and create new entry.
       */
      if (
        pointsConfig.groupConsecutiveEntries &&
        lastLoyaltyPointEntry.type === type
      ) {
        const lastEntryDate = moment(lastLoyaltyPointEntry.createdAt).startOf(
          'day',
        );
        const currentDate = moment().startOf('day');
        const isConsecutiveDay = lastEntryDate.isSame(currentDate, 'day');

        if (isConsecutiveDay) {
          newEntryPlaceholder = {
            ...placeholders,
            consecutivePostsCount:
              lastLoyaltyPointEntry.placeholders?.consecutivePostsCount + 1,
          };
          updateEntry = true;
        } else {
          newEntryPlaceholder = {
            ...placeholders,
            consecutivePostsCount: 1,
          };
        }
      } else if (pointsConfig.groupConsecutiveEntries) {
        newEntryPlaceholder = {
          ...placeholders,
          consecutivePostsCount: 1,
        };
      }

      // For rolling points, if the month has changed then reset the points.
      const lastEntryMonth = moment(lastLoyaltyPointEntry.createdAt).month();
      const currentMonth = moment().month();
      const resetRollingPoints = lastEntryMonth !== currentMonth;

      // Streak code has been commented out for now as it is not required to show Platinum Streak for Organisations
      const {
        rollingPoints: lastEntryRollingPoints,
        lifeTimePoints: lastEntryLifeTimePoints,
        streak: lastEntryStreak,
        currentTierIndex: lastEntryCurrentTierIndex,
        nextTierIndex: lastEntryNextTierIndex,
        previousMonthTierIndex: lastEntryPreviousMonthTierIndex,
        streakUpdatedAt: lastEntryStreakUpdatedAt,
      } = lastLoyaltyPointEntry;

      let rollingPoints = lastEntryRollingPoints + points;
      let nextTierIndex = lastEntryNextTierIndex;
      let currentTierIndex = lastEntryCurrentTierIndex;
      let previousMonthTierIndex = lastEntryPreviousMonthTierIndex;
      let streak = lastEntryStreak;
      let streakUpdatedAt = lastEntryStreakUpdatedAt;
      const lifeTimePoints = lastEntryLifeTimePoints + points;
      const totalTiers = OrganisationLoyaltyPointsService.TIERS.length - 1;

      const lastRollingPointsTierIndex =
        lastEntryRollingPoints >
          OrganisationLoyaltyPointsService.TIERS[totalTiers].maxPoints
          ? totalTiers
          : OrganisationLoyaltyPointsService.TIERS.findIndex(
            t =>
              t.minPoints <= lastEntryRollingPoints &&
              t.maxPoints >= lastEntryRollingPoints,
          );
      const rollingPointsTierIndex =
        OrganisationLoyaltyPointsService.TIERS.findIndex(
          t => t.minPoints <= rollingPoints && t.maxPoints >= rollingPoints,
        );

      if (resetRollingPoints) {
        previousMonthTierIndex = currentTierIndex;
        nextTierIndex = currentTierIndex;
        rollingPoints = points;
        currentTierIndex = 0;

        if (
          lastEntryRollingPoints <
          OrganisationLoyaltyPointsService.TIERS[totalTiers].minPoints
        ) {
          streak = 0;
        }

        if (lastRollingPointsTierIndex < lastEntryCurrentTierIndex - 1) {
          previousMonthTierIndex = lastEntryCurrentTierIndex - 1;
          nextTierIndex = lastEntryCurrentTierIndex - 1;
        }

        if (lastEntryPreviousMonthTierIndex > lastEntryCurrentTierIndex) {
          previousMonthTierIndex = lastEntryPreviousMonthTierIndex - 1;
          nextTierIndex = lastEntryPreviousMonthTierIndex - 1;
        }

        // Ensure nextTierIndex is always at least 1
        if (nextTierIndex < 1) {
          nextTierIndex = 1;
        }
      } else {
        // Manage streak
        if (
          rollingPoints >
          OrganisationLoyaltyPointsService.TIERS[lastRollingPointsTierIndex]
            ?.maxPoints &&
          lastRollingPointsTierIndex === totalTiers - 1
        ) {
          streak += 1;
          streakUpdatedAt = new Date();
        } else if (
          points < 0 &&
          lastEntryStreak > 0 &&
          rollingPoints <
          OrganisationLoyaltyPointsService.TIERS[lastRollingPointsTierIndex]
            .minPoints
        ) {
          const today = moment().startOf('day').toDate();
          const lastStreakDate = moment(streakUpdatedAt).toDate();
          if (moment(today).isSame(lastStreakDate, 'month')) {
            streak -= 1;
            streakUpdatedAt = new Date();
          }
        }

        // Manage current tier index
        if (
          rollingPoints >
          OrganisationLoyaltyPointsService.TIERS[currentTierIndex].maxPoints
        ) {
          currentTierIndex += 1;
          if (currentTierIndex > totalTiers) {
            currentTierIndex -= 1;
          }

          nextTierIndex = currentTierIndex + 1;
          if (nextTierIndex > totalTiers) {
            nextTierIndex = totalTiers;
          }
        } else if (
          points < 0 &&
          lastEntryStreak > 0 &&
          lastRollingPointsTierIndex === totalTiers &&
          rollingPoints <
          OrganisationLoyaltyPointsService.TIERS[currentTierIndex].minPoints
        ) {
          const startOfMonthDate = moment().startOf('month').toDate();
          const lastCurrentTierEntry = await this.findOne({
            organisationId,
            currentTierIndex: currentTierIndex - 1,
            createdAt: {
              [Op.gte]: startOfMonthDate,
            },
          });

          if (lastCurrentTierEntry) {
            currentTierIndex -= 1;

            if (currentTierIndex < 0) {
              currentTierIndex = 0;
            }

            if (!(lastEntryStreak > 0)) {
              nextTierIndex = currentTierIndex + 1;
              if (nextTierIndex > totalTiers) {
                nextTierIndex = totalTiers;
              }
            }
          }
        } else if (
          rollingPointsTierIndex === currentTierIndex &&
          currentTierIndex === nextTierIndex
        ) {
          nextTierIndex = currentTierIndex + 1;
          if (nextTierIndex > totalTiers) {
            nextTierIndex = totalTiers;
          }
        }
      }

      const activeTier =
        currentTierIndex >= previousMonthTierIndex
          ? currentTierIndex
          : previousMonthTierIndex;

      const loyaltyPointEntry = updateEntry
        ? await this.updateById(
          lastLoyaltyPointEntry.id,
          {
            points: lastLoyaltyPointEntry.points + points,
            type,
            rollingPoints,
            lifeTimePoints,
            organisationId,
            parentOrganisationId: parentOrganisationId || null,
            streak,
            tier: '',
            placeholders: newEntryPlaceholder,
            currentTierIndex,
            nextTierIndex,
            previousMonthTierIndex,
            activeTier,
            streakUpdatedAt,
          },
          {
            transaction,
          },
        )
        : await this.create(
          {
            points,
            type,
            rollingPoints,
            lifeTimePoints,
            organisationId,
            parentOrganisationId: parentOrganisationId || null,
            streak,
            tier: '',
            placeholders: newEntryPlaceholder,
            currentTierIndex,
            nextTierIndex,
            previousMonthTierIndex,
            activeTier,
            streakUpdatedAt,
          },
          {
            transaction,
          },
        );

      this.logger.verbose(
        'OrganisationLoyaltyPointsService.addPoints (points added)',
        {
          organisationId,
          type,
          points,
        },
      );

      let isTierUpgraded = currentTierIndex > lastEntryCurrentTierIndex;
      let isTierRetained = isTierUpgraded
        ? false
        : rollingPointsTierIndex === currentTierIndex &&
        rollingPointsTierIndex > lastRollingPointsTierIndex;

      if (isTierUpgraded || isTierRetained) {
        const currentTier =
          OrganisationLoyaltyPointsService.TIERS[currentTierIndex];
        const nextTier =
          currentTierIndex !== nextTierIndex
            ? OrganisationLoyaltyPointsService.TIERS[nextTierIndex]
            : null;


        // if (loyaltyPointEntry.streak > 11) {
        //   currentTier.name = 'Platinum';
        // }

        // if (loyaltyPointEntry.streak === 12) {
        //   isTierUpgraded = true;
        //   isTierRetained = false;
        // }

        this.logger.info(
          `Organisation ${organisationId} has been ${isTierUpgraded ? 'upgraded' : 'retained'
          } to ${currentTier.name} tier.`,
        );
      }

      return loyaltyPointEntry;
    } catch (error) {
      this.logger.error('Error in addPoints:', error);
      this.errorHelper.throwHttpException(
        `LoyaltyPointsService.addPoints`,
        error.message,
      );
    }
  }

  async getOrganisationLoyaltyPoints({
    organisationId,
  }: {
    organisationId: string;
  }): Promise<{
    rollingPoints: number;
    lifeTimePoints: number;
    streak: number;
    currentTier: Tier;
    nextTier: Tier | null;
    previousMonthTier: Tier;
    activeTier: Tier;
  }> {
    const lastLoyaltyPointEntry = await this.findOne(
      {
        organisationId,
      },
      {
        order: [['createdAt', 'DESC']],
      },
    );

    let rollingPoints = 0;
    let lifeTimePoints = 0;
    let streak = 0;
    let activeTier: Tier = OrganisationLoyaltyPointsService.TIERS[0];
    let currentTier: Tier = OrganisationLoyaltyPointsService.TIERS[0];
    let nextTier: Tier | null = OrganisationLoyaltyPointsService.TIERS[1];
    let previousMonthTier: Tier = OrganisationLoyaltyPointsService.TIERS[0];

    if (lastLoyaltyPointEntry) {
      const organisation = await this.organisationsService.findById(
        organisationId,
      );
      if (!organisation) {
        this.logger.error(`Organisation not found for id ${organisationId}`);
        return {
          rollingPoints,
          lifeTimePoints,
          streak,
          currentTier,
          nextTier,
          previousMonthTier,
          activeTier,
        };
      }

      // For rolling points, if the month has changed then reset the points.
      const lastEntryMonth = moment(lastLoyaltyPointEntry.createdAt).month();
      const currentMonth = moment().month();
      const resetRollingPoints = lastEntryMonth !== currentMonth;

      lifeTimePoints = lastLoyaltyPointEntry.lifeTimePoints;
      previousMonthTier =
        OrganisationLoyaltyPointsService.TIERS[
          lastLoyaltyPointEntry.previousMonthTierIndex
        ];

      if (resetRollingPoints) {
        rollingPoints = 0;
        nextTier =
          OrganisationLoyaltyPointsService.TIERS[
            lastLoyaltyPointEntry.currentTierIndex
          ];
      } else {
        rollingPoints = lastLoyaltyPointEntry.rollingPoints;
        nextTier =
          OrganisationLoyaltyPointsService.TIERS[
            lastLoyaltyPointEntry.nextTierIndex
          ];
        currentTier =
          OrganisationLoyaltyPointsService.TIERS[
            lastLoyaltyPointEntry.currentTierIndex
          ];
      }
      activeTier =
        OrganisationLoyaltyPointsService.TIERS[
          lastLoyaltyPointEntry.activeTier
        ];

      // if (lastLoyaltyPointEntry.streak > 11) {
      //   currentTier.key = 'platinum';
      //   nextTier.key = 'platinum';
      // }
      streak = lastLoyaltyPointEntry.streak;
    }

    return {
      rollingPoints,
      lifeTimePoints,
      streak,
      currentTier,
      nextTier,
      previousMonthTier,
      activeTier,
    };
  }

  async findOneByOrganisationId(
    organisationId: string,
  ): Promise<OrganisationLoyaltyPoint | null> {
    const OrganisationLoyaltyPoint =
      await this.organisationLoyaltyPointModel.findOne({
        where: { organisationId },
        order: [['createdAt', 'DESC']],
      });
    return OrganisationLoyaltyPoint;
  }

  getTierFromIndex(tierIndex: number): string {
    const tier = OrganisationLoyaltyPointsService.TIERS[tierIndex];
    return tier ? tier.key : 'blue';
  }

  async addZeroLoyaltyPointsToOrganisations(): Promise<void> {
    const allOrganisationIds =
      await this.organisationsService.findOrganisations(
        '',
        {
          hasClubHabloSubscription: true,
        },
        {},
      );

    // Add a zero points entry for each organisation
    await Promise.all(
      allOrganisationIds.records.map(async organisation => {
        await this.addPoints({
          organisationId: organisation.id,
          type: ActivityType.ZeroLoyaltyPoints,
        });
      }),
    );
  }
}
