import {
  Column,
  Model,
  Table,
  HasMany,
  BelongsToMany,
} from 'sequelize-typescript';
import { ObjectType, Field, ID } from '@nestjs/graphql';
import { DataTypes } from 'sequelize';
import { GraphQLJSON } from 'graphql-type-json';
import short from 'short-uuid';
import { Membership } from '../../memberships/models/membership.model';
import { Achievement } from '../../achievements/models/achievements.model';
import { Follower } from '../../followers/models/follower.model';
import {
  OrganisationType,
  OrganisationPage,
  OrganisationSize,
  OrganisationPrivacySettings,
  OrganisationStatus,
  Privacy,
} from '../args/organisations.args';
import { OrganisationModelHelper } from '../helpers/organisation.model.helper';
import {
  DestinationPage,
  DestinationPageOrgs,
} from '../../explore-pages/models/explore-page.model';
import { PartnerOrganisation } from '../../partner-organisations/models/partner-organisation.model';

@ObjectType()
export class ParentOrganisationObj {
  @Field()
  id: string;
}

@ObjectType()
class ParentOrganisationDetailsData {
  @Field({ nullable: false })
  id: string;

  @Field({ nullable: false })
  name: string;

  @Field({ nullable: true })
  image?: string;

  @Field({ nullable: true })
  vanityId?: string;
}

@Table
@ObjectType()
export class Organisation extends Model<Organisation> {
  @Field(() => ID)
  @Column({
    primaryKey: true,
    unique: true,
    allowNull: false,
    defaultValue: short.generate,
  })
  id: string;

  @Field()
  @Column({ allowNull: false })
  name: string;

  @Field({ nullable: true })
  @Column
  image: string;

  @Field({ nullable: true })
  @Column
  backgroundImage: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @Column({
    type: DataTypes.JSON,
  })
  location: any;

  @Field(() => OrganisationType, { nullable: true })
  @Column({
    type: DataTypes.STRING,
  })
  type: OrganisationType;

  @Field(() => OrganisationStatus, { nullable: true })
  @Column({
    type: DataTypes.STRING,
  })
  status: OrganisationStatus;

  @Field({ nullable: true })
  @Column({
    defaultValue: Privacy.Public,
  })
  privacy: string;

  @Field({ nullable: true })
  @Column({
    defaultValue: null,
  })
  followingPrivacy: string;

  @Field({ nullable: true })
  @Column({
    defaultValue: null,
  })
  peoplePrivacy: string;

  @Field({ nullable: true })
  @Column({
    defaultValue: null,
  })
  additionalPrivacy: string;

  @Field({ nullable: true })
  @Column
  website: string;

  @Field({ nullable: true })
  @Column
  resourceUrl: string;

  @Field({ nullable: true })
  @Column({
    type: DataTypes.TEXT,
  })
  resources: string;

  @Field({ nullable: true })
  @Column({
    type: DataTypes.TEXT,
  })
  description: string;

  @Field(() => OrganisationSize, { nullable: true })
  @Column({
    type: DataTypes.STRING,
  })
  size: OrganisationSize;

  @Field({ nullable: true })
  @Column({
    type: DataTypes.STRING,
    unique: true,
  })
  vanityId: string;

  @Column({
    type: DataTypes.ARRAY(DataTypes.STRING),
    defaultValue: [],
  })
  seenAnyPostBy: string[];

  @Field({ nullable: true })
  @Column({
    type: DataTypes.STRING,
  })
  stripeConnectAccount: string;

  @Field(() => Boolean, { nullable: true, defaultValue: false })
  @Column
  isConnectOnboarded: boolean;

  @Field(() => Boolean, { nullable: true, defaultValue: false })
  @Column
  isPaid: boolean;

  @Field(() => Boolean, { nullable: true, defaultValue: false })
  @Column
  hasClubHabloSubscription: boolean;

  @Field(() => Boolean, { nullable: true })
  isAchieved?: boolean;

  @Field({ nullable: false, defaultValue: 0 })
  @Column
  currentMonthPostCount: number;

  @Field(() => GraphQLJSON, { nullable: true })
  @Column({
    type: DataTypes.JSON,
    defaultValue: [],
  })
  preApprovedDomains: any;

  @Field(() => [ParentOrganisationObj], { nullable: true })
  @Column({
    type: DataTypes.ARRAY(DataTypes.JSON),
    defaultValue: [],
  })
  parentOrganisations: ParentOrganisationObj[];

  @Field(() => [String], { nullable: true })
  @Column({
    type: DataTypes.ARRAY(DataTypes.STRING),
    defaultValue: [],
  })
  connectedOrganisations: String[];

  @Field()
  @Column
  createdAt: Date;

  @Field()
  @Column
  updatedAt: Date;

  @Field(() => Boolean, { nullable: true })
  @Column
  isPublic: boolean;

  @HasMany(() => Membership, 'organisationId')
  memberships: Membership[];

  @HasMany(() => Follower, 'organisationId')
  followers: Follower[];

  @HasMany(() => Achievement)
  achievements: Achievement[];

  @Field(() => [OrganisationPage])
  @Column(DataTypes.VIRTUAL)
  get pages(): OrganisationPage[] {
    return OrganisationModelHelper.getPages(this.type);
  }

  @Field(type => [ParentOrganisationDetailsData], { nullable: true })
  parentOrganisationDetails: ParentOrganisationDetailsData[];

  @BelongsToMany(() => DestinationPage, () => DestinationPageOrgs)
  destinationPages: DestinationPage[];

  @Field(() => String, { nullable: true })
  activeTier?: string;

  @Field(() => Number, { nullable: true })
  level?: number;

  @Field(() => Number, { nullable: true })
  achievedCounts?: number;

  @Field(() => Number, { nullable: true })
  totalCounts?: number;

  @Field(() => Boolean, { defaultValue: false })
  @Column({
    allowNull: false,
    defaultValue: false,
  })
  isDMOSubscription: boolean;

  @Field(() => Number, { nullable: true })
  @Column({
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 10,
  })
  DMOMaxPartners: number;

  @Field(() => Number, { nullable: true })
  referralCount?: number;

  @HasMany(() => PartnerOrganisation, 'parentOrgId')
  childPartners: PartnerOrganisation[];

  @HasMany(() => PartnerOrganisation, 'childOrgId')
  parentPartner: PartnerOrganisation[];
}
