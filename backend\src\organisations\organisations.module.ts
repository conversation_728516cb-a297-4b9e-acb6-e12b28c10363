import { Module, forwardRef } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Organisation } from './models/organisation.model';
import { OrganisationsService } from './organisations.service';
import { OrganisationsResolver } from './organisations.resolver';
import { OrganisationsRepository } from './organisations.repository';
import { CommonModule } from '../common/common.module';
import { MembershipsModule } from '../memberships/memberships.module';
import { FollowersModule } from '../followers/followers.module';
import { PartnershipsModule } from '../partnerships/partnerships.module';
import { PartnershipRequestsModule } from '../partnership-requests/partnership-requests.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { IncentivesModule } from '../incentives/incentives.module';
import { IncentiveParticipantsModule } from '../incentive-participants/incentive-participants.module';
import { ProfilesModule } from '../profiles/profiles.module';
import { WebinarsModule } from '../webinars/webinars.module';
import { WebinarParticipantsModule } from '../webinar-participants/webinar-participants.module';
import { EventsModule } from '../events/events.module';
import { EventsInvitationsModule } from '../event-invitations/event-invitations.module';
import { ExperiencesModule } from '../experiences/experiences.module';
import { StreamOrganisationsModule } from '../feeds-organisations/stream-organisations.module';
import { DestinationPagesModule } from '../explore-pages/explore-pages.module';
import { StripeHelper } from './helpers/stripe';
import { SubscriptionsModule } from '../subscriptions/subscriptions.module';
import { PaymentTransactionsModule } from '../payment-transactions/payment-transactions.module';
import { StreamFollowersModule } from '../feeds-followers/stream-followers.module';
import { OrganisationLoyaltyPointsModule } from '../organisation-loyalty-points/organisation-loyalty-points.module';
import { PartnerOrganisationsModule } from '../partner-organisations/partner-organisations.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Organisation]),
    forwardRef(() => CommonModule),
    forwardRef(() => MembershipsModule),
    forwardRef(() => FollowersModule),
    forwardRef(() => PartnershipsModule),
    forwardRef(() => PartnershipRequestsModule),
    forwardRef(() => NotificationsModule),
    forwardRef(() => IncentivesModule),
    forwardRef(() => IncentiveParticipantsModule),
    forwardRef(() => ProfilesModule),
    forwardRef(() => WebinarsModule),
    forwardRef(() => WebinarParticipantsModule),
    forwardRef(() => EventsModule),
    forwardRef(() => EventsInvitationsModule),
    forwardRef(() => ExperiencesModule),
    forwardRef(() => StreamOrganisationsModule),
    forwardRef(() => DestinationPagesModule),
    forwardRef(() => SubscriptionsModule),
    forwardRef(() => PaymentTransactionsModule),
    forwardRef(() => StreamFollowersModule),
    forwardRef(() => OrganisationLoyaltyPointsModule),
    forwardRef(() => PartnerOrganisationsModule),
  ],
  providers: [
    OrganisationsResolver,
    OrganisationsService,
    OrganisationsRepository,
    StripeHelper,
  ],
  exports: [OrganisationsService],
})
export class OrganisationsModule {}
