import { UseGuards, Inject, forwardRef } from '@nestjs/common';
import {
  Args,
  Query,
  Resolver,
  Mutation,
  ResolveField,
  Parent,
  Int,
} from '@nestjs/graphql';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { Sequelize } from 'sequelize-typescript';

import { Organisation } from './models/organisation.model';
import { OrganisationsService } from './organisations.service';
import { GqlAuthGuard } from '../authz/graphql-auth.guard';
import {
  CurrentUser,
  ICurrentUser,
} from '../common/decorators/current-user.decorator';
import { CreateOrganisationInput } from './dto/create-organisation.input';
import {
  Membership,
  MembershipPermission,
  MembershipStatus,
} from '../memberships/models/membership.model';
import { MembershipsService } from '../memberships/memberships.service';
import {
  FollowersResult,
  IncentivesResult,
  MembershipsResult,
  OrganisationsResult,
  PaginatedResult,
} from '../common/args/paginated-result';
import {
  OrganisationsArgs,
  OrganisationStatus,
} from './args/organisations.args';
import { FollowersService } from '../followers/followers.service';
import { Follower, FollowerStatus } from '../followers/models/follower.model';
import { UpdateOrganisationInput } from './dto/update-organisation.input';
import { MembershipsArgs } from '../memberships/args/memberships.args';
import { FollowersArgs } from '../followers/args/followers.args';
import { Partnership } from '../partnerships/models/partnerships.model';
import { PartnershipsService } from '../partnerships/partnerships.service';
import {
  PartnershipRequest,
  PartnershipRequestStatus,
} from '../partnership-requests/models/partnership-request.model';
import { PartnershipRequestsService } from '../partnership-requests/partnership-requests.service';
import { IncentivesArgs } from '../incentives/args/incentives.args';
import { Incentive } from '../incentives/models/incentive.model';
import { IncentivesService } from '../incentives/incentives.service';
import { WebinarsService } from '../webinars/webinars.service';
import { EventsService } from '../events/events.service';
import { EventType } from '../events/args/events.args';
import {
  Subscription,
  SubscriptionStatus,
} from '../subscriptions/models/subscription.model';
import { PaymentTransaction } from '../payment-transactions/models/payment-transaction.model';
import { SubscriptionsService } from '../subscriptions/subscriptions.service';
import { PaymentTransactionsService } from '../payment-transactions/payment-transactions.service';
import { AddPreApprovedDomainInput } from './dto/add-pre-approved-domains.input';
import { ParentOrganisationInput } from './dto/add-parent-organisation';
import { OrganisationLoyaltyPointsService } from '../organisation-loyalty-points/organisation-loyalty-points.service';
import { AchievementType } from '../achievements/args/achievements.args';

@Resolver(() => Organisation)
export class OrganisationsResolver {
  @Inject(forwardRef(() => OrganisationsService))
  private readonly organisationsService: OrganisationsService;
  @Inject(forwardRef(() => MembershipsService))
  private readonly membershipsService: MembershipsService;
  @Inject(forwardRef(() => FollowersService))
  private readonly followersService: FollowersService;
  @Inject(forwardRef(() => PartnershipsService))
  private readonly partnershipsService: PartnershipsService;
  @Inject(forwardRef(() => PartnershipRequestsService))
  private readonly partnershipRequestsService: PartnershipRequestsService;
  @Inject(forwardRef(() => IncentivesService))
  private readonly incentivesService: IncentivesService;
  @Inject(forwardRef(() => WebinarsService))
  private readonly webinarsService: WebinarsService;
  @Inject(forwardRef(() => EventsService))
  private readonly eventsService: EventsService;
  @Inject(forwardRef(() => SubscriptionsService))
  private readonly subscriptionsService: SubscriptionsService;
  @Inject(forwardRef(() => PaymentTransactionsService))
  private readonly paymentTransactionsService: PaymentTransactionsService;
  @Inject(WINSTON_MODULE_PROVIDER)
  private readonly logger: Logger;
  @Inject(forwardRef(() => OrganisationLoyaltyPointsService))
  private readonly organisationLoyaltyPointsService: OrganisationLoyaltyPointsService;

  @Query(() => Organisation)
  @UseGuards(GqlAuthGuard)
  async organisation(
    @CurrentUser() user: ICurrentUser,
    @Args('id', { nullable: true }) id?: string,
    @Args('vanityId', { nullable: true }) vanityId?: string,
  ): Promise<Organisation> {
    this.logger.verbose('OrganisationsResolver.organisation (query)', {
      user: user.toLogObject(),
      id,
      vanityId,
    });

    if (!id && !vanityId) {
      throw new Error(`Organisation id or vanityId should be set`);
    }

    if (!!vanityId && vanityId.startsWith('-removed!-')) {
      throw new Error(`Organisation removed`);
    }

    const organisation = await this.organisationsService.findOne(
      id
        ? { id, status: OrganisationStatus.Active }
        : Sequelize.where(
            Sequelize.fn('lower', Sequelize.col('vanityId')),
            Sequelize.fn('lower', vanityId),
          ),
    );

    const parentOrgData = [];

    if (organisation?.parentOrganisations?.length) {
      for (const org of organisation.parentOrganisations) {
        const organisation = await this.organisationsService.findById(org.id);
        parentOrgData.push({
          id: organisation.id,
          name: organisation.name,
          image: organisation.image,
          vanityId: organisation.vanityId,
        });
      }
      organisation.parentOrganisationDetails = parentOrgData;
    }

    if (!organisation) {
      throw new Error(`Organisation not found`);
    }
    return organisation;
  }

  @Query(() => OrganisationsResult)
  @UseGuards(GqlAuthGuard)
  organisations(
    @CurrentUser() user: ICurrentUser,
    @Args() organisationArgs: OrganisationsArgs,
  ): Promise<PaginatedResult<Organisation>> {
    this.logger.verbose('OrganisationsResolver.organisations (query)', {
      user: user.toLogObject(),
      organisationArgs,
    });

    return this.organisationsService.findOrganisations(
      user.profileId,
      {
        id: organisationArgs?.filter?.id,
        type: organisationArgs?.filter?.type,
        searchText: organisationArgs?.filter?.searchText,
        vanityId: organisationArgs?.filter?.vanityId,
        isFuzzySearch: organisationArgs?.filter?.isFuzzySearch,
      },
      {
        first: organisationArgs?.first,
        after: organisationArgs?.after,
        sortBy: organisationArgs?.sortBy,
        sortOrder: organisationArgs?.sortOrder,
      },
    );
  }

  @Mutation(() => Organisation)
  @UseGuards(GqlAuthGuard)
  createOrganisation(
    @CurrentUser() user: ICurrentUser,
    @Args('organisationData') organisationData: CreateOrganisationInput,
  ): Promise<Organisation> {
    this.logger.verbose('OrganisationsResolver.createOrganisation (mutation)', {
      user: user.toLogObject(),
      organisationData,
    });

    return this.organisationsService.createOrganisation(user, organisationData);
  }

  @Mutation(() => Organisation)
  @UseGuards(GqlAuthGuard)
  updateOrganisation(
    @CurrentUser() user: ICurrentUser,
    @Args('organisationId') organisationId: string,
    @Args('organisationData') organisationData: UpdateOrganisationInput,
  ): Promise<Organisation> {
    this.logger.verbose('OrganisationsResolver.updateOrganisation (mutation)', {
      user: user.toLogObject(),
      organisationData,
    });

    return this.organisationsService.updateOrganisation(
      organisationId,
      organisationData,
      {
        profileId: user.profileId,
      },
    );
  }

  // @Mutation(() => Boolean)
  // @UseGuards(GqlAuthGuard)
  // async removeOrganisation(
  //   @CurrentUser() user: ICurrentUser,
  //   @Args('organisationId') organisationId: string,
  // ): Promise<boolean> {
  //   this.logger.verbose('OrganisationsResolver.removeOrganisation (mutation)', {
  //     user: user.toLogObject(),
  //     organisationId,
  //   });

  //   await this.organisationsService.removeOrganisation(organisationId, {
  //     profileId: user.profileId,
  //   });

  //   return true;
  // }

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  async adminPanelRemoveOrganisation(
    @CurrentUser() user: ICurrentUser,
    @Args('organisationId') organisationId: string,
  ): Promise<boolean> {
    this.logger.verbose('OrganisationsResolver.removeOrganisation (mutation)', {
      user: user.toLogObject(),
      organisationId,
    });

    await this.organisationsService.adminPanelRemoveOrganisation(
      organisationId,
      {
        profileId: user.profileId,
      },
    );

    return true;
  }

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  async changeOrganisationOwnership(
    @CurrentUser() user: ICurrentUser,
    @Args('organisationId') organisationId: string,
    @Args('newOwnerProfileId') newOwnerProfileId: string,
  ): Promise<boolean> {
    this.logger.verbose(
      'OrganisationsResolver.changeOrganisationOwnership (mutation)',
      {
        user: user.toLogObject(),
        organisationId,
      },
    );

    await this.organisationsService.changeOrganisationOwnership(
      organisationId,
      newOwnerProfileId,
      {
        currentUser: user,
      },
    );

    return true;
  }

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  async acceptOrganisationOwnership(
    @CurrentUser() user: ICurrentUser,
    @Args('organisationId') organisationId: string,
  ): Promise<boolean> {
    this.logger.verbose(
      'OrganisationsResolver.acceptOrganisationOwnership (mutation)',
      {
        organisationId,
      },
    );

    await this.organisationsService.acceptOrganisationOwnership(
      organisationId,
      {
        currentUser: user,
      },
    );

    return true;
  }

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  async rejectOrganisationOwnership(
    @CurrentUser() user: ICurrentUser,
    @Args('organisationId') organisationId: string,
  ): Promise<boolean> {
    this.logger.verbose(
      'OrganisationsResolver.rejectOrganisationOwnership (mutation)',
      {
        organisationId,
      },
    );

    await this.organisationsService.rejectOrganisationOwnership(
      organisationId,
      {
        currentUser: user,
      },
    );

    return true;
  }

  @ResolveField('memberships', () => MembershipsResult)
  memberships(
    @CurrentUser() user: ICurrentUser,
    @Parent() organisation: Organisation,
    @Args() membershipArgs?: MembershipsArgs,
  ): Promise<PaginatedResult<Membership>> {
    this.logger.verbose('OrganisationsResolver.memberships (field resolver)', {
      organisationId: organisation.id,
      membershipArgs,
      organisationStatus: organisation.status,
    });

    if (organisation.status !== OrganisationStatus.Active) {
      return;
    }

    return this.membershipsService.findMemberships(
      user.profileId,
      {
        profileId: membershipArgs?.filter?.profileId,
        organisationId: organisation.id,
        status: membershipArgs?.filter?.status,
        permission: membershipArgs?.filter?.permission,
        isPrimary: membershipArgs?.filter?.isPrimary,
        includeActiveProfile: membershipArgs?.filter?.includeActiveProfile,
      },
      {
        first: membershipArgs?.first,
        after: membershipArgs?.after,
        sortBy: membershipArgs?.sortBy,
        sortOrder: membershipArgs?.sortOrder,
      },
    );
  }

  @ResolveField('incentives', () => IncentivesResult)
  incentives(
    @CurrentUser() user: ICurrentUser,
    @Parent() organisation: Organisation,
    @Args() incentiveArgs?: IncentivesArgs,
  ): Promise<PaginatedResult<Incentive>> {
    this.logger.verbose('OrganisationsResolver.incentives (field resolver)', {
      organisationId: organisation.id,
      incentiveArgs,
    });

    return this.incentivesService.findIncentives(
      user.profileId,
      {
        incentiveParticipantStatus:
          incentiveArgs?.filter?.incentiveParticipantStatus,
        organisationId: organisation.id,
        type: incentiveArgs?.filter?.type,
        isPublic: incentiveArgs?.filter?.isPublic,
        searchText: incentiveArgs?.filter?.searchText,
        isEnded: incentiveArgs?.filter?.isEnded,
      },
      {
        first: incentiveArgs?.first,
        after: incentiveArgs?.after,
        sortBy: incentiveArgs?.sortBy,
        sortOrder: incentiveArgs?.sortOrder,
      },
    );
  }

  @ResolveField('followers', () => FollowersResult)
  followers(
    @CurrentUser() user: ICurrentUser,
    @Parent() organisation: Organisation,
    @Args() followerArgs?: FollowersArgs,
  ): Promise<PaginatedResult<Follower>> {
    this.logger.verbose('OrganisationsResolver.followers (field resolver)', {
      organisationId: organisation.id,
      followerArgs,
      organisationStatus: organisation.status,
    });

    if (organisation.status !== OrganisationStatus.Active) {
      return;
    }

    return this.followersService.findFollowers(
      user.profileId,
      {
        profileId: followerArgs?.filter?.profileId,
        organisationId: organisation.id,
        status: followerArgs?.filter?.status,
        includeActiveProfile: followerArgs?.filter?.includeActiveProfile,
      },
      {
        first: followerArgs?.first,
        after: followerArgs?.after,
        sortBy: followerArgs?.sortBy,
        sortOrder: followerArgs?.sortOrder,
      },
    );
  }

  @ResolveField('partnerships', () => [Partnership])
  async partnerships(
    @Parent() organisation: Organisation,
    @Args('isActive', { nullable: true }) isActive?: boolean,
  ): Promise<Partnership[]> {
    this.logger.verbose('OrganisationsResolver.partnerships (field resolver)', {
      organisation,
      isActive,
    });

    const queryOptions = {
      partnershipOrganisationId: organisation.id,
    };

    const includeOptions = [
      {
        model: PartnershipRequest,
        as: 'partnershipRequest',
        where: {
          status:
            isActive === undefined
              ? [
                  PartnershipRequestStatus.Approved,
                  PartnershipRequestStatus.Declined,
                  PartnershipRequestStatus.Pending,
                  PartnershipRequestStatus.Disconnected,
                ]
              : isActive
              ? [PartnershipRequestStatus.Approved]
              : [PartnershipRequestStatus.Disconnected],
        },
        include: [
          {
            model: Subscription,
            as: 'subscription',
            where: {
              status:
                isActive === undefined
                  ? [
                      SubscriptionStatus.Active,
                      SubscriptionStatus.Cancelled,
                      SubscriptionStatus.Expired,
                    ]
                  : isActive
                  ? [SubscriptionStatus.Active, SubscriptionStatus.Cancelled]
                  : [SubscriptionStatus.Cancelled, SubscriptionStatus.Expired],
            },
            required: false,
            include: [
              {
                model: PaymentTransaction,
                as: 'lastTransaction',
              },
            ],
          },
        ],
      },
    ];

    const findAllOptions = {
      includeParams: includeOptions,
      useCache: true,
    };

    const partnerships = await this.partnershipsService.findAll(
      queryOptions,
      findAllOptions,
    );

    for (const partnership of partnerships) {
      partnership.partnershipRequestData = partnership.partnershipRequest;

      if (partnership.partnershipRequest.subscription) {
        // @ts-ignore
        partnership?.partnershipRequestData?.subscriptionData = {
          subscriptionId: partnership?.partnershipRequest?.subscription?.id,
          currency: partnership?.partnershipRequest?.subscription?.currency,
          price: partnership?.partnershipRequest?.subscription?.price,
          duration: partnership?.partnershipRequest?.subscription?.duration,
          status: partnership?.partnershipRequest?.subscription?.status,
          lastTransaction: partnership?.partnershipRequest?.subscription
            ?.lastTransaction
            ? partnership?.partnershipRequest?.subscription?.lastTransaction
                ?.startDate
            : '',
          endDate: partnership?.partnershipRequest?.subscription?.endDate,
          canceledDate:
            partnership?.partnershipRequest?.subscription?.canceledDate,
        };
      }
    }

    return partnerships;
  }

  @ResolveField('receivedPartnershipRequests', () => [PartnershipRequest])
  async receivedPartnershipRequests(
    @Parent() organisation: Organisation,
  ): Promise<PartnershipRequest[]> {
    this.logger.verbose(
      'OrganisationsResolver.receivedPartnershipRequests (field resolver)',
      {
        organisation,
      },
    );

    const partnershipRequests = await this.partnershipRequestsService.findAll(
      {
        receiverOrganisationId: organisation.id,
        status: PartnershipRequestStatus.Pending,
      },
      { useCache: true },
    );

    for (const partnershipRequest of partnershipRequests) {
      const subscription = await this.subscriptionsService.findById(
        partnershipRequest.subscriptionId,
      );

      if (subscription) {
        const paymentTransaction =
          await this.paymentTransactionsService.findById(
            subscription.lastTransactionId,
          );

        partnershipRequest.subscriptionData = {
          subscriptionId: subscription?.id,
          currency: subscription?.currency,
          price: subscription?.price,
          duration: subscription?.duration,
          status: subscription?.status,
          stripeCheckoutId: subscription?.stripeCheckoutId,
          lastTransaction: paymentTransaction
            ? paymentTransaction.startDate
            : '',
          endDate: subscription?.endDate,
          canceledDate: subscription?.canceledDate,
        };
      }
    }

    return partnershipRequests;
  }

  @ResolveField('sentPartnershipRequests', () => [PartnershipRequest])
  async sentPartnershipRequests(
    @Parent() organisation: Organisation,
  ): Promise<PartnershipRequest[]> {
    this.logger.verbose(
      'OrganisationsResolver.sentPartnershipRequests (field resolver)',
      {
        organisation,
      },
    );

    const partnershipRequests = await this.partnershipRequestsService.findAll(
      {
        senderOrganisationId: organisation.id,
        status: PartnershipRequestStatus.Pending,
      },
      { useCache: true },
    );

    for (const partnershipRequest of partnershipRequests) {
      const subscription = await this.subscriptionsService.findById(
        partnershipRequest.subscriptionId,
      );

      if (subscription) {
        const paymentTransaction =
          await this.paymentTransactionsService.findById(
            subscription.lastTransactionId,
          );

        partnershipRequest.subscriptionData = {
          subscriptionId: subscription?.id,
          currency: subscription?.currency,
          price: subscription?.price,
          duration: subscription?.duration,
          status:
            subscription?.status === SubscriptionStatus.Pending
              ? 'Awaiting Response'
              : subscription?.status,
          stripeCheckoutId: subscription?.stripeCheckoutId,
          lastTransaction: paymentTransaction
            ? paymentTransaction.startDate
            : '',
          endDate: subscription?.endDate,
          canceledDate: subscription?.canceledDate,
        };
      }
    }

    return partnershipRequests;
  }

  @ResolveField('followerStatus', () => FollowerStatus, { nullable: true })
  async followerStatus(
    @CurrentUser() user: ICurrentUser,
    @Parent() organisation: Organisation,
  ): Promise<FollowerStatus> {
    const follower = await this.followersService.findOne(
      {
        organisationId: organisation.id,
        profileId: user.profileId,
      },
      { useCache: true },
    );

    return follower?.status;
  }

  private getMembership(
    organisationId: string,
    profileId: string,
  ): Promise<Membership> {
    return this.membershipsService.findOne(
      {
        organisationId,
        profileId,
        status: MembershipStatus.Active,
      },
      { useCache: true },
    );
  }

  @ResolveField('isMember', () => Boolean, { nullable: true })
  async isMember(
    @CurrentUser() user: ICurrentUser,
    @Parent() organisation: Organisation,
  ): Promise<boolean> {
    return !!(await this.getMembership(organisation.id, user.profileId));
  }

  @ResolveField('permissions', () => [MembershipPermission], { nullable: true })
  async permissions(
    @CurrentUser() user: ICurrentUser,
    @Parent() organisation: Organisation,
  ): Promise<MembershipPermission[]> {
    const membership = await this.getMembership(
      organisation.id,
      user.profileId,
    );

    return membership?.permissions;
  }

  @ResolveField('isOwner', () => Boolean, { nullable: true })
  async isOwner(
    @CurrentUser() user: ICurrentUser,
    @Parent() organisation: Organisation,
  ): Promise<boolean> {
    const membership = await this.getMembership(
      organisation.id,
      user.profileId,
    );

    return membership?.permissions?.includes(MembershipPermission.Owner);
  }

  @ResolveField('activeMembership', () => Membership, { nullable: true })
  async activeMembership(
    @CurrentUser() user: ICurrentUser,
    @Parent() organisation: Organisation,
  ): Promise<Membership> {
    return this.getMembership(organisation.id, user.profileId);
  }

  @ResolveField('isAdmin', () => Boolean, { nullable: true })
  async isAdmin(
    @CurrentUser() user: ICurrentUser,
    @Parent() organisation: Organisation,
  ): Promise<boolean> {
    const membership = await this.getMembership(
      organisation.id,
      user.profileId,
    );

    return (
      membership?.permissions?.includes(MembershipPermission.Admin) ||
      membership?.permissions?.includes(MembershipPermission.HiddenAdmin)
    );
  }

  @ResolveField('isManager', () => Boolean, { nullable: true })
  async isManager(
    @CurrentUser() user: ICurrentUser,
    @Parent() organisation: Organisation,
  ): Promise<boolean> {
    const membership = await this.getMembership(
      organisation.id,
      user.profileId,
    );

    return membership?.permissions?.includes(MembershipPermission.Manager);
  }

  @ResolveField('isEditor', () => Boolean, { nullable: true })
  async isEditor(
    @CurrentUser() user: ICurrentUser,
    @Parent() organisation: Organisation,
  ): Promise<boolean> {
    const membership = await this.getMembership(
      organisation.id,
      user.profileId,
    );

    return membership?.permissions?.includes(MembershipPermission.Editor);
  }

  @ResolveField('followersActiveCount', () => Int, { nullable: true })
  async followersActiveCount(
    @Parent() organisation: Organisation,
  ): Promise<number> {
    return this.followersService.count({
      organisationId: organisation.id,
      status: FollowerStatus.Active,
    });
  }

  @ResolveField('followersPendingCount', () => Int, { nullable: true })
  async followersPendingCount(
    @Parent() organisation: Organisation,
  ): Promise<number> {
    return this.followersService.count({
      organisationId: organisation.id,
      status: FollowerStatus.Pending,
    });
  }

  @ResolveField('followersRejectedCount', () => Int, { nullable: true })
  async followersRejectedCount(
    @Parent() organisation: Organisation,
  ): Promise<number> {
    return this.followersService.count({
      organisationId: organisation.id,
      status: FollowerStatus.Rejected,
    });
  }

  @ResolveField('webinarsCount', () => Int, { nullable: true })
  async webinarsCount(@Parent() organisation: Organisation): Promise<number> {
    return this.webinarsService.count({
      organisationId: organisation.id,
    });
  }

  @ResolveField('eventsCount', () => Int, { nullable: true })
  async eventsCount(@Parent() organisation: Organisation): Promise<number> {
    return this.eventsService.count({
      organisationId: organisation.id,
      type: EventType.Event,
    });
  }

  @ResolveField('referralCount', () => Int, { nullable: true })
  async referralCount(@Parent() organisation: Organisation): Promise<number> {
    return this.organisationsService.getReferralCount(organisation.id);
  }

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  async findConnectAccount(
    @CurrentUser() user: ICurrentUser,
    @Args('organisationId') organisationId: string,
  ): Promise<boolean> {
    this.logger.verbose('OrganisationsResolver.findConnectAccount (mutation)', {
      organisationId,
    });

    const connectAccount = await this.organisationsService.findConnectAccount(
      organisationId,
      {
        currentUser: user,
      },
    );

    if (
      connectAccount.stripeConnectAccount !== null ||
      connectAccount.stripeConnectAccount !== undefined ||
      connectAccount.stripeConnectAccount !== ''
    ) {
      return true;
    } else {
      return false;
    }
  }

  @Mutation(returns => String)
  @UseGuards(GqlAuthGuard)
  async createConnectAccount(
    @CurrentUser() user: ICurrentUser,
    @Args('organisationId') organisationId: string,
    @Args('email') email: string,
  ): Promise<string> {
    this.logger.verbose(
      'OrganisationsResolver.createConnectAccount (mutation)',
      {
        organisationId,
        email,
      },
    );

    return await this.organisationsService.createConnectAccount(
      organisationId,
      email,
      {
        currentUser: user,
      },
    );
  }

  @Mutation(returns => String)
  @UseGuards(GqlAuthGuard)
  async retrieveConnectAccount(
    @CurrentUser() user: ICurrentUser,
    @Args('organisationId') organisationId: string,
  ): Promise<string> {
    this.logger.verbose(
      'OrganisationsResolver.retrieveConnectAccount (mutation)',
      {
        organisationId,
      },
    );

    return await this.organisationsService.retrieveConnectAccount(
      organisationId,
      {
        currentUser: user,
      },
    );
  }

  @Mutation(returns => String)
  @UseGuards(GqlAuthGuard)
  async getCustomerPortal(
    @CurrentUser() user: ICurrentUser,
    @Args('partnershipId') partnershipId: string,
    @Args('organisationId') organisationId: string,
  ): Promise<string> {
    this.logger.verbose('OrganisationsResolver.getCustomerPortal (mutation)', {
      partnershipId,
      organisationId,
    });

    return await this.organisationsService.getCustomerPortal(
      partnershipId,
      organisationId,
      {
        currentUser: user,
      },
    );
  }

  @Mutation(returns => String)
  @UseGuards(GqlAuthGuard)
  async getStripeExpressLink(
    @CurrentUser() user: ICurrentUser,
    @Args('organisationId') organisationId: string,
  ): Promise<string> {
    this.logger.verbose(
      'OrganisationsResolver.getStripeExpressLink (mutation)',
      {
        organisationId,
      },
    );

    return await this.organisationsService.getStripeExpressLink(
      organisationId,
      {
        currentUser: user,
      },
    );
  }
  @Mutation(returns => Boolean)
  @UseGuards(GqlAuthGuard)
  async addPreApprovedDomains(
    @CurrentUser() user: ICurrentUser,
    @Args('data') data: AddPreApprovedDomainInput,
  ): Promise<boolean> {
    this.logger.verbose(
      'OrganisationsResolver.addPreApprovedDomains (mutation)',
      {
        data,
      },
    );

    await this.organisationsService.addPreApprovedDomains(data, {
      currentUser: user,
    });

    return true;
  }

  @Mutation(returns => Boolean)
  @UseGuards(GqlAuthGuard)
  async removePreApprovedDomains(
    @CurrentUser() user: ICurrentUser,
    @Args('data') data: AddPreApprovedDomainInput,
  ): Promise<boolean> {
    this.logger.verbose(
      'OrganisationsResolver.removePreApprovedDomains (mutation)',
      {
        data,
      },
    );

    await this.organisationsService.removePreApprovedDomains(data, {
      currentUser: user,
    });

    return true;
  }

  @Mutation(() => Organisation)
  @UseGuards(GqlAuthGuard)
  async addParentOrganisation(
    @CurrentUser() user: ICurrentUser,
    @Args('data') data: ParentOrganisationInput,
  ): Promise<Organisation> {
    this.logger.verbose(
      'OrganisationsResolver.addParentOrganisation (mutation)',
      {
        data,
      },
    );

    return await this.organisationsService.addParentOrganisation(data, {
      currentUser: user,
    });
  }

  @Mutation(() => Organisation)
  @UseGuards(GqlAuthGuard)
  async removeParentOrganisation(
    @CurrentUser() user: ICurrentUser,
    @Args('data') data: ParentOrganisationInput,
  ): Promise<Organisation> {
    this.logger.verbose(
      'OrganisationsResolver.removeParentOrganisation (mutation)',
      {
        data,
      },
    );

    return await this.organisationsService.removeParentOrganisation(data, {
      currentUser: user,
    });
  }

  @ResolveField('activeTier', () => String, { nullable: true })
  async activeTier(@Parent() organisation: Organisation): Promise<string | null> {
    if (organisation.hasClubHabloSubscription !== true) {
      return null;
    }

    const organisationLoyaltyPoint =
      await this.organisationLoyaltyPointsService.findOneByOrganisationId(
        organisation.id,
      );
    return this.organisationLoyaltyPointsService.getTierFromIndex(
      organisationLoyaltyPoint?.activeTier ?? 0,
    );
  }

  @Query(() => [Organisation])
  async getClubHabloSubscriptionOrganisations(
    @Args('profileId') profileId: string,
  ): Promise<Organisation[]> {
    const organisationsAcivements =
      await this.organisationsService.getClubHabloSubscriptionOrganisations(
        profileId,
      );
    return organisationsAcivements;
  }

  @Query(() => [Organisation])
  @UseGuards(GqlAuthGuard)
  async getHighFiveOrganisations(
    @CurrentUser() user: ICurrentUser,
    @Args('profileId') profileId: string,
  ): Promise<Organisation[]> {
    this.logger.verbose(
      'AchievementsResolver.getHighFiveOrganisations (query)',
      {
        user: user.toLogObject(),
      },
    );
    return await this.organisationsService.getHighFiveOrganisations(
      user.profileId,
      profileId,
    );
  }
}
