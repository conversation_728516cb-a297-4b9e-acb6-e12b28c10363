import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { Organisation } from './models/organisation.model';
import { OrganisationsRepository } from './organisations.repository';
import { CreateOrganisationDto } from './dto/create-organisation.dto';
import { Transaction } from 'sequelize/types';
import { Sequelize } from 'sequelize-typescript';
import { Op } from 'sequelize';

import { Underscore as _ } from '../common/helpers/underscore';
import { ICurrentUser } from '../common/decorators/current-user.decorator';
import { MembershipsService } from '../memberships/memberships.service';
import {
  MembershipPermission,
  Membership,
  MembershipStatus,
} from '../memberships/models/membership.model';
import { PaginatedResult } from '../common/args/paginated-result';
import { PaginationArgs } from '../common/args/pagination.args';
import {
  OrganisationType,
  OrganisationStatus,
  Privacy,
} from './args/organisations.args';
import { UpdateOrganisationInput } from './dto/update-organisation.input';
import { FollowersService } from '../followers/followers.service';
import { BaseService } from '../common/base.service';
import { ErrorHelper } from '../common/helpers/error';
import {
  MembershipsServiceRights,
  MembershipRight,
} from '../memberships/helpers/memberships.service.rights';
import { NotificationsService } from '../notifications/notifications.service';
import {
  NotificationMessage,
  NotificationType,
} from '../notifications/args/notifications.args';
import { ProfilesService } from '../profiles/profiles.service';
import { EventInvitationsService } from '../event-invitations/event-invitations.service';
import { EventsService } from '../events/events.service';
import { WebinarParticipantsService } from '../webinar-participants/webinar-participants.service';
import { WebinarsService } from '../webinars/webinars.service';
import { PartnershipRequestsService } from '../partnership-requests/partnership-requests.service';
import { PartnershipsService } from '../partnerships/partnerships.service';
import { IncentivesService } from '../incentives/incentives.service';
import { IncentiveParticipantsService } from '../incentive-participants/incentive-participants.service';
import { ExperiencesService } from '../experiences/experiences.service';
import { StreamOrganisationsService } from '../feeds-organisations/stream-organisations.service';
import {
  Follower,
  FollowerStatus,
  FollowerActionType,
} from '../followers/models/follower.model';
import { now } from 'lodash';
import { StripeHelper } from './helpers/stripe';
import { SubscriptionsService } from '../subscriptions/subscriptions.service';
import { AddPreApprovedDomainInput } from './dto/add-pre-approved-domains.input';
import { ParentOrganisationInput } from './dto/add-parent-organisation';
import { StreamFollowersService } from '../feeds-followers/stream-followers.service';
import { PartnershipRequestStatus } from '../partnership-requests/models/partnership-request.model';
import {
  Routes,
  encodePathParams,
} from '../email/helpers/notificationLayoutHelper';
import { AchievementType } from '../achievements/args/achievements.args';

@Injectable()
export class OrganisationsService extends BaseService(Organisation) {
  @Inject(forwardRef(() => OrganisationsRepository))
  private readonly organisationsRepository: OrganisationsRepository;
  @Inject(forwardRef(() => MembershipsService))
  private readonly membershipsService: MembershipsService;
  @Inject(forwardRef(() => MembershipsServiceRights))
  private readonly membershipsServiceRights: MembershipsServiceRights;
  @Inject(forwardRef(() => NotificationsService))
  private readonly notificationsService: NotificationsService;
  @Inject(forwardRef(() => FollowersService))
  private readonly followersService: FollowersService;
  @Inject(forwardRef(() => ProfilesService))
  private readonly profilesService: ProfilesService;
  @Inject(forwardRef(() => EventsService))
  private readonly eventsService: EventsService;
  @Inject(forwardRef(() => EventInvitationsService))
  private readonly eventInvitationsService: EventInvitationsService;
  @Inject(forwardRef(() => WebinarParticipantsService))
  private readonly webinarParticipantsService: WebinarParticipantsService;
  @Inject(forwardRef(() => WebinarsService))
  private readonly webinarsService: WebinarsService;
  @Inject(forwardRef(() => IncentivesService))
  private readonly incentivesService: IncentivesService;
  @Inject(forwardRef(() => IncentiveParticipantsService))
  private readonly incentiveParticipantsService: IncentiveParticipantsService;
  @Inject(forwardRef(() => PartnershipsService))
  private readonly partnershipsService: PartnershipsService;
  @Inject(forwardRef(() => PartnershipRequestsService))
  private readonly partnershipRequestsService: PartnershipRequestsService;
  @Inject(forwardRef(() => ExperiencesService))
  private readonly experiencesService: ExperiencesService;
  @Inject(forwardRef(() => StreamOrganisationsService))
  private readonly streamOrgService: StreamOrganisationsService;
  @Inject(forwardRef(() => SubscriptionsService))
  private readonly subscriptionsService: SubscriptionsService;
  @Inject(forwardRef(() => StreamFollowersService))
  private readonly streamFollowersService: StreamFollowersService;
  @Inject()
  private readonly sequelize: Sequelize;
  @Inject(WINSTON_MODULE_PROVIDER)
  private readonly logger: Logger;
  @Inject()
  private readonly errorHelper: ErrorHelper;
  @Inject()
  private readonly stripeHelper: StripeHelper;

  async createOrganisation(
    user: ICurrentUser,
    createOrganisationDto: CreateOrganisationDto,
    options?: {
      transaction?: Transaction;
    },
  ): Promise<Organisation> {
    this.logger.info('OrganisationsService.createOrganisation', {
      user,
      createOrganisationDto,
    });

    if (createOrganisationDto.vanityId) {
      if (
        await this.isVanityIdExists(
          createOrganisationDto.vanityId,
          options?.transaction,
        )
      ) {
        this.errorHelper.throwHttpException(
          `OrganisationsService.createOrganisation`,
          `Custom URL unavailable`,
        );
      }
    }

    if (!createOrganisationDto.status) {
      createOrganisationDto.status = OrganisationStatus.Active;
    }

    const transaction =
      options?.transaction || (await this.sequelize.transaction());

    try {
      if (
        createOrganisationDto.type === OrganisationType.TravelAgency &&
        !createOrganisationDto.privacy
      ) {
        createOrganisationDto.privacy = Privacy.Private;
      }
      const organisation: Organisation = await this.create(
        createOrganisationDto,
        {
          transaction,
        },
      );

      const membership = await this.membershipsService.create(
        {
          organisationId: organisation.id,
          profileId: user.profileId,
          permissions: [MembershipPermission.Owner],
          status: MembershipStatus.Active,
        },
        {
          transaction,
        },
      );

      const profile = await this.profilesService.findById(user.profileId, {
        transaction,
      });

      if (!profile.primaryMembershipId) {
        await this.profilesService.updateById(
          profile.id,
          {
            primaryMembershipId: membership.id,
          },
          { transaction },
        );

        await this.membershipsService.updateById(
          membership.id,
          { isPrimary: true },
          { transaction },
        );
      }

      await this.followersService.addOrApproveFollower(
        organisation.id,
        user.profileId,
        { transaction },
      );

      if (this.streamFollowersService.client) {
        await this.streamFollowersService.updatePrimaryOrganisation(
          user.profileId,
          null,
          organisation.id,
        );
      }

      await transaction.commit();

      return organisation;
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `OrganisationsService.createOrganisation`,
        e.message,
      );
    }
  }

  //to do: create separate UpdateOrganisationPage method for updating basic fields (logo, bg image, about page)

  async updateOrganisation(
    id: string,
    updateOrganisationInput: UpdateOrganisationInput,
    options: {
      profileId?: string;
    },
  ): Promise<Organisation> {
    this.logger.info('OrganisationsService.updateOrganisation', {
      id,
      updateOrganisationInput,
      profileId: options.profileId,
    });

    await this.membershipsServiceRights.checkRights(id, {
      currentProfileId: options.profileId,
      rights: [MembershipRight.UpdateOrganisation],
    });

    const organisation = await this.findById(id, {
      includeParams: [
        {
          model: Membership,
          as: 'memberships',
          where: {
            profileId: options.profileId,
          },
        },
      ],
    });

    if (
      updateOrganisationInput.vanityId &&
      updateOrganisationInput.vanityId !== organisation.vanityId
    ) {
      if (await this.isVanityIdExists(updateOrganisationInput.vanityId)) {
        this.errorHelper.throwHttpException(
          `OrganisationsService.updateOrganisation`,
          `Custom URL unavailable`,
        );
      }
    }

    updateOrganisationInput.description &&
      (updateOrganisationInput.description =
        updateOrganisationInput.description.replace(/\n\s*\n\s*\n/g, '\n\n'));

    const updatedOrganisation = this.updateById(
      organisation.id,
      updateOrganisationInput,
    );

    this.streamOrgService.updateOrganisation(id, updateOrganisationInput, {
      profileId: options.profileId,
    });

    if (updateOrganisationInput.privacy === 'public') {
      await this.followersService.acceptAllPendingFollowers(
        organisation,
        null,
        { currentProfileId: options.profileId },
      );
    }

    return updatedOrganisation;
  }

  async removeOrganisation(
    id: string,
    options: {
      profileId: string;
      transaction?: Transaction;
    },
  ): Promise<void> {
    this.logger.info('OrganisationsService.removeOrganisation', {
      id,
      profileId: options.profileId,
    });

    await this.membershipsServiceRights.checkRights(id, {
      currentProfileId: options.profileId,
      rights: [MembershipRight.RemoveOrganisation],
      transaction: options.transaction,
    });

    const organisation = await this.findById(id, {
      includeParams: [
        {
          model: Membership,
          as: 'memberships',
          where: {
            profileId: options.profileId,
          },
        },
      ],
      transaction: options.transaction,
    });

    if (!organisation) {
      this.errorHelper.throwHttpException(
        `OrganisationsService.removeOrganisation`,
        `Organisation can't be removed by the current user`,
      );
    }

    const membership = await this.membershipsService.findOne(
      {
        organisationId: id,
        profileId: options.profileId,
        status: MembershipStatus.Active,
      },
      { transaction: options.transaction },
    );

    const isOwner = membership.permissions.includes(MembershipPermission.Owner);

    if (!isOwner) {
      this.errorHelper.throwHttpException(
        `OrganisationsService.removeOrganisation`,
        `Organisation can't be removed by the current user (needs Owner permission)`,
      );
    }

    const transaction =
      options.transaction || (await this.sequelize.transaction());

    try {
      await this.membershipsService.remove({
        where: { organisationId: organisation.id },
        transaction,
      });
      await this.followersService.remove({
        where: { organisationId: organisation.id },
        transaction,
      });

      await this.removeById(organisation.id, { transaction });

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `OrganisationsService.removeOrganisation`,
        e.message,
      );
    }
  }

  async adminPanelRemoveOrganisation(
    organisationId: string,
    options: {
      profileId: string;
      transaction?: Transaction;
    },
  ): Promise<void> {
    this.logger.info('OrganisationsService.adminPanelRemoveOrganisation', {
      organisationId,
      profileId: options.profileId,
    });

    const habloSuperAdminUserIDs = [
      '5f5b44fc4ac650006fc9505c',
      '111149247624107403444',
    ];
    if (habloSuperAdminUserIDs.indexOf(options.profileId) === -1) {
      this.errorHelper.throwHttpException(
        `OrganisationsService.adminPanelRemoveOrganisation`,
        `Current user does not have admin privileges`,
      );
      return;
    }

    const organisation = await this.findById(organisationId, {
      transaction: options.transaction,
    });

    if (!organisation) {
      this.errorHelper.throwHttpException(
        `OrganisationsService.adminPanelRemoveOrganisation`,
        `Organisation doesn't exist`,
      );
    }

    if (organisation.status === OrganisationStatus.Removed) {
      this.errorHelper.throwHttpException(
        `OrganisationsService.adminPanelRemoveOrganisation`,
        `Organisation already removed`,
      );
    }

    const transaction =
      options.transaction || (await this.sequelize.transaction());

    try {
      await this.updateById(
        organisation.id,
        {
          name: '(deleted organisation)',
          image: '',
          status: OrganisationStatus.Removed,
          vanityId: '-removed!-' + organisationId,
        },
        { transaction },
      );

      const memberships = await this.membershipsService.findAll(
        {
          organisationId: organisation.id,
        },
        { transaction },
      );

      // inactivate all memberships at removed org

      for (const membership of memberships) {
        if (membership.isPrimary) {
          await this.membershipsService.updateById(
            membership.id,
            {
              status: MembershipStatus.Inactive,
              isPrimary: false,
            },
            { transaction },
          );
          const newPrimaryMembership = await this.membershipsService.create(
            {
              isPrimary: true,
              organisationName: organisation.name,
              profileId: membership.profileId,
              status: MembershipStatus.Active,
              permissions: [],
              position: membership.position,
            },
            { transaction },
          );
          await this.profilesService.updateById(
            newPrimaryMembership.profileId,
            {
              primaryMembershipId: newPrimaryMembership.id,
            },
            { transaction },
          );
        } else {
          await this.membershipsService.updateById(
            membership.id,
            {
              status: MembershipStatus.Inactive,
            },
            { transaction },
          );
        }
        // remove all notifications for inactivated memberships
        await this.notificationsService.remove({
          where: {
            membershipId: membership.id,
          },
          transaction,
        });
      }

      // remove all notifications for events from removed org [but preserve events]
      const events = await this.eventsService.findAll(
        {
          organisationId: organisation.id,
        },
        { transaction },
      );
      for (const event of events) {
        this.logger.info('looking up event:', {
          event,
        });
        const eventInvitations = await this.eventInvitationsService.findAll(
          {
            eventId: event.id,
          },
          { transaction },
        );
        for (const eventInvitation of eventInvitations) {
          await this.notificationsService.remove({
            where: {
              eventInvitationId: eventInvitation.id,
            },
            transaction,
          });
        }
      }

      // remove all notifications for webinars from removed org [but preserve webinars]
      const webinars = await this.webinarsService.findAll(
        {
          organisationId: organisation.id,
        },
        { transaction },
      );
      for (const webinar of webinars) {
        const webinarParticipants =
          await this.webinarParticipantsService.findAll(
            {
              webinarId: webinar.id,
            },
            { transaction },
          );
        for (const webinarParticipant of webinarParticipants) {
          await this.notificationsService.remove({
            where: {
              webinarParticipantId: webinarParticipant.id,
            },
            transaction,
          });
        }
      }

      // remove all notifications for incentives from removed org [but preserve webinars]
      const incentives = await this.incentivesService.findAll(
        {
          organisationId: organisation.id,
        },
        { transaction },
      );

      for (const incentive of incentives) {
        const incentiveParticipants =
          await this.incentiveParticipantsService.findAll(
            {
              incentiveId: incentive.id,
            },
            { transaction },
          );
        for (const incentiveParticipant of incentiveParticipants) {
          await this.notificationsService.remove({
            where: {
              incentiveParticipantId: incentiveParticipant.id,
            },
            transaction,
          });
        }
      }

      const now = Date.now();
      this.incentivesService.update({
        where: {
          organisationId: organisation.id,
          endDate: { [Op.gt]: now },
        },
        update: {
          endDate: now,
        },
        transaction,
      });

      // update experiences to remove org id and replace with org name text
      const experiences = await this.experiencesService.findAll(
        {
          organisationId: organisation.id,
        },
        { transaction },
      );

      for (const experience of experiences) {
        await this.experiencesService.updateById(
          experience.id,
          {
            organisationId: null,
            organisationName: organisation.name,
          },
          { transaction },
        );
      }

      //remove all partnership requests (which removes partnerships), and associated notifications
      const partnershipRequests = await this.partnershipRequestsService.findAll(
        {
          [Op.or]: [
            { senderOrganisationId: organisation.id },
            { receiverOrganisationId: organisation.id },
          ],
        },
        { transaction },
      );
      for (const partnershipRequest of partnershipRequests) {
        await this.partnershipRequestsService.removeById(
          partnershipRequest.id,
          {
            transaction,
          },
        );
        await this.notificationsService.remove({
          where: {
            partnershipRequestId: partnershipRequest.id,
          },
          transaction,
        });
      }

      //remove notifications related to organisation
      await this.notificationsService.remove({
        where: {
          organisationId: organisation.id,
        },
        transaction,
      });

      const followers = await this.followersService.findAll({
        organisationId: organisation.id,
        status: { [Op.notLike]: FollowerStatus.Inactive },
      });

      for (const follower of followers) {
        this.followersService.updateFollowerStatus(
          organisationId,
          follower.profileId,
          FollowerActionType.Inactivate,
          {
            currentProfileId: options.profileId,
          },
        );
        await this.notificationsService.remove({
          where: {
            followerId: follower.id,
          },
          transaction,
        });
      }

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `OrganisationsService.adminPanelRemoveOrganisation`,
        e.message,
      );
    }
  }

  async changeOrganisationOwnership(
    organisationId: string,
    newOwnerProfileId: string,
    options: {
      currentUser: ICurrentUser;
    },
  ): Promise<void> {
    this.logger.info('OrganisationsService.changeOrganisationOwnership', {
      organisationId,
      currentUser: options.currentUser.toLogObject(),
    });

    await this.membershipsServiceRights.checkRights(organisationId, {
      currentProfileId: options.currentUser.profileId,
      rights: [MembershipRight.ChangeOwner],
    });

    let newOwnerMembership = await this.membershipsService.findOne({
      organisationId,
      profileId: newOwnerProfileId,
    });

    const transaction = await this.sequelize.transaction();

    try {
      if (newOwnerMembership) {
        await this.membershipsService.updateById(
          newOwnerMembership.id,
          {
            permissions: _.uniq([
              ...newOwnerMembership.permissions,
              MembershipPermission.OwnerPending,
            ]),
            status: MembershipStatus.Active,
          },
          { transaction },
        );
      } else {
        newOwnerMembership = await this.membershipsService.create(
          {
            organisationId,
            profileId: newOwnerProfileId,
            permissions: [MembershipPermission.OwnerPending],
            status: MembershipStatus.Active,
          },
          { transaction },
        );
      }

      await this.notificationsService.createNotification(
        {
          ownerProfileId: newOwnerProfileId,
          profileId: options.currentUser.profileId,
          membershipId: newOwnerMembership.id,
          organisationId: newOwnerMembership.organisationId,
          type: NotificationType.OrganisationOwnershipRequested,
        },
        {
          transaction,
        },
      );

      await transaction.commit();

      const profileIds = [newOwnerProfileId];
      const replacementOrg = await this.findById(organisationId);
      const replacements = [options.currentUser.name, replacementOrg.name];

      await this.notificationsService.sendPushNotification({
        profileIds,
        replacements,
        messageType: NotificationMessage.OrganisationOwnershipRequested,
        route: encodePathParams(Routes.organisationSettingsUserRoles, {
          organisationId: newOwnerMembership.organisationId,
        }),
      });
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `OrganisationsService.changeOrganisationOwnership`,
        e.message,
      );
    }
  }

  async acceptOrganisationOwnership(
    organisationId: string,
    options: {
      currentUser: ICurrentUser;
    },
  ): Promise<void> {
    this.logger.info('OrganisationsService.acceptOrganisationOwnership', {
      organisationId,
      currentUser: options.currentUser.toLogObject(),
    });

    const transaction = await this.sequelize.transaction();

    try {
      const newOwnerMembership = await this.membershipsService.findOne(
        {
          organisationId,
          profileId: options.currentUser.profileId,
          permissions: {
            [Op.contains]: [MembershipPermission.OwnerPending],
          },
        },
        { transaction },
      );

      if (!newOwnerMembership) {
        throw new Error('OwnerPending status not found');
      }

      const previousOwnerMembership = await this.membershipsService.findOne(
        {
          organisationId,
          status: MembershipStatus.Active,
          permissions: {
            [Op.contains]: [MembershipPermission.Owner],
          },
        },
        { transaction },
      );

      if (!previousOwnerMembership) {
        throw new Error('Active Owner not found');
      }

      await this.membershipsService.updateById(
        previousOwnerMembership.id,
        {
          permissions: [MembershipPermission.Admin],
        },
        { transaction },
      );

      await this.membershipsService.updateById(
        newOwnerMembership.id,
        {
          permissions: [MembershipPermission.Owner],
        },
        { transaction },
      );

      await this.notificationsService.createNotification(
        {
          ownerProfileId: previousOwnerMembership.profileId,
          profileId: options.currentUser.profileId,
          membershipId: newOwnerMembership.id,
          type: NotificationType.OrganisationOwnershipAccepted,
        },
        {
          transaction,
        },
      );

      await transaction.commit();

      const profileIds = [previousOwnerMembership.profileId];
      const replacementOrg = await this.findById(organisationId);
      const replacements = [options.currentUser.name, replacementOrg.name];

      await this.notificationsService.sendPushNotification({
        profileIds,
        replacements,
        messageType: NotificationMessage.OrganisationOwnershipAccepted,
        route: encodePathParams(Routes.organisationSettingsUserRoles, {
          organisationId: replacementOrg?.id,
        }),
      });
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `OrganisationsService.acceptOrganisationOwnership`,
        e.message,
      );
    }
  }

  async rejectOrganisationOwnership(
    organisationId: string,
    options: {
      currentUser: ICurrentUser;
    },
  ): Promise<void> {
    this.logger.info('OrganisationsService.rejectOrganisationOwnership', {
      organisationId,
      currentUser: options.currentUser.toLogObject(),
    });

    const transaction = await this.sequelize.transaction();

    try {
      const newOwnerMembership = await this.membershipsService.findOne(
        {
          organisationId,
          profileId: options.currentUser.profileId,
          permissions: {
            [Op.contains]: [MembershipPermission.OwnerPending],
          },
        },
        { transaction },
      );

      if (!newOwnerMembership) {
        throw new Error('OwnerPending status not found');
      }

      const newPermissions = newOwnerMembership.permissions.filter(
        permission => permission !== MembershipPermission.OwnerPending,
      );

      if (newPermissions.length > 0) {
        await this.membershipsService.updateById(
          newOwnerMembership.id,
          {
            permissions: newPermissions,
          },
          { transaction },
        );
      } else {
        await this.membershipsService.removeById(newOwnerMembership.id, {
          transaction,
        });
      }

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `OrganisationsService.rejectOrganisationOwnership`,
        e.message,
      );
    }
  }

  async findOrganisations(
    profileId: string,
    filter: {
      id?: string;
      type?: [OrganisationType];
      searchText?: string;
      vanityId?: string[];
      isFuzzySearch?: boolean;
      hasClubHabloSubscription?: boolean;
    },
    pagination: PaginationArgs,
  ): Promise<PaginatedResult<Organisation>> {
    this.logger.verbose('OrganisationsService.findOrganisations', {
      profileId,
      filter,
      pagination,
    });

    const paginationOrganisationResult =
      await this.organisationsRepository.findOrganisations(
        profileId,
        filter,
        pagination,
      );

    return {
      records: paginationOrganisationResult.records,
      totalCount: paginationOrganisationResult.totalCount,
    };
  }

  async isVanityIdExists(
    vanityId: string,
    transaction?: Transaction,
  ): Promise<boolean> {
    const organisation = await this.findOne(
      Sequelize.where(
        Sequelize.fn('lower', Sequelize.col('vanityId')),
        Sequelize.fn('lower', vanityId),
      ),
      {
        transaction,
      },
    );
    const reservedURLS = [
      'profile',
      'organisation',
      'inbox',
      'calendar',
      'home',
      'search',
      'incentives',
      'connect',
      'habloadmin',
      'explore',
      'search',
      'signup',
      'login',
      'logout',
      'auth',
      'networkerror',
      'dailyquiz',
      'home',
      'loyalty',
      'rewards',
      'dashboard',
      'post',
    ];

    const isReservedURL = reservedURLS.indexOf(vanityId.toLowerCase()) > -1;
    const containsHablo = vanityId.toLowerCase().indexOf('hablo') > -1;

    return !!organisation || isReservedURL || containsHablo;
  }

  async getOrganisationId(
    profileId: string,
    includeRemoved?: boolean,
    withoutMemberShip?: boolean,
  ) {
    const queryParams: any = {};

    if (includeRemoved !== true) {
      queryParams['status'] = {
        [Op.in]: [OrganisationStatus.Active],
      };
    }

    const organisationsFollowed = await this.findAll(queryParams, {
      attributes: ['id', 'name'],
      includeParams: [
        {
          model: Follower,
          as: 'followers',
          where: {
            profileId,
            status: FollowerStatus.Active,
          },
        },
      ],
    });

    if (withoutMemberShip !== true) {
      const organisationsWithMembership = await this.findAll(queryParams, {
        attributes: ['id', 'name'],
        includeParams: [
          {
            model: Membership,
            as: 'memberships',
            where: {
              profileId,
              status: MembershipStatus.Active,
            },
          },
        ],
      });

      return {
        [Op.in]: _.uniq([
          ..._.map(organisationsWithMembership, 'id'),
          ..._.map(organisationsFollowed, 'id'),
        ]),
      };
    } else {
      return {
        [Op.in]: [..._.map(organisationsFollowed, 'id')],
      };
    }
  }

  async findConnectAccount(
    organisationId: string,
    options: {
      currentUser: ICurrentUser;
    },
  ): Promise<any> {
    this.logger.info('OrganisationsService.findConnectAccount', {
      organisationId,
      currentUser: options.currentUser.toLogObject(),
    });

    try {
      const connectAccount = await this.findById(organisationId);

      if (!connectAccount) {
        throw new Error(`Organisation not found`);
      }

      return connectAccount.stripeConnectAccount;
    } catch (e) {
      this.errorHelper.throwHttpException(
        `OrganisationsService.findConnectAccount`,
        e.message,
      );
    }
  }

  async createConnectAccount(
    organisationId: string,
    email: string,
    options: {
      currentUser: ICurrentUser;
    },
  ): Promise<string> {
    this.logger.info('OrganisationsService.createConnectAccount', {
      email,
      currentUser: options.currentUser.toLogObject(),
    });

    const transaction = await this.sequelize.transaction();

    try {
      const organisation = await this.findById(organisationId);

      if (!organisation) {
        throw new Error(`Organisation not found`);
      }

      let connect;

      if (organisation.stripeConnectAccount) {
        connect = await this.retrieveConnectAccount(organisationId, {
          currentUser: options?.currentUser,
        });
      } else {
        const newAccount = await this.stripeHelper.createConnectAccount({
          email: email,
          organisation_id: organisationId,
        });

        connect = newAccount.accountLink.url;

        await this.updateById(
          organisationId,
          {
            stripeConnectAccount: newAccount.connect.id,
            isConnectOnboarded: false,
          },
          { transaction },
        );
      }
      await transaction.commit();
      return connect;
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `OrganisationsService.createConnectAccount`,
        e.message,
      );
    }
  }

  async retrieveConnectAccount(
    organisationId: string,
    options: {
      currentUser: ICurrentUser;
    },
  ): Promise<string> {
    this.logger.info('OrganisationsService.retrieveConnectAccount', {
      organisationId,
      currentUser: options.currentUser.toLogObject(),
    });

    // const transaction = await this.sequelize.transaction();

    try {
      const organisation = await this.findOne({
        id: organisationId,
      });

      if (!organisation) {
        throw new Error(`Organisation not found`);
      }

      if (
        organisation.stripeConnectAccount &&
        organisation.isConnectOnboarded === true
      ) {
        return organisation.stripeConnectAccount;
      } else if (
        organisation.stripeConnectAccount &&
        (organisation.isConnectOnboarded === false ||
          organisation.isConnectOnboarded === null)
      ) {
        const connectAccount = await this.stripeHelper.retrieveConnectAccount({
          connect_id: organisation.stripeConnectAccount,
          organisation_id: organisationId,
        });

        if (typeof connectAccount === 'string') {
          return connectAccount;
        } else {
          await this.updateById(organisation.id, {
            isConnectOnboarded: true,
          });
        }
        return organisation.stripeConnectAccount;
      } else if (
        !organisation.stripeConnectAccount &&
        (organisation.isConnectOnboarded === false ||
          organisation.isConnectOnboarded === null)
      ) {
        throw new Error('Please set up a Payment Account to continue.');
      }
    } catch (e) {
      // await transaction.rollback();
      this.errorHelper.throwHttpException(
        `OrganisationsService.retrieveConnectAccount`,
        e.message,
      );
    }
  }

  async getCustomerPortal(
    partnershipId: string,
    organisationId: string,
    options: {
      currentUser: ICurrentUser;
    },
  ): Promise<string> {
    this.logger.info('OrganisationsService.getCustomerPortal', {
      partnershipId,
      organisationId,
      currentUser: options.currentUser.toLogObject(),
    });

    try {
      const organisation = await this.findById(organisationId);

      if (!organisation) {
        throw new Error(`Organisation not found`);
      }

      const partnership = await this.partnershipsService.findById(
        partnershipId,
      );

      if (!partnership) {
        throw new Error(`Partnership not found`);
      }

      const partnershipRequest = await this.partnershipRequestsService.findById(
        partnership.partnershipRequestId,
      );

      if (!partnershipRequest) {
        throw new Error(`PartnershipRequest not found`);
      }

      const subsciption = await this.subscriptionsService.findOne({
        partnershipRequestId: partnershipRequest.id,
      });

      if (!subsciption) {
        throw new Error(`Subscription not found`);
      }

      if (organisation.id === partnershipRequest.receiverOrganisationId) {
        //sender - connect account
        const senderOrganisation = await this.findById(
          partnershipRequest.senderOrganisationId,
        );

        const customerPortal = await this.stripeHelper.createExpressLoginLink({
          connect_id: senderOrganisation.stripeConnectAccount,
        });
        return customerPortal;
      } else if (organisation.id === partnershipRequest.senderOrganisationId) {
        //customer portal
        const customerPortal =
          await this.stripeHelper.createCustomerPortalSession({
            customer_id: subsciption.stripeCustomerId,
            connect_id: subsciption.stripeConnectId,
          });

        return customerPortal;
      }
    } catch (e) {
      this.errorHelper.throwHttpException(
        `OrganisationsService.getCustomerPortal`,
        e.message,
      );
    }
  }

  async getStripeExpressLink(
    organisationId: string,
    options: {
      currentUser: ICurrentUser;
    },
  ): Promise<string> {
    this.logger.info('OrganisationsService.getStripeExpressLink', {
      organisationId,
      currentUser: options.currentUser.toLogObject(),
    });

    try {
      const organisation = await this.findById(organisationId);

      if (!organisation) {
        throw new Error(`Organisation not found`);
      }

      if (!organisation.stripeConnectAccount) {
        return null;
      }

      const stripeExpressLink = await this.stripeHelper.createExpressLoginLink({
        connect_id: organisation.stripeConnectAccount,
      });
      return stripeExpressLink;
    } catch (e) {
      this.errorHelper.throwHttpException(
        `OrganisationsService.getStripeExpressLink`,
        e.message,
      );
    }
  }

  async addPreApprovedDomains(
    data: AddPreApprovedDomainInput,
    options: {
      currentUser: ICurrentUser;
    },
  ): Promise<Organisation> {
    this.logger.info('OrganisationsService.addPreApprovedDomains', {
      data,
      currentUser: options.currentUser.toLogObject(),
    });

    const transaction = await this.sequelize.transaction();

    try {
      const organisation = await this.findById(data.organisationId);

      if (!organisation) {
        throw new Error(`Organisation not found`);
      }

      if (!data.domain) {
        throw new Error('Domain is not valid');
      }

      const checkExists = Array.isArray(organisation.preApprovedDomains)
        ? organisation.preApprovedDomains.includes(data.domain)
        : '';

      if (checkExists) {
        throw new Error('Domain already added');
      }

      organisation.preApprovedDomains =
        organisation.preApprovedDomains === null
          ? []
          : organisation.preApprovedDomains;
      const allDomains = organisation.preApprovedDomains.concat(data.domain);

      await this.updateById(
        organisation.id,
        {
          preApprovedDomains: allDomains,
        },
        { transaction },
      );

      await transaction.commit();
      return organisation;
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `OrganisationsService.addPreApprovedDomains`,
        e.message,
      );
    }
  }

  async removePreApprovedDomains(
    data: AddPreApprovedDomainInput,
    options: {
      currentUser: ICurrentUser;
    },
  ): Promise<Organisation> {
    this.logger.info('OrganisationsService.removePreApprovedDomains', {
      data,
      currentUser: options.currentUser.toLogObject(),
    });

    const transaction = await this.sequelize.transaction();

    try {
      const organisation = await this.findById(data.organisationId);

      if (!organisation) {
        throw new Error(`Organisation not found`);
      }

      const checkExists = Array.isArray(organisation.preApprovedDomains)
        ? organisation.preApprovedDomains.includes(data.domain)
        : '';

      if (!checkExists) {
        throw new Error('Domain not found');
      }

      const allDomains = organisation.preApprovedDomains;
      allDomains.splice(allDomains.indexOf(data.domain), 1);

      await this.updateById(
        organisation.id,
        {
          preApprovedDomains: allDomains,
        },
        { transaction },
      );

      await transaction.commit();
      return organisation;
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `OrganisationsService.removePreApprovedDomains`,
        e.message,
      );
    }
  }

  async addParentOrganisation(
    data: ParentOrganisationInput,
    options: {
      currentUser: ICurrentUser;
    },
  ): Promise<Organisation> {
    this.logger.info('OrganisationsService.addParentOrganisation', {
      data,
      currentUser: options.currentUser.toLogObject(),
    });

    const transaction = await this.sequelize.transaction();

    try {
      const organisation = await this.findById(data.organisationId);

      if (!organisation) {
        throw new Error(`Organisation not found`);
      }

      const parentOrganisation = await this.findById(data.parentId);

      if (!parentOrganisation) {
        throw new Error(`Parent Organisation does not exist`);
      }

      const parentOrgs = organisation.parentOrganisations;

      if (parentOrgs.length === 1) {
        throw new Error(`Cannot add more than 1 parent organisation`);
      }

      const index = parentOrgs.findIndex(x => x.id === data.parentId);

      if (parentOrgs.length && index > -1) {
        throw new Error(`Parent Organisation already added`);
      }

      parentOrgs.push({ id: data.parentId });

      await this.updateById(
        organisation.id,
        {
          parentOrganisations: parentOrgs,
        },
        { transaction },
      );

      // follower and member logic
      await this.addMembershipAndFollowersToParentOrg(data, {
        currentUser: options.currentUser,
      });

      await transaction.commit();
      return organisation;
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `OrganisationsService.addParentOrganisation`,
        e.message,
      );
    }
  }

  async removeParentOrganisation(
    data: ParentOrganisationInput,
    options: {
      currentUser: ICurrentUser;
    },
  ): Promise<Organisation> {
    this.logger.info('OrganisationsService.removeParentOrganisation', {
      data,
      currentUser: options.currentUser.toLogObject(),
    });

    const transaction = await this.sequelize.transaction();

    try {
      const organisation = await this.findById(data.organisationId);

      if (!organisation) {
        throw new Error(`Organisation not found`);
      }

      const parentOrganisation = await this.findById(data.parentId);

      if (!parentOrganisation) {
        throw new Error(`Parent Organisation not exists`);
      }

      const parentOrgs = organisation.parentOrganisations;
      const index = parentOrgs.findIndex(x => x.id === data.parentId);

      if (parentOrgs.length && index < 0) {
        throw new Error(`Parent Organisation not found`);
      }

      parentOrgs.splice(index, 1);

      await this.updateById(
        organisation.id,
        {
          parentOrganisations: parentOrgs,
        },
        { transaction },
      );

      // follower and member logic
      await this.removeMembershipAndFollowersFromParentOrg(data, {
        currentUser: options.currentUser,
      });

      await transaction.commit();
      return organisation;
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `OrganisationsService.removeParentOrganisation`,
        e.message,
      );
    }
  }

  async addMembershipAndFollowersToParentOrg(
    data: ParentOrganisationInput,
    options: {
      currentUser: ICurrentUser;
    },
  ): Promise<any> {
    this.logger.info(
      'OrganisationsService.addMembershipAndFollowersToParentOrg',
      {
        data,
        currentUser: options.currentUser.toLogObject(),
      },
    );

    const transaction = await this.sequelize.transaction();

    try {
      const childOrgMemberships = await this.membershipsService.findAll({
        organisationId: data.organisationId,
        isPrimary: true,
        status: MembershipStatus.Active,
      });

      const parentOrgMemberships = await this.membershipsService.findAll({
        organisationId: data.parentId,
        isPrimary: true,
        status: MembershipStatus.Active,
      });

      // const parentPartnerships = await this.partnershipRequestsService.findAll(
      //   {
      //     senderOrganisationId: data.parentId,
      //     receiverOrganisationId: {
      //       [Op.ne]: data.organisationId,
      //     },
      //     status: PartnershipRequestStatus.Approved,
      //   },
      //   { transaction },
      // );

      // adding child memberships with primary = true and permission = member in parent
      if (childOrgMemberships.length) {
        for (const membership of childOrgMemberships) {
          await this.membershipsService.addMemberToParent(
            data.parentId,
            membership.profileId,
            data.organisationId,
            { transaction },
          );
        }
      }

      // Adding parent org emps as followers child org
      if (parentOrgMemberships.length) {
        const childfollowsArray = [];

        for (const membership of parentOrgMemberships) {
          await this.followersService.addOrApproveFollower(
            data.organisationId,
            membership.profileId,
            {
              transaction,
            },
          );

          const orgObj = {
            source: 'user:' + membership.profileId,
            target: 'organisations:' + data.organisationId,
          };
          childfollowsArray.push(orgObj);
        }

        //bulk follow (all parent member) child feed
        await this.streamFollowersService.bulkFollowScript(childfollowsArray);
      }

      await transaction.commit();
      return true;
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `OrganisationsService.addMembershipAndFollowersToParentOrg`,
        e.message,
      );
    }
  }

  async removeMembershipAndFollowersFromParentOrg(
    data: ParentOrganisationInput,
    options: {
      currentUser: ICurrentUser;
    },
  ): Promise<any> {
    this.logger.info(
      'OrganisationsService.removeMembershipAndFollowersFromParentOrg',
      {
        data,
        currentUser: options.currentUser.toLogObject(),
      },
    );

    const transaction = await this.sequelize.transaction();

    try {
      const childOrgMemberships = await this.membershipsService.findAll({
        organisationId: data.organisationId,
        isPrimary: true,
        status: MembershipStatus.Active,
      });

      const parentOrgMemberships = await this.membershipsService.findAll({
        organisationId: data.parentId,
        isPrimary: true,
        status: MembershipStatus.Active,
      });

      // const parentPartnerships = await this.partnershipRequestsService.findAll(
      //   {
      //     senderOrganisationId: data.parentId,
      //     receiverOrganisationId: {
      //       [Op.ne]: data.organisationId,
      //     },
      //     status: PartnershipRequestStatus.Approved,
      //   },
      //   { transaction },
      // );

      // removing child memberships with primary = false and permission = member in parent
      if (childOrgMemberships.length) {
        for (const membership of childOrgMemberships) {
          await this.membershipsService.removeMemberFromParent(
            data.parentId,
            membership.profileId,
            data.organisationId,
            { transaction },
          );
        }
      }

      const childUnfollowsArray = [];

      // removing parent org emps as child's followers
      if (parentOrgMemberships.length) {
        const parentMembershipIds = _.uniq(
          parentOrgMemberships.map(membership => membership.profileId),
        );

        const profileIds = _.uniq([...parentMembershipIds]);

        await this.followersService.remove({
          where: {
            profileId: profileIds,
            organisationId: data.organisationId,
          },
          transaction,
        });

        for (const profileId of profileIds) {
          const orgObj = {
            source: profileId,
            target: data.organisationId,
          };

          childUnfollowsArray.push(orgObj);
        }
      }

      //bulk unfollow (all child member) parent feed
      if (childUnfollowsArray.length) {
        await this.streamFollowersService.bulkUnfollowScript(
          childUnfollowsArray,
        );
      }

      await transaction.commit();
      return true;
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `OrganisationsService.removeMembershipAndFollowersFromParentOrg`,
        e.message,
      );
    }
  }

  /* Called when a new partnership is created between 2 orgs */
  async addNewConnectedOrgToExistingParentChild(
    data: ParentOrganisationInput,
    options?: {
      transaction?: Transaction;
    },
  ): Promise<any> {
    this.logger.info(
      'OrganisationsService.addNewConnectedOrgToExistingParentChild',
      {
        data,
      },
    );

    const isTransactionSet = !!options?.transaction;
    const transaction =
      options?.transaction || (await this.sequelize.transaction());

    try {
      const childOrgMemberships = await this.membershipsService.findAll({
        organisationId: data.organisationId, //child members
        isPrimary: true,
        status: MembershipStatus.Active,
      });

      // const partnershipOrganisationMemberships =
      //   await this.membershipsService.findAll(
      //     {
      //       organisationId: data.parentId,
      //       isPrimary: true,
      //       status: MembershipStatus.Active,
      //     },
      //     {
      //       transaction,
      //     },
      //   );
      const childfollowsArray = [];

      //Child's users -> partners followers
      if (childOrgMemberships.length) {
        for (const member of childOrgMemberships) {
          // parent's follower
          await this.followersService.addOrApproveFollower(
            data.parentId,
            member.profileId,
            { transaction },
          );

          const orgObj = {
            source: 'user:' + member.profileId,
            target: 'organisations:' + data.organisationId,
          };

          childfollowsArray.push(orgObj);
        }
      }

      if (!isTransactionSet) {
        await transaction.commit();
      }
      // bulk follow (all child member) parent feed
      await this.streamFollowersService.bulkFollowScript(childfollowsArray);

      return true;
    } catch (e) {
      if (!isTransactionSet) {
        await transaction.rollback();
      }
      this.errorHelper.throwHttpException(
        `OrganisationsService.addNewConnectedOrgToExistingParentChild`,
        e.message,
      );
    }
  }

  /* Called when a partnership is removed between 2 orgs */
  async removeConnectedOrgToExistingParentChild(
    data: ParentOrganisationInput,
    options?: {
      transaction?: Transaction;
    },
  ): Promise<any> {
    this.logger.info(
      'OrganisationsService.removeConnectedOrgToExistingParentChild',
      {
        data,
      },
    );

    const isTransactionSet = !!options?.transaction;
    const transaction =
      options?.transaction || (await this.sequelize.transaction());

    try {
      const childOrgMemberships = await this.membershipsService.findAll(
        {
          organisationId: data.organisationId,
          isPrimary: true,
          status: MembershipStatus.Active,
        },
        {
          transaction,
        },
      );

      const childUnfollowsArray = [];

      //Child's users -> partners followers
      if (childOrgMemberships.length) {
        const childOrgMemberIds = _.uniq(
          childOrgMemberships.map(membership => membership.profileId),
        );

        const profileIds = _.uniq([...childOrgMemberIds]);

        if (profileIds.length > 0) {
          await this.followersService.remove({
            where: {
              organisationId: data.parentId,
              profileId: {
                [Op.in]: profileIds,
              },
            },
            transaction,
          });

          for (const profile of profileIds) {
            const orgObj = {
              source: profile,
              target: data.parentId,
            };

            childUnfollowsArray.push(orgObj);
          }
        }
      }

      if (!isTransactionSet) {
        await transaction.commit();
      }

      await this.streamFollowersService.bulkUnfollowScript(childUnfollowsArray);

      return true;
    } catch (e) {
      if (!isTransactionSet) {
        await transaction.rollback();
      }
      this.errorHelper.throwHttpException(
        `OrganisationsService.removeConnectedOrgToExistingParentChild`,
        e.message,
      );
    }
  }

  async getClubHabloSubscriptionOrganisations(
    profileId: string,
  ): Promise<Organisation[]> {
    this.logger.verbose('Fetching all organisations with Hablo subscription');

    const organisationsAcivements =
      await this.organisationsRepository.findHabloSubscriptionOrganisations(
        profileId,
      );

    return organisationsAcivements.map(org => {
      // Add new properties to the existing Sequelize model instance
      org['isAchieved'] =
        org.achievements?.some(
          achievement =>
            achievement.profileId === profileId &&
            achievement.type === AchievementType.Ambassador &&
            achievement.isAchieved,
        ) ?? false;

      const ambassador = org.achievements?.find(
        achievement =>
          achievement.profileId === profileId &&
          achievement.type === AchievementType.Ambassador,
      );

      // a function to count org.achievements => (achievement.type !== AchievementType.Ambassador && achievement.isAchieved)
      // dont count if  achievement.type is not Ambassador or HighFive and achievement.isAchieved
      const achievedCounts = org.achievements?.filter(
        achievement =>
          achievement.profileId === profileId &&
          achievement.type !== AchievementType.Ambassador &&
          achievement.type !== AchievementType.HighFive &&
          achievement.isAchieved,
      );
      org['level'] = ambassador?.number ?? 0;
      org['achievedCounts'] = achievedCounts?.length ?? 0;
      org['totalCounts'] = 3;

      return org; // Return the modified Sequelize model instance
    });
  }

  async getHighFiveOrganisations(
    currentUserId: string,
    profileId: string,
  ): Promise<Organisation[]> {
    this.logger.verbose('OrganisationsService.getHighFiveOrganisations');
    const organisations = await this.findAll(
      {
        hasClubHabloSubscription: true,
      },
      {
        attributes: ['id', 'name', 'image', 'vanityId'],

        includeParams: [
          {
            model: Membership,
            as: 'memberships',
            where: {
              profileId: currentUserId,
              status: MembershipStatus.Active,
              permissions: {
                [Op.overlap]: [
                  MembershipPermission.Owner,
                  MembershipPermission.Admin,
                  MembershipPermission.HiddenAdmin,
                  MembershipPermission.Manager,
                  MembershipPermission.Editor,
                ],
              },
            },
            attributes: [], // Don't fetch extra fields
            required: true,
          },
          {
            model: Follower,
            as: 'followers',
            where: {
              profileId: profileId,
              status: FollowerStatus.Active,
            },
            attributes: [],
            required: true,
          },
        ],
      },
    );
    return organisations;
  }

  async getOrganisationIdbyVanityId(vanityId: string): Promise<string> {
    const organisation = await this.findOne(
      Sequelize.where(
        Sequelize.fn('lower', Sequelize.col('vanityId')),
        Sequelize.fn('lower', vanityId),
      ),
    );
    return organisation?.id;
  }

  async getReferralCount(organisationId: string): Promise<number> {
    this.logger.info('OrganisationsService.getReferralCount', {
      organisationId,
    });
    try {
      const count = await this.profilesService.count({
        referredByOrganisationId: organisationId,
      });
      return count;
    } catch (error) {
      this.logger.error(
        `Error in OrganisationsService.getReferralCount for organisationId ${organisationId}: ${error.message}`,
        { error },
      );
      this.errorHelper.throwHttpException(
        `OrganisationsService.getReferralCount`,
        error.message,
      );
    }
  }
}
