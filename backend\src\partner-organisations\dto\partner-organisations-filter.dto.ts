import { Field, InputType } from '@nestjs/graphql';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { PartnerOrganisationStatus } from '../args/partner-organisations.args';

@InputType()
export class PartnerOrganisationsFilter {
  @Field(() => [PartnerOrganisationStatus], { nullable: true })
  @IsOptional()
  @IsEnum(PartnerOrganisationStatus, { each: true })
  status?: PartnerOrganisationStatus[];

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  searchText?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  organisationId?: string;
}
