import {
  Column,
  Model,
  Table,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ObjectType, Field, ID, Int } from '@nestjs/graphql';
import { DataTypes } from 'sequelize';
import short from 'short-uuid';
import { Organisation } from '../../organisations/models/organisation.model';
import { PartnerOrganisationStatus } from '../args/partner-organisations.args';

@Table
@ObjectType()
export class PartnerOrganisation extends Model<PartnerOrganisation> {
  @Field(() => ID)
  @Column({
    primaryKey: true,
    unique: true,
    allowNull: false,
    defaultValue: short.generate,
  })
  id: string;

  @Field(() => ID)
  @ForeignKey(() => Organisation)
  @Column({
    allowNull: false,
    type: DataTypes.UUID,
  })
  parentOrgId: string;

  @Field(() => ID)
  @ForeignKey(() => Organisation)
  @Column({
    allowNull: false,
    type: DataTypes.UUID,
    unique: true,
  })
  childOrgId: string;

  @Field(() => PartnerOrganisationStatus)
  @Column({
    type: DataTypes.ENUM(...Object.values(PartnerOrganisationStatus)),
    allowNull: false,
    defaultValue: PartnerOrganisationStatus.Pending,
  })
  status: PartnerOrganisationStatus;

  @Field(() => Date, { nullable: true })
  @Column
  connectionApprovedDate: Date;

  @Field(() => Date, { nullable: true })
  @Column
  disconnectionDate: Date;

  @Field(() => Date, { nullable: true })
  @Column
  connectionRejectionDate: Date;

  @Field(() => Int)
  @Column({
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 2,
  })
  postsLimit: number;

  @Field()
  @Column
  createdAt: Date;

  @Field()
  @Column
  updatedAt: Date;

  @Field(() => Organisation)
  @BelongsTo(() => Organisation, 'parentOrgId')
  parentOrganisation: Organisation;

  @Field(() => Organisation)
  @BelongsTo(() => Organisation, 'childOrgId')
  childOrganisation: Organisation;

  @Field(() => Int)
  postsUsedThisMonth?: number;
}
