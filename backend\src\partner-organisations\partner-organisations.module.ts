import { Module, forwardRef } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { PartnerOrganisation } from './models/partner-organisation.model';
import { PartnerOrganisationsService } from './partner-organisations.service';
import { PartnerOrganisationsResolver } from './partner-organisations.resolver';
import { PartnerOrganisationsRepository } from './partner-organisations.repository';
import { OrganisationsModule } from '../organisations/organisations.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { MembershipsModule } from '../memberships/memberships.module';
import { PostsModule } from '../posts/posts.module';

@Module({
  imports: [
    SequelizeModule.forFeature([PartnerOrganisation]),
    forwardRef(() => OrganisationsModule),
    forwardRef(() => NotificationsModule),
    forwardRef(() => MembershipsModule),
    forwardRef(() => PostsModule),
  ],
  providers: [
    PartnerOrganisationsService,
    PartnerOrganisationsResolver,
    PartnerOrganisationsRepository,
  ],
  exports: [PartnerOrganisationsService, PartnerOrganisationsRepository],
})
export class PartnerOrganisationsModule {}
