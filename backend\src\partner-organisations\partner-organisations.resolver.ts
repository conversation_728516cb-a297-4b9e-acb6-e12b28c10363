import {
  UseGuards,
  Inject,
  forwardRef,
  NotFoundException,
} from '@nestjs/common';
import {
  Args,
  Query,
  Resolver,
  Mutation,
  ResolveField,
  Parent,
  InputType,
  Field,
  Int,
} from '@nestjs/graphql';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import moment from 'moment';

import { PartnerOrganisation } from './models/partner-organisation.model';
import { PartnerOrganisationsService } from './partner-organisations.service';
import { GqlAuthGuard } from '../authz/graphql-auth.guard';
import {
  CurrentUser,
  ICurrentUser,
} from '../common/decorators/current-user.decorator';
import { CreatePartnerOrganisationInput } from './dto/create-partner-organisation.dto';
import { UpdatePartnerOrganisationInput } from './dto/update-partner-organisation.dto';
import { PartnerOrganisationsFilter } from './dto/partner-organisations-filter.dto';
import { Organisation } from '../organisations/models/organisation.model';
import { OrganisationsService } from '../organisations/organisations.service';
import { PartnerOrganisationStatus } from './args/partner-organisations.args';
import { PostsService } from '../posts/posts.service';

@InputType()
export class UpdatePartnerOrganisationByOrgsInput {
  @Field(() => String)
  parentOrgId: string;

  @Field(() => String)
  childOrgId: string;

  @Field({ nullable: true })
  postsLimit?: number;

  @Field(() => String, { nullable: true })
  status?: string;
}

@Resolver(() => PartnerOrganisation)
export class PartnerOrganisationsResolver {
  constructor(
    @Inject(forwardRef(() => PartnerOrganisationsService))
    private readonly partnerOrganisationsService: PartnerOrganisationsService,
    @Inject(forwardRef(() => OrganisationsService))
    private readonly organisationsService: OrganisationsService,
    @Inject(forwardRef(() => PostsService))
    private readonly postsService: PostsService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  @Query(() => [PartnerOrganisation])
  @UseGuards(GqlAuthGuard)
  async partnerOrganisations(
    @CurrentUser() currentUser: ICurrentUser,
    @Args('filter', { nullable: true }) filter?: PartnerOrganisationsFilter,
  ): Promise<PartnerOrganisation[]> {
    return this.partnerOrganisationsService.findAllPartners(
      filter,
      currentUser,
    );
  }

  @Query(() => PartnerOrganisation, { nullable: true })
  @UseGuards(GqlAuthGuard)
  async partnerOrganisation(
    @CurrentUser() currentUser: ICurrentUser,
    @Args('id') id: string,
  ): Promise<PartnerOrganisation> {
    return this.partnerOrganisationsService.findById(id);
  }

  @Query(() => [PartnerOrganisation])
  @UseGuards(GqlAuthGuard)
  async childPartnerOrganisations(
    @CurrentUser() currentUser: ICurrentUser,
    @Args('organisationId') organisationId: string,
    @Args('filter', { nullable: true }) filter?: PartnerOrganisationsFilter,
  ): Promise<PartnerOrganisation[]> {
    return this.partnerOrganisationsService.findByParentOrgId(
      organisationId,
      filter,
    );
  }

  @Query(() => PartnerOrganisation, { nullable: true })
  @UseGuards(GqlAuthGuard)
  async parentPartnerOrganisation(
    @CurrentUser() currentUser: ICurrentUser,
    @Args('organisationId') organisationId: string,
  ): Promise<PartnerOrganisation> {
    return this.partnerOrganisationsService.findByChildOrgId(organisationId);
  }

  @Mutation(() => PartnerOrganisation)
  @UseGuards(GqlAuthGuard)
  async createPartnerOrganisation(
    @CurrentUser() currentUser: ICurrentUser,
    @Args('data') data: CreatePartnerOrganisationInput,
  ): Promise<PartnerOrganisation> {
    return this.partnerOrganisationsService.createPartnership(
      data,
      currentUser,
    );
  }

  @Mutation(() => PartnerOrganisation)
  @UseGuards(GqlAuthGuard)
  async updatePartnerOrganisation(
    @CurrentUser() currentUser: ICurrentUser,
    @Args('data') data: UpdatePartnerOrganisationInput,
  ): Promise<PartnerOrganisation> {
    return this.partnerOrganisationsService.updatePartnership(
      data.id,
      data,
      currentUser,
    );
  }

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  async deletePartnerOrganisation(
    @CurrentUser() currentUser: ICurrentUser,
    @Args('id') id: string,
  ): Promise<boolean> {
    return this.partnerOrganisationsService.removePartnership(id, currentUser);
  }

  @Mutation(() => PartnerOrganisation)
  @UseGuards(GqlAuthGuard)
  async acceptPartnerOrganisation(
    @CurrentUser() currentUser: ICurrentUser,
    @Args('id') id: string,
  ): Promise<PartnerOrganisation> {
    return this.partnerOrganisationsService.accept(id, currentUser);
  }

  @Mutation(() => PartnerOrganisation)
  @UseGuards(GqlAuthGuard)
  async declinePartnerOrganisation(
    @CurrentUser() currentUser: ICurrentUser,
    @Args('id') id: string,
  ): Promise<PartnerOrganisation> {
    return this.partnerOrganisationsService.decline(id, currentUser);
  }

  @Mutation(() => PartnerOrganisation)
  @UseGuards(GqlAuthGuard)
  async disconnectPartnerOrganisation(
    @CurrentUser() currentUser: ICurrentUser,
    @Args('id') id: string,
  ): Promise<PartnerOrganisation> {
    return this.partnerOrganisationsService.disconnect(id, currentUser);
  }

  @Mutation(() => PartnerOrganisation)
  @UseGuards(GqlAuthGuard)
  async updatePartnerOrganisationByOrgs(
    @CurrentUser() currentUser: ICurrentUser,
    @Args('data') data: UpdatePartnerOrganisationByOrgsInput,
  ): Promise<PartnerOrganisation> {
    const partnership =
      await this.partnerOrganisationsService.findByParentAndChildIds(
        data.parentOrgId,
        data.childOrgId,
      );

    if (!partnership) {
      throw new NotFoundException('Partnership not found');
    }

    if (data.status) {
      const status = data.status.toLowerCase();

      if (status === 'approved') {
        return this.partnerOrganisationsService.accept(
          partnership.id,
          currentUser,
        );
      } else if (status === 'rejected') {
        return this.partnerOrganisationsService.decline(
          partnership.id,
          currentUser,
        );
      } else if (status === 'disconnected') {
        return this.partnerOrganisationsService.disconnect(
          partnership.id,
          currentUser,
        );
      }
    }

    return this.partnerOrganisationsService.updatePartnership(
      partnership.id,
      {
        id: partnership.id,
        postsLimit: data.postsLimit,
      },
      currentUser,
    );
  }

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  async deletePartnerOrganisationByOrgs(
    @CurrentUser() currentUser: ICurrentUser,
    @Args('parentOrgId', { type: () => String }) parentOrgId: string,
    @Args('childOrgId', { type: () => String }) childOrgId: string,
  ): Promise<boolean> {
    const partnership =
      await this.partnerOrganisationsService.findByParentAndChildIds(
        parentOrgId,
        childOrgId,
      );

    if (!partnership) {
      throw new NotFoundException('Partnership not found');
    }

    // Only allow deletion if request is rejected or if the parent org is the one who created it
    if (
      partnership.status === PartnerOrganisationStatus.Rejected ||
      (partnership.status === PartnerOrganisationStatus.Pending &&
        partnership.parentOrgId === parentOrgId)
    ) {
      return this.partnerOrganisationsService.removePartnership(
        partnership.id,
        currentUser,
      );
    }

    throw new NotFoundException(
      'Partnership cannot be deleted in its current state',
    );
  }

  @ResolveField(() => Organisation)
  async parentOrganisation(
    @Parent() partnerOrganisation: PartnerOrganisation,
  ): Promise<Organisation> {
    if (partnerOrganisation.parentOrganisation) {
      return partnerOrganisation.parentOrganisation;
    }
    return this.organisationsService.findById(partnerOrganisation.parentOrgId);
  }

  @ResolveField(() => Organisation)
  async childOrganisation(
    @Parent() partnerOrganisation: PartnerOrganisation,
  ): Promise<Organisation> {
    if (partnerOrganisation.childOrganisation) {
      return partnerOrganisation.childOrganisation;
    }
    return this.organisationsService.findById(partnerOrganisation.childOrgId);
  }

  @ResolveField(() => Int)
  async postsUsedThisMonth(
    @Parent() partnerOrganisation: PartnerOrganisation,
  ): Promise<number> {
    // Only calculate for approved partnerships
    if (partnerOrganisation.status !== PartnerOrganisationStatus.Approved) {
      return 0;
    }

    const startOfMonth = moment().startOf('month').toDate();
    const endOfMonth = moment().endOf('month').toDate();

    const count = await this.postsService.countPartnerPosts({
      childOrgId: partnerOrganisation.childOrgId,
      parentOrgId: partnerOrganisation.parentOrgId,
      startDate: startOfMonth,
      endDate: endOfMonth,
    });

    return count;
  }
}
