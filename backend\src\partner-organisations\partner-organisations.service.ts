import {
  Injectable,
  Inject,
  forwardRef,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { Sequelize } from 'sequelize-typescript';
import { Op, QueryTypes } from 'sequelize';

import { PartnerOrganisation } from './models/partner-organisation.model';
import { CreatePartnerOrganisationInput } from './dto/create-partner-organisation.dto';
import { UpdatePartnerOrganisationInput } from './dto/update-partner-organisation.dto';
import { PartnerOrganisationsFilter } from './dto/partner-organisations-filter.dto';
import { PartnerOrganisationStatus } from './args/partner-organisations.args';
import { OrganisationsService } from '../organisations/organisations.service';
import { NotificationsService } from '../notifications/notifications.service';
import { NotificationType } from '../notifications/args/notifications.args';
import { ICurrentUser } from '../common/decorators/current-user.decorator';
import { PartnerOrganisationsRepository } from './partner-organisations.repository';
import { BaseService } from '../common/base.service';
import { MembershipsService } from '../memberships/memberships.service';
import {
  MembershipPermission,
  MembershipStatus,
} from '../memberships/models/membership.model';
import { NotificationMessage } from '../notifications/args/notifications.args';
import { OrganisationType } from '../organisations/args/organisations.args';

@Injectable()
export class PartnerOrganisationsService extends BaseService(
  PartnerOrganisation,
) {
  constructor(
    @Inject(forwardRef(() => PartnerOrganisationsRepository))
    private readonly partnerOrganisationsRepository: PartnerOrganisationsRepository,
    @Inject(forwardRef(() => OrganisationsService))
    private readonly organisationsService: OrganisationsService,
    @Inject(forwardRef(() => NotificationsService))
    private readonly notificationsService: NotificationsService,
    @Inject(forwardRef(() => MembershipsService))
    private readonly membershipsService: MembershipsService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly sequelize: Sequelize,
  ) {
    super();
  }

  async createPartnership(
    data: CreatePartnerOrganisationInput,
    currentUser: ICurrentUser,
  ): Promise<PartnerOrganisation> {
    // Validate if parent org exists
    const parentOrg = await this.organisationsService.findOne({
      id: data.parentOrgId,
    });
    if (!parentOrg) {
      throw new NotFoundException('Parent organisation not found');
    }

    // Validate if child org exists
    const childOrg = await this.organisationsService.findOne({
      id: data.childOrgId,
    });
    if (!childOrg) {
      throw new NotFoundException('Child organisation not found');
    }

    // Check if child organization is a paid (Hablo subscription) organization
    if (childOrg.hasClubHabloSubscription === true) {
      throw new BadRequestException(
        'Can not create partnership with a paid (Hablo subscription) organization',
      );
    }

    // Check if user has proper permissions (Owner, Admin, or HiddenAdmin) for the parent organization
    const membership = await this.membershipsService.findOne({
      organisationId: data.parentOrgId,
      profileId: currentUser.profileId,
      status: MembershipStatus.Active,
    });

    if (!membership) {
      throw new BadRequestException(
        'You do not have permission to create this partnership request',
      );
    }

    const hasAdminPermission = membership.permissions.some(permission =>
      [
        MembershipPermission.Owner,
        MembershipPermission.Admin,
        MembershipPermission.HiddenAdmin,
      ].includes(permission),
    );

    if (!hasAdminPermission) {
      throw new BadRequestException(
        'Only users with Admin permission can create partner requests',
      );
    }

    // Validate parent organization type - must be Destination
    if (parentOrg.type !== OrganisationType.Destination) {
      throw new BadRequestException(
        'Only Destination organizations can create partnerships',
      );
    }

    // Validate parent organization has DMO subscription
    if (!parentOrg.isDMOSubscription) {
      throw new BadRequestException(
        'Parent organization must have a DMO subscription to create partnerships',
      );
    }

    // Validate child organization type - must be either PrivateSector or Destination
    if (
      childOrg.type !== OrganisationType.PrivateSector &&
      childOrg.type !== OrganisationType.Destination
    ) {
      throw new BadRequestException(
        'Child organization must be of type Private Sector or Destination',
      );
    }

    const existingPartnership = await this.findByParentAndChildIds(
      data.parentOrgId,
      data.childOrgId,
    );

    if (existingPartnership) {
      if (existingPartnership.status === PartnerOrganisationStatus.Pending) {
        throw new BadRequestException(
          'A pending partnership request already exists between these organizations',
        );
      }

      if (existingPartnership.status === PartnerOrganisationStatus.Approved) {
        throw new BadRequestException(
          'These organizations are already partners',
        );
      }
    }

    // Validate if child org is already a partner of another org with Approved status only
    const existingPartner = await this.findByChildOrgId(data.childOrgId);
    if (
      existingPartner &&
      existingPartner.status === PartnerOrganisationStatus.Approved &&
      existingPartner.parentOrgId !== data.parentOrgId
    ) {
      throw new BadRequestException(
        'Child organisation is already a partner of another organisation',
      );
    }

    // Validate partner limit
    await this.validatePartnerLimit(data.parentOrgId);

    // Create partner organisation using BaseService's create method
    const partnerOrganisation = await this.create({
      parentOrgId: data.parentOrgId,
      childOrgId: data.childOrgId,
      postsLimit: data.postsLimit || 2, // Default to 2 if not provided
      status: PartnerOrganisationStatus.Pending,
    });

    // Send notification to child org admins and editors
    const childOrgMemberships = await this.membershipsService.findAll({
      organisationId: data.childOrgId,
      permissions: {
        [Op.overlap]: [
          MembershipPermission.Owner,
          MembershipPermission.Admin,
          MembershipPermission.HiddenAdmin,
        ],
      },
      status: MembershipStatus.Active,
    });

    const transaction = await this.sequelize.transaction();

    try {
      // Create notifications for each admin and editor
      for (const membership of childOrgMemberships) {
        await this.notificationsService.createNotification(
          {
            ownerProfileId: membership.profileId,
            profileId: currentUser.profileId,
            organisationId: data.childOrgId,
            type: NotificationType.OrgPartnerRequestReceived,
            data: {
              partnerOrganisationId: partnerOrganisation.id,
              senderOrgId: data.parentOrgId,
              senderOrgName: parentOrg.name,
              receiverOrgId: data.childOrgId,
              receiverOrgName: childOrg.name,
            },
          },
          {
            transaction,
          },
        );
      }

      await transaction.commit();

      // Send push notifications
      const profileIds = childOrgMemberships.map(
        membership => membership.profileId,
      );
      const replacements = [parentOrg.name, childOrg.name];

      await this.notificationsService.sendPushNotification({
        profileIds,
        replacements,
        messageType: NotificationMessage.OrgPartnerRequestReceived,
      });
    } catch (e) {
      await transaction.rollback();
      this.logger.error(
        'Error creating notifications for partnership request',
        e,
      );
    }

    return partnerOrganisation;
  }

  async findAllPartners(
    filter?: PartnerOrganisationsFilter,
    currentUser?: ICurrentUser,
  ): Promise<PartnerOrganisation[]> {
    const where: any = {};

    if (filter?.status?.length) {
      where.status = { [Op.in]: filter.status };
    }

    let organisationId = filter?.organisationId || null;
    
    if (!organisationId && currentUser) {
      const membership = await this.membershipsService.findOne({
        profileId: currentUser.profileId,
        status: MembershipStatus.Active,
      });
      
      if (membership) {
        organisationId = membership.organisationId;
      }
    }
    
    if (organisationId) {
      where[Op.or] = [
        { parentOrgId: organisationId },
        { childOrgId: organisationId }
      ];
    }

    return this.findAll(where, {
      includeParams: ['parentOrganisation', 'childOrganisation'],
    });
  }

  async findByParentOrgId(
    parentOrgId: string,
    filter?: PartnerOrganisationsFilter,
  ): Promise<PartnerOrganisation[]> {
    const where: any = { parentOrgId };

    if (filter?.status?.length) {
      where.status = { [Op.in]: filter.status };
    }

    return this.findAll(where, {
      includeParams: ['parentOrganisation', 'childOrganisation'],
    });
  }

  async findByChildOrgId(childOrgId: string): Promise<PartnerOrganisation> {
    const where = {
      childOrgId,
      status: {
        [Op.in]: [
          PartnerOrganisationStatus.Pending,
          PartnerOrganisationStatus.Approved,
        ],
      },
    };

    return this.findOne(where, {
      includeParams: ['parentOrganisation', 'childOrganisation'],
    });
  }

  async updatePartnership(
    id: string,
    data: UpdatePartnerOrganisationInput,
    currentUser: ICurrentUser,
  ): Promise<PartnerOrganisation> {
    const partnerOrganisation = await this.findById(id, {
      includeParams: ['parentOrganisation', 'childOrganisation'],
    });

    if (!partnerOrganisation) {
      throw new NotFoundException('Partner organisation not found');
    }

    return this.updateById(id, data);
  }

  async removePartnership(
    id: string,
    currentUser: ICurrentUser,
  ): Promise<boolean> {
    const result = await this.removeById(id);
    return result > 0;
  }

  async accept(
    id: string,
    currentUser: ICurrentUser,
  ): Promise<PartnerOrganisation> {
    const partnerOrganisation = await this.findById(id, {
      includeParams: ['parentOrganisation', 'childOrganisation'],
    });

    if (!partnerOrganisation) {
      throw new NotFoundException('Partner organisation not found');
    }

    if (partnerOrganisation.status !== PartnerOrganisationStatus.Pending) {
      throw new BadRequestException(
        'Partner organisation is not in pending status',
      );
    }

    // Check if current user has proper permissions (Owner, Admin, or HiddenAdmin) for the child organization
    const membership = await this.membershipsService.findOne({
      organisationId: partnerOrganisation.childOrgId,
      profileId: currentUser.profileId,
      status: MembershipStatus.Active,
    });

    if (!membership) {
      throw new BadRequestException(
        'You do not have permission to accept this partnership request',
      );
    }

    const hasAdminPermission = membership.permissions.some(permission =>
      [
        MembershipPermission.Owner,
        MembershipPermission.Admin,
        MembershipPermission.HiddenAdmin,
      ].includes(permission),
    );

    if (!hasAdminPermission) {
      throw new BadRequestException(
        'Only users with Admin permission can accept partner requests',
      );
    }

    const transaction = await this.sequelize.transaction();

    try {
      // Get the child organization to check for connected organizations
      const childOrg = await this.organisationsService.findById(
        partnerOrganisation.childOrgId,
        { transaction },
      );

      // If the child organization has any connected organizations, disconnect them
      if (childOrg?.connectedOrganisations?.length > 0) {
        this.logger.info(
          'Disconnecting connected organizations from child organization',
          {
            childOrgId: partnerOrganisation.childOrgId,
            connectedOrganisations: childOrg.connectedOrganisations,
          },
        );

        // Process each connected organization
        for (const connectedOrgId of childOrg.connectedOrganisations) {
          try {
            // Find partnership requests between the child org and connected org
            const partnershipRequests = (await this.sequelize.query(
              `SELECT pr.id FROM "PartnershipRequests" pr 
               WHERE ((pr."senderOrganisationId" = :childOrgId AND pr."receiverOrganisationId" = :connectedOrgId) 
               OR (pr."senderOrganisationId" = :connectedOrgId AND pr."receiverOrganisationId" = :childOrgId))
               AND pr."status" IN ('Approved', 'Pending')`,
              {
                replacements: {
                  childOrgId: partnerOrganisation.childOrgId,
                  connectedOrgId: connectedOrgId,
                },
                type: QueryTypes.SELECT,
                transaction,
              },
            )) as { id: string }[];

            if (partnershipRequests.length === 0) continue;

            // Disconnect each partnership request found
            for (const request of partnershipRequests) {
              // Update the status to "Disconnected"
              await this.sequelize.query(
                `UPDATE "PartnershipRequests" 
                 SET "status" = 'Disconnected', 
                     "updatedAt" = NOW()
                 WHERE "id" = :requestId`,
                {
                  replacements: { requestId: request.id },
                  type: QueryTypes.UPDATE,
                  transaction,
                },
              );

              this.logger.info(
                `Updated partnership request ${request.id} status to Disconnected between ${partnerOrganisation.childOrgId} and ${connectedOrgId}`,
                {
                  requestId: request.id,
                  childOrgId: partnerOrganisation.childOrgId,
                  connectedOrgId,
                },
              );
            }
          } catch (error) {
            this.logger.error(
              `Error disconnecting organization ${connectedOrgId} from child organization ${partnerOrganisation.childOrgId}`,
              error,
            );
          }
        }

        // Clear the connected organizations array for the child organization
        await this.organisationsService.updateById(
          partnerOrganisation.childOrgId,
          {
            connectedOrganisations: [],
          },
          { transaction },
        );
      }

      // Update status and set connection approved date
      const updatedPartnerOrg = await this.updateById(
        id,
        {
          status: PartnerOrganisationStatus.Approved,
          connectionApprovedDate: new Date(),
        },
        { transaction },
      );

      // Update child organization's privacy to public using raw SQL to bypass permission checks
      await this.sequelize.query(
        `UPDATE "Organisations" SET "isPublic" = true, "privacy" = 'public' WHERE "id" = :childOrgId`,
        {
          replacements: { childOrgId: partnerOrganisation.childOrgId },
          transaction,
        },
      );

      // Update the Stream organization directly
      try {
        const streamOrgService = this.organisationsService['streamOrgService'];
        if (streamOrgService) {
          await streamOrgService.updateOrganisation(
            partnerOrganisation.childOrgId,
            {
              isPublic: true,
              privacy: 'public',
            },
            {},
          );
          
          // Make parent org feed follow child org feed
          // This allows posts from child org to appear in parent org feed
          try {
            const client = streamOrgService.client;
            if (client) {
              await client
                .feed('organisations', partnerOrganisation.parentOrgId)
                .follow('organisations', partnerOrganisation.childOrgId);
                
              this.logger.info(
                `Parent organization feed now following child organization feed`,
                {
                  parentOrgId: partnerOrganisation.parentOrgId,
                  childOrgId: partnerOrganisation.childOrgId,
                },
              );
            }
          } catch (followError) {
            this.logger.error(
              `Error making parent org feed follow child org feed`,
              followError,
            );
          }
        }
      } catch (streamError) {
        // Log error but continue with the process
        this.logger.error('Error updating Stream organization', streamError);
      }

      // Find all other pending partnerships for this child organization
      const otherPendingPartnerships = await this.findAll({
        childOrgId: partnerOrganisation.childOrgId,
        status: PartnerOrganisationStatus.Pending,
        id: { [Op.ne]: id }, // Exclude the current partnership
      });

      // Reject all other pending partnerships
      for (const pendingPartnership of otherPendingPartnerships) {
        await this.updateById(
          pendingPartnership.id,
          {
            status: PartnerOrganisationStatus.Rejected,
            connectionRejectionDate: new Date(),
          },
          { transaction },
        );
      }

      // Get parent org admins and editors
      const parentOrgMemberships = await this.membershipsService.findAll({
        organisationId: partnerOrganisation.parentOrgId,
        permissions: {
          [Op.overlap]: [
            MembershipPermission.Owner,
            MembershipPermission.Admin,
            MembershipPermission.HiddenAdmin,
          ],
        },
        status: MembershipStatus.Active,
      });

      // Get child org admins and editors
      const childOrgMemberships = await this.membershipsService.findAll({
        organisationId: partnerOrganisation.childOrgId,
        permissions: {
          [Op.overlap]: [
            MembershipPermission.Owner,
            MembershipPermission.Admin,
            MembershipPermission.HiddenAdmin,
          ],
        },
        status: MembershipStatus.Active,
      });

      // Send notification to parent org admins and editors
      for (const membership of parentOrgMemberships) {
        await this.notificationsService.createNotification(
          {
            ownerProfileId: membership.profileId,
            profileId: currentUser.profileId,
            organisationId: partnerOrganisation.parentOrgId,
            type: NotificationType.OrgPartnerAcceptedSender,
            data: {
              partnerOrganisationId: partnerOrganisation.id,
              senderOrgId: partnerOrganisation.parentOrgId,
              senderOrgName: partnerOrganisation.parentOrganisation.name,
              receiverOrgId: partnerOrganisation.childOrgId,
              receiverOrgName: partnerOrganisation.childOrganisation.name,
            },
          },
          {
            transaction,
          },
        );
      }

      // Send notification to child org admins and editors
      for (const membership of childOrgMemberships) {
        await this.notificationsService.createNotification(
          {
            ownerProfileId: membership.profileId,
            profileId: currentUser.profileId,
            organisationId: partnerOrganisation.childOrgId,
            type: NotificationType.OrgPartnerAcceptedReceiver,
            data: {
              partnerOrganisationId: partnerOrganisation.id,
              senderOrgId: partnerOrganisation.parentOrgId,
              senderOrgName: partnerOrganisation.parentOrganisation.name,
              receiverOrgId: partnerOrganisation.childOrgId,
              receiverOrgName: partnerOrganisation.childOrganisation.name,
            },
          },
          {
            transaction,
          },
        );
      }

      // Send notifications to parent orgs of rejected partnerships
      for (const pendingPartnership of otherPendingPartnerships) {
        // Get the parent org details for the rejected partnership
        const rejectedParentOrg = await this.organisationsService.findOne(
          {
            id: pendingPartnership.parentOrgId,
          },
          { transaction },
        );

        if (rejectedParentOrg) {
          // Find admins and editors of the rejected parent org
          const rejectedParentOrgMemberships =
            await this.membershipsService.findAll(
              {
                organisationId: pendingPartnership.parentOrgId,
                permissions: {
                  [Op.overlap]: [
                    MembershipPermission.Owner,
                    MembershipPermission.Admin,
                    MembershipPermission.HiddenAdmin,
                  ],
                },
                status: MembershipStatus.Active,
              },
              { transaction },
            );

          // Send notifications to each admin and editor
          for (const membership of rejectedParentOrgMemberships) {
            await this.notificationsService.createNotification(
              {
                ownerProfileId: membership.profileId,
                profileId: currentUser.profileId,
                organisationId: pendingPartnership.parentOrgId,
                type: NotificationType.OrgPartnerRequestRejected,
                data: {
                  partnerOrganisationId: pendingPartnership.id,
                  senderOrgId: pendingPartnership.parentOrgId,
                  senderOrgName: rejectedParentOrg.name,
                  receiverOrgId: partnerOrganisation.childOrgId,
                  receiverOrgName: partnerOrganisation.childOrganisation.name,
                },
              },
              {
                transaction,
              },
            );
          }

          // Send push notifications to rejected parent org members
          const rejectedProfileIds = rejectedParentOrgMemberships.map(
            membership => membership.profileId,
          );

          if (rejectedProfileIds.length > 0) {
            const rejectedReplacements = [
              partnerOrganisation.childOrganisation.name,
            ];

            await this.notificationsService.sendPushNotification({
              profileIds: rejectedProfileIds,
              replacements: rejectedReplacements,
              messageType: NotificationMessage.OrgPartnerRequestRejected,
            });
          }
        }
      }

      await transaction.commit();

      // Send push notifications to parent org admins and editors
      const parentProfileIds = parentOrgMemberships.map(
        membership => membership.profileId,
      );
      const parentReplacements = [partnerOrganisation.childOrganisation.name];

      await this.notificationsService.sendPushNotification({
        profileIds: parentProfileIds,
        replacements: parentReplacements,
        messageType: NotificationMessage.OrgPartnerAcceptedSender,
      });

      // Send push notifications to child org admins and editors
      const childProfileIds = childOrgMemberships.map(
        membership => membership.profileId,
      );
      const childReplacements = [
        partnerOrganisation.parentOrganisation.name,
        partnerOrganisation.childOrganisation.name,
      ];

      await this.notificationsService.sendPushNotification({
        profileIds: childProfileIds,
        replacements: childReplacements,
        messageType: NotificationMessage.OrgPartnerAcceptedReceiver,
      });

      return updatedPartnerOrg;
    } catch (e) {
      await transaction.rollback();
      this.logger.error(
        'Error processing partnership acceptance',
        e,
      );
    }
  }

  async decline(
    id: string,
    currentUser: ICurrentUser,
  ): Promise<PartnerOrganisation> {
    const partnerOrganisation = await this.findById(id, {
      includeParams: ['parentOrganisation', 'childOrganisation'],
    });

    if (!partnerOrganisation) {
      throw new NotFoundException('Partner organisation not found');
    }

    if (partnerOrganisation.status !== PartnerOrganisationStatus.Pending) {
      throw new BadRequestException(
        'Partner organisation is not in pending status',
      );
    }

    // Check if current user has proper permissions (Owner, Admin, or HiddenAdmin) for the child organization
    const membership = await this.membershipsService.findOne({
      organisationId: partnerOrganisation.childOrgId,
      profileId: currentUser.profileId,
      status: MembershipStatus.Active,
    });

    if (!membership) {
      throw new BadRequestException(
        'You do not have permission to decline this partnership request',
      );
    }

    const hasAdminPermission = membership.permissions.some(permission =>
      [
        MembershipPermission.Owner,
        MembershipPermission.Admin,
        MembershipPermission.HiddenAdmin,
      ].includes(permission),
    );

    if (!hasAdminPermission) {
      throw new BadRequestException(
        'Only users with Admin permission can decline partner requests',
      );
    }

    // Update status and set rejection date
    return this.updateById(id, {
      status: PartnerOrganisationStatus.Rejected,
      connectionRejectionDate: new Date(),
    });
  }

  async disconnect(
    id: string,
    currentUser: ICurrentUser,
  ): Promise<PartnerOrganisation> {
    const partnerOrganisation = await this.findById(id, {
      includeParams: ['parentOrganisation', 'childOrganisation'],
    });

    if (!partnerOrganisation) {
      throw new NotFoundException('Partner organisation not found');
    }

    if (partnerOrganisation.status !== PartnerOrganisationStatus.Approved) {
      throw new BadRequestException(
        'Partner organisation is not in approved status',
      );
    }

    // Check if current user has proper permissions (Owner, Admin, or HiddenAdmin) for either organization
    const parentMembership = await this.membershipsService.findOne({
      organisationId: partnerOrganisation.parentOrgId,
      profileId: currentUser.profileId,
      status: MembershipStatus.Active,
    });

    const childMembership = await this.membershipsService.findOne({
      organisationId: partnerOrganisation.childOrgId,
      profileId: currentUser.profileId,
      status: MembershipStatus.Active,
    });

    const hasParentAdminPermission =
      parentMembership &&
      parentMembership.permissions.some(permission =>
        [
          MembershipPermission.Owner,
          MembershipPermission.Admin,
          MembershipPermission.HiddenAdmin,
        ].includes(permission),
      );

    const hasChildAdminPermission =
      childMembership &&
      childMembership.permissions.some(permission =>
        [
          MembershipPermission.Owner,
          MembershipPermission.Admin,
          MembershipPermission.HiddenAdmin,
        ].includes(permission),
      );

    if (!hasParentAdminPermission && !hasChildAdminPermission) {
      throw new BadRequestException(
        'Only users with Admin permission can disconnect partner organizations',
      );
    }

    // Update status and set disconnection date
    return this.updateById(id, {
      status: PartnerOrganisationStatus.Disconnected,
      disconnectionDate: new Date(),
    });
  }

  async validatePartnerLimit(parentOrgId: string): Promise<void> {
    const parentOrg = await this.organisationsService.findOne({
      id: parentOrgId,
    });

    if (!parentOrg) {
      throw new NotFoundException('Parent organisation not found');
    }

    const maxAllowedPartners = parentOrg.DMOMaxPartners || 10;

    const existingPartners = await this.findAll({
      parentOrgId,
      status: {
        [Op.in]: [
          PartnerOrganisationStatus.Pending,
          PartnerOrganisationStatus.Approved,
        ],
      },
    });

    if (existingPartners.length >= maxAllowedPartners) {
      throw new BadRequestException(
        `Maximum number of partners (${maxAllowedPartners}) reached for this organisation. Cannot create new partnership.`,
      );
    }
  }

  async findByParentAndChildIds(
    parentOrgId: string,
    childOrgId: string,
  ): Promise<PartnerOrganisation> {
    return this.findOne(
      {
        parentOrgId,
        childOrgId,
      },
      {
        includeParams: ['parentOrganisation', 'childOrganisation'],
        order: [['updatedAt', 'DESC']],
      },
    );
  }
}
