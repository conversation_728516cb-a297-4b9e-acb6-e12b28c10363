import {
  Column,
  Model,
  Table,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ObjectType, Field, ID, Int } from '@nestjs/graphql';
import short from 'short-uuid';
import { Profile } from '../../profiles/models/profile.model';
import { PostImageCategory, PostStatus, PostType } from '../args/posts.args';
import { DataTypes } from 'sequelize';
import { Organisation } from '../../organisations/models/organisation.model';
import { Event } from '../../events/models/event.model';
import { Incentive } from '../../incentives/models/incentive.model';
import { Webinar } from '../../webinars/models/webinar.model';
import GraphQLJSON from 'graphql-type-json';

@ObjectType()
export class MultipleImageDetails {
  @Field()
  image: string;

  @Field()
  imageWidth: number;

  @Field()
  imageHeight: number;

  @Field()
  imageFormat: string;
}

@Table
@ObjectType()
export class Post extends Model<Post> {
  @Field(() => ID)
  @Column({
    primaryKey: true,
    unique: true,
    allowNull: false,
    defaultValue: short.generate,
  })
  id: string;

  @Field(() => PostType, { nullable: false })
  @Column({
    allowNull: false,
    type: DataTypes.STRING,
  })
  type: PostType;

  @Field(() => String, { nullable: true })
  @Column
  image?: string;

  @Field(() => Number, { nullable: true })
  @Column
  mediaWidth?: number;

  @Field(() => Number, { nullable: true })
  @Column
  mediaHeight?: number;

  @Field(() => String, { nullable: true })
  @Column
  mediaFormat?: string;

  @Field(() => [MultipleImageDetails], { nullable: true })
  @Column({
    type: DataTypes.ARRAY(DataTypes.JSON),
    defaultValue: [],
  })
  multipleImages: MultipleImageDetails[];

  @Field(() => PostImageCategory, { nullable: true })
  @Column({
    allowNull: true,
    type: DataTypes.STRING,
  })
  imageCategory: PostImageCategory;

  @Field(() => String, { nullable: true })
  @Column
  video: string;

  @Field(() => String, { nullable: true })
  @Column
  document: string;

  @Field()
  @Column
  text: string;

  @ForeignKey(() => Profile)
  @Column
  profileId: string;

  @Field()
  @BelongsTo(() => Profile, 'profileId')
  profile: Profile;

  @ForeignKey(() => Organisation)
  @Column
  organisationId: string;

  @Field()
  @BelongsTo(() => Organisation, 'organisationId')
  organisation: Organisation;

  @Field(() => ID, { nullable: true })
  @ForeignKey(() => Organisation)
  @Column({
    allowNull: true,
    type: DataTypes.UUID,
  })
  parentOrgId: string;

  @Field(() => Organisation, { nullable: true })
  @BelongsTo(() => Organisation, 'parentOrgId')
  parentOrganisation: Organisation;

  @ForeignKey(() => Event)
  @Column
  eventId: string;

  @BelongsTo(() => Event, 'eventId')
  event: Event;

  @ForeignKey(() => Incentive)
  @Column
  incentiveId: string;

  @BelongsTo(() => Incentive, 'incentiveId')
  incentive: Incentive;

  @ForeignKey(() => Webinar)
  @Column
  webinarId: string;

  @BelongsTo(() => Webinar, 'webinarId')
  webinar: Webinar;

  @Field(() => [String], { nullable: true })
  @Column({
    type: DataTypes.ARRAY(DataTypes.STRING),
    defaultValue: [],
  })
  seenBy: string[];

  @Field(() => Int)
  @Column({
    defaultValue: 0,
  })
  totalViews: number;

  @Field(() => PostStatus, { nullable: true, defaultValue: PostStatus.Live })
  @Column({
    allowNull: true,
    type: DataTypes.STRING,
  })
  status: PostStatus;

  @Field({ nullable: true })
  @Column({ type: DataTypes.DATE })
  scheduledAt: Date;

  @Field()
  @Column
  createdAt: Date;

  @Column
  updatedAt: Date;

  @Field(() => Int)
  @Column(DataTypes.VIRTUAL)
  get views(): number {
    return (this.seenBy || []).length;
  }

  @Field(() => GraphQLJSON, { nullable: true })
  @Column({
    type: DataTypes.JSON,
  })
  postAudience: any;

  @Field(() => Boolean, { nullable: true })
  isPartnerPost?: boolean;
}
