import { Module, forwardRef } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Post } from './models/post.model';
import { PostsService } from './posts.service';
import { PostsResolver } from './posts.resolver';
import { PostsRepository } from './posts.repository';
import { ProfilesModule } from '../profiles/profiles.module';
import { OrganisationsModule } from '../organisations/organisations.module';
import { MembershipsModule } from '../memberships/memberships.module';
import { CommonModule } from '../common/common.module';
import { ActivitiesModule } from '../activities/activities.module';
import { EventsInvitationsModule } from '../event-invitations/event-invitations.module';
import { EventsModule } from '../events/events.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { IncentivesModule } from '../incentives/incentives.module';
import { IncentiveParticipantsModule } from '../incentive-participants/incentive-participants.module';
import { WebinarsModule } from '../webinars/webinars.module';
import { WebinarParticipantsModule } from '../webinar-participants/webinar-participants.module';
import { StreamUpdatesModule } from '../feeds-updates/stream-updates.module';
import { PostsController } from './posts.controller';
import { StreamPostsModule } from '../feeds-posts/stream-posts.module';
import { OrganisationLoyaltyPointsModule } from '../organisation-loyalty-points/organisation-loyalty-points.module';
import { AchievementsModule } from '../achievements/achievements.module';
import { PartnerOrganisationsModule } from '../partner-organisations/partner-organisations.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Post]),
    forwardRef(() => CommonModule),
    forwardRef(() => ProfilesModule),
    forwardRef(() => OrganisationsModule),
    forwardRef(() => EventsModule),
    forwardRef(() => EventsInvitationsModule),
    forwardRef(() => MembershipsModule),
    forwardRef(() => ActivitiesModule),
    forwardRef(() => NotificationsModule),
    forwardRef(() => IncentivesModule),
    forwardRef(() => IncentiveParticipantsModule),
    forwardRef(() => WebinarsModule),
    forwardRef(() => WebinarParticipantsModule),
    forwardRef(() => StreamUpdatesModule),
    forwardRef(() => StreamPostsModule),
    forwardRef(() => OrganisationLoyaltyPointsModule),
    forwardRef(() => AchievementsModule),
    forwardRef(() => PartnerOrganisationsModule),
  ],
  providers: [PostsResolver, PostsService, PostsRepository, PostsController],
  exports: [PostsService, PostsRepository, PostsController],
})
export class PostsModule {}
