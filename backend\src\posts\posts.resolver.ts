import { UseGuards, Inject, forwardRef } from '@nestjs/common';
import {
  Args,
  Query,
  Resolver,
  ResolveField,
  Parent,
  Mutation,
} from '@nestjs/graphql';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';

import { Post } from './models/post.model';
import { PostsService } from './posts.service';
import { GqlAuthGuard } from '../authz/graphql-auth.guard';
import {
  CurrentUser,
  ICurrentUser,
} from '../common/decorators/current-user.decorator';
import { PostsArgs } from './args/posts.args';
import { PaginatedResult, PostsResult } from '../common/args/paginated-result';
import { Profile } from '../profiles/models/profile.model';
import { ProfilesService } from '../profiles/profiles.service';
import { OrganisationsService } from '../organisations/organisations.service';
import { Organisation } from '../organisations/models/organisation.model';
import { Event } from '../events/models/event.model';
import { CreatePostInput } from './dto/create-post.input';
import {
  MembershipsServiceRights,
  MembershipRight,
} from '../memberships/helpers/memberships.service.rights';
import { UpdatePostInput } from './dto/update-post.input';
import { ErrorHelper } from '../common/helpers/error';
import { EventsService } from '../events/events.service';
import { Incentive } from '../incentives/models/incentive.model';
import { IncentivesService } from '../incentives/incentives.service';
import { Webinar } from '../webinars/models/webinar.model';
import { WebinarsService } from '../webinars/webinars.service';
import { UpdatePostScheduleDateInput } from './dto/update-post-schedule-date.input';

@Resolver(() => Post)
export class PostsResolver {
  @Inject(forwardRef(() => PostsService))
  private readonly postsService: PostsService;
  @Inject(forwardRef(() => ProfilesService))
  private readonly profilesService: ProfilesService;
  @Inject(forwardRef(() => EventsService))
  private readonly eventsService: EventsService;
  @Inject(forwardRef(() => IncentivesService))
  private readonly incentivesService: IncentivesService;
  @Inject(forwardRef(() => WebinarsService))
  private readonly webinarsService: WebinarsService;
  @Inject(forwardRef(() => OrganisationsService))
  private readonly organisationsService: OrganisationsService;
  @Inject(forwardRef(() => MembershipsServiceRights))
  private readonly membershipsServiceRights: MembershipsServiceRights;
  @Inject(WINSTON_MODULE_PROVIDER)
  private readonly logger: Logger;
  @Inject()
  private readonly errorHelper: ErrorHelper;

  @Mutation(() => Post)
  @UseGuards(GqlAuthGuard)
  async createPost(
    @CurrentUser() user: ICurrentUser,
    @Args('postData') postData: CreatePostInput,
  ): Promise<Post> {
    this.logger.verbose('PostsResolver.createPost (mutation)', {
      user: user.toLogObject(),
      postData,
    });

    const { organisationId } = postData;

    await this.membershipsServiceRights.checkRights(organisationId, {
      currentProfileId: user.profileId,
      rights: [MembershipRight.CreatePost],
    });

    return this.postsService.createPost(
      {
        ...postData,
        profileId: user.profileId,
      },
      {
        currentUser: user,
      },
    );
  }

  @Mutation(() => Post)
  @UseGuards(GqlAuthGuard)
  async updatePost(
    @CurrentUser() user: ICurrentUser,
    @Args('postId') postId: string,
    @Args('postData') postData: UpdatePostInput,
  ): Promise<Post> {
    this.logger.verbose('PostsResolver.updatePost (mutation)', {
      user: user.toLogObject(),
      postData,
    });

    const post = await this.postsService.findById(postId);

    await this.membershipsServiceRights.checkRights(post.organisationId, {
      currentProfileId: user.profileId,
      rights: [MembershipRight.UpdatePost],
    });

    return this.postsService.updatePost(postId, postData, {
      profileId: user.profileId,
    });
  }

  @Mutation(() => Post)
  @UseGuards(GqlAuthGuard)
  async updatePostScheduleTime(
    @CurrentUser() user: ICurrentUser,
    @Args('postId') postId: string,
    @Args('postData') postData: UpdatePostScheduleDateInput,
  ): Promise<Post> {
    this.logger.verbose('PostsResolver.updatePostScheduleTime (mutation)', {
      user: user.toLogObject(),
      postData,
    });

    const post = await this.postsService.findById(postId);

    await this.membershipsServiceRights.checkRights(post.organisationId, {
      currentProfileId: user.profileId,
      rights: [MembershipRight.UpdatePost],
    });

    return this.postsService.updatePostScheduleTime(postId, postData, {
      profileId: user.profileId,
    });
  }

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  async removePost(
    @CurrentUser() user: ICurrentUser,
    @Args('postId') postId: string,
  ): Promise<boolean> {
    this.logger.verbose('PostsResolver.removePost (mutation)', {
      user: user.toLogObject(),
      postId,
    });

    const post = await this.postsService.findById(postId);

    if (!post) return true;

    await this.membershipsServiceRights.checkRights(post.organisationId, {
      currentProfileId: user.profileId,
      rights: [MembershipRight.RemovePost],
    });

    await this.postsService.removePost(postId, {
      profileId: user.profileId,
    });

    return true;
  }

  @Mutation(() => Post)
  @UseGuards(GqlAuthGuard)
  seenPost(
    @CurrentUser() user: ICurrentUser,
    @Args('id') id: string,
  ): Promise<Post> {
    this.logger.verbose('PostsResolver.seenPost (mutation)', {
      user: user.toLogObject(),
      id,
    });

    return this.postsService.seenPost(id, {
      profileId: user.profileId,
    });
  }

  @Query(() => Post)
  @UseGuards(GqlAuthGuard)
  async post(
    @CurrentUser() user: ICurrentUser,
    @Args('id', { nullable: false }) id: string,
  ): Promise<Post> {
    this.logger.verbose('PostsResolver.post (query)', {
      id,
    });

    const post = await this.postsService.findById(id);

    if (!post) {
      this.errorHelper.throwHttpException(
        `PostsResolver.event`,
        `Post not exists`,
      );
    }

    return post;

    // return this.postsService.updateById(post.id, {
    //   totalViews: (post.totalViews || 0) + 1,
    // });
  }

  @Query(() => PostsResult)
  @UseGuards(GqlAuthGuard)
  async posts(
    @CurrentUser() user: ICurrentUser,
    @Args() postArgs: PostsArgs,
  ): Promise<PaginatedResult<Post>> {
    this.logger.verbose('PostsResolver.posts (query)', {
      user: user.toLogObject(),
      postArgs,
    });

    return this.postsService.findPosts(
      user.profileId,
      {
        type: postArgs.filter?.type,
        organisationId: postArgs.filter?.organisationId,
        parentOrgId: postArgs.filter?.parentOrgId,
        eventId: postArgs.filter?.eventId,
        incentiveId: postArgs.filter?.incentiveId,
        webinarId: postArgs.filter?.webinarId,
        updates: postArgs.filter?.updates,
        searchText: postArgs.filter?.searchText,
      },
      {
        first: postArgs.first,
        after: postArgs.after,
        offset: postArgs.offset,
        sortBy: postArgs.sortBy,
        sortOrder: postArgs.sortOrder,
      },
    );
  }

  @Query(() => PostsResult)
  @UseGuards(GqlAuthGuard)
  async scheduledPostList(
    @CurrentUser() user: ICurrentUser,
    @Args('organisationId') organisationId?: string,
    @Args('parentOrganisationId', { nullable: true }) parentOrganisationId?: string,
  ): Promise<PaginatedResult<Post>> {
    this.logger.verbose('PostsResolver.scheduledPostList (query)', {
      user: user.toLogObject(),
    });

    const posts = await this.postsService.scheduledPostList(
      {
        currentUser: user,
      },
      organisationId,
      parentOrganisationId,
    );

    const data = {
      records: posts,
      totalCount: posts.length,
    };

    return data;
  }

  @ResolveField('profile', () => Profile)
  async profile(@Parent() post: Post): Promise<Profile> {
    this.logger.verbose('PostsResolver.profile (field resolver)', {
      postId: post.id,
    });

    return this.profilesService.findById(post.profileId);
  }

  @ResolveField(() => Organisation, { nullable: true })
  async organisation(@Parent() post: Post): Promise<Organisation> {
    this.logger.verbose('PostsResolver.organisation (field resolver)', {
      postId: post.id,
    });

    if (!post.organisationId) return null;
    return this.organisationsService.findById(post.organisationId, {
      useCache: true,
    });
  }

  @ResolveField('isPartnerPost', () => Boolean, { nullable: true })
  isPartnerPost(@Parent() post: Post): boolean {
    return !!post.parentOrgId;
  }

  @ResolveField('parentOrganisation', () => Organisation, { nullable: true })
  async parentOrganisation(@Parent() post: Post): Promise<Organisation | null> {
    if (!post.parentOrgId) return null;
    return this.organisationsService.findById(post.parentOrgId);
  }

  @ResolveField('event', () => Event, { nullable: true })
  async event(@Parent() post: Post): Promise<Event> {
    if (!post.eventId) return null;
    return this.eventsService.findById(post.eventId, {
      useCache: true,
    });
  }

  @ResolveField('incentive', () => Incentive, { nullable: true })
  async incentive(@Parent() post: Post): Promise<Incentive> {
    if (!post.incentiveId) return null;
    return this.incentivesService.findById(post.incentiveId, {
      useCache: true,
    });
  }

  @ResolveField('webinar', () => Webinar, { nullable: true })
  async webinar(@Parent() post: Post): Promise<Webinar> {
    if (!post.webinarId) return null;
    return this.webinarsService.findById(post.webinarId, {
      useCache: true,
    });
  }

  @ResolveField('isSeen', () => Boolean, { nullable: true })
  isSeen(@CurrentUser() user: ICurrentUser, @Parent() post: Post): boolean {
    return post.seenBy.includes(user.profileId);
  }
}
