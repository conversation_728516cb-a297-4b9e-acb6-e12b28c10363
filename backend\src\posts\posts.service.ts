import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  forwardRef,
  BadRequestException,
} from '@nestjs/common';
import moment from 'moment';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Op, Transaction } from 'sequelize';
import { Sequelize } from 'sequelize-typescript';
import { Logger } from 'winston';
import { ActivitiesService } from '../activities/activities.service';
import { ActivityType } from '../activities/args/activities.args';
import { PaginatedResult } from '../common/args/paginated-result';
import { PaginationArgs } from '../common/args/pagination.args';
import { BaseService } from '../common/base.service';
import { ICurrentUser } from '../common/decorators/current-user.decorator';
import { ErrorHelper } from '../common/helpers/error';
import { Underscore as _ } from '../common/helpers/underscore';
import {
  Routes,
  encodePathParams,
} from '../email/helpers/notificationLayoutHelper';
import { EventInvitationStatus } from '../event-invitations/args/event-invitations.args';
import { EventInvitationsService } from '../event-invitations/event-invitations.service';
import { EventsService } from '../events/events.service';
import { StreamPostsService } from '../feeds-posts/stream-posts.service';
import { StreamUpdatesService } from '../feeds-updates/stream-updates.service';
import { IncentiveParticipantStatus } from '../incentive-participants/args/incentive-participants.args';
import { IncentiveParticipantsService } from '../incentive-participants/incentive-participants.service';
import { IncentivesService } from '../incentives/incentives.service';
import { MembershipsService } from '../memberships/memberships.service';
import {
  NotificationMessage,
  NotificationType,
} from '../notifications/args/notifications.args';
import { NotificationsService } from '../notifications/notifications.service';
import { OrganisationsService } from '../organisations/organisations.service';
import { ProfilesService } from '../profiles/profiles.service';
import { WebinarParticipantStatus } from '../webinar-participants/args/webinar-participants.args';
import { WebinarParticipantsService } from '../webinar-participants/webinar-participants.service';
import { WebinarsService } from '../webinars/webinars.service';
import { PostStatus, PostType } from './args/posts.args';
import { CreatePostDto } from './dto/create-post.dto';
import { UpdatePostInput } from './dto/update-post.input';
import { Post } from './models/post.model';
import { PostsRepository } from './posts.repository';
import { UpdatePostScheduleDateInput } from './dto/update-post-schedule-date.input';
import {
  createOrganisationActivityDataDto,
  createPostActivityDataDto,
} from '../activities/dto/create-organisation-activity-data.dto';
import { OrganisationLoyaltyPointsService } from '../organisation-loyalty-points/organisation-loyalty-points.service';
import { AchievementsService } from '../achievements/achievements.service';
import { AchievementType } from '../achievements/args/achievements.args';
import { OrganisationActivity } from '../activities/models/organisation-activity.model';
import { PartnerOrganisationsService } from '../partner-organisations/partner-organisations.service';
import { PartnerOrganisationStatus } from '../partner-organisations/args/partner-organisations.args';

@Injectable()
export class PostsService extends BaseService(Post) {
  @Inject(forwardRef(() => PostsRepository))
  private readonly postsRepository: PostsRepository;
  @Inject(forwardRef(() => ActivitiesService))
  private readonly activitiesService: ActivitiesService;
  @Inject(forwardRef(() => OrganisationsService))
  private readonly organisationsService: OrganisationsService;
  @Inject(forwardRef(() => NotificationsService))
  private readonly notificationsService: NotificationsService;
  @Inject(forwardRef(() => EventInvitationsService))
  private readonly eventInvitationsService: EventInvitationsService;
  @Inject(forwardRef(() => IncentiveParticipantsService))
  private readonly incentiveParticipantsService: IncentiveParticipantsService;
  @Inject(forwardRef(() => WebinarParticipantsService))
  private readonly webinarParticipantsService: WebinarParticipantsService;
  @Inject(forwardRef(() => StreamUpdatesService))
  private readonly streamUpdatesService: StreamUpdatesService;
  @Inject(forwardRef(() => ProfilesService))
  private readonly profilesService: ProfilesService;
  @Inject(forwardRef(() => EventsService))
  private readonly eventsService: EventsService;
  @Inject(forwardRef(() => WebinarsService))
  private readonly webinarsService: WebinarsService;
  @Inject(forwardRef(() => IncentivesService))
  private readonly incentivesService: IncentivesService;
  @Inject(forwardRef(() => StreamPostsService))
  private readonly streamPostsService: StreamPostsService;
  @Inject(forwardRef(() => MembershipsService))
  private readonly membershipsService: MembershipsService;
  @Inject()
  private readonly sequelize: Sequelize;
  @Inject(WINSTON_MODULE_PROVIDER)
  private readonly logger: Logger;
  @Inject()
  private readonly errorHelper: ErrorHelper;
  @Inject(forwardRef(() => OrganisationLoyaltyPointsService))
  private readonly organisationLoyaltyPointsService: OrganisationLoyaltyPointsService;
  @Inject(forwardRef(() => AchievementsService))
  private readonly achievementsService: AchievementsService;
  @Inject(forwardRef(() => PartnerOrganisationsService))
  private readonly partnerOrganisationsService: PartnerOrganisationsService;

  async createPost(
    createPostDto: CreatePostDto,
    options: {
      currentUser: ICurrentUser;
      transaction?: Transaction;
    },
  ): Promise<Post> {
    this.logger.info('PostsService.createPost', {
      currentUser: options.currentUser?.toLogObject(),
      createPostDto,
    });

    // Check post limits for partner organizations
    if (createPostDto.parentOrgId) {
      // This is a partner post
      const partnership =
        await this.partnerOrganisationsService.findByParentAndChildIds(
          createPostDto.parentOrgId,
          createPostDto.organisationId,
        );

      if (!partnership) {
        throw new BadRequestException(
          'No partnership exists between these organizations',
        );
      }

      if (partnership.status !== PartnerOrganisationStatus.Approved) {
        throw new BadRequestException('Partnership is not in approved status');
      }

      // Count existing posts this month
      const startOfMonth = moment().startOf('month').toDate();
      const endOfMonth = moment().endOf('month').toDate();

      const postsCount = await this.countPartnerPosts({
        childOrgId: createPostDto.organisationId,
        parentOrgId: createPostDto.parentOrgId,
        startDate: startOfMonth,
        endDate: endOfMonth,
      });

      if (postsCount >= partnership.postsLimit) {
        throw new BadRequestException(
          `Monthly post limit of ${
            partnership.postsLimit
          } has been reached for this partnership. The limit will reset on ${moment()
            .endOf('month')
            .add(1, 'day')
            .format('MMMM D, YYYY')}.`,
        );
      }
    }

    const isTransactionSet = !!options.transaction;
    const transaction =
      options.transaction || (await this.sequelize.transaction());

    try {
      // Fetch organization details early for use throughout the method
      const organisation = await this.organisationsService.findById(
        createPostDto.organisationId,
      );

      // Check if the organisation exists
      if (!organisation) {
        throw new HttpException(
          'Organisation not found',
          HttpStatus.BAD_REQUEST,
        );
      }
      createPostDto.text = createPostDto.text.replace(/\n\s*\n\s*\n/g, '\n\n');

      const post = await this.create(
        {
          ...createPostDto,
          seenBy: [options.currentUser.profileId],
        },
        { transaction },
      );

      if (createPostDto.eventId) {
        const eventInvitations = await this.eventInvitationsService.findAll(
          {
            eventId: createPostDto.eventId,
            status: {
              [Op.in]: [
                EventInvitationStatus.Interested,
                EventInvitationStatus.Attending,
              ],
            },
          },
          { transaction },
        );

        for (const eventInvitation of eventInvitations) {
          await this.notificationsService.createNotification(
            {
              ownerProfileId: eventInvitation.profileId,
              profileId: options.currentUser.profileId,
              eventInvitationId: eventInvitation.id,
              type: NotificationType.NewEventUpdate,
              data: {
                postId: post.id,
              },
            },
            {
              transaction,
            },
          );
        }

        const profileIds = eventInvitations.map(user => user.profileId);
        const replacementOrg = await this.organisationsService.findById(
          createPostDto.organisationId,
        );
        const eventDetails = await this.eventsService.findById(
          createPostDto.eventId,
        );
        const replacements = [replacementOrg.name, eventDetails.name];

        await this.notificationsService.sendPushNotification({
          profileIds,
          replacements,
          messageType: NotificationMessage.NewEventUpdate,
          route: encodePathParams(Routes.organisationEvent, {
            vanityId: replacementOrg?.vanityId as string,
            eventId: eventDetails?.id,
          }),
        });
      }

      if (createPostDto.incentiveId) {
        const incentiveParticipants =
          await this.incentiveParticipantsService.findAll(
            {
              incentiveId: createPostDto.incentiveId,
              status: IncentiveParticipantStatus.Registered,
            },
            { transaction },
          );

        for (const incentiveParticipant of incentiveParticipants) {
          await this.notificationsService.createNotification(
            {
              ownerProfileId: incentiveParticipant.profileId,
              profileId: options.currentUser.profileId,
              incentiveParticipantId: incentiveParticipant.id,
              type: NotificationType.NewIncentiveUpdate,
              data: {
                postId: post.id,
              },
            },
            {
              transaction,
            },
          );
        }

        const profileIds = incentiveParticipants.map(user => user.profileId);
        const replacementOrg = await this.organisationsService.findById(
          createPostDto.organisationId,
        );
        const incentiveDetails = await this.incentivesService.findById(
          createPostDto.incentiveId,
        );
        const replacements = [replacementOrg.name, incentiveDetails.name];

        await this.notificationsService.sendPushNotification({
          profileIds,
          replacements,
          messageType: NotificationMessage.NewIncentiveUpdate,
          route: encodePathParams(Routes.organisationIncentive, {
            vanityId: replacementOrg.vanityId,
            incentiveId: incentiveDetails?.id,
          }),
        });
      }

      if (createPostDto.webinarId) {
        const webinarParticipants =
          await this.webinarParticipantsService.findAll(
            {
              webinarId: createPostDto.webinarId,
              status: {
                [Op.in]: [
                  WebinarParticipantStatus.Registered,
                  WebinarParticipantStatus.Speaker,
                  WebinarParticipantStatus.Host,
                  WebinarParticipantStatus.HostAdmin,
                  WebinarParticipantStatus.HiddenHost,
                ],
              },
            },
            { transaction },
          );

        for (const webinarParticipant of webinarParticipants) {
          await this.notificationsService.createNotification(
            {
              ownerProfileId: webinarParticipant.profileId,
              profileId: options.currentUser.profileId,
              webinarParticipantId: webinarParticipant.id,
              type: NotificationType.NewWebinarUpdate,
              data: {
                postId: post.id,
              },
            },
            {
              transaction,
            },
          );
        }

        const profileIds = webinarParticipants.map(user => user.profileId);
        const replacementOrg = await this.organisationsService.findById(
          createPostDto.organisationId,
        );
        const webinarDetails = await this.webinarsService.findById(
          createPostDto.webinarId,
        );
        const replacements = [replacementOrg.name, webinarDetails.name];

        await this.notificationsService.sendPushNotification({
          profileIds,
          replacements,
          messageType: NotificationMessage.NewWebinarUpdate,
          route: encodePathParams(Routes.organisationWebinar, {
            vanityId: replacementOrg.vanityId,
            id: webinarDetails?.id,
          }),
        });
      }

      if (
        createPostDto.eventId ||
        createPostDto.incentiveId ||
        createPostDto.webinarId
      ) {
        const streamUpdateObj = {
          organisationId: createPostDto.organisationId,
          postId: post.id,
        };

        if (createPostDto.text) {
          streamUpdateObj['text'] = createPostDto.text;
        }

        if (createPostDto.eventId) {
          streamUpdateObj['eventId'] = createPostDto.eventId;
        }

        if (createPostDto.incentiveId) {
          streamUpdateObj['incentiveId'] = createPostDto.incentiveId;
        }

        if (createPostDto.webinarId) {
          streamUpdateObj['webinarId'] = createPostDto.webinarId;
        }

        await this.streamUpdatesService.addUpdate(
          createPostDto.profileId,
          streamUpdateObj,
        );
      }

      if (post.status === PostStatus.Live) {
        // add creatpost activity in activities table
        const createdByProfile = await this.profilesService.findById(
          options.currentUser.profileId,
        );

        const currentDayPostActivity =
          await this.organisationLoyaltyPointsService.findAll({
            organisationId: createPostDto.organisationId,
            [Op.and]: [
              Sequelize.literal(
                `DATE_TRUNC('day', "createdAt") = CURRENT_DATE`,
              ),
            ],
          });

        let createdPostsPoints = 0;
        let removedPostsPoints = 0;
        if (currentDayPostActivity.length) {
          currentDayPostActivity.forEach(activity => {
            if (activity.type === ActivityType.RemovePost) {
              const postCreatedAtDate = activity.placeholders.postCreatedAt;
              if (
                postCreatedAtDate &&
                new Date(postCreatedAtDate).toISOString().split('T')[0] ===
                  new Date().toISOString().split('T')[0]
              ) {
                removedPostsPoints += activity.points;
              }
            } else if (activity.type === ActivityType.CreatePost) {
              createdPostsPoints += activity.points;
            }
          });
        }

        if (createdPostsPoints + removedPostsPoints >= 0) {
          // Check if this is a parent organization creating its own post
          const isParentOrg = await this.isParentOrganization(createPostDto.organisationId);          
          // Always award loyalty points to the organization creating the post
          const loyaltyPointEntry =
            await this.organisationLoyaltyPointsService.addPoints({
              type: ActivityType.CreatePost,
              organisationId: createPostDto.organisationId,
              parentOrgId: createPostDto.parentOrgId,
              transaction,
              placeholders: {
                postId: post.id,
                postContent: createPostDto.text,
                streamActivityId: post.id,
                organisationId: createPostDto.organisationId,
                createdById: createdByProfile.id,
                name: createdByProfile.name,
                isParentOrg,
                organisationName: organisation?.name || "",
                organisationVanityId: organisation?.vanityId || ""
              },
            });
          
          // Call the activity logging service
          await this.activitiesService.create(
            {
              profileId: options.currentUser.profileId,
              data: loyaltyPointEntry,
              type: 'CreatePost',
            },
            { transaction },
          );
        }
      }

      // Create the activity in the OrganisationActivity table
      const orgActivityData: createPostActivityDataDto = {
        createdById: options.currentUser.profileId,
        organisationId: createPostDto.organisationId,
        parentOrgId: createPostDto.parentOrgId,
        type: ActivityType.CreatePost,
        postId: post.id,
        data: { 
          postId: post.id, 
          postContent: createPostDto.text,
          parentOrgId: createPostDto.parentOrgId
        },
      };
      // Call the activity logging service for organisation activity
      await this.activitiesService.createOrganisationActivity(orgActivityData, {
        transaction,
      });

      const currentMonth = moment().month();
      const lastPost = await this.findAll(
        {
          organisationId: organisation.id,
        },
        { order: [['createdAt', 'DESC']] },
      );
      const lastPostMonth = lastPost.length
        ? moment(lastPost[0].createdAt).month()
        : -1;

      const monthEndThreeDaysAgo = moment()
        .endOf('month')
        .subtract(3, 'days')
        .toDate();

      if (
        organisation &&
        organisation.hasClubHabloSubscription &&
        post.createdAt < monthEndThreeDaysAgo &&
        (post.status === PostStatus.Live || post.status === PostStatus.Repost)
      ) {
        if (currentMonth !== lastPostMonth) {
          organisation.currentMonthPostCount = 0;
        }

        // Increment the postCount
        const currentMonthPosts = await this.getCurrentMonthPosts(
          organisation.id,
          monthEndThreeDaysAgo,
        );

        await Promise.all([
          this.organisationsService.updateById(
            organisation.id,
            {
              currentMonthPostCount: currentMonthPosts.length + 1,
            },
            { transaction },
          ),
          this.achievementsService.update({
            where: {
              organisationId: organisation.id,
              type: AchievementType.PowerScroller,
            },
            update: { steps: currentMonthPosts.length + 1 },
            transaction,
          }),
        ]);
      }

      if (!isTransactionSet) {
        await transaction.commit();
      }

      return post;
    } catch (e) {
      console.log(e);
      if (!isTransactionSet) {
        await transaction.rollback();
      }

      this.errorHelper.throwHttpException(`PostsService.createPost`, e.message);
    }
  }

  updatePost(
    id: string,
    updatePostInput: UpdatePostInput,
    options: {
      profileId?: string;
    },
  ): Promise<Post> {
    this.logger.info('PostsService.updatePost', {
      id,
      updatePostInput,
      profileId: options.profileId,
    });

    return this.updateById(id, updatePostInput);
  }

  async updatePostScheduleTime(
    id: string,
    updatePostInput: UpdatePostScheduleDateInput,
    options: {
      profileId?: string;
    },
  ): Promise<Post> {
    this.logger.info('PostsService.updatePostScheduleTime', {
      id,
      updatePostInput,
      profileId: options.profileId,
    });

    await this.streamPostsService.updateStreamPostScheduleTime(
      id,
      options.profileId,
      updatePostInput.scheduledAt,
    );

    return this.updateById(id, updatePostInput);
  }

  async removePost(
    id: string,
    options: {
      profileId: string;
      transaction?: Transaction;
    },
  ): Promise<void> {
    this.logger.info('PostsService.removePost', {
      id,
      profileId: options.profileId,
    });
    const post = await this.findById(id);
    const isTransactionSet = !!options.transaction;
    const transaction =
      options.transaction || (await this.sequelize.transaction());
    try {
      if (post.eventId || post.incentiveId || post.webinarId) {
        await this.notificationsService.remove({
          where: {
            data: {
              postId: post.id,
            },
          },
        });
      }
      
      await this.remove({ where: { id }, transaction: options.transaction });

      const startOfMonth = moment().startOf('month').toDate();
      const monthEndThreeDaysAgo = moment()
        .endOf('month')
        .subtract(3, 'days')
        .toDate();
      let postCreatedAt = new Date(post.createdAt);
      if (post.scheduledAt) {
        postCreatedAt = new Date(post.scheduledAt);
      }
      const postCreatedThisMonth =
        postCreatedAt >= startOfMonth && postCreatedAt <= monthEndThreeDaysAgo;
      const organisation = await this.organisationsService.findById(
        post.organisationId,
      );
      if (
        organisation &&
        organisation.currentMonthPostCount > 0 &&
        postCreatedThisMonth
      ) {
        organisation.currentMonthPostCount -= 1;
        await this.organisationsService.updateById(organisation.id, {
          currentMonthPostCount: organisation.currentMonthPostCount,
        });
        const achievementsToUpdate = await this.achievementsService.findAll({
          organisationId: organisation.id,
          type: AchievementType.PowerScroller,
        });
        await Promise.all(
          achievementsToUpdate.map(async achievement => {
            if (achievement.steps > 0) {
              await this.achievementsService.decrementStepsComplete({
                achievement,
                post: post,
              });
            }
          }),
        );
      }

      // Check if this is a partner post
      const isPartnerPost = post.parentOrgId !== null && post.parentOrgId !== undefined;
      
      const createdByProfile = await this.profilesService.findById(
        options.profileId,
      );

      if (isPartnerPost) {
        // For partner posts, find the loyalty points entry for the child organization
        const childOrgResult = await this.organisationLoyaltyPointsService.findOne({
          where: {
            organisationId: organisation.id,
            parentOrganisationId: post.parentOrgId,
            [Op.and]: [
              Sequelize.where(
                Sequelize.fn(
                  'jsonb_extract_path_text',
                  Sequelize.col('placeholders'),
                  'postId',
                ),
                post.id,
              ),
            ],
          },
        });
        
        if (childOrgResult) {
          // Deduct points for partner post
          const variableRewardPoints = childOrgResult && childOrgResult?.points === 200 ? -200 : 0;
          
          await this.organisationLoyaltyPointsService.addPoints({
            type: ActivityType.RemovePost,
            organisationId: organisation.id,
            parentOrgId: post.parentOrgId,
            transaction,
            variableRewardPoints,
            placeholders: {
              postId: post.id,
              deleted: true,
              postCreatedAt: post.createdAt,
              deletedAt: new Date(),
              name: createdByProfile.name,
              createdById: createdByProfile.id,
              parentOrgId: post.parentOrgId,
              isPartnerPost: true,
              organisationName: organisation?.name || "",
              organisationVanityId: organisation?.vanityId || ""
            },
          });
        }
      } else {
        // For regular posts (non-partner posts), find and deduct points from the organization
        const result = await this.organisationLoyaltyPointsService.findOne({
          where: {
            organisationId: organisation.id,
            [Op.and]: [
              Sequelize.where(
                Sequelize.fn(
                  'jsonb_extract_path_text',
                  Sequelize.col('placeholders'),
                  'postId',
                ),
                post.id,
              ),
            ],
          },
        });
        
        if (result) {
          // Deduct points from the organization
          const variableRewardPoints = result && result?.points === 200 ? -200 : 0;
          
          await this.organisationLoyaltyPointsService.addPoints({
            type: ActivityType.RemovePost,
            organisationId: organisation.id,
            transaction,
            variableRewardPoints,
            placeholders: {
              postId: post.id,
              deleted: true,
              postCreatedAt: post.createdAt,
              deletedAt: new Date(),
              name: createdByProfile.name,
              createdById: createdByProfile.id,
              organisationName: organisation?.name || "",
              organisationVanityId: organisation?.vanityId || ""
            },
          });
        }
      }
      
      // Create organization activity entries based on whether it's a partner post or not
      if (isPartnerPost) {        
        // Create activity for the child organization with parent org ID set
        await OrganisationActivity.create(
          {
            createdById: options.profileId,
            organisationId: organisation.id, // Child org ID
            parentOrganisationId: post.parentOrgId, // Set parent org ID
            data: {
              postId: post.id,
              deleted: true,
              name: createdByProfile.name,
              isPartnerPost: true,
              parentOrgId: post.parentOrgId
            },
            type: 'RemovePost',
          },
          { transaction },
        );
      } else {
        // Create activity for regular post
        await OrganisationActivity.create(
          {
            createdById: options.profileId,
            organisationId: organisation.id,
            parentOrganisationId: null,
            data: {
              postId: post.id,
              deleted: true,
              name: createdByProfile.name
            },
            type: 'RemovePost',
          },
          { transaction },
        );
      }

      if (!isTransactionSet) {
        await transaction.commit();
      }
    } catch (e) {
      if (!isTransactionSet) {
        await transaction.rollback();
      }
      this.errorHelper.throwHttpException(`PostsService.removePost`, e.message);
    }
  }

  async seenPost(
    id: string,
    options: {
      profileId: string;
      transaction?: Transaction;
    },
  ): Promise<Post> {
    this.logger.info('PostsService.seenPost', {
      id,
      profileId: options.profileId,
    });

    const isTransactionSet = !!options?.transaction;
    const transaction =
      options.transaction || (await this.sequelize.transaction());

    try {
      const post = await this.findById(id, { transaction });

      if (!post.seenBy.includes(options.profileId)) {
        const updatedPost = await this.updateById(
          id,
          {
            totalViews: (post.totalViews || 0) + 1,
            seenBy: _.uniq([...post.seenBy, options.profileId]),
          },
          { transaction },
        );

        await this.activitiesService.create(
          {
            type: ActivityType.PostSeen,
            profileId: options.profileId,
            organisationId: post.organisationId,
            postId: updatedPost.id,
          },
          {
            transaction,
          },
        );

        const organisation = await this.organisationsService.findById(
          post.organisationId,
          {
            transaction,
          },
        );

        if (!organisation.seenAnyPostBy.includes(options.profileId)) {
          await this.organisationsService.updateById(
            post.organisationId,
            {
              seenAnyPostBy: _.uniq([
                ...organisation.seenAnyPostBy,
                options.profileId,
              ]),
            },
            { transaction },
          );
        }
        if (!isTransactionSet) {
          await transaction.commit();
        }

        return updatedPost;
      } else {
        const updatedPost = await this.updateById(
          id,
          {
            totalViews: (post.totalViews || 0) + 1,
          },
          { transaction },
        );
        if (!isTransactionSet) {
          await transaction.commit();
        }

        return updatedPost;
      }
    } catch (e) {
      if (!isTransactionSet) {
        await transaction.rollback();
      }
      //to do: removed post does not disappear from HomePosts infinite list after being deleted, so triggers this error:
      //this.errorHelper.throwHttpException(`PostsService.seenPost`, e.message);
    }
  }

  async findPosts(
    profileId: string,
    filter: {
      type?: PostType[];
      organisationId?: string;
      parentOrgId?: string;
      eventId?: string;
      incentiveId?: string;
      webinarId?: string;
      updates?: boolean;
      searchText?: string;
    },
    pagination: PaginationArgs,
  ): Promise<PaginatedResult<Post>> {
    this.logger.verbose('PostsService.findPosts', {
      profileId,
      filter,
      pagination,
    });

    const paginationPostResult = await this.postsRepository.findPosts(
      profileId,
      filter,
      pagination,
    );

    return {
      records: paginationPostResult.records,
      totalCount: paginationPostResult.totalCount,
    };
  }

  async scheduledPostList(
    options: {
      currentUser: ICurrentUser;
    },
    organisationId?: string,
    parentOrganisationId?: string,
  ): Promise<Post[]> {
    this.logger.info('PostsService.scheduledPostList', {
      currentUser: options.currentUser?.toLogObject(),
    });

    let where: any = {
      status: PostStatus.Scheduled,
    };

    if (parentOrganisationId && organisationId) {
      where = {
        ...where,
        organisationId: organisationId,
        parentOrgId: parentOrganisationId,
      };
    } else if (organisationId) {
      where = {
        ...where,
        organisationId: organisationId,
        parentOrgId: null ,
      };
    }

    try {
      return await this.findAll(where, {
        order: [['scheduledAt', 'ASC']],
      });
    } catch (e) {
      this.errorHelper.throwHttpException(
        `PostsService.scheduledPostList`,
        e.message,
      );
    }
  }

  async schedulePostsEveryMinScript(): Promise<boolean> {
    this.logger.info('PostsService.schedulePostsEveryMinScript', {});
    const now = moment().format('YYYY-MM-DDTHH:mm:ss');

    const posts = await this.findAll({
      status: PostStatus.Scheduled,
      scheduledAt: {
        [Op.and]: {
          [Op.lt]: moment(now).startOf('minute').add(1, 'minute').toDate(),
        },
      },
    });

    const transaction = await this.sequelize.transaction();

    try {
      if (posts.length) {
        for (const post of posts) {
          const currPost = await this.streamPostsService.getStreamPostById(
            post.id,
          );

          const membership = await this.membershipsService.findMembership(
            post.organisationId,
            post.profileId,
          );

          if (currPost) {
            await this.streamPostsService.publishPost(
              post.profileId,
              currPost,
              membership,
            );

            const updatedPost = await this.updateById(
              post.id,
              {
                status: PostStatus.Live,
              },
              { transaction },
            );

            // Increment the post count if the post is now live
            const organisation = await this.organisationsService.findById(
              updatedPost.organisationId,
            );
            if (organisation && organisation.hasClubHabloSubscription) {
              const currentMonth = moment().month();
              const lastPost = await this.findAll(
                {
                  organisationId: organisation.id,
                },
                { order: [['createdAt', 'DESC']] },
              );
              const lastPostMonth = lastPost.length
                ? moment(lastPost[0].createdAt).month()
                : -1;

              // Check if the post is scheduled for the last 3 days of the month
              const scheduledAt = moment(post.scheduledAt);
              const monthEndThreeDaysAgo = moment()
                .endOf('month')
                .subtract(3, 'days')
                .toDate();

              const isLastThreeDaysOfMonth =
                scheduledAt.isSameOrAfter(monthEndThreeDaysAgo);

              // Check if the post is scheduled for next month
              const isNextMonth = scheduledAt.month() !== currentMonth;

              if (!isLastThreeDaysOfMonth && !isNextMonth) {
                if (currentMonth !== lastPostMonth) {
                  organisation.currentMonthPostCount = 0;
                }

                // Increment the postCount
                const currentMonthPosts = await this.getCurrentMonthPosts(
                  organisation.id,
                  monthEndThreeDaysAgo,
                );

                await Promise.all([
                  this.organisationsService.updateById(
                    organisation.id,
                    {
                      currentMonthPostCount: currentMonthPosts.length + 1,
                    },
                    { transaction },
                  ),
                  this.achievementsService.update({
                    where: {
                      organisationId: organisation.id,
                      type: AchievementType.PowerScroller,
                    },
                    update: { steps: currentMonthPosts.length + 1 },
                    transaction,
                  }),
                ]);
              }
            }

            const createdByProfile = await this.profilesService.findById(
              post.profileId,
            );
            const loyaltyPointEntry =
              await this.organisationLoyaltyPointsService.addPoints({
                type: ActivityType.CreatePost,
                organisationId: updatedPost.organisationId,
                placeholders: {
                  postId: updatedPost.id,
                  postContent: updatedPost.text,
                  streamActivityId: updatedPost.id,
                  createdById: createdByProfile.id,
                  name: createdByProfile.name,
                  organisationName: organisation?.name || "",
                  organisationVanityId: organisation?.vanityId || ""
                },
              });

            await this.activitiesService.create({
              profileId: post.profileId,
              data: loyaltyPointEntry,
              type: 'CreatePost',
            });
          }
        }
      }

      await transaction.commit();
      return true;
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `PostsService.postsScheduled`,
        e.message,
      );
    }
  }

  async getCurrentMonthPosts(
    organisationId: string,
    monthEndThreeDaysAgo: Date,
  ): Promise<string[]> {
    const currentMonthPost = await this.findAll({
      organisationId,
      [Op.or]: [
        {
          [Op.and]: [
            {
              scheduledAt: {
                [Op.gte]: moment().startOf('month').toDate(),
                [Op.lte]: new Date() && monthEndThreeDaysAgo,
              },
            },
            {
              status: PostStatus.Live,
            },
          ],
        },
        {
          [Op.and]: [
            {
              status: {
                [Op.or]: [PostStatus.Live, PostStatus.Repost],
              },
            },
            {
              createdAt: {
                [Op.gte]: moment().startOf('month').toDate(),
                [Op.lte]: monthEndThreeDaysAgo,
              },
            },
          ],
        },
      ],
    });

    return currentMonthPost.map(post => post.id);
  }

  async countPartnerPosts(options: {
    childOrgId: string;
    parentOrgId: string;
    startDate: Date;
    endDate: Date;
  }): Promise<number> {
    this.logger.info('PostsService.countPartnerPosts', options);

    try {
      const count = await this.count({
        organisationId: options.childOrgId,
        parentOrgId: options.parentOrgId,
        createdAt: {
          [Op.between]: [options.startDate, options.endDate],
        },
      });

      return count;
    } catch (error) {
      this.logger.error('Error counting partner posts', error);
      return 0;
    }
  }

  async isParentOrganization(organisationId: string): Promise<boolean> {
    try {
      // Check if this organization has any child organizations
      const childOrgs = await this.partnerOrganisationsService.findAll({
        parentOrganisationId: organisationId,
        status: PartnerOrganisationStatus.Approved,
      });
      
      // If there are child organizations, this is a parent organization
      return childOrgs.length > 0;
    } catch (error) {
      this.logger.error('Error checking if organization is a parent organization', {
        organisationId,
        error,
      });
      return false;
    }
  }
}
