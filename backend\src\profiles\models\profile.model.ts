import moment from 'moment';
import {
  Column,
  Model,
  Table,
  HasMany,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Connection } from '../../connections/models/connection.model';
import { Notification } from '../../notifications/models/notification.model';
import { ObjectType, Field, ID, Int } from '@nestjs/graphql';
import {
  OnlineStatus,
  ProfileConnectionStatus,
  ProfileResponsibility,
  ProfileTypeOfHoliday,
  Region,
} from '../args/profiles.args';
import { DataTypes } from 'sequelize';
import { GraphQLJSON } from 'graphql-type-json';
import { Membership } from '../../memberships/models/membership.model';
import { Organisation } from '../../organisations/models/organisation.model';
import { GettingStartedStep } from '../../getting-started-steps/models/getting-started-step.model';

@ObjectType()
export class ProfileParentOrganisationObj {
  @Field()
  id: string;
}
@Table
@ObjectType()
export class Profile extends Model<Profile> {
  @Field(() => ID)
  @Column({
    primaryKey: true,
    unique: true,
    allowNull: false,
  })
  id: string;

  @Column({ unique: true, allowNull: false })
  authZeroUserId: string;

  @Column
  streamUserId: string;

  @Column
  suggestedFollowCount: number;

  @Field()
  @Column({ allowNull: false })
  name: string;

  @Field()
  @Column({ unique: true, allowNull: false })
  email: string;

  @Field({ nullable: true })
  @Column
  image: string;

  @Field({ nullable: true })
  @Column
  backgroundImage: string;

  @Field({ nullable: true })
  @Column({
    type: DataTypes.DATEONLY,
  })
  dateOfBirth: Date;

  @Field({ nullable: true })
  @Column
  gender: string;

  @Field({ nullable: true })
  @Column({
    type: DataTypes.TEXT,
  })
  bio: string;

  @Field({ nullable: true })
  @Column
  headline: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @Column({
    type: DataTypes.JSON,
  })
  location: any;

  @Field(() => [Region], { nullable: true })
  @Column({
    type: DataTypes.ARRAY(DataTypes.STRING),
  })
  regions: Region[];

  @Field(() => [ProfileResponsibility], { nullable: true })
  @Column({
    type: DataTypes.ARRAY(DataTypes.STRING),
  })
  responsibilities: ProfileResponsibility[];

  @Field(() => [ProfileTypeOfHoliday], { nullable: true })
  @Column({
    type: DataTypes.ARRAY(DataTypes.STRING),
  })
  typesOfHoliday: ProfileTypeOfHoliday[];

  @Field({ nullable: true })
  @Column
  phoneNumber: string;

  @Column
  lastActivityAt: Date;

  @Field()
  @Column({
    type: DataTypes.DATE,
    defaultValue: new Date(),
  })
  lastOnlineAt: Date;

  @Column({
    defaultValue: 0,
  })
  timezoneOffset: number;

  @Field({ nullable: true })
  @Column
  timezone: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @Column({
    type: DataTypes.JSON,
  })
  onboarding: any;

  @Field()
  @Column(DataTypes.VIRTUAL)
  get isComplete(): boolean {
    const fields = ['email', 'name', 'dateOfBirth', 'gender', 'location'];

    for (const field of fields) {
      const dataValue = this.getDataValue(field as keyof this);
      if (!dataValue) {
        return false;
      }
    }

    return true;
  }

  @Field(() => OnlineStatus)
  @Column(DataTypes.VIRTUAL)
  get onlineStatus(): OnlineStatus {
    const seconds = this.lastOnlineAt
      ? moment().diff(this.lastOnlineAt, 'seconds')
      : undefined;
    if (seconds < 70) {
      return OnlineStatus.Online;
    } else if (seconds < 900) {
      return OnlineStatus.Away;
    } else {
      return OnlineStatus.Offline;
    }
  }

  @Field(() => [ProfileParentOrganisationObj], { nullable: true })
  @Column({
    type: DataTypes.ARRAY(DataTypes.JSON),
    defaultValue: [],
  })
  parentOrganisations: ProfileParentOrganisationObj[];

  @Field({ nullable: true })
  @Column({ defaultValue: 0 })
  noOfMobileDevicesLoggedIn: number;

  @Field(() => GraphQLJSON, { nullable: true })
  @Column({
    type: DataTypes.JSONB,
    defaultValue: {
      events: ['push', 'sms', 'desktop', 'email'],
      messages: ['push', 'sms', 'desktop', 'email'],
      webinars: ['push', 'sms', 'desktop', 'email'],
      incentives: ['push', 'sms', 'desktop', 'email'],
      connections: ['push', 'sms', 'desktop', 'email'],
      interaction: ['push', 'sms', 'desktop', 'email'],
      invitations: ['push', 'sms', 'desktop', 'email'],
      mentionsInPosts: ['push', 'sms', 'desktop', 'email'],
      followSuggestions: ['push', 'sms', 'desktop', 'email'],
      organisationsYouManage: ['push', 'sms', 'desktop', 'email'],
      posts: ['push', 'sms', 'desktop', 'email'],
    },
  })
  notificationPreference: any;

  @Field()
  @Column
  createdAt: Date;

  @Field()
  @Column
  updatedAt: Date;

  @Field({ nullable: true })
  @Column
  isEmailVerified: boolean;

  @Field()
  @Column
  receiveNotificationEmails: boolean;

  @Field()
  @Column
  receiveNewMessagesEmails: boolean;

  @Field(() => ProfileConnectionStatus, { nullable: true })
  status?: ProfileConnectionStatus;

  @Column({
    type: DataTypes.ARRAY(DataTypes.STRING),
  })
  profileIdsConnections: string[];

  @Column({
    type: DataTypes.ARRAY(DataTypes.STRING),
  })
  profileIdsInvitationSent: string[];

  @Column({
    type: DataTypes.ARRAY(DataTypes.STRING),
  })
  profileIdsInvitationSentRejected: string[];

  @Column({
    type: DataTypes.ARRAY(DataTypes.STRING),
  })
  profileIdsInvitationReceived: string[];

  @Column({
    type: DataTypes.ARRAY(DataTypes.STRING),
  })
  profileIdsInvitationReceivedRejected: string[];

  @Field({
    nullable: true,
  })
  @Column
  sellHolidays: boolean;

  @Field({
    nullable: true,
  })
  @Column({
    type: DataTypes.DATE,
    allowNull: true,
  })
  showGettingStartedFeedAt: Date;

  @Field(() => GraphQLJSON, {
    nullable: true,
  })
  @Column({
    type: DataTypes.JSONB,
  })
  migrationFlags: any;

  @HasMany(() => Connection, 'connectionProfileId')
  connections: Connection[];

  @Field()
  @Column({ defaultValue: 0 })
  connectionsCount: number;

  @HasMany(() => Notification, 'ownerProfileId')
  notifications: Notification[];

  @HasMany(() => Membership, 'profileId')
  memberships: Membership[];

  @HasMany(() => GettingStartedStep, 'profileId')
  gettingStartedSteps: GettingStartedStep[];

  @ForeignKey(() => Membership)
  @Column
  primaryMembershipId: string;

  @BelongsTo(() => Membership, 'primaryMembershipId')
  primaryMembership: Membership;

  @Field(() => ID, { nullable: true })
  @Column({
    type: DataTypes.STRING,
    allowNull: true,
    references: { model: 'Organisations', key: 'id' },
  })
  referredByOrganisationId: string;

  @BelongsTo(() => Organisation, 'referredByOrganisationId')
  referringOrganisation: Organisation;

  static ConnectionsIncludeRule = {
    model: Connection,
    as: 'connections',
    include: [
      {
        model: Profile,
        as: 'profile',
        include: [
          {
            as: 'memberships',
            model: Membership,
            attributes: [
              'id',
              'position',
              'isPrimary',
              'organisationName',
              'organisationId',
              'hasClubHabloSubscription',
              'status',
            ],
            include: [
              {
                as: 'organisation',
                model: Organisation,
              },
            ],
          },
        ],
      },
    ],
  };

  @Field(() => String, { nullable: true })
  activeTier?: string;
}
