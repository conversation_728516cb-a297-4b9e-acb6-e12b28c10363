import { Inject, UseGuards, forwardRef } from '@nestjs/common';
import {
  Args,
  Mutation,
  Parent,
  Query,
  ResolveField,
  Resolver,
  Int,
} from '@nestjs/graphql';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';

import { GqlAuthGuard } from '../authz/graphql-auth.guard';
import {
  FollowersResult,
  PaginatedResult,
  ProfilesResult,
} from '../common/args/paginated-result';
import {
  CurrentUser,
  ICurrentUser,
} from '../common/decorators/current-user.decorator';
import { connectionArgs } from '../connections/args/connection.args';
import { ConnectionsService } from '../connections/connections.service';
import { Connection } from '../connections/models/connection.model';
import {
  Routes,
  encodePathParams,
} from '../email/helpers/notificationLayoutHelper';
import { ExperiencesService } from '../experiences/experiences.service';
import { Experience } from '../experiences/models/experience.model';
import { FollowersArgs } from '../followers/args/followers.args';
import { FollowersService } from '../followers/followers.service';
import { Follower } from '../followers/models/follower.model';
import { MembershipsArgs } from '../memberships/args/memberships.args';
import { MembershipsServiceHelper } from '../memberships/helpers/memberships.service.helper';
import { MembershipsService } from '../memberships/memberships.service';
import { Membership } from '../memberships/models/membership.model';
import {
  NotificationMessage,
  NotificationType,
} from '../notifications/args/notifications.args';
import { NotificationsService } from '../notifications/notifications.service';
import { OrganisationsService } from '../organisations/organisations.service';
import { ConnectionsResult } from './args/paginated-result';
import { ProfileConnectionStatus, ProfilesArgs } from './args/profiles.args';
import { ProfileParentOrganisationInput } from './dto/add-profile-parent-organisation';
import { MobileDevicesLoggedInCountInput } from './dto/mobile-devices-logged-in-count-input';
import { UpdateProfileInput } from './dto/update-profile.input';
import { Profile } from './models/profile.model';
import { ProfilesService } from './profiles.service';
import { LogActiveSessionInput } from './dto/log-daily-activity-input';
import { LoyaltyPointsService } from '../loyalty-points/loyalty-points.service';
import { Op } from 'sequelize';
import { UpdateProfileReferringOrganisationInput } from './dto/update-profile-referring-organisation';

@Resolver(() => Profile)
export class ProfilesResolver {
  @Inject(forwardRef(() => ProfilesService))
  private readonly profilesService: ProfilesService;
  @Inject(forwardRef(() => ConnectionsService))
  private readonly connectionsService: ConnectionsService;
  @Inject(forwardRef(() => ExperiencesService))
  private readonly experiencesService: ExperiencesService;
  @Inject(forwardRef(() => MembershipsService))
  private readonly membershipsService: MembershipsService;
  @Inject(forwardRef(() => FollowersService))
  private readonly followersService: FollowersService;
  @Inject(forwardRef(() => NotificationsService))
  private readonly notificationsService: NotificationsService;
  @Inject(forwardRef(() => OrganisationsService))
  private readonly organisationsService: OrganisationsService;
  @Inject(WINSTON_MODULE_PROVIDER)
  private readonly logger: Logger;
  @Inject(forwardRef(() => LoyaltyPointsService))
  private readonly loyaltyPointsService: LoyaltyPointsService;

  private async getMemberships(
    profile: Profile,
    membershipArgs?: MembershipsArgs,
  ): Promise<Membership[]> {
    if (profile.memberships && profile.memberships.length > 0) {
      return MembershipsServiceHelper.filter(
        profile.memberships,
        membershipArgs,
      );
    }

    this.logger.verbose('ProfilesResolver.getMemberships (field resolver)', {
      profileId: profile.id,
      membershipArgs,
    });

    if (membershipArgs?.filter?.isPrimary === true) {
      const primaryMembership = await this.primaryMembership(profile);

      if (!primaryMembership) return [];

      return MembershipsServiceHelper.filter(
        [primaryMembership],
        membershipArgs,
      );
    }

    return this.membershipsService.findAll(
      {
        profileId: profile.id,
        ...MembershipsServiceHelper.getQueryParams(membershipArgs),
      },
      { useCache: true },
    );
  }

  @Query(() => Profile)
  @UseGuards(GqlAuthGuard)
  async profile(
    @CurrentUser() user: ICurrentUser,
    @Args('id', { nullable: true }) id?: string,
  ): Promise<Profile> {
    this.logger.verbose('ProfilesResolver.profile (query)', {
      user: user.toLogObject(),
      id,
    });

    if (id) {
      return this.profilesService.findById(id);
    }
    return this.profilesService.findByCurrentUser(user);
  }

  @Query(() => ProfilesResult)
  @UseGuards(GqlAuthGuard)
  profiles(
    @CurrentUser() user: ICurrentUser,
    @Args() profileArgs: ProfilesArgs,
  ): Promise<PaginatedResult<Profile>> {
    this.logger.verbose('ProfilesResolver.profiles (query)', {
      user: user.toLogObject(),
      profileArgs,
    });

    return this.profilesService.findProfiles(
      user.profileId,
      {
        organisationId: profileArgs?.filter?.organisationId,
        followerStatus: profileArgs?.filter?.followerStatus,
        membershipStatus: profileArgs?.filter?.membershipStatus,
        membershipPermissions: profileArgs?.filter?.membershipPermissions,
        status: profileArgs?.filter?.status,
        searchText: profileArgs?.filter?.searchText,
        includeOwnProfile: profileArgs?.filter?.includeOwnProfile || false,
        searchLocationText: profileArgs?.filter?.searchLocationText,
        responsibilities: profileArgs?.filter?.responsibilities,
        typesOfHoliday: profileArgs?.filter?.typesOfHoliday,
        connectedOrgProfiles: profileArgs?.filter?.connectedOrgProfiles,
        searchOrgNameAndJob: true,
        isFuzzySearch: profileArgs?.filter?.isFuzzySearch,
      },
      {
        first: profileArgs?.first,
        after: profileArgs?.after,
        sortBy: profileArgs?.sortBy,
        sortOrder: profileArgs?.sortOrder,
        offset: profileArgs?.offset,
      },
    );
  }

  @Mutation(() => Profile)
  @UseGuards(GqlAuthGuard)
  async updateProfile(
    @CurrentUser() user: ICurrentUser,
    @Args('profileData') profileData: UpdateProfileInput,
  ): Promise<Profile> {
    this.logger.verbose('ProfilesResolver.updateProfile (mutation)', {
      user: user.toLogObject(),
      profileData,
    });

    return this.profilesService.updateProfile(user.profileId, profileData);
  }

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  removeProfile(@CurrentUser() user: ICurrentUser): Promise<boolean> {
    this.logger.verbose('ProfilesResolver.removeProfile (mutation)', {
      user: user.toLogObject(),
    });

    return this.profilesService.removeProfile(user.profileId);
  }

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  async removeOtherProfile(
    @CurrentUser() user: ICurrentUser,
    @Args('profileId') profileId: string,
  ): Promise<boolean> {
    this.logger.verbose('ProfilesResolver.removeOtherProfile (mutation)', {
      user: user.toLogObject(),
    });

    const membershipDetails = await this.membershipsService.findOne({
      profileId: user.profileId,
      organisationId: {
        [Op.in]: ['aJtx73K8SpeNpyz5MNm7bL', 'sTDUCYXXaBpjrBHDUExgpE'],
      },
    });

    if (membershipDetails) {
      const profile = await this.profilesService.findById(profileId);
      if (!profile) {
        this.logger.error(`Profile not found for id ${profileId}`);
        throw new Error('Profile not found.');
      }
      return this.profilesService.removeProfile(profileId);
    } else {
      this.logger.error(
        `You are not authorized to Delete the user with Id ${profileId}.`,
      );
      throw new Error('You are not authorized to Delete the user.');
    }
  }

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  setPrimaryOrganisation(
    @CurrentUser() user: ICurrentUser,
    @Args('organisationId', { nullable: true }) organisationId?: string,
    @Args('organisationName', { nullable: true }) organisationName?: string,
    @Args('position', { nullable: true }) position?: string,
    @Args('preApprove', { nullable: true }) preApprove?: boolean,
  ): Promise<boolean> {
    this.logger.verbose('ProfilesResolver.setPrimaryOrganisation (mutation)', {
      user: user.toLogObject(),
      organisationId,
      organisationName,
      position,
      preApprove,
    });

    return this.profilesService.setPrimaryOrganisation(
      user.profileId,
      organisationId,
      organisationName,
      position,
      preApprove,
    );
  }

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  sendEmailVerification(@CurrentUser() user: ICurrentUser): Promise<boolean> {
    this.logger.verbose('ProfilesResolver.setPrimaryOrganisation (mutation)', {
      user: user.toLogObject(),
    });

    return this.profilesService.sendEmailVerification(user.profileId);
  }

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  changeEmail(
    @CurrentUser() user: ICurrentUser,
    @Args('newEmail') newEmail: string,
  ): Promise<boolean> {
    this.logger.verbose('ProfilesResolver.changeEmail (mutation)', {
      user: user.toLogObject(),
    });

    return this.profilesService.changeEmail(
      user.profileId,
      newEmail.toLowerCase(),
    );
  }

  @ResolveField('connections', () => [Connection])
  async connections(@Parent() profile: Profile): Promise<Connection[]> {
    if (profile.connections) return profile.connections;

    this.logger.verbose('ProfilesResolver.connections (field resolver)', {
      profileId: profile.id,
    });

    //whilst we're checking connections, check for email invites
    if (profile.isComplete === true) {
      await this.profilesService.checkForEmailInvitations(
        profile.email,
        profile.id,
      );
    }

    return this.connectionsService.findAll(
      {
        connectionProfileId: profile.id,
      },
      { useCache: true },
    );
  }

  @ResolveField('connectionsViaPagination', () => ConnectionsResult)
  async connectionsViaPagination(
    @CurrentUser() user: ICurrentUser,
    @Parent() profile: Profile,
    @Args() connectionArgs?: connectionArgs,
  ): Promise<PaginatedResult<Connection>> {
    this.logger.verbose(
      'ProfilesResolver.connectionsViaPagination (field resolver)',
      {
        profileId: profile.id,
      },
    );
    return await this.connectionsService.findConnections(
      user.profileId,
      {
        profileId: connectionArgs?.filter?.profileId,
        connectionProfileId: profile.id,
      },
      {
        first: connectionArgs?.first,
        after: connectionArgs?.after,
        sortBy: connectionArgs?.sortBy,
        sortOrder: connectionArgs?.sortOrder,
      },
    );
  }

  @ResolveField('experiences', () => [Experience])
  experiences(@Parent() profile: Profile): Promise<Experience[]> {
    this.logger.verbose('ProfilesResolver.experiences (field resolver)', {
      profileId: profile.id,
    });

    return this.experiencesService.findAll(
      {
        profileId: profile.id,
      },
      { useCache: true },
    );
  }

  @ResolveField('memberships', () => [Membership])
  async memberships(
    @Parent() profile: Profile,
    @Args() membershipArgs?: MembershipsArgs,
  ): Promise<Membership[]> {
    return this.getMemberships(profile, membershipArgs);
  }

  @ResolveField('activeMemberships', () => [Membership])
  async activeMemberships(
    @Parent() profile: Profile,
    @Args() membershipArgs?: MembershipsArgs,
  ): Promise<Membership[]> {
    return this.getMemberships(profile, membershipArgs);
  }

  @ResolveField('nonPrimaryMemberships', () => [Membership])
  async nonPrimaryMemberships(
    @Parent() profile: Profile,
    @Args() membershipArgs?: MembershipsArgs,
  ): Promise<Membership[]> {
    membershipArgs.filter.isPrimary = false;
    return this.getMemberships(profile, membershipArgs);
  }

  @ResolveField('following', () => FollowersResult)
  following(
    @Parent() profile: Profile,
    @Args() followerArgs?: FollowersArgs,
  ): Promise<PaginatedResult<Follower>> {
    this.logger.verbose('ProfilesResolver.following (field resolver)', {
      profileId: profile.id,
      followerArgs,
    });

    return this.followersService.findFollowers(
      profile.id,
      {
        profileId: followerArgs?.filter?.profileId ?? profile.id,
        status: followerArgs?.filter?.status,
        includeActiveProfile: followerArgs?.filter?.includeActiveProfile,
      },
      {
        first: followerArgs?.first,
        after: followerArgs?.after,
        sortBy: followerArgs?.sortBy,
        sortOrder: followerArgs?.sortOrder,
      },
    );
  }

  @ResolveField('isEmailVerified', () => Boolean)
  isEmailVerified(@Parent() profile: Profile): Promise<boolean> {
    this.logger.verbose('ProfilesResolver.isEmailVerified (field resolver)', {
      profileId: profile.id,
    });

    return this.profilesService.isEmailVerified(profile);
  }

  @ResolveField('streamChannelId', () => String, { nullable: true })
  async streamChannelId(
    @CurrentUser() user: ICurrentUser,
    @Parent() profile: Profile,
  ): Promise<string> {
    if (!profile.profileIdsConnections.includes(user.profileId)) {
      return null;
    }

    this.logger.verbose('ProfilesResolver.streamChannelId (field resolver)', {
      profileId: profile.id,
    });

    const connection = await this.connectionsService.findOne(
      {
        profileId: user.profileId,
        connectionProfileId: profile.id,
      },
      {
        attributes: ['streamChannelId'],
        useCache: true,
      },
    );

    return connection?.streamChannelId;
  }

  @ResolveField('status', () => ProfileConnectionStatus, { nullable: true })
  async status(
    @CurrentUser() user: ICurrentUser,
    @Parent() profile: Profile,
  ): Promise<ProfileConnectionStatus> {
    const connection = profile.profileIdsConnections?.find(
      id => id === user.profileId,
    );
    const invitationSent = profile.profileIdsInvitationSent?.find(
      id => id === user.profileId,
    );
    const invitationSentRejected =
      profile.profileIdsInvitationSentRejected?.find(
        id => id === user.profileId,
      );
    const invitationReceived = profile.profileIdsInvitationReceived?.find(
      id => id === user.profileId,
    );
    const invitationReceivedRejected =
      profile.profileIdsInvitationReceivedRejected?.find(
        id => id === user.profileId,
      );

    if (connection) {
      return ProfileConnectionStatus.Connection;
    } else if (invitationSent) {
      return ProfileConnectionStatus.InvitationReceived;
    } else if (invitationSentRejected) {
      return ProfileConnectionStatus.InvitationReceivedRejected;
    } else if (invitationReceived) {
      return ProfileConnectionStatus.InvitationSent;
    } else if (invitationReceivedRejected) {
      return ProfileConnectionStatus.InvitationSentRejected;
    }
    return ProfileConnectionStatus.None;
  }

  @ResolveField('primaryMembership', () => Membership, { nullable: true })
  async primaryMembership(@Parent() profile: Profile): Promise<Membership> {
    if (!profile.primaryMembershipId) return null;
    if (profile.primaryMembership) return profile.primaryMembership;

    this.logger.verbose('ProfilesResolver.primaryMembership (field resolver)', {
      profileId: profile.id,
    });

    return this.membershipsService.findById(profile.primaryMembershipId);
  }

  @Mutation(() => Profile)
  @UseGuards(GqlAuthGuard)
  async addProfileParentOrganisation(
    @CurrentUser() user: ICurrentUser,
    @Args('data') data: ProfileParentOrganisationInput,
  ): Promise<Profile> {
    this.logger.verbose(
      'ProfilesResolver.addProfileParentOrganisation (mutation)',
      {
        data,
      },
    );

    return await this.profilesService.addProfileParentOrganisation(data);
  }

  @Mutation(() => Profile)
  @UseGuards(GqlAuthGuard)
  async removeProfileParentOrganisation(
    @CurrentUser() user: ICurrentUser,
    @Args('data') data: ProfileParentOrganisationInput,
  ): Promise<Profile> {
    this.logger.verbose(
      'ProfilesResolver.removeProfileParentOrganisation (mutation)',
      {
        data,
      },
    );

    const profile = await this.profilesService.removeProfileParentOrganisation(
      data,
    );

    // follower and member logic
    await this.profilesService.removeMembershipAndFollowersFromParentOrg(data);

    return profile;
  }

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  async acceptMemberByParentAdmin(
    @CurrentUser() user: ICurrentUser,
    @Args('data') data: ProfileParentOrganisationInput,
  ): Promise<boolean> {
    this.logger.verbose(
      'ProfilesResolver.acceptMemberByParentAdmin (mutation)',
      {
        data,
      },
    );

    return await this.profilesService.addMembershipAndFollowersToParentOrg(
      data,
      true,
    );
  }

  @Mutation(() => Profile)
  @UseGuards(GqlAuthGuard)
  async declineMemberByParentAdmin(
    @CurrentUser() user: ICurrentUser,
    @Args('data') data: ProfileParentOrganisationInput,
  ): Promise<Profile> {
    this.logger.verbose(
      'ProfilesResolver.declineMemberByParentAdmin (mutation)',
      {
        data,
      },
    );

    const parent = await this.profilesService.removeProfileParentOrganisation(
      data,
    );

    //notification of decline request
    await this.notificationsService.createNotification({
      ownerProfileId: data.profileId,
      profileId: data.profileId,
      organisationId: data.parentId,
      type: NotificationType.ParentMembershipDeclined,
    });

    const profileIds = [data.profileId];
    const replacementOrg = await this.organisationsService.findById(
      data.parentId,
    );
    const replacements = [replacementOrg.name];

    await this.notificationsService.sendPushNotification({
      profileIds,
      replacements,
      messageType: NotificationMessage.ParentMembershipDeclined,
      route: encodePathParams(Routes.profile),
    });

    return parent;
  }

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  async mobileDevicesLoggedInCount(
    @CurrentUser() user: ICurrentUser,
    @Args('data') data: MobileDevicesLoggedInCountInput,
  ): Promise<boolean> {
    this.logger.verbose(
      'ProfilesResolver.mobileDevicesLoggedInCount (mutation)',
      {
        data,
      },
    );

    return await this.profilesService.mobileDevicesLoggedInCount(data);
  }

  @Mutation(() => Boolean)
  @UseGuards(GqlAuthGuard)
  async logActiveSession(
    @CurrentUser() user: ICurrentUser,
    @Args('data') data: LogActiveSessionInput,
  ): Promise<boolean> {
    this.logger.verbose('ProfilesResolver.logActiveSession (mutation)', {
      profileId: user.profileId,
      ...data,
    });

    await this.profilesService.logActiveSession({
      profileId: user.profileId,
      data,
    });

    return true;
  }

  @ResolveField('activeTier', () => String)
  async activeTier(@Parent() profile: Profile): Promise<string> {
    const loyaltyPoint = await this.loyaltyPointsService.findOneByProfileId(
      profile.id,
    );
    return this.loyaltyPointsService.getTierFromIndex(
      loyaltyPoint?.activeTier ?? 0,
    );
  }

  @Mutation(() => Profile)
  @UseGuards(GqlAuthGuard)
  async updateProfileReferringOrganisation(
    @CurrentUser() user: ICurrentUser,
    @Args('data') data: UpdateProfileReferringOrganisationInput,
  ): Promise<Profile> {
    this.logger.verbose(
      'ProfilesResolver.updateProfileReferringOrganisation (mutation)',
      {
        user: user.toLogObject(),
        data,
      },
    );

    // Only allow users to update their own profile's referring organization
    if (user.profileId !== data.profileId) {
      throw new Error('You can only update your own profile');
    }

    return this.profilesService.updateProfileReferringOrganisation(
      data.profileId,
      data.referringOrganisationId,
    );
  }

  // @Mutation(() => Profile)
  // @UseGuards(GqlAuthGuard)
  // async updateNotificationPreference(
  //   @CurrentUser() user: ICurrentUser,
  //   @Args('data') data: UpdateNotificationPreferenceDto,
  // ): Promise<Profile> {
  //   this.logger.verbose(
  //     'ProfilesResolver.mobileDevicesLoggedInCount (mutation)',
  //     {
  //       data,
  //     },
  //   );

  //   return await this.profilesService.updateNotificationPreference(data);
  // }
}
