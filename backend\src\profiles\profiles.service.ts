import {
  Inject,
  Injectable,
  OnApplicationBootstrap,
  forwardRef,
} from '@nestjs/common';
import getTimezoneOffset from 'get-timezone-offset';
import { omit } from 'lodash';
import moment from 'moment-timezone';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Op, Transaction } from 'sequelize';
import { Sequelize } from 'sequelize-typescript';
import {
  emailInvitationRegistrationActivityDataDto,
  organisationFollowActivityDataDto,
  emptyActivityDataDto,
  variableLoginRewardActivityDataDto,
} from '../activities/dto/create-user-activity-data.dto';
import { Logger } from 'winston';
import { ActivitiesService } from '../activities/activities.service';
import { ActivityType } from '../activities/args/activities.args';
import { AuthZeroService } from '../authz/authz.service';
import { AutofollowService } from '../autofollow/autofollow.service';
import { ChatService } from '../chat/chat.service';
import { PaginatedResult } from '../common/args/paginated-result';
import { PaginationArgs } from '../common/args/pagination.args';
import { BaseService } from '../common/base.service';
import { ICurrentUser } from '../common/decorators/current-user.decorator';
import { ErrorHelper } from '../common/helpers/error';
import { Underscore as _ } from '../common/helpers/underscore';
import config from '../config/config';
import { ConnectionRequestsService } from '../connection-requests/connection-requests.service';
import { ConnectionRequestStatus } from '../connection-requests/models/connection-request.model';
import { ConnectionsService } from '../connections/connections.service';
import {
  Routes,
  encodePathParams,
} from '../email/helpers/notificationLayoutHelper';
import { EventInvitationsService } from '../event-invitations/event-invitations.service';
import { ExperiencesService } from '../experiences/experiences.service';
import { StreamFollowersService } from '../feeds-followers/stream-followers.service';
import { FollowersService } from '../followers/followers.service';
import { FollowerStatus } from '../followers/models/follower.model';
import { GettingStartedStepEnum } from '../getting-started-steps/args/getting-started-steps.args';
import { GettingStartedStepsService } from '../getting-started-steps/getting-started-steps.service';
import { IncentiveBookingsService } from '../incentive-bookings/incentive-bookings.service';
import { IncentiveParticipantsService } from '../incentive-participants/incentive-participants.service';
import { MembershipsService } from '../memberships/memberships.service';
import {
  MembershipActionType,
  MembershipPermission,
  MembershipStatus,
} from '../memberships/models/membership.model';
import {
  NotificationMessage,
  NotificationType,
} from '../notifications/args/notifications.args';
import { NotificationsService } from '../notifications/notifications.service';
import { OrganisationsService } from '../organisations/organisations.service';
import { PartnershipRequestsService } from '../partnership-requests/partnership-requests.service';
import { PartnershipsService } from '../partnerships/partnerships.service';
import { PostsService } from '../posts/posts.service';
import { WebinarParticipantsService } from '../webinar-participants/webinar-participants.service';
import { EmailInvitationsService } from './../email-invitations/email-invitations.service';
import {
  DeviceLogInCount,
  ProfileConnectionStatus,
  ProfileResponsibility,
  ProfileTypeOfHoliday,
} from './args/profiles.args';
import { ProfileParentOrganisationInput } from './dto/add-profile-parent-organisation';
import { MobileDevicesLoggedInCountInput } from './dto/mobile-devices-logged-in-count-input';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { UpdateProfileInput } from './dto/update-profile.input';
import { ProfilesServiceHelper } from './helpers/profiles.service.helper';
import { Profile } from './models/profile.model';
import { ProfilesRepository } from './profiles.repository';
import { LogActiveSessionInput } from './dto/log-daily-activity-input';
import { AchievementsService } from '../achievements/achievements.service';
import { StreamService } from '../feeds/stream.service';

@Injectable()
export class ProfilesService
  extends BaseService(Profile)
  implements OnApplicationBootstrap
{
  @Inject(forwardRef(() => ProfilesRepository))
  private readonly profilesRepository: ProfilesRepository;
  @Inject(forwardRef(() => ProfilesServiceHelper))
  private readonly helper: ProfilesServiceHelper;
  @Inject(forwardRef(() => MembershipsService))
  private readonly membershipsService: MembershipsService;
  @Inject(forwardRef(() => AutofollowService))
  private readonly autofollowService: AutofollowService;
  @Inject(forwardRef(() => ConnectionsService))
  private readonly connectionsService: ConnectionsService;
  @Inject(forwardRef(() => ConnectionRequestsService))
  private readonly connectionRequestsService: ConnectionRequestsService;
  @Inject(forwardRef(() => FollowersService))
  private readonly followersService: FollowersService;
  @Inject(forwardRef(() => OrganisationsService))
  private readonly organisationsService: OrganisationsService;
  @Inject(forwardRef(() => NotificationsService))
  private readonly notificationsService: NotificationsService;
  @Inject(forwardRef(() => ActivitiesService))
  private readonly activitiesService: ActivitiesService;
  @Inject(forwardRef(() => ChatService))
  private readonly chatService: ChatService;
  @Inject(forwardRef(() => AuthZeroService))
  private readonly authZeroService: AuthZeroService;
  @Inject(forwardRef(() => EventInvitationsService))
  private readonly eventInvitationsService: EventInvitationsService;
  @Inject(forwardRef(() => ExperiencesService))
  private readonly experiencesService: ExperiencesService;
  @Inject(forwardRef(() => IncentiveBookingsService))
  private readonly incentiveBookingsService: IncentiveBookingsService;
  @Inject(forwardRef(() => IncentiveParticipantsService))
  private readonly incentiveParticipantsService: IncentiveParticipantsService;
  @Inject(forwardRef(() => WebinarParticipantsService))
  private readonly webinarParticipantsService: WebinarParticipantsService;
  @Inject(forwardRef(() => PartnershipRequestsService))
  private readonly partnershipRequestsService: PartnershipRequestsService;
  @Inject(forwardRef(() => PostsService))
  private readonly postsService: PostsService;
  @Inject(forwardRef(() => EmailInvitationsService))
  private readonly emailInvitationsService: EmailInvitationsService;
  @Inject(forwardRef(() => StreamFollowersService))
  private readonly streamFollowersService: StreamFollowersService;
  @Inject(forwardRef(() => PartnershipsService))
  private readonly partnershipsService: PartnershipsService;
  @Inject(forwardRef(() => GettingStartedStepsService))
  private readonly gettingStartedStepsService: GettingStartedStepsService;
  @Inject(forwardRef(() => AchievementsService))
  private readonly achievementsService: AchievementsService;
  @Inject(forwardRef(() => StreamService))
  private readonly streamService: StreamService;
  @Inject()
  private readonly sequelize: Sequelize;
  @Inject(WINSTON_MODULE_PROVIDER)
  private readonly logger: Logger;
  @Inject()
  private readonly errorHelper: ErrorHelper;

  async findByCurrentUser(user: ICurrentUser): Promise<Profile> {
    this.logger.verbose('ProfilesService.findByCurrentUser', {
      user: user.toLogObject(),
    });

    let profile = await this.findById(user.profileId);

    const updateDto: UpdateProfileDto = {
      lastActivityAt: new Date(),
    };

    if (!profile) {
      const splitSubId = user.sub.split('|');
      const getUserId =
        splitSubId[0].toLowerCase() === 'apple'
          ? splitSubId[1].split('.')[1]
          : splitSubId[1];

      profile = await this.create({
        id: getUserId,
        authZeroUserId: user.sub,
        email: user.email,
        image: user.picture,
        name: user.name?.indexOf('@') > -1 ? '' : user.name || '',
      });

      // In production, follow Hablo by default
      if (config.SERVICE === 'api') {
        const hablo = await this.organisationsService.findById(
          'aJtx73K8SpeNpyz5MNm7bL',
        );

        if (hablo) {
          await this.followersService.create({
            profileId: profile.id,
            organisationId: hablo.id,
            status: FollowerStatus.Active,
          });

          try {
            await this.streamFollowersService.follow(
              { profileId: profile.id },
              hablo.id,
            );
          } catch (e) {
            this.logger.error(
              `ProfilesService.findByCurrentUser Error: ${e.message}`,
              e.response?.body,
            );
          }

          await this.activitiesService.createUserActivity({
            type: ActivityType.OrganisationFollower,
            schema: organisationFollowActivityDataDto,
            data: {
              follow: true,
            },
            organisationId: hablo.id,
            profileId: profile.id,
            addLoyaltyPoints: true,
            placeholders: {
              name: hablo.name,
              id: hablo.id,
              vanityId: hablo.vanityId,
            },
          });
        }
      }
    }

    profile = await this.updateById(profile.id, updateDto);

    if (!profile?.streamUserId) {
      await this.chatService.upsertUser(profile.id);
    }

    return profile;
  }

  async findProfiles(
    profileId: string,
    filter: {
      organisationId?: string;
      followerStatus?: FollowerStatus[];
      membershipStatus?: MembershipStatus[];
      membershipPermissions?: MembershipPermission[];
      status?: ProfileConnectionStatus[];
      searchText?: string;
      includeOwnProfile?: boolean;
      searchLocationText?: string;
      responsibilities?: ProfileResponsibility[];
      typesOfHoliday?: ProfileTypeOfHoliday[];
      connectedOrgProfiles?: boolean;
      searchOrgNameAndJob?: boolean;
      isFuzzySearch?: boolean;
      sellHolidays?: boolean;
    },
    pagination: PaginationArgs,
  ): Promise<PaginatedResult<Profile>> {
    this.logger.verbose('ProfilesService.findProfiles', {
      profileId,
      filter,
      pagination,
    });

    return this.profilesRepository.findProfiles(profileId, filter, pagination);
  }

  async removeProfile(profileId: string): Promise<boolean> {
    this.logger.info('ProfilesService.removeProfile', {
      profileId,
    });

    const profile = await this.findById(profileId);

    if (!profile) return true;

    const deleteduserId = '_deleted-user_';
    const deleteduser = await this.findById(deleteduserId);
    if (!deleteduser) {
      await this.create({
        id: deleteduserId,
        authZeroUserId: deleteduserId,
        email: '<EMAIL>',
        name: 'A deleted user',
      });
    }

    const ownerships = await this.membershipsService.findAll({
      profileId: {
        [Op.eq]: profile.id,
      },
      permissions: {
        [Op.overlap]: [MembershipPermission.Owner],
      },
      status: MembershipStatus.Active,
    });

    if (ownerships.length) {
      this.errorHelper.throwHttpException(
        `ProfilesService.removeProfile`,
        'Cannot remove user whilst owner of an organisation',
      );
    }

    const transaction = await this.sequelize.transaction();

    try {
      await this.notificationsService.update({
        where: {
          profileId: profile.id,
          type: {
            [Op.notIn]: [
              NotificationType.InvitationAccepted,
              NotificationType.InvitationReceived,
              NotificationType.MembershipRequested,
            ],
          },
        },
        update: {
          profileId: deleteduserId,
        },
        transaction,
      });
      await this.notificationsService.remove({
        where: { ownerProfileId: profile.id },
        transaction,
      });
      await this.notificationsService.remove({
        where: { profileId: profile.id },
        transaction,
      });
      await this.followersService.remove({
        where: { profileId: profile.id },
        transaction,
      });
      await this.connectionsService.remove({
        where: { profileId: profile.id },
        transaction,
      });
      await this.connectionsService.remove({
        where: { connectionProfileId: profile.id },
        transaction,
      });
      await this.connectionRequestsService.remove({
        where: { senderProfileId: profile.id },
        transaction,
      });
      await this.connectionRequestsService.remove({
        where: { receiverProfileId: profile.id },
        transaction,
      });
      await this.eventInvitationsService.remove({
        where: { profileId: profile.id },
        transaction,
      });
      await this.eventInvitationsService.update({
        where: { inviterProfileId: profile.id },
        update: {
          inviterProfileId: deleteduserId,
        },
        transaction,
      });
      await this.experiencesService.remove({
        where: { profileId: profile.id },
        transaction,
      });
      await this.incentiveBookingsService.remove({
        where: { profileId: profile.id },
        transaction,
      });
      await this.incentiveParticipantsService.remove({
        where: { profileId: profile.id },
        transaction,
      });
      await this.incentiveParticipantsService.update({
        where: { inviterProfileId: profile.id },
        update: {
          inviterProfileId: deleteduserId,
        },
        transaction,
      });
      await this.webinarParticipantsService.remove({
        where: { profileId: profile.id },
        transaction,
      });
      await this.webinarParticipantsService.update({
        where: { inviterProfileId: profile.id },
        update: {
          inviterProfileId: deleteduserId,
        },
        transaction,
      });
      await this.membershipsService.remove({
        where: { profileId: profile.id },
        transaction,
      });
      await this.partnershipRequestsService.update({
        where: { senderProfileId: profile.id },
        update: {
          senderProfileId: deleteduserId,
        },
        transaction,
      });
      await this.postsService.update({
        where: { profileId: profile.id },
        update: {
          profileId: deleteduserId,
        },
        transaction,
      });

      await this.removeById(profile.id, { transaction });

      await transaction.commit();

      await this.authZeroService.removeUser(profile.authZeroUserId);
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `ProfilesService.removeProfile`,
        e.message,
      );
    }

    return true;
  }

  async setPrimaryOrganisation(
    profileId: string,
    organisationId?: string,
    organisationName?: string,
    position?: string,
    preApprove?: boolean,
  ): Promise<boolean> {
    this.logger.info('ProfilesService.setPrimaryOrganisation', {
      profileId,
      organisationId,
      organisationName,
      position,
    });

    if (!organisationName && !organisationId) {
      this.errorHelper.throwHttpException(
        'ProfilesService.setPrimaryOrganisation',
        'OrganisationId or OrganisationName should be set',
      );
    }

    if (organisationName && organisationId) {
      this.errorHelper.throwHttpException(
        'ProfilesService.setPrimaryOrganisation',
        `OrganisationId and OrganisationName can't be set at the same time`,
      );
    }

    let oldPrimaryMembershipOrganisationId = null;
    let newActivePrimaryMembershipOrganisationId = null;

    const transaction = await this.sequelize.transaction();

    try {
      let primaryMembership = await this.membershipsService.findOne(
        {
          profileId,
          isPrimary: true,
        },
        { transaction },
      );

      if (organisationId) {
        const existingMembership = await this.membershipsService.findOne(
          {
            profileId,
            organisationId,
          },
          { transaction },
        );

        if (
          existingMembership &&
          existingMembership.status === MembershipStatus.Rejected
        ) {
          throw new Error(
            `You can't set this primary Organisation, you've been rejected earlier`,
          );
        }

        if (
          primaryMembership &&
          primaryMembership.organisationId !== organisationId
        ) {
          if (
            !primaryMembership.permissions.includes(MembershipPermission.Owner)
          ) {
            await this.membershipsService.removeById(primaryMembership.id, {
              transaction,
            });
          } else {
            await this.membershipsService.updateById(
              primaryMembership.id,
              { isPrimary: false },
              {
                transaction,
              },
            );
          }

          if (primaryMembership.organisationId) {
            // add primary org parent unfollow here
            const organisation = await this.organisationsService.findById(
              primaryMembership.organisationId,
              { transaction },
            );
            if (organisation.parentOrganisations.length) {
              for (const parent of organisation.parentOrganisations) {
                await this.removeMembershipAndFollowersFromParentOrg({
                  profileId,
                  parentId: parent.id,
                });
              }
            }

            await this.membershipsService.removeExistingMemberships(profileId, {
              transaction,
            });
            oldPrimaryMembershipOrganisationId =
              primaryMembership.organisationId;
          }

          primaryMembership = null;
        }

        if (primaryMembership) {
          await this.membershipsService.updateById(
            primaryMembership.id,
            {
              position: position || primaryMembership.position || '',
            },
            { transaction },
          );
        } else {
          if (existingMembership) {
            await this.membershipsService.updateById(
              existingMembership.id,
              {
                isPrimary: true,
                position: position || existingMembership.position || '',
              },
              { transaction },
            );

            newActivePrimaryMembershipOrganisationId =
              existingMembership.organisationId;

            //remove user's parents if found
            const userParents = await this.findById(profileId);

            if (userParents.parentOrganisations.length) {
              for (const parent of userParents.parentOrganisations) {
                await this.removeProfileParentOrganisation({
                  profileId,
                  parentId: parent.id,
                });

                await this.removeMembershipAndFollowersFromParentOrg({
                  profileId,
                  parentId: parent.id,
                });
              }
            }

            if (existingMembership.organisationId) {
              // add primary org parent follow here
              const organisation = await this.organisationsService.findById(
                existingMembership.organisationId,
                { transaction },
              );
              if (organisation.parentOrganisations.length) {
                for (const parent of organisation.parentOrganisations) {
                  await this.addMembershipAndFollowersToParentOrg(
                    {
                      profileId,
                      parentId: parent.id,
                    },
                    false,
                  );
                }
              }
            }
          } else {
            const organisation = await this.organisationsService.findById(
              organisationId,
              { transaction },
            );

            await this.membershipsService.create(
              {
                isPrimary: true,
                organisationId,
                profileId,
                status: MembershipStatus.Pending,
                permissions: [MembershipPermission.Linked],
                position,
                isAutoApprove: preApprove ? true : false,
              },
              { transaction },
            );

            if (organisation.isPublic) {
              await this.followersService.addOrApproveFollower(
                organisationId,
                profileId,
                undefined,
              );

              //remove user's parents if found
              const userParents = await this.findById(profileId);

              if (userParents.parentOrganisations.length) {
                for (const parent of userParents.parentOrganisations) {
                  await this.removeProfileParentOrganisation({
                    profileId,
                    parentId: parent.id,
                  });

                  await this.removeMembershipAndFollowersFromParentOrg({
                    profileId,
                    parentId: parent.id,
                  });
                }
              }

              // add primary org parent follow here
              if (organisation.parentOrganisations.length) {
                for (const parent of organisation.parentOrganisations) {
                  await this.addMembershipAndFollowersToParentOrg(
                    {
                      profileId,
                      parentId: parent.id,
                    },
                    false,
                  );
                }
              }
            }

            const organisationMemberships =
              await this.membershipsService.findAll(
                {
                  organisationId,
                  status: MembershipStatus.Active,
                  permissions: {
                    [Op.overlap]: [
                      MembershipPermission.Owner,
                      MembershipPermission.Admin,
                      MembershipPermission.HiddenAdmin,
                      MembershipPermission.Manager,
                    ],
                  },
                },
                { transaction },
              );

            for (const membership of organisationMemberships) {
              await this.notificationsService.createNotification(
                {
                  ownerProfileId: membership.profileId,
                  profileId: profileId,
                  membershipId: membership.id,
                  type: preApprove
                    ? NotificationType.MembershipAutoApprove
                    : NotificationType.MembershipRequested,
                },
                {
                  transaction,
                },
              );
            }

            const profileIds = organisationMemberships.map(
              user => user.profileId,
            );
            const organisationDetails =
              await this.organisationsService.findById(organisationId);
            const profileDetails = await this.findById(profileId);

            const message = preApprove
              ? `${profileDetails.name} has joined ${organisationDetails.name} using a signup invite link.`
              : `${profileDetails.name} has requested to join ${organisationDetails.name}. View this request to confirm their employment.`;

            await this.notificationsService.sendPushNotification({
              profileIds,
              messageType: preApprove
                ? NotificationMessage.MembershipAutoApprove
                : NotificationMessage.MembershipRequested,
              messageContent: message,
              route: preApprove
                ? ''
                : encodePathParams(Routes.organisationSettingsEmployees, {
                    organisationId: organisationDetails?.id,
                  }),
            });
          }
        }
      } else {
        if (primaryMembership && primaryMembership.organisationId) {
          if (
            !primaryMembership.permissions.includes(MembershipPermission.Owner)
          ) {
            await this.membershipsService.removeById(primaryMembership.id, {
              transaction,
            });
          } else {
            await this.membershipsService.updateById(
              primaryMembership.id,
              { isPrimary: false },
              {
                transaction,
              },
            );
          }

          // add primary org parent unfollow here
          const organisation = await this.organisationsService.findById(
            primaryMembership.organisationId,
            { transaction },
          );
          if (organisation.parentOrganisations.length) {
            for (const parent of organisation.parentOrganisations) {
              await this.removeMembershipAndFollowersFromParentOrg({
                profileId,
                parentId: parent.id,
              });
            }
          }

          oldPrimaryMembershipOrganisationId = primaryMembership.organisationId;

          if (primaryMembership.organisationId) {
            await this.membershipsService.removeExistingMemberships(profileId, {
              transaction,
            });
          }

          primaryMembership = null;
        }

        if (primaryMembership) {
          await this.membershipsService.updateById(
            primaryMembership.id,
            {
              position: position || primaryMembership.position || '',
              organisationName,
            },
            { transaction },
          );
        } else {
          await this.membershipsService.create(
            {
              isPrimary: true,
              organisationName,
              profileId,
              status: MembershipStatus.Active,
              permissions: [],
              position,
            },
            { transaction },
          );
        }
      }

      await transaction.commit();
    } catch (e) {
      await transaction.rollback();
      oldPrimaryMembershipOrganisationId = null;
      newActivePrimaryMembershipOrganisationId = null;

      this.errorHelper.throwHttpException(
        `ProfilesService.setPrimaryOrganisation`,
        e.message,
      );
    }

    const primaryMembership = await this.membershipsService.findOne({
      profileId,
      isPrimary: true,
    });

    await this.updateById(profileId, {
      primaryMembershipId: primaryMembership ? primaryMembership.id : null,
    });

    try {
      await this.streamFollowersService.updatePrimaryOrganisation(
        profileId,
        oldPrimaryMembershipOrganisationId,
        newActivePrimaryMembershipOrganisationId,
      );
    } catch (e) {
      this.logger.error(
        `StreamFollowersService Error: ${e.message}`,
        e.response?.body,
      );
    }

    return true;
  }

  async sendEmailVerification(profileId: string): Promise<boolean> {
    this.logger.verbose('ProfilesService.sendEmailVerification', {
      profileId,
    });

    const profile = await this.findById(profileId);
    return this.authZeroService.sendEmailVerification(profile.authZeroUserId);
  }

  async changeEmail(profileId: string, newEmail: string): Promise<boolean> {
    this.logger.verbose('ProfilesService.changeEmail', {
      profileId,
    });

    const existingProfile = await this.findOne({
      email: newEmail,
    });

    if (existingProfile) {
      this.errorHelper.throwHttpException(
        `ProfilesService.changeEmail`,
        `The new email you specified is already in use by another Hablo user account.`,
      );
    }

    const authZeroUsers = await this.authZeroService.getUsersByEmail(newEmail);

    if (authZeroUsers.length > 0) {
      this.errorHelper.throwHttpException(
        `ProfilesService.changeEmail`,
        `There is a duplicate email in our authentication system (Auth0). <NAME_EMAIL> for assistance.`,
      );
    }

    const profile = await this.findById(profileId);

    if (profile.email === newEmail) {
      this.errorHelper.throwHttpException(
        `ProfilesService.changeEmail`,
        `The new email specified is the same as the email you already have set.`,
      );
    }

    await this.authZeroService.changeEmail(profile.authZeroUserId, newEmail);

    await this.updateById(profileId, {
      email: newEmail,
      isEmailVerified: false,
    });

    return true;
  }

  async isEmailVerified(profile: Profile): Promise<boolean> {
    this.logger.verbose('ProfilesService.isEmailVerified', {
      profileId: profile.id,
    });

    if (profile.isEmailVerified) {
      return true;
    }

    const authZeroUser = await this.authZeroService.getAuthZeroUser(
      profile.authZeroUserId,
    );

    if (authZeroUser['email_verified']) {
      this.logger.verbose(
        'ProfilesService.isEmailVerified email verified by auth0',
      );
      await this.updateById(profile.id, { isEmailVerified: true });

      const transaction = await this.sequelize.transaction();

      try {
        // if employee auto-joined using link, approve membership here
        const primaryMembership = await this.membershipsService.findOne({
          profileId: profile.id,
          isPrimary: true,
        });

        if (primaryMembership) {
          if (primaryMembership.status === MembershipStatus.Pending) {
            if (primaryMembership.isAutoApprove) {
              const organisation = await this.organisationsService.findById(
                primaryMembership.organisationId,
              );
              const user = await this.findById(profile.id);

              const getDomain = user.email.split('@');
              const emailDomain = getDomain[1];
              const findDomain = Array.isArray(organisation.preApprovedDomains)
                ? organisation.preApprovedDomains.includes(emailDomain)
                : '';

              if (findDomain) {
                await this.membershipsService.updateMembershipStatus(
                  primaryMembership.organisationId,
                  profile.id,
                  MembershipActionType.Accept,
                  { currentProfileId: profile.id, transaction },
                );
              }
            }
          }
        }
        await transaction.commit();
      } catch (e) {
        await transaction.rollback();
        this.errorHelper.throwHttpException(
          `ProfilesService.isEmailVerified`,
          e.message,
        );
      }

      return true;
    }

    return false;
  }

  async updateProfile(
    id: string,
    updateProfileInput: UpdateProfileInput,
  ): Promise<Profile> {
    this.logger.info('ProfilesService.updatePost', {
      id,
      updateProfileInput,
    });

    const profile = await this.findById(id);

    const updateProfileDto: UpdateProfileDto = omit(
      updateProfileInput,
      'useAutofollow',
    );
    const useAutofollow = updateProfileInput.useAutofollow;

    if (updateProfileInput.timezone) {
      updateProfileDto.timezoneOffset = getTimezoneOffset(
        updateProfileInput.timezone,
      );
    }

    updateProfileDto.bio &&
      (updateProfileDto.bio = updateProfileDto.bio.replace(
        /\n\s*\n\s*\n/g,
        '\n\n',
      ));

    if (!profile.notificationPreference) {
      let preference;

      if (profile.receiveNotificationEmails) {
        preference = {
          events: ['push', 'sms', 'desktop', 'email'],
          messages: profile.receiveNewMessagesEmails
            ? ['push', 'sms', 'desktop', 'email']
            : ['push', 'sms', 'desktop'],
          webinars: ['push', 'sms', 'desktop', 'email'],
          incentives: ['push', 'sms', 'desktop', 'email'],
          connections: ['push', 'sms', 'desktop', 'email'],
          interaction: ['push', 'sms', 'desktop', 'email'],
          invitations: ['push', 'sms', 'desktop', 'email'],
          mentionsInPosts: ['push', 'sms', 'desktop', 'email'],
          followSuggestions: ['push', 'sms', 'desktop', 'email'],
          organisationsYouManage: ['push', 'sms', 'desktop', 'email'],
        };
      } else {
        preference = {
          events: ['push', 'sms', 'desktop'],
          messages: profile.receiveNewMessagesEmails
            ? ['push', 'sms', 'desktop', 'email']
            : ['push', 'sms', 'desktop'],
          webinars: ['push', 'sms', 'desktop'],
          incentives: ['push', 'sms', 'desktop'],
          connections: ['push', 'sms', 'desktop'],
          interaction: ['push', 'sms', 'desktop'],
          invitations: ['push', 'sms', 'desktop'],
          mentionsInPosts: ['push', 'sms', 'desktop'],
          followSuggestions: ['push', 'sms', 'desktop'],
          organisationsYouManage: ['push', 'sms', 'desktop'],
        };
      }

      updateProfileDto.notificationPreference = preference;
    }

    const updatedProfile = await this.updateById(id, updateProfileDto);

    if (
      updatedProfile.email &&
      updatedProfile.name &&
      updatedProfile.dateOfBirth &&
      updatedProfile.gender &&
      updatedProfile.location
    ) {
      await this.gettingStartedStepsService.createGettingStartedStep({
        step: GettingStartedStepEnum.FillBasicDetails,
        profileId: updatedProfile.id,
      });
    }

    if (updatedProfile.headline) {
      await this.gettingStartedStepsService.createGettingStartedStep({
        step: GettingStartedStepEnum.WriteHeadlineBio,
        profileId: updatedProfile.id,
      });
    }

    if (
      updatedProfile.image &&
      !updatedProfile.image.includes('gravatar.com')
    ) {
      await this.gettingStartedStepsService.createGettingStartedStep({
        step: GettingStartedStepEnum.AddProfilePic,
        profileId: updatedProfile.id,
      });
    }

    if (useAutofollow) {
      await this.autofollowService.followRegionOrgs(
        updatedProfile.id,
        updateProfileInput.regions,
      );
    }

    if (updateProfileInput.name || updateProfileInput.image) {
      await this.chatService.upsertUser(updatedProfile.id);
    }

    if (
      updatedProfile.streamUserId &&
      (updateProfileInput.primaryMembershipId ||
        updateProfileInput.name ||
        updateProfileInput.image)
    )
      await this.streamService.updateUser(updatedProfile.id);

    if (updatedProfile.isComplete === true) {
      await this.checkForEmailInvitations(
        updatedProfile.email,
        updatedProfile.id,
      );
      await this.gettingStartedStepsService.createGettingStartedStep({
        step: GettingStartedStepEnum.AddWork,
        profileId: updatedProfile.id,
      });
    }

    await this.achievementsService.addProfilePerfectAchievement({
      profileId: updatedProfile.id,
    });

    return updatedProfile;
  }

  async addToProfileIdArray(
    fieldName: string,
    id: string,
    options: { profileId: string; transaction?: Transaction },
  ): Promise<void> {
    const profile = await this.findById(options.profileId, {
      transaction: options.transaction,
    });

    if (!profile) return;

    await this.updateById(
      profile.id,
      {
        [fieldName]: _.uniq([...profile[fieldName], id]),
      },
      { transaction: options.transaction },
    );
  }

  async removeFromProfileIdArray(
    fieldName: string,
    id: string,
    options: { profileId: string; transaction?: Transaction },
  ): Promise<void> {
    const profile = await this.findById(options.profileId, {
      transaction: options.transaction,
    });

    if (!profile) return;

    await this.updateById(
      profile.id,
      {
        [fieldName]: _.uniq(
          profile[fieldName].filter(savedId => savedId !== id),
        ),
      },
      { transaction: options.transaction },
    );
  }

  async onApplicationBootstrap(): Promise<void> {
    if (process.env.NODE_ENV !== 'development') {
      await this.updatePrimaryMembershipIds();
      await this.updateProfileIds();
    }
  }

  async updatePrimaryMembershipIds(): Promise<void> {
    const profiles = await this.findAll({
      primaryMembershipId: null,
      id: {
        [Op.notLike]: 'fake_%',
      },
    });

    for (const profile of profiles) {
      await this.updatePrimaryMembershipId(profile);
    }
  }

  async updatePrimaryMembershipId(
    profile: Profile,
    transaction?: Transaction,
  ): Promise<void> {
    const primaryMembership = await this.membershipsService.findOne(
      {
        profileId: profile.id,
        isPrimary: true,
      },
      { transaction },
    );

    if (primaryMembership) {
      await this.updateById(
        profile.id,
        {
          primaryMembershipId: primaryMembership.id,
        },
        { transaction },
      );
    }
  }

  async updateProfileIds(): Promise<void> {
    const profiles = await this.findAll({
      profileIdsConnections: [],
      profileIdsInvitationSent: [],
      profileIdsInvitationSentRejected: [],
      profileIdsInvitationReceived: [],
      profileIdsInvitationReceivedRejected: [],
      id: {
        [Op.notLike]: 'fake_%',
      },
    });

    for (const profile of profiles) {
      const profileIdsConnections = await this.helper.getConnectionProfileIds(
        profile.id,
      );
      const profileIdsInvitationSent =
        await this.helper.getInvitationSentProfileIds(profile.id);
      const profileIdsInvitationSentRejected =
        await this.helper.getInvitationSentRejectedProfileIds(profile.id);
      const profileIdsInvitationReceived =
        await this.helper.getInvitationReceivedProfileIds(profile.id);
      const profileIdsInvitationReceivedRejected =
        await this.helper.getInvitationReceivedRejectedProfileIds(profile.id);
      await this.updateById(profile.id, {
        profileIdsConnections,
        profileIdsInvitationSent,
        profileIdsInvitationSentRejected,
        profileIdsInvitationReceived,
        profileIdsInvitationReceivedRejected,
      });
    }
  }

  async checkForEmailInvitations(
    invitedEmail: string,
    profileId: string,
  ): Promise<void> {
    this.logger.verbose('ProfileService.helper.checkForEmailInvitations', {
      profileId,
      invitedEmail,
    });

    const invites = await this.emailInvitationsService.findAll({
      senderProfileId: {
        [Op.ne]: profileId,
      },
      invitedEmail,
      sentConnectionRequestId: {
        [Op.eq]: null,
      },
    });

    const profile = await this.findById(profileId);

    for (const invite of invites) {
      const existingConnectionRequest =
        await this.connectionRequestsService.findOne({
          [Op.or]: [
            {
              receiverProfileId: profileId,
              senderProfileId: invite.senderProfileId,
            },
            {
              receiverProfileId: invite.senderProfileId,
              senderProfileId: profileId,
            },
          ],
        });
      if (!existingConnectionRequest) {
        const connectionRequest = await this.connectionRequestsService.connect({
          senderProfileId: invite.senderProfileId,
          receiverProfileId: profileId,
          status: ConnectionRequestStatus.Pending,
        });
        await this.emailInvitationsService.updateById(invite.id, {
          sentConnectionRequestId: connectionRequest.id,
        });

        // Check if loyalty points were already added for this invitation
        const previousActivity = await this.activitiesService.findOne({
          type: ActivityType.EmailInvitationRegistration,
          profileId: invite.senderProfileId,
          data: {
            [Op.contains]: {
              receiverProfileId: profileId,
              senderProfileId: invite.senderProfileId,
            },
          },
        });

        await this.activitiesService.createUserActivity({
          type: ActivityType.EmailInvitationRegistration,
          schema: emailInvitationRegistrationActivityDataDto,
          data: {
            senderProfileId: invite.senderProfileId,
            receiverProfileId: profileId,
          },
          profileId: invite.senderProfileId,
          addLoyaltyPoints: !previousActivity,
          placeholders: {
            email: invitedEmail,
            name: profile.name,
            userId: profile.id,
          },
        });

        await this.achievementsService.upsertIndustryInfluencerAchievement({
          profileId: invite.senderProfileId,
        });
      } else {
        await this.emailInvitationsService.updateById(invite.id, {
          sentConnectionRequestId: existingConnectionRequest.id,
        });
      }
    }
  }

  async addProfileParentOrganisation(
    data: ProfileParentOrganisationInput,
  ): Promise<Profile> {
    this.logger.info('ProfileService.addProfileParentOrganisation', {
      data,
    });

    const transaction = await this.sequelize.transaction();

    try {
      const profile = await this.findById(data.profileId);

      if (!profile) {
        throw new Error(`Profile not found`);
      }

      const profileMembership = await this.membershipsService.findOne({
        profileId: data.profileId,
        isPrimary: true,
        status: MembershipStatus.Active,
        organisationId: {
          [Op.ne]: null,
        },
      });

      if (profileMembership) {
        throw new Error(
          `You cannot add your parent as you are assigned to an organisation.`,
        );
      }

      const existingMembership = await this.membershipsService.findOne({
        profileId: data.profileId,
        status: MembershipStatus.Active,
        organisationId: data.parentId,
      });

      if (existingMembership) {
        return profile;
      }

      const parentOrganisation = await this.organisationsService.findById(
        data.parentId,
      );

      if (!parentOrganisation) {
        throw new Error(`Parent Organisation does not exist`);
      }

      const parentOrgs = profile.parentOrganisations;

      if (parentOrgs.length === 1) {
        throw new Error(`Cannot add more than 1 parent organisation`);
      }

      const index = parentOrgs.findIndex(x => x.id === data.parentId);

      if (parentOrgs.length && index > -1) {
        throw new Error(`Parent Organisation already added`);
      }

      parentOrgs.push({ id: data.parentId });

      await this.updateById(
        profile.id,
        {
          parentOrganisations: parentOrgs,
        },
        { transaction },
      );

      // Create member entry with status pending and send notification to parent's admin
      await this.membershipsService.create(
        {
          isPrimary: false,
          organisationId: data.parentId,
          profileId: data.profileId,
          status: MembershipStatus.Pending,
          permissions: [MembershipPermission.Member],
        },
        { transaction },
      );

      //notification of request
      const organisationMemberships = await this.membershipsService.findAll(
        {
          organisationId: data.parentId,
          status: MembershipStatus.Active,
          permissions: {
            [Op.overlap]: [
              MembershipPermission.Owner,
              MembershipPermission.Admin,
              MembershipPermission.HiddenAdmin,
              MembershipPermission.Manager,
            ],
          },
        },
        { transaction },
      );

      for (const membership of organisationMemberships) {
        await this.notificationsService.createNotification(
          {
            ownerProfileId: membership.profileId,
            profileId: data.profileId,
            membershipId: membership.id,
            type: NotificationType.ParentMembershipRequested,
          },
          {
            transaction,
          },
        );
      }

      await transaction.commit();

      const profileIds = organisationMemberships.map(user => user.profileId);
      const replacements = [profile.name, parentOrganisation.name];

      await this.notificationsService.sendPushNotification({
        profileIds,
        replacements,
        messageType: NotificationMessage.ParentMembershipRequested,
        route: encodePathParams(Routes.organisationSettingsMembers, {
          organisationId: data.parentId,
        }),
      });

      return profile;
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `ProfileService.addProfileParentOrganisation`,
        e.message,
      );
    }
  }

  async removeProfileParentOrganisation(
    data: ProfileParentOrganisationInput,
  ): Promise<Profile> {
    this.logger.info('ProfileService.removeProfileParentOrganisation', {
      data,
    });

    const transaction = await this.sequelize.transaction();

    try {
      const profile = await this.findById(data.profileId);

      if (!profile) {
        throw new Error(`Profile not found`);
      }

      const parentOrganisation = await this.organisationsService.findById(
        data.parentId,
      );

      if (!parentOrganisation) {
        throw new Error(`Parent Organisation not exists`);
      }

      const parentOrgs = profile.parentOrganisations;
      const index = parentOrgs.findIndex(x => x.id === data.parentId);

      if (parentOrgs.length && index < 0) {
        throw new Error(`Parent Organisation not found`);
      }

      parentOrgs.splice(index, 1);

      await this.updateById(
        profile.id,
        {
          parentOrganisations: parentOrgs,
        },
        { transaction },
      );

      await this.membershipsService.remove({
        where: {
          profileId: data.profileId,
          organisationId: data.parentId,
        },
        transaction: transaction,
      });

      await transaction.commit();
      return profile;
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `ProfileService.removeProfileParentOrganisation`,
        e.message,
      );
    }
  }

  async addMembershipAndFollowersToParentOrg(
    data: ProfileParentOrganisationInput,
    sendNotification: boolean,
  ): Promise<any> {
    this.logger.info('ProfileService.addMembershipAndFollowersToParentOrg', {
      data,
    });

    const transaction = await this.sequelize.transaction();

    try {
      // changing status of added child with primary = false and permission = member in parent to active
      await this.membershipsService.addMemberToParent(
        data.parentId,
        data.profileId,
        null,
        { transaction },
      );

      if (sendNotification) {
        const membership = await this.membershipsService.findOne({
          organisationId: data.parentId,
          profileId: data.profileId,
        });

        //notification of approval
        await this.notificationsService.createNotification(
          {
            ownerProfileId: membership.profileId,
            profileId: data.profileId,
            membershipId: membership.id,
            type: NotificationType.ParentMembershipAccepted,
          },
          {
            transaction,
          },
        );

        const profileIds = [membership.profileId];
        const replacementOrg = await this.organisationsService.findById(
          data.parentId,
        );
        const replacements = [replacementOrg.name];

        await this.notificationsService.sendPushNotification({
          profileIds,
          replacements,
          messageType: NotificationMessage.ParentMembershipAccepted,
          route: encodePathParams(Routes.profile),
        });
      }

      await transaction.commit();
      return true;
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `ProfileService.addMembershipAndFollowersToParentOrg`,
        e.message,
      );
    }
  }

  async removeMembershipAndFollowersFromParentOrg(
    data: ProfileParentOrganisationInput,
  ): Promise<any> {
    this.logger.info(
      'ProfileService.removeMembershipAndFollowersFromParentOrg',
      {
        data,
      },
    );

    const transaction = await this.sequelize.transaction();

    try {
      // removing child memberships with primary = false and permission = member in parent
      await this.membershipsService.removeMemberFromParent(
        data.parentId,
        data.profileId,
        null,
        { transaction },
      );

      //unfollow parent feed already done in above function

      await transaction.commit();
      return true;
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `ProfileService.removeMembershipAndFollowersFromParentOrg`,
        e.message,
      );
    }
  }

  async mobileDevicesLoggedInCount(
    data: MobileDevicesLoggedInCountInput,
  ): Promise<boolean> {
    this.logger.info('ProfilesService.mobileDevicesLoggedInCount', {
      data,
    });

    const transaction = await this.sequelize.transaction();

    try {
      const profile = await this.findById(data.profileId);

      if (!profile) {
        throw new Error(`Profile not found`);
      }

      if (data.type === DeviceLogInCount.Increment) {
        await this.updateById(data.profileId, {
          noOfMobileDevicesLoggedIn: profile.noOfMobileDevicesLoggedIn + 1,
        });
      } else if (
        data.type === DeviceLogInCount.Decrement &&
        profile.noOfMobileDevicesLoggedIn > 0
      ) {
        await this.updateById(data.profileId, {
          noOfMobileDevicesLoggedIn: profile.noOfMobileDevicesLoggedIn - 1,
        });
      }

      await transaction.commit();
      return true;
    } catch (e) {
      await transaction.rollback();
      this.errorHelper.throwHttpException(
        `ProfilesService.mobileDevicesLoggedInCount`,
        e.message,
      );
    }
  }

  async logActiveSession({
    profileId,
    data,
  }: {
    profileId: string;
    data: LogActiveSessionInput;
  }): Promise<void> {
    this.logger.info('ProfilesService.logActiveSession', {
      profileId,
      ...data,
    });

    const { notificationEnabled, variableMinutes, variableRewardPoints } = data;
    // Find current user's timezone
    const profile = await this.findById(profileId);
    const timezone = profile.timezone || 'UTC';

    const startOfDay = moment.tz(timezone).startOf('day').utc().toDate();
    const endOfDay = moment.tz(timezone).endOf('day').utc().toDate();

    if (!variableMinutes && !variableRewardPoints) {
      // Check if any activity exists for the day
      const existingLoginActivity = await this.activitiesService.findOne({
        profileId,
        type: ActivityType.ActiveSession,
        createdAt: {
          [Op.between]: [startOfDay, endOfDay],
        },
      });
      if (!existingLoginActivity) {
        /**
         * Add achievement for daily login activity.
         */
        await this.achievementsService.addHotStreakAchievement({
          profileId,
        });

        /**
         * Check for last login activity record.
         * If the last entry was a day ago, then take that day's streak and add it by 1.
         * Else reset the streak.
         */
        const lastLoginActivity = await this.activitiesService.findOne(
          {
            profileId,
            type: ActivityType.ActiveSession,
          },
          {
            order: [['createdAt', 'DESC']],
          },
        );

        let newStreakCount = 1;
        if (lastLoginActivity) {
          const createdAt = lastLoginActivity.createdAt;
          const startOfLastActivity = moment(createdAt)
            .tz(timezone)
            .startOf('day')
            .utc();
          const diffDays = moment(startOfDay).diff(startOfLastActivity, 'days');

          if (diffDays > 1) {
            newStreakCount = 1;
          } else {
            newStreakCount = lastLoginActivity.data.streakCount + 1;
          }
        }

        await this.activitiesService.createUserActivity({
          type: ActivityType.ActiveSession,
          profileId,
          data: {
            streakCount: newStreakCount,
          },
          schema: emptyActivityDataDto,
        });

        /**
         * Send in-app notification to the user if the daily streak count is one of the following number.
         */
        const streakNotificationCount = [
          3, 5, 10, 20, 30, 50, 100, 200, 300, 365,
        ];
        const streakCount = await this.activitiesService.getUserLoginStreak({
          profileId,
        });

        if (streakNotificationCount.includes(streakCount)) {
          await this.notificationsService.createNotification(
            {
              ownerProfileId: profileId,
              type: NotificationType.DailyLoginStreak,
              profileId,
              data: {
                streakCount,
              },
            },
            {},
          );
        }
      }
    } else if (
      profile.sellHolidays &&
      variableMinutes &&
      variableRewardPoints
    ) {
      // Check if any activity exists for the day for VariableLoginReward
      const existingLoginActivity = await this.activitiesService.findOne({
        profileId,
        type: ActivityType.VariableLoginReward,
        createdAt: {
          [Op.between]: [startOfDay, endOfDay],
        },
      });
      if (!existingLoginActivity) {
        await this.activitiesService.createUserActivity({
          type: ActivityType.VariableLoginReward,
          profileId,
          data: {
            minutes: variableMinutes,
          },
          schema: variableLoginRewardActivityDataDto,
          addLoyaltyPoints: true,
          variableRewardPoints,
        });
      }
    }

    // If notification is enabled in the frontend, log the activity
    if (notificationEnabled) {
      await this.activitiesService.createUserActivity({
        type: ActivityType.NotificationEnabled,
        profileId,
        data: {},
        schema: emptyActivityDataDto,
        addLoyaltyPoints: true,
        checkForUniqueEntry: true,
      });
    }
  }

  // async updateNotificationPreference(
  //   data: UpdateNotificationPreferenceDto,
  // ): Promise<Profile> {
  //   this.logger.info('ProfilesService.updateNotificationPreference', {
  //     data,
  //   });

  //   const transaction = await this.sequelize.transaction();

  //   try {
  //     const profile = await this.findById(data.profileId);

  //     if (!profile) {
  //       throw new Error(`Profile not found`);
  //     }

  //     profile.notificationPreference = data.preference;
  //     await profile.save();

  //     await transaction.commit();
  //     return profile;
  //   } catch (e) {
  //     await transaction.rollback();
  //     this.errorHelper.throwHttpException(
  //       `ProfilesService.updateNotificationPreference`,
  //       e.message,
  //     );
  //   }
  // }

  async incrementConnectionsCount(
    ids: string[],
    by: number,
    transaction?: Transaction,
  ): Promise<void> {
    await this.getModel().increment('connectionsCount' as any, {
      by,
      where: { id: { [Op.in]: ids } },
      transaction,
    });
  }

  async findProfilesByTimezoneOffset({
    maxOffset,
    minOffset,
    attributes,
  }: {
    minOffset: number;
    maxOffset: number;
    attributes?: string[];
  }): Promise<Profile[]> {
    this.logger.verbose('ProfilesService.findProfilesByTimezoneOffset', {
      minOffset,
      maxOffset,
    });

    return this.findAll(
      {
        timezoneOffset: {
          [Op.gte]: minOffset,
          [Op.lte]: maxOffset,
        },
        sellHolidays: true,
      },
      {
        attributes,
      },
    );
  }

  async updateProfileReferringOrganisation(
    profileId: string,
    referringOrganisationId: string,
  ): Promise<Profile> {
    this.logger.info('ProfilesService.updateProfileReferringOrganisation', {
      profileId,
      referringOrganisationId,
    });

    const profile = await this.findById(profileId);
    if (!profile) {
      throw new Error(`Profile not found for profileId: ${profileId}`);
    }

    const organisation = await this.organisationsService.findById(
      referringOrganisationId,
    );
    if (!organisation) {
      throw new Error(
        `Organisation not found for id: ${referringOrganisationId}`,
      );
    }

    const updatedProfile = await this.updateById(profileId, {
      referredByOrganisationId: referringOrganisationId,
    });

    this.logger.info(
      `Successfully updated profile ${profileId} with referring organisation ${referringOrganisationId}`,
    );

    return updatedProfile;
  }
}
