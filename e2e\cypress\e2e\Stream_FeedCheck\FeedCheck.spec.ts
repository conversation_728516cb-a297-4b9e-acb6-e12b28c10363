import { random } from '../../utils/random';
import { User } from '../../fixtures/Fixtures';
import 'cypress-file-upload';

describe('Check Stream/feed (Image) and follow with existing users', () => {
  let user3: User;
  let user4: User;
  let user5: User;
  let user6: User;
  before(() =>
    cy
      .setupUser('user3')
      .then((data) => (user3 = data))
      .setupUser('user4')
      .then((data) => (user4 = data))
      .setupUser('user5')
      .then((data) => (user5 = data))
      .setupUser('user6')
      .then((data) => (user6 = data))
  );

  it('Test Case 1 - User3, User4, User5 should receive Internal posts from OrgA in their main feed', () => {
    /// Login with ORG A (user4) and post the image (feed) and verify himself
    cy.login(user4)
      .visit('/home')
      .wait(5000)
      .getByTestId('home-organisation-actions-post')
      .click({ force: true })
      .wait(2000)
      .get('.ant-form-item-control-input-content .mentionsEditorContainer .DraftEditor-root')
      .should('exist')
      .type(user4.posts[0].text)
      .getByTestId('post-btn-image')
      .attachFile('test.jpg', { subjectType: 'drag-n-drop' })
      .wait(3000)
      .getByTestId('post-btn')
      .click({ force: true })
      .wait(3000)
      .getByTestId('post-text')
      .should('exist')
      .and('contain', user4.posts[0].text)
      .getByTestId('post-image')
      .should('be.visible')
      .logout();

    /// Login with user 3 (which is already connected with Org A) and verify feed
    cy.login(user3)
      .visit('/home')
      .url()
      .should('not.include', '/login') // Assuming successful login redirects to a different URL
      .getByTestId('post-text')
      .contains(user4.posts[0].text)
      .getByTestId('post-image')
      .should('be.visible')
      .logout();

    /// Login with user 5 (which is already connected with Org A) and verify feed
    cy.login(user5)
      .visit('/home')
      .url()
      .should('not.include', '/login') // Assuming successful login redirects to a different URL
      .getByTestId('post-text')
      .contains(user4.posts[0].text)
      .getByTestId('post-image')
      .should('be.visible')
      .logout();
  });

  it('Test Case -2 : User5 should receive Internal posts sent from OrgT ', () => {
    cy.login(user5)
      .visit('/home')
      .url()
      .should('not.include', '/login') // Assuming successful login redirects to a different URL
      .getByTestId('home-organisation-actions-post')
      .click()
      .wait(3000)
      .getByTestId('post-audience-dropdown')
      .click()
      .wait(3000)
      .getByTestId('my-organisation')
      .click()
      .getByTestId('post-audience-save')
      .click()
      .wait(2000)
      .get('.ant-form-item-control-input-content .mentionsEditorContainer .DraftEditor-root')
      .type(user5.posts[0].text)
      .getByTestId('post-btn-image')
      .attachFile('test.jpg', { subjectType: 'drag-n-drop' })
      .wait(3000)
      .getByTestId('post-btn')
      .click()
      .wait(2000)
      .get("div[data-node-key='organisation']")
      .click()
      .wait(2000)
      .getByTestId('post-image')
      .should('be.visible')
      .wait(2000)
      .getByTestId('post-text')
      .contains(user5.posts[0].text)
      .wait(5000)
      .logout();
  });

  it('Test Case 3- User6 should follow OrgA ', () => {
    //Login with user4 i.e. Org A and send the follow request to Org D
    cy.login(user4)
      .visit('/home')
      .url()
      .should('not.include', '/login')
      .getByTestId('home-manage-organisation')
      .click({ force: true })
      .getByTestId('connected-organisations')
      .click()
      .getByTestId('add-organisation')
      .click()
      .get('#organisation')
      .type(user6.organisations[0].name)
      .get('.rc-virtual-list-holder')
      .type('{downarrow}')
      .click({ force: true })
      .getByTestId('addOrg-send-request-btn')
      .click()
      .wait(3000)
      .getByTestId('addOrg-cancel-req')
      .should('be.visible')
      //  .getByTestId('connectedOrg-Pending-request').should('be.selected')
      .logout();

    //login with user6 and accept request of OrgA and also check user6 follow orgA
    cy.login(user6)
      .visit('/home')
      .url()
      .should('not.include', '/login')
      .getByTestId('home-manage-organisation')
      .click()
      .getByTestId('connected-organisations')
      .click()
      .getByTestId('connectedOrg-Pending-request')
      .click()
      .getByTestId('org-name')
      .should('have.text', user4.organisations[0].name)
      .getByTestId('accept-org-conn-req')
      .click()
      .get('.ant-empty-description')
      .should('be.visible')
      //check user6 is following or not to Org A
      .get('#search')
      .type(user4.organisations[0].name)
      .getByTestId('org-card-org-name')
      .each(($el, index, $lists) => {
        if ($el.text() === user4.organisations[0].name) {
          cy.wrap($el).click();
        }
      })

      .wait(3000)
      .getByTestId('button-unfollow')
      .should('be.visible');
  });

  //Login with user5 (which is already connected to the Org A) and check user5 is follow or not to OrgA
  it('Test Case 4 - User5 should follow OrgA', () => {
    cy.login(user5)
      .visit('/home')
      .get('#search')
      .type(user4.organisations[0].name)
      .getByTestId('org-card-org-name')
      .each(($el, index, $lists) => {
        if ($el.text() === user4.organisations[0].name) {
          cy.wrap($el).click();
        }
      })
      .wait(1000)
      .getByTestId('button-unfollow')
      .should('be.visible');
  });

  it('Test Case 5- User3, User4, User5 should receive posts Targeted to OrgA by OrgD', () => {
    cy.login(user6)
      .visit('/home')
      .getByTestId('home-organisation-actions-post')
      .click()
      .wait(3000)
      .getByTestId('post-audience-dropdown')
      .click()
      .getByTestId('conn-org-checkbox')
      .click()
      .getByTestId('post-audience-save')
      .click()
      .get('.ant-form-item-control-input-content .mentionsEditorContainer .DraftEditor-root')
      .type(user6.posts[1].text)
      .getByTestId('post-btn-image')
      .attachFile('test.jpg', { subjectType: 'drag-n-drop' })
      .wait(3000)
      .getByTestId('post-btn')
      .click()
      .wait(3000)
      .getByTestId('post-text')
      .should('exist')
      .and('contain', user6.posts[1].text)
      .getByTestId('targeted-post')
      .should('be.visible')
      .getByTestId('post-image')
      .should('be.visible')
      .logout();

    cy.login(user4)
      .visit('/home')
      .wait(3000)
      .url()
      .should('not.include', '/login') // Assuming successful login redirects to a different URL
      .getByTestId('post-text')
      .should('exist')
      .and('contain', user6.posts[1].text, { timeout: 6000 })
      .getByTestId('targeted-post')
      .should('be.visible')
      .getByTestId('post-image')
      .should('be.visible')
      .logout();

    cy.login(user3)
      .visit('/home')
      .wait(3000)
      .url()
      .should('not.include', '/login') // Assuming successful login redirects to a different URL
      .getByTestId('post-text')
      .should('exist')
      .and('contain', user6.posts[1].text, { timeout: 6000 })
      .getByTestId('targeted-post')
      .should('be.visible')
      .getByTestId('post-image')
      .should('be.visible')
      .logout();

    cy.login(user5)
      .visit('/home')
      .wait(3000)
      .url()
      .should('not.include', '/login') // Assuming successful login redirects to a different URL
      .getByTestId('post-text')
      .should('exist')
      .and('contain', user6.posts[1].text, { timeout: 6000 })
      .getByTestId('targeted-post')
      .should('be.visible')
      .getByTestId('post-image')
      .should('be.visible')
      .logout();
  });

  it('Test Case 6 - User6 should not follow OrgT', () => {
    cy.login(user6)
      .visit('/home')
      .get('#search')
      .type(user5.organisations[0].name)
      .getByTestId('org-card-org-name')
      .each(($el, index, $lists) => {
        if ($el.text() === user5.organisations[0].name) {
          cy.wrap($el).click({ force: true });
        }
      })
      .getByTestId('button-follow')
      .should('be.visible');
  });
});

describe('a userX at a Destination Org should not be able to connect with a userY, IF userY is a member of a Protected Organisation', () => {
  let userX: User;
  let userY: User;
  let user4: User;
  let user6: User;
  before(() =>
    cy
      .setupUser('userX')
      .then((data) => (userX = data))
      .setupUser('userY')
      .then((data) => (userY = data))
      .setupUser('user4')
      .then((data) => (user4 = data))
      .setupUser('user6')
      .then((data) => (user6 = data))
  );

  it('Test Case 1 - Create the userY under Association Org ', () => {
    const headline = random.sentence({ words: 10 });

    cy.login(userY)
      .visit('/profile/setup')
      .wait(5000)
      .url()
      .should('include', '/profile/setup')
      // .get('[data-cy=input-profile-name]  ')
      // .type(username)
      .selectOption('[data-cy=input-date-day]', '[data-cy=input-date-day-3]')
      .selectOption('[data-cy=input-date-month]', '[data-cy=input-date-month-3]')
      .selectOption('[data-cy=input-date-year]', '[data-cy=input-date-year-2000]')
      .get('[data-cy=select-gender-male]')
      .click()
      .get('#onboarding1_terms')
      .click()
      .get('button[type=submit]')
      .click()

      .get('[data-cy=input-location]:not(:disabled)')
      .type('london unite')
      .blur()
      .wait(2000)
      .get('#root')
      .click()
      .get('button[type=submit]')
      .click()

      .get('[data-cy=input-organisation]')
      .type(user4.organisations[0].name)
      .wait(1000)
      .get('.rc-virtual-list-holder')
      .type('{downarrow}')
      .click({ force: true })
      .get('#root')
      .click()

      .get('.ant-checkbox-input')
      .click()
      .get('button[type=submit]')
      .click()
      .wait(3000)
      .get('[data-cy=input-position]')
      .type(userY.memberships[0].position)
      .get('[data-cy=input-headline]')
      .type(headline)
      .selectOption('[data-cy=input-date-month]', '[data-cy=input-date-month-2]')
      .selectOption('[data-cy=input-date-year]', '[data-cy=input-date-year-2019]')
      .wait(1000)
      .get('#root')
      .click()
      .get('button[type=submit]')
      .click()

      .get('[data-cy=select-region-UnitedStatesOfAmerica]')
      .click()
      .wait(1000)
      .get('button[type=submit]')
      .click()

      .url()
      .should('not.include', '/profile/setup')
      .should('include', '/home')

      .visit('/profile')

      .get('[data-cy=profile-headline]')
      .should('contain.text', headline)
      .get('[data-cy=profile-position]')
      .should('contain.text', userY.memberships[0].position)
      .get('[data-cy=profile-location]')
      .should('contain.text', 'London, United Kingdom')
      .get('[data-cy=profile-organisation]')
      .should('contain.text', user4.organisations[0].name)

      .logout();
  });

  it('Test Case 2 - Accept the employee request (userY) and set the protected setting for destination', () => {
    // Change the Proctected setting - remove the people of destination
    cy.login(user4)
      .visit('/home')
      .wait(2000)
      .getByTestId('home-manage-organisation')
      .click({ force: true })
      .getByTestId('org-policy')
      .click()
      .get("input[name='protected']")
      .click()
      .should('be.checked')
      .getByTestId('protected-employee')
      .click()
      .getByTestId('emp-Destination')
      .click()
      .should('not.be.checked')
      .getByTestId('save-changes-btn')
      .click()
      .get("input[name='protected']")
      .should('be.checked')
      .wait(3000)

      // Accept employee (UserY) request
      .getByTestId('org-employee-tab')
      .click()
      .get('.ant-list-item-meta-title')
      .should('contain.text', userY.name)
      .get('.ant-list-item-meta-description')
      .should('contain.text', userY.memberships[0].position)
      .getByTestId('emp-confirm')
      .click()
      .get('.ant-empty-description')
      .should('be.visible')

      .logout();
  });

  it('Test Case 3 - Create userX under Destination org and try to connect with protected org user i.e.userY', () => {
    const headline = random.sentence({ words: 10 });

    cy.login(userX)
      .visit('/profile/setup')
      .wait(5000)
      .url()
      .should('include', '/profile/setup')
      // .get('[data-cy=input-profile-name]  ')
      // .type(username)
      .selectOption('[data-cy=input-date-day]', '[data-cy=input-date-day-3]')
      .selectOption('[data-cy=input-date-month]', '[data-cy=input-date-month-3]')
      .selectOption('[data-cy=input-date-year]', '[data-cy=input-date-year-2000]')
      .get('[data-cy=select-gender-male]')
      .click()
      .get('#onboarding1_terms')
      .click()
      .get('button[type=submit]')
      .click()

      .get('[data-cy=input-location]:not(:disabled)')
      .type('london unite')
      .blur()
      .wait(2000)
      .get('#root')
      .click()
      .get('button[type=submit]')
      .click()

      .get('[data-cy=input-organisation]')
      .type(user6.organisations[0].name)
      .wait(1000)
      .get('.rc-virtual-list-holder')
      .type('{downarrow}')
      .click({ force: true })
      .get('#root')
      .click()

      .get('.ant-checkbox-input')
      .click()
      .get('button[type=submit]')
      .click()

      .wait(3000)
      .get('[data-cy=input-position]')
      .type(userX.memberships[0].position)
      .get('[data-cy=input-headline]')
      .type(headline)
      .selectOption('[data-cy=input-date-month]', '[data-cy=input-date-month-2]')
      .selectOption('[data-cy=input-date-year]', '[data-cy=input-date-year-2019]')
      .wait(1000)
      .get('#root')
      .click()
      .get('button[type=submit]')
      .click()

      .get('[data-cy=select-region-UnitedStatesOfAmerica]')
      .click()
      .wait(1000)
      .get('button[type=submit]')
      .click()

      .url()
      .should('not.include', '/profile/setup')
      .should('include', '/home')

      .visit('/profile')

      .get('[data-cy=profile-headline]')
      .should('contain.text', headline)
      .get('[data-cy=profile-position]')
      .should('contain.text', userX.memberships[0].position)
      .get('[data-cy=profile-location]')
      .should('contain.text', 'London, United Kingdom')
      .get('[data-cy=profile-organisation]')
      .should('contain.text', user6.organisations[0].name)

      .wait(3000)

      .visit('/home')
      .wait(2000)
      .getByTestId('nav-search')
      .eq(0)
      .type(userY.name)
      .wait(3000)
      .getByTestId('emp-name')
      .should('contain.text', userY.name)
      .getByTestId('emp-org')
      .should('contain.text', user4.organisations[0].name)
      .getByTestId('emp-position')
      .should('contain.text', userY.memberships[0].position)
      .getByTestId('connect-connect')
      .should('be.disabled');
  });
});
