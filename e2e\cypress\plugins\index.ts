module.exports = (on: Cypress.PluginEvents, config: Cypress.PluginConfigOptions) => {
  on('before:browser:launch', (browser: Cypress.Browser | undefined, launchOptions) => {
    if (browser?.name === 'chrome' || browser?.name === 'chromium' || browser?.name === 'electron') {
      launchOptions.args.push('--disable-audio-output');
      launchOptions.args.push('--mute-audio');
      launchOptions.args.push('--no-sandbox');
      launchOptions.args.push('--use-fake-ui-for-media-stream');
      launchOptions.args.push('--use-fake-device-for-media-stream');
    }
    return launchOptions;
  });
};
