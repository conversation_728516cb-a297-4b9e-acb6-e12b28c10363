import { BORDER_RADIUS } from '@theme';
import { Popover, PopoverProps } from 'antd';
import React, { PropsWithChildren, ReactNode, useState } from 'react';
import { Link, LinkProps } from 'react-router-dom';
import styled from 'styled-components';

import { ButtonWithIcon, ButtonWithIconProps } from './Buttons/ButtonWithIcon';
import { IconContainer } from './icons/Icon';

export type DropdownMenuProps = Omit<PopoverProps, 'overlay' | 'children'> &
  PropsWithChildren<{
    button: ReactNode;
  }>;

export function DropdownMenu({ button, children, ...props }: DropdownMenuProps) {
  const [open, setOpen] = useState(false);
  return (
    <Popover
      open={open}
      onOpenChange={setOpen}
      trigger="click"
      placement="bottomRight"
      align={{ offset: [0, 14] }}
      content={() => <div onClick={() => setOpen(false)}>{children}</div>}
      {...props}
    >
      <div>{button}</div>
    </Popover>
  );
}

export type DropdownMenuItemProps = Pick<
  ButtonWithIconProps,
  'icon' | 'onClick' | 'children' | 'loading' | 'danger' | 'style' | 'className' | 'disabled'
> & {
  key?: number;
};

DropdownMenu.Item = function ({
  key,
  icon,
  onClick,
  children,
  loading,
  danger,
  style,
  className,
  disabled,
}: DropdownMenuItemProps) {
  return (
    <ItemContainer key={key} style={style} className={className} disabled={disabled}>
      <ButtonWithIcon
        size="small"
        type="primary"
        align="start"
        ghost={true}
        block={true}
        icon={icon}
        danger={danger}
        loading={loading}
        onClick={onClick}
        children={children}
        disabled={disabled}
      />
    </ItemContainer>
  );
};

export type DropdownMenuLinkProps = Pick<LinkProps, 'to'> & DropdownMenuItemProps;

DropdownMenu.Link = function ({
  key,
  to,
  icon,
  onClick,
  children,
  loading,
  danger,
  style,
  className,
}: DropdownMenuLinkProps) {
  return (
    <ItemContainer key={key} style={style} className={className}>
      <Link to={to} style={{ color: 'var(--color-text)' }}>
        <ButtonWithIcon
          size="small"
          type="primary"
          align="start"
          ghost={true}
          block={true}
          icon={icon}
          danger={danger}
          loading={loading}
          onClick={onClick}
          children={children}
        />
      </Link>
    </ItemContainer>
  );
};

const ItemContainer = styled.li<{ disabled?: boolean }>`
  display: flex;
  padding: 10px 14px;
  transition: background-color 0.4s ease;
  background-color: ${(props) => (props.disabled ? '#f5f5f5' : 'transparent')};
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};

  :hover {
    background-color: ${(props) => (props.disabled ? '#f5f5f5' : 'var(--color-primary90)')};
  }

  .ant-btn {
    border: none;
    box-shadow: none;
    text-align: left;
    color: var(--color-text);

    span {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    ${IconContainer} {
      color: var(--color-primary);
      background-color: var(--color-primary70);
      border-radius: ${BORDER_RADIUS};
      margin-right: 12px !important;
      padding: 8px;
    }

    @media only screen and (max-width: 800px) {
      ${IconContainer} {
        display: none;
      }
    }
  }
`;
