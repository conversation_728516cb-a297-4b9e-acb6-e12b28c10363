import { useTranslation } from 'react-i18next';
import { memo, useContext } from 'react';

import { DropdownMenu } from '../DropdownMenu';
import Icon, { Icons } from '../icons/Icon';
import { RightNavButton } from './Nav';
import { useModalToggle } from '@utils/hooks/useModalToggle';
import { CreateEvent } from '@src/pages/organisation/event/create/CreateEvent';
import { CreatePost } from '@src/pages/organisation/post/create/CreatePost';
import { CreateIncentive } from '@src/pages/organisation/incentive/create/CreateIncentive';
import { CreateWebinar } from '@src/pages/organisation/webinar/create/CreateWebinar';
import { useProfile } from '@src/routes/ProfileProvider';
import { FeedContext } from '@src/routes/PrivateRoutesHandler';
import { PartnershipRequestStatus } from '@src/graphql/GraphQLTypes';

const Create = memo(function Create() {
  const { t } = useTranslation();
  const { profile } = useProfile();
  const membership = profile.memberships[0];
  const isChild = membership?.partnerOrganisation
    ? membership.partnerOrganisation.status === PartnershipRequestStatus.Approved
    : false;
  const [CreatePostModal, setPostShow, postShow] = useModalToggle(CreatePost);
  const [CreateEventModal, setEventShow, eventShow] = useModalToggle(CreateEvent);
  const [CreateIncentiveModal, setIncentiveShow, incentiveShow] = useModalToggle(CreateIncentive);
  const [CreateWebinarModal, setWebinarShow, webinarShow] = useModalToggle(CreateWebinar);
  const { setShowAllScheduledPostsList } = useContext(FeedContext);

  return (
    <>
      <DropdownMenu
        overlayStyle={{ padding: 0 }}
        getPopupContainer={() => document.getElementById('nav') || document.body}
        button={
          <RightNavButton data-cy="nav-create">
            <Icon icon={Icons.add} size={20} />
          </RightNavButton>
        }
      >
        <DropdownMenu.Item icon={Icons.posts} onClick={() => setPostShow(true)} data-cy="nav-create-post">
          {t('Create Post')}
        </DropdownMenu.Item>
        <DropdownMenu.Item
          icon={Icons.time}
          onClick={() => {
            setPostShow(true);
            setShowAllScheduledPostsList(true);
          }}
          data-cy="nav-scheduled-posts"
        >
          {t('Scheduled Posts')}
        </DropdownMenu.Item>
        <DropdownMenu.Item
          icon={Icons.calendar}
          disabled={isChild}
          onClick={() => setEventShow(true)}
          data-cy="nav-create-event"
        >
          {t('Create Event')}
        </DropdownMenu.Item>
        <DropdownMenu.Item
          icon={Icons.incentives}
          disabled={isChild}
          onClick={() => setIncentiveShow(true)}
          data-cy="nav-create-incentive"
        >
          {t('Create Incentive')}
        </DropdownMenu.Item>
        <DropdownMenu.Item
          icon={Icons.webinar}
          disabled={isChild}
          onClick={() => setWebinarShow(true)}
          data-cy="nav-create-webinar"
        >
          {t('Upload Video')}
        </DropdownMenu.Item>
      </DropdownMenu>
      {postShow && <CreatePostModal />}
      {eventShow && <CreateEventModal />}
      {incentiveShow && <CreateIncentiveModal />}
      {webinarShow && <CreateWebinarModal />}
    </>
  );
});

export const NavCreate = memo(function NavCreate() {
  const { profile } = useProfile();
  return profile.memberships.length ? <Create /> : null;
});
