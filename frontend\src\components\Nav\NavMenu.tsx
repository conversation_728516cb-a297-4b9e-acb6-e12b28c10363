import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { Divider, Space, Typography } from 'antd';
import { useMutation } from '@apollo/client';

import { Routes } from '@src/routes/Routes';
import { RightNavButton } from './Nav';
import Icon, { Icons } from '../icons/Icon';
import { DropdownMenu } from '../DropdownMenu';
import { GUTTER_SM, PADDING_LG, PADDING_RESPONSIVE_LG, PADDING_RESPONSIVE_SM, PADDING_SM } from '@theme';
import { useProfile } from '../../routes/ProfileProvider';
import { Avatar } from '../Image/Image';
import { getFirstName } from '@utils/getOrganisationName';
import { HABLO_PRIVACY, HABLO_TERMS } from '@src/consts';
import { useAuth } from '@src/Auth0';
import { DeviceLogInCountType, MOBILE_DEVICES_LOGGEDIN_COUNT } from '@src/pages/home/<USER>';

export function NavMenu() {
  const { t } = useTranslation();
  const { logout } = useAuth();
  const { profile } = useProfile();
  const [mobileDevicesLoggedInCount] = useMutation(MOBILE_DEVICES_LOGGEDIN_COUNT);

  const signout = async () => {
    setTimeout(async () => {
      await mobileDevicesLoggedInCount({
        variables: {
          data: {
            profileId: profile.id,
            type: DeviceLogInCountType.Decrement,
          },
        },
      });
      logout();
    }, 1000);
  };

  return (
    <DropdownMenu
      overlayStyle={{ padding: 0, width: '280px' }}
      getPopupContainer={() => document.getElementById('nav') || document.body}
      button={
        <RightNavButton data-cy={'nav-dropdown-btn'}>
          <Icon icon={Icons.chevronDown} size={20} />
        </RightNavButton>
      }
    >
      <li>
        <Link to={Routes.profile}>
          <Profile>
            <Avatar id={profile.image} width={56} />
            <div>
              <Typography.Title level={4} ellipsis={true} style={{ margin: 0, maxWidth: '170px' }}>
                {getFirstName(profile)}
              </Typography.Title>
              <Typography.Text type="secondary">{t('nav{:}See your profile')}</Typography.Text>
            </div>
          </Profile>
        </Link>
      </li>
      <Container>
        <Divider />
      </Container>
      <Item icon={Icons.feedback}>
        <Link to="#">
          {t('nav{:}Give Feedback')}
          <br />
          <Small>{t('nav{:}Help us improve Hablo')}</Small>
        </Link>
      </Item>
      <Item icon={Icons.calendar}>
        <Link to="#">{t('nav{:}Calendar')}</Link>
      </Item>
      {profile.sellHolidays && (
        <Item icon={Icons.kudosFlat}>
          <Link to={Routes.clubHabloDashboard} data-cy={'navbar-clubHabloDashboard'}>
            {t('nav{:}Club Hablo Dashboard')}
          </Link>
        </Item>
      )}
      <Item icon={Icons.bookmarked} data-cy={'navbar-saved-posts'}>
        <Link to={Routes.savedPosts} data-cy={'navbar-saved-posts'}>
          {t('Saved Posts')}
        </Link>
      </Item>
      <Item icon={Icons.settings} data-cy={'navbar-settings'}>
        <Link to={Routes.profileSettings} data-cy={'navbar-settings'}>
          {t('nav{:}Settings')}
        </Link>
      </Item>
      <Item icon={Icons.help}>
        <a href="https://help.myhablo.com/" target="_blank">
          {t('nav{:}Help & Support')}
        </a>
      </Item>
      <Item icon={Icons.signOut} onClick={signout}>
        {t('nav{:}Sign out')}
      </Item>
      <Container>
        <Divider />
      </Container>
      <Container>
        <Typography.Text type="secondary">
          <Space size={GUTTER_SM}>
            <a href={HABLO_PRIVACY} target="_blank">
              {t('Privacy')}
            </a>
            <span> • </span>
            <a href={HABLO_TERMS} target="_blank">
              {t('Terms')}
            </a>
            <span> • </span>
            <span>Hablo © {new Date().getFullYear()}</span>
          </Space>
        </Typography.Text>
      </Container>
    </DropdownMenu>
  );
}

export function ResponsiveNavMenu() {
  const { t } = useTranslation();
  const { logout } = useAuth();
  const { profile } = useProfile();
  const [mobileDevicesLoggedInCount] = useMutation(MOBILE_DEVICES_LOGGEDIN_COUNT);

  const signout = async () => {
    await mobileDevicesLoggedInCount({
      variables: {
        data: {
          profileId: profile.id,
          type: DeviceLogInCountType.Decrement,
        },
      },
    });
    logout();
  };

  return (
    <DropdownMenu
      overlayStyle={{ padding: 0, width: '260px' }}
      getPopupContainer={() => document.getElementById('nav') || document.body}
      button={<Picture id={profile.image} width={40} />}
    >
      <Link to={Routes.profile}>
        <Profile>
          <Avatar id={profile.image} width={40} />
          <div>
            <Typography.Title level={4} ellipsis={true} style={{ margin: 0, maxWidth: '170px' }}>
              {getFirstName(profile)}
            </Typography.Title>
            <Typography.Text type="secondary">{t('nav{:}See your profile')}</Typography.Text>
          </div>
        </Profile>
      </Link>
      <Container>
        <Divider />
      </Container>
      <Link to={Routes.explore}>
        <Item icon={Icons.explore}>{t('common{:}ExploreOrg')}</Item>
      </Link>
      <Link to={Routes.calendar}>
        <Item icon={Icons.calendar}>{t('nav{:}Calendar')}</Item>
      </Link>
      <Link to={Routes.connect}>
        <Item icon={Icons.usersAdd}>{t('common{:}Connect')}</Item>
      </Link>
      <Link to={Routes.savedPosts}>
        <Item icon={Icons.bookmarked}>{t('Saved Posts')}</Item>
      </Link>
      <Link to={Routes.profileSettings}>
        <Item icon={Icons.settings}>{t('nav{:}Settings')}</Item>
      </Link>
      <a href="https://help.myhablo.com/" target="_blank">
        <Item icon={Icons.help}>{t('nav{:}Help & Support')}</Item>
      </a>
      <Item icon={Icons.signOut} onClick={signout}>
        {t('nav{:}Sign out')}
      </Item>
      <Container>
        <Divider />
      </Container>
      <Container>
        <Typography.Text type="secondary">
          <Space size={GUTTER_SM}>
            <a href={HABLO_PRIVACY} target="_blank">
              Privacy
            </a>
            <span> • </span>
            <a href={HABLO_TERMS} target="_blank">
              Terms
            </a>
            <span> • </span>
            <span>Hablo © {new Date().getFullYear()}</span>
          </Space>
        </Typography.Text>
      </Container>
    </DropdownMenu>
  );
}

const Picture = styled(Avatar)`
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  margin-right: 8px;
  border: 1px solid white;

  & img {
    border-radius: 50%;
  }
`;

const Profile = styled.div`
  display: flex;
  align-items: center;
  padding: ${PADDING_LG} ${PADDING_LG} ${PADDING_SM} ${PADDING_LG};

  ${Avatar} {
    margin-right: 10px;
  }

  @media screen and (max-width: 568px) {
    padding: ${PADDING_RESPONSIVE_LG} ${PADDING_RESPONSIVE_LG} ${PADDING_RESPONSIVE_SM} ${PADDING_RESPONSIVE_LG};
  }
`;

const Container = styled.li`
  padding: 0 ${PADDING_LG};

  :last-child {
    padding-bottom: ${PADDING_LG};
  }

  a {
    color: inherit;

    :hover {
      text-decoration: underline;
      color: var(--color-primary);
    }
  }

  @media screen and (max-width: 568px) {
    padding: 0 ${PADDING_RESPONSIVE_LG};

    :last-child {
      padding-bottom: ${PADDING_RESPONSIVE_LG};
    }
  }
`;

const Item = styled(DropdownMenu.Item)`
  padding: ${PADDING_SM} ${PADDING_LG};
  vertical-align: middle;

  a {
    color: var(--color-text) !important;

    :hover {
      color: var(--color-text) !important;
    }
  }

  @media screen and (max-width: 568px) {
    padding: ${PADDING_RESPONSIVE_SM} ${PADDING_RESPONSIVE_LG};
  }
`;

const Small = styled.div`
  display: block;
  font-size: 12px;
  color: var(--color-gray1);
`;
