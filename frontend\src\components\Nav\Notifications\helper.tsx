import { get } from 'lodash';
import React from 'react';
import { Typography } from 'antd';
import { TFunction } from 'i18next';
import { Notification, NotificationType } from '@GraphQLTypes';
import { Routes } from '@src/routes/Routes';
import Icon, { Icons } from '../../icons/Icon';
import { encodePathParams } from '@utils/encodePathParams';
import { getI18n } from 'react-i18next';

export interface NotificationConfiguration {
  image: string | undefined | null;
  route: string;
  text: any;
  icon: Icons;
  rightImage: string | undefined | null;
}

const getStringsForNotifications = (countOfUsers: number, usersFullNames: string[] = [], t: TFunction) => {
  switch (countOfUsers) {
    case 1:
      return {
        usersFullNamesString: usersFullNames[0],
        other: '',
        has: getI18n().language.includes('en') ? 'has ' : '',
      };

    case 2:
      return {
        usersFullNamesString: `${usersFullNames[0]} & ${usersFullNames[1]} `,
        other: '',
        has: getI18n().language.includes('en') ? 'have ' : '',
      };

    case 3:
      return {
        usersFullNamesString: `${usersFullNames[0]}, ${usersFullNames[1]} & 1 `,
        other: getI18n().language.includes('en') ? 'other ' : t(`others`),
        has: getI18n().language.includes('en') ? 'have ' : '',
      };

    default:
      return {
        usersFullNamesString: `${usersFullNames[0]}, ${usersFullNames[1]} & ${countOfUsers} `,
        other: getI18n().language.includes('en') ? 'others ' : t(`others`),
        has: getI18n().language.includes('en') ? 'have ' : '',
      };
  }
};

export const getOrgNameStringsForNotifications = (countOfOrgs: number, organistionNames: string[] = []) => {
  switch (countOfOrgs) {
    case 1:
      return {
        organisationNamesString: organistionNames[0],
        has: getI18n().language.includes('en') ? 'has ' : '',
      };

    case 2:
      return {
        organisationNamesString: `${organistionNames[0]} and ${organistionNames[1]} `,
        has: getI18n().language.includes('en') ? 'have ' : '',
      };

    case 3:
      return {
        organisationNamesString: `${organistionNames[0]}, ${organistionNames[1]} and ${organistionNames[2]} `,
        has: getI18n().language.includes('en') ? 'have ' : '',
      };

    default:
      return {
        organisationNamesString: `${organistionNames[0]} `,
        has: getI18n().language.includes('en') ? 'has ' : '',
      };
  }
};

export class NotificationHelper {
  static getEmptyConfiguration() {
    return {
      image: '',
      route: '',
      text: <div></div>,
      icon: Icons.time,
      rightImage: '',
    };
  }

  static getConfiguration(notification: Notification, t: TFunction): NotificationConfiguration {
    return {
      image: this.getImage(notification),
      route: this.getRoute(notification),
      text: this.getText(notification, t),
      icon: this.getIcon(notification),
      rightImage: this.getRightImage(notification),
    };
  }

  private static getImage(notification: Notification): string | undefined | null {
    switch (notification.type) {
      case NotificationType.InvitationAccepted:
      case NotificationType.InvitationReceived:
      case NotificationType.CommentMention:
      case NotificationType.OrgCommentMention:
        return notification.profile?.image;
      case NotificationType.PartnershipRequestReceived:
        return notification.partnershipRequest?.senderOrganisation?.image;
      case NotificationType.PartnershipRequestApproved:
        return notification.partnershipRequest?.receiverOrganisation?.image;
      case NotificationType.PartnershipRequestApprovedByOrganisation:
        return notification.partnershipRequest?.senderOrganisation?.image;
      case NotificationType.MembershipRequested:
      case NotificationType.MembershipAccepted:
      case NotificationType.MembershipAutoApprove:
      case NotificationType.ParentMembershipRequested:
      case NotificationType.ParentMembershipAccepted:
      case NotificationType.ParentMembershipDeclined:
        return notification.membership?.organisation?.image;
      case NotificationType.FollowerAccepted:
        return notification.follower?.organisation?.image;
      case NotificationType.MembershipPermissionsUpdated:
      case NotificationType.OrganisationOwnershipAccepted:
      case NotificationType.OrganisationOwnershipRequested:
        return notification.membership?.organisation?.image;
      case NotificationType.EventInvitationByGuest:
        return notification.eventInvitation?.inviterProfile?.image;
      case NotificationType.EventInvitationByHosts:
      case NotificationType.EventInvitationApproved:
      case NotificationType.NewEventUpdate:
      case NotificationType.EventLocationChanged:
      case NotificationType.EventDateTimeChanged:
        return notification.eventInvitation?.event?.organisation?.image;
      case NotificationType.IncentiveDateChanged:
      case NotificationType.IncentiveInvitationByHosts:
      case NotificationType.IncentiveRegistrationApproved:
      case NotificationType.IncentiveRegistrationRequested:
      case NotificationType.NewIncentiveUpdate:
        return notification.incentiveParticipant?.incentive?.organisation?.image;
      case NotificationType.IncentiveInvitationByParticipant:
        return notification.incentiveParticipant?.inviterProfile?.image;
      case NotificationType.WebinarDateChanged:
      case NotificationType.WebinarInvitationByHosts:
      case NotificationType.WebinarRegistrationApproved:
      case NotificationType.WebinarRegistrationRequested:
      case NotificationType.WebinarParticipantAddedAsHostAdmin:
      case NotificationType.WebinarParticipantAddedAsHost:
      case NotificationType.WebinarParticipantAddedAsSpeaker:
      case NotificationType.NewWebinarUpdate:
        return notification.webinarParticipant?.webinar?.organisation?.image;
      case NotificationType.WebinarInvitationByParticipant:
        return notification.webinarParticipant?.inviterProfile?.image;
      case NotificationType.SuggestFollow:
      case NotificationType.MonthlyAchievementSummary:
        return 'jpwbh9kjy7an2w05jijd'; // hablo org logo
      case NotificationType.PostComment:
        return notification.data.users[0].image;
      case NotificationType.CommentReact:
        return notification.data.users[0].image;
      case NotificationType.PostReact:
      case NotificationType.NewFollower:
        return notification.data?.users?.[0].image;
      case NotificationType.PostShared:
      case NotificationType.PostMention:
      case NotificationType.OrgPostMention:
      case NotificationType.OrgPostReminder7Days:
      case NotificationType.OrgPostReminder14Days:
      case NotificationType.HighFiveAchievement:
      case NotificationType.NewOrganisationAchievement:
        return notification.organisation?.image;
      case NotificationType.RecurringPaymentReminder:
        return notification.partnershipRequest?.senderOrganisation?.image;
      case NotificationType.InactiveUserReminder:
        return notification.data?.[0]?.organisationImage;
      case NotificationType.InboxMessage:
      case NotificationType.DailyLoginStreak:
      case NotificationType.NewTier:
      case NotificationType.WeeklySummary:
      case NotificationType.NewAchievement:
        return notification.profile?.image;
      case NotificationType.OrgPartnerRequestReceived:
        return notification.data?.senderOrgImage;
      case NotificationType.OrgPartnerAcceptedSender:
      case NotificationType.OrgPartnerAcceptedReceiver:
        return notification.data?.receiverOrgImage;
      default: {
        return null;
      }
    }
  }

  private static getRightImage(notification: Notification): string | undefined | null {
    switch (notification.type) {
      case NotificationType.EventInvitationByGuest:
      case NotificationType.EventInvitationByHosts:
      case NotificationType.EventInvitationApproved:
      case NotificationType.NewEventUpdate:
      case NotificationType.EventLocationChanged:
      case NotificationType.EventDateTimeChanged:
        return notification.eventInvitation?.event?.image;
      case NotificationType.MembershipRequested:
      case NotificationType.ParentMembershipRequested:
      case NotificationType.ParentMembershipAccepted:
      case NotificationType.ParentMembershipDeclined:
        return notification.profile?.image;
      case NotificationType.IncentiveDateChanged:
      case NotificationType.IncentiveInvitationByHosts:
      case NotificationType.IncentiveInvitationByParticipant:
      case NotificationType.IncentiveRegistrationApproved:
      case NotificationType.NewIncentiveUpdate:
        return notification.incentiveParticipant?.incentive?.image;
      case NotificationType.IncentiveRegistrationRequested:
        return notification.incentiveParticipant?.profile?.image;
      case NotificationType.WebinarDateChanged:
      case NotificationType.WebinarInvitationByHosts:
      case NotificationType.WebinarInvitationByParticipant:
      case NotificationType.WebinarRegistrationApproved:
      case NotificationType.NewWebinarUpdate:
      case NotificationType.WebinarParticipantAddedAsHostAdmin:
      case NotificationType.WebinarParticipantAddedAsHost:
      case NotificationType.WebinarParticipantAddedAsSpeaker:
        return notification.webinarParticipant?.webinar?.image;
      case NotificationType.WebinarRegistrationRequested:
        return notification.webinarParticipant?.profile?.image;
      case NotificationType.PostComment:
        return notification.data.users[0].image;
      case NotificationType.CommentReact:
        return notification.data.users[0].image;
      case NotificationType.PostReact:
      case NotificationType.NewFollower:
        return notification.data?.users?.[0].image;
      case NotificationType.PostShared:
      case NotificationType.PostMention:
      case NotificationType.OrgPostMention:
      case NotificationType.OrgPostReminder7Days:
      case NotificationType.OrgPostReminder14Days:
      case NotificationType.HighFiveAchievement:
      case NotificationType.NewOrganisationAchievement:
        return notification.organisation?.image;
      case NotificationType.CommentMention:
      case NotificationType.OrgCommentMention:
        return notification.profile?.image;
      case NotificationType.RecurringPaymentReminder:
        return notification.partnershipRequest?.receiverOrganisation?.image;
      case NotificationType.MembershipAutoApprove:
        return notification.membership?.profile?.image;
      case NotificationType.InactiveUserReminder:
        return notification.data?.[0]?.organisationImage;
      case NotificationType.InboxMessage:
        return notification.profile?.image;
      case NotificationType.OrgPartnerRequestReceived:
      case NotificationType.OrgPartnerAcceptedReceiver:
        return notification.data?.receiverOrgImage;
      case NotificationType.OrgPartnerAcceptedSender:
      case NotificationType.OrgPartnerRequestRejected:
        return notification.data?.senderOrgImage;
      default: {
        return null;
      }
    }
  }

  private static getRoute(notification: Notification): string {
    switch (notification.type) {
      case NotificationType.InvitationReceived:
        return Routes.connect;
      case NotificationType.InvitationAccepted:
        return encodePathParams(Routes.profileById, {
          profileId: notification.profile.id,
        });
      case NotificationType.PartnershipRequestReceived:
        return encodePathParams(Routes.pendingConnections, {
          organisationId: notification.partnershipRequest?.receiverOrganisation?.id,
        });
      case NotificationType.PartnershipRequestApproved:
        return encodePathParams(Routes.activeConnections, {
          organisationId: notification.partnershipRequest?.senderOrganisation?.id,
        });
      case NotificationType.RecurringPaymentReminder:
        return encodePathParams(Routes.activeConnections, {
          organisationId: notification.partnershipRequest?.receiverOrganisation?.id,
        });
      case NotificationType.PartnershipRequestApprovedByOrganisation:
        return encodePathParams(Routes.organisationProfile, {
          vanityId: notification.partnershipRequest?.senderOrganisation?.vanityId as string,
        });
      case NotificationType.OrganisationOwnershipAccepted:
      case NotificationType.OrganisationOwnershipRequested:
        return encodePathParams(Routes.organisationSettingsUserRoles, {
          organisationId: notification?.membership?.organisation?.id,
        });
      case NotificationType.MembershipPermissionsUpdated:
        return encodePathParams(Routes.organisationProfile, {
          vanityId: notification.membership?.organisation?.vanityId as string,
        });
      case NotificationType.MembershipRequested:
      case NotificationType.MembershipAutoApprove:
        return encodePathParams(Routes.organisationSettingsEmployees, {
          organisationId: notification?.membership?.organisation?.id,
        });
      case NotificationType.MembershipAccepted:
      case NotificationType.ParentMembershipAccepted:
      case NotificationType.ParentMembershipDeclined:
        return encodePathParams(Routes.profile);
      case NotificationType.FollowerAccepted:
        return encodePathParams(Routes.organisationProfile, {
          vanityId: notification?.follower?.organisation?.vanityId as string,
        });
      case NotificationType.IncentiveDateChanged:
      case NotificationType.IncentiveInvitationByHosts:
      case NotificationType.IncentiveInvitationByParticipant:
      case NotificationType.IncentiveRegistrationApproved:
      case NotificationType.NewIncentiveUpdate:
      case NotificationType.IncentiveRegistrationRequested:
        return encodePathParams(Routes.organisationIncentive, {
          vanityId: notification.incentiveParticipant?.incentive.organisation.vanityId,
          incentiveId: notification.incentiveParticipant?.incentive.id,
        });
      case NotificationType.WebinarDateChanged:
      case NotificationType.WebinarInvitationByHosts:
      case NotificationType.WebinarInvitationByParticipant:
      case NotificationType.WebinarRegistrationApproved:
      case NotificationType.NewWebinarUpdate:
      case NotificationType.WebinarRegistrationRequested:
      case NotificationType.WebinarParticipantAddedAsHostAdmin:
      case NotificationType.WebinarParticipantAddedAsHost:
      case NotificationType.WebinarParticipantAddedAsSpeaker:
        return encodePathParams(Routes.organisationWebinar, {
          vanityId: notification.webinarParticipant?.webinar.organisation.vanityId,
          id: notification.webinarParticipant?.webinar.id,
        });
      case NotificationType.SuggestFollow:
        return Routes.exploreOrganisationsYouFollow;
      case NotificationType.PostComment:
      case NotificationType.CommentReact:
      case NotificationType.PostReact:
      case NotificationType.PostShared:
      case NotificationType.PostMention:
      case NotificationType.CommentMention:
      case NotificationType.OrgCommentMention:
      case NotificationType.OrgPostMention:
        return encodePathParams(Routes.organisationPost, {
          vanityId: notification?.organisation?.vanityId,
          id: notification?.data?.activityId,
          userId: notification?.organisation?.id,
        });
      case NotificationType.NewFollower:
        return encodePathParams(
          Routes.organisationProfile,
          {
            vanityId: notification?.organisation?.vanityId,
          },
          { modal: 'followers' },
        );
      case NotificationType.EventInvitationByGuest:
      case NotificationType.EventInvitationByHosts:
      case NotificationType.EventInvitationApproved:
      case NotificationType.NewEventUpdate:
      case NotificationType.EventLocationChanged:
      case NotificationType.EventDateTimeChanged: {
        return encodePathParams(Routes.organisationEvent, {
          vanityId: notification?.eventInvitation?.event?.organisation?.vanityId as string,
          eventId: notification?.eventInvitation?.event?.id,
        });
      }
      case NotificationType.ParentMembershipRequested:
        return encodePathParams(Routes.organisationSettingsMembers, {
          organisationId: notification?.membership?.organisation?.id,
        });
      case NotificationType.OrgPostReminder7Days:
      case NotificationType.OrgPostReminder14Days:
        return Routes.organisationNewPost;
      case NotificationType.InactiveUserReminder:
        return Routes.home;
      case NotificationType.InboxMessage:
        return encodePathParams(Routes.inboxChat, {
          chatId: notification?.data?.streamChannelId,
        });
      case NotificationType.NewAchievement:
      case NotificationType.NewTier:
      case NotificationType.HighFiveAchievement:
      case NotificationType.NewOrganisationAchievement:
      case NotificationType.MonthlyAchievementSummary:
      case NotificationType.WeeklySummary:
        return encodePathParams(Routes.clubHabloDashboard);
      case NotificationType.OrgPartnerRequestReceived:
        return encodePathParams(Routes.organisationSettingsPartners, {
          organisationId: notification.data?.receiverOrgId,
        });
      case NotificationType.OrgPartnerAcceptedReceiver:
        return encodePathParams(Routes.organisationSettingsPartners, {
          organisationId: notification.data?.receiverOrgId,
        });
      case NotificationType.OrgPartnerRequestRejected:
        return encodePathParams(Routes.organisationSettingsPartnersPrevious, {
          organisationId: notification.data?.senderOrgId,
        });
      case NotificationType.OrgPartnerAcceptedSender:
        return encodePathParams(Routes.organisationSettingsPartnersActive, {
          organisationId: notification.data?.senderOrgId,
        });

      //DO NOT ADD NEW LINES BELOW HERE
      default:
        return encodePathParams(Routes.home);
    }
  }

  private static getText(notification: Notification, t: TFunction): any {
    switch (notification.type) {
      case NotificationType.InvitationAccepted: {
        // <b>[requester full name]</b> wants to connect with you. View your pending connection invitations.
        return (
          <Typography.Paragraph>
            <Typography.Text>{t(`You are now connected with`)} </Typography.Text>
            <Typography.Text strong>{notification.profile.name}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.InvitationReceived: {
        // You are now connected with <b>[approver full name]</b>.
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.profile.name}</Typography.Text>
            <Typography.Text>
              {' '}
              {t(`wants to connect with you. View your pending connection invitations.`)}
            </Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.OrganisationOwnershipRequested: {
        // <b>[current owner name]</b> has requested you take over as Owner of <b>[organisation name]</b>. View this request to accept or reject it.
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.profile?.name}</Typography.Text>
            <Typography.Text> {t(`has requested you take over as Owner of`)} </Typography.Text>
            <Typography.Text strong>{notification.membership?.organisation?.name}.</Typography.Text>
            <Typography.Text> {t(`View this request to accept or reject it.`)}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.OrganisationOwnershipAccepted: {
        // <b>[new owner name]</b> has agreed to become Owner of <b>[organisation name]</b>. You have reverted to an Admin role.
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.profile?.name}</Typography.Text>
            <Typography.Text> {t(`has agreed to become Owner of`)} </Typography.Text>
            <Typography.Text strong>{notification.membership?.organisation?.name}.</Typography.Text>
            <Typography.Text> {t(`You have reverted to an Admin role.`)}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.PartnershipRequestReceived: {
        // <b>[requesting organisation]</b> has requested to become a Connected Organisation with <b>[my requested organisation]</b>. View this request.
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.partnershipRequest?.senderOrganisation?.name}</Typography.Text>
            <Typography.Text> {t(`has requested to become a Connected Organisation with`)} </Typography.Text>
            <Typography.Text strong>{notification.partnershipRequest?.receiverOrganisation?.name}.</Typography.Text>
            <Typography.Text> {t(`View this request.`)}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.PartnershipRequestApproved: {
        // <b>[approving organisation]</b> has approved a request to become Connected Organisation with <b>[my requesting organisation]</b>.
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.partnershipRequest?.receiverOrganisation?.name}</Typography.Text>
            <Typography.Text> {t(`approved a connection request from`)} </Typography.Text>
            <Typography.Text strong>{notification.partnershipRequest?.senderOrganisation?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.PartnershipRequestApprovedByOrganisation: {
        // <b>[requesting organisation]</b> is now a Connected Organisation with [my approving organisation]
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.partnershipRequest?.senderOrganisation?.name}</Typography.Text>
            <Typography.Text> {t(`is now a Connected Organisation with`)} </Typography.Text>
            <Typography.Text strong>{notification.partnershipRequest?.receiverOrganisation?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.MembershipRequested: {
        // <b>[requester full name]</b> has requested to join <b>[organisation]</b>. View this request to confirm their employment.
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.profile?.name}</Typography.Text>
            <Typography.Text> {t(`has requested to join`)} </Typography.Text>
            <Typography.Text strong>{notification.membership?.organisation?.name}.</Typography.Text>
            <Typography.Text> {t(`View this request to confirm their employment`)}.</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.MembershipAccepted: {
        // You have been verified as an employee of <b>[organisation name]</b>.
        return (
          <Typography.Paragraph>
            <Typography.Text>{t(`You have been verified as an employee of`)} </Typography.Text>
            <Typography.Text strong>{notification.membership?.organisation?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.FollowerAccepted: {
        // <b>[organisation name]</b> has accepted your follow request. You can now explore their full page.
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.follower?.organisation?.name}</Typography.Text>
            <Typography.Text>
              {' '}
              {t(`has accepted your follow request. You can now explore their full page`)}.
            </Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.MembershipPermissionsUpdated: {
        // You have been assigned as [role] of [organisation name].
        return (
          <Typography.Paragraph>
            <Typography.Text>
              {t('You have been assigned as {{role}} of', {
                role: (notification?.data?.permissions || notification.membership?.permissions || []).join(', '),
              })}{' '}
            </Typography.Text>
            <Typography.Text strong>{notification.membership?.organisation?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.EventInvitationByGuest: {
        // <b>[name of inviting user]</b> has invited you to an event: <b>[event name]</b>
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.eventInvitation?.inviterProfile?.name}</Typography.Text>
            <Typography.Text> {t(`has invited you to an event:`)} </Typography.Text>
            <Typography.Text strong>{notification.eventInvitation?.event?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.EventInvitationByHosts: {
        // <b>[organisation name]</b> has invited you to an event: <b>[event name]</b>
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.eventInvitation?.event?.organisation?.name}</Typography.Text>
            <Typography.Text> {t(`has invited you to an event:`)} </Typography.Text>
            <Typography.Text strong>{notification.eventInvitation?.event?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.EventInvitationApproved: {
        // <b>[Org name]</b> has accepted your request to attend <b>[event name]</b>
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.eventInvitation?.event?.organisation?.name}</Typography.Text>
            <Typography.Text> {t(`has accepted your request to attend`)} </Typography.Text>
            <Typography.Text strong>{notification.eventInvitation?.event?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.NewEventUpdate: {
        // <b>[Org name]</b> has posted an update in <b>[event name]</b>
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.eventInvitation?.event?.organisation?.name}</Typography.Text>
            <Typography.Text> {t(`has posted an update in`)} </Typography.Text>
            <Typography.Text strong>{notification.eventInvitation?.event?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.EventLocationChanged:
        // <b>[Org name]</b> has updated the location of <b>[event name]</b>
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.eventInvitation?.event?.organisation?.name}</Typography.Text>
            <Typography.Text> {t(`has updated the location of`)} </Typography.Text>
            <Typography.Text strong>{notification.eventInvitation?.event?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      case NotificationType.EventDateTimeChanged:
        // <b>[Org name]</b> has updated the date / time of <b>[event name]</b>
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.eventInvitation?.event?.organisation?.name}</Typography.Text>
            <Typography.Text> {t(`has made a change to the date / time of`)} </Typography.Text>
            <Typography.Text strong>{notification.eventInvitation?.event?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      case NotificationType.IncentiveDateChanged:
        // <b>[Org name]</b> has made a change to the dates of <b>[incentive title]</b>
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.incentiveParticipant?.incentive?.organisation?.name}</Typography.Text>
            <Typography.Text> {t(`has made a change to the dates of`)} </Typography.Text>
            <Typography.Text strong>{notification.incentiveParticipant?.incentive?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      case NotificationType.IncentiveInvitationByParticipant:
        // <b>[name of inviting user]</b> has invited you to an incentive: <b>[incentive title]</b>
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.incentiveParticipant?.inviterProfile?.name}</Typography.Text>
            <Typography.Text> {t(`has invited you to an incentive:`)} </Typography.Text>
            <Typography.Text strong>{notification.incentiveParticipant?.incentive?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      case NotificationType.IncentiveInvitationByHosts:
        // <b>[organisation name]</b> has invited you to an incentive: <b>[incentive title]</b>
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.incentiveParticipant?.incentive?.organisation?.name}</Typography.Text>
            <Typography.Text> {t(`has invited you to an incentive:`)} </Typography.Text>
            <Typography.Text strong>{notification.incentiveParticipant?.incentive?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      case NotificationType.IncentiveRegistrationRequested:
        // <b>[requester full name]</b> has requested to register in <b>[incentive title]</b>. View this request to confirm their participation.
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.incentiveParticipant?.profile?.name}</Typography.Text>
            <Typography.Text> {t(`has requested to register in`)} </Typography.Text>
            <Typography.Text strong>{notification.incentiveParticipant?.incentive?.name}.</Typography.Text>
            <Typography.Text> {t(`View this request to confirm their participation`)}.</Typography.Text>
          </Typography.Paragraph>
        );
      case NotificationType.IncentiveRegistrationApproved:
        // <b>[Org name]</b> has accepted your request to register <b>[incentive title]</b>
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.incentiveParticipant?.incentive?.organisation?.name}</Typography.Text>
            <Typography.Text> {t(`has accepted your request to register`)} </Typography.Text>
            <Typography.Text strong>{notification.incentiveParticipant?.incentive?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      case NotificationType.NewIncentiveUpdate: {
        // <b>[Org name]</b> has posted an update in <b>[incentive title]</b>
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.incentiveParticipant?.incentive?.organisation?.name}</Typography.Text>
            <Typography.Text> {t(`has posted an update in`)} </Typography.Text>
            <Typography.Text strong>{notification.incentiveParticipant?.incentive?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.WebinarDateChanged:
        // <b>[Org name]</b> has made a change to the date / time of <b>[webinar title]</b>
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.webinarParticipant?.webinar?.organisation?.name}</Typography.Text>
            <Typography.Text> {t(`has made a change to the date / time of`)} </Typography.Text>
            <Typography.Text strong>{notification.webinarParticipant?.webinar?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      case NotificationType.WebinarInvitationByParticipant:
        // <b>[name of inviting user]</b> has invited you to a webinar: <b>[webinar title]</b>
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.webinarParticipant?.inviterProfile?.name}</Typography.Text>
            <Typography.Text> {t(`has invited you to a webinar:`)} </Typography.Text>
            <Typography.Text strong>{notification.webinarParticipant?.webinar?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      case NotificationType.WebinarInvitationByHosts:
        // <b>[organisation name]</b> has invited you to a webinar: <b>[webinar title]</b>
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.webinarParticipant?.webinar?.organisation?.name}</Typography.Text>
            <Typography.Text> {t(`has invited you to a webinar:`)} </Typography.Text>
            <Typography.Text strong>{notification.webinarParticipant?.webinar?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      case NotificationType.WebinarRegistrationRequested:
        // <b>[requester full name]</b> has requested to register in <b>[webinar title]</b>. View this request to confirm their participation.
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.webinarParticipant?.profile?.name}</Typography.Text>
            <Typography.Text> {t(`has requested to register in`)} </Typography.Text>
            <Typography.Text strong>{notification.webinarParticipant?.webinar?.name}.</Typography.Text>
            <Typography.Text> {t(`View this request to confirm their participation`)}.</Typography.Text>
          </Typography.Paragraph>
        );
      case NotificationType.WebinarRegistrationApproved:
        // <b>[Org name]</b> has accepted your request to register <b>[webinar title]</b>
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.webinarParticipant?.webinar?.organisation?.name}</Typography.Text>
            <Typography.Text> {t(`has accepted your request to register`)} </Typography.Text>
            <Typography.Text strong>{notification.webinarParticipant?.webinar?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      case NotificationType.NewWebinarUpdate: {
        // <b>[Org name]</b> has posted an update in <b>[webinar title]</b>
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.webinarParticipant?.webinar?.organisation?.name}</Typography.Text>
            <Typography.Text> {t(`has posted an update in`)} </Typography.Text>
            <Typography.Text strong>{notification.webinarParticipant?.webinar?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.WebinarParticipantAddedAsHostAdmin: {
        // <b>[organisation name]</b> has assigned you as a [Host Admin of / Host of / Speaker at]: <b>[webinar title]</b>
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.webinarParticipant?.webinar?.organisation?.name}</Typography.Text>
            <Typography.Text> {t(`has assigned you as a Host Admin of`)} </Typography.Text>
            <Typography.Text strong>{notification.webinarParticipant?.webinar?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.WebinarParticipantAddedAsHost: {
        // <b>[organisation name]</b> has assigned you as a [Host Admin of / Host of / Speaker at]: <b>[webinar title]</b>
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.webinarParticipant?.webinar?.organisation?.name}</Typography.Text>
            <Typography.Text> {t(`has assigned you as a Host of`)} </Typography.Text>
            <Typography.Text strong>{notification.webinarParticipant?.webinar?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.WebinarParticipantAddedAsSpeaker: {
        // <b>[organisation name]</b> has assigned you as a [Host Admin of / Host of / Speaker at]: <b>[webinar title]</b>
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.webinarParticipant?.webinar?.organisation?.name}</Typography.Text>
            <Typography.Text> {t(`has assigned you as a Speaker at`)} </Typography.Text>
            <Typography.Text strong>{notification.webinarParticipant?.webinar?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.SuggestFollow: {
        return (
          <Typography.Paragraph>
            <Typography.Text>
              {t(
                `You’ve had a suggestion to follow some new organisations that travel industry professionals similar to you also follow. Click here to check them out. You can unfollow at any time.`,
              )}{' '}
            </Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.PostComment: {
        const usersFullNames = notification.data.users.map(({ name }: { name: string }) => name);
        const countOfUsers = notification.data.users.length;
        const objectOfStrings = getStringsForNotifications(countOfUsers, usersFullNames, t);

        return (
          <Typography.Paragraph>
            <Typography.Text>
              <strong style={{ textTransform: 'capitalize' }}>
                {objectOfStrings.usersFullNamesString} {objectOfStrings.other}
              </strong>
              {objectOfStrings.has}
              {' ' + t(`commented on your post. Click here to reply or like their comment.`)}
            </Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.CommentReact: {
        const usersFullNames = notification.data.users.map(({ name }: { name: string }) => name);
        const countOfUsers = notification.data.users.length;
        const objectOfStrings = getStringsForNotifications(countOfUsers, usersFullNames, t);

        return (
          <Typography.Paragraph>
            <Typography.Text>
              <strong style={{ textTransform: 'capitalize' }}>
                {objectOfStrings.usersFullNamesString} {objectOfStrings.other}
              </strong>
              {objectOfStrings.has}
              {' ' +
                t(`reacted to your comment on {{organisationName}}’s post.`, {
                  organisationName: notification?.organisation?.name ?? '',
                })}
            </Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.PostReact: {
        const usersFullNames = notification.data.users.map(({ name }: { name: string }) => name);
        const countOfUsers = notification.data.users.length;
        const objectOfStrings = getStringsForNotifications(countOfUsers, usersFullNames, t);

        return (
          <Typography.Paragraph>
            <Typography.Text>
              <strong style={{ textTransform: 'capitalize' }}>
                {objectOfStrings.usersFullNamesString} {objectOfStrings.other}
              </strong>
              {objectOfStrings.has}
              {' ' +
                t(`reacted to your post. Click here to view your post.`, {
                  organisationName: notification?.organisation?.name ?? '',
                })}
            </Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.PostShared: {
        return (
          <Typography.Paragraph>
            <Typography.Text>
              <strong style={{ textTransform: 'capitalize' }}>{notification.organisation?.name}</strong>{' '}
              {t(`has reshared your post with their network.`)}
            </Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.NewFollower: {
        const usersFullNames = notification.data?.users?.map(({ name }: { name: string }) => name);
        const countOfUsers = notification.data?.users?.length;
        const objectOfStrings = getStringsForNotifications(countOfUsers, usersFullNames, t);
        return (
          <Typography.Paragraph>
            <Typography.Text>
              <strong style={{ textTransform: 'capitalize' }}>
                {objectOfStrings.usersFullNamesString} {objectOfStrings.other}
              </strong>
              {objectOfStrings.has}
              {' ' + t(`requested to follow `)}
              <strong>{notification?.organisation?.name ?? ''}</strong>
              {'. ' + t(`Click here to approve or decline their request${countOfUsers > 1 ? 's' : ''}.`)}
            </Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.CommentMention: {
        return (
          <Typography.Paragraph>
            <Typography.Text>
              <strong style={{ textTransform: 'capitalize' }}>{notification.profile?.name}</strong>{' '}
              {t(`mentioned you in a comment.`)}
            </Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.PostMention: {
        return (
          <Typography.Paragraph>
            <Typography.Text>
              <strong style={{ textTransform: 'capitalize' }}>{notification.organisation?.name}</strong>{' '}
              {t(`mentioned you in a post.`)}
            </Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.OrgPostMention: {
        return (
          <Typography.Paragraph>
            <Typography.Text>
              <strong style={{ textTransform: 'capitalize' }}>{notification.organisation?.name}</strong>{' '}
              {t(`has mentioned `)}{' '}
              <strong style={{ textTransform: 'capitalize' }}>{notification?.data?.organisation?.name}</strong>{' '}
              {t(` in a post.`)}
            </Typography.Text>
          </Typography.Paragraph>
        );
      }

      case NotificationType.OrgCommentMention: {
        return (
          <Typography.Paragraph>
            <Typography.Text>
              <strong style={{ textTransform: 'capitalize' }}>{notification.profile?.name}</strong>{' '}
              {t(`has mentioned `)}{' '}
              <strong style={{ textTransform: 'capitalize' }}>{notification?.data?.organisation?.name}</strong>{' '}
              {t(` in a comment.`)}
            </Typography.Text>
          </Typography.Paragraph>
        );
      }

      case NotificationType.RecurringPaymentReminder: {
        return (
          <Typography.Paragraph>
            <Typography.Text>
              {t(`Your paid subscription with`) + ' '}
              <strong style={{ textTransform: 'capitalize' }}>
                {notification.partnershipRequest?.senderOrganisation?.name}
              </strong>{' '}
              {t(`will renew soon. Please ensure that your active payment method is still valid.`)}
            </Typography.Text>
          </Typography.Paragraph>
        );
      }

      case NotificationType.MembershipAutoApprove: {
        // <b>[requester full name]</b> has requested to join <b>[organisation]</b>. View this request to confirm their employment.
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.profile?.name}</Typography.Text>
            <Typography.Text> {t(`has joined`)} </Typography.Text>
            <Typography.Text strong>{notification.membership?.organisation?.name}</Typography.Text>
            <Typography.Text> {t(`using a signup invite link. View pending employee requests.`)}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.ParentMembershipRequested: {
        // <b>[requester full name]</b> has requested to become a member of <b>[organisation]</b>. View this request to confirm their membership.
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.profile?.name}</Typography.Text>
            <Typography.Text> {t(`has requested to become a member of`)} </Typography.Text>
            <Typography.Text strong>{notification.membership?.organisation?.name}.</Typography.Text>
            <Typography.Text> {t(`View this request to confirm their membership`)}.</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.ParentMembershipAccepted: {
        // You have been verified as a member of <b>[organisation name]</b>.
        return (
          <Typography.Paragraph>
            <Typography.Text>{t(`You have been verified as a member of`)} </Typography.Text>
            <Typography.Text strong>{notification.membership?.organisation?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.ParentMembershipDeclined: {
        // You have been declined as a member of <b>[organisation name]</b>.
        return (
          <Typography.Paragraph>
            <Typography.Text>{t(`You have been declined as a member of`)} </Typography.Text>
            <Typography.Text strong>{notification.organisation?.name}</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.OrgPostReminder7Days: {
        // It’s been a week since <b>[organisation name]</b>’s last post. Create a post to keep your community engaged
        return (
          <Typography.Paragraph>
            <Typography.Text>{t(`It’s been a week since`)} </Typography.Text>
            <Typography.Text strong>{notification.organisation?.name}</Typography.Text>
            <Typography.Text>{t(`’s last post. Create a post to keep your community engaged`)}.</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.OrgPostReminder14Days: {
        // It’s been two weeks since <b>[organisation name]</b>’s last post and your community is waiting to hear from you. Create your next post now
        return (
          <Typography.Paragraph>
            <Typography.Text>{t(`It’s been two weeks since`)} </Typography.Text>
            <Typography.Text strong>{notification.organisation?.name}</Typography.Text>
            <Typography.Text>
              {t(`’s last post and your community is waiting to hear from you. Create your next post now`)}.
            </Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.InactiveUserReminder: {
        const organisationNames = notification.data?.map(
          ({ organisationName }: { organisationName: string }) => organisationName,
        );
        const countOfOrgs = organisationNames.length;
        const objectOfStrings = getOrgNameStringsForNotifications(countOfOrgs, organisationNames);
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{objectOfStrings.organisationNamesString} </Typography.Text>
            <Typography.Text>{objectOfStrings.has}</Typography.Text>
            <Typography.Text> {t(` shared new posts recently. View the latest updates in your feed`)}.</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.InboxMessage: {
        return (
          <Typography.Paragraph>
            <Typography.Text strong>{notification.profile?.name}</Typography.Text>
            <Typography.Text>{t(`: `)} </Typography.Text>
            <Typography.Text>{notification.data?.text}.</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.DailyLoginStreak: {
        return (
          <Typography.Paragraph>
            <Typography.Text>You're on a roll! You hit a daily streak of </Typography.Text>
            <Typography.Text strong>{get(notification, 'data.streakCount', 0)}</Typography.Text>
            <Typography.Text> days.</Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.NewAchievement: {
        const achievementType = get(notification, 'data.achievementType', '');

        return (
          <Typography.Paragraph>
            <Typography.Text>
              Congratulations! 🎉 You have unlocked the <b>{achievementType}</b> achievement.
            </Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.NewOrganisationAchievement: {
        const achievementType = get(notification, 'data.achievementType', '');
        const level = get(notification, 'data.level', '');

        return (
          <Typography.Paragraph>
            {level ? (
              <Typography.Text>
                Congratulations! 🎉 You have reached level {level} of the{' '}
                <b>
                  {notification.organisation?.name} {achievementType}
                </b>{' '}
                achievement.
              </Typography.Text>
            ) : (
              <Typography.Text>
                Congratulations! 🎉 You have unlocked the{' '}
                <b>
                  {notification.organisation?.name} {achievementType}
                </b>{' '}
                achievement.
              </Typography.Text>
            )}
          </Typography.Paragraph>
        );
      }
      case NotificationType.MonthlyAchievementSummary: {
        const uniqueOrgCount = get(notification, 'data.uniqueOrgCount', '');

        return (
          <Typography.Paragraph>
            <Typography.Text>
              You've unlocked{' '}
              <b>{`${
                uniqueOrgCount === 1
                  ? `a new Organisation Achievement!`
                  : `new Achievements for ${uniqueOrgCount} Organisations!`
              }`}</b>{' '}
              View your latest achievements now.
            </Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.NewTier: {
        const currentTier = get(notification, 'data.currentTier', '');
        const nextTier = get(notification, 'data.nextTier', '');
        const isTierRetained = get(notification, 'data.isTierRetained', '');

        if (currentTier === 'Blue') {
          return (
            <Typography.Paragraph>
              <Typography.Text>
                Welcome to the <b>Club Hablo!</b> Keep earning Kudos to move up to Silver Tier! 🥈
              </Typography.Text>
            </Typography.Paragraph>
          );
        }

        if (isTierRetained) {
          return (
            <Typography.Paragraph>
              <Typography.Text>
                Congratulations {get(notification, 'profile.name', '')} you have retained {currentTier} tier! 🥳
                {nextTier ? ` Next stop ${nextTier} tier! 🙌` : ''}
              </Typography.Text>
            </Typography.Paragraph>
          );
        }

        return (
          <Typography.Paragraph>
            <Typography.Text>
              Congratulations {get(notification, 'profile.name', '')} you are now a {currentTier} tier member! 🥳
              {nextTier ? ` Next stop ${nextTier} tier! 🙌` : ''}
            </Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.WeeklySummary: {
        const pointsEarned = get(notification, 'data.pointsEarned', 0);
        const rollingPoints = get(notification, 'data.rollingPoints', 0);
        const nextTier = get(notification, 'data.nextTier', '');
        const nextTierPoints = get(notification, 'data.nextTierPoints', 0);
        const userName = get(notification, 'profile.name', '');

        return (
          <Typography.Paragraph>
            <Typography.Text>
              Congratulations {userName} you have earned {pointsEarned} points last week.
              {nextTier ? ` You need ${nextTierPoints - rollingPoints} points to reach ${nextTier} tier.` : ''}
            </Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.HighFiveAchievement: {
        return (
          <Typography.Paragraph>
            <Typography.Text>
              Nice work! <b>{notification.profile?.name}</b> at <b>{notification.organisation?.name}</b> gave you a{' '}
              <b>High Five</b>!
            </Typography.Text>
          </Typography.Paragraph>
        );
      }
      case NotificationType.OrgPartnerRequestReceived:
        return (
          <Typography.Paragraph>
            <Typography.Text>
              {notification.data?.senderOrgName} has requested to become a Partner Organisation with{' '}
              {notification.data?.receiverOrgName}. View this request.
            </Typography.Text>
          </Typography.Paragraph>
        );
      case NotificationType.OrgPartnerAcceptedSender:
        return (
          <Typography.Paragraph>
            <Typography.Text>
              {notification.data?.receiverOrgName} has approved your request to become a Partner Organisation.
            </Typography.Text>
          </Typography.Paragraph>
        );
      case NotificationType.OrgPartnerRequestRejected:
        return (
          <Typography.Paragraph>
            <Typography.Text>
              {notification.data?.senderOrgName} has rejected your request to become a Partner Organisation.
            </Typography.Text>
          </Typography.Paragraph>
        );
      case NotificationType.OrgPartnerAcceptedReceiver:
        return (
          <Typography.Paragraph>
            <Typography.Text>
              {notification.data?.senderOrgName} is now a Partner Organisation with {notification.data?.receiverOrgName}
              .
            </Typography.Text>
          </Typography.Paragraph>
        );

      default: {
        return <div></div>;
      }
    }
  }

  private static getIcon(notification: Notification): Icons {
    switch (notification.type) {
      case NotificationType.InvitationAccepted:
      case NotificationType.InvitationReceived:
        return Icons.users;
      case NotificationType.PartnershipRequestReceived:
      case NotificationType.PartnershipRequestApproved:
      case NotificationType.PartnershipRequestApprovedByOrganisation:
      case NotificationType.MembershipRequested:
      case NotificationType.MembershipAutoApprove:
      case NotificationType.ParentMembershipRequested:
        return Icons.security;
      case NotificationType.MembershipAccepted:
      case NotificationType.FollowerAccepted:
      case NotificationType.MembershipPermissionsUpdated:
      case NotificationType.OrganisationOwnershipAccepted:
      case NotificationType.OrganisationOwnershipRequested:
      case NotificationType.ParentMembershipAccepted:
      case NotificationType.ParentMembershipDeclined:
        return Icons.explore;
      case NotificationType.EventInvitationByGuest:
      case NotificationType.EventInvitationByHosts:
      case NotificationType.EventInvitationApproved:
      case NotificationType.NewEventUpdate:
      case NotificationType.EventLocationChanged:
      case NotificationType.EventDateTimeChanged:
        return Icons.calendar;
      case NotificationType.IncentiveDateChanged:
      case NotificationType.IncentiveInvitationByHosts:
      case NotificationType.IncentiveInvitationByParticipant:
      case NotificationType.IncentiveRegistrationApproved:
      case NotificationType.IncentiveRegistrationRequested:
      case NotificationType.NewIncentiveUpdate:
        return Icons.incentives;
      case NotificationType.WebinarDateChanged:
      case NotificationType.WebinarInvitationByHosts:
      case NotificationType.WebinarInvitationByParticipant:
      case NotificationType.WebinarRegistrationApproved:
      case NotificationType.WebinarRegistrationRequested:
      case NotificationType.NewWebinarUpdate:
      case NotificationType.WebinarParticipantAddedAsHostAdmin:
      case NotificationType.WebinarParticipantAddedAsHost:
      case NotificationType.WebinarParticipantAddedAsSpeaker:
        return Icons.webinar;
      case NotificationType.PostShared:
      case NotificationType.OrgPostMention:
      case NotificationType.OrgPostReminder7Days:
      case NotificationType.OrgPostReminder14Days:
      case NotificationType.InactiveUserReminder:
        return Icons.posts;
      case NotificationType.PostComment:
      case NotificationType.CommentMention:
      case NotificationType.OrgCommentMention:
        return Icons.comment;
      case NotificationType.CommentReact:
        return Icons.like;
      case NotificationType.PostReact:
        return Icons.like;
      case NotificationType.RecurringPaymentReminder:
        return Icons.money;
      case NotificationType.DailyLoginStreak:
        return Icons.loginStreak;
      case NotificationType.NewAchievement:
      case NotificationType.NewOrganisationAchievement:
        return Icons.achievement;
      case NotificationType.NewTier:
        return Icons.kudosFlat;
      case NotificationType.InboxMessage:
        return Icons.chat;
      case NotificationType.HighFiveAchievement:
        return Icons.highfiveIcon;
      case NotificationType.OrgPartnerRequestReceived:
      case NotificationType.OrgPartnerAcceptedSender:
      case NotificationType.OrgPartnerAcceptedReceiver:
        return Icons.users;

      default: {
        return Icons.time;
      }
    }
  }
}
