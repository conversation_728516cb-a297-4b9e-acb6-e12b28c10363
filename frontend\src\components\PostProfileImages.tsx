import styled from 'styled-components';
import { Link } from 'react-router-dom';

import { encodePathParams } from '@utils/encodePathParams';
import { Routes } from '@Routes';
import { Avatar } from './Image/Image';
import { GUTTER_MD_PX } from '@theme';

type Props = {
  profiles: any[];
  show?: number;
  imageSize?: number;
  totalCount?: number;
};

export function PostProfileImages({ profiles, imageSize = 34 }: Props) {
  return (
    <Container>
      {profiles.map((profile, i) =>
        profile?.image ? (
          <ImageContainer
            key={i}
            $imageSize={imageSize}
            to={encodePathParams(Routes.profileById, { profileId: profile.image })}
            style={{ zIndex: i === 0 ? 1 : 0 }}
          >
            <ProfileImage width={imageSize} id={profile.image} />
          </ImageContainer>
        ) : (
          <></>
        ),
      )}
    </Container>
  );
}

const Container = styled.div`
  display: flex;
  flex-direction: row;
  margin-top: ${GUTTER_MD_PX};
  align-items: center;
  position: relative;
`;

const ImageContainer = styled(Link)<{ $imageSize: number }>`
  :not(:first-child) {
    margin-left: -${({ $imageSize }) => $imageSize / 2}px;
    margin-top: -${({ $imageSize }) => $imageSize / 1}px;
    z-index: 999;
  }
  display: block;
  position: relative;
`;

const ProfileImage = styled(Avatar)`
  position: relative;
  background-color: white;
  border: 1px solid #eaeaea;
  display: block;
  z-index: -1;
`;
