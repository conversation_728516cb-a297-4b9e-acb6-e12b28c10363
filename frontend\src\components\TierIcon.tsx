import styled from 'styled-components';

import Icon, { Icons } from './icons/Icon';
import { Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import { TooltipPlacement } from 'antd/es/tooltip';

type TierProps = {
  [key: string]: {
    icon: Icons;
    desc: string;
    color: string;
  };
};

type TierIconProps = {
  tier: string;
  position: TooltipPlacement | undefined;
  iconSize?: number;
  flatIcon?: boolean;
  spacing?: string;
  hideBlueTier?: boolean;
};

const TierIcon = ({ tier, position, iconSize, flatIcon, spacing = '5px', hideBlueTier }: TierIconProps) => {
  const { t } = useTranslation();

  const tierDetails: TierProps = {
    blue: {
      icon: flatIcon ? Icons.blueTierFlat : Icons.profileBlueTier,
      desc: t('Club Hablo {{tier}} Member', { tier: 'Blue' }),
      color: 'var(--color-primary)',
    },
    silver: {
      icon: flatIcon ? Icons.silverTierFlat : Icons.profileSilverTier,
      desc: t('Club Hablo {{tier}} Member', { tier: 'Silver' }),
      color: 'var(--color-silver)',
    },
    gold: {
      icon: flatIcon ? Icons.goldTierFlat : Icons.profileGoldTier,
      desc: t('Club Hablo {{tier}} Member', { tier: 'Gold' }),
      color: 'var(--color-gold)',
    },
    platinum: {
      icon: flatIcon ? Icons.platinumTierFlat : Icons.profilePlatinumTier,
      desc: t('Club Hablo {{tier}} Member', { tier: 'Platinum' }),
      color: 'var(--color-platinum)',
    },
  };

  if (hideBlueTier && tier === 'blue') return null;

  const tierDetail = tierDetails[tier];

  if (!tierDetail) {
    console.warn('Tier not found', tier);
    return null;
  }

  return (
    <div style={{ display: 'inline-block' }}>
      <Tooltip
        title={tierDetail.desc}
        placement={position}
        color={tierDetail.color}
        overlayInnerStyle={{ backgroundColor: 'black', fontWeight: 700, fontSize: '12px' }}
      >
        <div>
          <StyledIcon
            icon={tierDetail.icon}
            size={iconSize || 34}
            style={{ marginLeft: spacing, marginTop: '-4px', cursor: 'pointer' }}
          />
        </div>
      </Tooltip>
    </div>
  );
};

export default TierIcon;

const StyledIcon = styled(Icon)`
  -webkit-mask-image: linear-gradient(45deg, #000 25%, rgba(0, 0, 0, 0.2) 50%, #000 75%);
  mask-image: linear-gradient(45deg, #000 25%, rgba(0, 0, 0, 0.2) 50%, #000 75%);
  -webkit-mask-size: 800%;
  mask-size: 800%;
  transition: mask-position 1s ease, -webkit-mask-position 1s ease;
  -webkit-mask-position: 0;
  mask-position: 0;
  opacity: 1;

  &:hover {
    transition: mask-position 1s ease, -webkit-mask-position 1s ease;
    -webkit-mask-position: 120%;
    mask-position: 120%;
    opacity: 1;
  }
`;
