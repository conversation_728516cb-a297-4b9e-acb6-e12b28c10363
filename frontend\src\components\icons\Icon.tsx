import { CSSProperties, FunctionComponent, MouseEventHandler, SVGProps, Suspense, lazy, useMemo } from 'react';
import styled from 'styled-components';

// To add new icons, extract SVG from figma and run it through this optimize service:
// https://jakearchibald.github.io/svgomg/
// Replace fill value if set with `currentColor` to ensure it will inherit color
// Use viewBox over width/height to ensure size can be set viewBox width/height can be different but best to make them square

export enum Icons {
  accessDenied = 'accessDenied',
  add = 'add',
  addPhoto = 'addPhoto',
  arrow = 'arrow',
  arrowBack = 'arrowBack',
  arrowDown = 'arrowDown',
  arrowNext = 'arrowNext',
  arrowUp = 'arrowUp',
  achievement = 'achievement',
  achievementWhite = 'achievement-white',
  block = 'block',
  broadcast = 'broadcast',
  broadcastEnd = 'broadcastEnd',
  callEnded = 'callEnded',
  caret = 'caret',
  case = 'case',
  calendar = 'calendar',
  calendarAdd = 'calendarAdd',
  calendarCheck = 'calendarCheck',
  calendarClose = 'calendarClose',
  chart = 'chart',
  chat = 'chat',
  check = 'check',
  checkCircle = 'checkCircle',
  chevronUp = 'chevronUp',
  chevronDown = 'chevronDown',
  chevronDown_blue = 'chevronDown_blue',
  circle = 'circle',
  clock = 'clock',
  close = 'close',
  closeCircle = 'closeCircle',
  creditCard = 'creditCard',
  cupBlue = 'cupBlue',
  dataError = 'dataError',
  delete = 'delete',
  destination = 'destination',
  download = 'download',
  dropDownArrow = 'dropDownArrow',
  edit = 'edit',
  exit = 'exit',
  empty = 'empty',
  explore = 'explore',
  feedback = 'feedback',
  fileText = 'fileText',
  globe = 'globe',
  help = 'help',
  home = 'home',
  incentives = 'incentives',
  info = 'info',
  infoOutlined = 'infoOutlined',
  job = 'job',
  label = 'label',
  link = 'link',
  location = 'location',
  logo = 'logo',
  logoWhite = 'logoWhite',
  logoAndName = 'logoAndName',
  logoAndNameWhite = 'logoAndNameWhite',
  logoHollow = 'logoHollow',
  mail = 'mail',
  mic = 'mic',
  micOff = 'micOff',
  mms = 'mms',
  more = 'more',
  newMessage = 'newMessage',
  notFound = 'notFound',
  notifications = 'notifications',
  organisation = 'organisation',
  page = 'page',
  phone = 'phone',
  photo = 'photo',
  posts = 'posts',
  privacy = 'privacy',
  privateSector = 'privateSector',
  report = 'report',
  resources = 'resources',
  screenshare = 'screenshare',
  search = 'search',
  security = 'security',
  serverDown = 'serverDown',
  settings = 'settings',
  share = 'share',
  signOut = 'signOut',
  star = 'star',
  starColored = 'starColored',
  time = 'time',
  timeFill = 'timeFill',
  timer = 'timer',
  tourOperator = 'tourOperator',
  travelAgent = 'travelAgent',
  userAdd = 'userAdd',
  userAdded = 'userAdded',
  userRemove = 'userRemove',
  users = 'users',
  usersAdd = 'usersAdd',
  usersRemove = 'usersRemove',
  video = 'video',
  videoPlay = 'videoPlay',
  videoOff = 'videoOff',
  views = 'views',
  visible = 'visible',
  warningOutlined = 'warningOutlined',
  webinar = 'webinar',
  worldMapConnected = 'worldMapConnected',
  comment = 'Comment',
  like = 'Like',
  reply = 'reply',
  money = 'money',
  habloAI = 'HabloAI',
  ChatLike = 'ChatLike',
  DisLike = 'DisLike',
  LikeClicked = 'LikeClicked',
  DisLikeClicked = 'DisLikeClicked',
  downloadDocument = 'downloadDocument',
  document = 'document',
  habloLogoSqaure = 'habloLogoSqaure',
  facebook = 'facebook',
  whatsapp = 'whatsapp',
  linkedin = 'linkedin',
  twitter = 'twitter',
  x_twitter = 'x_twitter',
  repost = 'repost',
  copyLink = 'copyLink',
  clock_blue = 'clock_blue',
  clock_dark = 'clock_dark',
  clock_grey = 'clock_grey',
  trash = 'trash',
  bin = 'bin',
  user = 'user',
  stepIcon = 'stepIcon',
  stepProcessIcon = 'stepProcessIcon',
  doubleArrowRight = 'doubleArrowRight',
  goExternal = 'goExternal',
  notificationOn = 'notificationOn',
  personCheck = 'personCheck',
  postDelete = 'postDelete',
  postLike = 'postLike',
  commentLike = 'commentLike',
  incentive = 'incentive',
  emailPlus = 'emailPlus',
  emailCheck = 'emailCheck',
  monitor = 'monitor',
  login = 'login',
  habloAIBlue = 'hablo-ai',
  kudos = 'kudos',
  kudosFlat = 'kudos-flat',
  loginStreak = 'loginStreak',
  blueTier = 'blue-tier',
  goldTier = 'gold-tier',
  platinumTier = 'platinum-tier',
  silverTier = 'silver-tier',
  blueTierFlat = 'blueTierFlat',
  goldTierFlat = 'goldTierFlat',
  platinumTierFlat = 'platinumTierFlat',
  silverTierFlat = 'silverTierFlat',
  profilePerfect = 'profilePerfectBadge',
  dailyQuizStreak = 'dailyQuizStreakBadge',
  hotStreak = 'hotStreakBadge',
  habloFounder = 'founderBadge',
  communityBeaconBadge = 'communityBeaconBadge',
  industryInfluencerBadge = 'industryInfluencerBadge',
  ambassadorBadge = 'ambassadorBadge',
  wellbeingBadge = 'wellbeingBadge',
  powerScrollerBadge = 'powerScrollerBadge',
  avidViewerBadge = 'avidViewerBadge',
  incentiveWarriorBadge = 'incentiveWarriorBadge',
  profileBlueTier = 'profileBlueTier',
  profileSilverTier = 'profileSilverTier',
  profileGoldTier = 'profileGoldTier',
  profilePlatinumTier = 'profilePlatinumTier',
  highfiveIcon = 'highfiveIcon',
  highfiveBadge = 'highfiveBadge',
  recentlyViewed = 'recentlyViewed',
  bookmark = 'bookmark',
  bookmarked = 'bookmarked',
  orgAchievementLevelIndicator = 'orgAchievementLevelIndicator',
  achievementLocked = 'achievement-locked',
  levelDoubleColour = 'levelDoubleColour',
  orgAchievementsFrame = 'orgAchievementsFrame',
  partners = 'partners',
  disable = 'disable',
}

type SVG = FunctionComponent<SVGProps<SVGSVGElement>>;

type Props = {
  icon: Icons;
  size?: number;
  color?: string;
  className?: string;
  onClick?: MouseEventHandler<HTMLElement>;
  active?: boolean;
  style?: CSSProperties;
  dataTestId?: string;
};

const getLazyIcon = (icon: string) => lazy(() => import(`./svgs/${icon}.svg?react`));

export default function Icon({
  icon,
  size = 32,
  color = 'currentColor',
  className,
  style,
  onClick,
  dataTestId,
}: Props) {
  const SVGIcon = useMemo(() => getLazyIcon(icon), [icon]);

  if (!SVGIcon) {
    console.warn(`Can not find icon "${icon}"`);
    return null;
  }

  return (
    <IconContainer className={className} style={style} onClick={onClick} size={size} data-testid={dataTestId}>
      <Suspense fallback={null}>
        <SVGIcon color={color} width={size} height={size * calculateRatio(SVGIcon)} />
      </Suspense>
    </IconContainer>
  );
}

function calculateRatio(svg: SVG) {
  const viewBox = svg?.defaultProps?.viewBox;
  if (viewBox) {
    const [width, height] = viewBox.split(' ').slice(2);
    return parseInt(height) / parseInt(width);
  }
  return 1;
}

export const IconContainer = styled.span<{ size: number }>`
  display: inline-flex !important; //deal with an Safari styling
  vertical-align: middle !important;

  svg {
    overflow: hidden; //deal with an IE11 bug: https://stackoverflow.com/questions/44827875/issues-with-whitespace-around-svg-in-ie11-related-to-text-in-the-svg?utm_medium=organic&utm_source=google_rich_qa&utm_campaign=google_rich_qa
  }
`;
