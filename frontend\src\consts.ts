import {
  IncentiveBookingType,
  IncentiveType,
  MembershipPermission,
  OrganisationSize,
  OrganisationType,
  OrganisationStatus,
  ProfileResponsibility,
  ProfileTypeOfHoliday,
  Region,
  NotificationPreferenceDataTypes,
} from '@GraphQLTypes';
import i18n from './locale/i18n';

import { SupportedLocales } from './locale/SupportedLocales';
import { getEnv } from './utils/getEnv';

export const HABLO_JOIN = {
  production: 'https://join.myhablo.com',
  beta: 'https://beta.myhablo.com/login',
  local: 'https://localhost.myhablo.com:3000/login',
  e2e: 'https://localhost.myhablo.com:3000/login',
};

export const DOMAIN_ORIGIN =
  getEnv() === 'local'
    ? 'https://localhost.myhablo.com:3000'
    : getEnv() === 'beta'
    ? 'https://beta.myhablo.com'
    : 'https://myhablo.com';

export const HABLO_TERMS = 'https://join.myhablo.com/terms';
export const HABLO_PRIVACY = 'https://join.myhablo.com/privacy';

export const LANGUAGES: { [locale in SupportedLocales]: string } = {
  en: 'English (UK)', //i18n.t('const-languages{:}English (UK)'),
  it: 'Italiano', //i18n.t('const-languages{:}Italian'),
  es: 'Español', //i18n.t('const-languages{:}Spanish'),
  de: 'Deutsch', //i18n.t('const-languages{:}German'),
  fr: 'Français', //i18n.t('const-languages{:}French'),
  zh: '中文', //i18n.t('const-languages{:}Chinese'),
};

export const REGIONS: { [region in Region]: string } = {
  [Region.Africa]: i18n.t('const-regions{:}Africa'),
  [Region.Asia]: i18n.t('const-regions{:}Asia'),
  [Region.Canada]: i18n.t('const-regions{:}Canada'),
  [Region.CentralAmerica]: i18n.t('const-regions{:}Central America'),
  [Region.Europe]: i18n.t('const-regions{:}Europe'),
  [Region.Mexico]: i18n.t('const-regions{:}Mexico'),
  [Region.MiddleEast]: i18n.t('const-regions{:}Middle East'),
  [Region.Oceania]: i18n.t('const-regions{:}Oceania'),
  [Region.SouthAmerica]: i18n.t('const-regions{:}South America'),
  [Region.TheCaribbean]: i18n.t('const-regions{:}The Caribbean'),
  [Region.UnitedStatesOfAmerica]: i18n.t('const-regions{:}United States Of America'),
};

export const TYPE_OF_HOLIDAYS: { [type in ProfileTypeOfHoliday]: string } = {
  [ProfileTypeOfHoliday.Business]: i18n.t('const-type-of-holidays{:}Business'),
  [ProfileTypeOfHoliday.CityBreaks]: i18n.t('const-type-of-holidays{:}City Breaks'),
  [ProfileTypeOfHoliday.Cruises]: i18n.t('const-type-of-holidays{:}Cruises'),
  [ProfileTypeOfHoliday.Educational]: i18n.t('const-type-of-holidays{:}Educational'),
  [ProfileTypeOfHoliday.EscortedTours]: i18n.t('const-type-of-holidays{:}Escorted Tours'),
  [ProfileTypeOfHoliday.Expedition]: i18n.t('const-type-of-holidays{:}Expedition'),
  [ProfileTypeOfHoliday.FlyDrive]: i18n.t('const-type-of-holidays{:}Fly Drive'),
  [ProfileTypeOfHoliday.Groups]: i18n.t('const-type-of-holidays{:}Groups'),
  [ProfileTypeOfHoliday.Leisure]: i18n.t('const-type-of-holidays{:}Leisure'),
  [ProfileTypeOfHoliday.Luxury]: i18n.t('const-type-of-holidays{:}Luxury'),
  [ProfileTypeOfHoliday.Niche]: i18n.t('const-type-of-holidays{:}Niche'),
  [ProfileTypeOfHoliday.PackageHolidays]: i18n.t('const-type-of-holidays{:}Package Holidays'),
  [ProfileTypeOfHoliday.Rail]: i18n.t('const-type-of-holidays{:}Rail'),
  [ProfileTypeOfHoliday.RoundTheWorld]: i18n.t('const-type-of-holidays{:}Round The World'),
  [ProfileTypeOfHoliday.Solo]: i18n.t('const-type-of-holidays{:}Solo'),
};

export const RESPONSIBILITIES: { [responsibility in ProfileResponsibility]: string } = {
  [ProfileResponsibility.Events]: i18n.t('const-responsibilities{:}Events'),
  [ProfileResponsibility.Marketing]: i18n.t('const-responsibilities{:}Marketing'),
  [ProfileResponsibility.Partnerships]: i18n.t('const-responsibilities{:}Partnerships'),
  [ProfileResponsibility.ProductAndContracting]: i18n.t('const-responsibilities{:}Product & Contracting'),
  [ProfileResponsibility.PublicRelations]: i18n.t('const-responsibilities{:}Public Relations'),
  [ProfileResponsibility.SalesAgent]: i18n.t('const-responsibilities{:}Sales Agent'),
  [ProfileResponsibility.SalesManager]: i18n.t('const-responsibilities{:}Sales Manager'),
  [ProfileResponsibility.Sales]: i18n.t('const-responsibilities{:}Sales'),
  [ProfileResponsibility.SeniorManagement]: i18n.t('const-responsibilities{:}Senior Management'),
  [ProfileResponsibility.SocialAndDigital]: i18n.t('const-responsibilities{:}Social & Digital'),
  [ProfileResponsibility.Training]: i18n.t('const-responsibilities{:}Training'),
  [ProfileResponsibility.Other]: i18n.t('const-responsibilities{:}Other'),
};

export const ORGANISATION_TYPES: { [type in OrganisationType]: string } = {
  [OrganisationType.RepresentationAgency]: i18n.t('const-organisation-type{:}Representation Agency'),
  [OrganisationType.TourOperator]: i18n.t('const-organisation-type{:}Tour Operator'),
  [OrganisationType.TravelAgency]: i18n.t('const-organisation-type{:}Travel Agency'),
  [OrganisationType.Destination]: i18n.t('const-organisation-type{:}Destination'),
  [OrganisationType.PrivateSector]: i18n.t('const-organisation-type{:}Private Sector'),
  [OrganisationType.Community]: i18n.t('const-organisation-type{:}Community'),
  [OrganisationType.Association]: i18n.t('const-organisation-type{:}Association'),
  [OrganisationType.Consortia]: i18n.t('const-organisation-type{:}Consortia'),
};

export const ORGANISATION_STATUSES: { [type in OrganisationStatus]: string } = {
  [OrganisationStatus.Active]: i18n.t('const-organisation-status{:}Active'),
  [OrganisationStatus.Removed]: i18n.t('const-organisation-status{:}Removed'),
  [OrganisationStatus.Suspended]: i18n.t('const-organisation-status{:}Suspended'),
  [OrganisationStatus.Pending]: i18n.t('const-organisation-status{:}Pending'),
};

export const ORGANISATION_SIZES: { [size in OrganisationSize]: string } = {
  [OrganisationSize.OrgSize1]: i18n.t('const-organisation-size{:}1 Employee'),
  [OrganisationSize.OrgSize2_10]: i18n.t('const-organisation-size{:}2-10 Employees'),
  [OrganisationSize.OrgSize11_50]: i18n.t('const-organisation-size{:}11-50 Employees'),
  [OrganisationSize.OrgSize51_200]: i18n.t('const-organisation-size{:}51-200 Employees'),
  [OrganisationSize.OrgSize201_500]: i18n.t('const-organisation-size{:}201-500 Employees'),
  [OrganisationSize.OrgSize501_1000]: i18n.t('const-organisation-size{:}501-1000 Employees'),
  [OrganisationSize.OrgSize1001_5000]: i18n.t('const-organisation-size{:}1001-5000 Employees'),
  [OrganisationSize.OrgSize5001]: i18n.t('const-organisation-size{:}5001+ Employees'),
};

export const MEMBERSHIP_PERMISSIONS: { [permission in MembershipPermission]: string } = {
  [MembershipPermission.Owner]: i18n.t('const-membership-permission{:}Owner'),
  [MembershipPermission.Admin]: i18n.t('const-membership-permission{:}Admin'),
  [MembershipPermission.HiddenAdmin]: i18n.t('const-membership-permission{:}Hidden Admin'),
  [MembershipPermission.Manager]: i18n.t('const-membership-permission{:}Manager'),
  [MembershipPermission.Editor]: i18n.t('const-membership-permission{:}Editor'),
  [MembershipPermission.Staff]: i18n.t('const-membership-permission{:}Staff'),
  [MembershipPermission.Linked]: i18n.t('const-membership-permission{:}Linked'),
  [MembershipPermission.OwnerPending]: i18n.t('const-membership-permission{:}Owner Pending'),
  [MembershipPermission.InviteMembersToEvents]: i18n.t('const-membership-permission{:}Invite Members To Events'),
  [MembershipPermission.Member]: i18n.t('const-membership-permission{:}Member'),
};

export const INCENTIVE_TYPES: { [type in IncentiveType]: string } = {
  [IncentiveType.CashbackOrVoucher]: i18n.t('const-incentive-type{:}Cashback or Voucher'),
  [IncentiveType.FamTrip]: i18n.t('const-incentive-type{:}FAM Trip'),
  [IncentiveType.Prize]: i18n.t('const-incentive-type{:}Prize'),
};

export const INCENTIVE_BOOKING_TYPES: { [type in IncentiveBookingType]: string } = {
  [IncentiveBookingType.Flight]: i18n.t('const-incentive-booking-type{:}Flight Booking'),
  [IncentiveBookingType.Hotel]: i18n.t('const-incentive-booking-type{:}Hotel Booking'),
  [IncentiveBookingType.Cruise]: i18n.t('const-incentive-booking-type{:}Cruise Booking'),
  [IncentiveBookingType.Other]: i18n.t('const-incentive-booking-type{:}Other'),
};

export const MONTHS: { [months: string]: string } = {
  0: i18n.t('const-months{:}January'),
  1: i18n.t('const-months{:}February'),
  2: i18n.t('const-months{:}March'),
  3: i18n.t('const-months{:}April'),
  4: i18n.t('const-months{:}May'),
  5: i18n.t('const-months{:}June'),
  6: i18n.t('const-months{:}July'),
  7: i18n.t('const-months{:}August'),
  8: i18n.t('const-months{:}September'),
  9: i18n.t('const-months{:}October'),
  10: i18n.t('const-months{:}November'),
  11: i18n.t('const-months{:}December'),
};

export const canConnectWithData = [
  {
    value: 'ConnectedOrganisation',
    text: 'People at your Connected Organisations',
  },
  {
    value: OrganisationType.Destination,
    text: 'People at Destinations',
  },
  {
    value: OrganisationType.PrivateSector,
    text: 'People at Private Sector Organisations',
  },
  {
    value: OrganisationType.RepresentationAgency,
    text: 'People at Representation Agencies',
  },
  {
    value: OrganisationType.TourOperator,
    text: 'People at Tour Operators',
  },
  {
    value: OrganisationType.TravelAgency,
    text: 'People at Travel Agencies',
  },
  {
    value: 'UnassignedOrganisation',
    text: 'People at Unassigned Organisations',
  },
];

export const organisationTypesData = [
  {
    value: OrganisationType.Destination,
    text: 'Destinations',
  },
  {
    value: OrganisationType.PrivateSector,
    text: 'Private Sector',
  },
  {
    value: OrganisationType.RepresentationAgency,
    text: 'Representation Agencies',
  },
  {
    value: OrganisationType.TourOperator,
    text: 'Tour Operators',
  },
  {
    value: OrganisationType.TravelAgency,
    text: 'Travel Agencies',
  },
];

export const NotificationPreferenceTypesData: NotificationPreferenceDataTypes[] = [
  {
    key: 'messages',
    title: 'Inbox Messages',
    subTitle: `These are notifications for messages that you've received directly within Hablo Inbox from your connections.`,
  },
  {
    key: 'connections',
    title: 'Connections & Follows',
    subTitle: `Notifications when someone sends you a connection request or accepts your connection request.`,
  },
  {
    key: 'invitations',
    title: 'Invitations',
    subTitle: `Notifications when someone sends you a invitation for Event, Incentive and Video.`,
  },
  {
    key: 'events',
    title: 'Events',
    subTitle: `Notifications about Events you are attending or have indicated you're interested in.`,
  },
  {
    key: 'incentives',
    title: 'Incentives',
    subTitle: `Notifications about Incentives you are attending or have indicated you're interested in.`,
  },
  {
    key: 'mentionsInPosts',
    title: 'Mentions In Posts',
    subTitle: `Notifications when you get mentioned in a post.`,
  },
  {
    key: 'interaction',
    title: 'Interaction',
    subTitle: `Notifications related to user interactions`,
  },
  {
    key: 'organisationsYouManage',
    title: 'Organisations You Manage',
    subTitle: `Notifications related to your organisation.`,
  },
  {
    key: 'followSuggestions',
    title: 'Follow Suggestions',
    subTitle: `Notifications related to suggested followers.`,
  },
  {
    key: 'posts',
    title: 'Posts',
    subTitle: `Notifications about from organisations you follow.`,
  },
];

export const notificationPreferenceObject = {
  messages: ['push', 'email', 'sms', 'desktop'],
  connections: ['push', 'sms', 'desktop'],
  invitations: ['push', 'email', 'sms', 'desktop'],
  events: ['push', 'email', 'sms', 'desktop'],
  incentives: ['push', 'email', 'sms', 'desktop'],
  webinars: ['push', 'email', 'sms', 'desktop'],
  mentionsInPosts: ['push', 'email', 'sms', 'desktop'],
  interaction: ['push', 'email', 'sms', 'desktop'],
  organisationsYouManage: ['push', 'email', 'sms', 'desktop'],
  followSuggestions: ['push', 'email', 'sms', 'desktop'],
};
