export type Maybe<T> = T | null;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: string;
  String: string;
  Boolean: boolean;
  Int: number;
  Float: number;
  /** Date custom scalar type */
  Date: any;
  /** The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf). */
  JSON: any;
};

export type Query = {
  __typename?: 'Query';
  profile: Profile;
  profiles: ProfilesResult;
  notification: Notification;
  notifications: NotificationsResult;
  organisation: Organisation;
  organisations: OrganisationsResult;
  post: Post;
  posts: PostsResult;
  event: Event;
  calendarEvents: Array<Event>;
  events: EventsResult;
  incentive: Incentive;
  incentives: IncentivesResult;
  incentiveParticipant: IncentiveParticipant;
  incentiveBooking: IncentiveBooking;
  incentiveBookings: IncentiveBookingsResult;
  webinar: Webinar;
  webinars: WebinarsResult;
  webinarParticipant: WebinarParticipant;
  getConferenceAuthToken: ConferenceAuthTokenResponse;
  getChatAuthToken: ChatAuthTokenResponse;
  getFeedsAuthToken: FeedsAuthTokenResponse;
  eventInvitation: EventInvitation;
  signature: CloudinarySignatureResponse;
  analytics: AnalyticsResponse;
};

export type QueryProfileArgs = {
  id?: Maybe<Scalars['String']>;
};

export type QueryProfilesArgs = {
  first?: Maybe<Scalars['Int']>;
  after?: Maybe<Scalars['String']>;
  sortBy?: Maybe<Scalars['String']>;
  sortOrder?: Maybe<PaginationSortOrder>;
  filter?: Maybe<ProfilesFilter>;
};

export type QueryNotificationArgs = {
  id: Scalars['String'];
};

export type QueryNotificationsArgs = {
  first?: Maybe<Scalars['Int']>;
  after?: Maybe<Scalars['String']>;
  sortBy?: Maybe<Scalars['String']>;
  sortOrder?: Maybe<PaginationSortOrder>;
  filter?: Maybe<NotificationsFilter>;
};

export type QueryOrganisationArgs = {
  vanityId?: Maybe<Scalars['String']>;
  id?: Maybe<Scalars['String']>;
};

export type QueryOrganisationsArgs = {
  first?: Maybe<Scalars['Int']>;
  after?: Maybe<Scalars['String']>;
  sortBy?: Maybe<Scalars['String']>;
  sortOrder?: Maybe<PaginationSortOrder>;
  filter?: Maybe<OrganisationsFilter>;
};

export type QueryPostArgs = {
  id: Scalars['String'];
};

export type QueryPostsArgs = {
  first?: Maybe<Scalars['Int']>;
  after?: Maybe<Scalars['String']>;
  sortBy?: Maybe<Scalars['String']>;
  sortOrder?: Maybe<PaginationSortOrder>;
  filter?: Maybe<PostsFilter>;
};

export type QueryEventArgs = {
  id: Scalars['String'];
};

export type QueryEventsArgs = {
  first?: Maybe<Scalars['Int']>;
  after?: Maybe<Scalars['String']>;
  sortBy?: Maybe<Scalars['String']>;
  sortOrder?: Maybe<PaginationSortOrder>;
  filter?: Maybe<EventsFilter>;
};

export type QueryIncentiveArgs = {
  id: Scalars['String'];
};

export type QueryIncentivesArgs = {
  first?: Maybe<Scalars['Int']>;
  after?: Maybe<Scalars['String']>;
  sortBy?: Maybe<Scalars['String']>;
  sortOrder?: Maybe<PaginationSortOrder>;
  filter?: Maybe<IncentivesFilter>;
};

export type QueryIncentiveParticipantArgs = {
  id: Scalars['String'];
};

export type QueryIncentiveBookingArgs = {
  id: Scalars['String'];
};

export type QueryIncentiveBookingsArgs = {
  first?: Maybe<Scalars['Int']>;
  after?: Maybe<Scalars['String']>;
  sortBy?: Maybe<Scalars['String']>;
  sortOrder?: Maybe<PaginationSortOrder>;
  filter?: Maybe<IncentiveBookingsFilter>;
};

export type QueryWebinarArgs = {
  id: Scalars['String'];
};

export type QueryWebinarsArgs = {
  first?: Maybe<Scalars['Int']>;
  after?: Maybe<Scalars['String']>;
  sortBy?: Maybe<Scalars['String']>;
  sortOrder?: Maybe<PaginationSortOrder>;
  filter?: Maybe<WebinarsFilter>;
};

export type QueryWebinarParticipantArgs = {
  id: Scalars['String'];
};

export type QueryEventInvitationArgs = {
  id: Scalars['String'];
};

export type QuerySignatureArgs = {
  public_id?: Maybe<Scalars['String']>;
  eager?: Maybe<Scalars['String']>;
};

export type QueryAnalyticsArgs = {
  startDate: Scalars['Date'];
  endDate: Scalars['Date'];
  organisationId: Scalars['String'];
};

export type ZoomMeetingResponse = {
  __typename?: 'ZoomMeetingResponse';
  joinUrl: Scalars['String'];
  startUrl: Scalars['String'];
  meetingId: Scalars['String'];
};

export type ZoomExpireMessageResponse = {
  __typename?: 'ZoomMeetingResponse';
  success: Scalars['Boolean'];
};

export type NotificationPreferenceTypes = {
  messages: Array<string>;
  connections: Array<string>;
  invitations: Array<string>;
  events: Array<string>;
  incentives: Array<string>;
  webinars: Array<string>;
  mentionsInPosts: Array<string>;
  interaction: Array<string>;
  organisationsYouManage: Array<string>;
  followSuggestions: Array<string>;
};

export type Profile = {
  __typename?: 'Profile';
  id: Scalars['ID'];
  name: Scalars['String'];
  email: Scalars['String'];
  image?: Maybe<Scalars['String']>;
  backgroundImage?: Maybe<Scalars['String']>;
  dateOfBirth?: Maybe<Scalars['Date']>;
  gender?: Maybe<Scalars['String']>;
  bio?: Maybe<Scalars['String']>;
  headline?: Maybe<Scalars['String']>;
  location?: Maybe<Scalars['JSON']>;
  regions?: Maybe<Array<Region>>;
  responsibilities?: Maybe<Array<ProfileResponsibility>>;
  typesOfHoliday?: Maybe<Array<ProfileTypeOfHoliday>>;
  phoneNumber?: Maybe<Scalars['String']>;
  lastOnlineAt: Scalars['Date'];
  timezone?: Maybe<Scalars['String']>;
  onboarding?: Maybe<Scalars['JSON']>;
  isComplete: Scalars['Boolean'];
  onlineStatus: OnlineStatus;
  createdAt: Scalars['Date'];
  updatedAt: Scalars['Date'];
  isEmailVerified?: Maybe<Scalars['Boolean']>;
  receiveNotificationEmails: Scalars['Boolean'];
  receiveNewMessagesEmails: Scalars['Boolean'];
  status?: Maybe<ProfileConnectionStatus>;
  connections: Array<Connection>;
  connectionsCount: Scalars['Int'];
  experiences: Array<Experience>;
  memberships: Array<Membership>;
  following: Array<Follower>;
  streamChannelId?: Maybe<Scalars['String']>;
  primaryMembership?: Maybe<Membership>;
  activeMemberships?: Maybe<Array<Membership>>;
  nonPrimaryMemberships?: Maybe<Array<Membership>>;
  sellHolidays?: Maybe<Scalars['Boolean']>;
  migrationFlags?: Maybe<Scalars['JSON']>;
  showGettingStartedFeedAt?: Maybe<Scalars['Date']>;
  notificationPreference?: NotificationPreferenceTypes;
  noOfMobileDevicesLoggedIn?: Scalars['Int'];
  activeTier?: Maybe<Scalars['String']>;
};

type ProfileConnections = {
  profile: Pick<
    Profile,
    'id' | 'image' | 'lastOnlineAt' | 'memberships' | 'name' | '__typename' | 'headline' | 'onlineStatus'
  >;
};

export type parentOrganisation = {
  id: string;
  image: string;
  name: string;
  vanityId: string;
};

export type PartnerOrganisation = {
  id: string;
  parentOrgId: string;
  childOrgId: string;
  status: string;
  postsLimit: number;
  postsUsedThisMonth: number;
  connectionApprovedDate?: string;
  disconnectionDate?: string;
  createdAt: Date;
  updatedAt: Date;
  parentOrganisation: {
    id: string;
    name: string;
    vanityId: string;
    createdAt: Date;
    updatedAt: Date;
    image: string;
    activeTier?: string;
  };
  childOrganisation: {
    id: string;
    name: string;
    vanityId: string;
    createdAt: Date;
    updatedAt: Date;
    image: string;
  };
};

export type ProfileWithPaginatedConnections = {
  __typename?: 'Profile';
  id: Scalars['ID'];
  name: Scalars['String'];
  email: Scalars['String'];
  image?: Maybe<Scalars['String']>;
  backgroundImage?: Maybe<Scalars['String']>;
  dateOfBirth?: Maybe<Scalars['Date']>;
  gender?: Maybe<Scalars['String']>;
  bio?: Maybe<Scalars['String']>;
  headline?: Maybe<Scalars['String']>;
  location?: Maybe<Scalars['JSON']>;
  regions?: Maybe<Array<Region>>;
  responsibilities?: Maybe<Array<ProfileResponsibility>>;
  typesOfHoliday?: Maybe<Array<ProfileTypeOfHoliday>>;
  phoneNumber?: Maybe<Scalars['String']>;
  lastOnlineAt: Scalars['Date'];
  timezone?: Maybe<Scalars['String']>;
  onboarding?: Maybe<Scalars['JSON']>;
  isComplete: Scalars['Boolean'];
  onlineStatus: OnlineStatus;
  createdAt: Scalars['Date'];
  updatedAt: Scalars['Date'];
  isEmailVerified?: Maybe<Scalars['Boolean']>;
  receiveNotificationEmails: Scalars['Boolean'];
  receiveNewMessagesEmails: Scalars['Boolean'];
  status?: Maybe<ProfileConnectionStatus>;
  connections: Array<Connection>;
  connectionsCount: Scalars['Int'];
  experiences: Array<Experience>;
  memberships: Array<Membership>;
  following: Array<Follower>;
  streamChannelId?: Maybe<Scalars['String']>;
  primaryMembership?: Maybe<Membership>;
  connectionsViaPagination?: Maybe<{
    records: Array<ProfileConnections>;
    totalCount: Scalars['Int'];
  }>;
  activeMemberships?: Maybe<Array<Membership>>;
  nonPrimaryMemberships?: Maybe<Array<Membership>>;
  sellHolidays?: Maybe<Scalars['Boolean']>;
  activeTier?: Maybe<Scalars['String']>;
};

export type DestinationPageCategoryContent = {
  id: Scalars['String'];
  name: Scalars['String'];
  order: Scalars['Float'];
  content: Array<Organisation>;
};

export type DestinationPageResponse = {
  __typename?: 'DestinationPageResponse';
  id: Scalars['ID'];
  hostOrganisationIds: Array<string>;
  path: Array<string>;
  categoryIds: Array<string>;
  image: String;
  name: String;
  vanityId: String;
  status: String;
  children?: Maybe<Array<DestinationPageTree>>;
  hostOrganisations?: Maybe<Array<Organisation>>;
  content?: Maybe<Array<DestinationPageCategoryContent>>;
};

export type DestinationPage = {
  __typename?: 'DestinationPage';
  id: Scalars['ID'];
  hostOrganisationIds: Array<string>;
  path: Array<string>;
  categories: Array<string>;
  image: String;
  name: String;
  vanityId: String;
  status: String;
  hostOrganisations?: Maybe<Array<Organisation>>;
  content?: Maybe<Array<DestinationPageCategoryContent>>;
};

export type DestinationPageTree = {
  __typename?: 'DestinationPageTree';
  id: Scalars['ID'];
  hostOrganisationIds: Array<string>;
  path: Array<string>;
  categories: Array<string>;
  image: String;
  name: String;
  status: String;
  vanityId: String;
  children: Array<DestinationPageTree>;
};

export type ProfileMembershipsArgs = {
  first?: Maybe<Scalars['Int']>;
  after?: Maybe<Scalars['String']>;
  sortBy?: Maybe<Scalars['String']>;
  sortOrder?: Maybe<PaginationSortOrder>;
  filter?: Maybe<MembershipsFilter>;
};

export type ProfileFollowingArgs = {
  first?: Maybe<Scalars['Int']>;
  after?: Maybe<Scalars['String']>;
  sortBy?: Maybe<Scalars['String']>;
  sortOrder?: Maybe<PaginationSortOrder>;
  filter?: Maybe<FollowersFilter>;
};

export enum Region {
  UnitedStatesOfAmerica = 'UnitedStatesOfAmerica',
  Africa = 'Africa',
  Asia = 'Asia',
  Canada = 'Canada',
  TheCaribbean = 'TheCaribbean',
  CentralAmerica = 'CentralAmerica',
  Europe = 'Europe',
  Mexico = 'Mexico',
  MiddleEast = 'MiddleEast',
  Oceania = 'Oceania',
  SouthAmerica = 'SouthAmerica',
}

export enum ProfileResponsibility {
  Events = 'Events',
  Marketing = 'Marketing',
  Partnerships = 'Partnerships',
  ProductAndContracting = 'ProductAndContracting',
  PublicRelations = 'PublicRelations',
  Sales = 'Sales',
  SalesAgent = 'SalesAgent',
  SalesManager = 'SalesManager',
  SeniorManagement = 'SeniorManagement',
  SocialAndDigital = 'SocialAndDigital',
  Training = 'Training',
  Other = 'Other',
}

export enum ProfileTypeOfHoliday {
  Business = 'Business',
  CityBreaks = 'CityBreaks',
  Cruises = 'Cruises',
  Educational = 'Educational',
  EscortedTours = 'EscortedTours',
  Expedition = 'Expedition',
  FlyDrive = 'FlyDrive',
  Groups = 'Groups',
  Leisure = 'Leisure',
  Luxury = 'Luxury',
  Niche = 'Niche',
  PackageHolidays = 'PackageHolidays',
  Rail = 'Rail',
  RoundTheWorld = 'RoundTheWorld',
  Solo = 'Solo',
}

export enum OnlineStatus {
  Online = 'Online',
  Offline = 'Offline',
  Away = 'Away',
}

export enum ProfileConnectionStatus {
  Connection = 'Connection',
  InvitationSent = 'InvitationSent',
  InvitationAccepted = 'InvitationAccepted',
  InvitationReceived = 'InvitationReceived',
  InvitationSentRejected = 'InvitationSentRejected',
  InvitationReceivedRejected = 'InvitationReceivedRejected',
  None = 'None',
}

export type Connection = {
  __typename?: 'Connection';
  id: Scalars['ID'];
  profile: Profile;
  streamChannelId: Scalars['String'];
};

export type Experience = {
  __typename?: 'Experience';
  id: Scalars['ID'];
  position: Scalars['String'];
  description?: Maybe<Scalars['String']>;
  location?: Maybe<Scalars['JSON']>;
  startDate?: Maybe<Scalars['Date']>;
  endDate?: Maybe<Scalars['Date']>;
  organisation?: Maybe<Organisation>;
  organisationName?: Maybe<Scalars['String']>;
  profile: Profile;
  createdAt: Scalars['Date'];
  updatedAt: Scalars['Date'];
};

export type Organisation = {
  __typename?: 'Organisation';
  id: Scalars['ID'];
  name: Scalars['String'];
  image: Scalars['String'];
  backgroundImage?: Maybe<Scalars['String']>;
  location?: Maybe<Scalars['JSON']>;
  type?: Maybe<OrganisationType>;
  privacy?: Maybe<Scalars['String']>;
  peoplePrivacy?: Maybe<Array<Scalars['String']>>;
  followingPrivacy: Maybe<Array<Scalars['String']>>;
  additionalPrivacy: Maybe<Scalars['String']>;
  website?: Maybe<Scalars['String']>;
  resourceUrl?: Maybe<Scalars['String']>;
  resources?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  size?: Maybe<OrganisationSize>;
  vanityId?: Maybe<Scalars['String']>;
  createdAt: Scalars['Date'];
  updatedAt: Scalars['Date'];
  isPublic?: Maybe<Scalars['Boolean']>;
  privacySettings?: Maybe<Scalars['JSON']>;
  preApprovedDomains?: Maybe<Array<Scalars['String']>>;
  pages: Array<OrganisationPage>;
  memberships: MembershipsResult;
  incentives: IncentivesResult;
  followers: FollowersResult;
  partnerships: Array<Partnership>;
  receivedPartnershipRequests: Array<PartnershipRequest>;
  sentPartnershipRequests: Array<PartnershipRequest>;
  followerStatus?: Maybe<FollowerStatus>;
  isMember?: Maybe<Scalars['Boolean']>;
  permissions?: Maybe<Array<MembershipPermission>>;
  isOwner?: Maybe<Scalars['Boolean']>;
  activeMembership?: Maybe<Membership>;
  isAdmin?: Maybe<Scalars['Boolean']>;
  isManager?: Maybe<Scalars['Boolean']>;
  isEditor?: Maybe<Scalars['Boolean']>;
  followersActiveCount?: Maybe<Scalars['Int']>;
  followersPendingCount?: Maybe<Scalars['Int']>;
  followersRejectedCount?: Maybe<Scalars['Int']>;
  webinarsCount?: Maybe<Scalars['Int']>;
  eventsCount?: Maybe<Scalars['Int']>;
  status?: Maybe<OrganisationStatus>;
  stripeConnectAccount?: Maybe<Scalars['String']>;
  isConnectOnboarded?: Maybe<Scalars['Boolean']>;
  parentOrganisations?: Array<{ id: Scalars['String'] }>;
  parentOrganisationDetails?: Array<parentOrganisation>;
  connectedOrganisations?: Array<Scalars['ID']>;
  isPaid?: Maybe<Scalars['Boolean']>;
  hasClubHabloSubscription?: Maybe<Scalars['Boolean']>;
  activeTier?: Maybe<Scalars['String']>;
  isDMOSubscription?: Maybe<Scalars['Boolean']>;
  isPartner?: Maybe<Scalars['Boolean']>;
  childOrganisationId?: Maybe<Scalars['String']>;
  postsLimit?: Maybe<Scalars['Int']>;
  postsUsedThisMonth?: Maybe<Scalars['Int']>;
  DMOMaxPartners?: Maybe<Scalars['Int']>;
  referralCount?: Maybe<Scalars['Int']>;
};

export type OrganisationMembershipsArgs = {
  first?: Maybe<Scalars['Int']>;
  after?: Maybe<Scalars['String']>;
  sortBy?: Maybe<Scalars['String']>;
  sortOrder?: Maybe<PaginationSortOrder>;
  filter?: Maybe<MembershipsFilter>;
};

export type OrganisationIncentivesArgs = {
  first?: Maybe<Scalars['Int']>;
  after?: Maybe<Scalars['String']>;
  sortBy?: Maybe<Scalars['String']>;
  sortOrder?: Maybe<PaginationSortOrder>;
  filter?: Maybe<IncentivesFilter>;
};

export type OrganisationFollowersArgs = {
  first?: Maybe<Scalars['Int']>;
  after?: Maybe<Scalars['String']>;
  sortBy?: Maybe<Scalars['String']>;
  sortOrder?: Maybe<PaginationSortOrder>;
  filter?: Maybe<FollowersFilter>;
};

export enum OrganisationType {
  Destination = 'Destination',
  PrivateSector = 'PrivateSector',
  RepresentationAgency = 'RepresentationAgency',
  TourOperator = 'TourOperator',
  TravelAgency = 'TravelAgency',
  Community = 'Community',
  Association = 'Association',
  Consortia = 'Consortia',
}

export enum OrganisationStatus {
  Active = 'Active',
  Pending = 'Pending',
  Removed = 'Removed',
  Suspended = 'Suspended',
}

export enum OrganisationSize {
  OrgSize1 = 'OrgSize1',
  OrgSize2_10 = 'OrgSize2_10',
  OrgSize11_50 = 'OrgSize11_50',
  OrgSize51_200 = 'OrgSize51_200',
  OrgSize201_500 = 'OrgSize201_500',
  OrgSize501_1000 = 'OrgSize501_1000',
  OrgSize1001_5000 = 'OrgSize1001_5000',
  OrgSize5001 = 'OrgSize5001',
}

export enum OrganisationPage {
  Home = 'Home',
  About = 'About',
  Webinars = 'Webinars',
  Events = 'Events',
  Resources = 'Resources',
  People = 'People',
  Analytics = 'Analytics',
}

export enum PaginationSortOrder {
  Ascending = 'Ascending',
  Descending = 'Descending',
}

export type MembershipsFilter = {
  organisationId?: Maybe<Scalars['String']>;
  status?: Maybe<Array<MembershipStatus>>;
  permission?: Maybe<Array<MembershipPermission>>;
  isPrimary?: Maybe<Scalars['Boolean']>;
  includeActiveProfile?: Maybe<Scalars['Boolean']>;
};

export enum MembershipStatus {
  Active = 'Active',
  Pending = 'Pending',
  Rejected = 'Rejected',
  Inactive = 'Inactive',
}

export enum MembershipPermission {
  Owner = 'Owner',
  OwnerPending = 'OwnerPending',
  Admin = 'Admin',
  HiddenAdmin = 'HiddenAdmin',
  Manager = 'Manager',
  Editor = 'Editor',
  Staff = 'Staff',
  Linked = 'Linked',
  InviteMembersToEvents = 'InviteMembersToEvents',
  Member = 'Member',
}

export type MembershipsResult = {
  __typename?: 'MembershipsResult';
  records: Array<Membership>;
  totalCount: Scalars['Int'];
};

export type Membership = {
  __typename?: 'Membership';
  id: Scalars['ID'];
  organisationName?: Maybe<Scalars['String']>;
  isPrimary?: Maybe<Scalars['Boolean']>;
  position?: Maybe<Scalars['String']>;
  permissions?: Maybe<Array<MembershipPermission>>;
  status?: Maybe<MembershipStatus>;
  actions?: Maybe<Array<MembershipAction>>;
  createdAt?: Maybe<Scalars['Date']>;
  updatedAt?: Maybe<Scalars['Date']>;
  profile: Profile;
  organisation?: Maybe<Organisation>;
  parentOrganisation?: PartnerOrganisation;
  partnerOrganisation: PartnerOrganisation;
};

export type MembershipAction = {
  __typename?: 'MembershipAction';
  profileId: Scalars['String'];
  actionType: MembershipActionType;
  actionDate: Scalars['Date'];
};

export enum MembershipActionType {
  Accept = 'Accept',
  Reject = 'Reject',
}

export type IncentivesFilter = {
  incentiveParticipantStatus?: Maybe<Array<IncentiveParticipantStatus>>;
  organisationId?: Maybe<Scalars['String']>;
  type?: Maybe<Array<IncentiveType>>;
  bookingType?: Maybe<Array<IncentiveBookingType>>;
  isPublic?: Maybe<Scalars['Boolean']>;
  searchText?: Maybe<Scalars['String']>;
  isEnded?: Maybe<Scalars['Boolean']>;
};

export enum IncentiveParticipantStatus {
  Registered = 'Registered',
  InvitedByHost = 'InvitedByHost',
  InvitedByParticipant = 'InvitedByParticipant',
  InvitedRegistered = 'InvitedRegistered',
  Blocked = 'Blocked',
}

export enum IncentiveType {
  CashbackOrVoucher = 'CashbackOrVoucher',
  Prize = 'Prize',
  FamTrip = 'FAMTrip',
}

export enum IncentiveBookingType {
  Flight = 'Flight',
  Hotel = 'Hotel',
  Cruise = 'Cruise',
  Other = 'Other',
}

export type IncentivesResult = {
  __typename?: 'IncentivesResult';
  records: Array<Incentive>;
  totalCount: Scalars['Int'];
};

export type Incentive = {
  __typename?: 'Incentive';
  id: Scalars['ID'];
  name: Scalars['String'];
  type: IncentiveType;
  image?: Maybe<Scalars['String']>;
  description: Scalars['String'];
  startDate: Scalars['Date'];
  endDate: Scalars['Date'];
  regions: Array<Region>;
  bookingTypes?: Maybe<Array<IncentiveBookingType>>;
  bookingFields?: Maybe<Array<IncentiveBookingField>>;
  terms?: Maybe<Scalars['String']>;
  isPublic: Scalars['Boolean'];
  isParticipantListVisible: Scalars['Boolean'];
  isEnded: Scalars['Boolean'];
  createdAt: Scalars['Date'];
  updatedAt: Scalars['Date'];
  organisation: Organisation;
  participant?: Maybe<IncentiveParticipant>;
  participants: Array<IncentiveParticipant>;
  incentiveBookingsCount: Scalars['Int'];
};

export type IncentiveParticipantsArgs = {
  filter?: Maybe<IncentiveParticipantsFilter>;
};

export type IncentiveBookingField = {
  __typename?: 'IncentiveBookingField';
  name: Scalars['String'];
  type: IncentiveBookingFieldType;
  isOptional: Scalars['Boolean'];
};

export enum IncentiveBookingFieldType {
  String = 'String',
}

export type IncentiveParticipant = {
  __typename?: 'IncentiveParticipant';
  id: Scalars['ID'];
  status?: Maybe<IncentiveParticipantStatus>;
  createdAt: Scalars['Date'];
  updatedAt: Scalars['Date'];
  incentive: Incentive;
  organisation?: Maybe<Organisation>;
  profile: Profile;
  inviterProfile?: Maybe<Profile>;
};

export type IncentiveParticipantsFilter = {
  profileId?: Maybe<Scalars['ID']>;
  status?: Maybe<Array<IncentiveParticipantStatus>>;
};

export type FollowersFilter = {
  organisationId?: Maybe<Scalars['String']>;
  status?: Maybe<Array<FollowerStatus>>;
  includeActiveProfile?: Maybe<Scalars['Boolean']>;
};

export enum FollowerStatus {
  Active = 'Active',
  Pending = 'Pending',
  Rejected = 'Rejected',
  InActive = 'Inactive',
  // Blocked = 'Blocked',
}

export type FollowersResult = {
  __typename?: 'FollowersResult';
  records: Array<Follower>;
  totalCount: Scalars['Int'];
};

export type Follower = {
  __typename?: 'Follower';
  id: Scalars['ID'];
  status?: Maybe<FollowerStatus>;
  actions?: Maybe<Array<FollowerAction>>;
  createdAt: Scalars['Date'];
  updatedAt: Scalars['Date'];
  profile: Profile;
  organisation?: Maybe<Organisation>;
};

export type FollowerAction = {
  __typename?: 'FollowerAction';
  profileId: Scalars['String'];
  actionType: FollowerActionType;
  actionDate: Scalars['Date'];
};

export enum FollowerActionType {
  Accept = 'Accept',
  Reject = 'Reject',
  Inactivate = 'Inactivate',
  // Block = 'Block',
}

export type Partnership = {
  __typename?: 'Partnership';
  id: Scalars['ID'];
  organisation: Organisation;
  partnershipRequestData?: Scalars['JSON'];
  createdAt: Scalars['Date'];
  updatedAt: Scalars['Date'];
};

export type PartnershipRequest = {
  __typename?: 'PartnershipRequest';
  id: Scalars['ID'];
  status: PartnershipRequestStatus;
  createdAt: Scalars['Date'];
  updatedAt: Scalars['Date'];
  subscriptionData?: Maybe<Scalars['JSON']>;
  senderOrganisation?: Maybe<Organisation>;
  receiverOrganisation?: Maybe<Organisation>;
};

export enum PartnershipRequestStatus {
  Pending = 'Pending',
  Approved = 'Approved',
  Declined = 'Declined',
  Disconnected = 'Disconnected',
  Rejected = 'Rejected',
}

export type ProfilesFilter = {
  organisationId?: Maybe<Scalars['String']>;
  followerStatus?: Maybe<Array<FollowerStatus>>;
  membershipStatus?: Maybe<Array<MembershipStatus>>;
  membershipPermissions?: Maybe<Array<MembershipPermission>>;
  status?: Maybe<Array<ProfileConnectionStatus>>;
  searchText?: Maybe<Scalars['String']>;
  includeOwnProfile?: Maybe<Scalars['Boolean']>;
  searchLocationText?: Maybe<Scalars['String']>;
  responsibilities?: Maybe<Array<ProfileResponsibility>>;
  typesOfHoliday?: Maybe<Array<ProfileResponsibility>>;
  connectedOrgProfiles?: Maybe<Scalars['Boolean']>;
  isFuzzySearch?: Maybe<Scalars['Boolean']>;
};

export type ProfilesResult = {
  __typename?: 'ProfilesResult';
  records: Array<Profile>;
  totalCount: Scalars['Int'];
};

export type Notification = {
  __typename?: 'Notification';
  id: Scalars['ID'];
  type: NotificationType;
  image: Scalars['String'];
  isRead: Scalars['Boolean'];
  data?: Maybe<Scalars['JSON']>;
  createdAt: Scalars['Date'];
  profile: Profile;
  membership?: Maybe<Membership>;
  follower?: Maybe<Follower>;
  eventInvitation?: Maybe<EventInvitation>;
  organisation?: Maybe<Organisation>;
  partnershipRequest?: Maybe<PartnershipRequest>;
  incentiveParticipant?: Maybe<IncentiveParticipant>;
  webinarParticipant?: Maybe<WebinarParticipant>;
};

export enum NotificationType {
  InvitationReceived = 'InvitationReceived',
  InvitationAccepted = 'InvitationAccepted',
  PartnershipRequestReceived = 'PartnershipRequestReceived',
  PartnershipRequestApproved = 'PartnershipRequestApproved',
  PartnershipRequestApprovedByOrganisation = 'PartnershipRequestApprovedByOrganisation',
  MembershipAccepted = 'MembershipAccepted',
  OrganisationOwnershipRequested = 'OrganisationOwnershipRequested',
  OrganisationOwnershipAccepted = 'OrganisationOwnershipAccepted',
  MembershipRequested = 'MembershipRequested',
  FollowerAccepted = 'FollowerAccepted',
  MembershipPermissionsUpdated = 'MembershipPermissionsUpdated',
  EventInvitationByGuest = 'EventInvitationByGuest',
  EventInvitationByHosts = 'EventInvitationByHosts',
  EventInvitationApproved = 'EventInvitationApproved',
  NewEventUpdate = 'NewEventUpdate',
  EventLocationChanged = 'EventLocationChanged',
  EventDateTimeChanged = 'EventDateTimeChanged',
  IncentiveDateChanged = 'IncentiveDateChanged',
  IncentiveInvitationByParticipant = 'IncentiveInvitationByParticipant',
  IncentiveInvitationByHosts = 'IncentiveInvitationByHosts',
  IncentiveRegistrationRequested = 'IncentiveRegistrationRequested',
  IncentiveRegistrationApproved = 'IncentiveRegistrationApproved',
  NewIncentiveUpdate = 'NewIncentiveUpdate',
  WebinarDateChanged = 'WebinarDateChanged',
  WebinarInvitationByParticipant = 'WebinarInvitationByParticipant',
  WebinarInvitationByHosts = 'WebinarInvitationByHosts',
  WebinarParticipantAddedAsHostAdmin = 'WebinarParticipantAddedAsHostAdmin',
  WebinarParticipantAddedAsHost = 'WebinarParticipantAddedAsHost',
  WebinarParticipantAddedAsSpeaker = 'WebinarParticipantAddedAsSpeaker',
  WebinarRegistrationRequested = 'WebinarRegistrationRequested',
  WebinarRegistrationApproved = 'WebinarRegistrationApproved',
  NewWebinarUpdate = 'NewWebinarUpdate',
  SuggestFollow = 'SuggestFollow',
  PostComment = 'PostComment',
  CommentReact = 'CommentReact',
  PostShared = 'PostShared',
  CommentMention = 'CommentMention',
  PostMention = 'PostMention',
  PostReact = 'PostReact',
  NewFollower = 'NewFollower',
  OrgPostMention = 'OrgPostMention',
  OrgCommentMention = 'OrgCommentMention',
  RecurringPaymentReminder = 'RecurringPaymentReminder',
  MembershipAutoApprove = 'MembershipAutoApprove',
  ParentMembershipRequested = 'ParentMembershipRequested',
  ParentMembershipAccepted = 'ParentMembershipAccepted',
  ParentMembershipDeclined = 'ParentMembershipDeclined',
  OrgPostReminder7Days = 'OrgPostReminder7Days',
  OrgPostReminder14Days = 'OrgPostReminder14Days',
  InactiveUserReminder = 'InactiveUserReminder',
  InboxMessage = 'InboxMessage',
  DailyLoginStreak = 'DailyLoginStreak',
  NewTier = 'NewTier',
  WeeklySummary = 'WeeklySummary',
  NewAchievement = 'NewAchievement',
  NewOrganisationAchievement = 'NewOrganisationAchievement',
  HighFiveAchievement = 'HighFiveAchievement',
  MonthlyAchievementSummary = 'MonthlyAchievementSummary',
  OrgPartnerRequestReceived = 'OrgPartnerRequestReceived',
  OrgPartnerRequestRejected = 'OrgPartnerRequestRejected',
  OrgPartnerAcceptedSender = 'OrgPartnerAcceptedSender',
  OrgPartnerAcceptedReceiver = 'OrgPartnerAcceptedReceiver',
}

export type EventInvitation = {
  __typename?: 'EventInvitation';
  id: Scalars['ID'];
  status?: Maybe<EventInvitationStatus>;
  createdAt: Scalars['Date'];
  updatedAt: Scalars['Date'];
  event: Event;
  organisation?: Maybe<Organisation>;
  profile: Profile;
  inviterProfile?: Maybe<Profile>;
};

export enum EventInvitationStatus {
  Attending = 'Attending',
  Interested = 'Interested',
  Declined = 'Declined',
  InvitedByGuest = 'InvitedByGuest',
  InvitedByHost = 'InvitedByHost',
  InvitedAttending = 'InvitedAttending',
  InvitedInterested = 'InvitedInterested',
  InvitedDeclined = 'InvitedDeclined',
  Blocked = 'Blocked',
}

export type Event = {
  __typename?: 'Event';
  id: Scalars['ID'];
  name: Scalars['String'];
  displayName?: Maybe<Scalars['String']>;
  image?: Maybe<Scalars['String']>;
  location?: Maybe<Scalars['JSON']>;
  type: EventType;
  isOnline: Scalars['Boolean'];
  url?: Maybe<Scalars['String']>;
  description: Scalars['String'];
  isAllDay: Scalars['Boolean'];
  startDate: Scalars['Date'];
  endDate: Scalars['Date'];
  isPublic?: Maybe<Scalars['Boolean']>;
  isHostsManagedRSVP?: Maybe<Scalars['Boolean']>;
  isGuestsCanInvite?: Maybe<Scalars['Boolean']>;
  isGuestListVisible?: Maybe<Scalars['Boolean']>;
  isEnded: Scalars['Boolean'];
  createdAt: Scalars['Date'];
  updatedAt: Scalars['Date'];
  organisation: Organisation;
  invitation?: Maybe<EventInvitation>;
  invitations: Array<EventInvitation>;
  incentive?: Maybe<Incentive>;
  webinar?: Maybe<Webinar>;
};

export type EventInvitationsArgs = {
  filter?: Maybe<EventInvitationsFilter>;
};

export enum EventType {
  Event = 'Event',
  Incentive = 'Incentive',
  IncentiveStart = 'IncentiveStart',
  IncentiveEnd = 'IncentiveEnd',
  Webinar = 'Webinar',
}

export type EventInvitationsFilter = {
  profileId?: Maybe<Scalars['ID']>;
  status?: Maybe<Array<EventInvitationStatus>>;
};

export type Webinar = {
  __typename?: 'Webinar';
  id: Scalars['ID'];
  name: Scalars['String'];
  type: WebinarType;
  image?: Maybe<Scalars['String']>;
  description: Scalars['String'];
  startDate: Scalars['Date'];
  endDate: Scalars['Date'];
  keywords: Array<Scalars['String']>;
  isPublic: Scalars['Boolean'];
  isParticipantsCanInvite: Scalars['Boolean'];
  isParticipantListVisible: Scalars['Boolean'];
  isEnded: Scalars['Boolean'];
  createdAt: Scalars['Date'];
  updatedAt: Scalars['Date'];
  liveStreamId?: Maybe<Scalars['String']>;
  liveStreamPlaybackId?: Maybe<Scalars['String']>;
  liveStreamStartedAt?: Maybe<Scalars['Date']>;
  liveStreamStoppedAt?: Maybe<Scalars['Date']>;
  liveStreamRecordingId?: Maybe<Scalars['String']>;
  chatChannelId?: Maybe<Scalars['String']>;
  organisation: Organisation;
  participant?: Maybe<WebinarParticipant>;
  participants: Array<WebinarParticipant>;
  isInternal?: Maybe<Scalars['Boolean']>;
  isViewed?: Maybe<Scalars['Boolean']>;
  viewedDate?: Maybe<Scalars['String']>;
  assetId?: Maybe<Scalars['String']>;
};

export type WebinarParticipantsArgs = {
  filter?: Maybe<WebinarParticipantsFilter>;
};

export enum WebinarType {
  PreRecorded = 'PreRecorded',
  LiveStream = 'LiveStream',
}

export type WebinarParticipant = {
  __typename?: 'WebinarParticipant';
  id: Scalars['ID'];
  status?: Maybe<WebinarParticipantStatus>;
  createdAt: Scalars['Date'];
  updatedAt: Scalars['Date'];
  webinar: Webinar;
  organisation?: Maybe<Organisation>;
  profile: Profile;
  inviterProfile?: Maybe<Profile>;
};

export enum WebinarParticipantStatus {
  Host = 'Host',
  HostAdmin = 'HostAdmin',
  Speaker = 'Speaker',
  HiddenHost = 'HiddenHost',
  Registered = 'Registered',
  InvitedByHost = 'InvitedByHost',
  InvitedByParticipant = 'InvitedByParticipant',
  InvitedRegistered = 'InvitedRegistered',
  Blocked = 'Blocked',
}

export type WebinarParticipantsFilter = {
  profileId?: Maybe<Scalars['ID']>;
  status?: Maybe<Array<WebinarParticipantStatus>>;
};

export type NotificationsFilter = {
  isRead?: Maybe<Scalars['Boolean']>;
};

export type NotificationsResult = {
  __typename?: 'NotificationsResult';
  records: Array<Notification>;
  totalCount: Scalars['Int'];
  totalUnreadCount: Scalars['Int'];
};

export type UnreadNotifications = {
  unSeenNotifications: Scalars['Int'];
};

export type OrganisationsFilter = {
  id?: Maybe<Scalars['String']>;
  type?: Maybe<Array<OrganisationType>>;
  searchText?: Maybe<Scalars['String']>;
  vanityId?: Maybe<Array<Scalars['String']>>;
  isFuzzySearch?: Maybe<Scalars['Boolean']>;
};

export type DestinationPagesFilter = {
  searchText?: Maybe<Scalars['String']>;
};

export type OrganisationsResult = {
  __typename?: 'OrganisationsResult';
  records: Array<Organisation>;
  totalCount: Scalars['Int'];
};

export type MultipleImages = {
  image: Maybe<Scalars['String']>;
  imageHeight: Scalars['Int'];
  imageWidth: Scalars['Int'];
  imageFormat: Scalars['String'];
  url: Scalars['String'];
  status: Scalars['String'];
};

export enum PostStatus {
  Scheduled = 'Scheduled',
  Live = 'Live',
}

export type Post = {
  __typename?: 'Post';
  id: Scalars['ID'];
  type: PostType;
  text: Scalars['String'];
  image?: Maybe<Scalars['String']>;
  mediaHeight: Scalars['Int'];
  mediaWidth: Scalars['Int'];
  imageCategory?: Maybe<Scalars['String']>;
  multipleImages?: MultipleImages[];
  profile: Profile;
  organisation: Organisation;
  seenBy?: Maybe<Array<Scalars['String']>>;
  totalViews: Scalars['Int'];
  createdAt: Scalars['Date'];
  views: Scalars['Int'];
  event?: Maybe<Event>;
  incentive?: Maybe<Incentive>;
  webinar?: Maybe<Webinar>;
  isSeen?: Maybe<Scalars['Boolean']>;
  status?: Maybe<PostStatus>;
  scheduledAt: Scalars['Date'];
  postAudience: Scalars['JSON'];
};

export enum PostType {
  Normal = 'Normal',
  Image = 'Image',
  Webinar = 'Webinar',
  Event = 'Event',
  Incentive = 'Incentive',
  Video = 'Video',
  Document = 'Document',
}

export type PostsFilter = {
  type?: Maybe<Array<PostType>>;
  organisationId?: Maybe<Scalars['String']>;
  eventId?: Maybe<Scalars['String']>;
  incentiveId?: Maybe<Scalars['String']>;
  webinarId?: Maybe<Scalars['String']>;
  updates?: Maybe<Scalars['Boolean']>;
  searchText?: Maybe<Scalars['String']>;
};

export type PostsResult = {
  __typename?: 'PostsResult';
  records: Array<Post>;
  totalCount: Scalars['Int'];
};

export type EventsFilter = {
  eventInvitationStatus?: Maybe<Array<EventInvitationStatus>>;
  organisationId?: Maybe<Scalars['String']>;
  type?: Maybe<Array<EventType>>;
  isOnline?: Maybe<Scalars['Boolean']>;
  isPublic?: Maybe<Scalars['Boolean']>;
  searchText?: Maybe<Scalars['String']>;
  isEnded?: Maybe<Scalars['Boolean']>;
};

export type EventsResult = {
  __typename?: 'EventsResult';
  records: Array<Event>;
  totalCount: Scalars['Int'];
};

export type IncentiveBooking = {
  __typename?: 'IncentiveBooking';
  id: Scalars['ID'];
  dataArray?: Maybe<Array<IncentiveBookingDataItem>>;
  createdAt: Scalars['Date'];
  updatedAt: Scalars['Date'];
  incentive: Incentive;
  profile: Profile;
};

export type IncentiveBookingDataItem = {
  __typename?: 'IncentiveBookingDataItem';
  name: Scalars['String'];
  value: Scalars['String'];
  type: IncentiveBookingFieldType;
};

export type IncentiveBookingsFilter = {
  incentiveId?: Maybe<Scalars['ID']>;
  profileId?: Maybe<Scalars['ID']>;
};

export type IncentiveBookingsResult = {
  __typename?: 'IncentiveBookingsResult';
  records: Array<IncentiveBooking>;
  totalCount: Scalars['Int'];
};

export type WebinarsFilter = {
  webinarParticipantStatus?: Maybe<Array<WebinarParticipantStatus>>;
  organisationId?: Maybe<Scalars['String']>;
  type?: Maybe<WebinarType>;
  isPublic?: Maybe<Scalars['Boolean']>;
  searchText?: Maybe<Scalars['String']>;
  isEnded?: Maybe<Scalars['Boolean']>;
};

export type WebinarsResult = {
  __typename?: 'WebinarsResult';
  records: Array<Webinar>;
  totalCount: Scalars['Int'];
};

export type WebinarsAndKeywordsResult = {
  __typename?: 'WebinarsAndKeywordsResponse';
  webinars: Array<Webinar>;
  keywords: Array<string>;
};

export type ConferenceAuthTokenResponse = {
  __typename?: 'ConferenceAuthTokenResponse';
  authToken: Scalars['String'];
};

export type ChatAuthTokenResponse = {
  __typename?: 'ChatAuthTokenResponse';
  authToken: Scalars['String'];
};

export type FeedsAuthTokenResponse = {
  __typename?: 'FeedsAuthTokenResponse';
  authToken: Scalars['String'];
};
export type FeedsOrgTokenResponse = {
  __typename?: 'FeedsOrgTokenResponse';
  orgToken: Scalars['String'];
};

export type CloudinarySignatureResponse = {
  __typename?: 'CloudinarySignatureResponse';
  signature: Scalars['String'];
  timestamp: Scalars['Float'];
};

export type AnalyticsResponse = {
  __typename?: 'AnalyticsResponse';
  OrganisationFollower: Array<AnalyticsResponseValue>;
  OrganisationPageView: Array<AnalyticsResponseValue>;
  PostSeen: Array<AnalyticsResponseValue>;
  EventRSVP: Array<AnalyticsResponseValue>;
  WebinarVideoView: Array<AnalyticsResponseValue>;
  PostInteractions: Array<AnalyticsResponseValue>;
  PostImpressions: Array<AnalyticsResponseValue>;
};

export type AnalyticsResponseValue = {
  __typename?: 'AnalyticsResponseValue';
  date: Scalars['String'];
  value: Scalars['Int'];
};

export type Mutation = {
  __typename?: 'Mutation';
  updateProfile: Profile;
  removeProfile: Scalars['Boolean'];
  setPrimaryOrganisation: Scalars['Boolean'];
  sendEmailVerification: Scalars['Boolean'];
  changeEmail: Scalars['Boolean'];
  removeConnection: Scalars['Boolean'];
  connect: Scalars['Boolean'];
  acceptInvitation: Scalars['Boolean'];
  rejectInvitation: Scalars['Boolean'];
  setNotificationStatus: Array<Notification>;
  updateMembershipStatus: Scalars['Boolean'];
  removeMembership: Scalars['Boolean'];
  addMembershipPermissions: Scalars['Boolean'];
  updateMembershipPermissions: Scalars['Boolean'];
  createOrganisation: Organisation;
  updateOrganisation: Organisation;
  changeOrganisationOwnership: Scalars['Boolean'];
  acceptOrganisationOwnership: Scalars['Boolean'];
  rejectOrganisationOwnership: Scalars['Boolean'];
  follow: Scalars['Boolean'];
  unfollow: Scalars['Boolean'];
  updateFollowerStatus: Scalars['Boolean'];
  createActivity: Activity;
  createPost: Post;
  updatePost: Post;
  removePost: Scalars['Boolean'];
  seenPost: Post;
  createEvent: Event;
  updateEvent: Event;
  removeEvent: Scalars['Boolean'];
  rsvpEvent: EventInvitation;
  createIncentive: Incentive;
  updateIncentive: Incentive;
  removeIncentive: Scalars['Boolean'];
  registerToIncentive: IncentiveParticipant;
  inviteToIncentive: Array<IncentiveParticipant>;
  removeIncentiveParticipant: Scalars['Boolean'];
  updateIncentiveParticipantStatus: IncentiveParticipant;
  createIncentiveBooking: IncentiveBooking;
  updateIncentiveBooking: IncentiveBooking;
  removeIncentiveBooking: Scalars['Boolean'];
  createWebinar: Webinar;
  updateWebinar: Webinar;
  removeWebinar: Scalars['Boolean'];
  registerToWebinar: WebinarParticipant;
  unregisterToWebinar: WebinarParticipant;
  inviteToWebinar: Array<WebinarParticipant>;
  createWebinarParticipant: WebinarParticipant;
  removeWebinarParticipant: Scalars['Boolean'];
  updateWebinarParticipantStatus: WebinarParticipant;
  createBroadcastStream: Scalars['Boolean'];
  startBroadcast: Scalars['Boolean'];
  stopBroadcast: Scalars['Boolean'];
  startCall: StartCallResponse;
  updateAsModerators: UpdateAsModeratorsResponse;
  createGroupChannel: CreateGroupChannelResponse;
  createEventInvitations: Array<EventInvitation>;
  removeEventInvitation: Scalars['Boolean'];
  updateEventInvitationStatus: EventInvitation;
  removePartnership: Scalars['Boolean'];
  createPartnershipRequest: Scalars['Boolean'];
  approvePartnershipRequest: Scalars['Boolean'];
  declinePartnershipRequest: Scalars['Boolean'];
  removePartnershipRequest: Scalars['Boolean'];
  createExperience: Experience;
  updateExperience: Experience;
  removeExperience: Scalars['Boolean'];
  createTopic: TopicResponse;
};

export type MutationUpdateProfileArgs = {
  profileData: UpdateProfileInput;
};

export type MutationSetPrimaryOrganisationArgs = {
  position?: Maybe<Scalars['String']>;
  organisationName?: Maybe<Scalars['String']>;
  organisationId?: Maybe<Scalars['String']>;
};

export type MutationChangeEmailArgs = {
  newEmail: Scalars['String'];
};

export type MutationRemoveConnectionArgs = {
  profileId: Scalars['String'];
};

export type MutationConnectArgs = {
  profileId: Scalars['String'];
};

export type MutationAcceptInvitationArgs = {
  profileId: Scalars['String'];
};

export type MutationRejectInvitationArgs = {
  profileId: Scalars['String'];
};

export type MutationSetNotificationStatusArgs = {
  notificationIds?: Maybe<Array<Scalars['String']>>;
  isRead: Scalars['Boolean'];
};

export type MutationUpdateMembershipStatusArgs = {
  actionType: MembershipActionType;
  profileId: Scalars['String'];
  organisationId: Scalars['String'];
};

export type MutationRemoveMembershipArgs = {
  profileId: Scalars['String'];
  organisationId: Scalars['String'];
};

export type MutationAddMembershipPermissionsArgs = {
  permissions: Array<MembershipPermission>;
  status?: Maybe<MembershipStatus>;
  profileId: Scalars['String'];
  organisationId: Scalars['String'];
};

export type MutationUpdateMembershipPermissionsArgs = {
  permissions: Array<MembershipPermission>;
  status?: Maybe<MembershipStatus>;
  profileId: Scalars['String'];
  organisationId: Scalars['String'];
};

export type MutationCreateOrganisationArgs = {
  organisationData: CreateOrganisationInput;
};

export type MutationUpdateOrganisationArgs = {
  organisationData: UpdateOrganisationInput;
  organisationId: Scalars['String'];
};

export type MutationChangeOrganisationOwnershipArgs = {
  newOwnerProfileId: Scalars['String'];
  organisationId: Scalars['String'];
};

export type MutationAcceptOrganisationOwnershipArgs = {
  organisationId: Scalars['String'];
};

export type MutationRejectOrganisationOwnershipArgs = {
  organisationId: Scalars['String'];
};

export type MutationFollowArgs = {
  organisationId: Scalars['String'];
};

export type MutationUnfollowArgs = {
  organisationId: Scalars['String'];
};

export type MutationUpdateFollowerStatusArgs = {
  actionType: FollowerActionType;
  profileId: Scalars['String'];
  organisationId: Scalars['String'];
};

export type MutationCreateActivityArgs = {
  activityData: CreateActivityInput;
};

export type MutationCreatePostArgs = {
  postData: CreatePostInput;
};
export type MutationCreateStreamPostArgs = {
  postData: CreateStreamPostInput;
};

export type MutationUpdatePostArgs = {
  postData: UpdatePostInput;
  postId: Scalars['String'];
};

export type MutationRemovePostArgs = {
  postId: Scalars['String'];
};

export type MutationSeenPostArgs = {
  id: Scalars['String'];
};

export type MutationCreateEventArgs = {
  eventData: CreateEventInput;
};

export type MutationUpdateEventArgs = {
  eventData: UpdateEventInput;
  eventId: Scalars['String'];
};

export type MutationRemoveEventArgs = {
  eventId: Scalars['String'];
};

export type MutationRsvpEventArgs = {
  status?: Maybe<EventInvitationStatus>;
  eventId: Scalars['String'];
};

export type MutationCreateIncentiveArgs = {
  incentiveData: CreateIncentiveInput;
};

export type MutationUpdateIncentiveArgs = {
  incentiveData: UpdateIncentiveInput;
  incentiveId: Scalars['String'];
};

export type MutationRemoveIncentiveArgs = {
  id: Scalars['String'];
};

export type MutationRegisterToIncentiveArgs = {
  incentiveId: Scalars['String'];
};

export type MutationInviteToIncentiveArgs = {
  incentiveParticipantsData: CreateIncentiveParticipantsInput;
};

export type MutationRemoveIncentiveParticipantArgs = {
  id: Scalars['String'];
};

export type MutationUpdateIncentiveParticipantStatusArgs = {
  status: IncentiveParticipantStatus;
  id: Scalars['String'];
};

export type MutationCreateIncentiveBookingArgs = {
  incentiveBookingData: CreateIncentiveBookingInput;
};

export type MutationUpdateIncentiveBookingArgs = {
  incentiveBookingData: UpdateIncentiveBookingInput;
  incentiveBookingId: Scalars['String'];
};

export type MutationRemoveIncentiveBookingArgs = {
  incentiveBookingId: Scalars['String'];
};

export type MutationCreateWebinarArgs = {
  webinarData: CreateWebinarInput;
};

export type MutationUpdateWebinarArgs = {
  webinarData: UpdateWebinarInput;
  webinarId: Scalars['String'];
};

export type MutationRemoveWebinarArgs = {
  id: Scalars['String'];
};

export type MutationRegisterToWebinarArgs = {
  webinarId: Scalars['String'];
};

export type MutationUnregisterToWebinarArgs = {
  webinarId: Scalars['String'];
};

export type MutationInviteToWebinarArgs = {
  webinarParticipantsData: CreateWebinarParticipantsInput;
};

export type MutationCreateWebinarParticipantArgs = {
  webinarParticipantData: CreateWebinarParticipantInput;
};

export type MutationRemoveWebinarParticipantArgs = {
  id: Scalars['String'];
};

export type MutationUpdateWebinarParticipantStatusArgs = {
  status: WebinarParticipantStatus;
  id: Scalars['String'];
};

export type MutationStartBroadcastArgs = {
  webinarConferenceId: Scalars['String'];
  webinarId: Scalars['String'];
};

export type MutationStopBroadcastArgs = {
  webinarConferenceId: Scalars['String'];
  webinarId: Scalars['String'];
};

export type MutationStartCallArgs = {
  chatChannelId?: Maybe<Scalars['String']>;
  isVideoOn: Scalars['Boolean'];
  participants: Array<Scalars['String']>;
};

export type MutationUpdateAsModeratorsArgs = {
  userIds: Array<Scalars['String']>;
  channelId: Scalars['String'];
};

export type MutationCreateGroupChannelArgs = {
  connectionProfileIds: Array<Scalars['String']>;
  profileId: Scalars['String'];
};

export type MutationCreateEventInvitationsArgs = {
  eventInvitationData: CreateEventInvitationsInput;
};

export type MutationRemoveEventInvitationArgs = {
  id: Scalars['String'];
};

export type MutationUpdateEventInvitationStatusArgs = {
  status: EventInvitationStatus;
  id: Scalars['String'];
};

export type MutationRemovePartnershipArgs = {
  partnershipId: Scalars['String'];
};

export type MutationCreatePartnershipRequestArgs = {
  receiverOrganisationId: Scalars['String'];
  senderOrganisationId: Scalars['String'];
  isSubscription: Scalars['Boolean'];
  price?: Maybe<Scalars['String']>;
  currency?: Maybe<Scalars['String']>;
  duration?: Maybe<Scalars['String']>;
};

export type MutationApprovePartnershipRequestArgs = {
  partnershipRequestId: Scalars['String'];
};

export type MutationDeclinePartnershipRequestArgs = {
  partnershipRequestId: Scalars['String'];
};

export type MutationRemovePartnershipRequestArgs = {
  partnershipRequestId: Scalars['String'];
};

export type MutationCreateExperienceArgs = {
  experienceData: CreateExperienceInput;
};

export type MutationUpdateExperienceArgs = {
  experienceData: UpdateExperienceInput;
  experienceId: Scalars['String'];
};

export type MutationRemoveExperienceArgs = {
  experienceId: Scalars['String'];
};

export type UpdateProfileInput = {
  name?: Maybe<Scalars['String']>;
  image?: Maybe<Scalars['String']>;
  backgroundImage?: Maybe<Scalars['String']>;
  dateOfBirth?: Maybe<Scalars['Date']>;
  gender?: Maybe<Scalars['String']>;
  bio?: Maybe<Scalars['String']>;
  headline?: Maybe<Scalars['String']>;
  regions?: Maybe<Array<Region>>;
  responsibilities?: Maybe<Array<ProfileResponsibility>>;
  location?: Maybe<Scalars['JSON']>;
  typesOfHoliday?: Maybe<Array<ProfileTypeOfHoliday>>;
  phoneNumber?: Maybe<Scalars['String']>;
  timezone?: Maybe<Scalars['String']>;
  onboarding?: Maybe<Scalars['JSON']>;
  receiveNotificationEmails?: Maybe<Scalars['Boolean']>;
  receiveNewMessagesEmails?: Maybe<Scalars['Boolean']>;
  useAutofollow?: Maybe<Scalars['Boolean']>;
};

export type CreateOrganisationInput = {
  name: Scalars['String'];
  type: OrganisationType;
  image?: Maybe<Scalars['String']>;
  backgroundImage?: Maybe<Scalars['String']>;
  location?: Maybe<Scalars['JSON']>;
  privacy?: Maybe<Scalars['String']>;
  peoplePrivacy?: Maybe<Scalars['String']>;
  website?: Maybe<Scalars['String']>;
  resourceUrl?: Maybe<Scalars['String']>;
  resources?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  size?: Maybe<OrganisationSize>;
  vanityId?: Maybe<Scalars['String']>;
  isPublic?: Maybe<Scalars['Boolean']>;
  privacySettings?: Maybe<OrganisationPrivacySettings>;
  status?: Maybe<OrganisationStatus>;
};

export type OrganisationPrivacySettings = {
  postsAccess?: Maybe<OrganisationPrivacyAccessSettings>;
  eventsAccess?: Maybe<OrganisationPrivacyAccessSettings>;
  webinarsAccess?: Maybe<OrganisationPrivacyAccessSettings>;
  incentivesAccess?: Maybe<OrganisationPrivacyAccessSettings>;
  trainingsAccess?: Maybe<OrganisationPrivacyAccessSettings>;
};

export type OrganisationPrivacyAccessSettings = {
  public?: Maybe<Scalars['Boolean']>;
  approvedFollowers?: Maybe<Scalars['Boolean']>;
};

export type UpdateOrganisationInput = {
  name?: Maybe<Scalars['String']>;
  type?: Maybe<OrganisationType>;
  image?: Maybe<Scalars['String']>;
  backgroundImage?: Maybe<Scalars['String']>;
  location?: Maybe<Scalars['JSON']>;
  privacy?: Maybe<Scalars['String']>;
  peoplePrivacy?: Maybe<Scalars['String']>;
  website?: Maybe<Scalars['String']>;
  resourceUrl?: Maybe<Scalars['String']>;
  resources?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  size?: Maybe<OrganisationSize>;
  vanityId?: Maybe<Scalars['String']>;
  isPublic?: Maybe<Scalars['Boolean']>;
  privacySettings?: Maybe<OrganisationPrivacySettings>;
  status?: Maybe<OrganisationStatus>;
};

export type CreateActivityInput = {
  type: ActivityType;
  data?: Maybe<Scalars['JSON']>;
  organisationId?: Maybe<Scalars['ID']>;
  webinarId?: Maybe<Scalars['ID']>;
};

export enum ActivityType {
  OrganisationPageView = 'OrganisationPageView',
  OrganisationFollower = 'OrganisationFollower',
  PostSeen = 'PostSeen',
  EventRsvp = 'EventRSVP',
  WebinarVideoView = 'WebinarVideoView',
  WebinarCompleted = 'WebinarCompleted',
  CommentMessageResponse = 'CommentMessageResponse',
}

export type Activity = {
  __typename?: 'Activity';
  id: Scalars['ID'];
  type: ActivityType;
  data?: Maybe<Scalars['JSON']>;
  createdAt: Scalars['Date'];
  updatedAt: Scalars['Date'];
  webinarId: Maybe<Scalars['ID']>;
};

export type CreatePostInput = {
  video?: Maybe<Scalars['String']>;
  image?: Maybe<Scalars['String']>;
  mediaHeight?: Maybe<Scalars['Float']>;
  mediaWidth?: Maybe<Scalars['Float']>;
  mediaFormat?: Maybe<Scalars['String']>;
  document?: Maybe<Scalars['String']>;
  text?: Maybe<Scalars['String']>;
  type: PostType;
  organisationId?: Maybe<Scalars['ID']>;
  eventId?: Maybe<Scalars['ID']>;
  incentiveId?: Maybe<Scalars['ID']>;
  webinarId?: Maybe<Scalars['ID']>;
  postId?: Maybe<Scalars['ID']>;
  imageCategory?: Maybe<Scalars['String']>;
  multipleImages?: MultipleImages[];
  scheduledAt?: Scalars['Date'] | Scalars['String'];
  status: Scalars['String'];
  parentOrgId?: Maybe<Scalars['ID']>;
  isPartnerPost?: Maybe<Scalars['Boolean']>;
};
export type CreateStreamPostInput = {
  organisations?: Maybe<Array<['String']>>;
  users?: Maybe<Array<['String']>>;
  image?: Maybe<Scalars['String']>;
  imageFormat?: Maybe<Scalars['String']>;
  imageWidth?: Maybe<Scalars['Float']>;
  imageHeight?: Maybe<Scalars['Float']>;
  video?: Maybe<Scalars['String']>;
  videoWidth?: Maybe<Scalars['Float']>;
  videoHeight?: Maybe<Scalars['Float']>;
  videoFormat?: Maybe<Scalars['String']>;
  document?: Maybe<Scalars['String']>;
  scheduledAt?: Maybe<Scalars['String']>;
  text?: Maybe<Scalars['String']>;
  type: PostType;
  postId: Scalars['ID'];
  organisationId?: Maybe<Scalars['ID']>;
  eventId?: Maybe<Scalars['ID']>;
  incentiveId?: Maybe<Scalars['ID']>;
  webinarId?: Maybe<Scalars['ID']>;
  postAudience?: Maybe<Scalars['JSON']>;
  documentUrl?: Maybe<Scalars['String']>;
  documentSize?: Maybe<Scalars['String']>;
  documentFormat?: Maybe<Scalars['String']>;
  documentName?: Maybe<Scalars['String']>;
  imageCategory?: Maybe<Scalars['String']>;
  multipleImages?: MultipleImages[];
  parentOrgId?: Maybe<Scalars['ID']>;
};
export type CreateStreamCommentInput = {
  text?: Maybe<Scalars['String']>;
  users?: Maybe<Array<['String']>>;
  organisations?: Maybe<Array<['String']>>;
  parentId?: Maybe<Scalars['String']>;
};

export type UpdatePostInput = {
  image?: Maybe<Scalars['String']>;
  text?: Maybe<Scalars['String']>;
  scheduledAt?: Scalars['Date'] | Scalars['String'];
};

export type CreateEventInput = {
  name: Scalars['String'];
  image?: Maybe<Scalars['String']>;
  location?: Maybe<Scalars['JSON']>;
  isOnline?: Maybe<Scalars['Boolean']>;
  url?: Maybe<Scalars['String']>;
  description: Scalars['String'];
  isAllDay?: Maybe<Scalars['Boolean']>;
  startDate: Scalars['Date'];
  endDate: Scalars['Date'];
  isPublic?: Maybe<Scalars['Boolean']>;
  isHostsManagedRSVP?: Maybe<Scalars['Boolean']>;
  isGuestsCanInvite?: Maybe<Scalars['Boolean']>;
  isGuestListVisible?: Maybe<Scalars['Boolean']>;
  organisationId: Scalars['ID'];
  type: EventType;
};
export type StreamCreateEventInput = {
  name: Scalars['String'];
  image?: Maybe<Scalars['String']>;
  location?: Maybe<Scalars['JSON']>;
  isOnline?: Maybe<Scalars['Boolean']>;
  url?: Maybe<Scalars['String']>;
  description: Scalars['String'];
  isAllDay?: Maybe<Scalars['Boolean']>;
  startDate: Scalars['Date'];
  endDate: Scalars['Date'];
  isPublic?: Maybe<Scalars['Boolean']>;
  isHostsManagedRSVP?: Maybe<Scalars['Boolean']>;
  isGuestsCanInvite?: Maybe<Scalars['Boolean']>;
  isGuestListVisible?: Maybe<Scalars['Boolean']>;
  organisationId: Scalars['ID'];
  type: EventType;
  eventId: Scalars['ID'];
};

export type UpdateEventInput = {
  name?: Maybe<Scalars['String']>;
  image?: Maybe<Scalars['String']>;
  location?: Maybe<Scalars['JSON']>;
  isOnline?: Maybe<Scalars['Boolean']>;
  url?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  isAllDay?: Maybe<Scalars['Boolean']>;
  startDate?: Maybe<Scalars['Date']>;
  endDate?: Maybe<Scalars['Date']>;
  isPublic?: Maybe<Scalars['Boolean']>;
  isHostsManagedRSVP?: Maybe<Scalars['Boolean']>;
  isGuestsCanInvite?: Maybe<Scalars['Boolean']>;
  isGuestListVisible?: Maybe<Scalars['Boolean']>;
  organisationId?: Maybe<Scalars['ID']>;
};

export type StreamUpdateEventInput = {
  name?: Maybe<Scalars['String']>;
  image?: Maybe<Scalars['String']>;
  location?: Maybe<Scalars['JSON']>;
  isOnline?: Maybe<Scalars['Boolean']>;
  url?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  isAllDay?: Maybe<Scalars['Boolean']>;
  startDate?: Maybe<Scalars['Date']>;
  endDate?: Maybe<Scalars['Date']>;
  isPublic?: Maybe<Scalars['Boolean']>;
  isHostsManagedRSVP?: Maybe<Scalars['Boolean']>;
  isGuestsCanInvite?: Maybe<Scalars['Boolean']>;
  isGuestListVisible?: Maybe<Scalars['Boolean']>;
  organisationId?: Maybe<Scalars['ID']>;
};

export type CreateIncentiveInput = {
  name: Scalars['String'];
  image?: Maybe<Scalars['String']>;
  description: Scalars['String'];
  startDate: Scalars['Date'];
  endDate: Scalars['Date'];
  terms?: Maybe<Scalars['String']>;
  type: IncentiveType;
  regions: Array<Region>;
  bookingTypes: Array<IncentiveBookingType>;
  bookingFields: Array<IncentiveBookingFieldInput>;
  isPublic?: Maybe<Scalars['Boolean']>;
  isParticipantListVisible?: Maybe<Scalars['Boolean']>;
  organisationId: Scalars['ID'];
};
export type StreamCreateIncentiveInput = {
  name: Scalars['String'];
  image?: Maybe<Scalars['String']>;
  description: Scalars['String'];
  startDate: Scalars['Date'];
  endDate: Scalars['Date'];
  terms?: Maybe<Scalars['String']>;
  type: IncentiveType;
  regions: Array<Region>;
  bookingTypes: Array<IncentiveBookingType>;
  bookingFields: Array<IncentiveBookingFieldInput>;
  isPublic?: Maybe<Scalars['Boolean']>;
  isParticipantListVisible?: Maybe<Scalars['Boolean']>;
  organisationId: Scalars['ID'];
  incentiveId: Scalars['ID'];
};

export type IncentiveBookingFieldInput = {
  name: Scalars['String'];
  type?: Maybe<IncentiveBookingFieldType>;
  isOptional?: Maybe<Scalars['Boolean']>;
};

export type UpdateIncentiveInput = {
  name?: Maybe<Scalars['String']>;
  image?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  startDate?: Maybe<Scalars['Date']>;
  endDate?: Maybe<Scalars['Date']>;
  terms?: Maybe<Scalars['String']>;
};

export type CreateIncentiveParticipantsInput = {
  incentiveId: Scalars['ID'];
  profileIds?: Maybe<Array<Scalars['ID']>>;
};

export type CreateIncentiveBookingInput = {
  dataArray: Array<IncentiveBookingDataItemInput>;
  incentiveId: Scalars['ID'];
};

export type IncentiveBookingDataItemInput = {
  name: Scalars['String'];
  value: Scalars['String'];
  type: IncentiveBookingFieldType;
};

export type UpdateIncentiveBookingInput = {
  dataArray?: Maybe<Array<IncentiveBookingDataItemInput>>;
};

export type CreateWebinarInput = {
  name: Scalars['String'];
  image?: Maybe<Scalars['String']>;
  description: Scalars['String'];
  startDate: Scalars['Date'];
  endDate: Scalars['Date'];
  keywords?: Maybe<Array<Scalars['String']>>;
  isParticipantListVisible?: Maybe<Scalars['Boolean']>;
  type: WebinarType;
  isPublic?: Maybe<Scalars['Boolean']>;
  isParticipantsCanInvite?: Maybe<Scalars['Boolean']>;
  organisationId: Scalars['ID'];
  duration?: Scalars['Int'];
  liveStreamRecordingId?: Maybe<Scalars['String']>;
  isInternal?: Maybe<Scalars['Boolean']>;
};

export type StreamCreateWebinarInput = {
  name: Scalars['String'];
  webinarId: Scalars['ID'];
  image?: Maybe<Scalars['String']>;
  description: Scalars['String'];
  startDate: Scalars['Date'];
  endDate: Scalars['Date'];
  keywords?: Maybe<Array<Scalars['String']>>;
  isParticipantListVisible?: Maybe<Scalars['Boolean']>;
  type: WebinarType;
  isPublic?: Maybe<Scalars['Boolean']>;
  isParticipantsCanInvite?: Maybe<Scalars['Boolean']>;
  organisationId: Scalars['ID'];
  duration?: Scalars['Int'];
  liveStreamRecordingId?: Maybe<Scalars['String']>;
};

export type CreateWebinarInputWithPreRecorded = {
  name: Scalars['String'];
  image?: Maybe<Scalars['String']>;
  description: Scalars['String'];
  keywords?: Maybe<Array<Scalars['String']>>;
  isParticipantListVisible?: Maybe<Scalars['Boolean']>;
  type: WebinarType;
  isPublic?: Maybe<Scalars['Boolean']>;
  isParticipantsCanInvite?: Maybe<Scalars['Boolean']>;
  organisationId: Scalars['ID'];
  liveStreamRecordingId: Scalars['String'];
};

export type UpdateWebinarInput = {
  name?: Maybe<Scalars['String']>;
  image?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  startDate?: Maybe<Scalars['Date']>;
  endDate?: Maybe<Scalars['Date']>;
  keywords?: Maybe<Array<Scalars['String']>>;
  isParticipantListVisible?: Maybe<Scalars['Boolean']>;
};

export type StreamUpdateWebinarInput = {
  name?: Maybe<Scalars['String']>;
  image?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  startDate?: Maybe<Scalars['Date']>;
  endDate?: Maybe<Scalars['Date']>;
  keywords?: Maybe<Array<Scalars['String']>>;
  isParticipantListVisible?: Maybe<Scalars['Boolean']>;
};

export type CreateWebinarParticipantsInput = {
  webinarId: Scalars['ID'];
  profileIds?: Maybe<Array<Scalars['ID']>>;
};

export type CreateWebinarParticipantInput = {
  webinarId: Scalars['ID'];
  profileId: Scalars['ID'];
  role: Scalars['String'];
};

export type StreamCreateWebinarParticipantInput = {
  webinarId: Scalars['ID'];
  profileId: Scalars['ID'];
  role: Scalars['String'];
  organisationId: Scalars['ID'];
};

export type StartCallResponse = {
  __typename?: 'StartCallResponse';
  channelId: Scalars['String'];
};

export type UpdateAsModeratorsResponse = {
  __typename?: 'UpdateAsModeratorsResponse';
  channelId: Scalars['String'];
};

export type CreateGroupChannelResponse = {
  __typename?: 'CreateGroupChannelResponse';
  channelId: Scalars['String'];
};

export type CreateEventInvitationsInput = {
  eventId: Scalars['ID'];
  profileIds?: Maybe<Array<Scalars['ID']>>;
};

export type ZoomMeetingEndedResponse = {
  __typename?: 'ZoomMeetingEndedResponse';
  zoomMeetingId: Scalars['String'];
};

export type CreateExperienceInput = {
  position?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  location?: Maybe<Scalars['JSON']>;
  startDate?: Maybe<Scalars['Date']>;
  endDate?: Maybe<Scalars['Date']>;
  organisationId?: Maybe<Scalars['ID']>;
  organisationName?: Maybe<Scalars['String']>;
};

export type UpdateExperienceInput = {
  position?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  location?: Maybe<Scalars['JSON']>;
  startDate?: Maybe<Scalars['Date']>;
  endDate?: Maybe<Scalars['Date']>;
  organisationId?: Maybe<Scalars['ID']>;
  organisationName?: Maybe<Scalars['String']>;
};

export type Subscription = {
  __typename?: 'Subscription';
  newNotification: Notification;
  zoomMeetingEnded: ZoomMeetingEndedResponse;
};

export type UpdateAutofollowOrgInput = {
  region: Region;
  organisationIds: Array<Scalars['String']>;
};

export type CreateUpdateTopicInput = {
  fullName: Scalars['String'];
  shortName: Scalars['String'];
  docsbotId: Scalars['String'];
  linkedOrganisations?: Maybe<Scalars['ID']>;
  isPublic: Scalars['Boolean']; // Public or Private
};

export type TopicResponse = {
  // __typename?: 'TopicResponse';
  id: Scalars['ID'];
  // linkedOrganisationIds: Array<string>;
  fullName: Scalars['String'];
  shortName: Scalars['String'];
  docsbotId: Scalars['String'];
  isPublic: Scalars['Boolean'];
  // organisationDetails?: Maybe<Array<Organisation>>;
};

export type NotificationPreferenceDataTypes = {
  key: Scalars['String'];
  title: Scalars['String'];
  subTitle: Scalars['String'];
};
