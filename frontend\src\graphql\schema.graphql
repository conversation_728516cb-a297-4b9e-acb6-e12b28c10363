type Achievement {
  id: ID!
  type: AchievementType!
  steps: Float!
  stepsComplete: Float!
  number: Float
  createdAt: Date!
  updatedAt: Date!
}

enum AchievementType {
  ProfilePerfect
  DailyQuizStreak
  HotStreak
  HabloFounder
}

type Activity {
  id: ID!
  type: ActivityType!
  data: JSON
  createdAt: Date!
  updatedAt: Date!
}

enum ActivityType {
  OrganisationPageView
  OrganisationFollower
  OrganisationFollowerMigration
  PostSeen
  EventRSVP
  WebinarVideoView
  PostInteractions
  PostImpressions
  PostView
  PostCommentLike
  PostInteractions
  UserConnection
  InviteEmailContact
  InviteEmailContactMigration
  EmailInvitationRegistration
  EmailInvitationRegistrationMigration
  ActiveSession
  VariableLoginReward
  NotificationEnabled
  NewIncentive
  IncentiveRegistration
  IncentiveRegistrationMigration
  IncentiveBooking
  ProfileCompletion
  WebinarCompleted
}

input AddPreApprovedDomainInput {
  organisationId: String!
  domain: String!
}

type AnalyticsResponse {
  OrganisationFollower: [AnalyticsResponseValue!]!
  OrganisationPageView: [AnalyticsResponseValue!]!
  PostSeen: [AnalyticsResponseValue!]!
  EventRSVP: [AnalyticsResponseValue!]!
  WebinarVideoView: [AnalyticsResponseValue!]!
  PostInteractions: [AnalyticsResponseValue!]!
  PostImpressions: [AnalyticsResponseValue!]!
}

type AnalyticsResponseValue {
  date: String!
  value: Int!
}

type AutofollowRegionsItem {
  title: String!
  organisations: [Organisation!]
}

input ChangeFields {
  isRead: Boolean
  isSeen: Boolean
}

type ChatAuthTokenResponse {
  authToken: String!
}

input ChatDto {
  topicId: String!
  question: String!
  history: JSON
}

type ChatResult {
  response: JSON
}

type CloudinarySignatureResponse {
  signature: String!
  timestamp: Float!
}

type CommentMessageResponse {
  success: String!
}

type ConferenceAuthTokenResponse {
  authToken: String!
}

type Connection {
  id: ID!
  profile: Profile!
  streamChannelId: String!
}

type ConnectionRequest {
  status: String!
  profile: Profile!
}

input ConnectionsFilter {
  connectionProfileId: String
}

type ConnectionsResult {
  records: [Connection!]!
  totalCount: Int!
}

input CreateActivityInput {
  type: ActivityType!
  data: JSON
  organisationId: ID
  webinarId: ID
}

input CreateDestinationPageCategoryInput {
  destinationPageId: String!
  name: String!
}

input CreateDestinationPageInput {
  name: String!
  status: String!
  path: [String!]!
  categories: [String!]!
  hostOrganisationIds: [String!]!
  image: String!
}

input CreateDestinationPageOrgInput {
  organisationId: String!
  destinationPageId: String!
  categoryId: String!
}

input CreateEventInput {
  name: String!
  image: String
  location: JSON
  isOnline: Boolean
  url: String
  description: String!
  isAllDay: Boolean
  startDate: Date!
  endDate: Date!
  isPublic: Boolean
  isHostsManagedRSVP: Boolean
  isGuestsCanInvite: Boolean
  isGuestListVisible: Boolean
  organisationId: ID!
  type: EventType!
}

input CreateEventInvitationsInput {
  eventId: ID!
  profileIds: [ID!] = []
}

input CreateExperienceInput {
  position: String
  description: String
  location: JSON
  startDate: Date
  endDate: Date
  organisationId: ID
  organisationName: String
}

type CreateGroupChannelResponse {
  channelId: String!
}

input CreateIncentiveBookingInput {
  dataArray: [IncentiveBookingDataItemInput!]!
  incentiveId: ID!
}

input CreateIncentiveInput {
  name: String!
  image: String
  description: String!
  startDate: Date!
  endDate: Date!
  terms: String
  type: IncentiveType!
  regions: [Region!]!
  bookingTypes: [IncentiveBookingType!]!
  bookingFields: [IncentiveBookingFieldInput!]!
  isPublic: Boolean
  isParticipantListVisible: Boolean
  organisationId: ID!
}

input CreateIncentiveParticipantsInput {
  incentiveId: ID!
  profileIds: [ID!] = []
}

input CreateOrganisationInput {
  name: String!
  type: OrganisationType!
  image: String
  backgroundImage: String
  location: JSON
  order: JSON
  destinationCategory: JSON
  privacy: String
  followingPrivacy: String
  peoplePrivacy: String
  additionalPrivacy: String
  website: String
  resourceUrl: String
  resources: String
  description: String
  size: OrganisationSize
  vanityId: String
  isPublic: Boolean
  privacySettings: OrganisationPrivacySettings
  status: OrganisationStatus
}

input CreatePartnershipRequestDto {
  senderProfileId: String
  senderOrganisationId: String!
  receiverOrganisationId: String!
  status: String
  isSubscription: Boolean!
  price: String
  currency: String
  duration: String
}

input CreatePostInput {
  type: PostType!
  image: String
  mediaWidth: Float
  mediaHeight: Float
  mediaFormat: String
  multipleImages: [ImageDetailsInput!]
  imageCategory: PostImageCategory
  video: String
  document: String
  text: String
  status: PostStatus
  scheduledAt: Date
  organisationId: ID
  eventId: ID
  incentiveId: ID
  webinarId: ID
  postAudience: JSON
}

input CreateStreamCommentInput {
  text: String
  users: JSON
  organisations: JSON
  parentId: String
}

input CreateStreamOrganisationInput {
  name: String!
  type: StreamOrganisationType!
  image: String
  backgroundImage: String
  location: JSON
  privacy: String
  peoplePrivacy: String
  website: String
  resourceUrl: String
  resources: String
  description: String
  size: StreamOrganisationSize
  vanityId: String
  isPublic: Boolean
  privacySettings: StreamOrganisationPrivacySettings
  status: StreamOrganisationStatus
  organisationId: String!
}

input CreateStreamPostInput {
  image: String
  imageWidth: Float
  imageHeight: Float
  imageFormat: String
  multipleImages: [MultipleImageDetailsInput!]
  imageCategory: ImageCategory
  document: String
  documentUrl: String
  documentSize: String
  documentFormat: String
  documentName: String
  video: String
  videoWidth: Float
  videoHeight: Float
  videoFormat: String
  text: String
  scheduledAt: String
  type: PostStreamType!
  organisationId: ID
  postId: ID
  users: JSON
  organisations: JSON
  postAudience: JSON
}

input CreateStreamUpdateInput {
  text: String
  organisationId: ID
  postId: ID
  eventId: ID
  incentiveId: ID
  webinarId: ID
}

input CreateTopicDto {
  fullName: String!
  shortName: String!
  docsbotId: String!
  linkedOrganisations: [String!]!
  isPublic: Boolean
}

input CreateWebinarInput {
  name: String!
  image: String
  description: String!
  startDate: Date
  endDate: Date
  keywords: [String!]
  isParticipantListVisible: Boolean
  type: WebinarType!
  isPublic: Boolean
  isParticipantsCanInvite: Boolean
  isInternal: Boolean
  organisationId: ID!
  liveStreamRecordingId: String
  duration: Float
}

input CreateWebinarParticipantInput {
  webinarId: ID!
  profileId: ID!
  role: String!
}

input CreateWebinarParticipantsInput {
  webinarId: ID!
  profileIds: [ID!] = []
}

"""
Date custom scalar type
"""
scalar Date

type DeleteDestinationPageResponse {
  success: Boolean
  errorMessage: String
}

type DestinationPage {
  id: ID!
  hostOrganisationIds: [String!]
  path: [String!]
  categoryIds: [String!]
  image: String
  name: String
  vanityId: String
  status: String
  organisations: [Organisation!]
}

input DestinationPageCategory {
  id: String!
  order: Float!
}

type DestinationPageCategoryContent {
  id: String!
  name: String
  order: Float
  content: [Organisation!]
}

type DestinationPagePathItem {
  id: String
  name: String
}

type DestinationPageResponse {
  id: String
  path: [String!]
  pathDetail: [DestinationPagePathItem!]
  categoryIds: [String!]
  image: String
  name: String
  vanityId: String
  status: String
  children: [DestinationPageTree!]
  hostOrganisations: [Organisation!]
  content: [DestinationPageCategoryContent!]
}

input DestinationPagesFilter {
  searchText: String
}

type DestinationPagesResult {
  records: [DestinationPageTree!]!
  totalCount: Int!
}

type DestinationPageTree {
  id: String
  path: [String!]
  pathDetail: [DestinationPagePathItem!]
  categoryIds: [String!]
  image: String
  name: String
  vanityId: String
  status: String
  children: [DestinationPageTree!]
  hostOrganisations: [Organisation!]
}

enum DeviceLogInCount {
  Increment
  Decrement
}

type DirectUploadResponse {
  id: String!
  url: String!
}

input EmailConnectionInput {
  emailAddresses: String!
  inputTextChoice: Int!
}

type Event {
  id: ID!
  name: String!
  displayName: String
  image: String
  location: JSON
  type: EventType!
  isOnline: Boolean!
  url: String
  description: String!
  isAllDay: Boolean!
  startDate: Date!
  endDate: Date!
  isPublic: Boolean
  isHostsManagedRSVP: Boolean
  isGuestsCanInvite: Boolean
  isGuestListVisible: Boolean
  isEnded: Boolean!
  createdAt: Date!
  updatedAt: Date!
  organisation: Organisation!
  invitation: EventInvitation
  invitations(filter: EventInvitationsFilter): [EventInvitation!]!
  incentive: Incentive
  webinar: Webinar
}

type EventInvitation {
  id: ID!
  status: EventInvitationStatus
  invitationStatus: EventInvitationSentStatus
  createdAt: Date!
  updatedAt: Date!
  event: Event!
  organisation: Organisation
  profile: Profile!
  inviterProfile: Profile
}

enum EventInvitationSentStatus {
  Pending
  Sent
}

input EventInvitationsFilter {
  profileId: ID
  status: [EventInvitationStatus!] = []
}

enum EventInvitationStatus {
  Attending
  Interested
  Declined
  InvitedByGuest
  InvitedByHost
  InvitedAttending
  InvitedInterested
  InvitedDeclined
  Blocked
}

type EventMessageResponse {
  success: Boolean!
}

input EventsFilter {
  eventInvitationStatus: [EventInvitationStatus!] = []
  organisationId: String
  type: [EventType!] = []
  isOnline: Boolean = null
  isPublic: Boolean = null
  searchText: String
  isEnded: Boolean = null
}

type EventsResult {
  records: [Event!]!
  totalCount: Int!
}

enum EventType {
  Event
  IncentiveStart
  IncentiveEnd
  Webinar
}

type Experience {
  id: ID!
  position: String!
  description: String
  location: JSON
  startDate: Date
  endDate: Date
  organisation: Organisation
  organisationName: String
  profile: Profile!
  createdAt: Date!
  updatedAt: Date!
}

type FeedsAuthTokenResponse {
  authToken: String!
}

type FeedsOrgTokenResponse {
  orgToken: String!
}

type Follower {
  id: ID!
  status: FollowerStatus
  actions: [FollowerAction!]
  createdAt: Date!
  updatedAt: Date!
  profile: Profile!
  organisation: Organisation
}

type FollowerAction {
  profileId: String!
  actionType: FollowerActionType!
  actionDate: Date!
}

enum FollowerActionType {
  Accept
  Reject
  Inactivate
}

input FollowersFilter {
  profileId: String
  organisationId: String
  status: [FollowerStatus!] = []
  includeActiveProfile: Boolean = null
}

type FollowersResult {
  records: [Follower!]!
  totalCount: Int!
}

enum FollowerStatus {
  Active
  Pending
  Rejected
  Inactive
}

type GettingStartedStepResponse {
  title: String!
  status: String!
}

type HomePageProfileRewardsDataResponse {
  loginDayStreak: Float!
  habloPoints: Float!
  lifeTimePoints: Float!
  tier: String!
  nextTier: String!
  nextTierPoints: Float!
  achievements: Float!
}

enum ImageCategory {
  Single
  Multiple
}

input ImageDetailsInput {
  image: String
  imageWidth: Float
  imageHeight: Float
  imageFormat: String
  url: String
  status: String
}

type Incentive {
  id: ID!
  name: String!
  type: IncentiveType!
  image: String
  description: String!
  startDate: Date!
  endDate: Date!
  regions: [Region!]!
  bookingTypes: [IncentiveBookingType!]
  bookingFields: [IncentiveBookingField!]
  terms: String
  isPublic: Boolean!
  isParticipantListVisible: Boolean!
  isEnded: Boolean!
  createdAt: Date!
  updatedAt: Date!
  organisation: Organisation!
  participant: IncentiveParticipant
  participants(filter: IncentiveParticipantsFilter): [IncentiveParticipant!]!
  incentiveBookingsCount: Int!
}

type IncentiveBooking {
  id: ID!
  dataArray: [IncentiveBookingDataItem!]
  createdAt: Date!
  updatedAt: Date!
  incentive: Incentive!
  profile: Profile!
}

type IncentiveBookingDataItem {
  name: String!
  value: String!
  type: IncentiveBookingFieldType!
}

input IncentiveBookingDataItemInput {
  name: String!
  value: String!
  type: IncentiveBookingFieldType!
}

type IncentiveBookingField {
  name: String!
  type: IncentiveBookingFieldType!
  isOptional: Boolean!
}

input IncentiveBookingFieldInput {
  name: String!
  type: IncentiveBookingFieldType = String
  isOptional: Boolean = false
}

enum IncentiveBookingFieldType {
  String
}

input IncentiveBookingsFilter {
  incentiveId: ID
  profileId: ID
}

type IncentiveBookingsResult {
  records: [IncentiveBooking!]!
  totalCount: Int!
}

enum IncentiveBookingType {
  Flight
  Hotel
  Cruise
  Other
}

enum IncentiveInvitationStatus {
  Pending
  Sent
}

type IncentiveMessageResponse {
  id: String!
  organisationId: String!
  vanityId: String!
}

type IncentiveParticipant {
  id: ID!
  status: IncentiveParticipantStatus
  invitationStatus: IncentiveInvitationStatus
  createdAt: Date!
  updatedAt: Date!
  incentive: Incentive!
  organisation: Organisation
  profile: Profile!
  inviterProfile: Profile
}

input IncentiveParticipantsFilter {
  profileId: ID
  status: [IncentiveParticipantStatus!] = []
}

enum IncentiveParticipantStatus {
  Registered
  InvitedByHost
  InvitedByParticipant
  InvitedRegistered
  Blocked
}

input IncentivesFilter {
  incentiveParticipantStatus: [IncentiveParticipantStatus!] = []
  organisationId: String
  type: [IncentiveType!] = []
  bookingType: [IncentiveBookingType!] = []
  isPublic: Boolean = null
  searchText: String
  isEnded: Boolean = null
}

type IncentivesResult {
  records: [Incentive!]!
  totalCount: Int!
}

enum IncentiveType {
  CashbackOrVoucher
  Prize
  FAMTrip
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON

input LogActiveSessionInput {
  notificationEnabled: Boolean!
  variableMinutes: Float
  variableRewardPoints: Float
}

type LoyaltyPoint {
  id: ID!
  type: ActivityType!
  points: Float!
  count: Float!
  rollingPoints: Float!
  lifeTimePoints: Float!
  streak: Float!
  tier: String!
  placeholders: JSON
  createdAt: Date!
  updatedAt: Date!
}

type LoyaltyPointsResult {
  records: [LoyaltyPoint!]!
  totalCount: Int!
}

type Membership {
  id: ID!
  organisationName: String
  isPrimary: Boolean
  position: String
  permissions: [MembershipPermission!]
  status: MembershipStatus
  actions: [MembershipAction!]
  isAutoApprove: Boolean
  createdAt: Date
  updatedAt: Date
  profile: Profile!
  organisation: Organisation
}

type MembershipAction {
  profileId: String!
  actionType: MembershipActionType!
  actionDate: Date!
}

enum MembershipActionType {
  Accept
  Reject
}

enum MembershipPermission {
  Owner
  OwnerPending
  Admin
  HiddenAdmin
  Manager
  Editor
  Staff
  Linked
  InviteMembersToEvents
  Member
}

input MembershipsFilter {
  organisationId: String
  status: [MembershipStatus!] = []
  permission: [MembershipPermission!] = []
  isPrimary: Boolean = null
  includeActiveProfile: Boolean = null
}

type MembershipsResult {
  records: [Membership!]!
  totalCount: Int!
}

enum MembershipStatus {
  Active
  Pending
  Rejected
  Inactive
}

type MentionsItem {
  profiles: [Profile!]
  organisations: [Organisation!]
}

input MobileDevicesLoggedInCountInput {
  profileId: String!
  type: DeviceLogInCount!
}

type MultipleImageDetails {
  image: String!
  imageWidth: Float!
  imageHeight: Float!
  imageFormat: String!
}

input MultipleImageDetailsInput {
  image: String
  imageWidth: Float
  imageHeight: Float
  imageFormat: String
  url: String
  status: String
}

type Mutation {
  updateProfile(profileData: UpdateProfileInput!): Profile!
  removeProfile: Boolean!
  setPrimaryOrganisation(
    preApprove: Boolean
    position: String
    organisationName: String
    organisationId: String
  ): Boolean!
  sendEmailVerification: Boolean!
  changeEmail(newEmail: String!): Boolean!
  addProfileParentOrganisation(data: ProfileParentOrganisationInput!): Profile!
  removeProfileParentOrganisation(data: ProfileParentOrganisationInput!): Profile!
  acceptMemberByParentAdmin(data: ProfileParentOrganisationInput!): Boolean!
  declineMemberByParentAdmin(data: ProfileParentOrganisationInput!): Profile!
  mobileDevicesLoggedInCount(data: MobileDevicesLoggedInCountInput!): Boolean!
  logActiveSession(data: LogActiveSessionInput!): Boolean!
  inviteByEmail(emailConnectionInput: EmailConnectionInput!): Boolean!
  removeConnection(profileId: String!): Boolean!
  connect(profileId: String!): Boolean!
  acceptInvitation(profileId: String!): Boolean!
  rejectInvitation(profileId: String!): Boolean!
  setNotificationStatus(notificationIds: [String!], fields: ChangeFields!): [Notification!]!
  createExperience(experienceData: CreateExperienceInput!): Experience!
  updateExperience(experienceData: UpdateExperienceInput!, experienceId: String!): Experience!
  removeExperience(experienceId: String!): Boolean!
  createOrganisation(organisationData: CreateOrganisationInput!): Organisation!
  updateOrganisation(organisationData: UpdateOrganisationInput!, organisationId: String!): Organisation!
  adminPanelRemoveOrganisation(organisationId: String!): Boolean!
  changeOrganisationOwnership(newOwnerProfileId: String!, organisationId: String!): Boolean!
  acceptOrganisationOwnership(organisationId: String!): Boolean!
  rejectOrganisationOwnership(organisationId: String!): Boolean!
  findConnectAccount(organisationId: String!): Boolean!
  createConnectAccount(email: String!, organisationId: String!): String!
  retrieveConnectAccount(organisationId: String!): String!
  getCustomerPortal(organisationId: String!, partnershipId: String!): String!
  getStripeExpressLink(organisationId: String!): String!
  addPreApprovedDomains(data: AddPreApprovedDomainInput!): Boolean!
  removePreApprovedDomains(data: AddPreApprovedDomainInput!): Boolean!
  addParentOrganisation(data: ParentOrganisationInput!): Organisation!
  removeParentOrganisation(data: ParentOrganisationInput!): Organisation!
  updateMembershipStatus(actionType: MembershipActionType!, profileId: String!, organisationId: String!): Boolean!
  removeMembership(profileId: String!, organisationId: String!): Boolean!
  addMembershipPermissions(
    permissions: [MembershipPermission!]!
    status: MembershipStatus = Active
    profileId: String!
    organisationId: String!
  ): Boolean!
  updateMembershipPermissions(
    permissions: [MembershipPermission!]!
    status: MembershipStatus = Active
    profileId: String!
    organisationId: String!
  ): Boolean!
  follow(organisationId: String!): Boolean!
  unfollow(organisationId: String!): Boolean!
  updateFollowerStatus(actionType: FollowerActionType!, profileId: String!, organisationId: String!): Boolean!
  createActivity(activityData: CreateActivityInput!): Activity!
  activityLoyaltyPointsMigration: Boolean!
  createPost(postData: CreatePostInput!): Post!
  updatePost(postData: UpdatePostInput!, postId: String!): Post!
  updatePostScheduleTime(postData: UpdatePostScheduleDateInput!, postId: String!): Post!
  removePost(postId: String!): Boolean!
  seenPost(id: String!): Post!
  startCall(chatChannelId: String, isVideoOn: Boolean!, participants: [String!]!): StartCallResponse!
  updateAsModerators(userIds: [String!]!, channelId: String!): UpdateAsModeratorsResponse!
  createGroupChannel(connectionProfileIds: [String!]!, profileId: String!): CreateGroupChannelResponse!
  expireZoomMeetingMessage(
    timeout: Float!
    userName: String!
    userId: String!
    messageId: String!
  ): ZoomMeetingExpireMessageResponse!
  sendInboxMessageNotification(
    senderProfilePhoto: String!
    messageHeader: String!
    text: String!
    profileIds: [String!]!
  ): Boolean!
  removePartnership(partnershipId: String!): Boolean!
  createPartnershipRequest(partnershipRequestData: CreatePartnershipRequestDto!): Boolean!
  initiatePayment(partnershipRequestId: String!): String!
  retryPayment(partnershipRequestId: String!): String!
  approvePartnershipRequest(partnershipRequestId: String!): Boolean!
  declinePartnershipRequest(partnershipRequestId: String!): Boolean!
  removePartnershipRequest(partnershipRequestId: String!): Boolean!
  createEventInvitations(eventInvitationData: CreateEventInvitationsInput!): [EventInvitation!]!
  removeEventInvitation(id: String!): Boolean!
  updateEventInvitationStatus(status: EventInvitationStatus!, id: String!): EventInvitation!
  streamFollow(organisationId: String!): Boolean!
  streamUnfollow(organisationId: String!): Boolean!
  createEvent(eventData: CreateEventInput!): Event!
  updateEvent(eventData: UpdateEventInput!, eventId: String!): Event!
  removeEvent(eventId: String!): Boolean!
  rsvpEvent(status: EventInvitationStatus = Attending, eventId: String!): EventInvitation!
  createIncentive(incentiveData: CreateIncentiveInput!): Incentive!
  updateIncentive(incentiveData: UpdateIncentiveInput!, incentiveId: String!): Incentive!
  removeIncentive(id: String!): Boolean!
  registerToIncentive(incentiveId: String!): IncentiveParticipant!
  inviteToIncentive(incentiveParticipantsData: CreateIncentiveParticipantsInput!): [IncentiveParticipant!]!
  removeIncentiveParticipant(id: String!): Boolean!
  updateIncentiveParticipantStatus(status: IncentiveParticipantStatus!, id: String!): IncentiveParticipant!
  createIncentiveBooking(incentiveBookingData: CreateIncentiveBookingInput!): IncentiveBooking!
  updateIncentiveBooking(
    incentiveBookingData: UpdateIncentiveBookingInput!
    incentiveBookingId: String!
  ): IncentiveBooking!
  removeIncentiveBooking(incentiveBookingId: String!): Boolean!
  inviteToWebinar(webinarParticipantsData: CreateWebinarParticipantsInput!): [WebinarParticipant!]!
  createWebinarParticipant(webinarParticipantData: CreateWebinarParticipantInput!): WebinarParticipant!
  removeWebinarParticipant(id: String!): Boolean!
  updateWebinarParticipantStatus(status: WebinarParticipantStatus!, id: String!): WebinarParticipant!
  createStreamPost(postData: CreateStreamPostInput!): PostMessageResponse!
  repostStreamPost(
    postId: String!
    origOrganisationId: String!
    postAuthorId: String!
    postData: CreateStreamPostInput!
    organisationId: String!
    activityId: String!
  ): PostMessageResponse!
  updateStreamPost(postData: CreateStreamPostInput!, postId: String!): Boolean!
  addLikeStreamPost(postId: String!, feedId: String!, activityId: String!): Boolean!
  removeLikeStreamPost(postId: String!, reactionId: String!, feedId: String!, activityId: String!): Boolean!
  checkNewPost(activityId: String!): Boolean!
  addLikeStreamPostComment(
    postId: String!
    organisationId: String!
    postAuthorId: String!
    commentId: String!
    activityId: String!
  ): Boolean!
  removeLikeStreamPostComment(reactionId: String!, commentId: String!, activityId: String!): Boolean!
  removeStreamComment(commentId: String!): Boolean!
  updateStreamComment(commentData: CreateStreamCommentInput!, activityId: String!, commentId: String!): Boolean!
  removeStreamPost(activityId: String, isRepost: Boolean!, organisationId: String!, postId: String!): Boolean!
  createStreamCommentPost(
    postId: String!
    organisationId: String!
    postAuthorId: String!
    commentData: CreateStreamCommentInput!
    activityId: String!
  ): CommentMessageResponse!
  createWebinar(webinarData: CreateWebinarInput!): Webinar!
  updateWebinar(webinarData: UpdateWebinarInput!, webinarId: String!): Webinar!
  removeWebinar(id: String!): Boolean!
  registerToWebinar(webinarId: String!): WebinarParticipant!
  unregisterToWebinar(webinarId: String!): WebinarParticipant!
  createBroadcastStream: Boolean!
  startBroadcast(webinarConferenceId: String!, webinarId: String!): Boolean!
  stopBroadcast(webinarConferenceId: String!, webinarId: String!): Boolean!
  streamCreateOrganisation(organisationData: CreateStreamOrganisationInput!): OrganisationMessageResponse!
  gettingStartedStepsMigration: Boolean!
  addOrganisationToDestinationPage(destinationPageOrgData: CreateDestinationPageOrgInput!): DestinationPageResponse!
  addCategoryToDestinationPage(categoryData: CreateDestinationPageCategoryInput!): DestinationPageResponse!
  createDestinationPage(destinationPageData: CreateDestinationPageInput!): DestinationPageResponse!
  updateDestinationPage(destinationPageData: UpdateDestinationPageInput!): DestinationPageResponse!
  updateDestinationPageCategory(name: String!, id: String!, destinationPageId: String!): DestinationPageResponse!
  deleteDestinationPageCategory(id: String!, destinationPageId: String!): DestinationPageResponse!
  deleteDestinationPageOrganisation(
    id: String!
    categoryId: String!
    destinationPageId: String!
  ): DestinationPageResponse!
  updateDestinationPageCategoryOrder(
    destinationPageCategoryOrderData: UpdateDestinationPageCategoryOrderInput!
  ): DestinationPageResponse!
  removeDestinationPage(id: String!): DeleteDestinationPageResponse!
  removeSubscription(partnershipRequestId: String!): Boolean!
  updateAutofollowRegionOrgs(data: UpdateAutofollowOrgInput!): AutofollowRegionsItem!
  runAchievementMigrations(data: RunAchievementMigrationsInput!): Boolean!
  streamCreateEventInvitations(eventInvitationData: StreamCreateEventInvitationsInput!): StreamEventMessageResponse!
  streamCreateIncentive(incentiveData: StreamCreateIncentiveInput!): IncentiveMessageResponse!
  streamRemoveIncentive(id: String!): Boolean!
  streamUpdateIncentive(incentiveData: StreamUpdateIncentiveInput!, incentiveId: String!): IncentiveMessageResponse!
  streamInviteToWebinar(webinarParticipantsData: CreateWebinarParticipantsInput!): webinarParticipantsMessageResponse!
  streamCreateWebinarParticipant(webinarParticipantData: StreamCreateWebinarParticipantInput!): Boolean!
  streamRemoveWebinarParticipant(id: String!): Boolean!
  streamCreateWebinar(webinarData: StreamCreateWebinarInput!): WebinarMessageResponse!
  streamRemoveWebinar(id: String!): Boolean!
  streamUpdateWebinar(webinarData: StreamUpdateWebinarInput!, webinarId: String!): Boolean!
  streamRegisterToWebinar(webinarId: String!): WebinarParticipant!
  streamUnregisterToWebinar(webinarParticipantId: String!, webinarId: String!): WebinarParticipant!
  createStreamUpdate(postData: CreateStreamUpdateInput!): UpdateMessageResponse!
  removeStreamUpdate(eventId: String!, type: String!, organisationId: String!, updateId: String!): Boolean!
  removeActivityFeeds(activityId: String!): Boolean!
  addPostImpressionToActivity(repostPostId: String!, postId: String!, postType: String!, activityId: String!): Boolean!
  addPostViewToActivity(repostPostId: String!, postId: String!, postType: String!, activityId: String!): Boolean!
  streamCreateEvent(eventData: StreamCreateEventInput!): EventMessageResponse!
  streamUpdateEvent(eventData: StreamUpdateEventInput!, eventId: String!): EventMessageResponse!
  streamRemoveEvent(eventId: String!): Boolean!
  streamRsvpEvent(status: StreamEventInvitationStatus = Attending, eventId: String!): RsvpEventMessageResponse!
  createTopic(topicData: CreateTopicDto!): Topics!
  updateTopic(topicData: UpdateTopicInput!, topicId: String!): Topics!
  removeTopic(topicId: String!): Boolean!
  chatWithAI(chatData: ChatDto!): ChatResult!
  rateDocsbotAnswer(rateAnswerData: RateAnswerDto!): Boolean!
}

type Notification {
  id: ID!
  type: NotificationType!
  image: String!
  isRead: Boolean!
  isSeen: Boolean!
  data: JSON
  createdAt: Date!
  profile: Profile!
  membership: Membership
  follower: Follower
  eventInvitation: EventInvitation
  organisation: Organisation
  partnershipRequest: PartnershipRequest
  incentiveParticipant: IncentiveParticipant
  webinarParticipant: WebinarParticipant
}

input NotificationsFilter {
  isRead: Boolean
}

type NotificationsResult {
  records: [Notification!]!
  totalCount: Int!
  totalUnreadCount: Int!
}

enum NotificationType {
  InvitationReceived
  InvitationAccepted
  PartnershipRequestReceived
  PartnershipRequestApproved
  PartnershipRequestApprovedByOrganisation
  MembershipAccepted
  OrganisationOwnershipRequested
  OrganisationOwnershipAccepted
  MembershipRequested
  FollowerAccepted
  MembershipPermissionsUpdated
  EventInvitationByGuest
  EventInvitationByHosts
  EventInvitationApproved
  NewEventUpdate
  EventLocationChanged
  EventDateTimeChanged
  IncentiveDateChanged
  IncentiveInvitationByParticipant
  IncentiveInvitationByHosts
  IncentiveRegistrationRequested
  IncentiveRegistrationApproved
  NewIncentiveUpdate
  WebinarDateChanged
  WebinarInvitationByParticipant
  WebinarInvitationByHosts
  WebinarParticipantAddedAsHostAdmin
  WebinarParticipantAddedAsHost
  WebinarParticipantAddedAsSpeaker
  WebinarRegistrationRequested
  WebinarRegistrationApproved
  NewWebinarUpdate
  NewFollower
  SuggestFollow
  PostComment
  CommentMention
  PostMention
  CommentReact
  PostReact
  PostShared
  OrgPostMention
  OrgCommentMention
  RecurringPaymentReminder
  MembershipAutoApprove
  ParentMembershipRequested
  ParentMembershipAccepted
  ParentMembershipDeclined
  OrgPostReminder7Days
  OrgPostReminder14Days
  InactiveUserReminder
  InboxMessage
  DailyLoginStreak
  NewTier
  WeeklySummary
  NewAchievement
}

enum OnlineStatus {
  Online
  Offline
  Away
}

type Organisation {
  id: ID!
  name: String!
  image: String
  backgroundImage: String
  location: JSON
  type: OrganisationType
  status: OrganisationStatus
  privacy: String
  followingPrivacy: String
  peoplePrivacy: String
  additionalPrivacy: String
  website: String
  resourceUrl: String
  resources: String
  description: String
  size: OrganisationSize
  vanityId: String
  stripeConnectAccount: String
  isConnectOnboarded: Boolean
  preApprovedDomains: JSON
  parentOrganisations: [ParentOrganisationObj!]
  connectedOrganisations: [String!]
  createdAt: Date!
  updatedAt: Date!
  isPublic: Boolean
  pages: [OrganisationPage!]!
  parentOrganisationDetails: [ParentOrganisationDetailsData!]
  memberships(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    filter: MembershipsFilter
  ): MembershipsResult!
  incentives(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    filter: IncentivesFilter
  ): IncentivesResult!
  followers(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    filter: FollowersFilter
  ): FollowersResult!
  partnerships(isActive: Boolean): [Partnership!]!
  receivedPartnershipRequests: [PartnershipRequest!]!
  sentPartnershipRequests: [PartnershipRequest!]!
  followerStatus: FollowerStatus
  isMember: Boolean
  permissions: [MembershipPermission!]
  isOwner: Boolean
  activeMembership: Membership
  isAdmin: Boolean
  isManager: Boolean
  isEditor: Boolean
  followersActiveCount: Int
  followersPendingCount: Int
  followersRejectedCount: Int
  webinarsCount: Int
  eventsCount: Int
  isDMOSubscription: Boolean
  DMOMaxPartners: Int
}

type OrganisationDetailData {
  id: String!
  name: String!
  image: String
  vanityId: String
}

type OrganisationMessageResponse {
  success: Boolean!
}

enum OrganisationPage {
  Home
  About
  Webinars
  Events
  Resources
  People
  Analytics
}

input OrganisationPrivacyAccessSettings {
  public: Boolean
  approvedFollowers: Boolean
}

input OrganisationPrivacySettings {
  postsAccess: OrganisationPrivacyAccessSettings
  eventsAccess: OrganisationPrivacyAccessSettings
  webinarsAccess: OrganisationPrivacyAccessSettings
  incentivesAccess: OrganisationPrivacyAccessSettings
  trainingsAccess: OrganisationPrivacyAccessSettings
}

input OrganisationsFilter {
  id: String
  type: [OrganisationType!] = []
  searchText: String
  vanityId: [String!]
  isFuzzySearch: Boolean = false
}

enum OrganisationSize {
  OrgSize1
  OrgSize2_10
  OrgSize11_50
  OrgSize51_200
  OrgSize201_500
  OrgSize501_1000
  OrgSize1001_5000
  OrgSize5001
}

type OrganisationsResult {
  records: [Organisation!]!
  totalCount: Int!
}

enum OrganisationStatus {
  Active
  Pending
  Removed
  Suspended
}

enum OrganisationType {
  Destination
  PrivateSector
  RepresentationAgency
  TourOperator
  TravelAgency
  Community
  Association
  Consortia
}

enum PaginationSortOrder {
  Ascending
  Descending
}

type ParentOrganisationDetailsData {
  id: String!
  name: String!
  image: String
  vanityId: String
}

input ParentOrganisationInput {
  organisationId: String!
  parentId: String!
}

type ParentOrganisationObj {
  id: String!
}

type Partnership {
  id: ID!
  organisation: Organisation!
  partnershipRequestData: PartnershipRequest
  createdAt: Date!
  updatedAt: Date!
}

type PartnershipRequest {
  id: ID!
  status: PartnershipRequestStatus!
  subscriptionData: JSON
  createdAt: Date!
  updatedAt: Date!
  price: String
  duration: String
  currency: String
  isSubscription: Boolean
  senderOrganisation: Organisation
  receiverOrganisation: Organisation
}

enum PartnershipRequestStatus {
  Pending
  Approved
  Declined
  Disconnected
}

type PaymentTransaction {
  id: ID!
  type: TransactionType!
  stripeCheckoutId: String
  stripeInvoiceId: String
  startDate: Date
  endDate: Date
  paymentStatus: String
  transactionStatus: TransactionStatus!
  createdAt: Date!
  updatedAt: Date!
}

type Post {
  id: ID!
  type: PostType!
  image: String
  mediaWidth: Float
  mediaHeight: Float
  mediaFormat: String
  multipleImages: [MultipleImageDetails!]
  imageCategory: PostImageCategory
  video: String
  document: String
  text: String!
  profile: Profile!
  organisation: Organisation!
  seenBy: [String!]
  totalViews: Int!
  status: PostStatus
  scheduledAt: Date
  createdAt: Date!
  views: Int!
  postAudience: JSON
  event: Event
  incentive: Incentive
  webinar: Webinar
  isSeen: Boolean
}

enum PostImageCategory {
  Single
  Multiple
}

type PostMessageResponse {
  success: Boolean!
  activityId: String
}

type PostMessageResponse2 {
  success: Boolean!
}

input PostsFilter {
  type: [PostType!] = []
  organisationId: String
  eventId: String
  incentiveId: String
  webinarId: String
  updates: Boolean
  searchText: String
}

type PostsResult {
  records: [Post!]!
  totalCount: Int!
}

enum PostStatus {
  Scheduled
  Live
}

enum PostStreamType {
  Normal
  Image
  Webinar
  Event
  Incentive
  Video
  Mixed
  Document
}

enum PostType {
  Normal
  Image
  Webinar
  Event
  Incentive
  Video
  Mixed
  Document
}

type PreRecordedResponse {
  id: String!
  assetId: String!
}

type Profile {
  id: ID!
  name: String!
  email: String!
  image: String
  backgroundImage: String
  dateOfBirth: Date
  gender: String
  bio: String
  headline: String
  location: JSON
  regions: [Region!]
  responsibilities: [ProfileResponsibility!]
  typesOfHoliday: [ProfileTypeOfHoliday!]
  phoneNumber: String
  lastOnlineAt: Date!
  timezone: String
  onboarding: JSON
  isComplete: Boolean!
  onlineStatus: OnlineStatus!
  parentOrganisations: [ProfileParentOrganisationObj!]
  noOfMobileDevicesLoggedIn: Float
  notificationPreference: JSON
  createdAt: Date!
  updatedAt: Date!
  isEmailVerified: Boolean
  receiveNotificationEmails: Boolean!
  receiveNewMessagesEmails: Boolean!
  status: ProfileConnectionStatus
  sellHolidays: Boolean
  showGettingStartedFeedAt: Date
  migrationFlags: JSON
  connectionsCount: Float!
  connections: [Connection!]!
  connectionsViaPagination(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    filter: ConnectionsFilter
  ): ConnectionsResult!
  experiences: [Experience!]!
  memberships(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    filter: MembershipsFilter
  ): [Membership!]!
  activeMemberships(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    filter: MembershipsFilter
  ): [Membership!]!
  nonPrimaryMemberships(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    filter: MembershipsFilter
  ): [Membership!]!
  following(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    filter: FollowersFilter
  ): FollowersResult!
  streamChannelId: String
  primaryMembership: Membership
}

enum ProfileConnectionStatus {
  Connection
  InvitationSent
  InvitationReceived
  InvitationSentRejected
  InvitationReceivedRejected
  None
}

input ProfileParentOrganisationInput {
  profileId: String!
  parentId: String!
}

type ProfileParentOrganisationObj {
  id: String!
}

enum ProfileResponsibility {
  Events
  Marketing
  Partnerships
  ProductAndContracting
  PublicRelations
  Sales
  SalesAgent
  SalesManager
  SeniorManagement
  SocialAndDigital
  Training
  Other
}

input ProfilesFilter {
  organisationId: String
  followerStatus: [FollowerStatus!]
  membershipStatus: [MembershipStatus!]
  membershipPermissions: [MembershipPermission!]
  status: [ProfileConnectionStatus!] = []
  searchText: String
  includeOwnProfile: Boolean
  searchLocationText: String
  responsibilities: [ProfileResponsibility!]
  typesOfHoliday: [ProfileResponsibility!]
  connectedOrgProfiles: Boolean
  searchOrgNameAndJob: Boolean
  isFuzzySearch: Boolean = false
}

type ProfilesResult {
  records: [Profile!]!
  totalCount: Int!
}

enum ProfileTypeOfHoliday {
  Business
  CityBreaks
  Cruises
  Educational
  EscortedTours
  Expedition
  FlyDrive
  Groups
  Leisure
  Luxury
  Niche
  PackageHolidays
  Rail
  RoundTheWorld
  Solo
}

type ProfileWithConnectionsOnly {
  id: ID!
  connections: [Connection!]!
  connectionsViaPagination(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    filter: ConnectionsFilter
  ): ConnectionsResult!
}

type Query {
  profile(id: String): Profile!
  profiles(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    filter: ProfilesFilter
  ): ProfilesResult!
  profileConnectionsOnly(id: String): ProfileWithConnectionsOnly!
  notification(id: String!): Notification!
  notifications(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    filter: NotificationsFilter
  ): NotificationsResult!
  unSeenNotifications: Int!
  organisation(vanityId: String, id: String): Organisation!
  organisations(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    filter: OrganisationsFilter
  ): OrganisationsResult!
  getHomePageProfileRewardsData: HomePageProfileRewardsDataResponse!
  post(id: String!): Post!
  posts(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    filter: PostsFilter
  ): PostsResult!
  scheduledPostList(organisationId: String!): PostsResult!
  getChatAuthToken: ChatAuthTokenResponse!
  zoomMeetingData: ZoomMeetingResponse!
  eventInvitation(id: String!): EventInvitation!
  eventShareCount(eventId: String!): Float!
  event(id: String!): Event!
  calendarEvents: [Event!]!
  events(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    filter: EventsFilter
  ): EventsResult!
  incentive(id: String!): Incentive!
  incentives(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    filter: IncentivesFilter
  ): IncentivesResult!
  incentiveParticipant(id: String!): IncentiveParticipant!
  incentiveShareCount(incentiveId: String!): Float!
  incentiveBooking(id: String!): IncentiveBooking!
  incentiveBookings(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    filter: IncentiveBookingsFilter
  ): IncentiveBookingsResult!
  webinarParticipant(id: String!): WebinarParticipant!
  webinarShareCount(webinarId: String!): Float!
  followConnectedOrgsMissedMigration: PostMessageResponse!
  webinar(id: String!): Webinar!
  webinars(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    filter: WebinarsFilter
  ): WebinarsResult!
  webinarsAndKeywords(organisationId: String!): WebinarsAndKeywordsResponse!
  getConferenceAuthToken: ConferenceAuthTokenResponse!
  getDirectUpload: DirectUploadResponse!
  getPlaybackId(UploadId: String!): PreRecordedResponse!
  getGettingStartedSteps: [GettingStartedStepResponse!]!
  destinationPages(id: String): [DestinationPage!]!
  searchDestinationPages(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    filter: DestinationPagesFilter
  ): DestinationPagesResult!
  destinationPagesTree: [DestinationPageTree!]!
  destinationPage(vanityId: String, id: String): DestinationPageResponse!
  getLoyaltyPointsList(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    startDate: Date!
    endDate: Date!
  ): LoyaltyPointsResult!
  getVariableRewardPoints: VariableRewardPointsResult!
  autofollowRegionsOrgs: [AutofollowRegionsItem!]!
  getAchievements: [Achievement!]!
  signature(public_id: String, eager: String): CloudinarySignatureResponse!
  analytics(startDate: Date!, endDate: Date!, organisationId: String!, webinarId: String): AnalyticsResponse!
  addMissingStreamLikesActivities: PostMessageResponse2!
  getFeedsAuthToken: FeedsAuthTokenResponse!
  getFeedsAuthTokenNoAuth(userId: String!): FeedsAuthTokenResponse!
  getFeedsOrgToken(organisationId: String!): FeedsOrgTokenResponse!
  autocompleteMentions(isFuzzySearch: Boolean!, searchText: String!): MentionsItem!
  topics(
    first: Int
    after: String
    offset: Int
    sortBy: String
    sortOrder: PaginationSortOrder
    filter: TopicsFilter
  ): TopicsResult!
  getSuggestedQuestions(docsbotId: String!): SuggestedQuestionsResult!
}

input RateAnswerDto {
  docsbotId: String!
  answerId: String!
  rating: Float!
}

enum Region {
  UnitedStatesOfAmerica
  Africa
  Asia
  Canada
  TheCaribbean
  CentralAmerica
  Europe
  Mexico
  MiddleEast
  Oceania
  SouthAmerica
  All
}

type RsvpEventMessageResponse {
  result: Boolean!
}

input RunAchievementMigrationsInput {
  migrations: [String!]!
}

type StartCallResponse {
  channelId: String!
}

input StreamCreateEventInput {
  name: String!
  image: String
  location: JSON
  isOnline: Boolean
  url: String
  description: String!
  isAllDay: Boolean
  startDate: Date!
  endDate: Date!
  isPublic: Boolean
  isHostsManagedRSVP: Boolean
  isGuestsCanInvite: Boolean
  isGuestListVisible: Boolean
  organisationId: ID!
  type: StreamEventType!
  eventId: ID!
}

input StreamCreateEventInvitationsInput {
  eventId: ID!
  profileIds: [ID!] = []
}

input StreamCreateIncentiveInput {
  name: String!
  image: String
  description: String!
  startDate: Date!
  endDate: Date!
  terms: String
  incentiveId: String!
  type: StreamIncentiveType!
  regions: [Region!]!
  bookingTypes: [StreamIncentiveBookingType!]!
  bookingFields: [StreamIncentiveBookingFieldInput!]!
  isPublic: Boolean
  isParticipantListVisible: Boolean
  organisationId: ID!
}

input StreamCreateWebinarInput {
  name: String!
  image: String
  description: String!
  startDate: Date
  endDate: Date
  keywords: [String!]
  isParticipantListVisible: Boolean
  webinarId: String!
  type: StreamWebinarType!
  isPublic: Boolean
  isParticipantsCanInvite: Boolean
  isInternal: Boolean
  organisationId: ID!
  liveStreamRecordingId: String
  duration: Float
}

input StreamCreateWebinarParticipantInput {
  webinarId: ID!
  profileId: ID!
  role: String!
  organisationId: ID!
}

enum StreamEventInvitationStatus {
  Attending
  Interested
  Declined
  InvitedByGuest
  InvitedByHost
  InvitedAttending
  InvitedInterested
  InvitedDeclined
  Blocked
}

type StreamEventMessageResponse {
  success: Boolean!
}

enum StreamEventType {
  Event
  IncentiveStart
  IncentiveEnd
  Webinar
}

input StreamIncentiveBookingFieldInput {
  name: String!
  type: StreamIncentiveBookingFieldType = String
  isOptional: Boolean = false
}

enum StreamIncentiveBookingFieldType {
  String
}

enum StreamIncentiveBookingType {
  Flight
  Hotel
  Cruise
  Other
}

enum StreamIncentiveType {
  CashbackOrVoucher
  Prize
  FAMTrip
}

input StreamOrganisationPrivacyAccessSettings {
  public: Boolean
  approvedFollowers: Boolean
}

input StreamOrganisationPrivacySettings {
  postsAccess: StreamOrganisationPrivacyAccessSettings
  eventsAccess: StreamOrganisationPrivacyAccessSettings
  webinarsAccess: StreamOrganisationPrivacyAccessSettings
  incentivesAccess: StreamOrganisationPrivacyAccessSettings
  trainingsAccess: StreamOrganisationPrivacyAccessSettings
}

enum StreamOrganisationSize {
  OrgSize1
  OrgSize2_10
  OrgSize11_50
  OrgSize51_200
  OrgSize201_500
  OrgSize501_1000
  OrgSize1001_5000
  OrgSize5001
}

enum StreamOrganisationStatus {
  Active
  Pending
  Removed
  Suspended
}

enum StreamOrganisationType {
  Destination
  PrivateSector
  RepresentationAgency
  TourOperator
  TravelAgency
  Community
}

input StreamUpdateEventInput {
  name: String
  image: String
  location: JSON
  isOnline: Boolean
  url: String
  description: String
  isAllDay: Boolean
  startDate: Date
  endDate: Date
  isPublic: Boolean
  isHostsManagedRSVP: Boolean
  isGuestsCanInvite: Boolean
  isGuestListVisible: Boolean
  organisationId: ID
}

input StreamUpdateIncentiveInput {
  name: String
  image: String
  description: String
  startDate: Date
  endDate: Date
  terms: String
}

input StreamUpdateWebinarInput {
  name: String
  image: String
  description: String
  startDate: Date
  endDate: Date
  keywords: [String!]
  isParticipantListVisible: Boolean
}

enum StreamWebinarType {
  PreRecorded
  LiveStream
}

type Subscription {
  newNotification: Notification!
  zoomMeetingEnded(meetingId: String!): ZoomMeetingEndedResponse!
}

type SuggestedQuestionsResult {
  response: [String!]
}

type Topics {
  id: ID!
  fullName: String!
  shortName: String!
  docsbotId: String!
  linkedOrganisations: [String!]!
  isPublic: Boolean!
  createdAt: Date!
  updatedAt: Date!
  organisationDetails: [OrganisationDetailData!]
}

input TopicsFilter {
  isPublic: Boolean = null
  searchText: String
}

type TopicsResult {
  records: [Topics!]!
  totalCount: Int!
}

enum TransactionStatus {
  InProgress
  Completed
  Failed
}

enum TransactionType {
  Checkout
  Recurring
}

type UpdateAsModeratorsResponse {
  channelId: String!
}

input UpdateAutofollowOrgInput {
  region: String!
  organisationIds: [String!]!
}

input UpdateDestinationPageCategoryOrderInput {
  items: [DestinationPageCategory!]!
  destinationPageId: String!
}

input UpdateDestinationPageInput {
  id: String!
  name: String
  vanityId: String
  path: [String!]
  hostOrganisationIds: [String!]
  image: String
  status: String
}

input UpdateEventInput {
  name: String
  image: String
  location: JSON
  isOnline: Boolean
  url: String
  description: String
  isAllDay: Boolean
  startDate: Date
  endDate: Date
  isPublic: Boolean
  isHostsManagedRSVP: Boolean
  isGuestsCanInvite: Boolean
  isGuestListVisible: Boolean
  organisationId: ID
}

input UpdateExperienceInput {
  position: String
  description: String
  location: JSON
  startDate: Date
  endDate: Date
  organisationId: ID
  organisationName: String
}

input UpdateIncentiveBookingInput {
  dataArray: [IncentiveBookingDataItemInput!]
}

input UpdateIncentiveInput {
  name: String
  image: String
  description: String
  startDate: Date
  endDate: Date
  terms: String
}

type UpdateMessageResponse {
  success: Boolean!
}

input UpdateOrganisationInput {
  name: String
  type: OrganisationType
  image: String
  backgroundImage: String
  location: JSON
  order: JSON
  destinationCategory: JSON
  privacy: String
  followingPrivacy: String
  peoplePrivacy: String
  additionalPrivacy: String
  website: String
  resourceUrl: String
  resources: String
  description: String
  size: OrganisationSize
  vanityId: String
  isPublic: Boolean
  privacySettings: OrganisationPrivacySettings
  status: OrganisationStatus
}

input UpdatePostInput {
  type: PostType
  image: String
  mediaWidth: Float
  mediaHeight: Float
  mediaFormat: String
  multipleImages: [ImageDetailsInput!]
  imageCategory: PostImageCategory
  video: String
  document: String
  text: String
  status: PostStatus
  scheduledAt: Date
}

input UpdatePostScheduleDateInput {
  scheduledAt: Date!
}

input UpdateProfileInput {
  name: String
  image: String
  backgroundImage: String
  dateOfBirth: Date
  gender: String
  bio: String
  headline: String
  regions: [Region!]
  responsibilities: [ProfileResponsibility!]
  location: JSON
  typesOfHoliday: [ProfileTypeOfHoliday!]
  phoneNumber: String
  timezone: String
  onboarding: JSON
  receiveNotificationEmails: Boolean
  receiveNewMessagesEmails: Boolean
  useAutofollow: Boolean
  notificationPreference: JSON
  sellHolidays: Boolean
  showGettingStartedFeedAt: Date
}

input UpdateTopicInput {
  fullName: String
  shortName: String
  docsbotId: String
  linkedOrganisations: [String!]
  isPublic: Boolean
}

input UpdateWebinarInput {
  name: String
  image: String
  description: String
  startDate: Date
  endDate: Date
  keywords: [String!]
  isParticipantListVisible: Boolean
}

type VariableRewardPointsResult {
  points: Int!
}

type Webinar {
  id: ID!
  name: String!
  type: WebinarType!
  image: String
  description: String!
  startDate: Date
  endDate: Date
  keywords: [String!]!
  isPublic: Boolean!
  isParticipantsCanInvite: Boolean!
  isParticipantListVisible: Boolean!
  isInternal: Boolean!
  isEnded: Boolean!
  createdAt: Date!
  updatedAt: Date!
  liveStreamId: String
  liveStreamPlaybackId: String
  liveStreamStartedAt: Date
  liveStreamStoppedAt: Date
  liveStreamRecordingId: String
  chatChannelId: String
  assetId: String
  organisation: Organisation!
  participant: WebinarParticipant
  participants(filter: WebinarParticipantsFilter): [WebinarParticipant!]!
}

enum WebinarInvitationStatus {
  Pending
  Sent
}

type WebinarMessageResponse {
  organisationId: String!
  vanityId: String!
  id: String!
}

type WebinarParticipant {
  id: ID!
  status: WebinarParticipantStatus
  invitationStatus: WebinarInvitationStatus
  createdAt: Date!
  updatedAt: Date!
  webinar: Webinar!
  organisation: Organisation
  profile: Profile!
  inviterProfile: Profile
}

input WebinarParticipantsFilter {
  profileId: ID
  status: [WebinarParticipantStatus!] = []
}

type webinarParticipantsMessageResponse {
  webinarParticipants: Boolean!
}

enum WebinarParticipantStatus {
  Host
  HostAdmin
  Speaker
  HiddenHost
  Registered
  InvitedByHost
  InvitedByParticipant
  InvitedRegistered
  Blocked
}

type WebinarsAndKeywordsResponse {
  keywords: [String!]!
  webinars: [Webinar!]!
}

input WebinarsFilter {
  webinarParticipantStatus: [WebinarParticipantStatus!] = []
  organisationId: String
  type: [WebinarType!] = []
  isPublic: Boolean = null
  searchText: String
  isEnded: Boolean = null
}

type WebinarsResult {
  records: [Webinar!]!
  totalCount: Int!
}

enum WebinarType {
  PreRecorded
  LiveStream
}

type ZoomMeetingEndedResponse {
  zoomMeetingId: String!
}

type ZoomMeetingExpireMessageResponse {
  success: Boolean!
}

type ZoomMeetingResponse {
  startUrl: String!
  joinUrl: String!
  meetingId: String!
}
