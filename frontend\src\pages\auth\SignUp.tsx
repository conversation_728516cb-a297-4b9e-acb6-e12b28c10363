import React, { useEffect } from 'react';
import { RouteComponentProps, useLocation } from 'react-router-dom';
import { Loader } from '@components/Loader';
import { referringOrganisation } from '@utils/referringOrganisation';
import { ContainerCentered } from '@components/Layout/Container';
import qs from 'query-string';
import { useAuth } from '@src/Auth0';

export default function SignUp(props: RouteComponentProps) {
  const { login } = useAuth();
  const refVanityId = referringOrganisation();

  useEffect(() => {
    login('signup');
  }, []);

  let { search } = useLocation();
  const { ref, memref } = qs.parse(search);
  if (ref) localStorage.setItem('organisationId', JSON.parse(JSON.stringify(ref)));
  if (memref) localStorage.setItem('MembershipOrganisationId', JSON.parse(JSON.stringify(memref)));

  return (
    <ContainerCentered>
      <Loader useGIF={true} allowLoadingText={true} customLoadText={'Stuck at Signup, refresh'} />
    </ContainerCentered>
  );
}
