import { useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';
import { GET_INNER_ORG_ACHIEVEMENTS_LIST, INNER_ORG_ACHIEVEMENT, INNER_ORG_ACHIEVEMENT_PROPS } from './queries';
import styled from 'styled-components';
import { Loader } from '@src/components/Loader';
import { ContainerCentered } from '@src/components/Layout/Container';
import Icon, { Icons } from '@src/components/icons/Icon';
import { Divider, Progress, Tag, Typography } from 'antd';
import { Avatar } from '@src/components/Image/Image';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { encodePathParams } from '@src/utils/encodePathParams';
import { Routes } from '@src/routes/Routes';
import TierIcon from '@src/components/TierIcon';

type TypeIconsProps = {
  [key: string]: JSX.Element;
};

const achievementTypeIcons: TypeIconsProps = {
  PowerScroller: <Icon icon={Icons.powerScrollerBadge} size={window.innerWidth < 568 ? 60 : 85} />,
  AvidViewer: <Icon icon={Icons.avidViewerBadge} size={window.innerWidth < 568 ? 60 : 85} />,
  IncentiveWarrior: <Icon icon={Icons.incentiveWarriorBadge} size={window.innerWidth < 568 ? 60 : 85} />,
  Ambassador: <Icon icon={Icons.ambassadorBadge} size={window.innerWidth < 568 ? 60 : 85} />,
  HighFive: <Icon icon={Icons.highfiveBadge} style={{ marginTop: '5px' }} size={window.innerWidth < 568 ? 60 : 85} />,
};

const OrganisationAchievementModalContent = ({
  id,
  name,
  image,
  vanityId,
  hasClubHabloSubscription,
  activeTier,
}: {
  id: string | undefined;
  name: string | undefined;
  image: string | undefined;
  vanityId: string | undefined;
  hasClubHabloSubscription: boolean | undefined;
  activeTier: string | undefined;
}) => {
  const { t } = useTranslation();
  const { data, loading } = useQuery<INNER_ORG_ACHIEVEMENT_PROPS>(GET_INNER_ORG_ACHIEVEMENTS_LIST);
  const [achievements, setAchievements] = useState<INNER_ORG_ACHIEVEMENT[]>([]);

  useEffect(() => {
    if (data) {
      const filtered = data.getOrgAchievements.filter(
        (achievement) => achievement.organisation && achievement.organisation.id === id,
      );
      setAchievements(filtered);
    }
  }, [data]);

  if (loading || !data) {
    return (
      <ContainerCentered>
        <Loader />
      </ContainerCentered>
    );
  }

  const renderIcon = (type: string, level: number | null, isAchieved: boolean) => {
    return (
      <>
        {type === 'Ambassador' ? (
          <div className="achievement-icon">
            {image && <Avatar className="achievement-org-image" id={image} width={130} border={true} />}
            <Icon className="achievement-frame" icon={Icons.orgAchievementsFrame} size={95} />
          </div>
        ) : (
          achievementTypeIcons[type]
        )}
        {/* Level 0 won't be displayed as isAchieved will be false */}
        {isAchieved && level && level > 0 ? (
          <StyledLevel className={`achievement-level achievement-level--${type}`} level={level}>
            {t('LEVEL')} {level}
          </StyledLevel>
        ) : null}
      </>
    );
  };

  const modalContent = (achievement: INNER_ORG_ACHIEVEMENT | undefined) => {
    if (!achievement) return null;

    const { type, number, steps, stepsComplete, level, isAchieved } = achievement;
    const progress = stepsComplete === 0 ? 0 : (stepsComplete / steps) * 100;

    const isAllOtherAchieved = achievements.filter(
      (a) => a.type !== 'Ambassador' && a.type !== 'HighFive' && a.isAchieved,
    ).length;
    const ambassadorProgress =
      isAllOtherAchieved === 0 ? 0 : (isAllOtherAchieved / (Object.keys(achievementTypeIcons).length - 2)) * 100;

    const getStrokeColor = (level: number) => {
      if (!level) return '#CECECE';
      if (level === 1) return 'var(--color-accent)';
      if (level === 2) return 'var(--color-level-2)';
      if (level === 3) return 'var(--color-level-3)';
      if (level === 4) return 'var(--color-level-4)';
      if (level >= 12) return 'black';
      return 'var(--color-level-inbetween)';
    };

    return (
      <>
        {type === 'HighFive' && <Divider style={{ borderColor: '#c6d7e0' }} />}

        <AchievementCard completed={isAchieved} level={level}>
          <div className="achievement-icon-modal">{renderIcon(type, level, isAchieved)}</div>

          <div style={{ width: '100%' }}>
            <h3 className="achievement-t">
              {t(`${type}`)}{' '}
              {(type === 'Ambassador' || type === 'PowerScroller') && <StyledTag>{t('EARN MONTHLY')}</StyledTag>}
            </h3>
            <p style={{ paddingRight: type === 'Ambassador' && window.innerWidth < 568 ? '10px' : '' }}>
              {t(`${type}Description${isAchieved ? 'Complete' : 'Incomplete'}`, {
                number,
                organisationName: name,
              })}
            </p>
            {steps !== 0 && type !== 'Ambassador' && (
              <AchievementProgressWrapper>
                {type !== 'IncentiveWarrior' && (
                  <>
                    <AchievementProgress className="achievement-progress">
                      <Progress
                        percent={progress}
                        showInfo={false}
                        strokeColor={getStrokeColor(level)}
                        trailColor="#E3E3E3"
                      />
                      <span style={{ color: progress > 55 ? 'white' : 'inherit' }}>{progress.toFixed(0)} %</span>
                    </AchievementProgress>
                    <span>
                      {stepsComplete} / {steps}
                    </span>
                  </>
                )}
              </AchievementProgressWrapper>
            )}
            {type === 'Ambassador' && (
              <AchievementProgressWrapper
                style={{ paddingRight: type === 'Ambassador' && window.innerWidth < 568 ? '15px' : '' }}
              >
                <AchievementProgress className="achievement-progress">
                  <Progress
                    percent={ambassadorProgress}
                    showInfo={false}
                    strokeColor={getStrokeColor(level)}
                    trailColor="#E3E3E3"
                  />
                  <span style={{ color: ambassadorProgress > 55 ? 'white' : 'inherit' }}>
                    {ambassadorProgress.toFixed(0)} %
                  </span>
                </AchievementProgress>
                <span>
                  {/* excluded Ambassador and HighFive */}
                  {isAllOtherAchieved} / {Object.keys(achievementTypeIcons).length - 2}
                </span>
              </AchievementProgressWrapper>
            )}
          </div>
        </AchievementCard>
      </>
    );
  };

  return (
    <div>
      <Link to={encodePathParams(Routes.organisationProfile, { vanityId })} target="_blank" rel="noopener noreferrer">
        <OrganisationDetails>
          <Avatar id={image} width={72} border={true} style={{ border: '1px solid #e9eef1' }} />
          <Typography.Title level={3} ellipsis={true}>
            <span>{name}</span>
            {activeTier && hasClubHabloSubscription && (
              <TierIcon iconSize={20} position="top" tier={activeTier} spacing="2px" />
            )}
          </Typography.Title>
        </OrganisationDetails>
      </Link>
      <SubAchievementList>
        {Object.keys(achievementTypeIcons)
          .map((type) => {
            const achievement = achievements.find((a) => a.type === type);

            return {
              type,
              isAchieved: achievement?.isAchieved || false,
              level: achievement?.level || 0,
              steps: achievement?.steps || 0,
              stepsComplete: achievement?.stepsComplete || 0,
              number: achievement?.number || 0,
            };
          })
          .sort((a, b) => {
            if (a.type === 'HighFive') return 1;
            if (b.type === 'HighFive') return -1;
            return (b.isAchieved ? 1 : 0) - (a.isAchieved ? 1 : 0);
          })
          .map((achievement) => (
            <SubAchievement>{modalContent(achievement)}</SubAchievement>
          ))}
      </SubAchievementList>
    </div>
  );
};

export default OrganisationAchievementModalContent;

const SubAchievement = styled.div`
  margin-bottom: 20px;

  :last-child {
    margin-bottom: 0;
  }
`;

const SubAchievementList = styled.div`
  @media screen and (max-width: 1560px) {
    height: calc(100dvh - 250px);
    overflow-y: auto;
  }
  @media screen and (min-width: 1561px) {
    height: auto;
    max-height: none;
    overflow-y: visible;
  }
`;

const StyledTag = styled(Tag)`
  color: var(--color-accent);
  background-color: var(--color-accent90);
  font-weight: 800;
  font-size: 11px;
  padding: 0px 20px;
  border-color: var(--color-accent90);
`;

const OrganisationDetails = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  flex-direction: column;
  font-size: 16px;
`;

const AchievementCard = styled.div<{ completed: boolean; level?: number }>`
  display: flex;
  align-items: center;
  gap: 15px;
  border: 1px solid #c6d7e0;
  border-radius: 16px;
  padding: 15px 20px 20px 15px;

  @media screen and (max-width: 400px) {
    overflow-x: auto;
  }

  @media screen and (max-width: 568px) {
    gap: 10px;
    padding: 10px 15px 15px 10px;
  }

  .achievement-icon {
    position: relative;

    .achievement-frame {
      height: 85px;

      @media screen and (max-width: 568px) {
        width: 100%;
        height: 82px;
      }
    }

    ${(props) =>
      props.completed &&
      `
    &:hover {
      -webkit-mask-image: linear-gradient(45deg, #000 25%, rgba(0, 0, 0, 0.2) 50%, #000 75%);
      mask-image: linear-gradient(45deg, #000 25%, rgba(0, 0, 0, 0.2) 50%, #000 75%);
      -webkit-mask-size: 800%;
      mask-size: 800%;
      animation: shine 2.5s ease forwards;
    }`}

    @keyframes shine {
      0% {
        -webkit-mask-position: 0;
        mask-position: 0;
      }

      100% {
        -webkit-mask-position: 120%;
        mask-position: 120%;
      }
    }
  }

  .achievement-t {
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .achievement-icon-modal {
    text-align: center;
    min-width: 118px;
    filter: ${(props) => (!props.completed ? 'grayscale(100%)' : 'none')};
    opacity: ${(props) => (!props.completed ? '70%' : '100%')};

    & span {
      height: 78px;

      @media screen and (max-width: 568px) {
        height: 55px;
      }
    }

    @media screen and (max-width: 568px) {
      min-width: 90px;
    }
  }

  p {
    font-size: 14px;
    font-weight: 700;
    margin: 0;
  }

  .achievement-org-image {
    width: 44px;
    height: 44px;
    border-radius: 0;
    position: absolute;
    top: 18px;
    left: 50%;
    border: none;
    transform: translate(-52%, -3%);
    filter: ${(props) => (!props.completed ? 'grayscale(100%)' : 'none')};

    @media screen and (max-width: 568px) {
      transform: translate(-52%, 0%);
      width: 40px;
      height: 40px;
      top: 19px;
    }
  }
`;

const StyledLevel = styled.div<{ level: number }>`
  background: ${(props) =>
    props.level === 1
      ? 'var(--color-accent)'
      : props.level === 2
      ? 'var(--color-level-2)'
      : props.level === 3
      ? 'var(--color-level-3)'
      : props.level === 4
      ? 'var(--color-level-4)'
      : props.level >= 12
      ? 'var(--color-level-12)'
      : 'var(--color-level-inbetween)'};

  color: white;
  font-weight: 700;
  font-size: 14px;
  width: 100%;
  max-width: 100px;
  margin: 0 auto;
  text-align: center;
  display: inline-block;
  padding: 0px 10px;
  border-radius: 4px;

  &.achievement-level--Ambassador {
    margin-top: 5px;
  }

  @media screen and (max-width: 568px) {
    font-size: 12px;
  }
`;

const AchievementProgressWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 20px;
  margin-top: 10px;

  span {
    font-size: 13px;
    font-weight: 700;
  }
`;

const AchievementProgress = styled.div`
  flex: 1;
  position: relative;
`;
