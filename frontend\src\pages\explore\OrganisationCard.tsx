import React from 'react';
import { Modal, Skeleton, Typography } from 'antd';
import { Link } from 'react-router-dom';
import styled from 'styled-components';

import { Routes } from '@Routes';
import { ContainerCard } from '@components/Layout/Container';
import { GUTTER_MD_PX, GUTTER_SM_PX, PADDING_LG, PADDING_SM, BORDER_RADIUS_MOB_SML_PX } from '@theme';
import { ExploreOrganisation } from './queries';
import { Avatar } from '@components/Image/Image';
import { OrganisationFollowActions } from '../organisation/profile/components/OrganisationFollowActions';
import { encodePathParams } from '@utils/encodePathParams';
import { Linkable } from '@components/Buttons/Linkable';
import { IconButton } from '../inbox/VideoCall/IconButton';
import { Icons } from '@src/components/icons/Icon';
import { useTranslation } from 'react-i18next';
import { DestinationPage, REMOVE_ORG_FROM_DESTINATION_PAGE } from './queries';
import { useMutation } from '@apollo/client';
import { ComingSoon } from '@src/components/ComingSoon';
import { OrganisationsFilter } from '@src/graphql/GraphQLTypes';
import TierIcon from '@src/components/TierIcon';

type Props = {
  organisation: ExploreOrganisation;
  filter?: OrganisationsFilter;
  isEditing?: boolean;
  showFollowActions?: boolean;
  onClickLeftArrowMove?: () => void;
  onClickRightArrowMove?: () => void;
  destinationPageId?: string;
  categoryId?: string;
  updateDestination?: React.Dispatch<React.SetStateAction<DestinationPage | undefined>>;
};

export function OrganisationCard({
  organisation,
  showFollowActions = true,
  isEditing,
  onClickLeftArrowMove,
  onClickRightArrowMove,
  destinationPageId,
  updateDestination,
  categoryId,
  filter,
}: Props) {
  const { t } = useTranslation();
  const organisationPath = encodePathParams(Routes.organisationProfile, { vanityId: organisation.vanityId! });

  const [deleteDestinationPageOrganisation] = useMutation(REMOVE_ORG_FROM_DESTINATION_PAGE);

  const removeOrganisationHandler = () => {
    Modal.confirm({
      title: t('Remove Card'),
      content: t('Are you sure you want to remove this organisation card from the page?'),
      cancelText: t('Cancel'),
      okText: t('Remove'),
      okButtonProps: { danger: true },
      onOk: async () => {
        const res = await deleteDestinationPageOrganisation({
          variables: {
            destinationPageId: destinationPageId,
            categoryId: categoryId,
            id: organisation.id,
          },
        });
        res.data.deleteDestinationPageOrganisation &&
          updateDestination &&
          updateDestination(res.data.deleteDestinationPageOrganisation);
      },
    });
  };

  return (
    <Wrapper id={organisation.id} data-cy="explore-card">
      <Container>
        {isEditing && (
          <RemoveContainer>
            <RemoveIcon icon={Icons.close} onClick={removeOrganisationHandler} />
          </RemoveContainer>
        )}
        <Link to={organisationPath}>
          <Avatar id={organisation.image} width={64} height={64} />
        </Link>
        <OrganisationName>
          <Linkable to={organisationPath}>
            <Typography.Paragraph
              ellipsis={{
                rows: 2,
              }}
            >
              <span>
                {organisation.name && organisation.name.length > 40
                  ? organisation.name.slice(0, 40) + '...'
                  : organisation.name}
                {organisation.activeTier && organisation.hasClubHabloSubscription && (
                  <TierIcon position="top" tier={organisation.activeTier} iconSize={22} flatIcon />
                )}
              </span>
            </Typography.Paragraph>
          </Linkable>
        </OrganisationName>
        {showFollowActions && !isEditing && (
          <OrganisationFollowActions
            organisation={organisation}
            filter={filter}
            subtleFollow={true}
            showUnfollow={false}
          />
        )}
        {/* changing order of orgs */}
        {isEditing && (
          <MoveButtonsContainer>
            {onClickLeftArrowMove ? (
              <ComingSoon>
                <StyledIconButton icon={Icons.arrowBack} />
              </ComingSoon>
            ) : (
              <StyledIconButton
                icon={Icons.arrowBack}
                onClick={onClickLeftArrowMove}
                disabled={!onClickLeftArrowMove}
              />
            )}
            {onClickRightArrowMove ? (
              <ComingSoon>
                <StyledIconButton icon={Icons.arrowNext} />
              </ComingSoon>
            ) : (
              <StyledIconButton
                icon={Icons.arrowNext}
                onClick={onClickRightArrowMove}
                disabled={!onClickRightArrowMove}
              />
            )}
          </MoveButtonsContainer>
        )}
      </Container>
    </Wrapper>
  );
}

export function OrganisationCardLoading() {
  return (
    <Wrapper>
      <Container>
        <div>
          <Skeleton.Avatar size={64} shape="square" />
        </div>
        <OrganisationName>
          <span> </span>
        </OrganisationName>
        <div>
          <Skeleton.Button style={{ width: '107px' }} />
        </div>
      </Container>
    </Wrapper>
  );
}

const Wrapper = styled.div`
  padding: ${GUTTER_MD_PX};
`;

const Container = styled(ContainerCard)`
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 220px;
  max-width: 200px;
  padding: ${PADDING_LG} ${PADDING_SM};
  border-radius: ${BORDER_RADIUS_MOB_SML_PX};
`;

const OrganisationName = styled.div`
  flex: 1;
  font-weight: bold;
  text-align: center;
  padding: ${GUTTER_SM_PX} 0;
`;

const MoveButtonsContainer = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
`;

const StyledIconButton = styled(IconButton)<{ disabled?: boolean }>`
  border: ${({ disabled }) => (disabled ? '1px solid #F2F2F2 !important' : '1px solid #dcf2fe')};
  background: #fff !important;
  & svg {
    filter: ${({ disabled }) =>
      disabled
        ? 'invert(99%) sepia(0%) saturate(128%) hue-rotate(64deg) brightness(114%) contrast(90%)'
        : 'invert(75%) sepia(47%) saturate(304%) hue-rotate(152deg) brightness(96%) contrast(86%)'};
  }
`;

const RemoveContainer = styled.div`
  height: 20px;
  width: 100%;
  position: relative;
`;

const RemoveIcon = styled(IconButton)`
  position: absolute;
  z-index: 90;
  right: 0;
  top: -14px;
  border: 1px solid #dcf2fe;
  cursor: pointer;
`;
