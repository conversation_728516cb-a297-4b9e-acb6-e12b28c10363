import React, { useState } from 'react';
import { RouteComponentProps } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Button, Checkbox, Col, Row, Space, Typography } from 'antd';
import { useMutation, useQuery } from '@apollo/client';

import { BORDER_RADIUS, GUTTER_LG, GUTTER_MD_PX, GUTTER_SM, PADDING_LG } from '@theme';
import { HabloAdminSideNav } from '../HabloAdminSideNav';
import { ContainerCard } from '@components/Layout/Container';
import { REMOVE_ORGANISATION, RemoveOrganisationVariables, GetOrganisationData, GET_ORGANISATION } from './queries';
import Icon, { Icons } from '@components/icons/Icon';
import styled from 'styled-components';
import { OrganisationSearch } from 'components/OrganisationSearch/OrganisationSearch';
import { OrganisationPreview } from './OrganisationPreview';
import { useProfile } from '../../../routes/ProfileProvider';

type Props = RouteComponentProps;

export default function ManageOrganisations(props: Props) {
  const { profile } = useProfile();
  const superAdminProfiles = [
    //this check is in addition to isHabloStaff check
    '5f5b44fc4ac650006fc9505c', //Doug
    '111149247624107403444', //Russell
  ];
  const hasSuperAdminAccess = superAdminProfiles.indexOf(profile.id) > -1;

  const [understand1, setUnderstand1] = useState(false);
  const [understand2, setUnderstand2] = useState(false);
  const [organisationId, setOrganisationId] = useState<String>();
  const [organisationName, setOrganisationName] = useState<String>();

  const { t } = useTranslation();
  //const { error, loading, data } = useQuery<GetProfileData>(GET_PROFILE_DATA);

  const [removeOrganisation, { loading: removing, data: removed }] =
    useMutation<RemoveOrganisationVariables>(REMOVE_ORGANISATION);

  const { error, loading, data } = useQuery<GetOrganisationData>(GET_ORGANISATION, {
    variables: { organisationId },
  });

  const canRemove = understand1 && understand2;

  return (
    <Row gutter={GUTTER_LG}>
      <Col span={6}>
        <HabloAdminSideNav {...props} />
      </Col>
      <Col span={18}>
        <ContainerCard style={{ minHeight: '300px' }}>
          <Typography.Title level={3} style={{ marginBottom: GUTTER_MD_PX }}>
            {t('Manage an Organisation')}
          </Typography.Title>
          <Space size={GUTTER_LG} direction={'vertical'} style={{ width: '100%' }}>
            <div style={{ paddingBottom: '30px' }}>
              <Typography.Paragraph>
                <Typography.Text>
                  Use this page to find out details about an organisation and manage it. Look up an organisation to
                  begin...
                </Typography.Text>
              </Typography.Paragraph>
              <OrganisationSearch
                onChange={({ id, name }) => {
                  id && setOrganisationId(id);
                  name && setOrganisationName(name);
                }}
              />
            </div>
            {organisationId && <OrganisationPreview organisationId={organisationId} data-cy={'org-search'} />}
            {hasSuperAdminAccess && organisationId && (
              <>
                <DangerContainer>
                  <Typography.Title level={5}>REMOVE the '{organisationName}' Organisation from Hablo</Typography.Title>
                  <Space direction={'vertical'} size={GUTTER_SM} style={{ width: '100%' }}>
                    <Checkbox
                      disabled={!!removed || !!removing}
                      onChange={({ target }) => setUnderstand1(target.checked)}
                    >
                      I want to remove this organisation.
                    </Checkbox>
                    {understand1 && (
                      <Typography.Paragraph>
                        <UnorderedList>
                          <li>
                            This is a 'soft delete'. The page will not be accessible and it will not appear anywhere on
                            the site. The organisation name will become '(deleted organisation)' and the Custom URL
                            removed.
                          </li>
                          <li>Partnerships will be permanently removed.</li>
                          <li>
                            Memberships (roles and employement) will be deactivated. Employee's profiles will be updated
                            with the free-text organisation name. Associated notifications will be deleted.
                          </li>
                          <li>
                            Followings will be deactivated. Followers will not see the organisation in their Followed
                            Organisations.
                          </li>
                          <li>
                            Posts won't appear anywhere for anyone, due to followings/memberships being deactivated.
                          </li>
                          <li>
                            Videos, Events and Incentives will not be removed, but associated notifications will be
                            deleted. Current incentives will be ended. People will still be able to access these if they
                            kept the link, though the organisation will show as (deleted organisation).
                          </li>
                        </UnorderedList>
                      </Typography.Paragraph>
                    )}
                    <Checkbox
                      disabled={!understand1 || !!removed || !!removing}
                      onChange={({ target }) => setUnderstand2(target.checked)}
                    >
                      I understand the above and still wish to remove it.
                    </Checkbox>

                    <div style={{ display: 'flex', width: '100%', marginTop: GUTTER_MD_PX }}>
                      <ButtonContainer>
                        <Button
                          type={'primary'}
                          danger
                          disabled={!canRemove || !!removed || !!removing}
                          loading={removing}
                          onClick={() => removeOrganisation({ variables: { organisationId: organisationId } })}
                        >
                          {removing ? t('Removing') : removed ? t('Removed') : t('Remove Organisation')}
                        </Button>
                      </ButtonContainer>
                    </div>
                  </Space>
                </DangerContainer>
              </>
            )}
          </Space>
        </ContainerCard>
      </Col>
    </Row>
  );
}

const ButtonContainer = styled.div`
  display: flex;
  margin-left: auto;
  button {
    margin-left: 10px;
  }
`;

const DangerContainer = styled.div`
  padding: ${PADDING_LG};
  background-color: rgba(255, 0, 0, 0.05);
  border-radius: ${BORDER_RADIUS};
`;

const UnorderedList = styled.ul`
  list-style-type: disc !important;
  & > li {
    margin-bottom: ${GUTTER_MD_PX} !important;
  }
`;

type WarningProps = {
  children: React.ReactNode;
};

export function WarningInfo({ children }: WarningProps) {
  return (
    <Root>
      <Icon icon={Icons.warningOutlined} color="var(--color-text)" size={24} />
      <MessageContainer>{children}</MessageContainer>
    </Root>
  );
}

const Root = styled.div`
  display: flex;
  align-items: center;
  padding: ${PADDING_LG};
  background-color: rgba(var(--color-warning-rgb), 0.05);
  border-radius: ${BORDER_RADIUS};
`;

const MessageContainer = styled.div`
  margin-left: ${PADDING_LG};
`;
