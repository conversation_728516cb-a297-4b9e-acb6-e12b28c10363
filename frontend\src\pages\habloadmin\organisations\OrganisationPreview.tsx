import React, { useEffect, useState } from 'react';
import { Divider, List, Typography, Spin } from 'antd';
import { useQuery } from '@apollo/client';
import { useAuth } from '@src/Auth0';
import { getHttpApiUri } from '@utils/httpAPI';
// Assuming an httpAPI helper exists, if not, fetch would be used directly.
// import httpAPI from '@src/utils/httpAPI'; // Placeholder for actual path

import { PADDING_SM, GUTTER_MD_PX, GUTTER_LG_PX, PADDING_LG } from '@theme';
import { Loader } from '@components/Loader';
import { OrganisationHeader } from '@src/pages/organisation/profile/components/OrganisationHeader';
import { Membership, MembershipPermission, MembershipStatus, Organisation } from '@GraphQLTypes';
import { GetOrganisationData, GET_ORGANISATION } from './queries';
import styled from 'styled-components';
import dayjs from 'dayjs';
import { IconContainer } from '@components/icons/Icon';
import { ProfileList } from '@components/Profile/ProfileList';
import ParentOrganisationForm from './ParentOrganisationForm';

type Props = {
  organisationId: String;
};

type SelectOrganisationOption = { id: string; image: string; name: string };
// type SelectOrganisationOption = { id: string };

export function OrganisationPreview({ organisationId }: Props) {
  const { getToken } = useAuth();
  const [parentOrganisations, setParentOrganisations] = useState<SelectOrganisationOption[]>([]);

  const { error, loading, data } = useQuery<GetOrganisationData>(GET_ORGANISATION, {
    variables: { organisationId },
  });

  useEffect(() => {
    setParentOrganisations(data?.organisation?.parentOrganisationDetails ?? []);
  }, [data?.organisation?.parentOrganisationDetails]);

  if (error) {
    return <>error loading data from org id {organisationId}</>;
  }

  if (loading || !data) {
    return <Loader useGIF={true} />;
  }

  const organisation = data.organisation;

  const createdAt = dayjs(organisation.createdAt).format('DD/MM/YYYY HH:mm:ss');
  const updatedAt = dayjs(organisation.updatedAt).format('DD/MM/YYYY HH:mm:ss');

  // const members: Membership[] = [];

  // data.organisation.memberships.records.forEach((member) => {
  //   if ((member.status = MembershipStatus.Active)) {
  //     members.push(member);
  //   }
  // });

  return (
    <>
      <OrganisationHeader organisation={organisation as Required<Organisation>} />
      <Divider style={{ marginTop: '30px' }} />
      <Typography.Title level={3} style={{ marginBottom: GUTTER_MD_PX, marginTop: '30px' }}>
        Organisation Details
      </Typography.Title>
      <DataItem>
        <DataItemTitle>Organisation ID</DataItemTitle>
        <DataItemValue>{organisation.id}</DataItemValue>
      </DataItem>
      <DataItem>
        <DataItemTitle>Vanity URL</DataItemTitle>
        <DataItemValue>/{organisation.vanityId}</DataItemValue>
      </DataItem>
      <DataItem>
        <DataItemTitle>Created At</DataItemTitle>
        <DataItemValue>{createdAt}</DataItemValue>
      </DataItem>
      <DataItem>
        <DataItemTitle>Last Updated At</DataItemTitle>
        <DataItemValue>{updatedAt}</DataItemValue>
      </DataItem>
      <DataItem>
        <DataItemTitle>Website</DataItemTitle>
        <DataItemValue>{organisation.website}</DataItemValue>
      </DataItem>
      <DataItem>
        <DataItemTitle>Size</DataItemTitle>
        <DataItemValue>{organisation.size}</DataItemValue>
      </DataItem>
      <DataItem>
        <DataItemTitle>Active Followers</DataItemTitle>
        <DataItemValue>{organisation.followersActiveCount}</DataItemValue>
      </DataItem>
      <DataItem>
        <DataItemTitle>Pending Followers</DataItemTitle>
        <DataItemValue>{organisation.followersPendingCount}</DataItemValue>
      </DataItem>
      <DataItem>
        <DataItemTitle>Number of Videos</DataItemTitle>
        <DataItemValue>{organisation.webinarsCount}</DataItemValue>
      </DataItem>
      <DataItem>
        <DataItemTitle>Number of Events</DataItemTitle>
        <DataItemValue>{organisation.eventsCount}</DataItemValue>
      </DataItem>
      <DataItem>
        <DataItemTitle>Referred User Signups</DataItemTitle>
        <DataItemValue>{organisation.referralCount ?? 'N/A'}</DataItemValue>
      </DataItem>
      <Divider style={{ paddingTop: '30px' }} />
      <ParentOrganisationForm
        organisationId={organisationId}
        organisationName={organisation.name}
        parentOrganisations={parentOrganisations}
      />
      <Divider style={{ paddingTop: '30px' }} />
      <Typography.Title level={3}>User Roles and Staff</Typography.Title>
      <Members
        members={data.organisation.memberships.records}
        title={'Members'}
        permission={MembershipPermission.Editor}
        organisation={organisation}
      />
      <Divider style={{ paddingTop: '30px' }} />
    </>
  );
}

const DataItemTitle = styled(Typography.Title).attrs({ level: 5 })`
  width: 200px;
  ::after {
    content: ':';
  }
  margin-right: 20px;
`;
const DataItemValue = styled(Typography.Text)``;
const DataItem = styled.div`
  display: flex;
`;

type MembersProps = {
  title: string;
  members: Membership[];
  organisation: Organisation;
  permission: MembershipPermission;
};
function Members({ members, organisation }: MembersProps) {
  if (!members) {
    return null;
  }

  return (
    <>
      <List
        dataSource={members}
        itemLayout="horizontal"
        renderItem={(membership, key) => (
          // <MembershipUser key={key} membership={membership} organisation={organisation} permission={membership.permissions} />
          <ProfileList
            imageSize={55}
            showPosition={false}
            showOnlineStatus={true}
            description={membership.permissions}
            profile={membership.profile}
            linkToProfile={true}
            style={{ padding: `${PADDING_SM} 0` }}
          ></ProfileList>
        )}
      />
    </>
  );
}
const NoUsersContainer = styled.div`
  display: flex;
  justify-content: center;
  margin: ${GUTTER_MD_PX} 0 ${GUTTER_LG_PX};
  opacity: 0.4;

  ${IconContainer} {
    margin-right: 4px;
  }
`;

type Props1 = {
  members: Membership[];
  organisation: Organisation;
};
