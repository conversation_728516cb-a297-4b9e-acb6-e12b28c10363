import { gql } from '@apollo/client';

export type RemoveOrganisationVariables = {
  organisationId: string;
};

export const REMOVE_ORGANISATION = gql`
  mutation ($organisationId: String!) {
    adminPanelRemoveOrganisation(organisationId: $organisationId)
  }
`;
import { Organisation } from '@GraphQLTypes';

export type GetOrganisationData = {
  organisation: Organisation;
};

export const GET_ORGANISATION = gql`
  query GetOrganisation($organisationId: String!) {
    organisation(id: $organisationId) {
      id
      name
      type
      image
      backgroundImage
      location
      privacy
      followingPrivacy
      peoplePrivacy
      additionalPrivacy
      followingPrivacy
      additionalPrivacy
      website
      description
      size
      vanityId
      isOwner
      isMember
      isPublic
      pages
      permissions
      resources
      resourceUrl
      followerStatus
      followersActiveCount
      followersPendingCount
      webinarsCount
      eventsCount
      referralCount
      createdAt
      updatedAt
      location
      parentOrganisationDetails {
        id
        name
        image
      }
      memberships(filter: { status: [Active], includeActiveProfile: true }) {
        records {
          id
          status
          permissions
          createdAt
          position
          isPrimary
          profile {
            id
            name
            image
            onlineStatus
            lastOnlineAt
            streamChannelId
            responsibilities
          }
          organisation {
            id
            name
          }
        }
      }
    }
  }
`;

export const ADD_PARENT_ORGANISATION = gql`
  mutation ($data: ParentOrganisationInput!) {
    addParentOrganisation(data: $data) {
      id
    }
  }
`;

export const REMOVE_PARENT_ORGANISATION = gql`
  mutation ($data: ParentOrganisationInput!) {
    removeParentOrganisation(data: $data) {
      id
    }
  }
`;
