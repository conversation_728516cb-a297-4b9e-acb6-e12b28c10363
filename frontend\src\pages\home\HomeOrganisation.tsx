import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import numeral from 'numeral';
import { Col, Divider, Progress, Row, Select, Space, Typography } from 'antd';

import { GUTTER_MD, GUTTER_SM, GUTTER_SM_PX, NUMBER_FORMAT, PADDING_LG, PADDING_SM } from '@theme';
import { ContainerCard } from '@components/Layout/Container';
import { ButtonWithIcon } from '@components/Buttons/ButtonWithIcon';
import { Icons } from '@components/icons/Icon';
import { notEmpty } from '@utils/notEmpty';
import { SubTitle } from '@components/SubTitle';
import { Avatar } from '@components/Image/Image';
import { Routes } from '@Routes';
import { ButtonWithIconAndTextUnder } from '@components/Buttons/ButtonWithIconAndTextUnder';
import { IconBoxSmall } from '@components/icons/IconBox';
import { encodePathParams } from '@utils/encodePathParams';
import { hasPermission, OrganisationActions } from '../organisation/permissions';
import { useProfile } from '@src/routes/ProfileProvider';
import { BasicProfileMembership } from '@src/routes/queries';
import { TierLevel } from './HomeProfile';
import { useQuery } from '@apollo/client';
import { getTierProgress, numberWithCommas } from '@src/utils/getTierProgress';
import { GET_ORG_PROFILE_REWARDS, OrgRewardsProps } from '../clubHablo/queries';
import { PartnershipRequestStatus } from '@src/graphql/GraphQLTypes';

export function HomeOrganisation() {
  const { profile } = useProfile();

  const memberships = profile.memberships.filter(({ organisation }) => !!organisation).filter(notEmpty);

  return memberships?.length ? <HomeOrganisations memberships={memberships} /> : null;
}

function HomeOrganisations({ memberships }: { memberships: BasicProfileMembership[] }) {
  const { t } = useTranslation();
  const [organisation, setOrganisation] = useState<BasicProfileMembership['organisation']>();
  const [organisationOptions, setOrganisationOptions] = useState<any[]>();

  const { data: rewardsData, loading: rewardsLoading } = useQuery<{ getOrganisationRewardsData: OrgRewardsProps }>(
    GET_ORG_PROFILE_REWARDS,
    {
      skip: !organisation,
      variables: { organisationId: organisation?.id },
      fetchPolicy: 'cache-and-network',
    },
  );

  useEffect(() => {
    setOrganisation((memberships.find(({ isPrimary }) => isPrimary) || memberships[0])?.organisation);
    setOrganisationOptions(
      memberships.map(({ organisation }) => ({
        value: organisation.id,
        label: organisation.name,
        organisation,
      })),
    );
  }, [memberships]);

  if (!organisationOptions || !organisation || rewardsLoading) {
    return null;
  }

  const vanityId = organisation.vanityId || '';
  const followersCount = numeral(organisation.followersActiveCount).format(NUMBER_FORMAT);
  const followersPendingCount = numeral(organisation.followersPendingCount).format(NUMBER_FORMAT);

  const hasConnectedOrganisationPermission = hasPermission(
    OrganisationActions.updateConnections,
    organisation.permissions,
  );
  const canUpdateOrgSettings = hasPermission(OrganisationActions.updateOrganisation, organisation.permissions);
  const canViewDashboard = hasPermission(OrganisationActions.viewDashboard, organisation.permissions);

  const isChild = memberships.some(
    ({ partnerOrganisation }) =>
      partnerOrganisation?.parentOrganisation && partnerOrganisation.status === PartnershipRequestStatus.Approved,
  );

  return (
    <Container data-cy="home-organisation">
      <Heading>
        <div style={{ display: 'flex' }}>
          <div style={{ flex: 1, display: 'flex', alignItems: 'center' }}>
            <SubTitle style={{ margin: 0 }}>{t('YOUR ORGANISATION')}</SubTitle>
          </div>
        </div>
      </Heading>
      <div>
        <OrganisationSelect
          bordered={false}
          dropdownMatchSelectWidth={false}
          disabled={organisationOptions.length <= 1}
          value={organisation.id}
          options={organisationOptions}
          onSelect={(id, option) => setOrganisation(option.organisation)}
        />
      </div>
      <OrganisationOverview>
        <Link to={encodePathParams(Routes.organisationProfile, { vanityId })}>
          <Avatar id={organisation.image} width={72} />
        </Link>
        <OrganisationStats>
          <Space style={{ width: '100%' }} direction="vertical" size={GUTTER_SM}>
            <OrganisationStat>
              <IconBoxSmall color="var(--color-primary)" icon={Icons.users} />
              <OrganisationStatInfo>
                <OrganisationStatCount style={{ color: 'var(--color-primary)' }}>
                  {followersCount}
                </OrganisationStatCount>
                <Typography.Text type="secondary">{t('Followers')}</Typography.Text>
              </OrganisationStatInfo>
            </OrganisationStat>
            {!organisation.isPublic && (
              <Link to={encodePathParams(Routes.organisationProfile, { vanityId })}>
                <OrganisationStat>
                  <IconBoxSmall
                    color="var(--color-accent)"
                    backgroundColor="var(--color-accent90)"
                    icon={Icons.notifications}
                  />
                  <OrganisationStatInfo>
                    <OrganisationStatCount style={{ color: 'var(--color-accent)' }}>
                      {followersPendingCount}
                    </OrganisationStatCount>
                    <Typography.Text type="secondary">{t('Pending Followers')}</Typography.Text>
                  </OrganisationStatInfo>
                </OrganisationStat>
              </Link>
            )}
          </Space>
        </OrganisationStats>
      </OrganisationOverview>
      <Divider />
      <Row>
        <Col span={6}>
          <Link to={encodePathParams(Routes.organisationProfile, { vanityId })}>
            <ButtonWithIconAndTextUnder type="link" block={true} icon={Icons.posts}>
              {t('Posts')}
            </ButtonWithIconAndTextUnder>
          </Link>
        </Col>
        <Col span={6}>
          <Link to={encodePathParams(Routes.organisationEvents, { vanityId })}>
            <ButtonWithIconAndTextUnder type="link" block={true} icon={Icons.calendar}>
              {t('Events')}
            </ButtonWithIconAndTextUnder>
          </Link>
        </Col>
        <Col span={6}>
          <Link to={encodePathParams(Routes.organisationWebinars, { vanityId })}>
            <ButtonWithIconAndTextUnder type="link" block={true} icon={Icons.webinar}>
              {t('Videos')}
            </ButtonWithIconAndTextUnder>
          </Link>
        </Col>
        <Col span={6}>
          <Link to={encodePathParams(Routes.organisationPeople, { vanityId })} data-cy={'My-org'}>
            <ButtonWithIconAndTextUnder type="link" block={true} icon={Icons.users}>
              {t('People')}
            </ButtonWithIconAndTextUnder>
          </Link>
        </Col>
      </Row>
      <Divider />
      <Space style={{ width: '100%' }} direction="vertical" size={GUTTER_MD}>
        {/* <ComingSoon>
          <ButtonWithIcon type="primary" ghost={true} block={true}>
            {t('Invite People')}
          </ButtonWithIcon>
        </ComingSoon> */}
        {rewardsData && (
          <>
            {organisation.hasClubHabloSubscription && canViewDashboard ? (
              <>
                <TierLevel gutter={GUTTER_SM} tier={rewardsData?.getOrganisationRewardsData.activeTier}>
                  <Col span={24}>
                    <div className="current-level">
                      <p
                        dangerouslySetInnerHTML={{
                          __html: t('Current Tier {{level}}', {
                            level: rewardsData?.getOrganisationRewardsData.activeTier,
                          }),
                        }}
                      />
                    </div>
                    {+rewardsData.getOrganisationRewardsData.nextTierPoints >
                    +rewardsData.getOrganisationRewardsData.habloPoints ? (
                      <div className="progress-details">
                        <label
                          dangerouslySetInnerHTML={{
                            __html: t(
                              rewardsData?.getOrganisationRewardsData.activeTier ===
                                rewardsData.getOrganisationRewardsData.nextTier
                                ? '{{kudos}} to retain {{level}}'
                                : '{{kudos}} to reach {{level}}',
                              {
                                kudos: numberWithCommas(
                                  Number(rewardsData.getOrganisationRewardsData.nextTierPoints) -
                                    rewardsData.getOrganisationRewardsData.habloPoints,
                                ),
                                level: rewardsData.getOrganisationRewardsData.nextTier,
                              },
                            ),
                          }}
                        ></label>
                        <span className="progress">{getTierProgress(rewardsData.getOrganisationRewardsData)}%</span>
                      </div>
                    ) : null}
                    <Progress
                      percent={getTierProgress(rewardsData.getOrganisationRewardsData)}
                      showInfo={false}
                      size="small"
                    />

                    {rewardsData?.getOrganisationRewardsData.activeTier === 'gold' ? (
                      <p style={{ fontSize: '11px', fontWeight: '500', color: 'var(--color-gold)', marginBottom: 0 }}>
                        {t("You've reached the highest tier for Organisations!")}
                      </p>
                    ) : null}
                  </Col>
                </TierLevel>
              </>
            ) : null}
          </>
        )}
        {organisation.hasClubHabloSubscription && canViewDashboard && (
          <Link to={encodePathParams(Routes.organisationClubHabloDashboard, { vanityId: organisation.vanityId })}>
            <ButtonWithIcon type="primary" ghost={true} block={true}>
              {t('Club Hablo Dashboard')}
            </ButtonWithIcon>
          </Link>
        )}
        <Link to={encodePathParams(Routes.organisationAnalytics, { vanityId: organisation.vanityId })}>
          <ButtonWithIcon type="primary" ghost={true} block={true}>
            {t('Analytics')}
          </ButtonWithIcon>
        </Link>
        {canUpdateOrgSettings && !isChild && (
          <Link to={encodePathParams(Routes.organisationSettings, { organisationId: organisation.id })}>
            <ButtonWithIcon type="primary" ghost={true} block={true} data-cy="home-manage-organisation">
              {t('Manage Organisation')}
            </ButtonWithIcon>
          </Link>
        )}
        {hasConnectedOrganisationPermission && (
          <Link to={encodePathParams(Routes.organisationSettingsConnections, { organisationId: organisation.id })}>
            <ButtonWithIcon type="primary" ghost={true} block={true} data-cy="home-connected-oragnisations">
              {t('Connected Organisations')}
            </ButtonWithIcon>
          </Link>
        )}
      </Space>
    </Container>
  );
}

const Container = styled(ContainerCard)`
  display: flex;
  flex-direction: column;
  padding: ${PADDING_LG};
  min-height: 300px;
`;

const Heading = styled.div`
  margin-bottom: ${PADDING_SM};
`;

const OrganisationOverview = styled.div`
  display: flex;
`;

const OrganisationStats = styled.div`
  flex: 1;
  display: flex;
  margin-left: ${GUTTER_SM_PX};
`;

const OrganisationStat = styled.div`
  display: flex;
  align-items: flex-start;
`;

const OrganisationStatInfo = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-left: ${GUTTER_SM_PX};

  .ant-typography {
    font-size: 12px;
    line-height: 12px;
  }
`;

const OrganisationStatCount = styled.div`
  font-size: 14px;
  line-height: 16px;
`;

const OrganisationSelect = styled(Select)`
  width: auto;
  max-width: 100%;

  .ant-select-selector {
    font-size: 18px;
    font-weight: 500;
    color: var(--color-primary) !important;
    padding-left: 0 !important;
  }

  .ant-select-arrow {
    color: var(--color-primary) !important;
  }

  &.ant-select-disabled {
    .ant-select-selector {
      cursor: default !important;
    }
    .ant-select-arrow {
      display: none;
    }
  }
`;
