import { gql, useQuery } from '@apollo/client';
import { ButtonWithIcon } from '@components/Buttons/ButtonWithIcon';
import { Icons } from '@components/icons/Icon';
import { ContainerCard, ContainerCentered } from '@components/Layout/Container';
import { PartnershipRequestStatus, Profile } from '@GraphQLTypes';
import { GUTTER_MD, GUTTER_MD_PX, PADDING_SM } from '@theme';
import { useModalToggle } from '@utils/hooks/useModalToggle';
import { Col, Row, Tabs, Skeleton, Tooltip } from 'antd';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { useLocation } from 'react-router-dom';
import qs from 'query-string';

import { CreateEvent } from '../organisation/event/create/CreateEvent';
import { CreateIncentive } from '../organisation/incentive/create/CreateIncentive';
import { CreatePost } from '../organisation/post/create/CreatePost';
import { CreateWebinar } from '../organisation/webinar/create/CreateWebinar';
import { Loader } from '@src/components/Loader';
import { useProfile } from '@src/routes/ProfileProvider';

interface HomeOrganisationActionsType {
  primaryOrg?: any;
  loading: boolean;
  tab: string;
  setTab: React.Dispatch<React.SetStateAction<string>>;
}
export const HomeOrganisationActions = React.memo(
  ({ primaryOrg, loading, tab, setTab }: HomeOrganisationActionsType) => {
    const { data } = useQuery<{ profile: Pick<Profile, 'memberships'> }>(
      gql`
        query GetProfileMembershipsForHomeOrganisationActions {
          profile {
            id
            memberships(filter: { status: [Active], permission: [Owner, Admin, HiddenAdmin, Manager, Editor] }) {
              id
            }
          }
        }
      `,
      { fetchPolicy: 'cache-first' },
    );

    if (loading || !data) {
      return (
        <ContainerCentered>
          <Loader useGIF={true} />
        </ContainerCentered>
      );
    }

    return (
      <>
        {!!data?.profile.memberships.length && <Actions />}
        {!primaryOrg && loading && (
          <Row style={{ height: '64px', padding: '0 15px', textAlign: 'center', paddingTop: '12px' }}>
            <Col span={24}>
              <Skeleton.Input size={'small'} active style={{ marginRight: '18px' }} />
              <Skeleton.Input size={'small'} active />
            </Col>
          </Row>
        )}
        {!!primaryOrg && (
          <Row>
            <Col span={24}>
              <Tabs
                centered
                defaultActiveKey={tab}
                onChange={setTab}
                items={[
                  {
                    label: 'Following',
                    key: 'following',
                  },
                  {
                    label: `${primaryOrg?.name} Only`,
                    key: 'organisation',
                  },
                ]}
              />
            </Col>
          </Row>
        )}
      </>
    );
  },
);

function Actions() {
  const { t } = useTranslation();
  const { profile } = useProfile();
  let location = useLocation();
  const { create } = qs.parse(location.search);
  const membership = profile.memberships[0];

  const isChild = membership?.partnerOrganisation
    ? membership.partnerOrganisation.status === PartnershipRequestStatus.Approved
    : false;

  useEffect(() => {
    if (create && create === 'post') setPostShow(true);
  }, [location]);

  const [CreatePostModal, setPostShow, postShow] = useModalToggle((props) => <CreatePost {...props} />);
  const [CreateEventModal, setEventShow, eventShow] = useModalToggle(CreateEvent);
  const [CreateIncentiveModal, setIncentiveShow, incentiveShow] = useModalToggle(CreateIncentive);
  const [CreateWebinarModal, setWebinarShow, webinarShow] = useModalToggle(CreateWebinar);

  return (
    <Container data-cy="home-organisation-actions" style={{ padding: window.innerWidth < 568 ? '0 0 15px 0' : '15px' }}>
      <Card>
        <Row gutter={window.innerWidth < 568 ? 6 : GUTTER_MD}>
          <Col span={6}>
            <ButtonWithIcon
              type="primary"
              data-cy="home-organisation-actions-post"
              ghost={true}
              block={true}
              icon={Icons.posts}
              size={window.innerWidth < 568 ? 'small' : 'middle'}
              style={{ fontSize: window.innerWidth < 568 ? '11px' : '14px' }}
              onClick={() => setPostShow(true)}
            >
              {t('Post')}
            </ButtonWithIcon>
          </Col>
          <Col span={6}>
            <Tooltip
              title={isChild ? t('You cannot create events as a child organisation.') : undefined}
              placement="top"
            >
              <>
                <ButtonWithIcon
                  type="primary"
                  data-cy="home-organisation-actions-event"
                  ghost={true}
                  block={true}
                  disabled={isChild}
                  icon={Icons.calendarAdd}
                  size={window.innerWidth < 568 ? 'small' : 'middle'}
                  style={{ fontSize: window.innerWidth < 568 ? '11px' : '14px' }}
                  onClick={() => setEventShow(true)}
                >
                  {t('Event')}
                </ButtonWithIcon>
              </>
            </Tooltip>
          </Col>
          <Col span={6}>
            <Tooltip
              title={isChild ? t('You cannot upload videos as a child organisation.') : undefined}
              placement="top"
            >
              <>
                <ButtonWithIcon
                  type="primary"
                  data-cy="home-organisation-actions-webinar"
                  ghost={true}
                  block={true}
                  disabled={isChild}
                  icon={Icons.webinar}
                  size={window.innerWidth < 568 ? 'small' : 'middle'}
                  style={{ fontSize: window.innerWidth < 568 ? '11px' : '14px' }}
                  onClick={() => setWebinarShow(true)}
                >
                  {t('Video')}
                </ButtonWithIcon>
              </>
            </Tooltip>
          </Col>
          <Col span={6}>
            <Tooltip
              title={isChild ? t('You cannot create incentives as a child organisation.') : undefined}
              placement="top"
            >
              <>
                <ButtonWithIcon
                  type="primary"
                  data-cy="home-organisation-actions-incentive"
                  ghost={true}
                  block={true}
                  disabled={isChild}
                  icon={Icons.incentives}
                  size={window.innerWidth < 568 ? 'small' : 'middle'}
                  style={{ fontSize: window.innerWidth < 568 ? '11px' : '14px' }}
                  onClick={() => setIncentiveShow(true)}
                >
                  {t('Incentive')}
                </ButtonWithIcon>
              </>
            </Tooltip>
          </Col>
        </Row>
      </Card>
      {postShow && <CreatePostModal />}
      {eventShow && !isChild && <CreateEventModal />}
      {incentiveShow && !isChild && <CreateIncentiveModal />}
      {webinarShow && !isChild && <CreateWebinarModal />}
    </Container>
  );
}

const Container = styled.div`
  padding: ${GUTTER_MD_PX};
  @media screen and (max-width: 568px) {
    padding-top: 0;
  }
`;

const Card = styled(ContainerCard)`
  padding: ${PADDING_SM};
  min-height: unset;
`;
