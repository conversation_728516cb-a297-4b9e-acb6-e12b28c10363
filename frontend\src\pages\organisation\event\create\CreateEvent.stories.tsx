import React from 'react';
import { action } from '@storybook/addon-actions';
import { Form, Modal } from 'antd';

import { MockApolloProvider } from '@utils/mocks/MockApolloProvider';
import { GET_IMAGE_SIGNATURE } from '@components/Image/queries';
import { CreateEventForm } from './CreateEventForm';
import { mockOrganisation } from '@utils/mocks/mockOrganisation';
import { mockPartnerOrganisation } from '@utils/mocks/mockPartnerOrganisation';
import { useTranslation } from 'react-i18next';
import { BasicProfileMembership } from '@src/routes/queries';

function Wrapper({
  loading,
  memberships,
}: {
  loading: boolean;
  memberships: BasicProfileMembership[];
  image?: string;
}) {
  const { t } = useTranslation();
  return (
    <Modal title={t('CREATE AN EVENT')} width={550} footer={null} open={true}>
      <MockApolloProvider mocks={[{ request: { query: GET_IMAGE_SIGNATURE }, result: { data: {} } }]}>
        <CreateEventForm
          form={Form.useForm()[0]}
          loading={loading}
          memberships={memberships}
          onFinish={() => Promise.resolve(action('CreateEvent')())}
        />
      </MockApolloProvider>
    </Modal>
  );
}

const membership1: BasicProfileMembership = {
  id: '1',
  isPrimary: true,
  organisation: mockOrganisation(),
  partnerOrganisation: mockPartnerOrganisation(),
};
const membership2: BasicProfileMembership = {
  id: '2',
  isPrimary: false,
  organisation: mockOrganisation(),
  partnerOrganisation: mockPartnerOrganisation(),
};
const membership3: BasicProfileMembership = {
  id: '3',
  isPrimary: false,
  organisation: mockOrganisation(),
  partnerOrganisation: mockPartnerOrganisation(),
};

export default {
  title: 'Organisation/Event/Create',
};

export const withMemberships = () => <Wrapper loading={false} memberships={[membership1, membership2, membership3]} />;
