import { Membership, MembershipPermission } from '@GraphQLTypes';

const { Owner, OwnerPending, HiddenAd<PERSON>, Admin, Manager, Editor, Staff, Linked, InviteMembersToEvents, Member } =
  MembershipPermission;

export enum OrganisationActions {
  addMember = 'addMember',
  updatePost = 'updatePost',
  updateOrganisation = 'updateOrganisation',
  updateEditorMember = 'updateEditorMember',
  updateManagerMember = 'updateManagerMember',
  updateAdminMember = 'updateAdminMember',
  updateEmployees = 'updateEmployees',
  removeEmployees = 'removeEmployees',
  updateMembers = 'updateMembers',
  updateConnections = 'updateConnections',
  updateInvitations = 'updateInvitations',
  updateEvent = 'updateEvent',
  updateIncentive = 'updateIncentive',
  updateWebinar = 'updateWebinar',
  manageFollowers = 'manageFollowers',
  updateOrganisationPage = 'updateOrganisationPage',
  updateDestinationPage = 'updateDestinationPage',
  updatePrivacySettings = 'updatePrivacySettings',
  updatePartners = 'updatePartners',
  addPartners = 'addPartners',
  removePartners = 'removePartners',
  viewPartnersPage = 'viewPartnersPage',
  updatePartnerPostLimit = 'updatePartnerPostLimit',
  viewDashboard = 'viewDashboard',
}

export enum DestinationPageActions {}

const OrganisationAccessRights: { [action in OrganisationActions]: MembershipPermission[] } = {
  [OrganisationActions.addMember]: [Owner, OwnerPending, HiddenAdmin, Admin, Manager],
  [OrganisationActions.updateOrganisation]: [Owner, OwnerPending, HiddenAdmin, Admin, Manager],
  [OrganisationActions.updateOrganisationPage]: [Owner, OwnerPending, HiddenAdmin, Admin, Manager, Editor],
  [OrganisationActions.updateAdminMember]: [Owner, OwnerPending],
  [OrganisationActions.updateManagerMember]: [Owner, OwnerPending, HiddenAdmin, Admin],
  [OrganisationActions.updateEditorMember]: [Owner, OwnerPending, HiddenAdmin, Admin, Manager],
  [OrganisationActions.updateEmployees]: [Owner, OwnerPending, HiddenAdmin, Admin, Manager],
  [OrganisationActions.updateInvitations]: [Owner, OwnerPending, HiddenAdmin, Admin, Manager],
  [OrganisationActions.removeEmployees]: [Owner, OwnerPending, HiddenAdmin, Admin],
  [OrganisationActions.updateMembers]: [Owner, OwnerPending, HiddenAdmin, Admin, Manager],
  [OrganisationActions.updateConnections]: [Owner, OwnerPending, HiddenAdmin, Admin],
  [OrganisationActions.updateEvent]: [Owner, OwnerPending, HiddenAdmin, Admin, Manager, Editor],
  [OrganisationActions.updatePost]: [Owner, OwnerPending, HiddenAdmin, Admin, Manager, Editor],
  [OrganisationActions.updateIncentive]: [Owner, OwnerPending, HiddenAdmin, Admin, Manager, Editor],
  [OrganisationActions.updateWebinar]: [Owner, OwnerPending, HiddenAdmin, Admin, Manager, Editor],
  [OrganisationActions.manageFollowers]: [Owner, OwnerPending, HiddenAdmin, Admin, Manager, Editor],
  [OrganisationActions.updateDestinationPage]: [Owner, OwnerPending, HiddenAdmin, Admin, Manager],
  [OrganisationActions.updatePrivacySettings]: [Owner, OwnerPending, HiddenAdmin, Admin],
  [OrganisationActions.updatePartners]: [Owner, OwnerPending, HiddenAdmin, Admin],
  [OrganisationActions.viewPartnersPage]: [Owner, OwnerPending, HiddenAdmin, Admin, Manager, Editor],
  [OrganisationActions.updatePartnerPostLimit]: [Owner, HiddenAdmin, Admin, Manager],
  [OrganisationActions.viewDashboard]: [Owner, HiddenAdmin, Admin, Manager, Editor],
  [OrganisationActions.addPartners]: [Owner, OwnerPending, HiddenAdmin, Admin],
  [OrganisationActions.removePartners]: [Owner, OwnerPending, HiddenAdmin, Admin],
};

export const organisationPermissionOptions: { [permission in MembershipPermission]: MembershipPermission[] } = {
  [Owner]: [Admin, HiddenAdmin, Manager, Editor],
  [OwnerPending]: [Admin, HiddenAdmin, Manager, Editor],
  [HiddenAdmin]: [Manager, Editor],
  [Admin]: [Manager, Editor],
  [Manager]: [Editor],
  [Editor]: [],
  [Staff]: [],
  [Linked]: [],
  [InviteMembersToEvents]: [],
  [Member]: [],
};

type Permissions = MembershipPermission[] | null | undefined;

export function isOwner(permissions: Permissions): boolean {
  return (permissions || []).includes(Owner);
}

export function hasPermission(action: OrganisationActions, permissions?: Permissions | null) {
  return includesAny(OrganisationAccessRights[action], permissions || []);
}

export function highestPermission(permissions: Permissions) {
  if ((permissions || []).includes(Owner)) return Owner;
  if ((permissions || []).includes(OwnerPending)) return OwnerPending;
  if ((permissions || []).includes(Admin)) return Admin;
  if ((permissions || []).includes(HiddenAdmin)) return HiddenAdmin;
  if ((permissions || []).includes(Manager)) return Manager;
  if ((permissions || []).includes(Editor)) return Editor;
  if ((permissions || []).includes(Staff)) return Staff;
  return Linked;
}

function includesAny(allowedPermissions: MembershipPermission[], permissions: Permissions) {
  return (permissions || []).some((permission) => allowedPermissions?.includes(permission));
}

export function getMembershipPermission(memberships: Membership[], organisationId: string): Permissions | undefined {
  const membership = memberships.find(({ organisation }) => organisation?.id === organisationId);
  return membership?.permissions;
}

export function checkUpdatePostPermission(memberships: any[] | undefined, activity: any): boolean {
  if (!memberships) return false;
  let orgId: any;
  if (activity?.object?.collection === 'organisations') {
    orgId = activity?.object?.id;
  } else if (activity?.organisation?.id) {
    orgId = activity?.organisation?.id;
  } else if (activity?.post?.data?.organisationId) {
    orgId = activity?.post?.data?.organisationId;
  }

  if (!orgId) return false;

  const hasMembership = !!memberships.find((el) => el?.organisation?.id === orgId);
  return hasMembership;
}
