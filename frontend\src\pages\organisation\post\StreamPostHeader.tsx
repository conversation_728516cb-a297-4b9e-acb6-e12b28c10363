import { useMutation } from '@apollo/client';
import Icon, { Icons } from '@components/icons/Icon';
import ArrowRight from '@components/icons/svgs/ArrowRight.svg?react';
import { ButtonMore } from '@src/components/Buttons/ButtonMore';
import { Clickable } from '@src/components/Buttons/Clickable';
import { CreatedTime } from '@src/components/CreatedTime';
import { DropdownMenu } from '@src/components/DropdownMenu';
import { FeedContext } from '@src/routes/PrivateRoutesHandler';
import { useProfile } from '@src/routes/ProfileProvider';
import { Routes } from '@src/routes/Routes';
import { emptyObject } from '@src/utils/emptyObject';
import { encodePathParams } from '@src/utils/encodePathParams';
import { Button, message, Modal, notification, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { useCallback, useContext, useState, useEffect, useRef, useMemo, memo } from 'react';
import { useFeedContext } from 'react-activity-feed';
import { useTranslation } from 'react-i18next';
import { InView, useInView } from 'react-intersection-observer';
import { Link, useHistory } from 'react-router-dom';
import styled from 'styled-components';
import {
  REMOVE_UPDATE,
  RemoveUpdateVariables,
  STREAM_REMOVE_UPDATE,
  StreamRemoveUpdateVariables,
} from '../components/updates/queries';
import { checkUpdatePostPermission } from '../permissions';
import { CreatePost } from './create/CreatePost';
import {
  ADD_POST_IMPRESSION_TO_ACTIVITY,
  ADD_POST_VIEW_TO_ACTIVITY,
  AddPostImpressionToActivityVariables,
  AddPostViewToActivityVariables,
  REMOVE_REPOST,
  REMOVE_STREAM_POST,
  RemoveRepostVariables,
  RemoveStreamPostVariables,
  SAVE_POST,
  SavePostVariables,
  UNSAVE_POST,
  UnsavePostVariables,
} from './create/queries';
import { Header, HeaderImage, HeaderInfo, HeaderTitle, Info } from './styles';
import { useModalToggle } from '@utils/hooks/useModalToggle';
import { openNotification } from '@src/utils/notification';
import { ButtonWithIcon } from '@src/components/Buttons/ButtonWithIcon';
import { useSavedPosts } from '@src/hooks/useSavedPosts';
import { PostProfileImages } from '@src/components/PostProfileImages';
import TierIcon from '@src/components/TierIcon';
import { getPrimaryMembership } from '@src/utils/getOrganisationName';

const views = new Map();

const StreamPostHeader = memo(
  ({
    activity,
    isSinglePost,
    memberships,
  }: {
    activity: any;
    isSinglePost?: boolean;
    memberships?: any[] | undefined;
  }) => {
    const history = useHistory();
    const { t } = useTranslation();
    const { ref, inView } = useInView({
      threshold: 1,
      triggerOnce: true,
    });
    const { savedPosts, refetchSavedPosts } = useSavedPosts();
    const [saveActionLoading, setSaveActionLoading] = useState<boolean>(false);
    const [isPostSaved, setIsPostSaved] = useState<boolean>(savedPosts.includes(activity.id));
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);
    const viewProcessedRef = useRef<boolean>(false);

    const [addPostImpressionToActivity] = useMutation<{}, AddPostImpressionToActivityVariables>(
      ADD_POST_IMPRESSION_TO_ACTIVITY,
    );
    const [savePost] = useMutation<{}, SavePostVariables>(SAVE_POST);
    const [unsavePost] = useMutation<{}, UnsavePostVariables>(UNSAVE_POST);
    const [addPostViewToActivity] = useMutation<{}, AddPostViewToActivityVariables>(ADD_POST_VIEW_TO_ACTIVITY);
    const [CreatePostModal, setPostShow, postShow] = useModalToggle((props) => (
      <CreatePost {...props} activity={activity} isUpdate={true} />
    ));
    const [removePost] = useMutation<{}, RemoveStreamPostVariables>(REMOVE_STREAM_POST);
    const [removeRepost] = useMutation<{}, RemoveRepostVariables>(REMOVE_REPOST);
    const [removeUpdate] = useMutation<{}, RemoveUpdateVariables>(REMOVE_UPDATE);
    const [streamRemoveUpdate] = useMutation<{}, StreamRemoveUpdateVariables>(STREAM_REMOVE_UPDATE);

    const inViewHandler = useCallback(
      (inView: boolean) => {
        if (inView && !views.has(activity.id) && (activity?.update?.collection || activity?.post?.collection)) {
          addPostImpressionToActivity({
            variables: {
              activityId: activity.id,
              postId: activity?.update?.data?.postId ?? activity?.post?.id ?? '',
              repostPostId: activity?.repostData?.post?.id ?? '',
              postType: activity?.update?.collection ?? activity?.post?.collection,
            },
          });
          views.set(activity.id, true);
        }
      },
      [activity],
    );

    const inViewDelayHandler = useCallback(
      (inView: boolean) => {
        if (
          inView &&
          views.has(activity.id) &&
          !viewProcessedRef.current &&
          (activity?.update?.collection || activity?.post?.collection)
        ) {
          viewProcessedRef.current = true;
          addPostViewToActivity({
            variables: {
              activityId: activity.id,
              postId: activity?.update?.data?.postId ?? activity?.post?.id ?? '',
              repostPostId: activity?.repostData?.post?.id ?? '',
              postType: activity?.update?.collection ?? activity?.post?.collection,
            },
          });
        }
      },
      [activity],
    );

    const vanityId: string = activity?.object?.data?.vanityId || activity?.object.object?.data?.vanityId;
    const vanityIdUpdate: string =
      activity?.organisation?.data?.vanityId || activity?.object.organisation?.data?.vanityId;
    const webinarId: string = activity?.object?.data?.webinarId || activity?.object.object?.data?.webinarId;
    const pathToWebinarUpdate =
      webinarId &&
      encodePathParams(Routes.organisationWebinar, {
        vanityId: vanityIdUpdate,
        id: webinarId,
      });
    const eventId: string = activity?.object?.data?.eventId || activity?.object.object?.data?.eventId;
    const pathToEventUpdate =
      eventId &&
      encodePathParams(Routes.organisationEvent, {
        vanityId: vanityIdUpdate,
        eventId: eventId,
      });
    const incentiveId: string = activity?.object?.data?.incentiveId || activity?.object.object?.data?.incentiveId;
    const pathToIncentiveUpdate =
      incentiveId &&
      encodePathParams(Routes.organisationIncentive, {
        vanityId: vanityIdUpdate,
        incentiveId: incentiveId,
      });
    const pathToOrganisation = vanityId && encodePathParams(Routes.organisationProfile, { vanityId });
    const pathToOrganisationUpdate =
      vanityIdUpdate && encodePathParams(Routes.organisationProfile, { vanityId: vanityIdUpdate });

    const hasUpdatePostPermission = checkUpdatePostPermission(memberships, activity); // hasPermission(OrganisationActions.updatePost, activity.permission);
    const isHabloStaff = isSinglePost
      ? false
      : useProfile().profile.email.indexOf('@myhablo.com') > -1 && !!useProfile().profile.isEmailVerified;

    const url = window.location.origin;
    const organisationId = activity?.object?.id;
    const postUrl = `${url}/${activity?.object?.data?.vanityId}/post/${activity.id}/userId/${organisationId}`;
    const reportURL =
      'mailto:<EMAIL>?subject=Report%20A%20Post%20on%20Hablo&body=I%20would%20like%20to%20report%20this%20post%20on%20Hablo%3A%' +
      postUrl +
      '%0D%0A%0D%0AReason%3A';
    const isRegularPost = !(activity?.update || activity?.webinar || activity?.event || activity?.incentive);

    const feed = useFeedContext();
    const feedContext = !isSinglePost && useContext(FeedContext);

    useEffect(() => {
      setIsPostSaved(savedPosts.includes(activity.id));
    }, [savedPosts, activity.id]);

    useEffect(() => {
      if (!isSinglePost && feedContext && emptyObject(feedContext.feedContext)) feedContext.setFeedContext(feed);
    }, [feed]);

    useEffect(() => {
      if (
        !isSinglePost &&
        JSON.stringify(feedContext.feedContext.feedManager?.state.lastResponse) !==
          JSON.stringify(feed.feedManager?.state.lastResponse)
      ) {
        feedContext.setFeedContext(feed);
      }
    }, [feedContext]);

    useEffect(() => {
      if (inView) {
        timeoutRef.current = setTimeout(() => {
          inViewDelayHandler(inView);
        }, 1500);
      } else {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }
      }

      return () => {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }
      };
    }, [inView, activity]);

    const removeRepostHandler = useCallback(() => {
      Modal.confirm({
        title: t('Remove repost'),
        content: t('Are you sure you wish to remove this repost?'),
        cancelText: t('Cancel'),
        okText: t('Remove'),
        okButtonProps: { danger: true },
        onOk: async () => {
          await removePost({
            variables: {
              postId: activity.post.id,
              organisationId: activity.object.id,
              activityId: activity.id,
              isRepost: true,
            },
          });
          feed.refresh();
        },
      });
    }, [activity.id, feed]);

    const removePostHandler = useCallback(() => {
      Modal.confirm({
        title: t('Remove Post'),
        content: t('Are you sure you wish to remove this post?'),
        cancelText: t('Cancel'),
        okText: t('Remove'),
        okButtonProps: { danger: true },
        onOk: async () => {
          await removePost({
            variables: {
              postId: activity.post.id,
              organisationId: activity.object.id,
              activityId: activity.id,
              isRepost: false,
            },
          });
          message.success(t('Post successfully removed!'));
          feed.refresh();
          // @ts-ignore
          window.lastRemovedPostActivityId = activity.id;
          isSinglePost && history.push(Routes.home);
        },
      });
    }, [activity.id, feed, isSinglePost]);

    const updatePostHandler = useCallback(() => {
      setPostShow(true);
    }, []);

    const removeUpdateHandler = useCallback(() => {
      Modal.confirm({
        title: t('Remove Update'),
        content: t('Are you sure you wish to remove this update post for all registered users?'),
        cancelText: t('Cancel'),
        okText: t('Remove'),
        okButtonProps: { danger: true },
        onOk: async () => {
          const type = activity.update.data.typeUpdates;
          let updateEventId;
          if (type === 'events') updateEventId = eventId;
          else if (type === 'webinars') updateEventId = webinarId;
          else updateEventId = incentiveId;

          await removeUpdate({
            variables: {
              postId: activity.update.id,
            },
          });
          await streamRemoveUpdate({
            variables: {
              organisationId: activity.organisation.id,
              updateId: activity.update.id,
              type: type,
              eventId: updateEventId,
            },
          });
          feed.refresh();
        },
      });
    }, [activity.id, feed]);

    const removeInviteHandler = (type: string) => {
      Modal.confirm({
        title: t(`Remove intive to ${type}`),
        content: t(`Are you sure you wish to remove this invite to ${type}?`),
        cancelText: t('Cancel'),
        okText: t('Remove'),
        okButtonProps: { danger: true },
        onOk: async () => {
          await removeRepost({
            variables: {
              activityId: activity.id,
            },
          });
          feed.refresh();
        },
      });
    };

    const getViews = (activity: any) => {
      const viewsOld = activity?.post?.data?.totalViews || 0;
      const viewsNew = activity?.reaction_counts?.views || 0;

      return `${viewsOld + viewsNew}`;
    };

    const handleSavePost = async () => {
      setSaveActionLoading(true);
      try {
        const isRepost = activity?.foreign_id.split(':')[0].includes('repost');
        const isInvite = activity?.foreign_id.split(':')[0].includes('invite');
        const { data } = await savePost({
          variables: {
            activityId: activity?.id || '',
            feedId: isInvite ? activity.origin.split(':')[1] : activity?.object?.id,
            feedName: isRepost
              ? activity?.object.collection
              : isInvite
              ? activity.origin.split(':')[0]
              : activity?.verb,
          },
        });

        // @ts-ignore
        if (!data?.savePost) {
          setIsPostSaved(false);
          message.error(t('Something went wrong while saving the post. Please try again later.'));
          return;
        }

        await refetchSavedPosts();

        const nKey = `savePost-${Date.now()}`;

        openNotification({
          message: <b>{t('Post Saved.')}</b>,
          description: t('View all of your saved posts.'),
          key: nKey,
          onClick: () => {
            history.push(Routes.savedPosts);
            notification.close(nKey);
          },
          icon: (
            <ButtonWithIcon
              icon={Icons.check}
              style={{ border: 'none', backgroundColor: '#0BD471', color: '#ffffff', height: '30px', width: '30px' }}
            />
          ),
        });
      } catch (e: any) {
        console.error(e);
        message.error(e.message as string);
      } finally {
        setSaveActionLoading(false);
      }
    };

    const handleUnsavePost = async () => {
      setSaveActionLoading(true);
      try {
        const isRepost = activity?.foreign_id.split(':')[0].includes('repost');
        const isInvite = activity?.foreign_id.split(':')[0].includes('invite');
        const { data } = await unsavePost({
          variables: {
            activityId: activity?.id || '',
            feedId: isInvite ? activity.foreign_id.split(':')[1] : activity?.object?.id,
            feedName: isRepost ? activity?.object.collection : isInvite ? 'invites' : activity?.verb,
          },
        });

        // @ts-ignore
        if (!data?.unsavePost) {
          message.error(t('Something went wrong while unsaving the post. Please try again later.'));
          return;
        }

        const { data: savedPostData } = await refetchSavedPosts();

        // Removing node + post from DOM + feed as refresh flicks the page
        if (feed.feedGroup === 'user_saved') {
          const post = document.querySelector(`div[id='${activity.id}']`)?.parentElement?.parentElement;
          if (post) {
            post.style.display = 'none';
          }

          if (savedPostData.getSavedPostIds.activityIds.length === 0) {
            feed.refresh();
          }
        }

        const nKey = `unsavePost-${Date.now()}`;

        openNotification({
          message: <></>,
          description: t('Post removed from your saved posts.'),
          onClick: () => {
            history.push(Routes.savedPosts);
            notification.close(nKey);
          },
          icon: (
            <ButtonWithIcon
              icon={Icons.check}
              style={{ border: 'none', backgroundColor: '#0BD471', color: '#ffffff', height: '30px', width: '30px' }}
            />
          ),
        });
      } catch (e: any) {
        console.error(e);
        message.error(e.message as string);
      } finally {
        setSaveActionLoading(false);
      }
    };

    const permissionReceived = hasUpdatePostPermission || activity?.webinar || activity?.event || activity?.incentive;
    const allowDisplayImpressions = !(activity?.webinar || activity?.event || activity?.incentive);

    const imPostAuthor = activity?.user?.id === useProfile().profile?.id;
    const isNotInternalOrTargeted = !(
      activity?.post?.data?.postAudience?.isMyOrganisation ||
      activity?.post?.data?.postAudience?.connectedOrganisations?.length
    );
    const selectedOrganisationNames = activity?.post?.data?.postAudience?.selectedOrganisationNames as string[];
    const connectedOrganisations = activity?.post?.data?.postAudience?.connectedOrganisations as string[];
    let text = [] as string[];
    if (connectedOrganisations && selectedOrganisationNames && connectedOrganisations.length > 1) {
      text = selectedOrganisationNames.map((e: string, i) => {
        if (i === selectedOrganisationNames.length - 1) {
          return e + ' ';
        } else if (i === selectedOrganisationNames.length - 2) {
          return e + ' and ';
        } else {
          return e + ', ';
        }
      });
    }
    const moreOptionsComponent = useMemo(
      () => (
        <div>
          <DropdownMenu button={<ButtonMore noBackground={true} />}>
            <DropdownMenu.Item icon={Icons.report}>
              <a href={reportURL} target="_blank" style={{ color: 'var(--color-text)' }}>
                {t('Report Post')}
              </a>
            </DropdownMenu.Item>

            {permissionReceived && allowDisplayImpressions && (
              <DropdownMenu.Item
                icon={Icons.close}
                onClick={() => {
                  activity?.repostData && removeRepostHandler();
                  activity?.post && !activity?.repostData && removePostHandler();
                  activity?.webinar && removeInviteHandler('webinar');
                  activity?.event && removeInviteHandler('event');
                  activity?.incentive && removeInviteHandler('incentive');
                  activity?.update && removeUpdateHandler();
                }}
              >
                {t('Remove Post')}
              </DropdownMenu.Item>
            )}
            {(activity?.post || activity?.repostData) && hasUpdatePostPermission && (
              <DropdownMenu.Item icon={Icons.edit} onClick={updatePostHandler}>
                {t('Edit Post')}
              </DropdownMenu.Item>
            )}
          </DropdownMenu>
        </div>
      ),
      [permissionReceived, allowDisplayImpressions, activity.id, hasUpdatePostPermission],
    );

    const memoizedIcons = useMemo(
      () => ({
        viewsIcon: <Icon icon={Icons.views} size={16} style={{ marginRight: '5px', marginTop: '-2px' }} />,
        labelIcon: <Icon icon={Icons.label} size={16} style={{ marginRight: '5px' }} />,
        arrowRight: <ArrowRight style={{ margin: '0 5px' }} />,
        audienceIcons: {
          tourOperator: <AudienceIcon icon={Icons.tourOperator} size={16} />,
          organisation: <AudienceIcon icon={Icons.organisation} size={14} />,
          globe: <AudienceIcon icon={Icons.globe} size={16} />,
          users: <AudienceIcon icon={Icons.users} size={16} />,
        },
      }),
      [],
    );

    const foreignId = activity?.foreign_id.split(':')[0];
    const notAllowedToSavePost =
      ((foreignId.includes('event') || foreignId.includes('invite') || foreignId.includes('webinar')) &&
        activity.senderId === useProfile().profile?.id) ||
      foreignId.includes('event') ||
      foreignId.includes('update');

    const membership = getPrimaryMembership(useProfile().profile);

    return (
      <InView onChange={inViewHandler}>
        <Header id={activity.id} ref={ref}>
          {activity.verb === 'repost' ? (
            <>
              <Link to={pathToOrganisation}>
                <HeaderImage id={activity?.object?.organisation?.data?.image || activity?.object?.data?.image} />
              </Link>

              <HeaderInfo>
                <HeaderTitle>
                  {activity?.repostData?.update ? (
                    <>
                      <Clickable>
                        <Link to={pathToOrganisationUpdate} post-cy="post-title">
                          {activity?.object?.data?.name}
                        </Link>
                      </Clickable>
                      {memoizedIcons.arrowRight}
                      <Clickable>
                        <Link
                          to={pathToWebinarUpdate || pathToEventUpdate || pathToIncentiveUpdate}
                          post-cy="post-title"
                        >
                          {activity?.object?.data?.name}
                        </Link>
                      </Clickable>
                    </>
                  ) : (
                    <>
                      <Clickable>
                        <Link to={pathToOrganisation} post-cy="post-title">
                          {activity?.object?.data?.name}
                        </Link>
                      </Clickable>
                    </>
                  )}
                </HeaderTitle>
                <Info>
                  {(activity?.repostData?.update || activity?.repostData?.webinar || activity?.repostData?.event) && (
                    <>
                      <span style={{ color: 'var(--color-gray3)' }}>
                        {memoizedIcons.labelIcon}
                        {activity?.repostData?.update?.data?.webinarId && t('Video update')}
                        {activity?.repostData?.update?.data?.eventId && t('Event update')}
                        {activity?.repostData?.update?.data?.incentiveId && t('Incentive update')}
                        {(activity?.repostData?.webinar || activity?.repostData?.event) && t('Invitation')}
                      </span>
                      <span style={{ marginLeft: '3px' }}>{' · '}</span>
                    </>
                  )}

                  {(hasUpdatePostPermission || isHabloStaff) && allowDisplayImpressions && (
                    <span
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        color: 'var(--color-gray3)',
                      }}
                    >
                      <Tooltip title={getViews(activity) + t(' impressions on this post')}>
                        {memoizedIcons.viewsIcon}
                        {getViews(activity)}
                      </Tooltip>
                      <span style={{ marginLeft: '3px' }}>{' · '}</span>
                    </span>
                  )}
                  {isNotInternalOrTargeted ? (
                    <Clickable>
                      <Link to={`${pathToOrganisation}/post/${activity.id}/userId/${organisationId}`}>
                        <CreatedTime date={activity?.post?.created_at} condition="HeaderInfo" />
                      </Link>
                    </Clickable>
                  ) : (
                    <CreatedTime date={activity?.post?.created_at} condition="HeaderInfo" />
                  )}
                  {isRegularPost ? (
                    <span
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        color: 'var(--color-gray3)',
                      }}
                    >
                      {' · '}
                      {activity?.post?.data?.postAudience?.isMyOrganisation ? (
                        <>
                          <Tooltip title={t('Sent only to Members of your Organisation')}>
                            <span>{memoizedIcons.audienceIcons.tourOperator}</span>
                          </Tooltip>
                        </>
                      ) : activity?.post?.data?.postAudience?.connectedOrganisations ? (
                        <Tooltip
                          title={
                            imPostAuthor
                              ? connectedOrganisations && selectedOrganisationNames && connectedOrganisations.length > 1
                                ? t('Targeted by you to Members of ') + text.join('')
                                : selectedOrganisationNames
                                ? t('Targeted by you to Members of ') + selectedOrganisationNames[0]
                                : t('Targeted by you to Members of ') +
                                  connectedOrganisations.length +
                                  t(' Connected Organisation(s)')
                              : t('Sent to Members of your Organisation')
                          }
                        >
                          <span>
                            {memoizedIcons.audienceIcons.organisation}
                            {t('Targeted')}
                          </span>
                        </Tooltip>
                      ) : activity?.object?.data?.isPublic ? (
                        <Tooltip title={t('Public')}>
                          <span>{memoizedIcons.audienceIcons.globe}</span>
                        </Tooltip>
                      ) : (
                        <Tooltip title={t('Sent to Approved Followers of ') + activity?.object?.data?.name}>
                          <span>{memoizedIcons.audienceIcons.users}</span>
                        </Tooltip>
                      )}
                    </span>
                  ) : (
                    <>
                      <span style={{ color: 'var(--color-gray3)' }}>
                        {' · '}
                        {memoizedIcons.labelIcon}
                        {activity?.update?.data?.webinarId && t('Video update')}
                        {activity?.update?.data?.eventId && t('Event update')}
                        {activity?.update?.data?.incentiveId && t('Incentive update')}
                        {(activity?.webinar || activity?.event || activity?.incentive) && t('Invitation')}
                      </span>
                    </>
                  )}
                </Info>
              </HeaderInfo>
              <RightActions>
                {notAllowedToSavePost ? null : (
                  <Button
                    type="link"
                    loading={saveActionLoading}
                    className="save-action"
                    icon={
                      <Tooltip
                        title={t(feed.feedGroup === 'user_saved' || isPostSaved ? 'Post Saved' : 'Save this Post')}
                      >
                        <div>
                          {feed.feedGroup === 'user_saved' || isPostSaved ? (
                            <Icon
                              icon={Icons.bookmarked}
                              size={25}
                              color="var(--color-darkerblue)"
                              onClick={handleUnsavePost}
                            />
                          ) : (
                            <Icon
                              icon={Icons.bookmark}
                              size={25}
                              color="var(--color-darkerblue)"
                              onClick={handleSavePost}
                            />
                          )}
                        </div>
                      </Tooltip>
                    }
                  />
                )}
                {moreOptionsComponent}
              </RightActions>
            </>
          ) : (
            <>
              <Link to={pathToOrganisation}>
                {activity.childOrganisation ? (
                  <PostProfileImages profiles={[activity?.object?.data, activity.childOrganisation.data]} />
                ) : (
                  <HeaderImage id={activity?.organisation?.data?.image || activity?.object?.data?.image} />
                )}
              </Link>
              <HeaderInfo>
                <HeaderTitle>
                  {activity?.update ? (
                    <>
                      <Clickable>
                        <Link to={pathToOrganisationUpdate} post-cy="post-title">
                          {activity.organisation?.data?.name}
                        </Link>
                      </Clickable>
                      {memoizedIcons.arrowRight}
                      {activity?.object?.data?.name ? (
                        <Clickable>
                          <Link
                            to={pathToWebinarUpdate || pathToEventUpdate || pathToIncentiveUpdate}
                            post-cy="post-title"
                          >
                            {activity?.object?.data?.name}
                          </Link>
                        </Clickable>
                      ) : activity?.object?.collection === 'videos' ? (
                        t('(Deleted Webinar)')
                      ) : activity?.object?.collection === 'events' ? (
                        t('(Deleted Event)')
                      ) : (
                        activity?.object?.collection === 'incentives' && t('(Deleted Incentive)')
                      )}
                    </>
                  ) : (
                    <>
                      <Clickable>
                        <Link to={pathToOrganisation} post-cy="post-title">
                          {activity?.object?.data?.name}
                          {activity?.isPartnerPost && (
                            <>
                              {membership?.partnerOrganisation ? (
                                // Child
                                <>
                                  {membership.partnerOrganisation?.parentOrganisation &&
                                    membership.partnerOrganisation.parentOrganisation.activeTier && (
                                      <TierIcon
                                        position="top"
                                        tier={membership.partnerOrganisation.parentOrganisation.activeTier}
                                        iconSize={18}
                                        flatIcon
                                      />
                                    )}
                                </>
                              ) : (
                                // Parent
                                <>
                                  {membership?.organisation?.activeTier && (
                                    <TierIcon
                                      position="top"
                                      tier={(membership?.organisation?.activeTier as string) || ''}
                                      iconSize={18}
                                      flatIcon
                                    />
                                  )}
                                </>
                              )}
                            </>
                          )}
                        </Link>
                      </Clickable>
                      {activity.childOrganisation && (
                        <>
                          <span
                            style={{
                              fontWeight: 'normal',
                              margin: '0 5px',
                            }}
                          >
                            and
                          </span>
                          <Clickable>
                            <Link
                              to={encodePathParams(Routes.organisationProfile, {
                                vanityId: activity.childOrganisation.data.vanityId,
                              })}
                              post-cy="post-title"
                            >
                              {activity.childOrganisation.data.name}
                            </Link>
                          </Clickable>
                        </>
                      )}
                    </>
                  )}
                </HeaderTitle>
                <Info>
                  {(hasUpdatePostPermission || isHabloStaff) && allowDisplayImpressions && (
                    <>
                      <span
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          color: 'var(--color-gray3)',
                        }}
                      >
                        <Tooltip title={getViews(activity) + t(' impressions on this post')}>
                          {memoizedIcons.viewsIcon}
                          {getViews(activity)}
                        </Tooltip>
                        <span style={{ marginLeft: '3px' }}>{' · '}</span>
                      </span>
                    </>
                  )}
                  {activity?.post?.data && isNotInternalOrTargeted ? (
                    <Clickable>
                      <Link to={`${pathToOrganisation}/post/${activity.id}/userId/${organisationId}`}>
                        <CreatedTime
                          date={
                            activity?.post?.data?.scheduledAt ||
                            activity?.post?.data?.createdAt ||
                            dayjs.utc(activity?.time, 'YYYY-MM-DDTHH:mm:ss.SSSSSS').toISOString() ||
                            activity?.post?.created_at ||
                            activity?.webinar?.created_at ||
                            activity?.event?.created_at ||
                            activity?.incentive?.created_at ||
                            activity?.update?.created_at
                          }
                          condition="HeaderInfo"
                        />
                      </Link>
                    </Clickable>
                  ) : (
                    <CreatedTime
                      date={
                        activity?.post?.data?.scheduledAt ||
                        activity?.post?.data?.createdAt ||
                        dayjs.utc(activity?.time, 'YYYY-MM-DDTHH:mm:ss.SSSSSS').toISOString() ||
                        activity?.post?.created_at ||
                        activity?.webinar?.created_at ||
                        activity?.event?.created_at ||
                        activity?.incentive?.created_at ||
                        activity?.update?.created_at
                      }
                      condition="HeaderInfo"
                    />
                  )}
                  {isRegularPost ? (
                    <span
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        color: 'var(--color-gray3)',
                      }}
                    >
                      {' · '}
                      {activity?.post?.data?.postAudience?.isMyOrganisation ? (
                        <>
                          <Tooltip title={t('Sent only to Members of your Organisation')}>
                            <span>{memoizedIcons.audienceIcons.tourOperator}</span>
                          </Tooltip>
                        </>
                      ) : activity?.post?.data?.postAudience?.connectedOrganisations ? (
                        <Tooltip
                          title={
                            imPostAuthor
                              ? connectedOrganisations && selectedOrganisationNames && connectedOrganisations.length > 1
                                ? t('Targeted by you to Members of ') + text.join('')
                                : selectedOrganisationNames
                                ? t('Targeted by you to Members of ') + selectedOrganisationNames[0]
                                : t('Targeted by you to Members of ') +
                                  connectedOrganisations.length +
                                  t(' Connected Organisation(s)')
                              : t('Sent to Members of your Organisation')
                          }
                        >
                          <span data-cy={'targeted-post'}>
                            {memoizedIcons.audienceIcons.organisation}
                            {t('Targeted')}
                          </span>
                        </Tooltip>
                      ) : activity?.object?.data?.isPublic ? (
                        <Tooltip title={t('Public')}>
                          <span>{memoizedIcons.audienceIcons.globe}</span>
                        </Tooltip>
                      ) : (
                        <Tooltip title={t('Sent to Approved Followers of ') + activity?.object?.data?.name}>
                          <span>{memoizedIcons.audienceIcons.users}</span>
                        </Tooltip>
                      )}
                    </span>
                  ) : (
                    <>
                      <span style={{ color: 'var(--color-gray3)' }}>
                        {' · '}
                        {memoizedIcons.labelIcon}
                        {activity?.update?.data?.webinarId && t('Video update')}
                        {activity?.update?.data?.eventId && t('Event update')}
                        {activity?.update?.data?.incentiveId && t('Incentive update')}
                        {(activity?.webinar || activity?.event || activity?.incentive) && t('Invitation')}
                      </span>
                    </>
                  )}
                </Info>
              </HeaderInfo>
              <RightActions>
                {notAllowedToSavePost ? null : (
                  <Button
                    type="link"
                    loading={saveActionLoading}
                    className="save-action"
                    icon={
                      <Tooltip
                        title={t(feed.feedGroup === 'user_saved' || isPostSaved ? 'Post Saved' : 'Save this Post')}
                      >
                        <div>
                          {feed.feedGroup === 'user_saved' || isPostSaved ? (
                            <Icon
                              icon={Icons.bookmarked}
                              size={25}
                              color="var(--color-darkerblue)"
                              onClick={handleUnsavePost}
                            />
                          ) : (
                            <Icon
                              icon={Icons.bookmark}
                              size={25}
                              color="var(--color-darkerblue)"
                              onClick={handleSavePost}
                            />
                          )}
                        </div>
                      </Tooltip>
                    }
                  />
                )}
                {moreOptionsComponent}
              </RightActions>
            </>
          )}
          {postShow && <CreatePostModal />}
        </Header>
      </InView>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.activity.id === nextProps.activity.id &&
      prevProps.activity.reaction_counts?.views === nextProps.activity.reaction_counts?.views
    );
  },
);

export default StreamPostHeader;

const AudienceIcon = styled(Icon)`
  margin-top: -2px;
  margin-left: 3px;
  margin-right: 5px;
`;

const RightActions = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;

  .save-action {
    transition: transform 0.2s ease;
    margin-top: -3px;

    &:hover {
      transform: scale(1.08);
    }
  }

  @media screen and (max-width: 568px) {
    gap: 2px;
  }
`;
