import React from 'react';
import { action } from '@storybook/addon-actions';
import { Form, Modal } from 'antd';

import { MockApolloProvider } from '@utils/mocks/MockApolloProvider';
import { random } from '@utils/mocks/random';
import { GET_IMAGE_SIGNATURE } from '@components/Image/queries';
import { CreatePostForm } from './CreatePostForm';
import { mockOrganisation } from '@utils/mocks/mockOrganisation';
import { useTranslation } from 'react-i18next';
import { BasicProfileMembership } from '@src/routes/queries';
import { mockPartnerOrganisation } from '@src/utils/mocks/mockPartnerOrganisation';

function Wrapper({
  loading,
  memberships,
  image,
}: {
  loading: boolean;
  memberships: BasicProfileMembership[];
  image?: string;
}) {
  const { t } = useTranslation();
  return (
    <Modal title={t('CREATE A POST')} width={550} footer={null} open={true}>
      <MockApolloProvider mocks={[{ request: { query: GET_IMAGE_SIGNATURE }, result: { data: {} } }]}>
        <CreatePostForm
          form={Form.useForm()[0]}
          loading={loading}
          onFinish={() => Promise.resolve(action('CreatePost')())}
          memberships={memberships}
          post={{ image }}
          setShowScheduledPostModal={function (value: React.SetStateAction<boolean>): void {
            throw new Error('Function not implemented.');
          }}
          scheduledFormData={{
            dateTime: undefined,
            timeZone: '',
            errors: {
              dateTime: '',
              timeZone: '',
            },
            isScheduled: false,
          }}
          scheduledPostModal={false}
        />
      </MockApolloProvider>
    </Modal>
  );
}

const membership1: BasicProfileMembership = {
  id: '1',
  isPrimary: true,
  organisation: mockOrganisation(),
  partnerOrganisation: mockPartnerOrganisation(),
};
const membership2: BasicProfileMembership = {
  id: '2',
  isPrimary: false,
  organisation: mockOrganisation(),
  partnerOrganisation: mockPartnerOrganisation(),
};
const membership3: BasicProfileMembership = {
  id: '3',
  isPrimary: false,
  organisation: mockOrganisation(),
  partnerOrganisation: mockPartnerOrganisation(),
};

export default {
  component: Wrapper,
  title: 'Organisation/Post/Create',
};

export const withMemberships = () => <Wrapper loading={false} memberships={[membership1, membership2, membership3]} />;
export const withImage = () => (
  <Wrapper loading={false} memberships={[membership1, membership2, membership3]} image={random.image()} />
);
