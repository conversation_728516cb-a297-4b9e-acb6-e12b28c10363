import { useTranslation } from 'react-i18next';
import { Form, Modal } from 'antd';
import { useContext, useEffect, useState } from 'react';
import { useMutation } from '@apollo/client';
import { Store } from 'antd/lib/form/interface';
import { useHistory } from 'react-router-dom';
import {
  CREATE_POST,
  CreatePostQuery,
  CreatePostVariables,
  CREATE_STREAM_POST,
  CreateStreamPostQuery,
  RepostStreamPostVariables,
  REPOST_STREAM_POST,
  UpdateStreamPostQuery,
  UPDATE_STREAM_POST,
} from './queries';
import { CreatePostInput, CreateStreamPostInput, Organisation, PostType } from '@GraphQLTypes';
import { CreatePostForm } from './CreatePostForm';
import { encodePathParams } from '@utils/encodePathParams';
import { Routes } from '@src/routes/Routes';
import { emptyObject } from '@src/utils/emptyObject';
import { FeedContext } from '@src/routes/PrivateRoutesHandler';
import PostAudience from '../postAudience/PostAudience';
import { EditorState } from 'draft-js';
import { setCookie, getCookie } from '@utils/cookie';
import { useProfile } from '@src/routes/ProfileProvider';
import { SchedulePost } from '../schedulePost/schedulePost';
import { ITimezone } from 'react-timezone-select';
import moment from 'moment';
import 'moment-timezone';
import { Icons } from '@components/icons/Icon';
import { ButtonWithIcon } from '@components/Buttons/ButtonWithIcon';
import { openNotification } from '@src/utils/notification';
import { getPrimaryMembership } from '@src/utils/getOrganisationName';

type Props = {
  showModal: boolean;
  toggleModal(show: boolean): void;
  activity?: any;
  isUpdate?: boolean;
};

export function CreatePost({ showModal, toggleModal, activity, isUpdate }: Props) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const history = useHistory();
  const { profile } = useProfile();
  const primaryMembership = getPrimaryMembership(profile);
  const { feedContext, showAllScheduledPostsList, setShowAllScheduledPostsList } = useContext(FeedContext);
  const [postMentions, setPostMentions] = useState<any[]>([]);
  const [connectedOrganisations, setConnectedOrganisation] = useState<Array<string>>([]);
  const [selectedOrganisationNames, setSelectedOrganisationNames] = useState<Array<string>>([]);
  const [postAudienceModal, setShowPostAudienceModal] = useState<boolean>(false);
  const [scheduledPostModal, setShowScheduledPostModal] = useState<boolean>(false);
  const [showAllScheduleModal, setShowAllSchedueModal] = useState<boolean>(false);
  const [showScheduledPostDetail, setShowScheduledPostDetail] = useState<boolean>(false);
  const [editSchedule, setEditSchedule] = useState<boolean>(false);
  const [canSeePost, setCanSeePost] = useState<string>('followers');
  const [selectedOption, setSelectedOption] = useState<string>('followers');
  const [organisation, setOrganisation] =
    useState<
      Pick<
        Organisation,
        | 'id'
        | 'name'
        | 'image'
        | 'isPublic'
        | 'privacy'
        | 'type'
        | 'isPaid'
        | 'hasClubHabloSubscription'
        | 'isPartner'
        | 'childOrganisationId'
        | 'postsLimit'
        | 'postsUsedThisMonth'
      >
    >();
  const [organisationOptions, setOrganisationOptions] = useState<any[]>();
  const [postText, setPostText] = useState('');
  const [postContent, setPostContent] = useState(EditorState.createEmpty());
  const [isDisabled, setIsDisabled] = useState<boolean>(true);
  const [formValues, setFormValues] = useState<any>(null);
  const [scheduledFormData, setScheduledFormData] = useState<{
    dateTime: Date | string;
    timeZone: ITimezone;
    errors: { dateTime: string; timeZone: string };
    isScheduled: boolean;
  }>({
    dateTime: '',
    timeZone: Intl?.DateTimeFormat()?.resolvedOptions()?.timeZone,
    errors: { dateTime: '', timeZone: '' },
    isScheduled: false,
  });

  const [createPost, { loading: createPostLoading }] = useMutation<CreatePostQuery, CreatePostVariables>(CREATE_POST);
  const [createStreamPost, { loading: createStreamPostLoading }] =
    useMutation<CreateStreamPostQuery>(CREATE_STREAM_POST);
  const [updateStreamPost, { loading: updateStreamPostLoading }] =
    useMutation<UpdateStreamPostQuery>(UPDATE_STREAM_POST);
  const [repostStreamPost, { loading: repostStreamPostLoading }] = useMutation<{}, RepostStreamPostVariables>(
    REPOST_STREAM_POST,
  );

  useEffect(() => {
    setOrganisation(
      profile?.memberships.find((m) => m.isPrimary)?.organisation || profile?.memberships[0]?.organisation,
    );
    const organisations = profile?.memberships.map(({ organisation }) => ({
      value: organisation?.id,
      label: organisation?.name,
      organisation,
    }));
    const parentOrganisation = profile?.memberships
      .map(({ partnerOrganisation }) => {
        if (partnerOrganisation?.parentOrganisation) {
          return {
            value: partnerOrganisation.parentOrganisation.id,
            label: (
              <span>
                <strong>{partnerOrganisation.parentOrganisation.name} </strong>
                and <strong>{primaryMembership?.organisation?.name}</strong>
              </span>
            ),
            organisation: {
              ...partnerOrganisation.parentOrganisation,
              isPartner: true,
              postsLimit: partnerOrganisation.postsLimit,
              postsUsedThisMonth: partnerOrganisation.postsUsedThisMonth,
              childOrganisationId: partnerOrganisation.childOrgId,
            },
          };
        }
      })
      .filter((item) => item);
    setOrganisationOptions([...organisations, ...parentOrganisation]);
  }, [profile?.memberships]);

  useEffect(() => {
    if (connectedOrganisations.length || selectedOrganisationNames.length) {
      setCanSeePost('followers');
      setSelectedOption('followers');
      setConnectedOrganisation([]);
      setSelectedOrganisationNames([]);
    }
    const cookie = getCookie(profile?.id);
    if (cookie && cookie !== 'undefined' && cookie !== 'null' && cookie !== '{}') {
      let cookieJson = JSON.parse(cookie);
      if (cookieJson.isFollowers) {
        setCanSeePost('followers');
        setSelectedOption('followers');
      } else if (cookieJson.isMyOrganisation) {
        setCanSeePost('my-organisation');
        setSelectedOption('my-organisation');
      } else {
        setCanSeePost('');
        setSelectedOption('');
        setConnectedOrganisation(cookieJson.connectedOrganisations);
        setSelectedOrganisationNames(cookieJson.selectedOrganisationNames);
      }
    }
  }, [organisation]);

  useEffect(() => {
    setShowScheduledPostModal(showAllScheduledPostsList);
    setShowAllSchedueModal(showAllScheduledPostsList);

    return () => {
      setShowScheduledPostModal(false);
      setShowAllSchedueModal(false);
    };
  }, [showAllScheduledPostsList]);

  const handleCloseModal = () => {
    toggleModal(false);
    setShowAllScheduledPostsList(false);
  };

  async function onFinish(values: Store) {
    if (activity) {
      if (isUpdate) {
        let updatedPostText = values.text;
        postMentions?.map((mention) => {
          updatedPostText = updatedPostText.replace(`@[${mention?.name}](${mention?.name})`, mention?.name);
        });
        if (values?.image === undefined) values.image = '';
        if (values?.imageHeight === undefined) values.imageHeight = 0;
        if (values?.imageWidth === undefined) values.imageWidth = 0;
        if (values?.imageFormat === undefined) values.imageFormat = '';
        if (values?.video === undefined) values.video = '';
        if (values?.videoHeight === undefined) values.videoHeight = 0;
        if (values?.videoWidth === undefined) values.videoWidth = 0;
        if (values?.videoFormat === undefined) values.videoFormat = '';
        if (values?.document === undefined) values.document = '';
        if (values?.documentSize === undefined) values.documentSize = '';
        if (values?.documentFormat === undefined) values.documentFormat = '';
        if (values?.documentUrl === undefined) values.documentUrl = '';
        if (values?.documentName === undefined) values.documentName = '';
        if (values?.multipleImages === undefined) delete values.multipleImages;
        if (values?.multipleImages !== undefined && values?.multipleImages.length > 0) {
          values.multipleImages = values?.multipleImages.map((item: any) => ({
            image: item.image || item.response?.public_id,
            imageHeight: item.imageHeight || item.response.height,
            imageWidth: item.imageWidth || item.response.width,
            imageFormat: item.imageFormat || item.response.format,
            url: item.url || item.response.url,
            status: item.status,
          }));
        }

        const obj = {
          ...values,
          text: updatedPostText,
        };

        if (organisation?.isPartner) {
          Object.assign(obj, { ['parentOrgId']: organisation?.id });
        }
        await updateStreamPost({
          variables: {
            postId: activity?.post?.id,
            postData: {
              ...obj,
              postId: activity?.post?.id,
              users: postMentions?.filter((item: { __typename: string }) => item.__typename === 'Profile'),
              organisations: postMentions?.filter((item: { __typename: string }) => item.__typename === 'Organisation'),
            } as CreateStreamPostInput,
          },
        });
      } else {
        const postAuthorId = activity?.post?.data?.profileId;
        const postId = activity?.post?.data?.postId;
        const organisationId = activity?.post?.data?.organisationId;
        delete values?.postAudience;
        if (canSeePost === 'followers') {
          Object.assign(values, { ['postAudience']: { isFollowers: true } });
        } else if (canSeePost === 'my-organisation') {
          Object.assign(values, { ['postAudience']: { isMyOrganisation: true } });
        } else {
          Object.assign(values, {
            ['postAudience']: {
              connectedOrganisations: connectedOrganisations,
              selectedOrganisationNames: selectedOrganisationNames,
            },
          });
        }

        await repostStreamPost({
          variables: {
            activityId: activity.id,
            organisationId: organisation?.id || '',
            postData: {
              ...values,
              users: postMentions?.filter((item: { __typename: string }) => item.__typename === 'Profile'),
              organisations: postMentions?.filter((item: { __typename: string }) => item.__typename === 'Organisation'),
              organisationId: organisation?.id,
            },
            postAuthorId: postAuthorId || '',
            origOrganisationId: organisationId || '',
            postId: postId || '',
          },
        });
      }
    } else {
      delete values?.postAudience;
      if (canSeePost === 'followers') {
        Object.assign(values, { ['postAudience']: { isFollowers: true } });
      } else if (canSeePost === 'my-organisation') {
        Object.assign(values, { ['postAudience']: { isMyOrganisation: true } });
      } else {
        Object.assign(values, {
          ['postAudience']: {
            connectedOrganisations: connectedOrganisations,
            selectedOrganisationNames: selectedOrganisationNames,
          },
        });
      }
      let postValues = { ...values };
      delete postValues.documentUrl;
      delete postValues.documentSize;
      delete postValues.documentFormat;
      delete postValues.documentName;
      postValues.mediaHeight = postValues.type === PostType.Image ? postValues.imageHeight : postValues.videoHeight;
      postValues.mediaWidth = postValues.type === PostType.Image ? postValues.imageWidth : postValues.videoWidth;
      postValues.mediaFormat = postValues.type === PostType.Image ? postValues.imageFormat : postValues.videoFormat;
      delete postValues.imageHeight;
      delete postValues.imageWidth;
      delete postValues.imageFormat;
      delete postValues.videoHeight;
      delete postValues.videoWidth;
      delete postValues.videoFormat;

      const date = moment(scheduledFormData?.dateTime).format('YYYY-MM-DDTHH:mm:ss');
      const zone =
        typeof scheduledFormData?.timeZone === 'string'
          ? scheduledFormData?.timeZone
          : typeof scheduledFormData?.timeZone === 'object'
          ? scheduledFormData?.timeZone?.value
          : '';

      const scheduledUtcDate = moment.tz(date, zone).utc().format();
      if (scheduledFormData?.isScheduled) {
        postValues = {
          ...postValues,
          scheduledAt: scheduledUtcDate,
        };
      }
      if (organisation?.isPartner) {
        Object.assign(postValues, { ['parentOrgId']: organisation?.id });
        Object.assign(values, { ['parentOrgId']: organisation?.id });
        delete postValues?.postAudience;
        delete values?.postAudience;
      }
      const res = await createPost({
        variables: {
          postData: {
            ...postValues,
            organisationId: organisation?.isPartner ? organisation.childOrganisationId : organisation?.id,
            status: scheduledFormData?.isScheduled ? 'Scheduled' : 'Live',
          } as CreatePostInput,
        },
      });
      const streamRes = await createStreamPost({
        variables: {
          postData: {
            ...values,
            scheduledAt: scheduledFormData?.isScheduled ? scheduledUtcDate : null,
            organisationId: organisation?.isPartner ? organisation.childOrganisationId : organisation?.id,
            postId: res.data?.createPost.id,
            users: postMentions?.filter((item: { __typename: string }) => item.__typename === 'Profile'),
            organisations: postMentions?.filter((item: { __typename: string }) => item.__typename === 'Organisation'),
          } as CreateStreamPostInput,
        },
      });
      setCookie(profile.id, JSON.stringify(postValues.postAudience), 2);
      if (streamRes) {
        // @ts-ignore
        window.latestPostActivityId = streamRes?.activityId;
        // @ts-ignore
        if (window.lastRemovedPostActivityId) delete window.lastRemovedPostActivityId;
      }
    }
    if (emptyObject(feedContext) === false) feedContext.refresh();
    if (!isUpdate) history.push(encodePathParams(Routes.home));
    handleCloseModal();
    window.dispatchEvent(new Event('updateProfile'));
    form.resetFields();
    if (scheduledFormData?.isScheduled) {
      openNotification({
        message: <b>{t('Post Scheduled.')}</b>,
        description: t('View all of your scheduled posts.'),
        onClick: () => {
          toggleModal(true);
          setShowAllScheduledPostsList(true);
        },
        icon: (
          <ButtonWithIcon
            icon={Icons.check}
            style={{ border: 'none', backgroundColor: '#0BD471', color: '#ffffff', height: '30px', width: '30px' }}
          />
        ),
      });
    }
  }

  if (!organisationOptions) {
    return null;
  }

  return (
    <Modal
      title={
        isUpdate
          ? t('EDIT POST')
          : activity
          ? t('REPOST')
          : postAudienceModal
          ? 'POST AUDIENCE'
          : showAllScheduleModal
          ? t('SCHEDULED POSTS')
          : showScheduledPostDetail
          ? t('SCHEDULED POST')
          : scheduledPostModal
          ? t('SCHEDULE POST')
          : t('CREATE A POST')
      }
      width={550}
      footer={null}
      open={showModal}
      maskClosable={false}
      destroyOnClose={false}
      className={'ant-modal-with-overflow'}
      onCancel={() => {
        if (form.isFieldsTouched()) {
          Modal.confirm({
            centered: true,
            content: t('Your draft post will be lost. Are you sure you want to discard without posting?'),
            cancelText: t('Cancel'),
            okText: t('Ok'),
            onOk: () => {
              form.resetFields();
              handleCloseModal();
            },
          });
        } else {
          handleCloseModal();
        }
      }}
    >
      {postAudienceModal ? (
        <PostAudience
          organisation={organisation}
          setShowPostAudienceModal={setShowPostAudienceModal}
          canSeePost={canSeePost}
          setCanSeePost={setCanSeePost}
          setSelectedOption={setSelectedOption}
          connectedOrganisations={connectedOrganisations}
          setConnectedOrganisation={setConnectedOrganisation}
          memberships={profile.memberships}
          selectedOrganisationNames={selectedOrganisationNames}
          setSelectedOrganisationNames={setSelectedOrganisationNames}
        />
      ) : scheduledPostModal ? (
        <SchedulePost
          setShowScheduledPostModal={setShowScheduledPostModal}
          setShowAllSchedueModal={setShowAllSchedueModal}
          showAllScheduleModal={showAllScheduleModal}
          setShowScheduledPostDetail={setShowScheduledPostDetail}
          showScheduledPostDetail={showScheduledPostDetail}
          organisation={organisation}
          setOrganisation={setOrganisation}
          organisationOptions={organisationOptions}
          setScheduledFormData={setScheduledFormData}
          scheduledFormData={scheduledFormData}
          editSchedule={editSchedule}
          setEditSchedule={setEditSchedule}
        />
      ) : (
        <CreatePostForm
          organisation={organisation}
          setOrganisation={setOrganisation}
          organisationOptions={organisationOptions}
          canSeePost={canSeePost}
          selectedOption={selectedOption}
          form={form}
          loading={createPostLoading || createStreamPostLoading || updateStreamPostLoading || repostStreamPostLoading}
          onFinish={onFinish}
          memberships={profile.memberships}
          setPostMentions={setPostMentions}
          activity={activity}
          isUpdate={isUpdate}
          setShowPostAudienceModal={setShowPostAudienceModal}
          setShowScheduledPostModal={setShowScheduledPostModal}
          selectedOrganisationNames={selectedOrganisationNames}
          setConnectedOrganisation={setConnectedOrganisation}
          setSelectedOrganisationNames={setSelectedOrganisationNames}
          postText={postText}
          setPostText={setPostText}
          postContent={postContent}
          setPostContent={setPostContent}
          isDisabled={isDisabled}
          setIsDisabled={setIsDisabled}
          scheduledFormData={scheduledFormData}
          scheduledPostModal={scheduledPostModal}
          editSchedule={editSchedule}
          setEditSchedule={setEditSchedule}
          savedFormValues={formValues}
          onSchedule={(values) => {
            setFormValues(values);
            setShowScheduledPostModal(true);
          }}
        />
      )}
    </Modal>
  );
}
