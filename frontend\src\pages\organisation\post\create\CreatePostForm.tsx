import { Store } from 'antd/lib/form/interface';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import React, { useEffect, useState } from 'react';
import { Button, Col, Form, Input, Row, Select, Space, Typography, Tooltip, Alert } from 'antd';
import { Avatar } from '@components/Image/Image';
import { FormInstance } from 'antd/lib/form';
import { Post, PostType, Organisation, OrganisationType } from '@GraphQLTypes';
import { BORDER_RADIUS, GUTTER_MD } from '@theme';
import { ImageUpload } from '@components/Image/ImageUpload';
import { ButtonWithIcon } from '@components/Buttons/ButtonWithIcon';
import Icon, { Icons } from '@components/icons/Icon';
import { ButtonRemove } from '@components/Buttons/ButtonRemove';
import { VideoUpload } from '@src/components/Video/VideoUpload';
import { Video } from '@src/components/Video/Video';
import { Mention } from '@src/components/Mention/Mention';
import PostContent, { RepostContainer } from '../postContent/PostContent';
import { Header, HeaderImage, HeaderInfo, HeaderTitle, Info } from '../styles';
import { CreatedTime } from '@src/components/CreatedTime';
import { hasPermission, OrganisationActions } from '../../permissions';
import { DocUpload } from '@src/components/Doc/DocUpload';
import { Doc } from '@src/components/Doc/Doc';
import MultiImageUpload from '@components/Image/MultiImageUpload';
import { ITimezone } from 'react-timezone-select';
import moment from 'moment-timezone';
import { BasicProfileMembership } from '@src/routes/queries';
import { PostProfileImages } from '@src/components/PostProfileImages';
import { useProfile } from '@src/routes/ProfileProvider';
import { getPrimaryMembership } from '@src/utils/getOrganisationName';
import { ContainerCentered } from '@src/components/Layout/Container';
import { Loader } from '@src/components/Loader';

type PostFormProps = {
  form: FormInstance;
  loading: boolean;
  memberships: BasicProfileMembership[];
  onFinish(values: Store): Promise<any>;
  post?: Partial<Post>;
  setPostMentions?: any;
  activity?: any;
  isUpdate?: boolean;
  setShowPostAudienceModal?: React.Dispatch<React.SetStateAction<boolean>>;
  setShowScheduledPostModal: React.Dispatch<React.SetStateAction<boolean>>;
  canSeePost?: string;
  selectedOption?: string;
  selectedOrganisationNames?: Array<string>;
  setConnectedOrganisation?: React.Dispatch<React.SetStateAction<Array<string>>>;
  setSelectedOrganisationNames?: React.Dispatch<React.SetStateAction<Array<string>>>;
  organisation?:
    | Pick<
        Organisation,
        | 'id'
        | 'name'
        | 'image'
        | 'isPublic'
        | 'privacy'
        | 'type'
        | 'isPaid'
        | 'hasClubHabloSubscription'
        | 'isPartner'
        | 'childOrganisationId'
        | 'postsLimit'
        | 'postsUsedThisMonth'
      >
    | undefined;
  organisationOptions?: any[];
  setOrganisation?: React.Dispatch<React.SetStateAction<any>>;
  postText?: string;
  setPostText?: React.Dispatch<React.SetStateAction<string>>;
  postContent?: any;
  setPostContent?: any;
  isDisabled?: boolean;
  setIsDisabled?: React.Dispatch<React.SetStateAction<boolean>>;
  scheduledFormData: {
    dateTime: Date | string | undefined;
    timeZone: ITimezone;
    errors: {
      dateTime: string;
      timeZone: string;
    };
    isScheduled: boolean;
  };
  scheduledPostModal: boolean;
  editSchedule?: boolean;
  setEditSchedule?: React.Dispatch<React.SetStateAction<boolean>>;
  savedFormValues?: any;
  onSchedule?: (values: any) => void;
};

export function CreatePostForm({
  form,
  loading,
  onFinish,
  post,
  setPostMentions,
  activity,
  isUpdate,
  setShowPostAudienceModal,
  setShowScheduledPostModal,
  selectedOption,
  selectedOrganisationNames,
  organisation,
  setOrganisation,
  organisationOptions,
  postText,
  setPostText,
  postContent,
  setPostContent,
  isDisabled,
  setIsDisabled,
  scheduledFormData,
  scheduledPostModal,
  setEditSchedule,
  savedFormValues,
  onSchedule,
}: PostFormProps) {
  const { t } = useTranslation();
  const { profile } = useProfile();
  const primaryMembership = getPrimaryMembership(profile);
  const [image, setImage] = useState<string | undefined>(
    activity && isUpdate ? activity.post?.data?.image : post?.image,
  );
  const [imageIsLoading, setImageIsLoading] = useState(false);
  const [videoIsLoading, setVideoIsLoading] = useState(false);
  const [docIsLoading, setDocIsLoading] = useState(false);
  const [video, setVideo] = useState<string | undefined | null>(
    activity && isUpdate ? activity.post?.data?.video : post?.image,
  );
  const [document, setDocument] = useState<string | undefined | null>(
    activity && isUpdate ? activity.post?.data?.document : post?.image,
  );
  const [multipleImage, setMultipleImage] = useState<object[] | undefined>(
    activity && isUpdate ? activity?.post.data?.multipleImages : post?.image,
  );
  const getViews = (activity: any) => {
    const viewsOld = activity?.post?.data?.totalViews || 0;
    const viewsNew = activity?.reaction_counts?.views || 0;

    return `${viewsOld + viewsNew}`;
  };

  const showAddActionsImageVideoDoc = !image && !video && !document && !multipleImage;
  const hasUpdatePostPermission = activity && hasPermission(OrganisationActions.updatePost, activity.permissions);

  const [imageWidth, setImageWidth] = useState<number | undefined | null>(
    activity && isUpdate ? activity.post?.data?.imageWidth : '',
  );
  const [imageHeight, setImageHeight] = useState<number | undefined | null>(
    activity && isUpdate ? activity.post?.data?.imageHeight : '',
  );
  const [imageFormat, setImageFormat] = useState<string | undefined | null>(
    activity && isUpdate ? activity.post?.data?.imageFormat : '',
  );

  const [videoWidth, setVideoWidth] = useState<number | undefined | null>(
    activity && isUpdate ? activity.post?.data?.videoWidth : '',
  );
  const [videoHeight, setVideoHeight] = useState<number | undefined | null>(
    activity && isUpdate ? activity.post?.data?.videoHeight : '',
  );
  const [videoFormat, setVideoFormat] = useState<string | undefined | null>(
    activity && isUpdate ? activity.post?.data?.videoFormat : '',
  );

  const [docUrl, setDocUrl] = useState(activity && isUpdate ? activity.post?.data?.documentUrl : '');
  const [docSize, setDocSize] = useState<string | undefined | null>(
    activity && isUpdate ? activity.post?.data?.documentSize : '',
  );
  const [docName, setDocName] = useState<string | undefined | null>(
    activity && isUpdate ? activity.post?.data?.documentName : '',
  );
  const [docFormat, setDocFormat] = useState<string | undefined | null>(
    activity && isUpdate ? activity.post?.data?.documentFormat : '',
  );
  const [isImageCategory, setIsImageCategory] = useState<string | undefined>(
    activity && isUpdate ? activity.post?.data?.imageCategory : '',
  );

  const limitPostingFunctionality = organisation?.type === OrganisationType.Destination && !organisation?.isPaid;

  useEffect(() => {
    form.setFieldsValue({ ['text']: postText });
  }, [postText]);

  useEffect(() => {
    if (!scheduledPostModal && savedFormValues) {
      form.setFieldsValue(savedFormValues);
      // Also restore local state
      setMultipleImage(savedFormValues?.multipleImages);
      setImage(savedFormValues?.image);
      setImageFormat(savedFormValues?.imageFormat);
      setImageHeight(savedFormValues?.imageHeight);
      setImageWidth(savedFormValues?.imageWidth);

      setVideo(savedFormValues?.video);
      setVideoFormat(savedFormValues?.videoFormat);
      setVideoHeight(savedFormValues?.videoHeight);
      setVideoWidth(savedFormValues?.videoWidth);
      setIsImageCategory(savedFormValues?.imageCategory);

      setDocument(savedFormValues?.document);
      setDocFormat(savedFormValues?.documentFormat);
      setDocName(savedFormValues?.documentName);
      setDocSize(savedFormValues?.documentSize);
      setDocUrl(savedFormValues?.documentUrl);
    }
  }, [scheduledPostModal, savedFormValues]);

  const convertBytes = function (bytes: number): string {
    const sizes: string[] = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

    if (bytes === 0) {
      return '';
    }

    const i: number = Math.floor(Math.log(bytes) / Math.log(1024));

    if (i === 0) {
      return bytes + sizes[i];
    }
    return (bytes / Math.pow(1024, i)).toFixed(1) + sizes[i];
  };

  const onScheduledButtonClick = () => {
    try {
      const values = form.getFieldsValue(true);
      onSchedule && onSchedule(values);
    } catch (err: any) {
      console.error(err);
    }
  };

  const getAudienceValue = (selectedOrganisationNames: string[]) =>
    `${selectedOrganisationNames[0]} ${
      selectedOrganisationNames.length > 1 ? `${t('and')} ${selectedOrganisationNames.length - 1} ${t('other(s)')}` : ''
    }`;

  if (!organisation) {
    return (
      <ContainerCentered>
        <Loader useGIF={true} />
      </ContainerCentered>
    );
  }

  const hasReachedPostLimit =
    organisation?.isPartner &&
    organisation?.postsUsedThisMonth &&
    organisation?.postsLimit &&
    Number(organisation.postsUsedThisMonth) >= Number(organisation.postsLimit);

  const postsRemaining =
    Number(organisation?.postsLimit ?? '0') - Number(organisation?.postsUsedThisMonth ?? '0') <= 0
      ? 0
      : Number(organisation?.postsLimit ?? '0') - Number(organisation?.postsUsedThisMonth ?? '0');

  return (
    <Form form={form} size="middle" onFinish={onFinish}>
      <Form.Item name="type" noStyle={true} initialValue={isUpdate ? activity.post?.data?.type : PostType.Normal}>
        <Input hidden={true} />
      </Form.Item>
      <Form.Item name="imageCategory" noStyle={true}>
        <Input hidden={true} />
      </Form.Item>
      {!isUpdate ? (
        <>
          <Row gutter={12}>
            <Col span={24}>
              <Form.Item
                name="organisationId"
                initialValue={organisation?.id}
                style={{ marginBottom: 0 }}
                rules={[{ required: true, message: t('Please choose an organisation to post as.') }]}
              >
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: organisation?.isPartner ? 10 : 20 }}>
                  <ImageContainer>
                    {organisation?.isPartner ? (
                      <PostProfileImages profiles={[organisation, primaryMembership?.organisation]} />
                    ) : (
                      <Avatar id={organisation?.image} width={48} />
                    )}
                  </ImageContainer>
                  <Row gutter={20} style={{ width: '100%' }}>
                    <Col md={organisation?.isPartner ? 24 : 12} sm={24} xs={24}>
                      <SubTitle>{t('Post As')}</SubTitle>
                      <OrganisationSelect
                        bordered={false}
                        suffixIcon={<Icon icon={Icons.chevronDown_blue} style={{ width: '12px' }} />}
                        dropdownMatchSelectWidth={false}
                        disabled={organisationOptions && organisationOptions.length <= 1}
                        value={organisation?.id}
                        options={organisationOptions}
                        onSelect={(id, option) => {
                          setOrganisation && setOrganisation(option.organisation);
                        }}
                      />
                    </Col>
                    {!organisation?.isPartner && (
                      <Col md={12} sm={24} xs={24}>
                        <SubTitle>{t('Post Audience')}</SubTitle>
                        {selectedOrganisationNames && selectedOrganisationNames.length > 0 ? (
                          <Tooltip
                            title={
                              t('This post will only be sent to Members of:') +
                              ' ' +
                              selectedOrganisationNames.join(', ')
                            }
                          >
                            <PostAudienceInput
                              onFocus={() => {
                                setShowPostAudienceModal && setShowPostAudienceModal(true);
                              }}
                              readOnly
                              value={getAudienceValue(selectedOrganisationNames)}
                            />
                          </Tooltip>
                        ) : (
                          <PostAudienceSelect
                            onFocus={() => {
                              setShowPostAudienceModal && setShowPostAudienceModal(true);
                            }}
                            value={selectedOption || 'followers'}
                            suffixIcon={<Icon icon={Icons.chevronDown_blue} style={{ width: '12px' }} />}
                            size="middle"
                            style={{ width: '100%' }}
                            data-cy={'post-audience-dropdown'}
                          >
                            <Select.Option value="followers">{getFollowersPostAudienceText()}</Select.Option>
                            <Select.Option value="my-organisation">{t('My Organisation')}</Select.Option>
                          </PostAudienceSelect>
                        )}
                      </Col>
                    )}
                  </Row>
                </div>
              </Form.Item>
            </Col>
          </Row>
        </>
      ) : (
        <Form.Item
          name="organisationId"
          initialValue={organisation?.id}
          rules={[{ required: true, message: t('Please choose an organisation to post as.') }]}
          style={{ height: 0, margin: 0 }}
        />
      )}
      {scheduledFormData?.dateTime && scheduledFormData?.timeZone ? (
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px', columnGap: '6px' }}>
          <Icon icon={Icons.clock_grey} style={{ width: '16px' }} />
          <span>
            {`Posting on ${moment(scheduledFormData?.dateTime).format('dddd, MMMM Do YYYY, [at] hh:mm A')} (GMT ${
              typeof scheduledFormData?.timeZone === 'string'
                ? moment().tz(scheduledFormData?.timeZone).format('Z')
                : typeof scheduledFormData?.timeZone === 'object'
                ? moment().tz(scheduledFormData?.timeZone?.value).format('Z')
                : ''
            })`}
          </span>
          <span
            style={{ cursor: 'pointer', color: '#0093C7' }}
            onClick={() => {
              setEditSchedule && setEditSchedule(true);
              setShowScheduledPostModal(true);
            }}
          >
            Edit
          </span>
        </div>
      ) : (
        <></>
      )}
      {limitPostingFunctionality && (
        <Alert
          message={'No active subscription found for ' + organisation?.name + '. Posting will soon be limited.'}
          type="warning"
          showIcon
          style={{ marginBottom: organisation?.isPartner ? 10 : 20 }}
        />
      )}

      {organisation?.isPartner && (
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px', columnGap: '6px' }}>
          <Icon icon={Icons.globe} size={20} />
          <span style={{ fontSize: 14, fontWeight: 500 }}>{`Sent to Followers of ${organisation.name}`}</span>
        </div>
      )}

      <Form.Item
        name="text"
        rules={[
          { required: true, message: t('What do you want to share?') },
          { max: 1024, message: t('Too long. Max length: 1024') },
        ]}
      >
        <Mention
          isUpdate={isUpdate}
          placeholder={activity ? 'Add a comment...' : 'What do you want to share?'}
          setPostText={setPostText}
          setPostMentions={setPostMentions}
          editorStoreData={activity && isUpdate && activity?.post?.data}
          postText={postText}
          postContentObject={postContent}
          setPostContentObject={setPostContent}
          setIsDisabled={setIsDisabled}
        />
      </Form.Item>
      {!activity || (activity && isUpdate && !activity?.repostData) ? (
        <>
          <SubTitle style={{ marginBottom: '5px' }}>
            {isUpdate ? t('Added To Your Post') : t('Add To Your Post')}
          </SubTitle>

          {(image || multipleImage) && (
            <>
              <Form.Item name="image" initialValue={image}>
                <div style={{ position: 'relative' }}>
                  {multipleImage && (
                    <MultiImageUpload
                      loading={imageIsLoading}
                      setLoading={setImageIsLoading}
                      imageData={multipleImage}
                      setCategory={setIsImageCategory}
                      onChange={async (image: any) => {
                        if (image.length === 0) {
                          setImage(undefined);
                          setMultipleImage(undefined);
                          form.setFieldsValue({ image: undefined });
                          form.setFieldsValue({ imageCategory: undefined });
                          form.setFieldsValue({ type: PostType.Normal });
                          form.setFieldsValue({ multipleImages: undefined });
                          form.setFieldsValue({ imageWidth: '' });
                          form.setFieldsValue({ imageHeight: '' });
                          form.setFieldsValue({ imageFormat: '' });
                          setIsDisabled && setIsDisabled(false);
                          return;
                        }
                        if (image.length === 1) {
                          form.setFieldsValue({ imageCategory: 'Single' });
                        } else {
                          form.setFieldsValue({ imageCategory: 'Multiple' });
                        }
                        setImage(image[0].image || image[0].response.public_id);
                        form.setFieldsValue({ image: image[0].image || image[0].response.public_id });
                        form.setFieldsValue({ imageWidth: image[0].imageWidth || image[0].response.width });
                        form.setFieldsValue({ imageHeight: image[0].imageHeight || image[0].response.height });
                        form.setFieldsValue({ imageFormat: image[0].imageFormat || image[0].response.format });
                        let multipleImagesArray = image.map((item: any) => ({
                          image: item.image || item.response?.public_id,
                          imageHeight: item.imageHeight || item.response.height,
                          imageWidth: item.imageWidth || item.response.width,
                          imageFormat: item.imageFormat || item.response.format,
                          url: item.url || item.response.url,
                          status: item.status,
                        }));
                        setMultipleImage(multipleImagesArray);
                        form.setFieldsValue({ type: PostType.Image });
                        form.setFieldsValue({ multipleImages: multipleImagesArray });
                        setIsDisabled && setIsDisabled(false);
                        // }
                      }}
                    />
                  )}
                </div>
              </Form.Item>
              <Form.Item name="imageWidth" noStyle={true} initialValue={imageWidth}>
                <Input hidden={true} />
              </Form.Item>
              <Form.Item name="imageHeight" noStyle={true} initialValue={imageHeight}>
                <Input hidden={true} />
              </Form.Item>
              <Form.Item name="imageFormat" noStyle={true} initialValue={imageFormat}>
                <Input hidden={true} />
              </Form.Item>
              <Form.Item name="multipleImages" noStyle={true} initialValue={multipleImage}>
                <Input hidden={true} />
              </Form.Item>
            </>
          )}

          {video && (
            <>
              <Form.Item name="video" initialValue={video}>
                <div style={{ position: 'relative' }}>
                  <ButtonRemove
                    onClick={() => {
                      setVideo(undefined);
                      form.setFieldsValue({ video: undefined });
                      form.setFieldsValue({ type: PostType.Normal });
                      form.setFieldsValue({ videoWidth: '' });
                      form.setFieldsValue({ videoHeight: '' });
                      form.setFieldsValue({ videoFormat: '' });
                      setIsDisabled && setIsDisabled(false);
                    }}
                  />
                  <VideoPreview isCreate={true} id={video} />
                </div>
              </Form.Item>
              <Form.Item name="videoWidth" noStyle={true} initialValue={videoWidth}>
                <Input hidden={true} />
              </Form.Item>
              <Form.Item name="videoHeight" noStyle={true} initialValue={videoHeight}>
                <Input hidden={true} />
              </Form.Item>
              <Form.Item name="videoFormat" noStyle={true} initialValue={videoFormat}>
                <Input hidden={true} />
              </Form.Item>
            </>
          )}

          {document && (
            <>
              <Form.Item name="document" initialValue={document}>
                <div style={{ position: 'relative' }}>
                  <DocumentButtonRemove
                    onClick={() => {
                      setDocument(undefined);
                      form.setFieldsValue({ document: undefined });
                      form.setFieldsValue({ type: PostType.Normal });
                      form.setFieldsValue({ documentUrl: '' });
                      form.setFieldsValue({ documentSize: '' });
                      form.setFieldsValue({ documentFormat: '' });
                      form.setFieldsValue({ documentFormat: '' });
                      form.setFieldsValue({ documentName: '' });
                      setIsDisabled && setIsDisabled(false);
                    }}
                  />
                  <DocPreview
                    id={document}
                    url={docUrl}
                    size={docSize}
                    name={docName}
                    format={docFormat}
                    isCreate={true}
                  />
                </div>
              </Form.Item>
              <Form.Item name="documentUrl" noStyle={true} initialValue={docUrl}>
                <Input hidden={true} />
              </Form.Item>
              <Form.Item name="documentSize" noStyle={true} initialValue={docSize}>
                <Input hidden={true} />
              </Form.Item>
              <Form.Item name="documentFormat" noStyle={true} initialValue={docFormat}>
                <Input hidden={true} />
              </Form.Item>
              <Form.Item
                name="documentName"
                label={t('Document Title')}
                rules={[
                  { required: true, message: t('Please add an document title.') },
                  { max: 90, message: t('Document name is too long. Max length: 90') },
                ]}
                labelCol={{ span: 24 }}
                initialValue={isUpdate && isDisabled ? docName : ''}
              >
                <Input
                  onChange={() => setIsDisabled && setIsDisabled(false)}
                  placeholder={t('Add a descriptive title for your document')}
                />
              </Form.Item>
            </>
          )}

          <ActionsButtonsContainer>
            {showAddActionsImageVideoDoc && !videoIsLoading && !docIsLoading && (
              <Space size={GUTTER_MD}>
                <Form.Item name="image" style={{ margin: 0 }}>
                  <ImageUpload
                    loading={imageIsLoading}
                    setLoading={setImageIsLoading}
                    onChange={async (image) => {
                      if (isImageCategory == 'Single') {
                        const multipleImage = [
                          {
                            imageHeight: image[0].response.height,
                            imageWidth: image[0].response.width,
                            imageFormat: image[0].response.format,
                            image: image[0].response.public_id,
                            url: image[0].response.url,
                            status: image[0].status,
                          },
                        ];
                        setMultipleImage(multipleImage);
                        setImage(image[0].response.public_id);
                        form.setFieldsValue({ image: image[0].response.public_id });
                        form.setFieldsValue({ type: PostType.Image });
                        form.setFieldsValue({ imageWidth: image[0].response.width });
                        form.setFieldsValue({ imageHeight: image[0].response.height });
                        form.setFieldsValue({ imageFormat: image[0].response.format });
                        form.setFieldsValue({ imageCategory: isImageCategory });
                        form.setFieldsValue({ multipleImages: multipleImage });
                        setIsDisabled && setIsDisabled(false);
                      } else if (isImageCategory == 'Multiple') {
                        let multipleImagesArray = image.map((item: any) => ({
                          imageHeight: item.response.height,
                          imageWidth: item.response.width,
                          imageFormat: item.response.format,
                          image: item.response.public_id,
                          url: item.response.url,
                          status: item.status,
                        }));
                        setMultipleImage(multipleImagesArray);
                        setImage(image[0].response.public_id);
                        form.setFieldsValue({ image: image[0].response.public_id });
                        form.setFieldsValue({ type: PostType.Image });
                        form.setFieldsValue({ imageWidth: image[0].response.width });
                        form.setFieldsValue({ imageHeight: image[0].response.height });
                        form.setFieldsValue({ imageFormat: image[0].response.format });
                        form.setFieldsValue({ type: PostType.Image });
                        form.setFieldsValue({ imageCategory: isImageCategory });
                        form.setFieldsValue({ multipleImages: multipleImagesArray });
                        setIsDisabled && setIsDisabled(false);
                      }
                    }}
                    setCategory={setIsImageCategory}
                  >
                    <ButtonWithIcon type="primary" ghost={true} icon={Icons.photo} data-cy="post-btn-image">
                      {t('Image')}
                    </ButtonWithIcon>
                  </ImageUpload>
                </Form.Item>
              </Space>
            )}
            {showAddActionsImageVideoDoc && !imageIsLoading && !docIsLoading && (
              <Space size={GUTTER_MD}>
                <Form.Item name="video" style={{ margin: 0 }}>
                  <VideoUpload
                    loading={videoIsLoading}
                    setLoading={setVideoIsLoading}
                    onChange={async ({ public_id, height, width, format }) => {
                      setVideo(public_id);
                      form.setFieldsValue({ video: public_id });
                      form.setFieldsValue({ type: PostType.Video });
                      form.setFieldsValue({ videoWidth: width });
                      form.setFieldsValue({ videoHeight: height });
                      form.setFieldsValue({ videoFormat: format });
                      setIsDisabled && setIsDisabled(false);
                    }}
                  >
                    <ButtonWithIcon
                      type="primary"
                      ghost={true}
                      icon={Icons.video}
                      style={{ marginLeft: '0' }}
                      data-cy="post-btn-video"
                    >
                      {t('Video')}
                    </ButtonWithIcon>
                  </VideoUpload>
                </Form.Item>
              </Space>
            )}
            {showAddActionsImageVideoDoc && !imageIsLoading && !videoIsLoading && (
              <Form.Item name="document" style={{ margin: 0 }}>
                <DocUpload
                  loading={docIsLoading}
                  setLoading={setDocIsLoading}
                  onChange={async ({ public_id, secure_url, bytes, original_filename }) => {
                    const size = convertBytes(bytes);
                    const format = secure_url.split('.').pop()?.toUpperCase();
                    setDocument(public_id);
                    setDocUrl(secure_url);
                    setDocSize(size);
                    setDocName(original_filename);
                    setDocFormat(format);
                    form.setFieldsValue({ document: public_id });
                    form.setFieldsValue({ type: PostType.Document });
                    form.setFieldsValue({ documentUrl: secure_url });
                    form.setFieldsValue({ documentSize: size });
                    form.setFieldsValue({ documentFormat: format });
                    setIsDisabled && setIsDisabled(false);
                  }}
                >
                  <ButtonWithIcon
                    type="primary"
                    ghost={true}
                    icon={Icons.document}
                    style={{ marginLeft: '0' }}
                    data-cy="post-btn-document"
                  >
                    {t('Document')}
                  </ButtonWithIcon>
                </DocUpload>
              </Form.Item>
            )}
          </ActionsButtonsContainer>
        </>
      ) : (
        <>
          {!isUpdate ? (
            <>
              <SubTitle>{t('Added To Your Post')}</SubTitle>
              <RepostContainer>
                <Header>
                  <HeaderImage id={activity.object?.data?.image} />
                  <HeaderInfo>
                    <HeaderTitle>
                      <span post-cy="post-title">{activity.object?.data?.name}</span>
                    </HeaderTitle>
                    <Info>
                      {hasUpdatePostPermission && (
                        <span style={{ color: 'var(--color-gray3)' }}>
                          <Icon icon={Icons.views} size={16} style={{ marginRight: '5px' }} />
                          {getViews(activity)}
                        </span>
                      )}
                      <CreatedTime
                        date={activity?.post?.data?.createdAt || activity?.post?.created_at}
                        condition="HeaderInfo"
                      />
                    </Info>
                  </HeaderInfo>
                </Header>
                <PostContent activity={activity} />
              </RepostContainer>
            </>
          ) : (
            <PostContent activity={activity} />
          )}
        </>
      )}

      <Form.Item style={{ margin: '20px 0 0 0', flex: 1 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', columnGap: '6px' }}>
          {organisation?.isPartner && (
            <div>
              <Tooltip
                title={t(
                  `${organisation.name} allows you to post to their followers up to ${organisation.postsLimit} times a month. The allowance resets at the start of each month.`,
                )}
              >
                <span style={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                  <Icon icon={Icons.calendar} color="var(--color-gray2)" size={16} />
                  {t('{{postsUsedThisMonth}} out of {{postsLimit}} posts remaining this month', {
                    postsUsedThisMonth: postsRemaining,
                    postsLimit: organisation.postsLimit,
                  })}
                </span>
              </Tooltip>
            </div>
          )}
          <div
            style={{ flex: 1, display: 'flex', justifyContent: 'flex-end', alignItems: 'center', columnGap: '24px' }}
          >
            {!isUpdate && !activity ? (
              <Tooltip title={t('Schedule for later')}>
                <span>
                  <Icon
                    icon={Icons.clock_blue}
                    style={{
                      width: '24px',
                      cursor: 'pointer',
                      pointerEvents: loading || imageIsLoading || videoIsLoading || docIsLoading ? 'none' : 'auto',
                    }}
                    onClick={onScheduledButtonClick}
                  />
                </span>
              </Tooltip>
            ) : (
              <></>
            )}
            <Tooltip
              title={
                hasReachedPostLimit
                  ? t('You have reached your post limit for this month as a partner organisation.')
                  : undefined
              }
            >
              <>
                <Button
                  className="post-btn"
                  data-cy="post-btn"
                  type="primary"
                  disabled={(isUpdate && isDisabled) || (hasReachedPostLimit as boolean)}
                  loading={loading || imageIsLoading || videoIsLoading || docIsLoading}
                  htmlType="submit"
                >
                  {isUpdate ? t('Save Post') : scheduledFormData?.isScheduled ? t('Schedule') : t('Post')}
                </Button>
              </>
            </Tooltip>
          </div>
        </div>
      </Form.Item>
    </Form>
  );

  function getFollowersPostAudienceText() {
    return organisation?.privacy === 'public' || organisation?.isPublic === true
      ? t('Everyone')
      : organisation?.privacy === 'protected'
      ? t('My Organisation & Connected Organisations')
      : t('Approved Followers');
  }
}

const SubTitle = styled(Typography.Text)`
  display: block;
  margin-bottom: 0;
  font-weight: 700;
  color: var(--color-text);
`;

const ActionsButtonsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
`;

const VideoPreview = styled(Video).attrs({ width: 500 })`
  width: 500px;
  height: 275px;
  overflow: hidden;
  border-radius: ${BORDER_RADIUS};
  position: relative;
  background-color: black;
  @media screen and (max-width: 568px) {
    width: 100%;
    height: 200px;
  }
`;

const OrganisationSelect = styled(Select)`
  border: 1px solid #dcf2fe;
  border-radius: 12px;

  & .ant-select-selector {
    height: 32px !important;

    & .ant-select-selection-item {
      line-height: 30px;
    }
  }
`;

const PostAudienceSelect = styled(Select)`
  border-radius: 12px;

  & .ant-select-selector {
    height: 32px !important;

    & .ant-select-selection-item {
      line-height: 30px;
    }
  }
`;

const PostAudienceInput = styled(Input)`
  cursor: pointer;
  border: 1px solid #dcf2fe;
  height: 32px !important;
`;

const ImageContainer = styled.div`
  margin-right: 10px;
  display: inline-block;
`;

const DocPreview = styled(Doc).attrs({ width: 500 })`
  // width: 500px;
  // height: 275px;
  // overflow: hidden;
  // border-radius: ${BORDER_RADIUS};
  // position: relative;
  // background-color: black;
  // @media screen and (max-width: 568px) {
  //   width: 100%;
  //   height: 200px;
  // }
`;

const DocumentButtonRemove = styled(Icon).attrs({
  size: 24,
  icon: Icons.close,
})`
  position: absolute;
  top: 25px;
  right: 15px;
  z-index: 10;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: ${BORDER_RADIUS};
  color: black;
  cursor: pointer;
  transition: background-color 0.4s ease-in-out;

  :hover {
    background-color: rgba(255, 255, 255, 0.3);
  }
`;
