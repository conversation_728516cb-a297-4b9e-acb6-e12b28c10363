import { gql } from '@apollo/client';

import { CreatePostInput, CreateStreamPostInput, CreateStreamCommentInput, Organisation, Post } from '@GraphQLTypes';

export type CreatePostQuery = {
  createPost: {
    id: string;
    organisation: {
      vanityId: string;
    };
  };
};
export type CreateStreamPostQuery = {
  createPost: {
    id: string;
    organisation: {
      vanityId: string;
    };
  };
};

export type CreatePostVariables = {
  postData: CreatePostInput;
};
export type CreateStreamPostVariables = {
  postData: CreateStreamPostInput;
};

export const CREATE_POST = gql`
  mutation ($postData: CreatePostInput!) {
    createPost(postData: $postData) {
      id
      organisation {
        id
        vanityId
      }
    }
  }
`;

export type UpdatePostQuery = {
  createPost: {
    id: string;
    organisation: {
      vanityId: string;
    };
  };
};
export type UpdateStreamPostQuery = {
  createPost: {
    id: string;
    organisation: {
      vanityId: string;
    };
  };
};

export type UpdatePostVariables = {
  postData: CreatePostInput;
};
export type UpdateStreamPostVariables = {
  postData: CreateStreamPostInput;
};

export const UPDATE_POST = gql`
  mutation ($postData: CreatePostInput!) {
    createPost(postData: $postData) {
      id
      organisation {
        id
        vanityId
      }
    }
  }
`;

export type RemovePostVariables = {
  postId: string;
};
export type RemoveStreamPostVariables = {
  postId: string;
  organisationId: string;
  activityId?: string;
  isRepost: boolean;
};

export type UpdateSchedulePostVaraible = {
  postId: string;
  postData: CreatePostInput;
};

export const REMOVE_POST = gql`
  mutation ($postId: String!) {
    removePost(postId: $postId)
  }
`;
export const REMOVE_STREAM_POST = gql`
  mutation ($postId: String!, $organisationId: String!, $activityId: String, $isRepost: Boolean!) {
    removeStreamPost(postId: $postId, organisationId: $organisationId, activityId: $activityId, isRepost: $isRepost)
  }
`;

export type ShowAllSchedulePostData = Required<
  Pick<Post, 'id' | 'type' | 'image' | 'text' | 'profile' | 'organisation' | 'createdAt' | 'totalViews' | 'views'>
>;

export type ShowAllSchedulePostQuery = {
  scheduledPostList: {
    records: Array<Post>;
    totalCount: number;
  };
};

export type ShowAllSchedulePostvariable = {
  organisationId?: string;
  parentOrganisationId?: string;
};

export const GET_SCHEDULED_POST_LIST = gql`
  query GetScheduledPostList($organisationId: String!, $parentOrganisationId: String) {
    scheduledPostList(organisationId: $organisationId, parentOrganisationId: $parentOrganisationId) {
      totalCount
      records {
        id
        type
        text
        image
        mediaHeight
        mediaWidth
        imageCategory
        profile {
          id
          email
          name
          image
        }
        organisation {
          id
          name
          image
          type
          isPaid
          hasClubHabloSubscription
        }
        seenBy
        totalViews
        createdAt
        views
        isSeen
        status
        scheduledAt
        postAudience
      }
    }
  }
`;

export type CheckNewStreamPostsVariables = {
  activityId: string;
};
export const CHECK_NEW_POSTS = gql`
  mutation CheckNewPost($activityId: String!) {
    checkNewPost(activityId: $activityId)
  }
`;

export type CreateStreamCommentVariables = {
  commentData: CreateStreamCommentInput;
  activityId: string;
  postAuthorId: string;
  organisationId: string;
  postId: string;
};
export const CREATE_STREAM_COMMENT = gql`
  mutation CreateStreamCommentPost(
    $activityId: String!
    $commentData: CreateStreamCommentInput!
    $postAuthorId: String!
    $organisationId: String!
    $postId: String!
  ) {
    createStreamCommentPost(
      activityId: $activityId
      commentData: $commentData
      postAuthorId: $postAuthorId
      organisationId: $organisationId
      postId: $postId
    ) {
      success
    }
  }
`;

export type UpdateStreamCommentVariables = {
  commentData: CreateStreamCommentInput;
  commentId: string;
  activityId: string;
};
export const UPDATE_STREAM_COMMENT = gql`
  mutation ($commentId: String!, $activityId: String!, $commentData: CreateStreamCommentInput!) {
    updateStreamComment(commentId: $commentId, activityId: $activityId, commentData: $commentData)
  }
`;

export type RemoveStreamCommentVariables = {
  commentId: string;
};
export const REMOVE_STREAM_COMMENT = gql`
  mutation ($commentId: String!) {
    removeStreamComment(commentId: $commentId)
  }
`;

export type RemoveRepostVariables = {
  activityId: string;
};

export const REMOVE_REPOST = gql`
  mutation ($activityId: String!) {
    removeActivityFeeds(activityId: $activityId)
  }
`;

export type AddPostImpressionToActivityVariables = {
  activityId: string;
  postId: string;
  repostPostId?: string;
  postType: string;
};

export const ADD_POST_IMPRESSION_TO_ACTIVITY = gql`
  mutation AddPostImpressionToActivity(
    $activityId: String!
    $postId: String!
    $repostPostId: String!
    $postType: String!
  ) {
    addPostImpressionToActivity(
      activityId: $activityId
      postId: $postId
      repostPostId: $repostPostId
      postType: $postType
    )
  }
`;

export type AddPostViewToActivityVariables = {
  activityId: string;
  postId: string;
  repostPostId?: string;
  postType: string;
};

export const ADD_POST_VIEW_TO_ACTIVITY = gql`
  mutation AddPostViewToActivity($activityId: String!, $postId: String!, $repostPostId: String!, $postType: String!) {
    addPostViewToActivity(activityId: $activityId, postId: $postId, repostPostId: $repostPostId, postType: $postType)
  }
`;

export type RepostStreamPostVariables = {
  organisationId: string;
  activityId: string;
  postData: any;
  postAuthorId: string;
  origOrganisationId: string;
  postId: string;
};
export const CREATE_STREAM_POST = gql`
  mutation CreateStreamPost($postData: CreateStreamPostInput!) {
    createStreamPost(postData: $postData) {
      success
      activityId
    }
  }
`;

export const UPDATE_STREAM_POST = gql`
  mutation ($postId: String!, $postData: CreateStreamPostInput!) {
    updateStreamPost(postId: $postId, postData: $postData)
  }
`;

export const UPDATE_SCHEDULED_POST = gql`
  mutation ($postId: String!, $postData: UpdatePostScheduleDateInput!) {
    updatePostScheduleTime(postId: $postId, postData: $postData) {
      id
    }
  }
`;

export const REPOST_STREAM_POST = gql`
  mutation (
    $organisationId: String!
    $activityId: String!
    $postData: CreateStreamPostInput!
    $postAuthorId: String!
    $origOrganisationId: String!
    $postId: String!
  ) {
    repostStreamPost(
      organisationId: $organisationId
      activityId: $activityId
      postData: $postData
      postAuthorId: $postAuthorId
      origOrganisationId: $origOrganisationId
      postId: $postId
    ) {
      success
    }
  }
`;

export const SAVE_POST = gql`
  mutation SavePost($activityId: String!, $feedId: String!, $feedName: String!) {
    savePost(activityId: $activityId, feedId: $feedId, feedName: $feedName)
  }
`;

export const UNSAVE_POST = gql`
  mutation UnsavePost($activityId: String!, $feedId: String!, $feedName: String!) {
    unsavePost(activityId: $activityId, feedId: $feedId, feedName: $feedName)
  }
`;

export type SavePostVariables = {
  activityId: string;
  feedId: string;
  feedName: string;
};

export type UnsavePostVariables = {
  activityId: string;
  feedId: string;
  feedName: string;
};
