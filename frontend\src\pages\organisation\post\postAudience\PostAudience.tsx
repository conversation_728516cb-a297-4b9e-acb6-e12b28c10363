import { <PERSON><PERSON>, Checkbox, Col, Radio, Row, Tooltip, Typo<PERSON>, Empty } from 'antd';
import React, { useState, useEffect } from 'react';
import { BORDER_RADIUS } from '@theme';
import styled from 'styled-components';
import Icon, { Icons } from '@components/icons/Icon';
import { <PERSON>er, HeaderInfo, HeaderTitle, Info } from '../styles';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@apollo/client';
import {
  GetPartnershipsData,
  GetPartnershipsVariables,
  GET_PARTNERSHIPS,
} from '../../settings/pages/connectedOrganisations/queries';
import { Loader } from '@src/components/Loader';
import { Organisation, OrganisationType } from '@src/graphql/GraphQLTypes';
import { BasicProfileMembership } from '@src/routes/queries';
import { getPrimaryMembership } from '@src/utils/getOrganisationName';

type PostAudienceProps = {
  organisation: Pick<Organisation, 'image' | 'id' | 'type' | 'name' | 'privacy' | 'isPublic' | 'isPartner'> | undefined;
  memberships: BasicProfileMembership[];
  setShowPostAudienceModal: React.Dispatch<React.SetStateAction<boolean>>;
  connectedOrganisations: Array<string>;
  selectedOrganisationNames: Array<string>;
  setConnectedOrganisation: React.Dispatch<React.SetStateAction<Array<string>>>;
  canSeePost: string;
  setCanSeePost: React.Dispatch<React.SetStateAction<string>>;
  setSelectedOption: React.Dispatch<React.SetStateAction<string>>;
  setSelectedOrganisationNames: React.Dispatch<React.SetStateAction<Array<string>>>;
};
export function PostAudience({
  organisation,
  memberships,
  setShowPostAudienceModal,
  connectedOrganisations,
  setConnectedOrganisation,
  canSeePost,
  setCanSeePost,
  setSelectedOption,
  selectedOrganisationNames,
  setSelectedOrganisationNames,
}: PostAudienceProps) {
  const { t } = useTranslation();
  const { loading, data } = useQuery<GetPartnershipsData, GetPartnershipsVariables>(GET_PARTNERSHIPS, {
    variables: {
      organisationId: organisation?.id || '',
      isActive: true,
    },
  });
  const [checkedStateData, setCheckedStateData] = useState<Array<any>>([]);

  useEffect(() => {
    const updatedCheckedState = data?.organisation?.partnerships.map((item, index) => {
      if (connectedOrganisations.find((el) => item?.organisation?.id === el)) {
        return {
          ...item,
          checked: true,
        };
      } else {
        return item;
      }
    });
    setCheckedStateData(updatedCheckedState || []);
  }, [data?.organisation?.partnerships]);

  const selectConnectedOrganisation = (idx: number, orgId: string, orgName: string) => {
    setCanSeePost('');
    const updatedCheckedState = checkedStateData.map((item, index) => {
      if (item?.organisation?.id === orgId) {
        if (item && item.checked) {
          return {
            ...checkedStateData[index],
            checked: !item.checked,
          };
        } else {
          return {
            ...checkedStateData[index],
            checked: true,
          };
        }
      } else {
        return item;
      }
    });
    setCheckedStateData(updatedCheckedState);
    let newArray = [...connectedOrganisations, orgId];
    if (connectedOrganisations.includes(orgId)) {
      newArray = newArray.filter((og) => {
        return og !== orgId;
      });
    }
    setConnectedOrganisation(newArray);
    let nameArray = [...selectedOrganisationNames, orgName];
    if (selectedOrganisationNames.includes(orgName)) {
      nameArray = nameArray.filter((org) => {
        return org !== orgName;
      });
    }
    setSelectedOrganisationNames(nameArray);
  };

  const ConnectedOrgs = () => {
    return (
      <div>
        {checkedStateData?.map(
          (org: { organisation: { name: string; id: string; type: OrganisationType } }, index: number) => {
            return (
              <PostAudienceRowItem
                style={{ display: 'flex', alignItems: 'center' }}
                onClick={() => selectConnectedOrganisation(index, org?.organisation?.id, org?.organisation?.name)}
              >
                <HeaderSpan>
                  <Icon icon={Icons.organisation} size={24} color="#0093C7" />
                </HeaderSpan>
                <HeaderInfo>
                  <HeaderTitle style={{ display: 'flex', alignItems: 'center' }}>{org?.organisation?.name}</HeaderTitle>
                  <Description>
                    {org?.organisation?.type === OrganisationType.Association ||
                    org?.organisation?.type === OrganisationType.Consortia
                      ? t('Send this post to Employees/Members of {{organisationName}}', {
                          organisationName: org?.organisation?.name,
                        })
                      : t('Send this post to Employees of {{organisationName}}', {
                          organisationName: org?.organisation?.name,
                        })}
                  </Description>
                </HeaderInfo>
                <HeaderOption>
                  <Checkbox
                    // @ts-ignore
                    checked={org?.checked}
                    onChange={() => {
                      selectConnectedOrganisation(index, org?.organisation?.id, org?.organisation?.name);
                    }}
                    data-cy={'conn-org-checkbox'}
                  />
                </HeaderOption>
              </PostAudienceRowItem>
            );
          },
        )}
      </div>
    );
  };

  const primaryMembership = getPrimaryMembership({
    memberships
  });
  
  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <>
          <SubTitle>{t('Who can see your post?')}</SubTitle>
          <Typography.Text>
            {t('Choose who your post is sent to and where it can be seen on Hablo...')}.
          </Typography.Text>
          <Scrollable style={{ marginBottom: organisation?.isPartner ? '10px' : '' }}>
            <Row gutter={[6, 6]}>
              <Col span={24}>
                <PostAudienceRowItem
                  style={{ display: 'flex', alignItems: 'center', paddingRight: '5px' }}
                  onClick={() => {
                    setCanSeePost('followers');
                    setCheckedStateData(data?.organisation?.partnerships || []);
                    setConnectedOrganisation([]);
                    setSelectedOrganisationNames([]);
                  }}
                >
                  <HeaderSpan>
                    {organisation?.privacy === 'public' || organisation?.isPublic === true ? (
                      <Icon icon={Icons.globe} size={24} color="#0093C7" />
                    ) : (
                      <Icon icon={Icons.users} size={24} color="#0093C7" />
                    )}
                  </HeaderSpan>
                  <HeaderInfo>
                    <HeaderTitle style={{ display: 'flex', alignItems: 'center' }}>
                      {organisation?.privacy === 'public' || organisation?.isPublic === true
                        ? 'Everyone'
                        : organisation?.privacy === 'protected'
                        ? 'My Organisation & Connected Organisations'
                        : 'Approved Followers'}
                      <Tooltip
                        placement="right"
                        title={
                          organisation?.privacy === 'public' || organisation?.isPublic === true
                            ? t(
                                "As a Public organisation, anyone can view {{organisationName}}'s organisation page and choose to follow the page. Go to Manage Organisation to change your privacy settings.",
                                { organisationName: organisation?.name },
                              )
                            : organisation?.privacy === 'protected'
                            ? t(
                                'As a Protected organisation, only approved Organisations that {{organisationName}} does business with & verified employees will receive this Post. Go to Manage Organisation to change your privacy settings.',
                                { organisationName: organisation?.name },
                              )
                            : t(
                                'As a Private organisation, all follower requests require approval before they can see your posts. Go to Manage Organisation to change your privacy settings.',
                                { organisationName: organisation?.name },
                              )
                        }
                      >
                        <span>
                          <Icon style={{ margin: '0 0 0 10px' }} icon={Icons.infoOutlined} size={18} color="#0093C7" />
                        </span>
                      </Tooltip>
                    </HeaderTitle>
                    <AccessInfo>
                      {organisation?.privacy === 'public' || organisation?.isPublic === true
                        ? t('All followers of {{organisationName}} and on your public page', {
                            organisationName: organisation?.name,
                          })
                        : organisation?.privacy === 'protected'
                        ? organisation?.type === OrganisationType.Association ||
                          organisation?.type === OrganisationType.Consortia
                          ? t(
                              'Employees and Members at {{organisationName}} and pre-approved companies you do business with.',
                              {
                                organisationName: organisation?.name,
                              },
                            )
                          : t('Employees at {{organisationName}} and pre-approved companies you do business with.', {
                              organisationName: organisation?.name,
                            })
                        : t('Followers you have approved on Hablo')}
                    </AccessInfo>
                  </HeaderInfo>
                  <HeaderOption>
                    <Radio
                      onChange={() => {
                        setCanSeePost('followers');
                        setCheckedStateData(data?.organisation?.partnerships || []);
                        setConnectedOrganisation([]);
                        setSelectedOrganisationNames([]);
                      }}
                      checked={canSeePost === 'followers'}
                    />
                  </HeaderOption>
                </PostAudienceRowItem>
                <PostAudienceRowItem
                  style={{ display: 'flex', alignItems: 'center', paddingRight: '5px' }}
                  onClick={() => {
                    setCanSeePost('my-organisation');
                    setCheckedStateData(data?.organisation?.partnerships || []);
                    setConnectedOrganisation([]);
                    setSelectedOrganisationNames([]);
                  }}
                  data-cy="my-organisation"
                >
                  <HeaderSpan>
                    <Icon icon={Icons.tourOperator} size={24} color="#0093C7" />
                  </HeaderSpan>
                  <HeaderInfo>
                    <HeaderTitle style={{ display: 'flex', alignItems: 'center' }}>{t('My Organisation')}</HeaderTitle>
                    <Description>
                      {organisation?.type === OrganisationType.Association ||
                      organisation?.type === OrganisationType.Consortia
                        ? t('Only to Employees and Members of {{organisationName}}', {
                            organisationName: organisation?.name,
                          })
                        : t('Only to Employees of {{organisationName}}', {
                            organisationName: organisation?.name,
                          })}
                    </Description>
                  </HeaderInfo>
                  <HeaderOption>
                    <Radio
                      onChange={() => {
                        setCanSeePost('my-organisation');
                        setCheckedStateData(data?.organisation?.partnerships || []);
                        setConnectedOrganisation([]);
                        setSelectedOrganisationNames([]);
                      }}
                      checked={canSeePost === 'my-organisation'}
                    />
                  </HeaderOption>
                </PostAudienceRowItem>
              </Col>
            </Row>

            {!primaryMembership?.parentOrganisation && (
              <Row gutter={[6, 6]}>
                <SubTitle style={{ fontSize: '12px', fontWeight: 800, padding: '18px 0 8px 3px', margin: 0 }}>
                  {t('CONNECTED ORGANISATIONS')}
                </SubTitle>
                <Typography.Text style={{ padding: '0 0 13px 3px' }}>
                  {t('Target your post to Employees of Organisations that you work with.')}
                </Typography.Text>
                {data &&
                data?.organisation &&
                data?.organisation?.partnerships &&
                data?.organisation?.partnerships?.length > 0 ? (
                  <Col span={24} style={{ paddingBottom: '30px' }}>
                    {/* {ConnectedOrgs()} */}
                    <ConnectedOrgs />
                  </Col>
                ) : (
                  <Col span={24} style={{ margin: '-20px 0 10px 0' }}>
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description={
                        <span style={{ fontSize: '12px' }}>
                          {t('{{organisationName}} has no Connected Organisations yet.', {
                            organisationName: organisation?.name,
                          })}
                        </span>
                      }
                    />
                  </Col>
                )}
              </Row>
            )}
          </Scrollable>
          <Row>
            <div style={{ flex: 1, display: 'flex', justifyContent: 'flex-end' }}>
              <Button type="link" onClick={() => setShowPostAudienceModal(false)}>
                {t('Back')}
              </Button>
              <Button
                type="primary"
                disabled={!canSeePost && connectedOrganisations.length === 0}
                onClick={() => {
                  setShowPostAudienceModal(false);
                  setSelectedOption(canSeePost);
                }}
                data-cy={'post-audience-save'}
              >
                {t('Save')}
              </Button>
            </div>
          </Row>
        </>
      )}
    </>
  );
}

const HeaderOption = styled.div``;

const AccessInfo = styled(Info)`
  margin-top: 5px;
`;

const Description = styled.div`
  margin-top: 5px;
  font-size: 13px;
  color: var(--color-text30);
`;

const Scrollable = styled.div`
  overflow-y: scroll;
  max-height: 390px;
  overflow-x: hidden;
  --mask: linear-gradient(to bottom, rgba(0, 0, 0, 1) 0, rgba(0, 0, 0, 1) 88%, rgba(0, 0, 0, 0) 95%, rgba(0, 0, 0, 0) 0)
    100% 50% / 100% 100% repeat-x;
  -webkit-mask: var(--mask);
  mask: var(--mask);
  margin-top: 20px;
`;

const HeaderSpan = styled.span`
  background: rgba(0, 147, 199, 0.1);
  border-radius: 12px;
  width: 45px;
  height: 45px;
  display: flex !important;
  justify-content: center;
  align-items: center;
`;

const SubTitle = styled(Typography.Text)`
  display: block;
  margin-bottom: 12px;
  font-weight: 600;
  font-size: 18px;
  color: var(--color-text);
`;

export default PostAudience;

const PostAudienceRowItem = styled(Header)`
  cursor: pointer;
  border-radius: ${BORDER_RADIUS};
  margin-right: 6px;
  padding-right: 15px;
  margin-left: 10px;

  :hover {
    background-color: var(--color-primary80);
  }
`;
