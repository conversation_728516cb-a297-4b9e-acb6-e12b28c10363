import { <PERSON><PERSON>, Col, Form, Row, Typography, Select, Tooltip, Modal, Empty } from 'antd';
import { Avatar, Image } from '@components/Image/Image';
import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Icon, { Icons } from '@components/icons/Icon';
import { useTranslation } from 'react-i18next';
import TimezoneSelect, { type ITimezone } from 'react-timezone-select';
import { DateTimePicker } from '@src/components/Dates/DateTimePicker';
import { HeaderTitle } from '../styles';
import { Organisation, Post } from '@src/graphql/GraphQLTypes';
import moment from 'moment';
import {
  GET_SCHEDULED_POST_LIST,
  REMOVE_STREAM_POST,
  RemoveStreamPostVariables,
  ShowAllSchedulePostQuery,
  ShowAllSchedulePostvariable,
  UPDATE_SCHEDULED_POST,
} from '../create/queries';
import { useMutation, useQuery } from '@apollo/client';
import { Loader } from '@src/components/Loader';
import { DataFetchingError } from '@src/components/Results';
import { useProfile } from '@src/routes/ProfileProvider';
import { setCookie, getCookie } from '@utils/cookie';

type SchedulePostProps = {
  setShowScheduledPostModal: React.Dispatch<React.SetStateAction<boolean>>;
  setShowAllSchedueModal: React.Dispatch<React.SetStateAction<boolean>>;
  showAllScheduleModal: boolean;
  setShowScheduledPostDetail: React.Dispatch<React.SetStateAction<boolean>>;
  showScheduledPostDetail: boolean;
  organisation:
    | Pick<Organisation, 'image' | 'name' | 'id' | 'isPublic' | 'privacy' | 'isPartner' | 'childOrganisationId'>
    | undefined;
  setOrganisation: React.Dispatch<
    React.SetStateAction<Pick<Organisation, 'id' | 'name' | 'privacy' | 'isPublic' | 'image'> | undefined>
  >;
  organisationOptions: any[];
  setScheduledFormData: React.Dispatch<
    React.SetStateAction<{
      dateTime: Date | string;
      timeZone: ITimezone;
      errors: {
        dateTime: string;
        timeZone: string;
      };
      isScheduled: boolean;
    }>
  >;
  scheduledFormData: {
    dateTime: Date | string;
    timeZone: ITimezone;
    errors: {
      dateTime: string;
      timeZone: string;
    };
    isScheduled: boolean;
  };
  editSchedule: boolean;
  setEditSchedule: React.Dispatch<React.SetStateAction<boolean>>;
};

export function SchedulePost({
  setShowScheduledPostModal,
  setShowAllSchedueModal,
  showAllScheduleModal,
  organisation,
  setOrganisation,
  organisationOptions,
  setShowScheduledPostDetail,
  showScheduledPostDetail,
  setScheduledFormData,
  scheduledFormData,
  editSchedule,
  setEditSchedule,
}: SchedulePostProps) {
  const { t } = useTranslation();
  //const [showScheduledPostDetail, setShowScheduledPostDetail] = useState<boolean>(false);

  const { data, loading, error, refetch } = useQuery<ShowAllSchedulePostQuery, ShowAllSchedulePostvariable>(
    GET_SCHEDULED_POST_LIST,
    {
      variables: {
        organisationId: organisation?.isPartner ? (organisation?.childOrganisationId as string) : (organisation?.id as string),
        parentOrganisationId: organisation?.isPartner ? (organisation?.id as string) : undefined,
      },
    },
  );

  const [removePost, { loading: deleteOnRemovePostLoading }] = useMutation<{}, RemoveStreamPostVariables>(
    REMOVE_STREAM_POST,
    {
      onCompleted: () => refetch(),
    },
  );

  const [updateStreamPost, { loading: updateOnStreamLoading }] = useMutation(UPDATE_SCHEDULED_POST, {
    onCompleted: () => refetch(),
  });

  const [scheduledPostDetail, setScheduledPostDetail] = useState<Post>();
  const [timeZoneValue, setTimeZoneValue] = useState<string>(Intl?.DateTimeFormat()?.resolvedOptions()?.timeZone);
  const [isEdit, setIsEdit] = useState(false);
  const [scheduledPostList, setScheduledPostList] = useState<Array<Post>>();

  useEffect(() => {
    if (getCookie('Timezone')) setTimeZoneValue(getCookie('Timezone'));
    if (data) {
      setScheduledPostList(data?.scheduledPostList?.records);
    }
  }, [data?.scheduledPostList?.records, showAllScheduleModal]);

  const onShowAllSchedulePostClick = () => {
    setIsEdit(false);
    setShowAllSchedueModal(true);
  };

  const handleSave = async () => {
    const { dateTime, timeZone } = scheduledFormData;
    let isValid = true;
    const errors = { dateTime: '', timeZone: '' };

    if (!dateTime) {
      errors.dateTime = 'Please select a date and time';
      isValid = false;
    }

    if (!timeZone) {
      errors.timeZone = 'Please select a timezone';
      isValid = false;
    }

    setScheduledFormData({ ...scheduledFormData, errors });

    if (isValid) {
      setScheduledFormData({ ...scheduledFormData, isScheduled: true });
      if (isEdit) {
        const date = moment(scheduledFormData?.dateTime).format('YYYY-MM-DDTHH:mm:ss');
        const zone =
          typeof scheduledFormData?.timeZone === 'string'
            ? scheduledFormData?.timeZone
            : typeof scheduledFormData?.timeZone === 'object'
            ? scheduledFormData?.timeZone?.value
            : '';
        const utcDate = moment.tz(date, zone).utc().format();
        await updateStreamPost({
          variables: {
            postId: scheduledPostDetail?.id,
            postData: {
              scheduledAt: utcDate,
            },
          },
        });
        setShowAllSchedueModal(true);
      } else {
        setEditSchedule(false);
        setShowScheduledPostModal(false);
      }
    }

    setCookie(
      'Timezone',
      typeof scheduledFormData?.timeZone === 'string'
        ? scheduledFormData?.timeZone
        : typeof scheduledFormData?.timeZone === 'object'
        ? scheduledFormData?.timeZone?.value
        : '',
      2,
    );
  };

  const handleChangeDateTime = (dateTime: Date | string) => {
    setScheduledFormData({ ...scheduledFormData, dateTime });
  };

  const handleChangeTimeZone = (timeZone: ITimezone) => {
    setScheduledFormData({ ...scheduledFormData, timeZone });
  };

  const onRowClick = (data: Post) => {
    setScheduledPostDetail(data);
    setShowScheduledPostDetail(true);
    setShowAllSchedueModal(false);
  };

  const { dateTime, timeZone, errors } = scheduledFormData;

  const imPostAuthor = scheduledPostDetail?.profile?.id === useProfile().profile?.id;
  const selectedOrganisationNames = scheduledPostDetail?.postAudience?.selectedOrganisationNames as string[];
  const connectedOrganisations = scheduledPostDetail?.postAudience?.connectedOrganisations as string[];
  let text = [] as string[];
  if (connectedOrganisations && selectedOrganisationNames && connectedOrganisations.length > 1) {
    text = selectedOrganisationNames.map((e: string, i) => {
      if (i === selectedOrganisationNames.length - 1) {
        return e + ' ';
      } else if (i === selectedOrganisationNames.length - 2) {
        return e + ' and ';
      } else {
        return e + ', ';
      }
    });
  }

  const onDeletePost = (scheduledPostdata: Post, isScheduledPostList: boolean) => {
    Modal.confirm({
      centered: true,
      content: t('Your scheduled post will be lost. Are you sure you want to remove this post?'),
      cancelText: t('Cancel'),
      okText: t('Ok'),
      onOk: async () => {
        await removePost({
          variables: {
            postId: scheduledPostdata?.id,
            organisationId: scheduledPostdata?.organisation?.id,
            isRepost: false,
          },
        });

        if (!isScheduledPostList) {
          setShowScheduledPostDetail(false);
          setShowAllSchedueModal(true);
        }
      },
    });
  };

  const onViewSchedulePost = (scheduledPostdata: Post, isScheduledPostList: boolean) => {
    setScheduledFormData({
      ...scheduledFormData,
      isScheduled: true,
      dateTime: scheduledPostdata.scheduledAt,
      timeZone: timeZoneValue,
    });
    setScheduledPostDetail(scheduledPostdata);
    setIsEdit(true);
    if (isScheduledPostList) {
      setShowAllSchedueModal(false);
    } else {
      setShowScheduledPostDetail(false);
      //setShowAllSchedueModal(false);
    }
  };

  const handleChangeTimeZoneScheduledList = (timeZone: ITimezone) => {
    setTimeZoneValue(typeof timeZone === 'object' ? timeZone?.value : '');
  };

  const resetScheduledFormData = () => {
    setScheduledFormData({
      ...scheduledFormData,
      dateTime: '',
      timeZone: Intl?.DateTimeFormat()?.resolvedOptions()?.timeZone,
      isScheduled: false,
      errors: { dateTime: '', timeZone: '' },
    });
  };

  return error ? (
    <DataFetchingError />
  ) : loading || !scheduledPostList ? (
    <Loader />
  ) : showAllScheduleModal ? (
    <>
      <Row gutter={12}>
        <Col span={24}>
          <Form.Item
            name="organisationId"
            initialValue={organisation?.id}
            style={{ marginBottom: 0 }}
            rules={[{ required: true, message: t('Please choose an organisation to post as.') }]}
          >
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <ImageContainer>
                <Avatar id={organisation?.image} width={48} />
              </ImageContainer>
              <Row gutter={16} style={{ width: '100%' }}>
                <Col md={12} sm={24} xs={24}>
                  <SubTitle2>{t('Show Posts From')}</SubTitle2>
                  <OrganisationSelect
                    bordered={false}
                    dropdownMatchSelectWidth={false}
                    suffixIcon={<Icon icon={Icons.chevronDown_blue} style={{ width: '12px' }} />}
                    disabled={organisationOptions && organisationOptions.length <= 1}
                    value={organisation?.id}
                    options={organisationOptions}
                    onSelect={(id, option) => {
                      setOrganisation && setOrganisation(option.organisation);
                    }}
                  />
                </Col>
                <Col md={12} sm={24} xs={24}>
                  <SubTitle2>{t('View in Timezone')}</SubTitle2>
                  <StyledTimezoneSelected>
                    <TimezoneSelect value={timeZoneValue} onChange={handleChangeTimeZoneScheduledList} />
                    <TimezoneSuffixIcon icon={Icons.chevronDown_blue} style={{ width: '12px' }} />
                  </StyledTimezoneSelected>
                </Col>
              </Row>
            </div>
          </Form.Item>
        </Col>
      </Row>
      <Scrollable>
        {scheduledPostList.length > 0 ? (
          scheduledPostList?.map((scheduledData) => {
            return (
              <div key={scheduledData.id}>
                <BorderedDiv />

                <div key={scheduledData.id} style={{ display: 'flex', flexDirection: 'column', position: 'relative' }}>
                  <div style={{ marginBottom: '-10px' }}>
                    <Icon icon={Icons.user} style={{ width: '12px' }} />{' '}
                    <span style={{ fontSize: '12px', color: '#616161' }}>
                      {t('Created By')} {scheduledData.profile?.name}
                    </span>
                  </div>
                  {timeZoneValue && (
                    <div style={{ marginBottom: '10px' }}>
                      <Icon icon={Icons.clock_grey} style={{ width: '12px' }} />{' '}
                      <span style={{ fontSize: '12px', color: '#616161' }}>
                        {`Posting on ${moment(scheduledData.scheduledAt)
                          .tz(timeZoneValue)
                          .format('dddd, MMMM Do YYYY, [at] hh:mm A')} (GMT ${
                          typeof scheduledFormData?.timeZone === 'string'
                            ? moment().tz(timeZoneValue).format('Z')
                            : typeof scheduledFormData?.timeZone === 'object'
                            ? moment().tz(timeZoneValue).format('Z')
                            : ''
                        })`}
                      </span>
                    </div>
                  )}
                  <div style={{ position: 'absolute', right: 0, top: '13px', display: 'flex', gap: '9px' }}>
                    <Icon
                      icon={Icons.bin}
                      style={{ width: '14px', cursor: 'pointer' }}
                      onClick={() => onDeletePost(scheduledData, true)}
                    />
                    <Icon
                      icon={Icons.clock_dark}
                      style={{ width: '18px', cursor: 'pointer' }}
                      onClick={() => onViewSchedulePost(scheduledData, true)}
                    />
                  </div>
                </div>
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    cursor: 'pointer',
                    width: '88%',
                    alignItems: 'center',
                  }}
                  onClick={() => onRowClick(scheduledData)}
                >
                  {scheduledData.image ? (
                    <ImageContainer>
                      <Image id={scheduledData.image} width={40} alt={'scheduledImage'} />
                    </ImageContainer>
                  ) : (
                    <></>
                  )}
                  <WordWrap>{scheduledData.text}</WordWrap>
                </div>
              </div>
            );
          })
        ) : (
          <Empty
            image={<Icon size={40} color="#cccccc" icon={Icons.posts} />}
            imageStyle={{ height: '40px', marginBottom: '24px', marginTop: '48px' }}
            description={<span className="ant-empty-normal">{t('No scheduled posts at the moment')}</span>}
            style={{ marginBottom: '30px', marginTop: '60px' }}
          />
        )}
      </Scrollable>
      <Row style={{ marginTop: '12px' }}>
        <div style={{ flex: 1, display: 'flex', justifyContent: 'flex-end', columnGap: '10px' }}>
          <Button
            type="primary"
            onClick={() => {
              resetScheduledFormData();
              setShowAllSchedueModal(false);
            }}
            loading={deleteOnRemovePostLoading}
          >
            {t('Back')}
          </Button>
        </div>
      </Row>
    </>
  ) : showScheduledPostDetail && scheduledPostDetail ? (
    <>
      <div style={{ display: 'flex', flexDirection: 'column', position: 'relative' }}>
        <div style={{ marginBottom: '-10px' }}>
          <Icon icon={Icons.user} style={{ width: '12px' }} />{' '}
          <span style={{ fontSize: '12px', color: '#616161' }}>{scheduledPostDetail?.profile?.name}</span>
        </div>
        {timeZoneValue && (
          <div style={{ marginBottom: '10px' }}>
            <Icon icon={Icons.clock_grey} style={{ width: '12px' }} />{' '}
            <span style={{ fontSize: '12px', color: '#616161' }}>{`Posting on ${moment(scheduledPostDetail.scheduledAt)
              .tz(timeZoneValue)
              .format('dddd, MMMM Do YYYY, [at] hh:mm A')} (GMT ${
              typeof scheduledFormData?.timeZone === 'string'
                ? moment().tz(timeZoneValue).format('Z')
                : typeof scheduledFormData?.timeZone === 'object'
                ? moment().tz(timeZoneValue).format('Z')
                : ''
            })`}</span>
          </div>
        )}
      </div>
      <div style={{ display: 'flex', flexDirection: 'column', position: 'relative' }}>
        <ImageContainer>
          <Avatar id={organisation?.image} width={48} />
        </ImageContainer>
        <SelectContainer>
          <SubTitle2>{scheduledPostDetail?.organisation?.name}</SubTitle2>
          <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
              <Icon icon={Icons.clock_grey} style={{ width: '14px' }} />{' '}
              <span style={{ fontSize: '13px', color: '#616161' }}>{t('Scheduled')}</span>{' '}
            </div>
            {scheduledPostDetail?.postAudience?.isMyOrganisation ? (
              <>
                <Tooltip title={t('Sent only to Members of your Organisation')}>
                  <span style={{ color: 'var(--color-gray3)', display: 'flex', alignItems: 'center', gap: '2px' }}>
                    <AudienceIcon icon={Icons.tourOperator} size={14} />
                    <span style={{ fontSize: '13px', color: '#616161' }}>{t('My Organisation')}</span>
                  </span>
                </Tooltip>
              </>
            ) : scheduledPostDetail?.postAudience?.connectedOrganisations ? (
              <Tooltip
                title={
                  imPostAuthor
                    ? connectedOrganisations && selectedOrganisationNames && connectedOrganisations.length > 1
                      ? t('Targeted by you to Members of ') + text.join('')
                      : selectedOrganisationNames
                      ? t('Targeted by you to Members of ') + selectedOrganisationNames[0]
                      : t('Targeted by you to Members of ') +
                        connectedOrganisations.length +
                        t(' Connected Organisation(s)')
                    : t('Sent to Members of your Organisation')
                }
              >
                <span
                  style={{ color: 'var(--color-gray3)', display: 'flex', alignItems: 'center', gap: '2px' }}
                  data-cy={'targeted-post'}
                >
                  <AudienceIcon icon={Icons.organisation} size={14} />
                  <span style={{ fontSize: '13px', color: '#616161' }}>{t('Connected Organisations')}</span>
                </span>
              </Tooltip>
            ) : (
              <Tooltip title={t('Sent to Approved Followers of ') + scheduledPostDetail?.organisation?.name}>
                <span style={{ color: 'var(--color-gray3)', display: 'flex', alignItems: 'center' }}>
                  <AudienceIcon icon={Icons.users} size={14} />
                  <span style={{ fontSize: '13px', color: '#616161' }}>{t('Approved Followers')}</span>
                </span>
              </Tooltip>
            )}
          </div>
        </SelectContainer>
        <div style={{ position: 'absolute', right: 0, top: '13px', display: 'flex', gap: '9px' }}>
          <Icon
            icon={Icons.trash}
            style={{ width: '18px', cursor: 'pointer' }}
            onClick={() => onDeletePost(scheduledPostDetail, false)}
          />
          <Icon
            icon={Icons.clock_grey}
            style={{ width: '18px', cursor: 'pointer' }}
            onClick={() => onViewSchedulePost(scheduledPostDetail, false)}
          />
        </div>
        <div style={{ display: 'flex', flexDirection: 'column', position: 'relative' }}>
          <div
            style={{
              fontSize: '14px',
              marginTop: '12px',
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word',
            }}
          >
            {scheduledPostDetail?.text}
          </div>
          {scheduledPostDetail?.image ? (
            <ImageContainer>
              <Image
                style={{
                  marginTop: '12px',
                  width: '500px',
                  height: '250px',
                  borderRadius: '18px',
                  overflow: 'hidden',
                  objectFit: 'contain',
                  zIndex: 5,
                  border: 'none',
                }}
                id={scheduledPostDetail?.image}
                width={300}
              />
            </ImageContainer>
          ) : null}
        </div>
      </div>
      <Row style={{ marginTop: '12px' }}>
        <div style={{ flex: 1, display: 'flex', justifyContent: 'flex-end', columnGap: '10px' }}>
          <Button
            type="primary"
            onClick={() => {
              resetScheduledFormData();
              setShowScheduledPostDetail(false);
              setShowAllSchedueModal(true);
            }}
            loading={deleteOnRemovePostLoading}
          >
            {t('Back')}
          </Button>
        </div>
      </Row>
    </>
  ) : (
    <>
      <SubTitle>
        {scheduledFormData.dateTime && scheduledFormData?.timeZone ? (
          <>
            <Icon icon={Icons.clock_grey} style={{ width: '16px' }} />{' '}
            {`Posting on ${moment(scheduledFormData?.dateTime).format('dddd, MMMM Do YYYY, [at] hh:mm A')} (GMT ${
              typeof scheduledFormData?.timeZone === 'string'
                ? moment().tz(scheduledFormData?.timeZone).format('Z')
                : typeof scheduledFormData?.timeZone === 'object'
                ? moment().tz(scheduledFormData?.timeZone?.value).format('Z')
                : ''
            })`}
          </>
        ) : (
          <></>
        )}
      </SubTitle>
      <Row gutter={[6, 6]}>
        <Col span={24}>
          {/* Date / Time */}
          <DateTimePicker
            value={dateTime !== '' ? moment(dateTime) : ''}
            label
            onChange={handleChangeDateTime}
            disabledDate={(date) => {
              return moment(date).isBefore(moment().subtract(1, 'hour'));
            }}
            selectedDate={moment(scheduledFormData.dateTime)}
            disabledTimes
          />
          {errors.dateTime && <ErrorText>{errors.dateTime}</ErrorText>}
        </Col>
      </Row>
      <Row gutter={[6, 6]}>
        <Col span={24}>
          <HeaderTitle
            style={{ display: 'flex', alignItems: 'center', fontSize: '12px', marginBottom: '6px', marginTop: '16px' }}
          >
            {t('Timezone')}
          </HeaderTitle>
          <StyledTimezone>
            <TimezoneSelect value={timeZone} onChange={handleChangeTimeZone} />
            {errors.timeZone && <ErrorText>{errors.timeZone}</ErrorText>}
            <TimezoneSuffixIcon icon={Icons.chevronDown_blue} style={{ width: '12px', top: '4px' }} />
          </StyledTimezone>
        </Col>
      </Row>
      <Row gutter={[6, 6]}>
        <Typography
          style={{ fontSize: '16px', color: '#067DA9', cursor: 'pointer', marginTop: '16px', fontWeight: 600 }}
          onClick={onShowAllSchedulePostClick}
        >
          {t('View All Scheduled Posts')} <Icon icon={Icons.arrow} style={{ width: '16px' }} />
        </Typography>
      </Row>
      <Row>
        <div
          style={{
            flex: 1,
            display: editSchedule ? 'flex' : '',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginTop: '20px',
          }}
        >
          {editSchedule ? (
            <div style={{ flex: 1 }}>
              <Button
                type="text"
                style={{ fontWeight: 600, fontSize: '14px', color: '#616161' }}
                onClick={() => {
                  setShowScheduledPostModal(false);
                  setEditSchedule(false);
                  resetScheduledFormData();
                }}
              >
                Remove Schedule
              </Button>
            </div>
          ) : (
            <></>
          )}
          <div style={{ display: 'flex', justifyContent: 'flex-end', columnGap: '10px' }}>
            {!isEdit && (
              <Button
                type="default"
                onClick={() => {
                  setShowScheduledPostModal(false);
                  setEditSchedule(false);
                  resetScheduledFormData();
                }}
              >
                {t('Back')}
              </Button>
            )}
            <Button type="primary" onClick={handleSave} loading={updateOnStreamLoading}>
              {t('Save')}
            </Button>
          </div>
        </div>
      </Row>
    </>
  );
}

const SubTitle = styled(Typography.Text)`
  display: flex;
  align-items: center;
  column-gap: 6px;
  margin-bottom: 12px;
  font-size: 13px;
  font-weight: 500;
  margin-top: -15px;
  color: var(--color-text30);
`;

const SubTitle2 = styled(Typography.Text)`
  display: inline-bloack;
  font-size: 12px;
  font-weight: 600;
  margin-top: -15px;
  color: var(--color-text);
`;

const BorderedDiv = styled.div`
  border: 1px solid #f2f2f2;
  margin: 10px 0;
`;

const ErrorText = styled.span`
  color: red;
  font-size: 12px;
`;

const ImageContainer = styled.div`
  margin-right: 10px;
  display: inline-block;
`;

const SelectContainer = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  line-height: 1.3em;
  margin-left: 60px;
  margin-top: -30px;
`;

const OrganisationSelect = styled(Select)`
  border: 1px solid #dcf2fe;
  border-radius: 12px;

  & .ant-select-selector {
    height: 32px !important;

    & .ant-select-selection-item {
      line-height: 30px;
    }
  }
`;

const Scrollable = styled.div`
  max-height: 300px;
  overflow: auto;
  overflow-y: auto;
  padding: 0 10px 0 0;
  &::-webkit-scrollbar {
    width: 5px;
    background-color: #f5f5f5;
  }
  &::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    background-color: #f5f5f5;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: #555;
  }
`;

const WordWrap = styled.span`
  font-size: 14px;
  display: block;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const container = styled.div`
  display: flex;
  flexdirection: column;
  position: relative;
`;

const AudienceIcon = styled(Icon)`
  margin-top: -2px;
  margin-left: 3px;
  margin-right: 5px;
`;

const StyledTimezone = styled.div`
  position: relative;
  cursor: pointer;

  & [class*='control'] {
    border: 1px solid #dcf2fe;
    border-radius: 12px;
    min-height: 40px;
    box-shadow: none;

    &:hover {
      border-color: #22aad4;
    }
  }

  & [class*='singleValue'] {
    max-width: calc(100% - 35px);
  }

  & [class*='indicatorSeparator'] {
    display: none;
  }

  & [class*='indicatorContainer'] {
    display: none;
    color: #3c91c3;
    padding: 0 8px;
  }
`;

const StyledTimezoneSelected = styled.div`
  position: relative;
  cursor: pointer;

  & [class*='control'] {
    border: 1px solid #dcf2fe;
    border-radius: 12px;
    height: 34px;
    min-height: 34px;
    box-shadow: none;

    &:hover {
      border-color: #22aad4;
    }
  }

  & [class*='singleValue'] {
    max-width: calc(100% - 35px);
  }

  & [class*='indicatorSeparator'] {
    display: none;
  }

  & [class*='indicatorContainer'] {
    display: none;
    color: #3c91c3;
    padding: 0 8px;
  }
`;

const TimezoneSuffixIcon = styled(Icon)`
  position: absolute;
  right: 15px;
  top: 1px;
`;
