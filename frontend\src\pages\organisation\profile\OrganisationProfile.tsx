import React from 'react';
import { useQuery } from '@apollo/client';
import { Route, RouteComponentProps, useLocation } from 'react-router-dom';

import { ContainerCentered } from '@components/Layout/Container';
import { Layout } from '@components/Layout/Layout';
import { Loader } from '@components/Loader';
import { NotFoundError, UnauthorisedError } from '@components/Results';
import { FollowerStatus, OrganisationPage, PartnershipRequestStatus } from '@GraphQLTypes';
import { Routes } from '@src/routes/Routes';
import { OrganisationHeader } from './components/OrganisationHeader';
import { OrganisationAbout } from './pages/About/OrganisationAbout';
import { OrganisationEvents } from './pages/Events/OrganisationEvents';
import { OrganisationPeople } from './pages/People/OrganisationPeople';
import { OrganisationPosts } from './pages/Posts/OrganisationPosts';
import { Resources } from './pages/resources/Resources';
import { OrganisationWebinars } from './pages/Webinars/OrganisationWebinars';
import { GET_ORGANISATION_BY_VANITY_ID, OrganisationQueryData } from './queries';
import { canViewPage } from './canViewPage';
import { OrganisationAnalytics } from './pages/analytics/OrganisationAnalytics';
import { useIsHabloStaff } from '@src/utils/hooks/useIsHabloStaff';
import { UpdatePrimaryOrganisationConfirmation } from '@src/pages/profile/Info/Info';
import { ProfileDataWithPaginatedConnections, GET_PROFILE_WITH_PAGINATION } from '@src/pages/connect/SideNav/queries';
import { useProfile } from '@src/routes/ProfileProvider';

import { CreatePost as RepostModal } from '@src/pages/organisation/post/create/CreatePost';
import { ReshareModalContextProvider, useReshareModal } from '@src/pages/home/<USER>';
import { TopAgentsWrapper } from './pages/TopAgents/TopAgentsWrapper';

export type OrganisationProfileProps = RouteComponentProps<{ vanityId?: string }>;

export default function OrganisationProfile({ match: { params } }: OrganisationProfileProps) {
  const { vanityId } = params;
  const profileId = useProfile().profile.id;
  const { search } = useLocation();
  const searchParams = new URLSearchParams(search);
  const organisationId = searchParams.get('organisationId');
  const organisationName = searchParams.get('organisationName');

  const { error, loading, data } = useQuery<OrganisationQueryData>(GET_ORGANISATION_BY_VANITY_ID, {
    variables: { vanityId },
  });

  const { loading: profileLoading, data: profileData } = useQuery<ProfileDataWithPaginatedConnections>(
    GET_PROFILE_WITH_PAGINATION,
    {
      variables: {
        profileId,
      },
      fetchPolicy: 'network-only',
    },
  );

  const membership = profileData?.profile.memberships.filter(({ isPrimary }) => isPrimary)[0];

  const isChild = membership?.partnerOrganisation
    ? membership.partnerOrganisation.status === PartnershipRequestStatus.Approved
    : false;

  const { isHabloStaff } = useIsHabloStaff({
    withOrgCheckFlag: false,
    withPermissionCheckFlag: false,
  });

  if (error || (!loading && !data)) {
    return (
      <ContainerCentered>
        <NotFoundError />
      </ContainerCentered>
    );
  }

  if (loading || profileLoading || !data) {
    return (
      <ContainerCentered>
        <Loader useGIF={true} />
      </ContainerCentered>
    );
  }

  if (!loading && data && data.organisation && data.organisation.followerStatus === FollowerStatus.Rejected) {
    return (
      <ContainerCentered>
        <UnauthorisedError />
      </ContainerCentered>
    );
  }

  return (
    <Layout>
      {organisationId && organisationName && membership?.position ? (
        <UpdatePrimaryOrganisationConfirmation
          membership={membership}
          organisationId={organisationId}
          organisationPosition={membership.position}
          organisationName={organisationName}
        />
      ) : null}

      <OrganisationHeader organisation={data.organisation} />

      {canViewPage(OrganisationPage.Home, data.organisation, isHabloStaff) ? (
        <Route
          exact={true}
          path={Routes.organisationProfile}
          render={(props: OrganisationProfileProps) => (
            <ReshareModalContextProvider>
              <OrganisationPosts {...props} organisation={data.organisation} />
              <RepostModalComponent />
            </ReshareModalContextProvider>
          )}
        />
      ) : (
        canViewPage(OrganisationPage.About, data.organisation, isHabloStaff) && (
          <Route
            exact={true}
            path={Routes.organisationProfile}
            render={(props: OrganisationProfileProps) => (
              <OrganisationAbout {...props} organisation={data.organisation} />
            )}
          />
        )
      )}
      {/* As we are showing it in Home/Posts, no need to add another check */}
      {canViewPage(OrganisationPage.Home, data.organisation, isHabloStaff) && !isChild && (
        <Route
          exact={true}
          path={Routes.organisationTopAgents}
          render={(props: OrganisationProfileProps) => <TopAgentsWrapper {...props} organisation={data.organisation} />}
        />
      )}
      {canViewPage(OrganisationPage.Analytics, data.organisation, isHabloStaff) && (
        <Route
          exact={true}
          path={Routes.organisationAnalytics}
          render={(props: OrganisationProfileProps) => (
            <OrganisationAnalytics {...props} organisation={data.organisation} />
          )}
        />
      )}
      {canViewPage(OrganisationPage.About, data.organisation, isHabloStaff) && (
        <Route
          exact={true}
          path={Routes.organisationAbout}
          render={(props: OrganisationProfileProps) => (
            <OrganisationAbout {...props} organisation={data.organisation} />
          )}
        />
      )}
      {canViewPage(OrganisationPage.Webinars, data.organisation, isHabloStaff) && !isChild && (
        <Route
          exact={true}
          path={Routes.organisationWebinars}
          render={(props: OrganisationProfileProps) => (
            <OrganisationWebinars {...props} organisation={data.organisation} />
          )}
        />
      )}
      {canViewPage(OrganisationPage.Events, data.organisation, isHabloStaff) && !isChild && (
        <Route
          exact={true}
          path={Routes.organisationEvents}
          render={(props: OrganisationProfileProps) => (
            <OrganisationEvents {...props} organisation={data.organisation} />
          )}
        />
      )}
      {canViewPage(OrganisationPage.Resources, data.organisation, isHabloStaff) && !isChild && (
        <Route
          exact={true}
          path={Routes.organisationResources}
          render={(props: OrganisationProfileProps) => <Resources {...props} organisation={data.organisation} />}
        />
      )}
      {canViewPage(OrganisationPage.People, data.organisation, isHabloStaff) && !isChild && (
        <Route
          exact={true}
          path={Routes.organisationPeople}
          render={(props: OrganisationProfileProps) => (
            <OrganisationPeople {...props} organisation={data.organisation} />
          )}
        />
      )}
    </Layout>
  );
}

// Creating a child component to consume the context in Modal itself.
const RepostModalComponent = () => {
  const { activity, showReshareModal, updateShowReshareModal } = useReshareModal();
  return (
    <>
      {showReshareModal && (
        <RepostModal showModal={showReshareModal} toggleModal={updateShowReshareModal} activity={activity} />
      )}
    </>
  );
};
