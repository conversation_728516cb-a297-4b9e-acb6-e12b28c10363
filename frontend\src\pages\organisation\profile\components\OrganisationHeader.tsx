import { Badge, Col, Modal, Row, Space, Typography, message } from 'antd';
import { useEffect, useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { Link, useLocation } from 'react-router-dom';
import { useMutation } from '@apollo/client';

import { Icons } from '@components/icons/Icon';
import { Avatar, Image } from '@components/Image/Image';
import { Tag } from '@components/Tag';
import { DOMAIN_ORIGIN, ORGANISATION_TYPES } from 'consts';
import { Organisation, parentOrganisation, PartnershipRequestStatus } from '@GraphQLTypes';
import { GUTTER_MD, GUTTER_SM_PX } from '@src/theme';
import { Routes } from '@src/routes/Routes';
import { encodePathParams } from '@utils/encodePathParams';
import { EditImageBackgroundIcon, EditImageIcon } from '@components/EditButtons';
import { UPDATE_ORGANISATION, UpdateOrganisationData, UpdateOrganisationVariables } from '../../settings/queries';
import { DropdownMenu } from '@components/DropdownMenu';
import { hasPermission, OrganisationActions } from '@src/pages/organisation/permissions';
import { ContainerCard } from '@src/components/Layout/Container';
import { ImageUploadWithCrop } from '@components/Image/ImageUploadWithCrop';
import { ImageUploadContainer } from '@components/Image/ImageUploadContainer';
import { ButtonMore } from '@components/Buttons/ButtonMore';
import { OrganisationFollowActions } from './OrganisationFollowActions';
import { OrganisationFollowers } from './OrganisationFollowers';
import { Clickable } from '@components/Buttons/Clickable';
import { getFormattedLocation } from '@components/LocationSearch/LocationSearch';
import { ErrorHandler } from '@components/ErrorHandler';
import navTitleService from '@src/services/navTitleService';
import { useIsHabloStaff } from '@src/utils/hooks/useIsHabloStaff';
import { ParentOrganisationName } from '@src/pages/profile/Info/OrganisationName';
import TierIcon from '@src/components/TierIcon';
import { useProfile } from '@src/routes/ProfileProvider';

export type HeaderOrganisation = Required<
  Pick<
    Organisation,
    | 'id'
    | 'vanityId'
    | 'name'
    | 'image'
    | 'backgroundImage'
    | 'isPublic'
    | 'privacy'
    | 'isMember'
    | 'isPaid'
    | 'hasClubHabloSubscription'
    | 'activeTier'
    | 'permissions'
    | 'type'
    | 'location'
    | 'followers'
    | 'followerStatus'
    | 'followersActiveCount'
    | 'followersPendingCount'
    | 'followersRejectedCount'
    | 'parentOrganisationDetails'
  >
>;

type Props = {
  organisation: HeaderOrganisation;
};

const StyledLink = styled(Link)`
  color: var(--color-text);
`;

export function OrganisationHeader({ organisation }: Props) {
  const { t } = useTranslation();
  const { profile } = useProfile();
  const organisationId = organisation.id!;
  const vanityId = organisation.vanityId!;
  const membership = profile.memberships?.[0];
  const isChild = membership?.partnerOrganisation
    ? membership.partnerOrganisation.status === PartnershipRequestStatus.Approved
    : false;
  const canUpdateOrgSettings =
    !!organisationId && hasPermission(OrganisationActions.updateOrganisation, organisation.permissions);
  const canUpdateOrgPage =
    !!organisationId && hasPermission(OrganisationActions.updateOrganisationPage, organisation.permissions);
  const canManageFollowers =
    !!organisationId && hasPermission(OrganisationActions.manageFollowers, organisation.permissions);
  const canViewDashboard = hasPermission(OrganisationActions.viewDashboard, organisation.permissions);

  const { search } = useLocation();
  const searchParams = useMemo(() => new URLSearchParams(search), [search]);
  const showFollowersModalOnLoad = canManageFollowers && searchParams.get('modal') === 'followers';

  const [showFollowersModal, toggleFollowersModal] = useState(showFollowersModalOnLoad || false);
  const [updateOrganisation] = useMutation<UpdateOrganisationData, UpdateOrganisationVariables>(UPDATE_ORGANISATION);

  const { isHabloStaff } = useIsHabloStaff({
    withOrgCheckFlag: false,
    withPermissionCheckFlag: false,
  });

  useEffect(() => {
    navTitleService(organisation?.name ?? 'Organisation');
  }, []);

  let fontSizeFactor = (5 * window.innerWidth) / 1920;

  const getReferralLink = () => {
    return `${DOMAIN_ORIGIN}/signup/${organisation.vanityId}`;
  };

  if (window.innerWidth < 568) {
    return (
      <Container>
        <Header>
          {canUpdateOrgPage ? (
            <ImageUploadWithCrop
              aspect={6.19}
              disallowRotate
              minZoom={0.2}
              restrictPosition={false}
              allowColorChooser
              onChange={({ public_id }) =>
                updateOrganisation({
                  variables: { organisationId, organisationData: { backgroundImage: public_id } },
                })
              }
            >
              <ImageUploadContainer>
                <BackgroundPicture id={organisation.backgroundImage} width={1238} />
                <EditImageBackgroundIcon />
              </ImageUploadContainer>
            </ImageUploadWithCrop>
          ) : (
            <BackgroundPicture id={organisation.backgroundImage} width={1238} />
          )}

          <ProfilePictureContainer>
            {canUpdateOrgPage ? (
              <ImageUploadWithCrop
                aspect={1}
                restrictPosition={false}
                minZoom={0.1}
                disallowRotate
                onChange={({ public_id }) =>
                  updateOrganisation({
                    variables: { organisationId, organisationData: { image: public_id } },
                  })
                }
              >
                <ImageUploadContainer style={{ position: 'relative' }}>
                  <Avatar id={organisation.image} width={81} border={true} />
                  <EditImageIcon />
                </ImageUploadContainer>
              </ImageUploadWithCrop>
            ) : (
              <Avatar id={organisation.image} width={81} border={true} />
            )}
          </ProfilePictureContainer>
        </Header>
        <Row style={{ justifyContent: 'center' }}>
          <Col flex={1} style={{ textAlign: 'center' }}>
            <Typography.Title ellipsis={true} style={{ fontSize: '24px', textAlign: 'center' }}>
              {organisation.name || <Typography.Text type="secondary">{t('Name')} </Typography.Text>}
              {organisation.activeTier && organisation.hasClubHabloSubscription && (
                <TierIcon position="top" tier={organisation.activeTier} spacing="8px" />
              )}
            </Typography.Title>
          </Col>
        </Row>
        <Row style={{ justifyContent: 'center' }}>
          <Col style={{ textAlign: 'center' }}>
            <Space
              size={GUTTER_MD}
              style={{ width: '100%', flexWrap: 'wrap', minHeight: '39px', justifyContent: 'center' }}
            >
              <Tag icon={Icons.location} align="flex-end" style={{ fontSize: `${12 * fontSizeFactor}px` }}>
                {getFormattedLocation(organisation.location) || (
                  <Typography.Text type="secondary">{t('Location')}</Typography.Text>
                )}
              </Tag>
              <Tag icon={Icons.job} align="flex-end" style={{ fontSize: `${12 * fontSizeFactor}px` }}>
                {ORGANISATION_TYPES[organisation.type!]}
              </Tag>
            </Space>
          </Col>
          <Col style={{ textAlign: 'center' }}>
            <Space size={GUTTER_MD} style={{ width: '100%', flexWrap: 'wrap', minHeight: '39px' }}>
              {(organisation.isMember || isHabloStaff) && (
                <>
                  <Tag
                    icon={Icons.users}
                    color="var(--color-primary)"
                    align="flex-end"
                    style={{ fontSize: `${12 * fontSizeFactor}px`, marginBottom: '10px' }}
                  >
                    <Clickable primary={true} onClick={() => toggleFollowersModal(true)}>
                      <span>{t('{{count}} Followers', { count: organisation.followersActiveCount || 0 })}</span>
                    </Clickable>
                    {canManageFollowers && (
                      <div style={{ marginLeft: GUTTER_SM_PX }}>
                        <Badge count={organisation.followersPendingCount} />
                      </div>
                    )}
                  </Tag>
                  <Modal
                    title={t("{{organisationName}}'s Followers", { organisationName: organisation.name })}
                    width={window.innerWidth < 568 ? 500 : 300}
                    footer={null}
                    open={showFollowersModal}
                    onCancel={() => toggleFollowersModal(false)}
                    wrapClassName="modal-wrap-followers"
                  >
                    <ErrorHandler>
                      <OrganisationFollowers editable={canManageFollowers} organisation={organisation} />
                    </ErrorHandler>
                  </Modal>
                </>
              )}
              {/* : (

                    <Tag icon={Icons.users} align="flex-end">
                      {t('{{count}} Followers', { count: organisation.followersActiveCount || 0 })}
                    </Tag>
                  ))} */}
            </Space>
          </Col>
        </Row>
        <Row style={{ justifyContent: 'space-evenly', gap: '10px' }}>
          {organisation.parentOrganisationDetails &&
            organisation.parentOrganisationDetails.length > 0 &&
            organisation.parentOrganisationDetails.map((el: parentOrganisation) => {
              return (
                <OrganisationContainer>
                  <OrganisationImageContainer>
                    <Image id={el.image} />
                  </OrganisationImageContainer>
                  <ParentOrganisationName name={el.name} vanityId={el.vanityId} />
                </OrganisationContainer>
              );
            })}
        </Row>
        <Row style={{ justifyContent: 'center', marginTop: '15px' }}>
          <Col style={{ textAlign: 'center' }}>
            <OrganisationFollowActions organisation={organisation} />
          </Col>
        </Row>
      </Container>
    );
  } else {
    return (
      <Container>
        <Header>
          {canUpdateOrgPage ? (
            <ImageUploadWithCrop
              aspect={6.19}
              disallowRotate
              minZoom={0.2}
              restrictPosition={false}
              allowColorChooser
              onChange={({ public_id }) =>
                updateOrganisation({
                  variables: { organisationId, organisationData: { backgroundImage: public_id } },
                })
              }
            >
              <ImageUploadContainer>
                <BackgroundPicture id={organisation.backgroundImage} width={1238} />
                <EditImageBackgroundIcon />
              </ImageUploadContainer>
            </ImageUploadWithCrop>
          ) : (
            <BackgroundPicture id={organisation.backgroundImage} width={1238} />
          )}

          <ProfilePictureContainer>
            {canUpdateOrgPage ? (
              <ImageUploadWithCrop
                aspect={1}
                restrictPosition={false}
                minZoom={0.1}
                disallowRotate
                onChange={({ public_id }) =>
                  updateOrganisation({
                    variables: { organisationId, organisationData: { image: public_id } },
                  })
                }
              >
                <ImageUploadContainer style={{ position: 'relative' }}>
                  <Avatar id={organisation.image} width={124} border={true} />
                  <EditImageIcon />
                </ImageUploadContainer>
              </ImageUploadWithCrop>
            ) : (
              <Avatar id={organisation.image} width={124} border={true} />
            )}
          </ProfilePictureContainer>

          <RightActions>
            {organisation.parentOrganisationDetails &&
              organisation.parentOrganisationDetails.length > 0 &&
              organisation.parentOrganisationDetails.map((el: parentOrganisation) => {
                return (
                  <OrganisationContainer>
                    <OrganisationImageContainer>
                      <Image id={el.image} />
                    </OrganisationImageContainer>
                    <ParentOrganisationName name={el.name} vanityId={el.vanityId} />
                  </OrganisationContainer>
                );
              })}
            <div style={{ marginLeft: '20px' }}>
              <OrganisationFollowActions organisation={organisation} />
            </div>
            {canUpdateOrgPage ? (
              <DropdownMenu button={<ButtonMore />}>
                <DropdownMenu.Item icon={Icons.chart}>
                  <StyledLink to={encodePathParams(Routes.organisationAnalytics, { vanityId })}>
                    {t('Analytics')}
                  </StyledLink>
                </DropdownMenu.Item>
                {canUpdateOrgSettings && !isChild && (
                  <DropdownMenu.Item icon={Icons.edit}>
                    <StyledLink to={encodePathParams(Routes.organisationSettingsInfo, { organisationId })}>
                      {t('Edit Page info')}
                    </StyledLink>
                  </DropdownMenu.Item>
                )}
                {canUpdateOrgSettings && !isChild && (
                  <DropdownMenu.Item icon={Icons.userAdd}>
                    <StyledLink to={encodePathParams(Routes.organisationSettingsUserRoles, { organisationId })}>
                      {t('Manage Roles')}
                    </StyledLink>
                  </DropdownMenu.Item>
                )}
                {canUpdateOrgSettings && (
                  <DropdownMenu.Item icon={Icons.posts}>
                    <StyledLink
                      to={encodePathParams(
                        isChild ? Routes.organisationSettingsPartners : Routes.organisationSettings,
                        { organisationId },
                      )}
                    >
                      {t('Manage Organisation')}
                    </StyledLink>
                  </DropdownMenu.Item>
                )}
                {organisation.hasClubHabloSubscription && (canViewDashboard || isHabloStaff) && (
                  <DropdownMenu.Item icon={Icons.kudosFlat}>
                    <StyledLink
                      to={encodePathParams(Routes.organisationClubHabloDashboard, { vanityId: organisation.vanityId })}
                    >
                      {t('Club Hablo Dashboard')}
                    </StyledLink>
                  </DropdownMenu.Item>
                )}
                {!isChild && (
                  <DropdownMenu.Item
                    icon={Icons.share}
                    onClick={async () => {
                      const referralLink = getReferralLink();
                      try {
                        await navigator.clipboard.writeText(referralLink);
                        message.success(t('Referral signup link copied to clipboard!'));
                      } catch (err) {
                        message.error(t('Failed to copy referral link.'));
                        console.error('Failed to copy: ', err);
                      }
                    }}
                  >
                    {t('Copy Referral Signup Link')}
                  </DropdownMenu.Item>
                )}
              </DropdownMenu>
            ) : null}
          </RightActions>
        </Header>

        <Row>
          <Col flex={1}>
            <Typography.Title ellipsis={true}>
              {organisation.name || <Typography.Text type="secondary">{t('Name')}</Typography.Text>}
              {organisation.activeTier && organisation.hasClubHabloSubscription && (
                <TierIcon position="top" tier={organisation.activeTier} spacing="8px" />
              )}
            </Typography.Title>
          </Col>
          <Col>
            <Space size={GUTTER_MD} style={{ width: '100%', flexWrap: 'wrap', minHeight: '39px' }}>
              <Tag icon={Icons.location} align="flex-end">
                {getFormattedLocation(organisation.location) || (
                  <Typography.Text type="secondary">{t('Location')}</Typography.Text>
                )}
              </Tag>
              <Tag icon={Icons.job} align="flex-end">
                {ORGANISATION_TYPES[organisation.type!]}
              </Tag>
              {(organisation.isMember || isHabloStaff) && (
                <>
                  <Tag icon={Icons.users} color="var(--color-primary)" align="flex-end">
                    <Clickable primary={true} onClick={() => toggleFollowersModal(true)}>
                      <span>{t('{{count}} Followers', { count: organisation.followersActiveCount || 0 })}</span>
                    </Clickable>
                    {canManageFollowers && (
                      <div style={{ marginLeft: GUTTER_SM_PX }}>
                        <Badge count={organisation.followersPendingCount} />
                      </div>
                    )}
                  </Tag>
                  <Modal
                    title={t("{{organisationName}}'s Followers", { organisationName: organisation.name })}
                    width={600}
                    footer={null}
                    open={showFollowersModal}
                    onCancel={() => toggleFollowersModal(false)}
                    wrapClassName="modal-wrap-followers"
                  >
                    <ErrorHandler>
                      <OrganisationFollowers editable={canManageFollowers} organisation={organisation} />
                    </ErrorHandler>
                  </Modal>
                </>
              )}
              {/* : (

                    <Tag icon={Icons.users} align="flex-end">
                      {t('{{count}} Followers', { count: organisation.followersActiveCount || 0 })}
                    </Tag>
                  ))} */}
            </Space>
          </Col>
        </Row>
      </Container>
    );
  }
}

const Container = styled(ContainerCard)`
  padding: 220px 40px 40px 40px;

  .ant-typography {
    margin-bottom: 0;
  }
  @media screen and (max-width: 568px) {
    padding-top: 145px;
    padding-bottom: 20px;
  }
`;

const Header = styled.div`
  display: flex;
  flex: 1;
  margin-bottom: ${GUTTER_SM_PX};
`;

const BackgroundPicture = styled(Image)`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  width: 100%;

  img {
    aspect-ratio: 16 / 9;
  }

  @media screen and (max-width: 568px) {
    height: 100px;
  }
`;

const ProfilePictureContainer = styled.div`
  position: relative;
  margin-top: -85px;
  @media screen and (max-width: 568px) {
    margin: auto;
    margin-top: -85px;
  }
`;

const RightActions = styled(Space).attrs({ size: GUTTER_MD, align: 'start' })`
  flex: 1;
  justify-content: flex-end;
`;

const OrganisationContainer = styled.div`
  display: flex;
  gap: 5px;
  align-items: center;
  height: 32px;
`;

const OrganisationImageContainer = styled.div`
  width: 28px;
  height: 28px;
  border-radius: 8px;
  border: 1px solid #eaeaea;
`;
