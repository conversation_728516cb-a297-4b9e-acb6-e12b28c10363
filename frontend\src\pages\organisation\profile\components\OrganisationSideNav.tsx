import { Link, Redirect } from 'react-router-dom';
import React, { useState } from 'react';
import { Space } from 'antd';
import { useTranslation } from 'react-i18next';

import { SideNavButton } from '@components/SideNav/SideNavButton';
import { Icons } from '@components/icons/Icon';
import { GUTTER_MD_PX } from '@src/theme';
import { Routes } from '@src/routes/Routes';
import { OrganisationProfileProps } from '../OrganisationProfile';
import { ActivityType, Organisation, OrganisationPage, PartnershipRequestStatus } from '@GraphQLTypes';
import { ContainerCard } from '@components/Layout/Container';
import { canViewPage } from '../canViewPage';
import { encodePathParams } from '@utils/encodePathParams';
import { useCreateActivity } from '../../useCreateAnalytics';
import { useIsHabloStaff } from '@src/utils/hooks/useIsHabloStaff';
import { hasPermission, OrganisationActions } from '../../permissions';
import { useProfile } from '@src/routes/ProfileProvider';

type Props = OrganisationProfileProps & {
  organisation: Organisation;
};

export function OrganisationSideNav({ organisation, match: { path, params } }: Props) {
  const { vanityId } = params;
  const { profile } = useProfile();
  const { t } = useTranslation();

  const [testOrgCheckCompleted, setTestOrgCheckCompleted] = useState(false);
  const [permissionCheckCompleted, setPermissionCheckCompleted] = useState(false);

  const { isHabloStaff } = useIsHabloStaff({
    withOrgCheckFlag: true,
    withPermissionCheckFlag: true,
    orgCheckFlag: testOrgCheckCompleted,
    permissionCheckFlag: permissionCheckCompleted,
    setOrgCheckFlag: setTestOrgCheckCompleted,
    setPermissionCheckFlag: setPermissionCheckCompleted,
  });

  const canViewDashboard = hasPermission(OrganisationActions.viewDashboard, organisation.permissions);
  const membership = profile.memberships?.[0];
  const isChild = membership?.partnerOrganisation
    ? membership.partnerOrganisation.status === PartnershipRequestStatus.Approved
    : false;

  useCreateActivity(
    () => ({
      organisationId: organisation.id,
      type: ActivityType.OrganisationPageView,
      data: {
        page: path.split('/').pop(),
      },
    }),
    [organisation, path],
  );

  return (
    <ContainerCard
      style={{
        padding: window.innerWidth < 568 ? 0 : GUTTER_MD_PX,
        paddingTop: window.innerWidth < 568 ? '5px' : GUTTER_MD_PX,
        minHeight: '40px',
        overflowX: window.innerWidth < 568 ? 'auto' : 'hidden',
      }}
    >
      {permissionCheckCompleted && !canViewPage(OrganisationPage.Home, organisation, isHabloStaff) && (
        <Redirect to={encodePathParams(Routes.organisationAbout, { vanityId })} />
      )}

      <Space direction={window.innerWidth < 568 ? 'horizontal' : 'vertical'} size={4} style={{ width: '100%' }}>
        {canViewPage(OrganisationPage.Analytics, organisation, isHabloStaff) &&
          (path.split('/').pop()?.includes('analytics') || window.innerWidth < 568) && (
            <Link to={encodePathParams(Routes.organisationAnalytics, { vanityId })}>
              <SideNavButton active={path === Routes.organisationAnalytics} icon={Icons.chart}>
                {t('Analytics')}
              </SideNavButton>
            </Link>
          )}
        {window.innerWidth < 568 && organisation.hasClubHabloSubscription && (canViewDashboard || isHabloStaff) && (
          <Link to={encodePathParams(Routes.organisationClubHabloDashboard, { vanityId })}>
            <SideNavButton active={path === Routes.organisationClubHabloDashboard} icon={Icons.kudosFlat}>
              {t('Club Hablo')}
            </SideNavButton>
          </Link>
        )}
        {canViewPage(OrganisationPage.Home, organisation, isHabloStaff) && (
          <Link to={encodePathParams(Routes.organisationProfile, { vanityId })}>
            <SideNavButton active={path === Routes.organisationProfile} icon={Icons.posts}>
              {t('Posts')}
            </SideNavButton>
          </Link>
        )}
        {window.innerWidth < 568 &&
          organisation.hasClubHabloSubscription &&
          canViewPage(OrganisationPage.Home, organisation, isHabloStaff) && (
            <Link to={encodePathParams(Routes.organisationTopAgents, { vanityId })}>
              <SideNavButton active={path === Routes.organisationTopAgents} icon={Icons.star}>
                {t('Top Agents')}
              </SideNavButton>
            </Link>
          )}
        {canViewPage(OrganisationPage.About, organisation, isHabloStaff) && (
          <Link to={encodePathParams(Routes.organisationAbout, { vanityId })}>
            <SideNavButton active={path === Routes.organisationAbout} icon={Icons.info}>
              {t('About')}
            </SideNavButton>
          </Link>
        )}
        {canViewPage(OrganisationPage.Webinars, organisation, isHabloStaff) && !isChild && (
          <>
            <Link to={encodePathParams(Routes.organisationWebinars, { vanityId })}>
              <SideNavButton active={path === Routes.organisationWebinars} icon={Icons.webinar}>
                {t('Videos')}
              </SideNavButton>
            </Link>
          </>
        )}
        {canViewPage(OrganisationPage.Events, organisation, isHabloStaff) && !isChild && (
          <Link to={encodePathParams(Routes.organisationEvents, { vanityId })}>
            <SideNavButton active={path === Routes.organisationEvents} icon={Icons.calendar}>
              {t('Events')}
            </SideNavButton>
          </Link>
        )}
        {canViewPage(OrganisationPage.Resources, organisation, isHabloStaff) && !isChild && (
          <Link to={encodePathParams(Routes.organisationResources, { vanityId })}>
            <SideNavButton active={path === Routes.organisationResources} icon={Icons.resources}>
              {t('Resources')}
            </SideNavButton>
          </Link>
        )}
        {canViewPage(OrganisationPage.People, organisation, isHabloStaff) && !isChild && (
          <Link to={encodePathParams(Routes.organisationPeople, { vanityId })}>
            <SideNavButton active={path === Routes.organisationPeople} icon={Icons.users}>
              {t('People')}
            </SideNavButton>
          </Link>
        )}
      </Space>
    </ContainerCard>
  );
}
