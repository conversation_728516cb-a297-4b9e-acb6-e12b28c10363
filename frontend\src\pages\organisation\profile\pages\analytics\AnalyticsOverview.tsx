import React, { useEffect, useState } from 'react';
import { Col, Row } from 'antd';
import { useQuery } from '@apollo/client';
import { useTranslation } from 'react-i18next';

import { ContainerCentered } from '@components/Layout/Container';
import { Loader } from '@components/Loader';
import { DataFetchingError } from '@components/Results';
import { AnalyticsResponse, Organisation, PartnershipRequestStatus } from '@GraphQLTypes';
import { GUTTER_MD, GUTTER_MD_PX } from '@src/theme';
import { GET_AGGREGATED_ANALYTICS, GET_ANALYTICS, GetAnalyticsData, GetAnalyticsVariables } from './queries';
import { StatOverview } from '@components/charts/StatOverview';
import { calculateDelta } from './calculateDelta';
import dayjs from 'dayjs';
import { getPrimaryMembership } from '@src/utils/getOrganisationName';
import { useProfile } from '@src/routes/ProfileProvider';

type Props = {
  organisation: Organisation;
  dates: {
    start: string;
    end: string;
  };
  datesDelta: {
    start: string;
    end: string;
  };
};

export function AnalyticsOverview({ organisation, dates, datesDelta }: Props) {
  const { t } = useTranslation();
  const { profile } = useProfile();
  const membership = getPrimaryMembership(profile);
  const isChild = membership?.partnerOrganisation
    ? membership.partnerOrganisation.status === PartnershipRequestStatus.Approved
    : false;

  const [deltaAnalytics, setDeltaAnalytics] = useState<AnalyticsResponse | null>(null);
  const [analytics, setAnalytics] = useState<AnalyticsResponse | null>(null);

  const { error, loading, data } = useQuery<GetAnalyticsData, GetAnalyticsVariables>(isChild ? GET_ANALYTICS : GET_AGGREGATED_ANALYTICS, {
    variables: {
      startDate: dates.start,
      endDate: dayjs(dates.end).endOf('day').toString(),
      organisationId: isChild ? (membership?.partnerOrganisation?.id || '') : organisation.id,
    },
  });

  const {
    error: deltaError,
    loading: deltaLoading,
    data: deltaData,
  } = useQuery<GetAnalyticsData, GetAnalyticsVariables>(isChild ? GET_ANALYTICS : GET_AGGREGATED_ANALYTICS, {
    variables: {
      startDate: datesDelta.start,
      endDate: datesDelta.end,
      organisationId: isChild ? (membership?.partnerOrganisation?.id || '') : organisation.id,
    },
  });

  useEffect(() => {
    if (data) {
      if (data.analytics) {
        setAnalytics(data.analytics);
      } else if (data.aggregatedAnalytics) {
        setAnalytics(data.aggregatedAnalytics);
      }
    }
  }, [data]);

  useEffect(() => {
    if (deltaData) {
      if (deltaData.analytics) {
        setDeltaAnalytics(deltaData.analytics);
      } else if (deltaData.aggregatedAnalytics) {
        setDeltaAnalytics(deltaData.aggregatedAnalytics);
      }
    }
  }, [deltaData]);

  if (error || deltaError) {
    return <DataFetchingError />;
  }

  if (deltaLoading || loading || !data || !deltaData || !analytics || !deltaAnalytics) {
    return (
      <ContainerCentered>
        <Loader />
      </ContainerCentered>
    );
  }

  const renderPostStats = () => {
    return (
      <>
        <Col
          span={window.innerWidth < 568 ? 24 : 8}
          style={{ marginBottom: window.innerWidth < 568 ? GUTTER_MD_PX : 0 }}
        >
          <StatOverview
            color="#0093C7"
            title={t('Post Impressions')}
            subTitle={t('Total Impressions')}
            data={analytics.PostImpressions}
            delta={calculateDelta(analytics.PostImpressions, deltaAnalytics.PostImpressions)}
          />
        </Col>
        <Col
          span={window.innerWidth < 568 ? 24 : 8}
          style={{ marginBottom: window.innerWidth < 568 ? GUTTER_MD_PX : 0 }}
        >
          <StatOverview
            color="#0093C7"
            title={t('Post Interactions')}
            subTitle={t('Total Interactions')}
            data={analytics.PostInteractions}
            delta={calculateDelta(analytics.PostInteractions, deltaAnalytics.PostInteractions)}
          />
        </Col>
      </>
    );
  }

  return (
    <>
      <Row
        gutter={window.innerWidth < 568 ? 0 : GUTTER_MD}
        style={{ marginBottom: GUTTER_MD_PX }}
        className={'custom-responsive-row'}
      >
        <Col
          span={window.innerWidth < 568 ? 24 : 8}
          style={{ marginBottom: window.innerWidth < 568 ? GUTTER_MD_PX : 0 }}
        >
          <StatOverview
            color="#0093C7"
            title={t('Page Views')}
            subTitle={t('Total Page Views')}
            data={analytics.OrganisationPageView}
            delta={calculateDelta(analytics.OrganisationPageView, deltaAnalytics.OrganisationPageView)}
          />
        </Col>
        {!isChild ? (
          <>
            <Col
              span={window.innerWidth < 568 ? 24 : 8}
              style={{ marginBottom: window.innerWidth < 568 ? GUTTER_MD_PX : 0 }}
            >
              <StatOverview
                color="#0093C7"
                title={t('Page Followers')}
                subTitle={t('New Followers')}
                data={analytics.OrganisationFollower}
                delta={calculateDelta(analytics.OrganisationFollower, deltaAnalytics.OrganisationFollower)}
              />
            </Col>
            <Col
              span={window.innerWidth < 568 ? 24 : 8}
              style={{ marginBottom: window.innerWidth < 568 ? GUTTER_MD_PX : 0 }}
            >
              <StatOverview
                color="#0093C7"
                title={t('Video Views')}
                subTitle={t('Total Video Views')}
                data={analytics.WebinarVideoView}
                delta={calculateDelta(analytics.WebinarVideoView, deltaAnalytics.WebinarVideoView)}
              />
            </Col>
          </>
        ) : renderPostStats()}
      </Row>

      {isChild ? null : (
        <Row gutter={window.innerWidth < 568 ? 0 : GUTTER_MD} className={'custom-responsive-row'}>
          {renderPostStats()}
        </Row>
      )}
    </>
  );
}
