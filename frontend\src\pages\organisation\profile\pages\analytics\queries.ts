import { gql } from '@apollo/client';
import { AnalyticsResponse } from '@GraphQLTypes';

export type GetAnalyticsData = {
  analytics: AnalyticsResponse;
  aggregatedAnalytics: AnalyticsResponse;
};

export type GetAnalyticsVariables = {
  startDate: string;
  endDate: string;
  organisationId: string;
};

export const GET_ANALYTICS = gql`
  query($startDate: Date!, $endDate: Date!, $organisationId: String!) {
    analytics(startDate: $startDate, endDate: $endDate, organisationId: $organisationId) {
      PostSeen {
        date
        value
      }
      EventRSVP {
        date
        value
      }
      OrganisationPageView {
        date
        value
      }
      OrganisationFollower {
        date
        value
      }
      WebinarVideoView {
        date
        value
      }
      PostInteractions {
        date
        value
      }
      PostImpressions {
        date
        value
      }
    }
  }
`;

export const GET_AGGREGATED_ANALYTICS = gql`
  query ($startDate: Date!, $endDate: Date!, $organisationId: String!) {
    aggregatedAnalytics(startDate: $startDate, endDate: $endDate, organisationId: $organisationId) {
      PostSeen {
        date
        value
      }
      EventRSVP {
        date
        value
      }
      OrganisationPageView {
        date
        value
      }
      OrganisationFollower {
        date
        value
      }
      WebinarVideoView {
        date
        value
      }
      PostInteractions {
        date
        value
      }
      PostImpressions {
        date
        value
      }
    }
  }
`;