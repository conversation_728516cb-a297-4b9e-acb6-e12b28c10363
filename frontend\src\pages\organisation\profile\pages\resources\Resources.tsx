import React, { useEffect, useState } from 'react';
import { Button, Col, Empty, Row, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';

import { GUTTER_LG, GUTTER_LG_PX, GUTTER_MD_PX, MARGIN_SM_PX } from '@src/theme';
import { OrganisationProfileProps } from '../../OrganisationProfile';
import { OrganisationSideNav } from '../../components/OrganisationSideNav';
import { Organisation, PartnershipRequestStatus } from '@GraphQLTypes';
import { SubTitle } from '@components/SubTitle';
import { ResourceCard } from '@src/pages/organisation/profile/pages/resources/ResourceCard';
import { CreateResourceModal } from '@src/pages/organisation/profile/pages/resources/CreateResource';
import { InfiniteList } from '@components/InfiniteList/InfiniteList';
import { hasPermission, OrganisationActions } from '@src/pages/organisation/permissions';
import Icon, { Icons } from '@components/icons/Icon';
import { useProfile } from '@src/routes/ProfileProvider';

type Props = OrganisationProfileProps & {
  organisation: Organisation;
};

export function Resources(props: Props) {
  const [showModal, setShowModal] = useState(false);
  const [sortedResources, setSortedResources] = useState<any[]>([]);
  const { t } = useTranslation();
  const { profile } = useProfile();
  const membership = profile.memberships[0];
  const isChild = membership?.partnerOrganisation
    ? membership.partnerOrganisation.status === PartnershipRequestStatus.Approved
    : false;
  const organisationId = props.organisation.id!;
  const canUpdateOrgPage =
    !!organisationId && hasPermission(OrganisationActions.updateOrganisationPage, props.organisation.permissions);

  useEffect(() => {
    const resources = JSON.parse(props.organisation.resources!) as any[];

    if (resources && resources.length) {
      setSortedResources(resources.reverse());
    }
  }, [props.organisation.resources]);

  return (
    <>
      <Row
        gutter={window.innerWidth < 568 ? 0 : GUTTER_LG}
        className={'custom-responsive-row'}
        style={{ marginTop: window.innerWidth < 568 ? MARGIN_SM_PX : GUTTER_LG_PX }}
      >
        <Col span={window.innerWidth < 568 ? 24 : 4} className={'custom-responsive-side-nav-column'}>
          <OrganisationSideNav {...props} />
        </Col>
        <Col span={window.innerWidth < 568 ? 24 : 20}>
          <Row style={{ marginBottom: GUTTER_MD_PX, alignItems: 'center' }}>
            <Col flex={1}>
              <SubTitle style={{ margin: 0, marginLeft: GUTTER_MD_PX }}>{t('Resources')}</SubTitle>
            </Col>
            {canUpdateOrgPage && (
              <Col span={8}>
                <div style={{ display: 'flex', justifyContent: 'end' }}>
                  <Tooltip
                    title={isChild ? t('You cannot create resources as a child organisation.') : undefined}
                    placement="top"
                  >
                    <Button
                      style={{ marginLeft: 'auto' }}
                      type={'primary'}
                      ghost={true}
                      disabled={isChild}
                      onClick={() => setShowModal(true)}
                    >
                      {t('Add a Resource')}
                    </Button>
                  </Tooltip>
                </div>
              </Col>
            )}
          </Row>
          <InfiniteList
            columnCount={window.innerWidth < 568 ? 1 : 3}
            defaultRowHeight={380}
            records={sortedResources}
            totalCount={sortedResources.length || 0}
            loadMore={({ after }) => Promise.resolve()}
            CellComponent={({ data, measure }) => (
              <ResourceCard data={data} organisation={props.organisation} canUpdateOrgPage={canUpdateOrgPage} />
            )}
            noRecords={() => (
              <Empty
                image={<Icon size={40} color="#cccccc" icon={Icons.resources} />}
                imageStyle={{ height: '40px', marginBottom: '24px', marginTop: '48px' }}
                description={<span className="ant-empty-normal">{t('No resources have been added yet.')}</span>}
                style={{ marginLeft: '26px', marginBottom: window.innerWidth < 568 ? '30px' : '0px' }}
              />
            )}
          />
        </Col>
      </Row>
      <CreateResourceModal
        organisation={props.organisation}
        showModal={showModal}
        toggleModal={() => setShowModal(!showModal)}
      />
    </>
  );
}
