import React, { ComponentType } from 'react';
import { Route, RouteComponentProps } from 'react-router-dom';
import { useQuery } from '@apollo/client';
import { RouteProps } from 'react-router';
import { Col, Row } from 'antd';

import { Layout } from '@components/Layout/Layout';
import { Routes } from '@src/routes/Routes';
import { ContainerCard, ContainerCentered } from '@components/Layout/Container';
import { Loader } from '@components/Loader';
import { GetOrganisationData, GET_ORGANISATION } from './queries';
import { DataFetchingError, UnauthorisedError } from '@components/Results';
import { Info } from './pages/info/Info';
import { Invitations } from './pages/invitations/Invitations';
import { ConnectedOrganisations } from './pages/connectedOrganisations/ConnectedOrganisations';
import { Privacy } from './pages/privacy/Privacy';
import { UserRoles } from './pages/userRoles/UserRoles';
import { Employees } from './pages/employees/Employees';
import { Members } from './pages/members/Members';
import { Organisation, OrganisationType, PartnershipRequestStatus } from '@GraphQLTypes';
import { GUTTER_LG } from '@theme';
import { SettingsSideNav } from './components/SettingsSideNav';
import { hasPermission, OrganisationActions } from '../permissions';
import { AddOrganisation } from './pages/connectedOrganisations/AddOrganisation';
import { pendingConnections } from './pages/connectedOrganisations/PendingConnections';
import { ActiveConnections } from './pages/connectedOrganisations/ActiveConnections';
import { PreviousConnections } from './pages/connectedOrganisations/PreviousConnections';
import { Partners } from './pages/partners/PartnersPage'; // Update the import path
import { AddPartner } from './pages/partners/AddPartner';
import { ActivePartners } from './pages/partners/ActivePartners';
import { PreviousPartners } from './pages/partners/PreviousPartners';
import { useProfile } from '@src/routes/ProfileProvider';

export type OrganisationProfileProps = RouteComponentProps<{ organisationId: string }>;

export type OrganisationComponentProps = ComponentType<OrganisationProfileProps & { organisation: Organisation }>;

export default function OrganisationSettings({ match: { params } }: OrganisationProfileProps) {
  const { organisationId } = params;
  const { profile } = useProfile();

  const { error, loading, data } = useQuery<GetOrganisationData>(GET_ORGANISATION, {
    variables: { organisationId },
  });

  if (error) {
    return (
      <ContainerCentered>
        <DataFetchingError />
      </ContainerCentered>
    );
  }

  if (loading || !data) {
    return (
      <ContainerCentered>
        <Loader />
      </ContainerCentered>
    );
  }

  const membership = profile.memberships?.[0];
  const isChild = membership?.partnerOrganisation
    ? membership.partnerOrganisation.status === PartnershipRequestStatus.Approved
    : false;

  const hasAddPartnersPermission = hasPermission(OrganisationActions.addPartners, data.organisation.permissions);

  const isPartnerMenuActive =
    data.organisation.type === OrganisationType.Destination ||
    data.organisation.type === OrganisationType.PrivateSector;

  const isPartnerSubMenuActive =
    data.organisation.type === OrganisationType.Destination && data.organisation.isDMOSubscription;

  return (
    <Layout>
      {!isChild && (
        <OrganisationRoute
          exact={true}
          path={[Routes.organisationSettings, Routes.organisationSettingsInfo]}
          organisation={data.organisation}
          Component={Info}
          permission={OrganisationActions.updateOrganisation}
        />
      )}
      {!isChild && (
        <OrganisationRoute
          exact={true}
          path={Routes.organisationSettingsEmployees}
          organisation={data.organisation}
          Component={Employees}
          permission={OrganisationActions.updateEmployees}
        />
      )}
      {(data.organisation.type === OrganisationType.Association ||
        data.organisation.type === OrganisationType.Consortia) &&
        !isChild && (
          <OrganisationRoute
            exact={true}
            path={Routes.organisationSettingsMembers}
            organisation={data.organisation}
            Component={Members}
            permission={OrganisationActions.updateEmployees}
          />
        )}
      {!isChild && (
        <OrganisationRoute
          exact={true}
          path={Routes.organisationSettingsUserRoles}
          organisation={data.organisation}
          Component={UserRoles}
          permission={OrganisationActions.updateMembers}
        />
      )}
      {!isChild && (
        <OrganisationRoute
          exact={true}
          path={Routes.organisationSettingsPrivacy}
          organisation={data.organisation}
          Component={Privacy}
          permission={OrganisationActions.updatePrivacySettings}
        />
      )}
      {/* Update this route to use PartnersPage */}
      {isPartnerMenuActive && (
        <OrganisationRoute
          exact={true}
          path={Routes.organisationSettingsPartners}
          organisation={data.organisation}
          Component={Partners}
          permission={OrganisationActions.viewPartnersPage} // Use the correct permission
        />
      )}
      {isPartnerSubMenuActive && (
        <>
          {hasAddPartnersPermission && (
            <OrganisationRoute
              exact={true}
              path={Routes.addPartners}
              organisation={data.organisation}
              Component={AddPartner}
              permission={OrganisationActions.updatePartners} // Assuming a permission exists
            />
          )}
          <OrganisationRoute
            exact={true}
            path={Routes.organisationSettingsPartnersActive}
            organisation={data.organisation}
            Component={ActivePartners}
            permission={OrganisationActions.viewPartnersPage} // Assuming a permission exists
          />
          <OrganisationRoute
            exact={true}
            path={Routes.organisationSettingsPartnersPrevious}
            organisation={data.organisation}
            Component={PreviousPartners}
            permission={OrganisationActions.viewPartnersPage} // Assuming a permission exists
          />
        </>
      )}
      {!isChild && (
        <>
          <OrganisationRoute
            exact={true}
            path={Routes.organisationSettingsConnections}
            organisation={data.organisation}
            Component={ConnectedOrganisations}
            permission={OrganisationActions.updateConnections}
          />
          <OrganisationRoute
            exact={true}
            path={Routes.organisationSettingsInvitations}
            organisation={data.organisation}
            Component={Invitations}
            permission={OrganisationActions.updateInvitations}
          />
          <OrganisationRoute
            exact={true}
            path={Routes.addConnection}
            organisation={data.organisation}
            Component={AddOrganisation}
            permission={OrganisationActions.updateConnections}
          />
          <OrganisationRoute
            exact={true}
            path={Routes.pendingConnections}
            organisation={data.organisation}
            Component={pendingConnections}
            permission={OrganisationActions.updateConnections}
          />
          <OrganisationRoute
            exact={true}
            path={Routes.approvedConnections}
            organisation={data.organisation}
            Component={pendingConnections}
            permission={OrganisationActions.updateConnections}
          />
          <OrganisationRoute
            exact={true}
            path={Routes.activeConnections}
            organisation={data.organisation}
            Component={ActiveConnections}
            permission={OrganisationActions.updateConnections}
          />
          <OrganisationRoute
            exact={true}
            path={Routes.previousConnections}
            organisation={data.organisation}
            Component={PreviousConnections}
            permission={OrganisationActions.updateConnections}
          />
        </>
      )}
    </Layout>
  );
}

function OrganisationRoute({
  Component,
  organisation,
  permission,
  ...props
}: RouteProps & {
  Component: OrganisationComponentProps;
  organisation: Organisation;
  permission?: OrganisationActions;
}) {
  return (
    <Route
      {...props}
      render={(props: OrganisationProfileProps | any) => {
        if (permission && !hasPermission(permission, organisation.permissions)) {
          return (
            <Row gutter={GUTTER_LG}>
              <Col span={6}>
                <SettingsSideNav {...props} organisation={organisation} />
              </Col>
              <Col span={18}>
                <ContainerCard>
                  <UnauthorisedError />
                </ContainerCard>
              </Col>
            </Row>
          );
        }

        return <Component {...props} organisation={organisation} />;
      }}
    />
  );
}
