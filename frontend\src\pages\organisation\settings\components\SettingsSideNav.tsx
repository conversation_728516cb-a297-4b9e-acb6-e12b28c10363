import { Link, Route, useHistory } from 'react-router-dom';
import React from 'react';
import { Space, Typography, message } from 'antd';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';

import { SideNavButton } from '@components/SideNav/SideNavButton';
import { Icons } from '@components/icons/Icon';
import { Routes } from '@src/routes/Routes';
import { OrganisationProfileProps } from '../OrganisationSettings';
import { encodePathParams } from '@utils/encodePathParams';
import { Organisation, OrganisationType, PartnershipRequestStatus } from '@GraphQLTypes';
import { ContainerCard } from '@components/Layout/Container';
import { GUTTER_MD_PX } from '@src/theme';
import { ComingSoon } from '@components/ComingSoon';
import { useMutation } from '@apollo/client';
import { hasPermission, OrganisationActions } from '../../permissions';
import {
  GET_STRIPE_EXPRESS_LINK,
  GetStripeExpressLinkVariables,
  RETRIEVE_CONNECT_ACCOUNT,
  RetrieveConnectAccountVariables,
} from '../queries';
import { useProfile } from '@src/routes/ProfileProvider';

type Props = OrganisationProfileProps & {
  organisation: Organisation;
};

export function SettingsSideNav({ organisation, match: { path, params } }: Props) {
  const { organisationId } = params;
  const { profile } = useProfile();
  const { t } = useTranslation();
  const history = useHistory();
  const currentPath = history.location.pathname.substring(history.location.pathname.lastIndexOf('/') + 1);

  const membership = profile.memberships?.[0];
  const isChild = membership?.partnerOrganisation
    ? membership.partnerOrganisation.status === PartnershipRequestStatus.Approved
    : false;

  const hasConnectedOrganisationPermission = hasPermission(
    OrganisationActions.updateConnections,
    organisation.permissions,
  );
  const hasAddPartnersPermission = hasPermission(OrganisationActions.addPartners, organisation.permissions);

  const [onManagePaymentAccount, { data: stripeExpressLinkURL }] = useMutation<{}, GetStripeExpressLinkVariables>(
    GET_STRIPE_EXPRESS_LINK,
    {
      variables: { organisationId: organisation.id },
      onError(err) {
        message.error(err.message);
        //if message contains 'not completed onboarding' then...
        retrieveConnectAccount();
      },
    },
  );

  const [retrieveConnectAccount, { data: retrieveAccountResponse }] = useMutation<{}, RetrieveConnectAccountVariables>(
    RETRIEVE_CONNECT_ACCOUNT,
    {
      variables: { organisationId: organisation.id },
      onError(err) {
        message.error(err.message);
        //if message contains 'not completed onboarding' then...
      },
    },
  );

  if (stripeExpressLinkURL) {
    window.open(
      // @ts-ignore
      stripeExpressLinkURL.getStripeExpressLink,
      '_blank',
    );
  }

  if (retrieveAccountResponse) {
    window.open(
      // @ts-ignore
      retrieveAccountResponse?.retrieveConnectAccount,
      '_blank',
    );
  }

  // const { error, loading, data } = useQuery<GetStripeExpressLinkVariables>(GET_STRIPE_EXPRESS_LINK, {
  //   variables: { organisationId },
  // });

  const SubMenuComponent = () => {
    return (
      <Route
        path={Routes.organisationSettingsConnections}
        render={() => (
          <>
            <SubMenuItem>
              <div style={{ cursor: 'pointer' }}>
                <SideNavButton
                  key={'nav-add-connection'}
                  active={currentPath === 'add'}
                  icon={currentPath === 'add' ? Icons.caret : Icons.empty}
                  onClick={() => {
                    history.push(encodePathParams(Routes.addConnection, { organisationId }));
                  }}
                  style={{
                    color: window.innerWidth > 568 && currentPath === 'add' ? 'white' : 'var(--color-darkerblue)',
                  }}
                  data-cy="connectedOrg-Add-Org"
                >
                  {t('Add Organisation')}
                </SideNavButton>
              </div>
            </SubMenuItem>
            <SubMenuItem>
              <div style={{ cursor: 'pointer' }}>
                <SideNavButton
                  key={'nav-pending-connections'}
                  active={currentPath === 'pending'}
                  icon={currentPath === 'pending' ? Icons.caret : Icons.empty}
                  onClick={() => {
                    history.push(encodePathParams(Routes.pendingConnections, { organisationId }));
                  }}
                  style={{
                    color: window.innerWidth > 568 && currentPath === 'pending' ? 'white' : 'var(--color-darkerblue)',
                  }}
                  data-cy="connectedOrg-Pending-request"
                >
                  {t('Pending Requests')}
                </SideNavButton>
              </div>
            </SubMenuItem>
            <SubMenuItem>
              <div style={{ cursor: 'pointer' }}>
                <SideNavButton
                  key={'nav-active-connections'}
                  active={currentPath === 'active'}
                  icon={currentPath === 'active' ? Icons.caret : Icons.empty}
                  onClick={() => {
                    history.push(encodePathParams(Routes.activeConnections, { organisationId }));
                  }}
                  style={{
                    color: window.innerWidth > 568 && currentPath === 'active' ? 'white' : 'var(--color-darkerblue)',
                  }}
                >
                  {t('Active Connections')}
                </SideNavButton>
              </div>
            </SubMenuItem>
            <SubMenuItem>
              <div style={{ cursor: 'pointer' }}>
                <SideNavButton
                  key={'nav-previous-connections'}
                  active={currentPath === 'previous'}
                  icon={currentPath === 'previous' ? Icons.caret : Icons.empty}
                  onClick={() => {
                    history.push(encodePathParams(Routes.previousConnections, { organisationId }));
                  }}
                  style={{
                    color: window.innerWidth > 568 && currentPath === 'previous' ? 'white' : 'var(--color-darkerblue)',
                  }}
                >
                  {t('Previous Connections')}
                </SideNavButton>
              </div>
            </SubMenuItem>
            {organisation.stripeConnectAccount?.length &&
              (organisation.isConnectOnboarded ? (
                <SubMenuItem>
                  <div style={{ cursor: 'pointer' }}>
                    <SideNavButton
                      key={'nav-stripe-payments-dashboard'}
                      active={false}
                      icon={Icons.empty}
                      onClick={() => onManagePaymentAccount()}
                      style={{
                        color: 'var(--color-darkerblue)',
                      }}
                    >
                      {t('Stripe Payments Dashboard')}
                    </SideNavButton>
                  </div>
                </SubMenuItem>
              ) : (
                <SubMenuItem>
                  <div style={{ cursor: 'pointer' }}>
                    <SideNavButton
                      key={'nav-setup-payment-account'}
                      active={false}
                      icon={Icons.empty}
                      onClick={() => retrieveConnectAccount()}
                      style={{
                        color: 'var(--color-darkerblue)',
                      }}
                    >
                      {t('Setup Payment Account')}
                    </SideNavButton>
                  </div>
                </SubMenuItem>
              ))}
          </>
        )}
      />
    );
  };

  const PartnersSubMenuComponent = () => {
    // Define partner sub-routes - ensure these are added to Routes.tsx
    const addPartnerPath = encodePathParams(Routes.addPartners, { organisationId });
    const activePartnersPath = encodePathParams(Routes.organisationSettingsPartnersActive, { organisationId });
    const previousPartnersPath = encodePathParams(Routes.organisationSettingsPartnersPrevious, { organisationId });

    const isAddPartnerActive = history.location.pathname === addPartnerPath;
    const isActivePartnersActive = history.location.pathname === activePartnersPath;
    const isPreviousPartnersActive = history.location.pathname === previousPartnersPath;

    return (
      <Route
        path={Routes.organisationSettingsPartners} // Match the base partners path
        render={() => (
          <>
            {hasAddPartnersPermission && (
              <SubMenuItem>
                <div style={{ cursor: 'pointer' }}>
                  <SideNavButton
                    key={'nav-add-partner'}
                    active={isAddPartnerActive}
                    icon={isAddPartnerActive ? Icons.caret : Icons.empty}
                    onClick={() => history.push(addPartnerPath)}
                    style={{
                      color: window.innerWidth > 568 && isAddPartnerActive ? 'white' : 'var(--color-darkerblue)',
                    }}
                    data-cy="partners-Add-Partner"
                  >
                    {t('Add Partner')}
                  </SideNavButton>
                </div>
              </SubMenuItem>
            )}

            <SubMenuItem>
              <div style={{ cursor: 'pointer' }}>
                <SideNavButton
                  key={'nav-active-partners'}
                  active={isActivePartnersActive}
                  icon={isActivePartnersActive ? Icons.caret : Icons.empty}
                  onClick={() => history.push(activePartnersPath)}
                  style={{
                    color: window.innerWidth > 568 && isActivePartnersActive ? 'white' : 'var(--color-darkerblue)',
                  }}
                  data-cy="partners-Active-Partners"
                >
                  {t('Active Partners')}
                </SideNavButton>
              </div>
            </SubMenuItem>
            <SubMenuItem>
              <div style={{ cursor: 'pointer' }}>
                <SideNavButton
                  key={'nav-previous-partners'}
                  active={isPreviousPartnersActive}
                  icon={isPreviousPartnersActive ? Icons.caret : Icons.empty}
                  onClick={() => history.push(previousPartnersPath)}
                  style={{
                    color: window.innerWidth > 568 && isPreviousPartnersActive ? 'white' : 'var(--color-darkerblue)',
                  }}
                  data-cy="partners-Previous-Partners"
                >
                  {t('Previous Partners')}
                </SideNavButton>
              </div>
            </SubMenuItem>
          </>
        )}
      />
    );
  };

  const isPartnerMenuActive =
    organisation.type === OrganisationType.Destination || organisation.type === OrganisationType.PrivateSector;

  const isPartnerSubMenuActive = organisation.type === OrganisationType.Destination && organisation.isDMOSubscription;

  return (
    <ContainerCard
      style={{
        padding: window.innerWidth < 568 ? 0 : GUTTER_MD_PX,
        paddingTop: window.innerWidth < 568 ? '5px' : GUTTER_MD_PX,
        minHeight: '40px',
        height: 'max-content',
        overflowX: window.innerWidth < 568 ? 'auto' : 'hidden',
        minWidth: window.innerWidth < 568 ? '100%' : window.innerWidth > 1238 ? '270px' : '175px',
        width: window.innerWidth < 568 ? '100%' : window.innerWidth > 1238 ? '270px' : 'unset',
      }}
    >
      <Header>
        <Link to={encodePathParams(Routes.organisationProfile, { vanityId: organisation.vanityId })}>
          <Typography.Text strong type="secondary">
            {organisation.name}
          </Typography.Text>
        </Link>
        <Typography.Title level={4}>{t('Manage Organisation')}</Typography.Title>
      </Header>
      <Space
        direction={window.innerWidth < 568 ? 'horizontal' : 'vertical'}
        size={4}
        style={{ width: '100%', display: 'block' }}
      >
        {!isChild && (
          <Link to={encodePathParams(Routes.organisationSettingsInfo, { organisationId })}>
            <SideNavButton
              active={path === Routes.organisationSettings || path === Routes.organisationSettingsInfo}
              icon={Icons.info}
            >
              {t('Organisation Information')}
            </SideNavButton>
          </Link>
        )}
        {!isChild && (
          <Link to={encodePathParams(Routes.organisationSettingsEmployees, { organisationId })}>
            <SideNavButton
              active={path === Routes.organisationSettingsEmployees}
              icon={Icons.users}
              data-cy={'org-employee-tab'}
            >
              {t('Employees')}
            </SideNavButton>
          </Link>
        )}
        {(organisation.type === OrganisationType.Association || organisation.type === OrganisationType.Consortia) &&
          !isChild && (
            <Link to={encodePathParams(Routes.organisationSettingsMembers, { organisationId })}>
              <SideNavButton
                active={path === Routes.organisationSettingsMembers}
                icon={Icons.users}
                data-cy={'Members-org'}
              >
                {t('Members')}
              </SideNavButton>
            </Link>
          )}

        {!isChild && (
          <Link to={encodePathParams(Routes.organisationSettingsUserRoles, { organisationId })}>
            <SideNavButton active={path === Routes.organisationSettingsUserRoles} icon={Icons.security}>
              {t('User Roles')}
            </SideNavButton>
          </Link>
        )}
        {!isChild && (
          <Link to={encodePathParams(Routes.organisationSettingsPrivacy, { organisationId })}>
            <SideNavButton
              active={path === Routes.organisationSettingsPrivacy}
              icon={Icons.page}
              data-cy={'org-policy'}
            >
              {t('Organisation Privacy')}
            </SideNavButton>
          </Link>
        )}
        {isPartnerMenuActive && (
          <Link to={encodePathParams(Routes.organisationSettingsPartners, { organisationId })}>
            <SideNavButton
              active={path === Routes.organisationSettingsPartners}
              icon={Icons.partners}
              data-cy="org-partners"
            >
              {t('Partners')}
            </SideNavButton>
          </Link>
        )}
        {isPartnerSubMenuActive && <PartnersSubMenuComponent />}
        {!isChild && (
          <Link to={encodePathParams(Routes.organisationSettingsConnections, { organisationId })}>
            <SideNavButton
              active={path === Routes.organisationSettingsConnections}
              icon={Icons.organisation}
              data-cy="connected-organisations"
            >
              {t('Connected Organisations')}
            </SideNavButton>
          </Link>
        )}
        {hasConnectedOrganisationPermission && <SubMenuComponent />}
        {/* <ComingSoon>
            <SideNavButton active={path === Routes.organisationSettingsInvitations} icon={Icons.calendar}>
              {t('Invitations')}
            </SideNavButton>
          </ComingSoon> */}
      </Space>
    </ContainerCard>
  );
}

const Header = styled.div`
  padding: 8px;

  h4 {
    margin-top: 10px !important;
  }
`;

const SubMenuItem = styled.div`
  margin-left: ${GUTTER_MD_PX}px;
`;
