import { gql } from '@apollo/client';

import { Organisation, OrganisationsResult } from '@GraphQLTypes';

export type GetPartnershipsData = {
  organisation: Pick<Organisation, 'partnerships' | 'receivedPartnershipRequests' | 'sentPartnershipRequests'>;
};

export type GetPartnershipsVariables = {
  organisationId: string;
  isActive?: boolean;
};

export const GET_PARTNERSHIPS = gql`
  fragment OrganisationInfo on Organisation {
    id
    name
    image
    type
    vanityId
  }

  query ($organisationId: String!, $isActive: Boolean) {
    organisation(id: $organisationId) {
      id
      partnerships(isActive: $isActive) {
        id
        createdAt
        organisation {
          ...OrganisationInfo
        }
        partnershipRequestData {
          id
          status
          receiverOrganisation {
            ...OrganisationInfo
          }
          subscriptionData
        }
      }
      receivedPartnershipRequests {
        id
        status
        createdAt
        subscriptionData
        senderOrganisation {
          ...OrganisationInfo
        }
        receiverOrganisation {
          ...OrganisationInfo
        }
      }
      sentPartnershipRequests {
        id
        status
        createdAt
        subscriptionData
        senderOrganisation {
          ...OrganisationInfo
        }
        receiverOrganisation {
          ...OrganisationInfo
        }
      }
    }
  }
`;

export type SearchOrganisationsData = {
  organisations: OrganisationsResult;
};

export type SearchOrganisationsVariables = {
  searchText?: string;
};

export const SEARCH_ORGANISATIONS = gql`
  query SearchOrganisations($searchText: String!) {
    organisations(first: 20, sortBy: "name", sortOrder: Descending, filter: { searchText: $searchText }) {
      totalCount
      records {
        id
        name
        image
        location
        type
        hasClubHabloSubscription
        isPaid
      }
    }
  }
`;

export type CreatePartnershipRequestVariables = {
  partnershipRequestData: {
    senderOrganisationId: string;
    receiverOrganisationId: string;
    isSubscription: boolean;
    price?: string;
    currency?: string;
    duration?: string;
  };
};

export type RetrieveConnectAccountVariables = {
  organisationId: string;
};

export type CreateConnectAccountRequestVariables = {
  organisationId: string;
  email: string;
};

export const CREATE_PARTNERSHIP_REQUEST = gql`
  mutation CreatePartnershipRequest($partnershipRequestData: CreatePartnershipRequestDto!) {
    createPartnershipRequest(partnershipRequestData: $partnershipRequestData)
  }
`;

export const RETRIEVE_CONNECT_ACCOUNT = gql`
  mutation RetreiveConnectAccount($organisationId: String!) {
    retrieveConnectAccount(organisationId: $organisationId)
  }
`;

export const CREATE_CONNECT_ACCOUNT = gql`
  mutation CreateConnectAccount($organisationId: String!, $email: String!) {
    createConnectAccount(organisationId: $organisationId, email: $email)
  }
`;

export type RemovePartnershipVariables = {
  partnershipId: string;
};

export const REMOVE_PARTNERSHIP = gql`
  mutation RemovePartnership($partnershipId: String!) {
    removePartnership(partnershipId: $partnershipId)
  }
`;

export type ApprovePartnershipRequestVariables = {
  partnershipRequestId: string;
};

export type InitiatePaymentRequestVariable = {
  partnershipRequestId: string;
};

export const INITIATE_PAYMENT_REQUEST = gql`
  mutation InitiatePayment($partnershipRequestId: String!) {
    initiatePayment(partnershipRequestId: $partnershipRequestId)
  }
`;

export type RetryPaymentRequestVariable = {
  partnershipRequestId: string;
};

export const REINIT_PAYMENT_REQUEST = gql`
  mutation RetryPayment($partnershipRequestId: String!) {
    retryPayment(partnershipRequestId: $partnershipRequestId)
  }
`;

export const APPROVE_PARTNERSHIP_REQUEST = gql`
  mutation ApprovePartnershipRequest($partnershipRequestId: String!) {
    approvePartnershipRequest(partnershipRequestId: $partnershipRequestId)
  }
`;

export type DeclinePartnershipRequestVariables = {
  partnershipRequestId: string;
};

export const DECLINE_PARTNERSHIP_REQUEST = gql`
  mutation DeclinePartnershipRequest($partnershipRequestId: String!) {
    declinePartnershipRequest(partnershipRequestId: $partnershipRequestId)
  }
`;

export type RemovePartnershipRequestVariables = {
  partnershipRequestId: string;
};

export const REMOVE_PARTNERSHIP_REQUEST = gql`
  mutation RemovePartnershipRequest($partnershipRequestId: String!) {
    removePartnershipRequest(partnershipRequestId: $partnershipRequestId)
  }
`;

export type GetCustomerPortalVariables = {
  partnershipId: string;
  organisationId: string;
};

export const GET_CUSTOMER_PORTAL = gql`
  mutation GetCustomerPortal($partnershipId: String!, $organisationId: String!) {
    getCustomerPortal(partnershipId: $partnershipId, organisationId: $organisationId)
  }
`;

export type AddPreApprovedDomainsVariables = {
  data: {
    organisationId: string;
    domain: string;
  };
};

export const ADD_PREAPPROVED_DOMAINS = gql`
  mutation AddPreapprovedDomains($data: AddPreApprovedDomainInput!) {
    addPreApprovedDomains(data: $data)
  }
`;

export const REMOVE_PREAPPROVED_DOMAINS = gql`
  mutation RemovePreapprovedDomains($data: AddPreApprovedDomainInput!) {
    removePreApprovedDomains(data: $data)
  }
`;

export type RemoveSubscription = {
  partnershipRequestId: string;
};

export const REMOVE_SUBSCRIPTION = gql`
  mutation RemoveSubscription($partnershipRequestId: String!) {
    removeSubscription(partnershipRequestId: $partnershipRequestId)
  }
`;
