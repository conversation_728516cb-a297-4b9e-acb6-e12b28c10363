import React, { useCallback, useEffect, useState } from 'react';
import { Button, Col, Divider, Form, Input, Row, Typography, message } from 'antd';
import { useTranslation } from 'react-i18next';

import { GUTTER_LG, GUTTER_MD_PX, GUTTER_SM_PX, GUTTER_LG_PX } from '@src/theme';
import { OrganisationProfileProps } from '../../OrganisationSettings';
import { SettingsSideNav } from '../../components/SettingsSideNav';
import { Membership, MembershipStatus, Organisation } from '@GraphQLTypes';
import { ContainerCard } from '@components/Layout/Container';
import { DataFetchingError } from '@components/Results';
import { MembersPending } from './MembersPending';
import { MembersActive } from './MembersActive';
import { Loader } from '@components/Loader';
import { useMutation, useQuery } from '@apollo/client';
import {
  GET_ORGANISATION,
  GET_ORGANISATION_MEMBERS,
  GetOrganisationMembersData,
  GetOrganisationMembersVariables,
} from '../../queries';
import styled from 'styled-components';
import { CloseOutlined } from '@ant-design/icons';
import {
  ADD_PREAPPROVED_DOMAINS,
  REMOVE_PREAPPROVED_DOMAINS,
  AddPreApprovedDomainsVariables,
} from '../connectedOrganisations/queries';
import { getEnv } from '@src/utils/getEnv';
import { BORDER_RADIUS } from '@theme';
import { debounce } from '@src/utils/debounce';
import { DOMAIN_ORIGIN } from '@src/consts';

type Props = OrganisationProfileProps & {
  organisation: Organisation;
};

interface FormValues {
  domain: string;
}

export type EmployeeProps = {
  membership: Membership;
  organisation: Organisation;
  measure?(): void;
};

const validUrl = new RegExp(/^((?:(?:(?:\w[\.\-\+]?)*)\w)+)((?:(?:(?:\w[\.\-\+]?){0,62})\w)+)\.(\w{2,6})$/);
function CheckIsValidDomain(domain: string) {
  return validUrl.test(domain);
}

export function Members({ organisation, ...routerProps }: Props) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [domains, setDomains] = useState<string>('');
  const [copyButtonText, setCopyButtonText] = useState<string>(t('Copy'));
  const [domainList, setDomainList] = useState<Array<string>>(organisation.preApprovedDomains ?? []);

  const [addApprovedDomains, { loading, error: addDomainsError }] = useMutation<{}, AddPreApprovedDomainsVariables>(
    ADD_PREAPPROVED_DOMAINS,
    {
      refetchQueries: [{ query: GET_ORGANISATION, variables: { organisationId: organisation.id } }],
      awaitRefetchQueries: true,
      onCompleted: () => {
        setDomainList(organisation.preApprovedDomains as Array<string>);
      },
    },
  );

  const [removePreApprovedDomain] = useMutation<{}, AddPreApprovedDomainsVariables>(REMOVE_PREAPPROVED_DOMAINS, {
    refetchQueries: [{ query: GET_ORGANISATION, variables: { organisationId: organisation.id } }],
    awaitRefetchQueries: true,
    onCompleted: () => {
      setDomainList(organisation.preApprovedDomains as Array<string>);
    },
  });

  const onAddDomain = async (values: FormValues) => {
    if (values.domain && CheckIsValidDomain(values.domain)) {
      const newDomain = values.domain;
      setDomains(newDomain);

      form.resetFields();
      const data = {
        organisationId: organisation?.id,
        domain: domains,
      };

      await addApprovedDomains({ variables: { data } });
      setDomains('');
      form.resetFields();
    } else {
      message.error('Please enter a valid domain name.');
    }
  };

  const removeDomain = async (domain: string) => {
    const data = {
      organisationId: organisation?.id,
      domain: domain,
    };
    await removePreApprovedDomain({
      variables: { data },
    });
  };

  const signUpLink = `${DOMAIN_ORIGIN}/signup?memref=${organisation?.id}`;

  return (
    <Row gutter={GUTTER_LG}>
      <Col span={window.innerWidth < 568 ? 24 : 6}>
        <SettingsSideNav {...routerProps} organisation={organisation} />
      </Col>
      <Col span={window.innerWidth < 568 ? 24 : 18}>
        <ContainerCard>
          <Title>{t('Manage Members')}</Title>
          <SubTitle>{t('Invite Members')}</SubTitle>
          <Text>
            {t(
              'Share this link with members of {{organisationName}} and they`ll automatically join your organisation on Hablo (pending your approval, or pending their email verification if employees sign up using a Pre-Approved Domain below).',
              {
                organisationName: organisation.name,
              },
            )}
          </Text>
          <Row style={{ flex: 1, marginBottom: GUTTER_LG_PX, marginTop: GUTTER_MD_PX }}>
            <Col span={12}>
              <Input type="text" value={signUpLink} readOnly />
            </Col>
            <Col span={window.innerWidth < 568 ? 6 : 3}>
              <Button
                type="primary"
                style={{ width: '100%', marginLeft: GUTTER_MD_PX }}
                onClick={() => {
                  navigator.clipboard
                    .writeText(signUpLink)
                    .then(() => {
                      setCopyButtonText(t('Copied!'));
                    })
                    .catch(() => {
                      alert('Failed to copy');
                    });
                }}
              >
                {copyButtonText}
              </Button>
            </Col>
          </Row>
          <Divider />
          <SubTitle style={{ marginTop: GUTTER_MD_PX }}>{t('Pre-Approved Domains')}</SubTitle>
          <Text>
            {t(
              "Members that sign up to Hablo with email addresses using the following domain(s) will be automatically approved to join {{organisationName}}, once they've verified their email address:",
              {
                organisationName: organisation.name,
              },
            )}
          </Text>

          <Form form={form} onFinish={onAddDomain}>
            <Row style={{ flex: 1, marginBottom: GUTTER_MD_PX, marginTop: GUTTER_MD_PX, justifyContent: 'flex-start' }}>
              <Col span={12}>
                <Form.Item name="domain" status={addDomainsError ? 'error' : undefined} help={addDomainsError?.message}>
                  <Input
                    type="text"
                    placeholder={t('Type an email domain, e.g. mycompany.com')}
                    onChange={(e) => setDomains(e.target.value)}
                    status={addDomainsError ? 'error' : undefined}
                  />
                </Form.Item>
              </Col>
              <Col span={window.innerWidth < 568 ? 6 : 3}>
                <Button
                  type="primary"
                  htmlType="submit"
                  style={{ width: '100%', marginLeft: GUTTER_MD_PX }}
                  loading={loading}
                >
                  {t('Add')}
                </Button>
              </Col>
            </Row>
          </Form>

          <Row
            style={{
              flex: 1,
              marginBottom: GUTTER_SM_PX,
              marginTop: GUTTER_SM_PX,
              justifyContent: 'flex-start',
              display: 'flex',
            }}
          >
            {domainList &&
              domainList.length > 0 &&
              domainList.map((domain, key) => {
                return (
                  <DomainLabel key={key}>
                    <span>{domain}</span>
                    <Button
                      style={{ border: 'none', background: 'transparent', padding: '0 0 0 20px' }}
                      onClick={() => removeDomain(domain)}
                      className="close-button"
                    >
                      <CloseOutlined />
                    </Button>
                  </DomainLabel>
                );
              })}
          </Row>
          <Divider />
          <RenderMembers organisation={organisation} />
        </ContainerCard>
      </Col>
    </Row>
  );
}

function RenderMembers({ organisation }: { organisation: Organisation }) {
  const [searchText, setSearchText] = useState<string>('');

  const updateSearch = useCallback<typeof setSearchText>(
    debounce((value) => setSearchText(value)),
    [searchText],
  );

  const { error, loading, data } = useQuery<GetOrganisationMembersData, GetOrganisationMembersVariables>(
    GET_ORGANISATION_MEMBERS,
    {
      variables: {
        organisationId: organisation.id,
        membershipsFilter: { isPrimary: false },
      },
    },
  );

  const memberships: { [key in MembershipStatus]: Membership[] } = {
    [MembershipStatus.Pending]: [],
    [MembershipStatus.Active]: [],
    [MembershipStatus.Rejected]: [],
    [MembershipStatus.Inactive]: [],
  };

  const [membershipsArray, setMembershipsArray] = useState<{ [key in MembershipStatus]: Membership[] }>({
    [MembershipStatus.Pending]: [],
    [MembershipStatus.Active]: [],
    [MembershipStatus.Rejected]: [],
    [MembershipStatus.Inactive]: [],
  });

  const getMemberships = () => {
    const res: any = {
      [MembershipStatus.Pending]: [],
      [MembershipStatus.Active]: [],
      [MembershipStatus.Rejected]: [],
      [MembershipStatus.Inactive]: [],
    };
    data?.organisation.memberships.records.forEach((membership) => {
      if (
        membership.status &&
        (membership.profile.name.toLowerCase().includes(searchText.toLowerCase()) ||
          membership?.position?.toLowerCase().includes(searchText.toLowerCase()))
      ) {
        res[membership.status].push(membership);
      }
    });
    setMembershipsArray(res);
  };

  useEffect(() => {
    getMemberships();
  }, [data, searchText]);

  if (error) {
    return <DataFetchingError />;
  }

  if (!data || loading) {
    return <Loader useGIF={true} allowLoadingText={true} />;
  }

  data.organisation.memberships.records.forEach(
    (membership) => membership.status && memberships[membership.status].push(membership),
  );
  return (
    <>
      <MembersPending
        memberships={membershipsArray[MembershipStatus.Pending]}
        organisation={organisation}
        updateSearch={updateSearch}
      />
      <Divider />
      <MembersActive memberships={membershipsArray[MembershipStatus.Active]} organisation={organisation} />
    </>
  );
}

const Title = styled(Typography.Title).attrs({ level: 4 })`
  font-size: 18px !important;
  font-weight: 600;
  line-height: 30px !important;
`;

const SubTitle = styled(Typography.Title)`
  font-size: 16px !important;
  font-weight: 400 !important;
  line-height: 20px !important;
  color: #000000 !important;
`;

const Text = styled(Typography.Text).attrs({ type: 'secondary' })`
  font-size: 14px !important;
  font-weight: 400 !important;
  line-height: 20px !important;
`;

const DomainLabel = styled.div`
  border: 1px solid #eeeeee;
  border-radius: ${BORDER_RADIUS};
  background: #f2f2f2;
  padding: 0 15px;
  margin: 0 20px 10px 0;
  font-size: 14px;
  font-weight: 500;
`;
