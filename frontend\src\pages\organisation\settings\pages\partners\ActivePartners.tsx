import React, { useState } from 'react';
import { Button, Col, Divider, Empty, message, Modal, Row, Select, Typography } from 'antd';

import { GUTTER_LG, GUTTER_LG_PX, GUTTER_SM_PX } from '@src/theme';
import { OrganisationProfileProps } from '../../OrganisationSettings';
import { SettingsSideNav } from '../../components/SettingsSideNav';
import { Organisation, PartnerOrganisation, PartnershipRequestStatus } from '@GraphQLTypes';
import { ContainerCard, ContainerCentered } from '@components/Layout/Container';
import { useTranslation } from 'react-i18next';
import PartnerItem from './PartnerItem';
import { DropdownMenu } from '@src/components/DropdownMenu';
import { ButtonMore } from '@src/components/Buttons/ButtonMore';
import { Icons } from '@src/components/icons/Icon';
import styled from 'styled-components';
import { useMutation, useQuery } from '@apollo/client';
import {
  DELETE_PARTNER_ORGANISATION,
  GET_PARTNER_ORG_LIST,
  PartnerOrganisationsResponse,
  UPDATE_PARTNER_ORGANISATION,
} from './queries';
import { hasPermission, OrganisationActions } from '@src/pages/organisation/permissions';

type Props = OrganisationProfileProps & {
  organisation: Organisation;
};

export function ActivePartners({ organisation, ...routerProps }: Props) {
  const { t } = useTranslation();
  const brandName = organisation.name;
  const [openPostModalForm, setOpenPostModalForm] = useState(false);
  const [selectedPartnerDetails, setSelectedPartnerDetails] = useState<PartnerOrganisation | null>(null);
  const [updatedPostLimit, setUpdatedPostLimit] = useState<number | null>();

  const hasUpdatePartnerPermission = hasPermission(
    OrganisationActions.updatePartnerPostLimit,
    organisation.permissions,
  );
  const hasRemovePartnerPermission = hasPermission(OrganisationActions.removePartners, organisation.permissions);

  const { data, refetch } = useQuery<PartnerOrganisationsResponse>(GET_PARTNER_ORG_LIST, {
    variables: {
      filter: {
        status: [PartnershipRequestStatus.Approved, PartnershipRequestStatus.Pending],
        organisationId: organisation.id,
      },
    },
  });

  const [updatePartnerOrganisation, { loading }] = useMutation<{}, any>(UPDATE_PARTNER_ORGANISATION);
  const [deletePartnerOrganisation] = useMutation<{}, { parentOrgId: string; childOrgId: string }>(
    DELETE_PARTNER_ORGANISATION,
  );

  const handleDisconnect = (childOrgId: string) => {
    Modal.confirm({
      centered: true,
      content: t('Are you sure you want to disconnect this partner?'),
      cancelText: t('Cancel'),
      okText: t('Ok'),
      onOk: async () => {
        try {
          const data = {
            parentOrgId: organisation.id,
            childOrgId: childOrgId,
            status: PartnershipRequestStatus.Disconnected,
          };
          const response = await updatePartnerOrganisation({
            variables: { data },
          });
          if (response.data) {
            message.success(t('Partner disconnected successfully'));
            await refetch();
            return true;
          }
          return false;
        } catch (error) {
          console.log(error);
        }
      },
    });
  };

  const handleCancelRequest = (childOrgId: string) => {
    Modal.confirm({
      centered: true,
      content: t('Are you sure you want to cancel this request?'),
      cancelText: t('Cancel'),
      okText: t('Ok'),
      onOk: async () => {
        const data = {
          parentOrgId: organisation.id,
          childOrgId: childOrgId,
        };
        await deletePartnerOrganisation({ variables: data });
        await refetch();
      },
    });
  };

  const handleUpdatePostLimit = async () => {
    try {
      const data = {
        parentOrgId: organisation.id,
        childOrgId: selectedPartnerDetails?.childOrgId,
        postsLimit: updatedPostLimit,
      };
      await updatePartnerOrganisation({ variables: { data } });
      setUpdatedPostLimit(selectedPartnerDetails?.postsLimit);
      setSelectedPartnerDetails(null);
      setOpenPostModalForm(false);
      await refetch();
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <>
      <Row gutter={GUTTER_LG}>
        <Col xs={24} sm={6}>
          <SettingsSideNav {...routerProps} organisation={organisation} />
        </Col>
        <Col xs={24} sm={18}>
          <ContainerCard>
            <Typography.Title level={4} style={{ fontSize: 18 }}>
              {t('Current Partners')}
            </Typography.Title>
            <Typography.Paragraph style={{ color: 'var(--color-gray2)', fontSize: 14 }}>
              {t('Below are the currently active & pending Partners of {{brandName}} on Hablo.', {
                brandName: brandName,
              })}
            </Typography.Paragraph>

            {data?.partnerOrganisations && data?.partnerOrganisations.length > 0 ? (
              data?.partnerOrganisations?.map((partner: PartnerOrganisation, index) => {
                return (
                  <div key={index}>
                    <PartnerItem linkToProfile partner={partner} vanityId={partner.childOrganisation.vanityId}>
                      {hasUpdatePartnerPermission && (
                        <DropdownMenu button={<ButtonMore />}>
                          <DropdownMenu.Item
                            icon={Icons.edit}
                            onClick={() => {
                              setSelectedPartnerDetails(partner);
                              setUpdatedPostLimit(partner.postsLimit);
                              setOpenPostModalForm(true);
                            }}
                          >
                            <InnerText>{t('Edit Number of Posts')}</InnerText>
                          </DropdownMenu.Item>
                          {partner.status === PartnershipRequestStatus.Approved ? (
                            <DropdownMenu.Item
                              icon={Icons.disable}
                              onClick={() => handleDisconnect(partner.childOrgId)}
                            >
                              <InnerText>{t('Disconnect Partner')}</InnerText>
                            </DropdownMenu.Item>
                          ) : null}
                        </DropdownMenu>
                      )}

                      {partner.status === PartnershipRequestStatus.Approved ? (
                        <Button type="ghost" disabled style={{ color: 'var(--color-primary)', fontWeight: 600 }}>
                          {t('Connected')}
                        </Button>
                      ) : null}

                      {partner.status === PartnershipRequestStatus.Pending && hasRemovePartnerPermission && (
                        <Button
                          type="ghost"
                          data-cy="cancel-request"
                          style={{ color: 'var(--color-primary)', fontWeight: 600 }}
                          onClick={() => handleCancelRequest(partner.childOrgId)}
                        >
                          {t('Cancel Request')}
                        </Button>
                      )}
                    </PartnerItem>
                    {index !== data?.partnerOrganisations.length - 1 && <Divider style={{ margin: '10px 0 10px 0' }} />}
                  </div>
                );
              })
            ) : (
              <ContainerCentered style={{ marginTop: 20 }}>
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={t('No active partners')} />
              </ContainerCentered>
            )}
          </ContainerCard>
        </Col>
      </Row>

      <Modal
        footer={null}
        centered
        title={t('Update Organisation Post Limit')}
        open={openPostModalForm}
        onCancel={() => {
          setOpenPostModalForm(false);
          setUpdatedPostLimit(null);
          setSelectedPartnerDetails(null);
        }}
      >
        <Label htmlFor="Number of Posts Per Month">{t('Number of Posts Per Month')}</Label>
        <Select
          value={updatedPostLimit}
          style={{ width: '100px' }}
          onChange={(value: number) => setUpdatedPostLimit(value)}
        >
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((number) => (
            <Select.Option key={number} value={number}>
              {number}
            </Select.Option>
          ))}
        </Select>

        <Button
          type="primary"
          data-cy="send-request"
          style={{
            marginTop: GUTTER_LG_PX,
            display: 'block',
          }}
          onClick={handleUpdatePostLimit}
          loading={loading}
        >
          {t('Update')}
        </Button>
      </Modal>
    </>
  );
}

const InnerText = styled.span`
  font-size: 14px;
  font-weight: 600;
  line-height: 18px;
  color: #89a7b8;
`;

const Label = styled.label`
  font-size: 12px;
  font-weight: 800;
  width: 100%;
  margin-bottom: ${GUTTER_SM_PX};
  line-height: 15px;
  display: block;
`;
