import { useCallback, useEffect, useState } from 'react';
import { AutoComplete, Button, Col, List, message, Row, Select, Tooltip, Typography } from 'antd';

import { GUTTER_LG, GUTTER_LG_PX, GUTTER_MD, GUTTER_MD_PX, GUTTER_SM_PX } from '@src/theme';
import { OrganisationProfileProps } from '../../OrganisationSettings';
import { SettingsSideNav } from '../../components/SettingsSideNav';
import { Organisation, OrganisationType } from '@GraphQLTypes';
import { ContainerCard } from '@components/Layout/Container';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { debounce } from '@src/utils/debounce';
import { useMutation, useQuery } from '@apollo/client';
import {
  SEARCH_ORGANISATIONS,
  SearchOrganisationsData,
  SearchOrganisationsVariables,
} from '../connectedOrganisations/queries';
import { Avatar } from '@src/components/Image/Image';
import { getFormattedLocation } from '@src/components/LocationSearch/LocationSearch';
import {
  CREATE_PARTNER_ORGANISATION,
  CreatePartnerOrganisationResponse,
  CreatePartnerOrganisationVariables,
  GET_PARTNER_ORG_LIST,
  PartnerOrganisationsResponse,
} from './queries';
import { useHistory } from 'react-router-dom';

type Props = OrganisationProfileProps & {
  organisation: Organisation;
};

export function AddPartner({ organisation, ...routerProps }: Props) {
  const { t } = useTranslation();
  const history = useHistory();
  const brandName = organisation.name || 'this destination';

  const [searchValue, setSearchValue] = useState<string>();
  const [searchText, setSearchText] = useState<string>();
  const [receiverOrganisationId, setReceiverOrganisationId] = useState<string | undefined>('');
  const partnershipIds = organisation.partnerships?.map(({ id }) => id) || [];

  const [postLimit, setPostLimit] = useState<number>(2);

  const updateSearchText = useCallback<typeof setSearchText>(debounce(setSearchText, 200), []);

  const { data } = useQuery<SearchOrganisationsData, SearchOrganisationsVariables>(SEARCH_ORGANISATIONS, {
    variables: { searchText },
    skip: !searchText,
  });

  const { data: allPartners } = useQuery<PartnerOrganisationsResponse>(GET_PARTNER_ORG_LIST);

  const [createPartnerOrganisation, { loading }] = useMutation<
    CreatePartnerOrganisationResponse,
    CreatePartnerOrganisationVariables
  >(CREATE_PARTNER_ORGANISATION);

  useEffect(() => {
    if (searchValue?.length === 0) {
      setReceiverOrganisationId('');
    }
  }, [searchValue]);

  const results = data?.organisations.records.filter(
    ({ id }) => id !== organisation.id && !partnershipIds.includes(id),
  );

  const onSubmit = async () => {
    if (!receiverOrganisationId) {
      message.error('Please select a organization');
      return;
    }
    try {
      const data = {
        parentOrgId: organisation.id,
        childOrgId: receiverOrganisationId,
        postsLimit: postLimit,
      };
      await createPartnerOrganisation({ variables: { data } });
      message.success(t('Your request has been sent successfully'));
      history.push(`/organisation/${organisation.id}/partners/active`);
    } catch (error: string | any) {
      message.error(error.message);
    }
  };

  return (
    <Row gutter={GUTTER_LG}>
      <Col span={window.innerWidth < 568 ? 24 : 6}>
        <SettingsSideNav {...routerProps} organisation={organisation} />
      </Col>
      <Col span={window.innerWidth < 568 ? 24 : 18}>
        <ContainerCard>
          <Typography.Title level={4}>{t('Add a Partner')}</Typography.Title>
          <Typography.Paragraph style={{ color: 'var(--color-gray2)' }}>
            {t(
              'To assign a new Partner to {{brandName}} on Hablo, start by searching for their Organisation name below.',
              {
                brandName: brandName,
              },
            )}
          </Typography.Paragraph>

          <Row gutter={GUTTER_MD} style={{ margin: `${GUTTER_LG_PX} 0 ${GUTTER_LG_PX} 0` }}>
            <Label htmlFor="organisation">{t('Organisation')}</Label>
            <Col span={window.innerWidth < 568 ? 24 : 18} style={{ paddingLeft: 0 }}>
              <AutoComplete
                id="organisation"
                value={searchValue}
                defaultValue={searchValue}
                onChange={setSearchValue}
                onSearch={updateSearchText}
                onSelect={(_, option) => setReceiverOrganisationId(option.id)}
                placeholder={t("Type an Organisation's name")}
                popupClassName="org-autocomplete"
                style={{ width: '100%' }}
              >
                {results?.map(({ id, name, image, location, type, hasClubHabloSubscription, isPaid }, key) => {
                  return (
                    <AutoComplete.Option
                      value={name!}
                      id={id}
                      label={name}
                      key={key}
                      className={
                        (type !== OrganisationType.PrivateSector && type !== OrganisationType.Destination) ||
                        (type === OrganisationType.Destination && organisation.isDMOSubscription) ||
                        hasClubHabloSubscription ||
                        isPaid
                          ? 'disabled-item'
                          : ''
                      }
                    >
                      <List.Item.Meta
                        avatar={<Avatar id={image} width={40} />}
                        title={<strong>{name}</strong>}
                        description={getFormattedLocation(location)}
                      />
                    </AutoComplete.Option>
                  );
                })}
              </AutoComplete>
              <span style={{ fontSize: 12, color: 'var(--color-gray2)', fontWeight: 600 }}>
                {t('*You can only invite Private Sector or Destination organisations to become Partners.')}
              </span>
            </Col>
          </Row>

          <Typography.Title level={4} style={{ margin: `${GUTTER_MD_PX} 0` }}>
            {t('Limit the number of posts from this Partner')}
          </Typography.Title>
          <Typography.Paragraph style={{ color: 'var(--color-gray2)' }}>
            {t('Choose how to limit the number of posts that this Partner can broadcast to your followers per month.')}
          </Typography.Paragraph>

          <Label style={{ marginTop: `${GUTTER_LG_PX}` }} htmlFor="Number of Posts Per Month">
            {t('Number of Posts Per Month')}
          </Label>
          <Select
            defaultValue={2}
            value={postLimit}
            onChange={(value: number) => setPostLimit(value)}
            style={{ width: '100px' }}
          >
            {Array.from({ length: 10 }, (_, i) => i + 1).map((num) => (
              <Select.Option key={num} value={num}>
                {num}
              </Select.Option>
            ))}
          </Select>

          {allPartners?.partnerOrganisations && (
            <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
              <Tooltip
                title={
                  allPartners.partnerOrganisations.length - 1 > (organisation.DMOMaxPartners as number)
                    ? t(
                        'Sorry, you’ve hit the limit of Partners included in your subscription. Contact Support to upgrade your subscription',
                      )
                    : undefined
                }
              >
                <>
                  <Button
                    type="primary"
                    data-cy="send-request"
                    style={{
                      marginTop: GUTTER_LG_PX,
                    }}
                    disabled={
                      loading ||
                      !receiverOrganisationId ||
                      !postLimit ||
                      allPartners.partnerOrganisations.length - 1 > (organisation.DMOMaxPartners as number)
                    }
                    onClick={onSubmit}
                  >
                    {t('Send Request')}
                  </Button>
                </>
              </Tooltip>
            </div>
          )}
        </ContainerCard>
      </Col>
    </Row>
  );
}

const Label = styled.label`
  font-size: 12px;
  font-weight: 800;
  width: 100%;
  margin-bottom: ${GUTTER_SM_PX};
  line-height: 15px;
  display: block;
`;

const DisabledItem = styled.div`
  opacity: 0.5;
  pointer-events: none;
`;
