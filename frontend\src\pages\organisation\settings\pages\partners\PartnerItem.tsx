import { Linkable } from '@src/components/Buttons/Linkable';
import { BORDER_RADIUS, GUTTER_MD, GUTTER_MD_PX, GUTTER_SM_PX } from '@src/theme';
import { Space, Typography } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { Avatar } from '@src/components/Image/Image';
import { Routes } from '@src/routes/Routes';
import { encodePathParams } from '@src/utils/encodePathParams';
import dayjs from 'dayjs';
import { PartnerOrganisation } from '@src/graphql/GraphQLTypes';

function PartnerItem({
  partner,
  vanityId,
  isChild = false,
  children,
  linkToProfile = true,
}: {
  partner: PartnerOrganisation;
  vanityId: string;
  isChild?: boolean;
  linkToProfile: boolean;
  children?: React.ReactNode;
}) {
  const { t } = useTranslation();

  const path = encodePathParams(Routes.organisationProfile, { vanityId });
  const createdAt = dayjs(partner.createdAt).format('D MMMM YYYY');

  return (
    <Container>
      <AvatarContainer>
        <Linkable enabled={linkToProfile} to={path}>
          <Avatar id={isChild ? partner.parentOrganisation.image : partner.childOrganisation.image} width={56} />
        </Linkable>
      </AvatarContainer>
      <Info>
        <Linkable enabled={linkToProfile} to={path}>
          <OrganisationInfo level={4} style={{ display: 'inline-block' }} data-cy={'org-name'}>
            {isChild ? partner.parentOrganisation.name : partner.childOrganisation.name}
          </OrganisationInfo>
        </Linkable>
        <Typography.Text type="secondary" style={{ fontSize: 14, lineHeight: 1.4 }}>
          {partner.status} {createdAt}
        </Typography.Text>
        <Label style={{ marginTop: `${GUTTER_MD_PX}` }} htmlFor="Number of Posts Per Month">
          {t('Number of Posts Per Month')}
        </Label>
        <Typography.Text type="secondary" style={{ fontSize: 14, lineHeight: 1.4 }}>
          {partner.postsLimit}
        </Typography.Text>
      </Info>
      <Space size={GUTTER_MD}>{children}</Space>
    </Container>
  );
}

export default PartnerItem;

const Container = styled.div`
  flex: 1;
  display: flex;
  display: flex;
  align-items: flex-start;
  padding: ${GUTTER_MD_PX} 0;
  border-radius: ${BORDER_RADIUS};

  .ant-typography {
    margin: 0;
    line-height: 1.2em;
  }

  :not(:last-child) {
    margin-bottom: ${GUTTER_SM_PX};
  }
`;

const OrganisationInfo = styled(Typography.Title)`
  font-size: 16px !important;
  font-weight: 700 !important;
  line-height: 22px !important;
`;

const AvatarContainer = styled.div`
  margin-right: ${GUTTER_MD_PX};
  display: flex;
  align-items: flex-start;
`;

const Info = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  line-height: 1.3em;
`;

const Label = styled.label`
  font-size: 12px;
  font-weight: 800;
  width: 100%;
  margin-bottom: ${GUTTER_SM_PX};
  line-height: 15px;
  display: block;
`;
