import { Organisation, PartnerOrganisation, PartnershipRequestStatus } from '@GraphQLTypes'; // Assuming OrganisationType is available
import { OrganisationProfileProps } from '../../OrganisationSettings'; // Import common props
import { Row, Col, Typography, Button, List, Divider, message, Empty, Modal } from 'antd'; // Removed Checkbox, not used directly here
import { SettingsSideNav } from '../../components/SettingsSideNav';
import { ContainerCard } from '@components/Layout/Container';
import { GUTTER_LG, GUTTER_LG_PX, GUTTER_MD_PX } from '@theme';
import { hasPermission, OrganisationActions } from '../../../permissions';
import Icon, { Icons } from '@src/components/icons/Icon';
import { useTranslation } from 'react-i18next';
import { encodePathParams } from '@src/utils/encodePathParams';
import { useHistory } from 'react-router-dom';
import { Routes } from '@src/routes/Routes';
import PartnerItem from './PartnerItem';
import { GET_PARTNER_ORG_LIST, PartnerOrganisationsResponse, UPDATE_PARTNER_ORGANISATION } from './queries';
import { useProfile } from '@src/routes/ProfileProvider';
import { useMutation, useQuery } from '@apollo/client';
import { getPrimaryMembership } from '@src/utils/getOrganisationName';

interface PartnersPageProps extends OrganisationProfileProps {
  organisation: Organisation;
}

export function Partners({ organisation, ...routerProps }: PartnersPageProps) {
  const { t } = useTranslation();
  const { profile } = useProfile();
  const history = useHistory();

  const hasAddPartnersPermission = hasPermission(OrganisationActions.addPartners, organisation.permissions);

  const brandName = organisation.name || 'this destination';
  const membership = getPrimaryMembership(profile);

  const isChild = membership?.partnerOrganisation
    ? membership.partnerOrganisation.status === PartnershipRequestStatus.Approved
    : false;

  const partnerFeaturePoints = [
    {
      key: 'broadcast',
      text: isChild
        ? t(
            'Approved Partners can broadcast posts into their Partner’s followers’ feeds. The post will show as being from ‘[Partner Name] and {{brandName}}’.',
            {
              brandName,
            },
          )
        : t(
            "Approved Partner will be able to broadcast posts into {{brandName}}'s follower's feeds. The post will show as being from '{{brandName}} and [Partner Name]'.",
            {
              brandName,
            },
          ),
    },

    {
      key: 'adminRequest',
      text: isChild
        ? t('Only {{brandName}} Admins can accept a request to become a Partner.', {
            brandName,
          })
        : t(
            'Only {{brandName}} Admins can make a request to assign a Partner, and an Admin at the requested Organisation must approve the request.',
            {
              brandName,
            },
          ),
    },
    {
      key: 'limitedFeatures',
      text: isChild
        ? t(
            'After becoming a Partner, functionality on your {{brandName}} page will be limited, with no ability to create incentives, events, upload videos or add resources.',
            {
              brandName,
            },
          )
        : t(
            'All assigned Partners will have limited features on their organisation page, with no ability to create incentives, events, upload videos etc..',
          ),
    },
    {
      key: 'disconnect',
      text: isChild
        ? t(
            'On disconnecting from a Partner, the ability to post into your Partner’s followers feed will be removed and full organisation features will be reinstated (if applicable).',
          )
        : t(
            "On disconnecting an assigned Partner, the ability to post into {{brandName}}'s followers feed will be removed and full organisation feature reinstated (if applicable).",
            {
              brandName,
            },
          ),
    },
  ];

  const { data, refetch } = useQuery<PartnerOrganisationsResponse>(GET_PARTNER_ORG_LIST, {
    variables: {
      filter: {
        status: PartnershipRequestStatus.Pending,
      },
    },
  });

  const [updatePartnerOrganisation, { loading }] = useMutation<{}, any>(UPDATE_PARTNER_ORGANISATION);

  const handleAcceptRequest = async (childOrgId: string, parentOrgId: string) => {
    try {
      const data = {
        parentOrgId,
        childOrgId,
        status: PartnershipRequestStatus.Approved,
      };
      const response = await updatePartnerOrganisation({
        variables: { data },
      });
      if (response.data) {
        message.success(t('Partner accepted successfully'));
        await refetch();
        window.dispatchEvent(new Event('updateProfile'));
        return true;
      }
      return false;
    } catch (error: any) {
      message.error(error.message);
    }
  };

  const handleDeclineRequest = async (childOrgId: string, parentOrgId: string) => {
    try {
      const data = {
        parentOrgId,
        childOrgId,
        status: PartnershipRequestStatus.Rejected,
      };
      const response = await updatePartnerOrganisation({
        variables: { data },
      });
      if (response.data) {
        message.success(t('Partner declined successfully'));
        await refetch();
        window.dispatchEvent(new Event('updateProfile'));
        return true;
      }
      return false;
    } catch (error: any) {
      message.error(error.message);
    }
  };

  const handleDisconnectRequest = async (childOrgId: string, parentOrgId: string) => {
    Modal.confirm({
      centered: true,
      content: t('Are you sure you want to disconnect with your current partner?'),
      cancelText: t('Cancel'),
      okText: t('Yes, disconnect'),
      onOk: async () => {
        try {
          const data = {
            parentOrgId,
            childOrgId,
            status: PartnershipRequestStatus.Disconnected,
          };
          const response = await updatePartnerOrganisation({
            variables: { data },
          });
          if (response.data) {
            message.success(t('Partner disconnected successfully'));
            await refetch();
            window.dispatchEvent(new Event('updateProfile'));
            return true;
          }
          return false;
        } catch (error: any) {
          message.error(error.message);
        }
      },
    });
  };

  return (
    <>
      <Row gutter={GUTTER_LG}>
        <Col span={window.innerWidth < 568 ? 24 : 6}>
          <SettingsSideNav {...routerProps} organisation={organisation} />
        </Col>
        <Col span={window.innerWidth < 568 ? 24 : 18}>
          <ContainerCard>
            <div>
              <Typography.Title level={4} style={{ fontSize: 18 }}>
                {t('Partners')}
              </Typography.Title>
              <Typography.Paragraph style={{ fontSize: 14 }}>
                {t(
                  'Adding Partners allows destinations to assign their private sector and regional DMO partners to their page on Hablo. This means:',
                )}
              </Typography.Paragraph>

              <Row
                gutter={[
                  { xs: 0, sm: 0, md: 16 },
                  { xs: 0, sm: 0, md: 16 },
                ]}
                style={{ flex: 1, marginBottom: GUTTER_LG_PX, marginTop: GUTTER_MD_PX }}
              >
                <Col xs={24} md={12}>
                  <List
                    dataSource={partnerFeaturePoints.slice(0, 2)} // First two points
                    renderItem={(item) => (
                      <List.Item
                        style={{
                          borderBottom: 'none',
                          padding: '4px 0',
                          gap: '10px',
                          display: 'flex',
                          alignItems: 'flex-start',
                        }}
                      >
                        <Icon icon={Icons.check} size={18} color="var(--color-primary)" style={{ marginTop: '4px' }} />
                        <Typography.Text style={{ flex: 1, fontSize: 14 }}>{item.text}</Typography.Text>
                      </List.Item>
                    )}
                  />
                </Col>
                <Col xs={24} md={12}>
                  <List
                    dataSource={partnerFeaturePoints.slice(2)} // Last two points
                    renderItem={(item) => (
                      <List.Item
                        style={{
                          borderBottom: 'none',
                          padding: '4px 0',
                          gap: '10px',
                          display: 'flex',
                          alignItems: 'flex-start',
                        }}
                      >
                        <Icon icon={Icons.check} size={18} color="var(--color-primary)" style={{ marginTop: '4px' }} />
                        <Typography.Text style={{ flex: 1, fontSize: 14 }}>{item.text}</Typography.Text>
                      </List.Item>
                    )}
                  />
                </Col>
              </Row>
              <Divider style={{ margin: '30px 0 30px 0' }} />
              {organisation.isDMOSubscription ? (
                <Row>
                  <Col xs={24}>
                    <Typography.Title level={5}>
                      {t('Connect {{brandName}} To A New Partner', { brandName })}
                    </Typography.Title>
                    <Typography.Paragraph>
                      {t(
                        'To make a request to assign a partner to {{brandName}}, click the button below to get started:',
                        {
                          brandName,
                        },
                      )}
                    </Typography.Paragraph>

                    <Button
                      type="primary"
                      data-cy="add-partners"
                      style={{
                        marginBottom: window.innerWidth < 568 ? '24px' : 0,
                      }}
                      onClick={() => {
                        history.push(
                          encodePathParams(Routes.addPartners, {
                            organisationId: organisation?.id,
                          }),
                        );
                      }}
                      disabled={!hasAddPartnersPermission}
                    >
                      {t('Add Partner')}
                    </Button>
                  </Col>
                </Row>
              ) : (
                <Row>
                  <Col xs={24}>
                    <Typography.Title level={5}>{t('Current Partner')}</Typography.Title>
                  </Col>

                  <Col xs={24} sm={24}>
                    {data?.partnerOrganisations && data?.partnerOrganisations.length > 0 ? (
                      data?.partnerOrganisations?.map((partner: PartnerOrganisation, index) => {
                        return (
                          <div key={index}>
                            <PartnerItem
                              linkToProfile
                              partner={partner}
                              vanityId={partner.childOrganisation?.vanityId as string}
                            >
                              {partner.status === PartnershipRequestStatus.Pending ? (
                                <>
                                  <Button
                                    type="ghost"
                                    data-cy="partner-decline"
                                    loading={loading}
                                    onClick={() => handleDeclineRequest(partner.childOrgId, partner.parentOrgId)}
                                  >
                                    {t('Decline')}
                                  </Button>
                                  <Button
                                    type="primary"
                                    data-cy="partner-accept"
                                    loading={loading}
                                    style={{
                                      marginBottom: window.innerWidth < 568 ? '24px' : 0,
                                    }}
                                    onClick={() => handleAcceptRequest(partner.childOrgId, partner.parentOrgId)}
                                  >
                                    {t('Accept')}
                                  </Button>
                                </>
                              ) : (
                                <Button
                                  type="primary"
                                  data-cy="partner-disconnect"
                                  loading={loading}
                                  style={{
                                    marginBottom: window.innerWidth < 568 ? '24px' : 0,
                                  }}
                                  onClick={() => handleDisconnectRequest(partner.childOrgId, partner.parentOrgId)}
                                >
                                  {t('Leave Parent Organisation')}
                                </Button>
                              )}
                            </PartnerItem>
                            {index !== data.partnerOrganisations.length - 1 && (
                              <Divider style={{ margin: '10px 0 10px 0' }} />
                            )}
                          </div>
                        );
                      })
                    ) : membership?.partnerOrganisation &&
                      membership.partnerOrganisation.status === PartnershipRequestStatus.Approved ? (
                      <PartnerItem
                        linkToProfile
                        partner={membership.partnerOrganisation as any}
                        vanityId={membership.partnerOrganisation.parentOrganisation?.vanityId as string}
                        isChild
                      >
                        <Button
                          type="primary"
                          data-cy="partner-disconnect"
                          loading={loading}
                          style={{
                            marginBottom: window.innerWidth < 568 ? '24px' : 0,
                          }}
                          onClick={() =>
                            handleDisconnectRequest(
                              membership.partnerOrganisation?.childOrgId as string,
                              membership.partnerOrganisation?.parentOrgId as string,
                            )
                          }
                        >
                          {t('Leave Parent Organisation')}
                        </Button>
                      </PartnerItem>
                    ) : (
                      <Empty
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                        description="No Partner requests received. To add Partners to your own organsation, contact <EMAIL> to upgrade your Subscription."
                      />
                    )}
                  </Col>
                </Row>
              )}
            </div>
          </ContainerCard>
        </Col>
      </Row>
    </>
  );
}
