import { <PERSON><PERSON>, <PERSON>, Divider, Empty, Row, Typography } from 'antd';

import { GUTTER_LG } from '@src/theme';
import { OrganisationProfileProps } from '../../OrganisationSettings';
import { SettingsSideNav } from '../../components/SettingsSideNav';
import { Organisation, PartnershipRequestStatus } from '@GraphQLTypes';
import { ContainerCard, ContainerCentered } from '@components/Layout/Container';
import { useTranslation } from 'react-i18next';
import PartnerItem from './PartnerItem';
import { GET_PARTNER_ORG_LIST, PartnerOrganisationsResponse } from './queries';
import { useQuery } from '@apollo/client';

type Props = OrganisationProfileProps & {
  organisation: Organisation;
};

export function PreviousPartners({ organisation, ...routerProps }: Props) {
  const { t } = useTranslation();
  const brandName = organisation.name || 'this destination';

  const { data } = useQuery<PartnerOrganisationsResponse>(GET_PARTNER_ORG_LIST, {
    variables: {
      filter: {
        status: [PartnershipRequestStatus.Rejected, PartnershipRequestStatus.Disconnected],
        organisationId: organisation.id,
      },
    },
  });

  return (
    <Row gutter={GUTTER_LG}>
      <Col xs={24} sm={6}>
        <SettingsSideNav {...routerProps} organisation={organisation} />
      </Col>
      <Col xs={24} sm={18}>
        <ContainerCard>
          <Typography.Title level={4}>{t('Previous Partners')}</Typography.Title>
          <Typography.Paragraph style={{ color: 'var(--color-gray2)' }}>
            {t('Below are the disconnected and cancelled requests for Partners of {{brandName}} on Hablo.', {
              brandName: brandName,
            })}
          </Typography.Paragraph>

          {data?.partnerOrganisations && data?.partnerOrganisations.length > 0 ? (
            data?.partnerOrganisations?.map((partner, index) => {
              return (
                <div key={index}>
                  <PartnerItem
                    linkToProfile
                    partner={partner}
                    key={index}
                    vanityId={partner.childOrganisation.vanityId}
                  >
                    <Button type="ghost" data-cy={partner.status} disabled>
                      {partner.status}
                    </Button>
                  </PartnerItem>
                  {index !== data?.partnerOrganisations.length - 1 && <Divider style={{ margin: '10px 0 10px 0' }} />}
                </div>
              );
            })
          ) : (
            <ContainerCentered style={{ marginTop: 20 }}>
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={t('No previours partners yet.')} />
            </ContainerCentered>
          )}
        </ContainerCard>
      </Col>
    </Row>
  );
}
