import { gql } from '@apollo/client';
import { PartnerOrganisation } from '@src/graphql/GraphQLTypes';

export type CreatePartnerOrganisationType = {
  parentOrgId: string;
  childOrgId: string;
  postsLimit: number;
};

export interface CreatePartnerOrganisationResponse {
  createPartnerOrganisation: {
    id: string;
    parentOrgId: string;
    childOrgId: string;
    status: string;
    postsLimit: number;
  };
}

export type CreatePartnerOrganisationVariables = {
  data: CreatePartnerOrganisationType;
};

export type PartnerOrganisationsResponse = {
  partnerOrganisations: PartnerOrganisation[];
};

export const CREATE_PARTNER_ORGANISATION = gql`
  mutation CreatePartnerOrganisation($data: CreatePartnerOrganisationInput!) {
    createPartnerOrganisation(data: $data) {
      id
      parentOrgId
      childOrgId
      status
      postsLimit
    }
  }
`;

export const UPDATE_PARTNER_ORGANISATION = gql`
  mutation UpdatePartnerOrganisationByOrgs($data: UpdatePartnerOrganisationByOrgsInput!) {
    updatePartnerOrganisationByOrgs(data: $data) {
      id
      parentOrgId
      childOrgId
      status
      postsLimit
      connectionApprovedDate
      disconnectionDate
      createdAt
      updatedAt
    }
  }
`;

export const DELETE_PARTNER_ORGANISATION = gql`
  mutation DeletePartnerOrganisationByOrgs($parentOrgId: String!, $childOrgId: String!) {
    deletePartnerOrganisationByOrgs(parentOrgId: $parentOrgId, childOrgId: $childOrgId)
  }
`;

export const GET_PARTNER_ORG_LIST = gql`
  query PartnerOrganisations($filter: PartnerOrganisationsFilter) {
    partnerOrganisations(filter: $filter) {
      id
      parentOrgId
      childOrgId
      status
      postsLimit
      connectionApprovedDate
      disconnectionDate
      createdAt
      updatedAt
      parentOrganisation {
        id
        name
        vanityId
        image
      }
      childOrganisation {
        id
        name
        vanityId
        image
      }
    }
  }
`;
