import { gql } from '@apollo/client';

import {
  Maybe,
  MembershipPermission,
  MembershipsFilter,
  Organisation,
  Profile,
  ProfilesFilter,
  ProfilesResult,
  Scalars,
  UpdateOrganisationInput,
} from '@GraphQLTypes';

export type GetOrganisationData = {
  organisation: Organisation;
};

export const GET_ORGANISATION = gql`
  query GetOrganisation($organisationId: String!) {
    organisation(id: $organisationId) {
      id
      name
      type
      image
      backgroundImage
      location
      isPublic
      privacy
      followingPrivacy
      peoplePrivacy
      additionalPrivacy
      followingPrivacy
      website
      description
      size
      vanityId
      isOwner
      isMember
      pages
      permissions
      followerStatus
      preApprovedDomains
      stripeConnectAccount
      DMOMaxPartners
      isDMOSubscription
    }
  }
`;

export type UpdateOrganisationData = {
  updateOrganisation: Organisation;
};

export type UpdateOrganisationVariables = {
  organisationId: string;
  organisationData: UpdateOrganisationInput;
};

export const UPDATE_ORGANISATION = gql`
  mutation ($organisationId: String!, $organisationData: UpdateOrganisationInput!) {
    updateOrganisation(organisationId: $organisationId, organisationData: $organisationData) {
      id
      name
      type
      image
      backgroundImage
      location
      privacy
      peoplePrivacy
      additionalPrivacy
      followingPrivacy
      website
      description
      size
      vanityId
      isOwner
      isMember
      resourceUrl
      resources
      followerStatus
      pages
    }
  }
`;

export type GetOrganisationMembersData = {
  organisation: Pick<Organisation, 'memberships'>;
};

export type GetOrganisationMembersVariables = {
  organisationId: string;
  membershipsFilter?: MembershipsFilter;
};

export const GET_ORGANISATION_MEMBERS = gql`
  query GetOrganisationMembers($organisationId: String!, $membershipsFilter: MembershipsFilter) {
    organisation(id: $organisationId) {
      id
      memberships(filter: $membershipsFilter) {
        records {
          id
          status
          permissions
          createdAt
          position
          isPrimary
          profile {
            id
            name
            image
            onlineStatus
            lastOnlineAt
            streamChannelId
            responsibilities
            memberships {
              id
              isPrimary
              position
              organisation {
                id
                name
              }
              organisationName
            }
          }
          organisation {
            id
            name
          }
        }
      }
    }
  }
`;

export type SearchProfilesData = {
  profiles: ProfilesResult;
};

export type SearchProfilesVariables = {
  filter: ProfilesFilter;
};

export const SEARCH_PROFILES = gql`
  query SearchProfiles($filter: ProfilesFilter!) {
    profiles(first: 30, after: null, sortBy: "name", sortOrder: Descending, filter: $filter) {
      totalCount
      records {
        id
        name
        email
        image
        location
        headline
        memberships {
          id
          isPrimary
          position
          organisationName
          organisation {
            id
            isOwner
            name
            description
          }
        }
      }
    }
  }
`;

export type SearchOrganisationsAndProfilesData = {
  autocompleteMentions: {
    profiles: Array<Profile>;
    organisations: Array<Organisation>;
  };
};

export type SearchOrganisationsAndProfilesVariables = {
  searchText?: Maybe<Scalars['String']>;
};

export const SEARCH_ORGANISATIONS_AND_PROFILES = gql`
  query SearchOrganisationsAndProfiles($searchText: String!, $isFuzzySearch: Boolean!) {
    autocompleteMentions(searchText: $searchText, isFuzzySearch: $isFuzzySearch) {
      profiles {
        id
        name
        image
        location
      }
      organisations {
        id
        name
        image
        type
        location
        vanityId
        isPublic
        followerStatus
      }
    }
  }
`;

export type OrganisationAddMembershipVariables = {
  permissions: MembershipPermission[];
  profileId: string;
  organisationId: string;
};

export const ORGANISATION_ADD_MEMBERSHIP = gql`
  mutation ($permissions: [MembershipPermission!]!, $profileId: String!, $organisationId: String!) {
    addMembershipPermissions(permissions: $permissions, profileId: $profileId, organisationId: $organisationId)
  }
`;

export type OrganisationUpdateMembershipVariables = {
  permissions: MembershipPermission[];
  profileId: string;
  organisationId: string;
};

export const ORGANISATION_UPDATE_MEMBERSHIP = gql`
  mutation ($permissions: [MembershipPermission!]!, $profileId: String!, $organisationId: String!) {
    updateMembershipPermissions(permissions: $permissions, profileId: $profileId, organisationId: $organisationId)
  }
`;

export type OrganisationRemoveMembershipVariables = {
  organisationId: string;
  profileId: string;
};

export const ORGANISATION_REMOVE_MEMBERSHIP = gql`
  mutation ($organisationId: String!, $profileId: String!) {
    removeMembership(organisationId: $organisationId, profileId: $profileId)
  }
`;

export type OrganisationChangeOwnerVariables = {
  profileId: string;
  organisationId: string;
};

export const ORGANISATION_CHANGE_OWNER = gql`
  mutation ($profileId: String!, $organisationId: String!) {
    changeOrganisationOwnership(newOwnerProfileId: $profileId, organisationId: $organisationId)
  }
`;

export type OrganisationAcceptOwnerVariables = {
  organisationId: string;
};

export const ORGANISATION_ACCEPT_OWNER = gql`
  mutation ($organisationId: String!) {
    acceptOrganisationOwnership(organisationId: $organisationId)
  }
`;

export type OrganisationRejectOwnerVariables = {
  organisationId: string;
};

export const ORGANISATION_REJECT_OWNER = gql`
  mutation ($organisationId: String!) {
    rejectOrganisationOwnership(organisationId: $organisationId)
  }
`;

export type GetStripeExpressLinkVariables = {
  organisationId: string;
};

export const GET_STRIPE_EXPRESS_LINK = gql`
  mutation GetStripeExpressLink($organisationId: String!) {
    getStripeExpressLink(organisationId: $organisationId)
  }
`;

export type RetrieveConnectAccountVariables = {
  organisationId: string;
};

export const RETRIEVE_CONNECT_ACCOUNT = gql`
  mutation RetreiveConnectAccount($organisationId: String!) {
    retrieveConnectAccount(organisationId: $organisationId)
  }
`;
