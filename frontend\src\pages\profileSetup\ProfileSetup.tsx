import { useMutation, useQuery } from '@apollo/client';
import { Form, Result, Steps, Typography, Modal } from 'antd';
import { FormFinishInfo } from 'rc-field-form/lib/FormContext';
import { useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { RouteComponentProps } from 'react-router-dom';

import Icon, { Icons } from '@components/icons/Icon';
import { Loader } from '@components/Loader';
import { Profile, FollowerStatus } from '@GraphQLTypes';
import { GUTTER_LG_PX } from '@src/theme';
import { updateCacheProfile } from '@src/graphql/graphQLUpdaters';
import { getNextStep } from './ProfileSetupSteps';
import {
  GET_PROFILE_SETUP,
  UPDATE_PRIMARY_ORGANISATION,
  UPDATE_PROFILE,
  OrganisationQueryData,
  GET_ORGANISATION_BY_VANITY_ID,
  FollowerOrganisationVariables,
  FOLLOW_ORGANISATION,
  UPDATE_PROFILE_REFERRING_ORGANISATION,
} from './queries';
import { GET_PROFILE } from '@src/routes/queries';
import { CREATE_EXPERIENCE } from '../profile/queries';
import { ContainerCard } from '@components/Layout/Container';
import { referringOrganisation } from '@utils/referringOrganisation';
import { updateCacheOrganisationFollowerStatus } from '@src/graphql/graphQLUpdaters';
import { SettingLanguage } from '../profileSettings/account/SettingLanguage';
import { LANGUAGES } from '../../consts';
import { SupportedLocales } from '@src/locale/SupportedLocales';
import { ADD_PROFILE_PARENT_ORGANISATION } from '../profile/Info/queries';

export enum Forms {
  onboarding1 = 'onboarding1',
  onboarding2 = 'onboarding2',
  onboarding3 = 'onboarding3',
  onboarding4 = 'onboarding4',
  onboarding5 = 'onboarding5',
}

type QueryData = {
  profile: Profile;
};

export default function ProfileSetup(props: RouteComponentProps) {
  const { t } = useTranslation('profile-setup');
  const { error, loading, data } = useQuery<QueryData>(GET_PROFILE_SETUP);
  const referringOrgVanityId = referringOrganisation();
  const [showModal, setShowModal] = useState(false);
  const { i18n } = useTranslation();

  const {
    error: errorOrg,
    loading: loadingOrg,
    data: dataOrg,
  } = referringOrgVanityId
    ? useQuery<OrganisationQueryData>(GET_ORGANISATION_BY_VANITY_ID, {
        variables: { vanityId: referringOrgVanityId },
      })
    : { error: null, loading: null, data: null };

  const referringOrgName = !errorOrg && !loadingOrg && dataOrg ? dataOrg.organisation.name : null;

  const referringOrgId = dataOrg ? dataOrg.organisation.id : '';
  const [followOrganisation, { loading: followLoading }] = useMutation<{}, FollowerOrganisationVariables>(
    FOLLOW_ORGANISATION,
    {
      update: updateCacheOrganisationFollowerStatus(
        referringOrgId,
        dataOrg && dataOrg.organisation.isPublic ? FollowerStatus.Active : FollowerStatus.Pending,
      ),
    },
  );

  const [updateProfile, { loading: updateProfileLoading }] = useMutation(UPDATE_PROFILE, {
    update: updateCacheProfile({ query: GET_PROFILE_SETUP }),
    refetchQueries: [{ query: GET_PROFILE }],
  });
  const [updatePrimaryOrganisation, { loading: updatePrimaryOrganisationLoading }] = useMutation(
    UPDATE_PRIMARY_ORGANISATION,
    {
      refetchQueries: [{ query: GET_PROFILE_SETUP }],
      onCompleted: () => {
        localStorage.removeItem('organisationId');
      },
    },
  );
  const [createExperience, { loading: createExperienceLoading }] = useMutation(CREATE_EXPERIENCE, {
    refetchQueries: [{ query: GET_PROFILE_SETUP }],
  });

  const [addProfileParentOrganisation, { loading: addParentLoading }] = useMutation(ADD_PROFILE_PARENT_ORGANISATION, {
    onCompleted: () => {
      localStorage.removeItem('MembershipOrganisationId');
    },
  });

  const [updateProfileReferringOrganisation] = useMutation(UPDATE_PROFILE_REFERRING_ORGANISATION);

  if (error) {
    return (
      <Page>
        <Result
          status="error"
          title={t('Error Completing Profile')}
          subTitle={
            <Trans>
              We are unable to complete your profile, please contact{' '}
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </Trans>
          }
        />
      </Page>
    );
  }

  if (loading || !data) {
    return (
      <Page>
        <Loader useGIF={true} allowLoadingText={true} customLoadText={'Stuck loading profile, refresh'} />;
      </Page>
    );
  }

  const { progress, Step } = getNextStep(data.profile, updateProfileLoading);

  async function handleFormSubmission(form: string, { values: { _, ...values } }: FormFinishInfo) {
    if (form === Forms.onboarding1 && !loadingOrg && dataOrg && data?.profile && dataOrg.organisation.id) {
      await followOrganisation({ variables: { organisationId: dataOrg.organisation.id } });
      await updateProfileReferringOrganisation({
        variables: {
          data: {
            profileId: data.profile.id,
            referringOrganisationId: dataOrg.organisation.id,
          },
        },
      });
    }

    if (form === Forms.onboarding3) {
      await updatePrimaryOrganisation({ variables: values }).catch((error) => {
        console.error('Error updating primary organisation: ', error);
      });

      if (localStorage.getItem('MembershipOrganisationId')) {
        const membershipOrgId = localStorage.getItem('MembershipOrganisationId');
        if (data?.profile.memberships[0]?.organisation?.id !== membershipOrgId) {
          await addProfileParentOrganisation({
            variables: {
              data: {
                profileId: data?.profile.id,
                parentId: membershipOrgId,
              },
            },
          });
        }
        localStorage.removeItem('MembershipOrganisationId');
      }
      return;
    }

    if (form === Forms.onboarding4) {
      await updatePrimaryOrganisation({
        variables: {
          organisationId: data?.profile.memberships[0]?.organisation?.id,
          organisationName: data?.profile.memberships[0]?.organisationName,
          organisationPosition: values.organisationPosition,
        },
      });
      values.startDate &&
        (await createExperience({
          variables: {
            experienceData: {
              startDate: values.startDate,
              position: values.organisationPosition,
              description: values.headline,
              location: data?.profile.location,
              organisationId: data?.profile.memberships[0]?.organisation?.id,
              organisationName: data?.profile.memberships[0]?.organisationName,
            },
          },
        }));
      await updateProfile({
        variables: {
          profileData: {
            headline: values.headline,
            sellHolidays: values.sellHolidays,
          },
        },
      });
      return;
    }

    if (values?.name) {
      values.name = values.name.trim();
    }

    if (values.regions && values.regions.length) {
      values.useAutofollow = true; // flag to automatically follow orgs for selected regions
    }
    if (
      data?.profile &&
      data?.profile?.memberships[0] &&
      data?.profile.memberships[0]?.organisation &&
      data?.profile.memberships[0]?.organisation.privacy === 'protected'
    ) {
      values.useAutofollow = false;
    }

    return updateProfile({ variables: { profileData: values } });
  }

  return (
    <Page>
      <Container>
        {progress > -1 && (
          <StepsContainer>
            <Steps
              current={progress}
              size="small"
              labelPlacement="vertical"
              progressDot={(_: any, props: { status: 'finish' | 'wait' | 'process' | 'error' }) => {
                switch (props.status) {
                  case 'finish': {
                    return (
                      <ProgressIcon backgroundColor="var(--color-accent)" borderColor="var(--color-accent)">
                        <Icon icon={Icons.check} size={20} color="white" />
                      </ProgressIcon>
                    );
                  }
                  case 'process': {
                    return <ProgressIcon backgroundColor="transparent" borderColor="var(--color-primary)" />;
                  }
                  case 'wait':
                  case 'error':
                    return <ProgressIcon backgroundColor="transparent" borderColor="var(--color-text30)" />;
                }
              }}
            >
              <Steps.Step title={t('Sign Up')} />
              <Steps.Step title={t('Basic Details')} />
              <Steps.Step title={t('Your Work')} />
            </Steps>
          </StepsContainer>
        )}
        <CustomBr />
        <ContainerCard style={{ padding: 0 }}>
          <Header>
            {referringOrgName ? (
              <>
                <Typography.Text type="secondary" style={{ color: '#F2F2F2' }}>
                  {t('Join')}
                </Typography.Text>
                <ReferringOrgTitle>{referringOrgName.toUpperCase()}</ReferringOrgTitle>
                <OnHablo>
                  <Typography.Text type="secondary" style={{ color: '#F2F2F2' }}>
                    {t('On')}
                  </Typography.Text>
                  <HabloLogo icon={Icons.logoAndNameWhite} size={100} />
                </OnHablo>
              </>
            ) : (
              <>
                <Icon icon={Icons.logoAndNameWhite} size={200} style={{ margin: '-70px 0' }} />
                <Typography.Text type="secondary" style={{ color: '#F2F2F2' }}>
                  {t('Connecting the Travel Industry')}
                </Typography.Text>
              </>
            )}
          </Header>
          <Body>
            <Form.Provider onFormFinish={handleFormSubmission}>
              <Step
                profile={data.profile}
                loading={updateProfileLoading || updatePrimaryOrganisationLoading || createExperienceLoading}
              />
            </Form.Provider>
          </Body>
        </ContainerCard>
        <LanguageSetting onClick={() => setShowModal(true)}>
          <LanguageContainer>
            <a href="#">
              <LanguageText>
                {LANGUAGES[i18n.language as SupportedLocales]
                  ? LANGUAGES[i18n.language as SupportedLocales]
                  : 'Language'}
              </LanguageText>
            </a>
            <span>{FLAGS[i18n.language as SupportedLocales] ? FLAGS[i18n.language as SupportedLocales] : '🌎'}</span>
          </LanguageContainer>
        </LanguageSetting>
        <Modal width={550} destroyOnClose={true} footer={null} open={showModal} onCancel={() => setShowModal(false)}>
          <SettingLanguage />
        </Modal>
      </Container>
    </Page>
  );
}

const HabloLogo = styled(Icon)`
  svg {
    height: 45px !important;
  }
`;

const ProgressIcon = styled.div<{ backgroundColor: string; borderColor: string }>`
  width: 40px;
  height: 40px;
  background-color: ${({ backgroundColor }) => backgroundColor};
  border-style: solid;
  border-width: 2px;
  border-color: ${({ borderColor }) => borderColor};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  @media screen and (max-width: 568px) {
    width: 20px;
    height: 20px;
  }
`;

const Page = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 100px;

  @media screen and (max-width: 568px) {
    padding-top: unset;
  }
`;

const Container = styled.div`
  width: 415px;
  @media screen and (max-width: 568px) {
    width: 100%;
    height: 100%;
    display: flex;
    flex-flow: column;
  }
`;

const StepsContainer = styled.div`
  @media screen and (max-width: 568px) {
    margin: 15px;
  }
  .ant-steps {
    line-height: 40px;
  }
  .ant-steps-dot .ant-steps-item-icon {
    width: 40px;
    height: 40px;
    margin-left: 45px;
  }
  .ant-steps-dot .ant-steps-item-tail {
    width: 60%;
    margin: 0 0 0 90px;
  }
  .ant-steps-item-process > .ant-steps-item-container {
    > .ant-steps-item-tail::after {
      background-color: var(--color-text30);
    }

    > .ant-steps-item-content > .ant-steps-item-title {
      color: var(--color-primary);
    }
  }
  .ant-steps-item-wait > .ant-steps-item-container {
    > .ant-steps-item-tail::after {
      background-color: var(--color-text30);
    }
  }
  @media screen and (max-width: 568px) {
    .ant-steps-item-container {
      display: flex;
      flex-direction: column;
      /* justify-content: center; */
      align-items: center;
    }
    .ant-steps {
      line-height: 20px;
      width: 100%;
      display: flex;
      flex-direction: row;
    }
    .ant-steps-dot .ant-steps-item-icon {
      width: 20px !important;
      height: 20px !important;
      margin-left: 22.5px;
      display: block;
    }
    .ant-steps-dot .ant-steps-item-tail {
      margin: 0 0 0 60px;
      top: -10.5px !important;
      left: 80% !important;
      transform: translate(50%, 0%);
    }
    .ant-steps-item-title {
      padding: 0;
      font-size: 12px;
      text-align: center;
    }
    .ant-steps-dot .ant-steps-item-tail::after,
    .ant-steps-dot.ant-steps-small .ant-steps-item-tail::after {
      height: 1.5px;
      margin-left: 3px;
      width: 50px;
    }
    .ant-steps-dot .ant-steps-item-content,
    .ant-steps-dot.ant-steps-small .ant-steps-item-content {
      // width: 70px;
      min-height: 30px;
    }
  }
`;

export const Header = styled.div`
  background-color: var(--color-primary);
  padding: 20px;
  width: 100%;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 14px;
`;

export const Body = styled.div`
  padding: ${GUTTER_LG_PX};
  @media screen and (max-width: 568px) {
    height: 100%;
    padding-bottom: 60px;
  }
`;

export const OnHablo = styled.div`
  display: flex;
  align-items: center;
  > .ant-typography {
    margin-right: 10px;
  }
`;

export const CustomBr = styled.br`
  display: block;
  @media screen and (max-width: 568px) {
    display: none;
  }
`;

export const ReferringOrgTitle = styled(Typography.Title).attrs({ level: 1 })`
  color: #f2f2f2 !important;
  margin-top: 5px !important;
  margin-bottom: 5px !important;
  text-align: center !important;
  @media screen and (max-width: 568px) {
    font-size: 8vw !important;
  }
`;

const LanguageSetting = styled.div`
  display: flex;
  margin-top: 25px !important;
  font-size: 14px;
  justify-content: center;
`;
const LanguageContainer = styled.div`
  display: flex;
`;

const LanguageText = styled.div`
  flex: 1;
  margin-right: 6px;
`;
const FLAGS: { [locale in SupportedLocales]: string } = {
  en: '🇬🇧',
  de: '🇩🇪',
  fr: '🇫🇷',
  it: '🇮🇹',
  es: '🇪🇸',
  zh: '🇨🇳',
};
