import { gql } from '@apollo/client';
import { PrimaryMembershipFragment } from '@src/graphql/graphQLFragments';
import { Organisation } from '@GraphQLTypes';

export const GET_PROFILE_SETUP = gql`
  query GetProfileSetup {
    profile {
      id
      isComplete
      name
      dateOfBirth
      gender
      location
      headline
      bio
      regions
      sellHolidays
      showGettingStartedFeedAt
      experiences {
        id
      }
      ...PrimaryMembership
    }
  }
  ${PrimaryMembershipFragment}
`;

export const UPDATE_PROFILE = gql`
  mutation UpdateProfile($profileData: UpdateProfileInput!) {
    updateProfile(profileData: $profileData) {
      id
      isComplete
      name
      dateOfBirth
      gender
      location
      headline
      bio
      regions
      sellHolidays
      showGettingStartedFeedAt
      experiences {
        id
      }
      ...PrimaryMembership
    }
  }
  ${PrimaryMembershipFragment}
`;

export const UPDATE_PRIMARY_ORGANISATION = gql`
  mutation UpdatePrimaryOrganisation(
    $organisationPosition: String
    $organisationName: String
    $organisationId: String
    $preApprove: Boolean
  ) {
    setPrimaryOrganisation(
      position: $organisationPosition
      organisationName: $organisationName
      organisationId: $organisationId
      preApprove: $preApprove
    )
  }
`;

export const UPDATE_PROFILE_REFERRING_ORGANISATION = gql`
  mutation UpdateProfileReferringOrganisation($data: UpdateProfileReferringOrganisationInput!) {
    updateProfileReferringOrganisation(data: $data) {
      id
      referredByOrganisationId
    }
  }
`;

export type OrganisationQueryData = {
  organisation: Required<Organisation>;
};

export const GET_ORGANISATION_BY_VANITY_ID = gql`
  query GetOrganisationNameByVanityId($vanityId: String) {
    organisation(vanityId: $vanityId) {
      id
      name
      vanityId
      isPublic
    }
  }
`;

export type FollowerOrganisationVariables = {
  organisationId: string;
};

export const FOLLOW_ORGANISATION = gql`
  mutation FollowOrganisation($organisationId: String!) {
    follow(organisationId: $organisationId)
  }
`;
