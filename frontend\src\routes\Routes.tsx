import React, { ComponentType, LazyExoticComponent } from 'react';
import { Route, RouteProps } from 'react-router';
import { RouteComponentProps } from 'react-router-dom';

import { ErrorHandler } from '@components/ErrorHandler';
import { lazyRetry } from '@utils/retry';

type LazyComponent = LazyExoticComponent<ComponentType<RouteComponentProps | any>>;
type RouterProps = RouteProps & { Component: LazyComponent; hideNav?: boolean };

export enum Routes {
  // public
  login = `/login`,
  logout = `/logout`,
  signUp = `/signup`,
  authCallback = `/auth/callback`,
  networkError = `/networkerror`,

  // private
  default = `/`,
  home = `/home`,
  calendar = `/calendar`,
  notifications = `/notifications`,
  search = `/search`,
  inbox = `/inbox`,
  inboxChat = `/inbox/chat/:chatId`,
  inboxChatNew = `/inbox/chat/new`,
  video = `/inbox/call/:callId`,
  explore = `/explore`,
  savedPosts = '/saved-posts',
  organisationChooseType = `/organisation/join`,
  organisationCreate = `/organisation/join/:type`,
  organisationSettings = `/organisation/:organisationId`,
  organisationSettingsInfo = `/organisation/:organisationId/info`,
  organisationSettingsEmployees = `/organisation/:organisationId/employees`,
  organisationSettingsMembers = `/organisation/:organisationId/members`,
  organisationSettingsUserRoles = `/organisation/:organisationId/user-roles`,
  organisationSettingsPrivacy = `/organisation/:organisationId/privacy`,
  organisationSettingsConnections = `/organisation/:organisationId/connections`,
  organisationSettingsInvitations = `/organisation/:organisationId/events`,
  organisationSettingsPartners = `/organisation/:organisationId/partners`,
  organisationSettingsPartnersActive = `/organisation/:organisationId/partners/active`,
  organisationSettingsPartnersPrevious = `/organisation/:organisationId/partners/previous`,
  connect = `/connect`,
  connectInviteContacts = `/connect/invite-contacts`,
  connectInviteContactsSent = `/connect/invite-contacts/sent`,
  incentives = `/incentives`,
  profile = `/profile`,
  profileSetup = `/profile/setup`,
  profileSettings = `/profile/settings`,
  profileSettingsAccount = `/profile/settings`,
  profileSettingsAccountDelete = `/profile/settings/account/delete`,
  profileSettingsPrivacy = `/profile/settings/privacy`,
  profileSettingsNotifications = `/profile/settings/notifications`,
  profileById = `/profile/:profileId`,
  habloAdmin = `/habloadmin`,
  habloAdminManageOrganisation = `/habloadmin/manage-organisation`,
  habloAdminManageExplorePages = `/habloadmin/manage-explore-pages`,
  habloAdminManageAutoFollows = `/habloadmin/manage-auto-follows`,
  habloAdminManageTopics = `/habloadmin/manage-topics`,
  habloAdminAwardKudos = `/habloadmin/give-kudos`,
  habloAdminManageUsers = '/habloadmin/manage-users',
  organisationNewPost = `/home?create=post`,

  //exlore category

  exploreAllDestinations = `/explore/destination`,
  exploreDestination = `/explore/destination/:destinationId`,
  explorePrivateSector = `/explore/private-sector`,
  exploreTourOperator = `/explore/tour-operator`,
  exploreTravelAgent = `/explore/travel-agent`,
  exploreAgencies = `/explore/agencies`,
  exploreOrganisationsYouFollow = `/explore/organisations-you-follow`,

  // Partners
  addPartners = `/organisation/:organisationId/partners/add`,

  //connected organisations

  addConnection = `/organisation/:organisationId/connections/add`,
  pendingConnections = `/organisation/:organisationId/connections/pending`,
  approvedConnections = `/organisation/:organisationId/connections/pending/success`,
  activeConnections = `/organisation/:organisationId/connections/active`,
  previousConnections = `/organisation/:organisationId/connections/previous`,

  // Rewards
  clubHabloDashboard = `/club-hablo/dashboard`,
  habloAiChat = `/habloaichat`,

  // All unhandled routes will load an organisation
  organisationProfile = `/:vanityId`,
  organisationAbout = `/:vanityId/about`,
  organisationWebinars = `/:vanityId/videos`,
  organisationWebinarBroadcast = `/:vanityId/videos/:id/broadcast/`,
  organisationResources = `/:vanityId/resources`,
  organisationEvents = `/:vanityId/events`,
  organisationEvent = `/:vanityId/events/:eventId`,
  organisationIncentive = `/:vanityId/incentives/:incentiveId`,
  organisationWebinar = `/:vanityId/videos/:id`,
  organisationPeople = `/:vanityId/people`,
  organisationAnalytics = `/:vanityId/analytics`,
  organisationPost = `/:vanityId/post/:id/userid/:userId`,
  userPost = `/:vanityId/post/:id`,
  organisationClubHabloDashboard = `/:vanityId/club-hablo`,
  organisationTopAgents = `/:vanityId/top-agents`,
}

export const publicRoutes: RouterProps[] = [
  {
    exact: true,
    path: Routes.default,
    Component: lazyRetry(() => import(/* webpackChunkName: "Landing" */ '../pages/landing/Landing')),
    hideNav: true,
  },
  {
    path: Routes.login,
    Component: lazyRetry(() => import(/* webpackChunkName: "Login" */ '../pages/auth/Login')),
    hideNav: true,
  },
  {
    path: Routes.logout,
    Component: lazyRetry(() => import(/* webpackChunkName: "Logout" */ '../pages/auth/Logout')),
    hideNav: true,
  },
  {
    path: Routes.signUp,
    Component: lazyRetry(() => import(/* webpackChunkName: "SignUp" */ '../pages/auth/SignUp')),
    hideNav: true,
  },
  {
    path: Routes.authCallback,
    Component: lazyRetry(() => import(/* webpackChunkName: "AuthCallback" */ '../pages/auth/AuthCallback')),
    hideNav: true,
  },
  {
    path: Routes.networkError,
    Component: lazyRetry(() => import(/* webpackChunkName: "AuthCallback" */ '../pages/auth/Networkerror')),
    hideNav: true,
  },
  {
    path: Routes.networkError,
    Component: lazyRetry(() => import(/* webpackChunkName: "AuthCallback" */ '../pages/auth/Networkerror')),
    hideNav: true,
  },
  {
    path: Routes.organisationPost,
    Component: lazyRetry(() => import(/* webpackChunkName: "StreamPost" */ 'pages/organisation/post/StreamPost')),
  },
  {
    path: Routes.userPost,
    Component: lazyRetry(() => import(/* webpackChunkName: "StreamPost" */ 'pages/organisation/post/UserStreamPost')),
  },
];

export const publicRoutesPaths: string[] = publicRoutes.reduce(
  (paths: string[], { path }) => (Array.isArray(path) ? [...paths, ...path] : path ? [...paths, path] : paths),
  [],
);

export const privateRoutes: RouterProps[] = [
  {
    exact: true,
    path: [Routes.home],
    Component: lazyRetry(() => import(/* webpackChunkName: "Home" */ '../pages/home/<USER>')),
  },
  {
    path: Routes.calendar,
    Component: lazyRetry(() => import(/* webpackChunkName: "Calendar" */ '../pages/calendar/Calendar')),
  },
  {
    path: Routes.notifications,
    Component: lazyRetry(() => import(/* webpackChunkName: "Notifications" */ '../pages/notifications/Notifications')),
  },
  {
    path: [Routes.search],
    Component: lazyRetry(() => import(/* webpackChunkName: "Search" */ '../pages/search/Search')),
  },
  {
    path: [Routes.inboxChatNew, Routes.inbox],
    Component: lazyRetry(() => import(/* webpackChunkName: "Inbox" */ '../pages/inbox/Inbox')),
  },
  {
    exact: true,
    path: Routes.connect,
    Component: lazyRetry(() => import(/* webpackChunkName: "Connect" */ '../pages/connect/Connect')),
  },
  {
    path: Routes.connectInviteContacts,
    Component: lazyRetry(
      () => import(/* webpackChunkName: "ConnectInviteContacts" */ '../pages/connect/InviteContacts/InviteContacts'),
    ),
  },
  {
    path: Routes.connectInviteContactsSent,
    Component: lazyRetry(
      () =>
        import(/* webpackChunkName: "ConnectInviteContactsSent" */ '../pages/connect/InviteContacts/InviteContacts'),
    ),
  },
  {
    path: Routes.explore,
    Component: lazyRetry(() => import(/* webpackChunkName: "Explore" */ '../pages/explore/Explore')),
  },
  {
    path: Routes.savedPosts,
    Component: lazyRetry(() => import(/* webpackChunkName: "SavedPosts" */ '../pages/savedPosts/SavedPosts')),
  },
  {
    exact: true,
    path: Routes.organisationChooseType,
    Component: lazyRetry(
      () =>
        import(/* webpackChunkName: "OrganisationChooseType" */ '../pages/organisation/create/OrganisationChooseType'),
    ),
  },
  {
    exact: true,
    path: Routes.organisationCreate,
    Component: lazyRetry(
      () => import(/* webpackChunkName: "OrganisationCreate" */ '../pages/organisation/create/OrganisationCreate'),
    ),
  },
  {
    path: Routes.organisationSettings,
    Component: lazyRetry(
      () =>
        import(/* webpackChunkName: "OrganisationSettings" */ '../pages/organisation/settings/OrganisationSettings'),
    ),
  },
  {
    path: Routes.incentives,
    Component: lazyRetry(() => import(/* webpackChunkName: "Incentives" */ '../pages/incentives/Incentives')),
  },
  {
    exact: true,
    path: Routes.clubHabloDashboard,
    Component: lazyRetry(() => import(/* webpackChunkName: "ClubHabloDashboard" */ '../pages/clubHablo/Dashboard')),
  },
  {
    exact: true,
    path: Routes.habloAiChat,
    Component: lazyRetry(() => import(/* webpackChunkName: "HabloAiChat" */ '../pages/habloAiChat/habloAiChat')),
  },
  {
    exact: true,
    path: Routes.organisationClubHabloDashboard,
    Component: lazyRetry(
      () => import(/* webpackChunkName: "OrganisationClubHabloDashboard" */ '../pages/clubHablo/OrganisationDashboard'),
    ),
  },
  {
    exact: true,
    path: Routes.profile,
    Component: lazyRetry(() => import(/* webpackChunkName: "Profile" */ '../pages/profile/Profile')),
  },
  {
    path: Routes.profileSetup,
    Component: lazyRetry(() => import(/* webpackChunkName: "ProfileSetup" */ '../pages/profileSetup/ProfileSetup')),
    hideNav: true,
  },
  {
    path: Routes.profileSettings,
    Component: lazyRetry(
      () => import(/* webpackChunkName: "ProfileSettings" */ '../pages/profileSettings/ProfileSettings'),
    ),
  },
  {
    path: Routes.profileById,
    Component: lazyRetry(() => import(/* webpackChunkName: "Profile" */ '../pages/profile/Profile')),
  },
  {
    path: Routes.habloAdmin,
    Component: lazyRetry(() => import(/* webpackChunkName: "HabloAdmin" */ '../pages/habloadmin/HabloAdmin')),
  },
  {
    path: Routes.habloAdminAwardKudos,
    Component: lazyRetry(
      () => import(/* webpackChunkName: "HabloAdminAwardKudos" */ '../pages/habloadmin/kudos/AwardKudos'),
    ),
  },
  {
    path: Routes.habloAdminManageUsers,
    Component: lazyRetry(
      () => import(/* webpackChunkName: "HabloAdminManageUsers" */ '../pages/habloadmin/manageUsers/ManageUsers'),
    ),
  },
  {
    path: Routes.organisationEvent,
    Component: lazyRetry(
      () => import(/* webpackChunkName: "OrganisationEvent" */ '../pages/organisation/event/page/OrganisationEvent'),
    ),
  },
  {
    path: Routes.organisationIncentive,
    Component: lazyRetry(
      () => import(/* webpackChunkName: "Incentive" */ 'pages/organisation/incentive/page/Incentive'),
    ),
  },
  {
    exact: true,
    path: Routes.organisationWebinarBroadcast,
    Component: lazyRetry(
      () =>
        import(/* webpackChunkName: "OrganisationWebinarBroadcast" */ 'pages/organisation/webinar/broadcast/Broadcast'),
    ),
    hideNav: true,
  },
  {
    path: Routes.organisationWebinar,
    Component: lazyRetry(() => import(/* webpackChunkName: "Webinar" */ 'pages/organisation/webinar/page/Webinar')),
  },
  // OrganisationVanityId routes should always be last
  {
    path: Routes.organisationProfile,
    Component: lazyRetry(
      () => import(/* webpackChunkName: "OrganisationProfile" */ '../pages/organisation/profile/OrganisationProfile'),
    ),
  },
];

export const allRoutes = [...publicRoutes, ...privateRoutes];

export function getRoutes() {
  return allRoutes.map(({ Component, ...props }, key) => (
    <Route
      key={key}
      {...props}
      render={(props) => (
        <ErrorHandler>
          <Component {...props} />
        </ErrorHandler>
      )}
    />
  ));
}
