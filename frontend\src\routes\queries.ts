import { gql } from '@apollo/client';

import { Membership, Organisation, Profile } from 'graphql/GraphQLTypes';

export type BasicProfileMembership = Pick<
  Membership,
  'id' | 'isPrimary' | 'position' | 'organisationName' | 'partnerOrganisation'
> & {
  organisation: Pick<
    Organisation,
    | 'id'
    | 'name'
    | 'type'
    | 'isPublic'
    | 'isEditor'
    | 'vanityId'
    | 'image'
    | 'privacy'
    | 'followersActiveCount'
    | 'followersPendingCount'
    | 'permissions'
    | 'hasClubHabloSubscription'
  >;
};

export type BasicProfile = Pick<
  Profile,
  | 'id'
  | 'isComplete'
  | 'isEmailVerified'
  | 'email'
  | 'image'
  | 'name'
  | 'onboarding'
  | 'sellHolidays'
  | 'migrationFlags'
  | 'createdAt'
  | 'showGettingStartedFeedAt'
  | 'noOfMobileDevicesLoggedIn'
  | 'timezone'
> & {
  memberships: Array<BasicProfileMembership>;
};

export type QueryData = {
  profile: BasicProfile;
};

export const GET_PROFILE = gql`
  query GetProfile {
    profile {
      id
      isEmailVerified
      isComplete
      email
      image
      name
      onboarding
      createdAt
      sellHolidays
      showGettingStartedFeedAt
      noOfMobileDevicesLoggedIn
      migrationFlags
      memberships(filter: { status: [Active], permission: [Owner, Admin, HiddenAdmin, Manager, Editor] }) {
        id
        isPrimary
        position
        organisationName
        organisation {
          id
          name
          type
          isPublic
          isEditor
          vanityId
          image
          privacy
          isPaid
          hasClubHabloSubscription
          followersActiveCount
          followersPendingCount
          permissions
          activeTier
        }
        partnerOrganisation {
          id
          status
          parentOrgId
          childOrgId
          postsLimit
          postsUsedThisMonth
          parentOrganisation {
            id
            name
            image
            vanityId
            type
            privacy
            activeTier
          }
        }
      }
    }
  }
`;

export type LogActiveSessionVariables = {
  notificationEnabled: boolean;
  variableMinutes?: number;
  variableRewardPoints?: number;
};

export const LOG_ACTIVE_SESSION = gql`
  mutation LogActiveSession($data: LogActiveSessionInput!) {
    logActiveSession(data: $data)
  }
`;

export type VariableRewardPoints = {
  getVariableRewardPoints: {
    points: number;
  };
};

export const GET_VARIABLE_REWARD_POINTS = gql`
  query GetVariableRewardPoints {
    getVariableRewardPoints {
      points
    }
  }
`;

export type RunAchievementMigrationsVariables = {
  migrations: string[];
};

export const RUN_ACHIEVEMENT_MIGRATIONS = gql`
  mutation RunAchievementMigrations($data: RunAchievementMigrationsInput!) {
    runAchievementMigrations(data: $data)
  }
`;

export type ACTIVITY_DETAILS = {
  checkEverydayActivities: {
    loginDayStreak: boolean;
    variableReward: boolean;
  };
};

export const GET_DAILY_ACTIVITY_DETAILS = gql`
  query CheckEverydayActivities {
    checkEverydayActivities {
      loginDayStreak
      variableReward
    }
  }
`;
