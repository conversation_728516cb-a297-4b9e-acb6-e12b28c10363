@import '~antd/dist/antd.less';

@primary-color: #0093c7;
@link-color: #149ccb;
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;
@font-size-base: 16px;
@heading-color: #000000;
@text-color: #303030;
@text-color-secondary: #828282;
@disabled-color: #8a8a8a;
@border-radius-base: 12px;
@border-color-base: #dcf2fe;
@box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
@font-family: Muli, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
  'Segoe UI Symbol', 'Noto Color Emoji';

@checkbox-size: 18px;

@input-border-color: #c6d7e0;
@form-item-label-height: 20px;
@form-item-label-font-size: 12px;
@form-vertical-label-padding: 0 0 4px;

@height-sm: 30px;
@height-base: 40px;
@height-lg: 50px;

@font-size-base: 14px;
@heading-1-size: 32px;
@heading-2-size: 24px;
@heading-3-size: 20px;
@heading-4-size: 16px;
@heading-5-size: 15px;

@btn-shadow: none;
@btn-padding-horizontal-base: 20px;
@btn-link-ghost-color: var(--color-text30);
@btn-default-border: @border-color-base;
@btn-disable-border: @border-color-base;

p,
.ant-typography {
  word-wrap: anywhere;
  white-space: pre-line;
}

.ant-divider-horizontal {
  margin: 15px 0;
}

.ant-btn {
  box-shadow: @btn-shadow;
}

.ant-btn-primary.ant-btn-background-ghost {
  border-color: @border-color-base;
}

.ant-hide-footer {
  .ant-picker-footer {
    display: none;
  }
}

.ant-checkbox-wrapper .ant-checkbox-inner {
  border-radius: 4px;
}

.ant-checkbox-wrapper.ant-checkbox-indent {
  display: flex;
  align-items: flex-start;
  margin-bottom: 5px;

  .ant-checkbox {
    top: 2px;
  }

  span:nth-child(1) {
    display: inline-block;
  }

  span:nth-child(2) {
    vertical-align: 2px;
  }
}

.ant-form-item-label {
  font-size: 12px !important;
  font-weight: bold;
  color: var(--color-text);
}

.ant-input-affix-wrapper .ant-input {
  border-radius: 0;
}

.ant-dropdown-menu {
  overflow: hidden;
}

.ant-list-item-action {
  display: flex;
}

.ant-list-item-meta {
  align-items: center;
}

.ant-list-item-meta-title {
  line-height: 1.4;
  margin-bottom: 0;
}

.ant-badge-count {
  box-shadow: none;
}

.ant-card {
  position: relative;
  overflow: hidden;
  border: none;
  border-radius: 12px;
  box-shadow: 0 0 24px rgba(0, 0, 0, 0.07);
  color: var(--color-text);
  background-color: var(--color-text-background);
}

.ant-skeleton-avatar-square {
  border-radius: 12px;
}

.ant-skeleton-title {
  margin: 0 0 10px 0 !important;
}

.ant-skeleton-paragraph {
  margin: 0 !important;
}

.ant-list-item-action-split {
  display: none;
}

.ant-modal-content {
  overflow: hidden;
}

.ant-modal-with-overflow {
  .ant-modal-content {
    overflow: visible !important;
  }
}

.ant-modal-header {
  border-bottom: none;
  padding-bottom: 0;
}

.ant-modal-title {
  font-weight: 700;
  font-size: 14px;
  text-transform: uppercase;
}

.ant-modal-close-icon {
  padding: 6px;
  border-radius: 50%;
  background-color: var(--color-text-background);
}

.ant-modal.achievement-modal {
  & .ant-modal-body {
    svg {
      width: 120px;
      height: 95px;
    }
  }
}

.ant-modal.achievement-modal,
.ant-modal.org-achievement-modal {
  & .ant-modal-footer {
    display: none;
  }

  & .achievement-progress {
    & .ant-progress-inner,
    & .ant-progress-bg {
      height: 18px !important;
      border-radius: 0 !important;
    }

    span {
      margin-bottom: 0;
      position: absolute;
      top: 56%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 13px;
      font-weight: 700;
    }
  }

  h3 {
    font-size: 16px;
    font-weight: 700;
    margin-top: 10px;
    margin-bottom: 0;

    @media screen and (max-width: 568px) {
      font-size: 14px;
    }
  }

  p {
    font-size: 13px;
    font-weight: 400;
    margin-bottom: 0;
  }
}

.ant-modal.org-achievement-modal {
  & .achievement-progress {
    & .ant-progress-inner,
    & .ant-progress-bg {
      border-radius: 20px !important;
    }

    & .ant-progress-bg {
      border-top-right-radius: 0 !important;
      border-bottom-right-radius: 0 !important;
    }
  }
}

label.ant-checkbox-wrapper.ant-checkbox-green {
  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #52c41a;
    border-color: #52c41a;
  }

  .ant-checkbox-checked::after {
    border-color: #52c41a;
  }

  :hover {
    .ant-checkbox-inner {
      border-color: #52c41a;
    }
  }
}

.ant-anchor-wrapper {
  overflow: visible;
}

.ant-anchor-no-style {
  margin: 0;
  padding: 0;
  background-color: transparent;

  .ant-anchor-ink {
    display: none;
  }
}

.custom-responsive-form {
  @media screen and (max-width: 568px) {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}

.custom-responsive-radio-group {
  @media screen and (max-width: 568px) {
    display: grid;
  }
}

.custom-responsive-display-none {
  @media screen and (max-width: 586px) {
    display: none !important;
  }
}

.custom-responsive-home-posts-column {
  @media screen and (max-width: 586px) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.ant-back-top {
  @media screen and (max-width: 586px) {
    bottom: 55px;
  }
}

.custom-responsive-side-nav-column {
  @media screen and (max-width: 586px) {
    padding-left: 0px;
    padding-right: 0px;
    max-width: 100%;
    margin-bottom: 5px;
  }
}

.custom-responsive-row {
  @media screen and (max-width: 586px) {
    margin-top: 5px;
    margin-left: 0px;
    margin-right: 0px;
    margin-bottom: 40px;
  }
}

.custom-responsive-overflow-x-auto {
  @media screen and (max-width: 586px) {
    overflow-x: auto;
  }
}

.custom-responsive-side-nav-bottom {
  @media screen and (max-width: 586px) {
    margin-bottom: 15px;
  }
}

.custom-responsive-display-only-mobile {
  display: none !important;
  @media screen and (max-width: 586px) {
    display: flex !important;
  }
}

.custom-responsive-margin-around {
  @media screen and (max-width: 568px) {
    margin: 15px;
  }
}

.ant-dropdown {
  z-index: 99 !important;
}

.ant-btn {
  font-weight: 500;
}

.block-ui {
  width: 100vw;
  height: 100vh;
  background: rgb(0 0 0 / 20%);
  position: fixed;
  background: rgba(0, 0, 0, 0.2);
  left: 0;
  top: 0;
  z-index: 1000;
}

.highlight-mention {
  background: rgb(230, 243, 255);
  color: var(--color-text);
  font-weight: bold;
  cursor: pointer;
  font-size: 14px;
  a {
    word-break: break-all;
  }
}

.raf-comment-item__content {
  padding: 3px 15px 9px 15px !important;
}

.raf-comment-item__content .raf-comment-item__author {
  margin-left: 0 !important;
}

.raf-comment-item__content .raf-comment-item__extraInfo {
  margin-left: 0 !important;
  font-size: 11px !important;
}

.ant-form-item-control-input-content textarea {
  outline: none !important;
  box-shadow: none !important;
}

.ant-radio-inner {
  border-color: #abb7bd;
}

.mentions-div {
  border: 1px solid rgb(221, 221, 221);
  padding: 16px;
  border-radius: 12px;
  min-height: 120px;
  ul {
    background: #fff;
    border-radius: 5px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    li {
      padding: 10px 8px;
      &:hover {
        background-color: #f5f5f5;
      }
    }
  }
}

.edit-mentions-div__suggestions {
  flex: auto;
  white-space: nowrap;
  margin: 15px -18px;
  max-width: 500px;
  max-height: 300px;
  border-radius: 12px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
  background-color: #fefefe;
  cursor: pointer;
  transition: background 0.3s ease;
  z-index: 99;
  // position: absolute;
  padding: 0;
  width: calc(100% - 50px);
  @media screen and (max-width: 568px) {
    width: 100%;
  }
  overflow: hidden;
  overflow-y: auto;
}

.edit-mentions-div__suggestions__item {
  padding-bottom: 0 !important;
  padding-top: 0 !important;
}

.edit-mentions-div__input {
  border: 0;
}

.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #0093c7;
  font-weight: 700 !important;
  font-size: 15px !important;
}

.ant-tabs-tab-btn {
  font-size: 15px;
  font-weight: 700;
  color: #828282;
}

.mention-container__suggestions {
  min-width: 300px !important;
}

.ant-list-item-meta {
  padding: 8px 15px;
}

.ant-tabs-top > .ant-tabs-nav::before,
.ant-tabs-bottom > .ant-tabs-nav::before,
.ant-tabs-top > div > .ant-tabs-nav::before,
.ant-tabs-bottom > div > .ant-tabs-nav::before {
  border-bottom: 1px solid #e0e0e0;
}

.ant-tabs-top > .ant-tabs-nav .ant-tabs-ink-bar,
.ant-tabs-bottom > .ant-tabs-nav .ant-tabs-ink-bar,
.ant-tabs-top > div > .ant-tabs-nav .ant-tabs-ink-bar,
.ant-tabs-bottom > div > .ant-tabs-nav .ant-tabs-ink-bar {
  height: 3px;
}
.post-btn {
  &:disabled {
    border: none;
  }
}

.responive-list-item {
  flex-wrap: nowrap;
  .ant-list-item-meta {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 8px 0;
    max-width: inherit;
  }
  .ant-list-item-meta-content {
    width: auto;
  }
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}

.ant-popover-inner-content {
  padding: 0;
}
.ant-popover-inner {
  overflow: hidden;
}

@media screen and (max-width: 568px) {
  .custom-range-picker .ant-picker-panel-container .ant-picker-panels {
    flex-direction: column;
  }
}

.org-autocomplete {
  .disabled-item {
    pointer-events: none;
    opacity: 0.5;
  }
}
