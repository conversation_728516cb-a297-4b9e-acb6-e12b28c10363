import { Membership, MembershipStatus, Post } from '@GraphQLTypes';
import { random } from './random';
import { mockOrganisation } from './mockOrganisation';
import { mockProfileData } from './mockProfile';
import { mockPartnerOrganisation } from './mockPartnerOrganisation';

export function mockMembership(membership: Partial<Membership> = {}, r = random): Required<Membership> {
  return {
    __typename: 'Membership',
    id: r.guid(),
    isPrimary: true,
    position: r.profession(),
    status: MembershipStatus.Active,
    organisation: mockOrganisation({}, r),
    organisationName: r.company(),
    permissions: [],
    actions: [],
    profile: mockProfileData({}, r),
    createdAt: r.utcDate(),
    updatedAt: r.utcDate(),
    parentOrganisation: mockPartnerOrganisation({}, r),
    partnerOrganisation: mockPartnerOrganisation({}, r),
    ...membership,
  };
}
