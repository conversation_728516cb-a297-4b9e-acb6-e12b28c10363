import { Organisation, OrganisationSize, OrganisationStatus, OrganisationType } from '@GraphQLTypes';
import { random } from './random';

export function mockOrganisation(organisation: Partial<Organisation> = {}, r = random): Required<Organisation> {
  return {
    __typename: 'Organisation',
    id: r.guid(),
    name: r.company(),
    image: r.image(200, 200, r),
    status: OrganisationStatus.Active,
    backgroundImage: r.image(200, 1000, r),
    location: {
      city: r.city(),
      country: r.country(),
      lat: r.latitude(),
      long: r.longitude(),
    },
    type: OrganisationType.Destination,
    privacy: r.string(),
    peoplePrivacy: [],
    additionalPrivacy: null,
    followingPrivacy: [],
    privacySettings: {},
    website: r.url(),
    resources: r.url(),
    resourceUrl: r.url(),
    description: r.sentence(),
    size: OrganisationSize.OrgSize2_10,
    vanityId: r.string({ alpha: true }),
    isOwner: false,
    isMember: false,
    isPublic: false,
    isEditor: false,
    isAdmin: false,
    isManager: false,
    activeMembership: null,
    permissions: [],
    pages: [],
    memberships: {
      totalCount: 0,
      records: [],
    },
    followers: {
      totalCount: 0,
      records: [],
    },
    followerStatus: null,
    followersActiveCount: 0,
    followersPendingCount: 0,
    followersRejectedCount: 0,
    webinarsCount: 0,
    eventsCount: 0,
    incentives: {
      totalCount: 0,
      records: [],
    },
    partnerships: [],
    sentPartnershipRequests: [],
    receivedPartnershipRequests: [],
    createdAt: r.nextDescendingDate(),
    updatedAt: r.utcDate(),
    preApprovedDomains: [],
    stripeConnectAccount: null,
    isConnectOnboarded: null,
    parentOrganisations: [
      {
        id: r.string(),
      },
    ],
    parentOrganisationDetails: [
      {
        id: r.string(),
        image: r.string(),
        name: r.string(),
        vanityId: r.string(),
      },
    ],
    connectedOrganisations: [r.string()],
    isPaid: true,
    hasClubHabloSubscription: true,
    activeTier: 'blue',
    isDMOSubscription: true,
    DMOMaxPartners: 2,
    childOrganisationId: null,
    isPartner: false,
    postsLimit: 2,
    postsUsedThisMonth: 0,
    referralCount: 0,
    ...organisation,
  };
}

export const mockOrganisations = (count: number, organisation: Partial<Organisation> = {}, organisationIdStart = 1) =>
  new Array(count).fill(1).map((_) => mockOrganisation({ ...organisation, id: (organisationIdStart++).toString() }));
