import { OrganisationType, PartnerOrganisation } from '@src/graphql/GraphQLTypes';
import { random } from './random';

export const mockPartnerOrganisation = (partnerOrganisation: Partial<PartnerOrganisation> = {}, r = random) => {
  return {
    id: r.guid(),
    status: 'Approved',
    parentOrgId: r.guid(),
    childOrgId: r.guid(),
    postsLimit: 8,
    postsUsedThisMonth: 1,
    createdAt: r.nextDescendingDate(),
    updatedAt: r.utcDate(),
    parentOrganisation: {
      id: r.guid(),
      name: r.company(),
      image: r.image(200, 200, r),
      vanityId: r.string({ alpha: true }),
      type: OrganisationType.Destination,
      createdAt: r.nextDescendingDate(),
      updatedAt: r.utcDate(),
      privacy: null,
      __typename: 'Organisation',
    },
    childOrganisation: {
      id: r.guid(),
      name: r.company(),
      image: r.image(200, 200, r),
      vanityId: r.string({ alpha: true }),
      type: OrganisationType.Destination,
      createdAt: r.nextDescendingDate(),
      updatedAt: r.utcDate(),
      privacy: null,
      __typename: 'Organisation',
    },
    __typename: 'PartnerOrganisation',
    ...partnerOrganisation,
  };
};

export const mockPartnerOrganisations = (
  count: number,
  parentOrganisation: Partial<PartnerOrganisation> = {},
  partnerOrganisationIdStart = 1,
) =>
  new Array(count)
    .fill(1)
    .map((_) => mockPartnerOrganisation({ ...parentOrganisation, id: (partnerOrganisationIdStart++).toString() }));
